<template>
    <div class="default_layout">
        <!-- <header> -->
        <header ref="headerRef" :class="{ fix_top: fixTopStatus, height_header: isHeightHeader }">
            <fs-header v-show="!hideHeaderFooter"></fs-header>
        </header>
        <main>
            <Nuxt />
        </main>
        <footer>
            <fs-footer v-show="!hideHeaderFooter"></fs-footer>
        </footer>
        <div
            class="customer_service_box"
            v-if="customer_service_btn"
            @click.stop="
                fsLiveChatMixins()
                GaLiveChat()
            ">
            <span class="iconfont">&#xf188;</span>
            <span class="num" v-if="noReplyNum">{{ noReplyNum > 99 ? "99+" : noReplyNum }}</span>
            <div class="customer_service_tip"></div>
        </div>
        <fs-global-float-btn></fs-global-float-btn>
        <CookieTip />
        <GRecaptcha />
        <CountriesRegions />
    </div>
</template>

<script>
import FsHeader from "@/components/FsHeader/FsHeader.vue"
import FsFooter from "@/components/FsFooter/FsFooter.vue"
import CookieTip from "@/components/CookieTip/CookieTip.vue"
import CountriesRegions from "@/popup/CountriesRegions/CountriesRegions.vue"
import { schemaOrganization } from "@/constants/schemaOrganization"
import { mapState, mapGetters } from "vuex"
import { loadFontCss, defaultPV } from "@/util/util.js"
import AES from "@/util/AES.js"
import BackTop from "@/components/BackTop/BackTop"
import FsGlobalFloatBtn from "@/components/FsGlobalFloatBtn/FsGlobalFloatBtn"
import liveChatMixins from "@/layouts/liveChatMixins"
import { htmlLang } from "@/constants/htmlLang"
import { digi } from "@/util/meta.js"
import GRecaptcha from "@/components/GRecaptcha/GRecaptcha.vue"

export default {
    name: "DefaultLayout",
    mixins: [liveChatMixins],
    head() {
        return {
            htmlAttrs: {
                lang: htmlLang(this.website, this.iso_code),
            },
            title: this.title,
            meta: [
                ...this.commonMeta,
                {
                    hid: "viewport",
                    name: "viewport",
                    content: "width=device-width, initial-scale=1, maximum-scale=1, minimum-scale=1",
                },
                ...this.verifyMeta,
                ...this.ogMeta,
                ...this.twitterMeta,
            ],
            link: [...this.commonLink, ...this.link],
            style: [loadFontCss(this.website)],
            __dangerouslyDisableSanitizers: ["title", "meta", "script", "link", "style"],
            script: [
                digi(),
                {
                    type: "application/ld+json",
                    json: schemaOrganization(this.website),
                },
            ],
        }
    },
    data() {
        return {
            hideHeaderFooter: this.$route.query && this.$route.query.flag && this.$route.query.flag === "app" ? true : false,
            customer_service_btn: false,
            fixTopStatus: false,
            isHeightHeader: false,
        }
    },

    computed: {
        ...mapState({
            title: (state) => state.meta.title,
            commonMeta: (state) => state.meta.commonMeta,
            verifyMeta: (state) => state.meta.verifyMeta,
            robotsMeta: (state) => state.meta.robotsMeta,
            ogMeta: (state) => state.meta.ogMeta,
            twitterMeta: (state) => state.meta.twitterMeta,
            commonLink: (state) => state.meta.commonLink,
            link: (state) => state.meta.link,
            langLink: (state) => state.meta.langLink,
            userInfo: (state) => state.userInfo.userInfo,
            website: (state) => state.webSiteInfo.website,
            isLogin: (state) => state.userInfo.isLogin,
            iso_code: (state) => state.webSiteInfo.iso_code,
            language: (state) => state.webSiteInfo.language,
            pageLocation: (state) => state.ga.pageLocation,
            pageTitle: (state) => state.meta.title,
            currency: (state) => state.webSiteInfo.currency,
            refererName: (state) => state.device.refererName,
            noReplyNum: (state) => state.liveChat.noReplyNum,
            pageGroup: (state) => state.ga.pageGroup,
            // lang_id: state => state.webSiteInfo.language_id,
            // lang: state => state.webSiteInfo.language,
            // country_code: state => state.webSiteInfo.iso_code,
            // site_code: state => state.webSiteInfo.website,
        }),
        ...mapGetters({
            gaLoginString: "userInfo/gaLoginString",
        }),
    },
    components: {
        FsHeader,
        FsFooter,
        BackTop,
        FsGlobalFloatBtn,
        CookieTip,
        GRecaptcha,
        CountriesRegions,
    },
    methods: {
        initCommunityWakeUpLiveChat() {
            if (this.$route.query.communityWakeUpChat === "1") {
                this.fsLiveChatMixins()
            }
        },

        GaLiveChat() {
            if (window.dataLayer) {
                window.dataLayer.push({
                    event: "uaEvent",
                    eventCategory: this.pageGroup,
                    eventAction: "floating_button",
                    eventLabel: "Live Chat",
                    nonInteraction: false,
                })
            }
        },
    },
    mounted() {
        const rect = this.$refs.headerRef.getBoundingClientRect().height
        if (rect > 84) {
            this.isHeightHeader = true
        } else {
            this.isHeightHeader = false
        }
        const url = window.location.href
        console.log("url0000==", url, rect)
        const regex = /\/products\/|\/specials\/|\/solutions\/|\/solutions_industries\/|\/case-study\/|\/products_support.html|\/about-us.html/
        if (!regex.test(url)) {
            this.fixTopStatus = true
        } else {
            const filenames = ["2002.html", "2003.html", "2004.html", "2005.html", "2006.html", "2007.html"]
            this.fixTopStatus = filenames.some((filename) => url.includes(filename))
        }
        if (window.dataLayer && !this.refererName) {
            let loginState = this.isLogin ? `Login_${this.gaLoginString}` : `Logout_${this.gaLoginString}`
            let userId = this.isLogin ? AES.encrypt(`${this.userInfo.customers_level}${this.userInfo.customers_id}`, `_-yu_xuan_3507-_`, `fs_com_phone2016`) : ""
            let website = `${this.iso_code}_${this.language}_${this.currency}`
            let fsPvid = this.$cookies.get("_fs_pvid")
            defaultPV(loginState, userId, website, fsPvid, this.$route, this.pageLocation, this.pageTitle, undefined, this)
        }
        if (["au", "sg"].includes(this.website)) {
            this.customer_service_btn = true
        }
        this.initCommunityWakeUpLiveChat()
    },
}
</script>

<style lang="scss" scoped>
header.fix_top {
    position: sticky;
    top: 0;
    z-index: 107;
    @media (max-width: 1024px) {
        position: sticky;
        top: -36px;
    }
    &.height_header {
        @media (max-width: 1024px) {
            position: sticky;
            top: -56px;
        }
    }
}
.customer_service_box {
    display: none;
    width: 48px;
    height: 48px;
    align-items: center;
    justify-content: center;
    background: #fff;
    border-radius: 50%;
    position: fixed;
    z-index: 1;
    right: 40px;
    bottom: 165px;
    cursor: pointer;
    box-shadow: 0px 2px 14px 0px rgba(137, 137, 140, 0.2);
    user-select: none;
    z-index: 50;
    transition: all 0.3s;
    &:hover {
        box-shadow: 0 6px 22px 0 rgb(137 137 140 / 40%);
    }
    @media (max-width: 960px) {
        right: 24px;
        bottom: 50px;
    }
    .iconfont {
        font-size: 20px;
        color: #19191a;
    }
    .num {
        position: absolute;
        height: 18px;
        color: #fff;
        font-size: 14px;
        line-height: 18px;
        border-radius: 18px;
        padding: 0 6px;
        top: -9px;
        right: -4px;
        background: #c00000;
    }
}
</style>
