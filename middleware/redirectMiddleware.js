import { parseQueryString, generateUUID, setCookieOptions } from "@/util/util"
import { website_prefix } from "@/constants/validate"

//重定向链接
const redirects = {
    "/support/jumpers-test-assured-program-21.html": "/specials/jumpers-test-assured-program-21.html",
    "/support/test-assured-program.html ": "/specials/test-assured-program-151.html",
    "/support/jumpers-test-assured-program.html": "/specials/jumpers-test-assured-program-21.html",
    "/support/test-assured-program-for-ethernet-cables-112.html": "/specials/test-assured-program-for-ethernet-cables-112.html",
    "/support/test-assured-program-151.html": "/specials/test-assured-program-151.html",
    "/company-profile.html": "/about-us.html",
    "/company/about_us.html": "/about-us.html",
    "/research_center.html": "/advanced-research-development-center.html",
    "/company/quality_assurance.html": "/quality-control.html",
    "/company/quality_control.html": "/compliance-center.html",
    "/company/test_center.html": "/test-center.html",
    "/contact_us.html": "/contact-us.html",
    "/legal_notice.html": "/legal-notice.html",
    "/tech_support.html": "/product_and_project_inquiry.html",
    "/free_design_solution.html": "/solution-services.html",
    "/solutions/picos-for-h100-infiniband-solution.html": "/solutions/picos-for-h100-infiniband-solution-S3000.html",
    "/solutions/infiniband-transceivers-and-cables-connectivity-solution-overview.html": "/solutions/infiniband-transceivers-and-cables-connectivity-solution-overview-S3001.html",
    "/solutions/efficient-and-easy-to-use-100G-dwdm-pam4-solutions.html": "/solutions/efficient-and-easy-to-use-100G-dwdm-pam4-solutions-S3002.html",
    "/solutions/data-center-power-solution.html": "/solutions/data-center-power-solution-S3003.html",
    "/solutions/enterprise-office-buildings-surveillance-solution.html": "/solutions/enterprise-office-buildings-surveillance-solution-S3004.html",
    "/solutions/400g-ai-roce-data-center-networking.html": "/solutions/400g-ai-roce-data-center-networking-S3005.html",
    "/solutions/seamless-video-streaming-experience-in-data-center.html": "/solutions/seamless-video-streaming-experience-in-data-center-S3006.html",
}

// export default function ({ app, $axios, store, route, req, $cookies, redirect, env }) {
//     console.log("redirectMiddleware")
//     console.log(route)
//     const is404 = route.path.includes("/404.html")

//     if (is404 && process.server && route.redirectedFrom) {
//         let u = route.redirectedFrom.replace(/^\//, "")
//         if (website_prefix.test(u)) {
//             u = u.replace(website_prefix, "")
//         }
//         u = `/${u}`

//         console.log("redirect_redirect_redirect_redirect_")
//         console.log(redirects[u])
//         console.log(store.state.meta.domain)
//         console.log(`${store.state.meta.domain}${u}`)
//         if (redirects[u]) {
//             let newUrl = app.$localeLink(redirects[u])
//             return redirect(`${store.state.meta.domain}${newUrl}`)
//         }
//     }
// }
export default function ({ app, redirect, $localeLink, route, env, req }) {
    if (env && req?.url) {
        let p = ""
        for (let k in redirects) {
            if (req.url.includes(k)) {
                p = redirects[k]
            }
        }
        if (p) {
            return redirect(301, $localeLink(p))
        }
    }
}
