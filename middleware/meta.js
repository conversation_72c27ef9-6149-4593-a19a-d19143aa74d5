import { parseQueryString, generateUUID, setCookieOptions } from "@/util/util"
import { website_prefix } from "@/constants/validate"

export default function ({ app, $axios, store, route, req, redirect, env }) {
    const { name, redirectedFrom } = route
    const is404 = /^404/g.test(name)
    const isListFront = /^list/g.test(name)
    const excludesRouteNames = ["products", "home"]

    if (is404) {
        let url = ""
        if (redirectedFrom) {
            let domain = store.state.meta.domain
            url = `${domain}${redirectedFrom}`
        }
        store.commit("ga/setRedirectedFrom", url)
    }
    if (process.server && route.redirectedFrom && route.redirectedFrom.includes("index.php") && parseQueryString(route.redirectedFrom)) {
        let u = route.redirectedFrom.replace(/^\//, "")
        let w = ""
        if (website_prefix.test(u)) {
            w = u.slice(0, u.indexOf("/"))
        }
        w = w ? `/${w}` : ""
        if (parseQueryString(route.redirectedFrom).main_page === "products_payment" && parseQueryString(route.redirectedFrom).orders_id) {
            return redirect(`${w}/additional_payment/${parseQueryString(route.redirectedFrom).orders_id}`)
        } else if (parseQueryString(route.redirectedFrom).main_page === "quotes_pdf" && parseQueryString(route.redirectedFrom).quotes_id) {
            return redirect(`${w}/quote_invoice?id=${parseQueryString(route.redirectedFrom).quotes_id}`)
        } else if (parseQueryString(route.redirectedFrom).main_page === "shopping_products" && parseQueryString(route.redirectedFrom).orders_id) {
            return redirect(`${w}/shopping_products/${parseQueryString(route.redirectedFrom).orders_id}`)
        } else if (parseQueryString(route.redirectedFrom).main_page === "customer_payment_link") {
            return redirect(`${w}/customer_payment_link`)
        }
    } else {
        if (env && process.client) {
            let _fs_pvid = generateUUID()
            if (store.getters["webSiteInfo/isLoadAdSdk"]) {
                app.$cookies.set("_fs_pvid", _fs_pvid)
            }

            // if (window && window.FsAnalytics) {
            // 	store.commit("webSiteInfo/setPreurl", window.location.href)
            // 	let route_name = route.name.split('_')[0] || '';
            // 	if (route_name !== 'list' && route_name !== "products") {
            // 		let domain = store.state.meta.domain;
            // 		let url = `${domain}${route.fullPath}`;
            // 		window.FsAnalytics.push({
            // 			u: url,
            // 			ref_url: window.location.href,
            // 			page_name: route_name
            // 		})
            // 	}
            // }
        }

        //return store.dispatch('meta/getMeta', {route})
        let cate = []
        if (store.state.category && store.state.category.category_data && store.state.category.category_data.data && store.state.category.category_data.data.length) {
            cate = store.state.category.category_data.data.map((item) => {
                return item.categories_id
            })
        }
        let flag = excludesRouteNames.some((i) => {
            const regexp = new RegExp(`^${i}`, "g")
            return regexp.test(name)
        })
        if (isListFront) {
            let c_id = route.params.id.match(/\d+$/g) ? route.params.id.match(/\d+$/g)[0] : ""
            console.log("c_id:", c_id, ",c_id type:", typeof c_id)
            if (c_id && !cate.includes(parseInt(c_id))) {
                flag = true
            }
        }
        if (!flag) {
            return store.dispatch("meta/getMeta", { route })
        }
    }
}
