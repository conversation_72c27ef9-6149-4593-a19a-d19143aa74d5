import fs from "fs"
export default function ({ app, $axios, store, route, req, redirect, env }) {
    console.log("55_55")
    console.log(process.server)
    console.log(store.state.category)

    if (process.server && store.state.category && store.state.category.menu_data && !store.state.category.menu_data.length) {
        console.log("66_66")
        let cache_file_name = store.state.webSiteInfo.cache_file_name
        if (fs && fs.existsSync && cache_file_name && fs.existsSync(`/data/test-platform/cache/header/${cache_file_name}_header.json`)) {
            store.dispatch("category/getCategory")
            store.commit("cache/setCategorySource", "interface")
            // try {
            //     let r = fs.readFileSync(`/data/test-platform/cache/header/${cache_file_name}_header.json`, "utf-8")
            //     if (r && JSON.parse(r)) {
            //         data = JSON.parse(r)
            //         store.commit("cache/setCategorySource", "cache")
            //         let { category, classify, footer } = data
            //         let arr = JSON.parse(JSON.stringify(classify))
            //         arr.unshift(JSON.parse(JSON.stringify(category)))
            //         store.commit("category/setMenuData", arr)
            //         store.commit("category/setCategoryData", JSON.parse(JSON.stringify(category)))
            //         store.commit("category/setClassifyData", JSON.parse(JSON.stringify(classify)))
            //         store.commit("category/setTopData", res.data.tip)
            //         store.commit("category/setFooterData", JSON.parse(JSON.stringify(footer.classify)))
            //         store.commit("category/setAusAcknowledges", JSON.parse(JSON.stringify(footer.fs_acknowledges)))
            //     } else {
            //         store.dispatch("category/getCategory")
            //         store.commit("cache/setCategorySource", "interface")
            //     }
            // } catch (error) {
            //     store.dispatch("category/getCategory")
            //     store.commit("cache/setCategorySource", "interface")
            // }
        } else {
            store.dispatch("category/getCategory")
            store.commit("cache/setCategorySource", "interface")
        }
    }
}
