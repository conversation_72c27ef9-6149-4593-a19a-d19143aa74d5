import { setCookieOptions } from "@/util/util"
// 中国站点集合
const CN_WEBSITE_LIST = ["cn"]
export default function ({ app, store, route, query, redirect, res }) {
    // console.log("getUserInfogetUserInfogetUserInfo");
    //if (process.server) {
    let token = app.$cookies.get("token_new") || ""
    //console.log(token)
    //res.setHeader('token',token );
    //}
    const updateOldWebsite = app.$cookies.get("old_website")
    // const oldWebsite = app.$cookies.get("website")
    const oldWebsite = store.state.webSiteInfo.website
    const maxOldWebsite = updateOldWebsite || oldWebsite
    if (updateOldWebsite) {
        app.$cookies.remove("old_website")
    }
    if (token) {
        return store.dispatch("userInfo/getUserInfo", () => {
            if (route.meta && route.meta.length) {
                route.meta.map((item) => {
                    if (item.auth && !store.state.userInfo.isLogin) {
                        let q = JSON.parse(JSON.stringify(query))
                        if (q && q.redirect) {
                            delete q.redirect
                        }
                        const newWebsite = store.state.webSiteInfo.website
                        let redirectQuery = { redirect: app.localePath({ path: route.path, query: q }) }
                        if (CN_WEBSITE_LIST.includes(maxOldWebsite) !== CN_WEBSITE_LIST.includes(newWebsite)) {
                            redirectQuery.isSwitchingSite = true
                        }
                        redirect(app.localePath({ name: "login", query: redirectQuery }))
                    }
                })
            }
        })
    } else {
        store.commit("userInfo/resetUserInfo")
        if (route.meta && route.meta.length) {
            route.meta.map((item) => {
                if (item.auth && !store.state.userInfo.isLogin) {
                    let q = JSON.parse(JSON.stringify(query))
                    if (q && q.redirect) {
                        delete q.redirect
                    }
                    redirect(app.localePath({ name: "login", query: { redirect: app.localePath({ path: route.path, query: q }) } }))
                }
            })
        }
    }
}
