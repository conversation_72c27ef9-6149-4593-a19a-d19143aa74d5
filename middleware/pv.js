import { gaCommunityPramas } from "@/util/util"
import AES from "@/util/AES.js"

export default function ({ app, route, store, env }) {
    // console.log(route.name.slice(0, route.name.length - 8), 'aaaaaaaaaaa')
    // console.log(store.state.webSiteInfo);
    store.commit("device/setGaScroll", 0)
    if (env && process.client) {
        if (window.dataLayer) {
            window.dataLayer.push(function () {
                this.reset()
            })
            const alonePV = ["products", "search_result", "shopping-cart", "confirm-order", "quote", "checkout-against", "checkout", "checkout-success"]
            let name = route.name.substring(0, route.name.lastIndexOf("___"))
            if (!alonePV.includes(name)) {
                let loginState = store.state.userInfo.isLogin ? "Login" : "Logout"
                let userId =
                    store.state.userInfo.isLogin && store.state.userInfo.userInfo
                        ? AES.encrypt(`${store.state.userInfo.userInfo.customers_level}${store.state.userInfo.userInfo.customers_id}`, `_-yu_xuan_3507-_`, `fs_com_phone2016`)
                        : ""
                let website = store?.state?.webSiteInfo?.iso_code + "_" + store?.state?.webSiteInfo?.language + "_" + store?.state?.webSiteInfo?.currency
                let fsPvid = app.$cookies.get("_fs_pvid")
                let ecommPagetype = ""
                let gaLoginString = store.getters["userInfo/gaLoginString"]
                if (name === "home") {
                    ecommPagetype = "home"
                } else if (name === "search_result") {
                    ecommPagetype = "searchresults"
                } else if (name === "list") {
                    ecommPagetype = "category"
                } else {
                    ecommPagetype = "other"
                }
                let ga_community_str = gaCommunityPramas(route)
                window.dataLayer.push({
                    event: "PageView",
                    loginStatus: `${loginState}_${gaLoginString}`,
                    userId: userId,
                    websiteLanguage: website,
                    fsPvid: fsPvid,
                    ecommPagetype: ecommPagetype,
                    page_location: store?.state?.ga?.pageLocation,
                    page_path: route.fullPath,
                    page_title: store?.state?.meta?.title,
                    "c_site&c_ctype&c_from&c_cat&c_rel": ga_community_str,
                    page_url: `${store.state.meta.domain}${route.fullPath}`,
                    page_referrer: store.state.webSiteInfo.pre_url,
                    urlSite: store.state.webSiteInfo.website === "en" ? "us" : store.state.webSiteInfo.website,
                })
            }
        }
    }
}
