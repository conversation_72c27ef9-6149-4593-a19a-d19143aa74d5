import path from "path"
const resolve = (pagePath) => path.resolve(process.cwd(), pagePath)

const solutions = [
    // {
    // 	name: "enterprise-wi-fi-6-network-solution-cns030063",
    // 	path: "/solutions/campus-wireless/enterprise-wi-fi-6-network-solution-cns030063.html",
    // 	component: resolve('./pages/Solutions/Solutions.vue'),
    // 	meta: {
    // 		group: "Solution Page_Campus Wireless"
    // 	}
    // },
    // {
    // 	name: "internet-enterprise-office-network-solution-cns040064",
    // 	path: "/solutions/campus-network/internet-enterprise-office-network-solution-cns040064.html",
    // 	component: resolve('./pages/Solutions/Solutions.vue'),
    // 	meta: {
    // 		group: "Solution Page_Campus Network"
    // 	}
    // },
    // {
    // 	name: "smart-hotel-wireless-network-solution-cns030058",
    // 	path: "/solutions/campus-wireless/smart-hotel-wireless-network-solution-cns030058.html",
    // 	component: resolve('./pages/Solutions/Solutions.vue'),
    // 	meta: {
    // 		group: "Solution Page_Campus Wireless"
    // 	}
    // },
    // {
    // 	name: "enterprises-security-video-surveillance-solution-cns010001",
    // 	path: "/solutions/video-surveillance/enterprises-security-video-surveillance-solution-cns010001.html",
    // 	component: resolve('./pages/Solutions/Solutions.vue'),
    // 	meta: {
    // 		group: "Solution Page_Video Surveillance"
    // 	}
    // },
    // {
    // 	name: "voip-solutions-for-schools-and-universities-cns020022",
    // 	path: "/solutions/business-phone-system/voip-solutions-for-schools-and-universities-cns020022.html",
    // 	component: resolve('./pages/Solutions/Solutions.vue'),
    // 	meta: {
    // 		group: "Solution Page_Business Phone System"
    // 	}
    // },
    // {
    // 	name: "smart-hotel-video-surveillance-solution-cns010006",
    // 	path: "/solutions/video-surveillance/smart-hotel-video-surveillance-solution-cns010006.html",
    // 	component: resolve('./pages/Solutions/Solutions.vue'),
    // 	meta: {
    // 		group: "Solution Page_Video Surveillance"
    // 	}
    // },
    // {
    // 	name: "smart-supermarket-and-retail-video-surveillance-solution-cns010057",
    // 	path: "/solutions/video-surveillance/smart-supermarket-and-retail-video-surveillance-solution-cns010057.html",
    // 	component: resolve('./pages/Solutions/Solutions.vue'),
    // 	meta: {
    // 		group: "Solution Page_Video Surveillance"
    // 	}
    // },
    {
        name: "25g-100g-data-center-network-solution-dcs010065",
        path: "/solutions/data-center/25g-100g-data-center-network-solution-dcs010065.html",
        component: resolve("./pages/Solutions/Solutions.vue"),
        meta: {
            group: "Solution Page_Data Center",
        },
    },
    // {
    // 	name: "security-solution-for-small-business-internet-edge-cns0400610",
    // 	path: "/solutions/campus-network/security-solution-for-small-business-internet-edge-cns0400610.html",
    // 	component: resolve('./pages/Solutions/Solutions.vue'),
    // 	meta: {
    // 		group: "Solution Page_Campus Network"
    // 	}
    // },
    // {
    // 	name: "private-cloud-solutions-for-small-businesses-dcs0100011",
    // 	path: "/solutions/data-center/private-cloud-solutions-for-small-businesses-dcs0100011.html",
    // 	component: resolve('./pages/Solutions/Solutions.vue'),
    // 	meta: {
    // 		group: "Solution Page_Data Center"
    // 	}
    // },
    // {
    // 	name: "campus-video-surveillance-solutions-cns010029",
    // 	path: "/solutions/video-surveillance/campus-video-surveillance-solutions-cns010029.html",
    // 	component: resolve('./pages/Solutions/Solutions.vue'),
    // 	meta: {
    // 		group: "Solution Page_Video Surveillance"
    // 	}
    // },
    // {
    // 	name: "family-villa-video-surveillance-solutions-IH010215",
    // 	path: "/solutions/campus-wireless/wisdom-villa-wi-fi-coverage-network-solution-ih010215.html",
    // 	component: resolve('./pages/Solutions/Solutions.vue'),
    // 	meta: {
    // 		group: "Solution Page_Campus Wireless"
    // 	}
    // },
    // {
    // 	name: "interconnect-solution-for-small-and-medium-sized-data-centers-RH010216",
    // 	path: "/solutions/campus-wireless/large-shopping-mall-wireless-network-solution-rh010216.html",
    // 	component: resolve('./pages/Solutions/Solutions.vue'),
    // 	meta: {
    // 		group: "Solution Page_Campus Wireless"
    // 	}
    // },
    // {
    // 	name: "network-visibility-solution-in-data-centers-NET010514",
    // 	path: "/solutions/data-center/network-visibility-solution-in-data-centers-net010514.html",
    // 	component: resolve('./pages/Solutions/Solutions.vue'),
    // 	meta: {
    // 		group: "Solution Page_Data Center"
    // 	}
    // },
    // {
    // 	name: "wisdom-villa-wi-fi-coverage-network-solution-IH010312",
    // 	path: "/solutions/video-surveillance/family-villa-video-surveillance-solutions-ih010312.html",
    // 	component: resolve('./pages/Solutions/Solutions.vue'),
    // 	meta: {
    // 		group: "Solution Page_Video Surveillance"
    // 	}
    // },
    // {
    // 	name: "solution_mall_wireless-NET020113",
    // 	path: "/solutions/optical-networking/interconnect-solution-for-small-and-medium-sized-data-centers-net020113.html",
    // 	component: resolve('./pages/Solutions/Solutions.vue'),
    // 	meta: {
    // 		group: "Solution Page_Optical Networking"
    // 	}
    // },
    // {
    // 	name: "data-center-structured-cabling-solution-net03011",
    // 	path: "/solutions/data-center/data-center-structured-cabling-solution-net03011.html",
    // 	component: resolve('./pages/Solutions/Solutions.vue'),
    // 	meta: {
    // 		group: "Solution Page_Data Center"
    // 	}
    // },
    // {
    // 	name: "outdoor-smart-parking-solution-manu010119",
    // 	path: "/solutions/campus-network/outdoor-smart-parking-solution-manu010119.html",
    // 	component: resolve('./pages/Solutions/Solutions.vue'),
    // 	meta: {
    // 		group: "Solution Page_Campus Network"
    // 	}
    // },
    // {
    // 	name: "bar-security-video-surveillance-solution-me010317",
    // 	path: "/solutions/video-surveillance/bar-security-video-surveillance-solution-me010317.html",
    // 	component: resolve('./pages/Solutions/Solutions.vue'),
    // 	meta: {
    // 		group: "Solution Page_Video Surveillance"
    // 	}
    // },
    // {
    // 	name: "voip-solutions-for-governments-gov010318",
    // 	path: "/solutions/business-phone-system/voip-solutions-for-governments-gov010318.html",
    // 	component: resolve('./pages/Solutions/Solutions.vue'),
    // 	meta: {
    // 		group: "Solution Page_Business Phone System"
    // 	}
    // },
    {
        name: "solutions",
        path: "/solutions/:id",
        component: resolve("./pages/Solutions/Solutions.vue"),
        meta: {
            group: "Solutions Page",
        },
    },
    {
        name: "solutions_industries",
        path: "/solutions_industries/:id",
        component: resolve("./pages/SolutionsSummary/SolutionsSummary.vue"),
        meta: {
            group: "solutions_industries Page",
        },
    },
    {
        name: "picos-for-multi-branch-network-solution",
        path: "/solutions/picos-for-multi-branch-network-solution.html",
        component: resolve("./pages/Solutions/StaticPages/CampusBranch/CampusBranch.vue"),
        meta: {
            group: "Solutions Page",
        },
    },
    {
        name: "accelerated-h100-infiniband-solutions-for-hpc",
        path: "/solutions/picos-for-h100-infiniband-solution-S3000.html",
        component: resolve("./pages/Solutions/StaticPages/InfiniBandSolutions/InfiniBandSolutions.vue"),
        meta: {
            group: "Solutions Page",
        },
    },
    {
        name: "solution-new-detail",
        path: "/solutions/solution-new-detail.html",
        component: resolve("./pages/Solutions/DetailSolutionsNew/DetailSolutionNew.vue"),
        meta: {
            group: "Solutions Page",
        },
    },
    {
        name: "AI-defined Automated Driving Solution",
        path: "/solutions/hpc-defined-automated-driving-solution.html",
        component: resolve("./pages/Solutions/StaticPages/AutomatedDrivingSolution/AutomatedDrivingSolution.vue"),
        meta: {
            group: "Solutions Page",
        },
    },
    {
        name: "seamless_video_streaming",
        path: "/solutions/seamless-video-streaming-experience-in-data-center-S3006.html",
        component: resolve("./pages/Solutions/StaticPages/VideoStreaming/VideoStreaming.vue"),
        meta: {
            group: "Solutions Page",
        },
    },
    {
        name: "fhd-and-mtp-for-modular-cabling-system",
        path: "/solutions/fhd-and-mtp-for-modular-cabling-system.html",
        component: resolve("./pages/Solutions/StaticPages/CablingSystem/CablingSystem.vue"),
        meta: {
            group: "Solutions Page",
        },
    },
    {
        name: "gaming_data_center",
        path: "/solutions/upgrading-gaming-data-center-networks-for-seamless-experiences.html",
        component: resolve("./pages/Solutions/StaticPages/GamingDataCenter/GamingDataCenter.vue"),
        meta: {
            group: "Solutions Page",
        },
    },
    {
        name: "campus-converged-wired-and-wireless-solution",
        path: "/solutions/campus-converged-wired-and-wireless-solution.html",
        component: resolve("./pages/Solutions/StaticPages/CampusConverged/CampusConverged.vue"),
    },
    {
        name: "office_buildings",
        path: "/solutions/enterprise-office-buildings-surveillance-solution-S3004.html",
        component: resolve("./pages/Solutions/StaticPages/OfficeBuildings/OfficeBuildings.vue"),
        meta: {
            group: "Solutions Page",
        },
    },
    {
        name: "data-center-power-solution",
        path: "/solutions/data-center-power-solution-S3003.html",
        component: resolve("./pages/Solutions/StaticPages/DataCenterPower/DataCenterPower.vue"),
        meta: {
            group: "Solutions Page",
        },
    },
    {
        name: "efficient-and-easy-to-use-100G-dwdm-pam4-solutions",
        path: "/solutions/efficient-and-easy-to-use-100G-dwdm-pam4-solutions-S3002.html",
        component: resolve("./pages/Solutions/StaticPages/DataCenterInterconnect/DataCenterInterconnect.vue"),
        meta: {
            group: "Solutions Page",
        },
    },
    {
        name: "infiniband-transceivers-and-cables-connectivity-solution-overview",
        path: "/solutions/infiniband-transceivers-and-cables-connectivity-solution-overview-S3001.html",
        component: resolve("./pages/Solutions/StaticPages/InfiniBandTransceivers/InfiniBandTransceivers.vue"),
        meta: {
            group: "Solutions Page",
        },
    },
    {
        name: "400g-ai-roce-data-center-networking",
        path: "/solutions/400g-ai-roce-data-center-networking-S3005.html",
        component: resolve("./pages/Solutions/StaticPages/RoCEDataCenterNetworking/RoCEDataCenterNetworking.vue"),
        meta: {
            group: "Solutions Page",
        },
    },
]

export default solutions
