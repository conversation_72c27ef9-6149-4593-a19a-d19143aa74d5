<template>
    <div class="main_container">
        <h2>{{ $c("single.BusinessAccount.contactInfo.title") }}</h2>
        <p class="tips">{{ $c("single.BusinessAccount.contactInfo.tips") }}</p>
        <div class="user_info">
            <div class="form">
                <div class="form_item">
                    <p class="label">{{ $c("single.BusinessAccount.contactInfo.firstName") }}</p>
                    <input class="is_new" type="text" v-model="contactInfo.firstName" readonly disabled />
                </div>
                <div class="form_item">
                    <p class="label">{{ $c("single.BusinessAccount.contactInfo.lastName") }}</p>
                    <input class="is_new" type="text" v-model="contactInfo.lastName" readonly disabled />
                </div>
                <div class="form_item">
                    <p class="label">{{ $c("single.BusinessAccount.contactInfo.email") }}</p>
                    <input class="is_new" type="text" v-model="contactInfo.email" readonly disabled />
                </div>
            </div>
        </div>
        <h2>{{ $c("pages.Login.org_form.title") }}</h2>
        <p class="tips">{{ $c("single.BusinessAccount.businessInfo.tips") }}</p>
        <div class="form">
            <div class="form_item">
                <p class="label">{{ $c("pages.Login.org_form.name.label") }} *</p>
                <input class="is_new" type="text" v-model="form.company_name" @blur="validator('company_name')" @input="validator('company_name')" />
                <validate-error :error="errors.company_name"></validate-error>
            </div>
            <div class="form_item">
                <p class="label">{{ $c("form.form.country2") }} *</p>
                <!-- <fs-select class="country" :options="country_list" search v-model="form.entry_country_id" @change="countryChange" :inpPlaceholder="$c('components.smallComponents.searchCountry')"></fs-select> -->
                <select-country :isNewStyle="true" position="absolute"></select-country>
                <validate-error :error="errors.entry_country_id"></validate-error>
            </div>
            <div class="form_item flex1">
                <p class="label">{{ $c("pages.Login.org_form.street_address.label") }} *</p>
                <input class="is_new" type="text" v-model="form.address_one" @blur="validator('address_one')" @input="validator('address_one')" :placeholder="$c('pages.confirmOrder.address.holderAd')" />
                <validate-error :error="errors.address_one"></validate-error>
            </div>
            <div class="form_item flex1">
                <p class="label">{{ $c("pages.Login.org_form.street_address.label") }} 2</p>
                <input class="is_new" type="text" v-model="form.address_two" @blur="validator('address_two')" @input="validator('address_two')" :placeholder="$c('pages.confirmOrder.address.holderAd2')" />
                <validate-error :error="errors.address_two"></validate-error>
            </div>
            <div class="form_item">
                <p class="label">{{ $c("pages.CaseDetail.detailsPage.askDemo.city") }} *</p>
                <fs-select :isNewStyle="true" v-if="isShowCitySelect" :showDefaultValue="false" :options="city_list" v-model="form.city" @change="validator('city')"></fs-select>
                <input v-else class="is_new" type="text" v-model="form.city" @blur="validator('city')" @input="validator('city')" />
                <validate-error :error="errors.city"></validate-error>
            </div>
            <div class="form_item" v-if="isShowState">
                <p class="label">{{ $c("form.form.state_province_region") }} *</p>
                <!-- <fs-select class="state" :options="stateList" v-model="form.state" /> -->
                <RegionSelect :isNewStyle="true" ref="regionSelect" />
                <validate-error :error="errors.state"></validate-error>
            </div>
            <div class="form_item">
                <p class="label">{{ $c("pages.CaseDetail.detailsPage.askDemo.zipCode") }} *</p>
                <input class="is_new" type="text" v-model="form.entry_postcode" @blur="validator('entry_postcode')" @input="validator('entry_postcode')" />
                <validate-error :error="errors.entry_postcode"></validate-error>
            </div>

            <div class="form_item">
                <p class="label">{{ $c("form.form.phone_number") }} *</p>
                <tel-code-new
                    :isNewStyle="true"
                    :telPrefix="telPrefix"
                    @changeCode="changeCode"
                    v-model="form.company_phone"
                    @change="(val) => handleChange('company_phone', val)"
                    @input="handleInput('company_phone')"></tel-code-new>
                <validate-error :error="errors.company_phone"></validate-error>
            </div>

            <div class="form_item" v-show="taxLabel">
                <p class="label">{{ taxLabel }}</p>
                <input
                    class="is_new"
                    type="text"
                    v-model="form.tax_number"
                    maxlength="21"
                    @blur="validator('tax_number')"
                    @input="validator('tax_number')"
                    :placeholder="$c('single.BusinessAccount.businessInfo.placeholder.tid')" />
                <validate-error :error="errors.tax_number"></validate-error>
            </div>

            <!-- <div class="form_item">
                <p class="label">{{ $c("pages.Login.org_form.organization_size_label") }} *</p>
                <fs-select :isNewStyle="true" :options="organizationSizeOption" v-model="form.organization_size"></fs-select>
                <validate-error :error="errors.organization_size"></validate-error>
            </div> -->
        </div>
        <div class="form_item_upload">
            <upload-file :isNewStyle="true" @change="uploadFile" ref="uploadFile" type="file" :accept="upload.accept" :text="upload.text" :multiple="true" :limit="5" :maxSize="5 * 1024 * 1024">
                <fs-popover position="right" slot="tip" class="file_popover">
                    <div class="tipFont">
                        <div class="file_tips_title">{{ $c("single.BusinessAccount.businessInfo.fileTips.title") }}</div>
                        <ul>
                            <li>{{ $c("single.BusinessAccount.businessInfo.fileTips.text1") }}</li>
                            <li>{{ $c("single.BusinessAccount.businessInfo.fileTips.text2") }}</li>
                            <li>{{ $c("single.BusinessAccount.businessInfo.fileTips.text3") }}</li>
                        </ul>
                    </div>
                </fs-popover>
            </upload-file>
        </div>
        <!-- <h2 class="address_title">{{ $c("single.BusinessAccount.businessAddress.title") }}</h2> -->
        <!-- <div class="form">
            <div class="form_item" :class="isShowState ? '' : 'flex1'">
                <p class="label">{{ $c("form.form.country2") }}</p>
                <fs-select class="country" :options="country_list" search v-model="form.entry_country_id" @change="countryChange" :inpPlaceholder="$c('components.smallComponents.searchCountry')"></fs-select>
                <select-country position="absolute"></select-country>
                <validate-error :error="errors.entry_country_id"></validate-error>
            </div>
            <div class="form_item" v-if="isShowState">
                <p class="label">{{ $c("form.form.state_province_region") }}</p>
                <fs-select class="state" :options="stateList" v-model="form.state" />
                <RegionSelect ref="regionSelect" />
                <validate-error :error="errors.state"></validate-error>
            </div>
            <div class="form_item">
                <p class="label">{{ $c("pages.CaseDetail.detailsPage.askDemo.city") }} *</p>
                <fs-select v-if="isShowCitySelect" :showDefaultValue="false" :options="city_list" v-model="form.city" @change="validator('city')"></fs-select>
                <input v-else type="text" v-model="form.city" @blur="validator('city')" @input="validator('city')" />
                <validate-error :error="errors.city"></validate-error>
            </div>

            <div class="form_item">
                <p class="label">{{ $c("pages.CaseDetail.detailsPage.askDemo.zipCode") }} *</p>
                <input class="is_new" type="text" v-model="form.entry_postcode" @blur="validator('entry_postcode')" @input="validator('entry_postcode')" />
                <validate-error :error="errors.entry_postcode"></validate-error>
            </div>
        </div> -->
        <!-- <div class="form_item address">
            <p class="label">{{ $c("single.BusinessAccount.businessAddress.address1") }} *</p>
            <input class="is_new" type="text" v-model="form.address_one" @blur="validator('address_one')" @input="validator('address_one')" :placeholder="$c('pages.confirmOrder.address.holderAd')" />
            <validate-error :error="errors.address_one"></validate-error>
        </div>
        <div class="form_item address">
            <p class="label">{{ $c("single.BusinessAccount.businessAddress.address2") }}</p>
            <input class="is_new" type="text" v-model="form.address_two" @blur="validator('address_two')" @input="validator('address_two')" :placeholder="$c('pages.confirmOrder.address.holderAd2')" />
            <validate-error :error="errors.address_two"></validate-error>
        </div> -->
        <div class="attention">
            <div class="policy_box">
                <input v-model="form.isAgreePolicy" @change="validator('isAgreePolicy')" type="checkbox" class="chk" />
                <div
                    class="agreement_wrap"
                    v-html="
                        $c('form.validate.aggree_policy_new')
                            .replace('AAAA', localePath({ name: 'privacy-notice' }))
                            .replace('BBBB', localePath({ name: 'terms-of-use' }))
                    "></div>
            </div>
            <validate-error :error="errors.isAgreePolicy"></validate-error>
        </div>
        <!-- <p class="attention" v-html="$c('single.BusinessAccount.submitTips').replace('xxxx', localePath('/policies/business_accounts_terms_conditions'))"></p> -->
        <fs-button :loading="loading" @click="handelSubmit">{{ $c("pages.SupportTicket.detail.submit") }}</fs-button>
        <div class="side_container">
            <ul>
                <li v-for="({ title, content }, index) in benefits" :key="index">
                    <div>
                        <h5>{{ title }}</h5>
                        <p>{{ content }}</p>
                    </div>
                </li>
            </ul>
            <p class="tips" v-html="$c('single.BusinessAccount.title.tips').replace('/business_account.html', localePath({ name: 'business_account' }))"></p>
        </div>
    </div>
</template>

<script>
import FormMinxin from "../formMixin"
import { mapState, mapGetters, mapActions } from "vuex"
export default {
    mixins: [FormMinxin],
    data() {
        return {
            currentCountry: {},
            benefits: [
                { title: this.$c("single.BusinessAccount.benefit.quickly.tittle"), content: this.$c("single.BusinessAccount.benefit.quickly.text") },
                { title: this.$c("single.BusinessAccount.benefit.easily.title"), content: this.$c("single.BusinessAccount.benefit.easily.text") },
                { title: this.$c("single.BusinessAccount.benefit.business.title"), content: this.$c("single.BusinessAccount.benefit.business.text") },
            ],
        }
    },
    methods: {
        countryChange(val) {
            this.form.entry_country_id = val
            const country = this.country_list.filter((i) => i.value === val)
            this.currentCountry = country[0] || { states: [] }
            this.form.state = this.currentCountry.states[0]?.states_code || "0"
            console.log(this.currentCountry, "currentCountry")
        },
    },
    computed: {
        ...mapState({
            select_country_code: (state) => state.selectCountry.select_country_code,
            city_list: (state) => state.selectCountry.city_list,
        }),

        stateList() {
            const { states } = this.currentCountry
            let arr = []
            if (states && states.length > 0) {
                arr = states.map(({ states: name, states_code: value }) => {
                    return { name, value }
                })
            }
            return [...arr]
        },
        taxLabel() {
            const vatCountryList = [
                "AT",
                "DE",
                "LU",
                "CH",
                "NL",
                "MT",
                "DK",
                "FI",
                "SE",
                "CY",
                "HR",
                "CZ",
                "LT",
                "PL",
                "SK",
                "SI",
                "BG",
                "EE",
                "GR",
                "HU",
                "RO",
                "LV",
                "NO",
                "BE",
                "FR",
                "IT",
                "ES",
                "PT",
                "GB",
                "IE",
                "IS",
            ]
            if (vatCountryList.includes(this.select_country_code)) {
                return "VAT"
            } else if (this.select_country_code === "MD") {
                return "IDNO"
            } else if (this.select_country_code === "MC") {
                return "NIE"
            } else if (this.select_country_code === "BR") {
                return "CNPJ"
            } else if (this.select_country_code === "MX") {
                return "RFC"
            } else if (this.select_country_code === "PA") {
                return "RUC"
            } else if (["PR", "US"].includes(this.select_country_code)) {
                return "EIN"
            } else if (this.select_country_code === "CA") {
                return "BN"
            } else if (this.select_country_code === "ID") {
                return "NIB"
            } else if (["MY", "MM", "FJ"].includes(this.select_country_code)) {
                return "CRN"
            } else if (this.select_country_code === "SG") {
                return "UEN"
            } else if (this.select_country_code === "VN") {
                return "MSD"
            } else if (this.select_country_code === "JP") {
                return "CN"
            } else if (this.select_country_code === "AU") {
                return "ABN"
            } else if (this.select_country_code === "NZ") {
                return "NZBN"
            } else {
                return ""
            }
        },
    },
    watch: {
        countries_id: {
            handler(val) {
                if (val) {
                    this.countryChange(val)
                }
            },
            immediate: true,
        },
        isShowCitySelect: {
            handler(val) {
                if (this.form.city) {
                    this.form.city = ""
                }
            },
        },
    },
}
</script>

<style lang="scss" scoped>
// input:disabled {
//     color: $textColor3;
// }
.file_popover {
    margin-left: 8px;
}
.main_container {
    max-width: 900px;
    background-color: #fff;
    > h2 {
        @include font20;
        margin-bottom: 4px;
        &:not(:first-of-type) {
            margin-top: 20px;
        }
    }
    .tips {
        color: $textColor1;
        @include font12;
    }

    .side_container {
        display: none;
    }
}
.form {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    column-gap: 20px;
    margin-top: 12px;
    row-gap: 16px;
}
.form_item {
    @include font12;
    .label {
        color: $textColor3;
        margin-bottom: 4px;
    }
    /* &:has(.error_info) {
        > input {
            border-color: #f00;
        }
    } */
}

.policy_box {
    display: flex;
    .chk {
        margin-top: 4px;
        margin-right: 8px;
        width: 14px;
        height: 14px;
        font-size: 14px;
    }
    .agreement_wrap {
        @include font14;
        color: $textColor3;
    }
}
.flex1 {
    grid-column: 1 / span 2;
}
.form_item_upload {
    margin-top: 12px;
}
.address {
    margin-top: 16px;
}
.attention {
    @include font12;
    margin-top: 16px;
    margin-bottom: 20px;
    color: $textColor3;
}
@media (max-width: 1024px) {
    .account_application {
        .main {
            .main_container {
                padding: 40px 16px;
                // margin: 40px;
            }
        }
    }
}
@media (max-width: 768px) {
    .account_application {
        .banner {
            width: 100vw;
            height: 233px;
            > p {
                @include font24;
            }
        }
        .main {
            padding: 0;
            .main_container {
                padding: 32px 16px;
            }
            .side_container {
                display: block;
                > ul {
                    margin-top: 20px;
                    @include font12;
                    > li {
                        position: relative;
                        padding-left: 12px;
                        margin-bottom: 20px;
                        h5 {
                            font-weight: normal;
                            margin-bottom: 4px;
                            color: $textColor1;
                            @include font12;
                        }
                        &::before {
                            content: "";
                            width: 4px;
                            height: 4px;
                            border-radius: 50%;
                            background-color: $textColor3;
                            position: absolute;
                            left: 0;
                            top: 7px;
                        }
                        p {
                            color: #707070;
                            @include font12;
                        }
                    }
                }
                .tips {
                    color: $textColor1;
                    @include font12;
                    margin-top: 20px;
                }
            }
        }
    }
    .form {
        grid-template-columns: repeat(1, 1fr);
    }
    .attention {
        color: $textColor3;
    }
    .fs-button {
        width: 100%;
    }
    .success {
        padding: 36px 16px;
        text-align: center;
        > h3 {
            @include font16;
            margin-top: 16px;
        }
        .iconfont {
            font-size: 50px;
        }
    }
    .error_notice {
        width: auto;
        padding: 20px;
        @include font14;
    }
    .file_popover {
        margin-left: 4px;
    }

    .main_container {
        > h2 {
            &.address_title {
                margin-top: 36px;
                margin-bottom: 0px;
            }
        }
    }
    .form_item {
        grid-column: 1 / span 2;
    }
}
</style>
