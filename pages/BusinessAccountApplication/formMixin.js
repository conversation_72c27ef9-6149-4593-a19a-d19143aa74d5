import FsSelect from "@/components/FsSelect/FsSelect.vue"
import TelCode from "@/components/TelCode/TelCode.vue"
import TelCodeNew from "@/components/TelCode/TelCodeNew.vue"
import UploadFile from "@/components/UploadFile/UploadFile.vue"
import UploadCnFile from "@/components/UploadFile/UploadCnFile.vue"
import FsPopover from "@/components/FsPopover/"
import FsButton from "@/components/FsButton/FsButton.vue"
import { mapState, mapGetters } from "vuex"
import ValidateError from "@/components/ValidateError/ValidateError.vue"
import { scrollTo } from "@/util/util.js"
import SelectCountry from "@/components/SelectCountry/SelectCountry.vue"
import RegionSelect from "@/components/RegionSelect/RegionSelect"
export default {
    components: {
        FsSelect,
        TelCode,
        UploadFile,
        FsPopover,
        FsButton,
        ValidateError,
        SelectCountry,
        RegionSelect,
        TelCodeNew,
        UploadCnFile,
    },
    data() {
        return {
            loading: false,
            upload: {
                accept: ".pdf,.jpg,.png,.doc,.docx,.xls,.xlsx,.txt",
                text: this.$c("single.BusinessAccount.businessInfo.file"),
            },
            companySizeOption: [
                { name: this.$c("single.BusinessAccount.businessInfo.options.text1"), value: 1 },
                { name: this.$c("single.BusinessAccount.businessInfo.options.text2"), value: 2 },
                { name: this.$c("single.BusinessAccount.businessInfo.options.text3"), value: 3 },
                { name: this.$c("single.BusinessAccount.businessInfo.options.text4"), value: 4 },
            ],
            organizationSizeOption: [
                { name: this.$c("pages.Login.org_form.organization_size[1]"), value: 2 },
                { name: this.$c("pages.Login.org_form.organization_size[2]"), value: 3 },
                { name: this.$c("pages.Login.org_form.organization_size[3]"), value: 4 },
            ],
            contactInfo: {
                firstName: "",
                lastName: "",
                email: "",
                phone: "",
            },
            files: [],
            form: {
                company_name: "",
                // organization_size: "",
                tax_number: "",
                company_phone: "",
                entry_country_id: "",
                city: "",
                state: "",
                entry_postcode: "",
                address_one: "",
                address_two: "",
                telPrefix: "",
                isAgreePolicy: false,
            },
            errors: {
                company_name: "",
                tax_number: "",
                company_phone: "",
                // organization_size: "",
                entry_country_id: "",
                city: "",
                state: "",
                entry_postcode: "",
                address_one: "",
                address_two: "",
                isAgreePolicy: "",
            },
            isSuccess: false,
            showError: false,
            caseNumber: "",
        }
    },
    computed: {
        ...mapState({
            pageGroup: (state) => state.ga.pageGroup,
            userInfo: (state) => state.userInfo.userInfo,
            resourcePage: (state) => state.device.resource_page,
            country_list: (state) => {
                let list = state.selectCountry.country_list || []
                if (list.length) {
                    list = list.filter((i) => i.countries_id !== 44)
                    list = list.map((item) => {
                        return { name: item.countries_name, value: item.countries_id, tel_prefix: item.tel_prefix, states: item.states }
                    })
                }
                return list
            },
            tel_prefix: (state) => state.webSiteInfo.tel_prefix,
            countries_id: (state) => state.webSiteInfo.countries_id,
            select_country_id: (state) => state.selectCountry.select_country_id,
            select_tel_prefix: (state) => state.selectCountry.select_tel_prefix,
        }),
        ...mapGetters({
            isShowState: "selectCountry/isShowState",
            isShowCitySelect: "selectCountry/isShowCitySelect",
        }),
        telPrefix: {
            get() {
                return this.form.telPrefix || this.tel_prefix || "+86"
            },
            set(val) {
                this.form.telPrefix = val
            },
        },
        isJp() {
            return this.form.entry_country_id === 107
        },
    },
    methods: {
        uploadFile({ error, files }) {
            if (!error) {
                this.files = files
            }
        },
        handelSubmit() {
            if (this.validataAll()) {
                this.form.entry_country_id = this.select_country_id
                this.form.state = this.$refs.regionSelect?.state || "0"
                const params = new FormData()
                const arr = Object.keys(this.form)
                arr.forEach((i) => {
                    params.append(i, this.form[i])
                })
                if (this.files.length) {
                    this.files.forEach((file) => {
                        params.append("require_file[]", file)
                    })
                }
                params.append("resource_page", this.resourcePage)
                this.loading = true
                this.$axios
                    .post("/api/companyAccount/accountApply", params)
                    .then((res) => {
                        const {
                            data: { data },
                        } = res
                        if (res.code === 200) {
                            this.$emit("submitSuccess", res.data?.case_number || "")
                            this.gaFunction("certify_business_account", `Submit_success_${undefined}`)
                        }
                    })
                    .catch((err) => {
                        console.log(err, "err")
                        this.gaFunction("certify_business_account", `Submit_fail_${err.message}`)
                        if (err.code === 3010) {
                            this.$emit("showError", err.errors.code, err.message)
                        } else if (err.code === 422) {
                            if (err.errors) {
                                for (let attr in err.errors) {
                                    console.log(attr)
                                    if (attr == "verification_key") {
                                        this.errors.code = err.errors[attr]
                                    } else {
                                        this.errors[attr] = err.errors[attr]
                                    }
                                }
                            }
                            // const { entry_postcode } = err.errors
                            // if (entry_postcode) {
                            //     this.errors.entry_postcode = entry_postcode
                            // } else {
                            //     this.$message.error(err.message)
                            // }
                        } else {
                            this.$emit("showError", err.errors.code, err.message)
                        }
                    })
                    .finally(() => {
                        this.loading = false
                    })
            }
        },
        validator(k) {
            const value = this.form[k]
            let str = ""
            if (k === "isAgreePolicy") {
                this.errors.isAgreePolicy = value ? "" : this.$c("form.form.errors.check2_error")
                return
            }
            if (!(value || ["entry_postcode", "address_one", "address_two", "tax_number"].includes(k))) {
                str = this.$c("form.form.errors.interest_type_error")
                this.errors[k] = str
                return
            }
            if (k === "company_name") {
                if (value.length > 120) {
                    str = this.$c("form.validate.company.company_validate")
                }
            } else if (k === "tax_number") {
                if (value.length > 0 && value.length > 40) {
                    str = this.$c("form.form.TaxMaxAdress")
                } else {
                    str = ""
                }
            } else if (k === "entry_postcode") {
                if (!value) {
                    str = this.$c("pages.confirmOrder.form.zip_code_required")
                    if (this.isJp) {
                        str = this.$c("form.form.placeHolder.nodata")
                    }
                } else if (value.length < 3) {
                    str = this.$c("pages.confirmOrder.form.zip_code_validate")
                } else if (value.length > 10) {
                    str = this.$c("pages.confirmOrder.form.zipCodeMaxLength")
                } else {
                    str = ""
                }
            } else if (k === "address_one") {
                if (!value) {
                    str = this.isJp ? this.$c("form.form.placeHolder.addressNone") : this.$c("pages.confirmOrder.form.address_required")
                } else if (value.length < 2 || value.length > 35) {
                    str = this.isJp ? this.$c("form.form.placeHolder.addressLength") : this.$c("pages.confirmOrder.form.address_validate")
                } else {
                    str = ""
                }
            } else if (k === "address_two") {
                if ((value.length && value.length < 2) || value.length > 35) {
                    str = this.isJp ? this.$c("form.form.placeHolder.addressLength") : this.$c("pages.confirmOrder.form.address_validate")
                } else {
                    str = ""
                }
            } else if (k === "city") {
                if (value.length < 2) {
                    str = this.$c("pages.NetTermsApplication.validate.city.city_validate")
                } else if (value.length > 40) {
                    str = this.$c("pages.NetTermsApplication.validate.city.city_validate")
                } else {
                    str = ""
                }
            }
            this.errors[k] = str
            return
        },
        validataAll() {
            const arr = Object.keys(this.errors)
            arr.forEach((k) => {
                this.validator(k)
            })
            const isValidate = Object.values(this.errors).every((i) => !i)
            if (!isValidate) {
                this.$nextTick(() => {
                    const dom = document.querySelector(".error_info")
                    if (dom) {
                        scrollTo(dom, 9, () => {}, -100)
                    }
                })
            }
            return isValidate
        },
        changeCode(val) {
            this.form.telPrefix = val
        },
        handleInput(target) {
            if (target === "company_phone") {
                console.log(this.form.company_phone)
                this.form.company_phone = this.form.company_phone.replace(/^\.+|[^\d.]/g, "")
                if (!this.form.company_phone.replace(/^\s+|\s+$/g, "")) {
                    this.errors.entry_telephone_error = this.$c("form.form.errors.entry_telephone_error")
                } else if (this.form.company_phone.length > 0 && this.form.company_phone.length < 6) {
                    this.errors.company_phone = this.$c("pages.NetTermsApplication.validate.phone.phone_min")
                } else if (this.form.company_phone.length > 40) {
                    this.errors.company_phone = this.$c("pages.NetTermsApplication.validate.phone.phone_validate")
                } else {
                    this.errors.company_phone = ""
                }
            }
        },
        handleChange(target, value) {
            console.log(target, "target")

            if (target === "company_phone") {
                if (!value.replace(/^\s+|\s+$/g, "")) {
                    this.errors.entry_telephone_error = this.$c("form.form.errors.entry_telephone_error")
                } else if (value.length > 0 && value.length < 6) {
                    this.errors.company_phone = this.$c("pages.NetTermsApplication.validate.phone.phone_min")
                } else if (value.length > 40) {
                    this.errors.company_phone = this.$c("pages.NetTermsApplication.validate.phone.phone_validate")
                } else {
                    this.errors.company_phone = ""
                }
                // console.log(this.form.company_phone)
                // if (value.length > 11) {
                //     this.form.company_phone = value.substr(0, 11)
                // }
            }
        },
        gaFunction(eventAction, eventLabel) {
            window.dataLayer &&
                window.dataLayer.push({
                    event: "uaEvent",
                    eventCategory: this.pageGroup,
                    eventAction,
                    eventLabel,
                    nonInteraction: false,
                })
        },
    },
    watch: {
        userInfo: {
            handler(val) {
                this.contactInfo.firstName = val.customers_firstname
                this.contactInfo.lastName = val.customers_lastname
                this.contactInfo.email = val.customers_email_address
                this.contactInfo.phone = val.customers_telephone
                this.contactInfo.account = `${val.customers_lastname} ${val.customers_firstname}`
            },
            immediate: true,
        },
        // "form.organization_size": {
        //     handler(val) {
        //         if (val) {
        //             this.errors.organization_size = ""
        //         }
        //     },
        // },
        select_tel_prefix: {
            handler(n) {
                this.form.telPrefix = n
            },
        },
    },
}
