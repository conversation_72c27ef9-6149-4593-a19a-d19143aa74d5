<template>
    <div>
        <div class="table" ref="table" v-if="entry.length > 0">
            <template>
                <div v-for="(item, index) in entry" :class="{ last_box: index === entry.length - 1 }" :key="entry[index]">
                    <div class="table_item" :class="{ mark_left: index === 0 }">
                        <div class="header">
                            <template v-if="item === 'category'">
                                <div class="header_cate">
                                    <div>{{ getTableTIle(item) }}</div>
                                    <Category ref="tabelCategorys" @confirm="handleTypeConfirm" v-model="popoverTypeShow" :values="types" :options="categories">
                                        <i class="iconfont icon-filter" @mouseenter="handleCateMouseEnter" @mouseleave="handleCateMouseLeave"> &#xe712;</i>
                                    </Category>
                                </div>
                            </template>
                            <template
                                v-else-if="
                                    item === 'pn' ||
                                    item === 'sku' ||
                                    item === 'qty' ||
                                    item === 'orderNumber' ||
                                    item === 'invoiceNumber' ||
                                    item === 'purchasedBy' ||
                                    item === 'warrantyStr' ||
                                    item === 'price' ||
                                    item === 'shipToRegion' ||
                                    item === 'deliveryDate' ||
                                    item === 'trackingNumber'
                                ">
                                <div class="header_cate">
                                    <!-- <div>{{ getTableTIle(item) }}</div> -->
                                    <div class="arrow">
                                        <fs-popover :popperStyle="popperStyle" v-model="orderShow[item]" isAccessible isFocusPopper position="bottom" trigger="click" :closeIcon="false" :icon="false">
                                            <div slot="trigger">
                                                <div>{{ getTableTIle(item) }}</div>
                                                <i class="iconfont icofont-down">{{ orderShow[item] ? "&#xe700;" : "&#xe704;" }}</i>
                                            </div>
                                            <ul class="handle_tip">
                                                <li
                                                    v-for="(q, t) in orderStatus"
                                                    :key="t"
                                                    tabindex="0"
                                                    @keyup.enter.stop=";(orderShow[item] = false), $emit('orderStatusChange', q.value, item)"
                                                    @click=";(orderShow[item] = false), $emit('orderStatusChange', q.value, item)">
                                                    <label>
                                                        {{ q.name }}
                                                    </label>
                                                </li>
                                            </ul>
                                        </fs-popover>
                                    </div>
                                </div>
                            </template>
                            <template
                                v-else-if="item === 'warrantyStartDate' || item === 'warrantyEndDate' || item === 'invoiceDate' || item === 'purchasedDate' || item === 'serviceEndDate' || item === 'serviceStartDate'">
                                <div class="header_cate">
                                    <div>{{ getTableTIle(item) }}</div>
                                    <fs-date-picker v-model="rangeData" range type="date" @change="handleDateChange(item)" />
                                </div>
                            </template>

                            <div class="header_cate" v-else>{{ getTableTIle(item) }}</div>
                        </div>
                        <div class="content" v-if="info.length > 0">
                            <ul>
                                <li class="item_box" v-for="(content, i) in info" :key="i + 'item'">
                                    <div class="item_content">
                                        <span v-if="!content[item]">{{ "- -" }}</span>
                                        <template v-else-if="item === 'sn'">
                                            <div v-if="content[item].split(',').length > 1" class="item_content_text" @click="SKUClick(content)">
                                                {{ $c("components.smallComponents.mSideBar.viewAll") }}
                                            </div>
                                            <div v-else class="item_content_text">
                                                {{ content[item] }}
                                            </div>
                                        </template>
                                        <template v-else-if="item === 'orderNumber' || item === 'invoiceNumber'">
                                            <div class="item_content_underline item_content_text" @click="underLineClick(content, item)">{{ content[item] }}</div>
                                        </template>
                                        <template v-else-if="item === 'sku'">
                                            <div class="item_content_sku">
                                                <div>{{ content[item] }}</div>
                                                <div v-if="content.isHybridCombo" class="item_content_flag">Hybrid Combo</div>
                                            </div>
                                        </template>
                                        <template v-else>
                                            <div class="item_content_text">{{ content[item] }}</div>
                                        </template>
                                    </div>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </template>
        </div>
        <div v-if="info.length === 0" class="empty">
            <div class="text-container">
                <div class="text-title"> {{ $c("pages.OrderReport.productReport.noRecordsFound") }}</div>
                <div class="text-content">{{ $c("pages.OrderReport.productReport.yourPurchasedProductsWill") }}</div>
            </div>
        </div>
        <fs-popup-new :show="SKUShow" @close="handleClose" transition="slide-up" width="480" :title="`SKU:${skuContent.sku}`" :isMDrawer="true">
            <div class="skuTip">
                <div class="skuTip_content">{{ $c("pages.OrderReport.productReport.allSerialNumber") }}:</div>
                <div class="skuTip_content">
                    {{ skuContent.sn }}
                </div>
            </div>
            <template slot="footer">
                <div class="btn">
                    <fs-button type="red" :text="$c('pages.OrderReport.productReport.copy')" @click="copyClick"></fs-button>
                </div>
            </template>
        </fs-popup-new>
    </div>
</template>

<script>
import ReportTable from "../ReportTable"
import Category from "./Customise/Category.vue"
import FsSelect from "@/components/FsSelect/FsSelect"
import FsPopover from "@/components/FsPopover"
import FsPopupNew from "@/components/FsPopupNew/FsPopupNew.vue"
import FsButton from "@/components/FsButton/FsButton.vue"
import { copyText } from "@/util/util"
import FsDatePicker from "@/components/FsDatePicker"
export default {
    components: {
        Category,
        FsSelect,
        FsPopover,
        FsPopupNew,
        FsButton,
        FsDatePicker,
    },
    mixins: [ReportTable],
    props: {
        info: {
            type: Array,
            default: () => [],
        },
        entry: {
            type: Array,
            default: () => [],
        },
        categories: {
            type: Array,
            default: () => [],
        },
    },
    data() {
        return {
            loading: false,
            popupLoading: false,
            types: [],
            popoverTypeShow: false,
            isCateFilter: false,
            selectedOrder: "",
            orderShow: false,
            orderStatus: [
                { name: this.$c("pages.OrderReport.productReport.sortAscending"), value: "asc" },
                { name: this.$c("pages.OrderReport.productReport.sortDescending"), value: "desc" },
                { name: this.$c("pages.OrderReport.productReport.clearSorting"), value: "" },
            ],
            SKUShow: false,
            skuContent: {},
            orderShow: {
                pn: false,
                sku: false,
                qty: false,
                orderNumber: false,
                invoiceNumber: false,
                invoiceDate: false,
                purchasedDate: false,
                purchasedBy: false,
                warrantyStr: false,
                price: false,
                shipToRegion: false,
                deliveryDate: false,
                trackingNumber: false,
            },
            rangeData: ["", ""],
            popperStyle: {
                padding: "4px",
            },
        }
    },
    mounted() {},
    methods: {
        clearSelctedCategory() {
            this.types = []
        },
        getTableTIle(index) {
            if ((index || index === 0) && this.TABLE_LIST_HEARD) {
                return this.TABLE_LIST_HEARD[index]
            }
            return index
        },
        handleTypeConfirm(values) {
            this.popoverTypeShow = false
            this.$emit("categoryClick", values)
        },
        handleCateMouseEnter() {
            this.isCateFilter = true
        },
        handleCateMouseLeave() {
            this.isCateFilter = false
        },
        handleClose() {
            this.SKUShow = false
        },
        copyClick() {
            this.SKUShow = false
            copyText(this.skuContent.sn)
            this.$message.success(this.$c("pages.OrderHistory.Copy_successfully"))
        },
        SKUClick(content) {
            this.skuContent = content
            this.SKUShow = true
        },

        underLineClick(content, item) {
            console.log("99999999item=", item, "content=", content)
            if (item === "orderNumber") {
                this.$router.push(this.localePath(`/order-detail?id=${content.orderId}&offline=${content.orderType}`))
            } else if (item === "invoiceNumber") {
                window.open(content.invoicePath, "_blank")
            }
        },
        handleDateChange(name) {
            console.log("88888888timerangeData=", this.rangeData)
            if (this.rangeData.includes("")) {
                return
            }
            this.$emit("orderStatusChange", this.rangeData, name)
        },
    },
}
</script>

<style lang="scss" scoped>
.table {
    display: flex;
}
.empty {
    height: 284px;
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    .text-container {
        text-align: center;
        .text-title {
            font-size: 16px;
            font-weight: 600;
            text-align: center;
            color: #19191a;
        }
        .text-content {
            font-size: 16px;
            font-weight: 400;
            text-align: center;
            color: #707070;
            margin-top: 8px;
        }
    }
}

.last_box {
    flex: 1;
    .table_item {
        .header {
            padding-right: 90px;
        }
        .content {
            > ul {
                > li {
                    > div {
                        margin-right: 24px;
                    }
                    &::after {
                        content: "";
                        width: 24px;
                        height: 0px;
                        background-color: #fff;
                        // background-color: #c00;
                        position: absolute;
                        bottom: -1px;
                        right: 0;
                        z-index: 2;
                        &.showDetail {
                            width: 0;
                        }
                    }
                }
            }
        }
    }
}

.handle_tip {
    // margin: -20px;
    // padding: 12px 0;
    b,
    i {
        @include font14;
    }

    .thin {
        color: #707070;
    }

    li {
        cursor: pointer;
        min-width: max-content;
        @include font14;
        font-weight: 400;
        color: $textColor1;
        padding: 8px 16px;
        display: flex;
        align-items: center;
        position: relative;
        z-index: 11;
        &:hover {
            background: #f7f7f7;
        }
    }
}

.icofont-down {
    transition: all 0.2s;
    color: $textColor3;
    font-size: 16px;
    font-weight: 400;
    &.icofont-down-up {
        color: #19191a;
        transform: rotateX(-180deg);
    }
    &:hover {
        color: #19191a;
    }
}
.table_item {
    .header {
        padding: 12px 24px;
        background-color: #f7f7f7;
        min-width: max-content;
        display: flex;
        align-content: center;
        height: 46px;
        @include font14;
        font-weight: 600;
        .header_cate {
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 4px;
            .icon-filter {
                align-self: center;
                @include font14();
                font-size: 18px;
                color: #707070;
                font-weight: 400;
                width: 16px;
                height: 16px;
                &:hover {
                    color: $textColor1;
                }
            }
            .arrow {
                align-self: center;
                // margin-top: 4px;
            }
            ::v-deep {
                .trigger {
                    > div {
                        display: flex;
                        gap: 4px;
                        .icofont-down {
                            padding-top: 2px;
                        }
                    }
                }
                .date-picker--value-wrapper {
                    display: none;
                }
                .date-picker--range-separator {
                    display: none;
                }
                .date-picker--container {
                    border: 0px solid #dcdfe6;
                    background-color: transparent;
                }
                .date-picker--date:not(.date-picker--custom-render),
                .date-picker--week:not(.date-picker--custom-render),
                .date-picker--quarter:not(.date-picker--custom-render) {
                    width: max-content;
                }
            }
        }
    }
    .content {
        > ul {
            > li {
                position: relative;
                padding: 28px 24px;
                border-bottom: 1px solid #e5e5e5;
                // height: 80px;
                .item_content {
                    min-width: max-content;
                    @include font14;
                    cursor: pointer;
                    display: flex;
                    .item_content_text {
                        width: 120px;
                        white-space: nowrap;
                        overflow: hidden;
                        text-overflow: ellipsis;
                    }
                    .item_content_underline {
                        text-decoration: underline;
                    }
                    .item_content_sku {
                        display: flex;

                        .item_content_flag {
                            color: #4b4b4d;
                            border-radius: 4px;
                            background: #f2f2f2;
                            padding: 0px 4px;
                            margin-left: 8px;
                        }
                    }
                    > .iconfont {
                        display: inline-block;
                        font-size: 12px;
                        margin-right: 12px;
                        &.active {
                            transform: rotate(-180deg);
                        }
                    }
                    .order_num {
                        text-decoration: underline;
                        &:hover {
                            color: $textColor3;
                        }
                        &.unChildren {
                            margin-left: 24px;
                        }
                    }
                }
                &:last-child {
                    border: none;
                }
                .row_child_content {
                    // margin-top: 28px;
                    margin-bottom: calc(40px - 28px);
                    > p {
                        padding: 20px 0;
                        height: 72px;
                        &:first-child {
                            padding: 12px 0;
                            // height: 46px;
                        }
                    }
                }
                .open_detail_box {
                    position: absolute;
                    // width:calc(926px - 48*2px);
                    left: 24px;
                    bottom: 40px;
                    z-index: 2;
                }
                &.showDetail {
                    background-color: #f7f7f7;
                    border: none;
                }
                &.no_border {
                    border: none;
                }
            }
        }
    }

    &.mark_left {
        .header {
            padding-left: 24px;
        }
        .content > ul > li {
            position: relative;
            padding-left: 24px;
            &::before {
                content: "";
                width: 24px;
                height: 0px;
                background-color: #fff;
                position: absolute;
                bottom: -1px;
                left: 0;
                z-index: 2;
                &.showDetail {
                    width: 0;
                }
            }
        }
    }
}

.btn {
    padding: 20px 32px;
    display: flex;
    justify-content: flex-end;
}
.skuTip {
    padding: 16px 20px;
    max-height: 150px;
    .skuTip_content {
        font-size: 14px;
        color: #707070;
        line-height: 22px;
        overflow-wrap: break-word;
    }
}
@media (max-width: 1024px) {
}
@media (max-width: 760px) {
    .table {
        display: flex;
        flex-direction: column;
    }
    .empty {
        display: none;
    }

    .last_box {
        .table_item {
            height: 42px;
            .header {
                padding-right: 0px;
            }
            .content {
                > ul {
                    > li {
                        border-bottom: 1px solid #e5e5e5;
                        > div {
                            margin-right: 0px;
                        }
                        &::after {
                            content: "";
                            width: 0px;
                            height: 0px;
                        }
                    }
                }
            }
        }
    }

    .table_item {
        display: flex;
        flex-direction: row;
        height: 42px;

        .header {
            padding: 12px 0px 12px 24px;
            background-color: #f7f7f7;
            border-bottom: 1px solid #e5e5e5;
            border-right: 1px solid #e5e5e5;
            min-width: max-content;
            display: flex;
            align-content: center;
            width: 195px;
            flex-shrink: 0;
            height: 42px;
            @include font14;
            .header_cate {
                display: flex;
                justify-content: center;
                align-items: center;
                .icon-filter {
                    align-self: center;
                    @include font14();
                    font-size: 18px;
                    color: #707070;
                    font-weight: 400;
                    width: 16px;
                    height: 16px;
                    &:hover {
                        color: $textColor1;
                    }
                }
                .arrow {
                    align-self: center;
                    margin-top: 4px;
                }
            }
        }
        .content {
            flex-grow: 1;

            > ul {
                display: flex;
                > li {
                    position: relative;
                    padding: 12px 24px;
                    border-bottom: 1px solid #e5e5e5;
                    border-right: 1px solid #e5e5e5;
                    height: 42px;
                    .item_content {
                        min-width: max-content;
                        @include font14;
                        cursor: pointer;
                        display: flex;
                        width: 160px;
                        .item_content_text {
                            width: 120px;
                            white-space: nowrap;
                            overflow: hidden;
                            text-overflow: ellipsis;
                        }

                        .order_num {
                            text-decoration: underline;
                            &:hover {
                                color: $textColor3;
                            }
                            &.unChildren {
                                margin-left: 24px;
                            }
                        }
                    }
                    &:last-child {
                        border-bottom: 1px solid #e5e5e5;
                    }
                }
            }
        }

        &.mark_left {
            .header {
                padding: 12px 0px 12px 24px;
            }
            .content > ul > li {
                &::before {
                    width: 0px;
                }
                position: relative;
                padding: 12px 24px;
                border-bottom: 1px solid #e5e5e5;
                border-right: 1px solid #e5e5e5;
                height: 42px;
            }
        }
    }
}
</style>
