<template>
    <div class="en_regist_box" v-loading.fullscreen="loading">
        <RegisterEntry v-if="regStep === 1"></RegisterEntry>
        <form class="form" @submit.prevent="submit" autocomplete="off" v-else>
            <p class="form_title">{{ accountType === 0 ? $c("pages.Login.org_form.account_info") : $c("pages.Login.org_form.title") }}</p>
            <template v-if="accountType === 0">
                <div class="user_name_box">
                    <div class="form_item">
                        <div class="label">{{ $c("form.form.first_name") }}{{ labelSuffix }}</div>
                        <div class="inp_box">
                            <input
                                type="text"
                                class="is_new"
                                aria-label="first_name"
                                :class="{ error_input: errors.first_name && isDeSite }"
                                @focus.stop="titlePoint('First Name')"
                                v-model="form.first_name"
                                @blur.stop="inputCheck('first_name')"
                                @input.stop="inputCheck('first_name')"
                                nls_fa_el_name="first_name" />
                            <validate-error :error="errors.first_name"></validate-error>
                        </div>
                    </div>
                    <div class="form_item">
                        <div class="label">{{ $c("form.form.last_name") }}{{ labelSuffix }}</div>
                        <div class="inp_box">
                            <input
                                type="text"
                                class="is_new"
                                aria-label="last_name"
                                :class="{ error_input: errors.last_name && isDeSite }"
                                @focus.stop="titlePoint('Last Name')"
                                v-model="form.last_name"
                                @blur.stop="inputCheck('last_name')"
                                @input.stop="inputCheck('last_name')"
                                nls_fa_el_name="last_name" />
                            <validate-error :error="errors.last_name"></validate-error>
                        </div>
                    </div>
                </div>
                <div class="form_item">
                    <div class="label">{{ $c("form.form.email_ddress") }}{{ labelSuffix }}</div>
                    <div class="inp_box">
                        <input
                            type="text"
                            ref="email"
                            class="is_new"
                            aria-label="Email"
                            @focus.stop="titlePoint('Email')"
                            :class="{ error_input: errors.email && isDeSite }"
                            v-model="form.email"
                            @input.stop="inputCheck('email')"
                            @blur.stop="emailBlur"
                            nls_fa_el_name="email" />
                        <validate-error :error="errors.email"></validate-error>
                    </div>
                    <div class="action" v-show="actionType">
                        {{ $c("pages.Login.FYS") }}
                    </div>
                </div>
                <div class="form_item">
                    <div class="label">{{ $c("form.form.password") }}{{ labelSuffix }}</div>
                    <PwdVerify ref="pwdVerify" v-model="form.password" @focus="titlePoint('Password')" :showRedBorder="isDeSite"></PwdVerify>
                </div>
                <div class="country_box">
                    <div class="form_item">
                        <div class="label">{{ $c("form.form.country") }} *</div>
                        <div class="inp_box">
                            <select-country :isNewStyle="true" :filter="reg_filter_country_list" @toogleClick="countryPoint" position="absolute" sort></select-country>
                            <validate-error :error="errors.country"></validate-error>
                        </div>
                    </div>
                    <div class="form_item" v-if="isShowState">
                        <div class="label">{{ $c("single.ProductReturnForm.state.tit") }} *</div>
                        <div class="inp_box">
                            <RegionSelect :isNewStyle="true" :setDefaultState="false" ref="regionSelect" @change="inputCheck('customer_state')" />
                            <validate-error :error="errors.customer_state"></validate-error>
                        </div>
                    </div>
                    <div class="form_item" v-if="isShowCitySelect">
                        <div class="label">{{ $c("form.form.city") }}{{ labelSuffix }}</div>
                        <div class="inp_box">
                            <fs-select :isNewStyle="true" :showDefaultValue="false" :options="city_list" v-model="form.city" @change="inputCheck('city')"></fs-select>
                            <validate-error :error="errors.city"></validate-error>
                        </div>
                    </div>
                </div>
            </template>
            <!-- 流程变更隐藏 -->
            <!-- <div class="form_item">
        <div class="label">{{ $c("form.form.company_name") }}</div>
        <div class="inp_box">
          <input
            type="text"
            aria-label="company_company"
            :class="{ error_input: errors.customers_company && isDeSite }"
            @focus.stop="titlePoint('Company Name')"
            v-model="form.customers_company"
            @blur.stop="inputCheck('customers_company')"
            nls_fa_el_name="company_company"
          />
          <validate-error :error="errors.customers_company"></validate-error>
        </div>
      </div>
      <div class="form_item">
        <div class="label">{{ $c("pages.Login.businessAccount.title") }}</div>
        <div class="inp_box">
          <input
            type="text"
            @focus.stop="titlePoint('Account Number')"
            :class="{ error_input: errors.account_number && isDeSite }"
            v-model="form.account_number"
            @blur.stop="accountNumberBlur"
          />
          <validate-error :error="errors.account_number"></validate-error>
        </div>
        <div
          class="action"
          v-show="accountNumberFocus"
        >
          {{ $c("pages.Login.businessAccount.tips") }}
        </div>
      </div> -->
            <!-- 新加坡手机验证 -->
            <!-- <SingaporeBox ref="singapore" v-if="isSingaporeRegister" /> -->
            <!-- 组织机构注册 -->
            <div class="org_form_wrap" v-if="accountType === 1">
                <!-- <p class="info_title">{{ $c("pages.Login.org_form.title") }}</p> -->
                <div class="form_item">
                    <div class="label">{{ $c("pages.Login.org_form.name.label") }}{{ labelSuffix }}</div>
                    <div class="inp_box">
                        <input
                            type="text"
                            class="is_new"
                            aria-label="company_name"
                            :class="{ error_input: errors.company_name }"
                            v-model="form.company_name"
                            @blur.stop="validateCompanyName"
                            @input.stop="inputCheck('company_name')"
                            nls_fa_el_name="company_name" />
                        <validate-error :error="errors.company_name"></validate-error>
                    </div>
                </div>
                <div class="form_item">
                    <div class="label">{{ $c("form.form.country") }}{{ labelSuffix }}</div>
                    <div class="country_select_box">
                        <select-country :isNewStyle="true" :filter="reg_filter_country_list" @toogleClick="countryPoint" position="absolute" sort></select-country>
                        <validate-error :error="errors.country"></validate-error>
                    </div>
                </div>
                <div class="form_item">
                    <div class="label">{{ $c("pages.Login.org_form.street_address.label") }}{{ labelSuffix }}</div>
                    <div class="inp_box">
                        <input
                            type="text"
                            class="is_new"
                            aria-label="street_address"
                            :class="{ error_input: errors.address_one }"
                            v-model="form.street_address"
                            @blur.stop="inputCheck('street_address')"
                            @input.stop="inputCheck('street_address')"
                            nls_fa_el_name="street_address" />
                        <validate-error :error="errors.address_one"></validate-error>
                    </div>
                </div>
                <div class="form_item">
                    <div class="label">{{ $c("pages.Login.org_form.street_address.label") }} 2</div>
                    <div class="inp_box">
                        <input
                            type="text"
                            class="is_new"
                            aria-label="street_address2"
                            :class="{ error_input: errors.address_two }"
                            v-model="form.street_address2"
                            nls_fa_el_name="street_address2"
                            @blur.stop="inputCheck('street_address2')"
                            @input.stop="inputCheck('street_address2')" />
                        <validate-error :error="errors.address_two"></validate-error>
                    </div>
                </div>
                <div class="form_item">
                    <div class="label">{{ $c("form.form.city") }}{{ labelSuffix }}</div>
                    <div class="inp_box">
                        <fs-select :isNewStyle="true" v-if="isShowCitySelect" :showDefaultValue="false" :options="city_list" v-model="form.city" @change="inputCheck('city')"></fs-select>
                        <input
                            v-else
                            class="is_new"
                            type="text"
                            aria-label="city"
                            :class="{ error_input: errors.city }"
                            v-model="form.city"
                            nls_fa_el_name="city"
                            @blur.stop="inputCheck('city')"
                            @input.stop="inputCheck('city')" />
                        <validate-error :error="errors.city"></validate-error>
                    </div>
                </div>
                <div class="state_zip_wrap">
                    <div class="form_item" v-if="isShowState">
                        <div class="label">{{ $c("form.form.state_province_region") }} *</div>
                        <div class="inp_box">
                            <RegionSelect :isNewStyle="true" :setDefaultState="false" ref="regionSelect" @change="inputCheck('state_province')" />
                            <validate-error :error="errors.state_province"></validate-error>
                        </div>
                    </div>
                    <div class="form_item">
                        <div class="label">{{ $c("form.form.zip_code") }}</div>
                        <div class="inp_box">
                            <input
                                type="text"
                                class="is_new"
                                aria-label="zip_code"
                                :class="{ error_input: errors.entry_postcode }"
                                v-model="form.zip_code"
                                nls_fa_el_name="zip_code"
                                @blur.stop="inputCheck('zip_code')"
                                @input.stop="inputCheck('zip_code')" />
                            <validate-error :error="errors.entry_postcode"></validate-error>
                        </div>
                    </div>
                </div>
                <div class="form_item">
                    <div class="label">{{ $c("form.form.phone_number") }}{{ labelSuffix }}</div>
                    <div class="inp_box">
                        <tel-code-new :isNewStyle="true" ref="telCode" :code.sync="form.tel_prefix" v-model="form.company_phone" @blur="inputCheck('company_phone')"></tel-code-new>
                        <validate-error :error="errors.company_phone"></validate-error>
                    </div>
                </div>
                <div class="form_item" v-show="taxLabel">
                    <div class="label">{{ taxLabel }}</div>
                    <div class="inp_box">
                        <input class="is_new" type="text" aria-label="tax_number" :class="{ error_input: errors.tax_number }" v-model="form.tax_number" nls_fa_el_name="tax_number" />
                        <validate-error :error="errors.tax_number"></validate-error>
                    </div>
                </div>
                <!-- <div class="form_item">
                    <p class="label">{{ $c("pages.Login.org_form.organization_size_label") }} {{ labelSuffix }}</p>
                    <fs-select :isNewStyle="true" :options="companySizeOption" v-model="form.organization_size" @change="inputCheck('organization_size')"></fs-select>
                    <validate-error :error="errors.organization_size"></validate-error>
                </div> -->
            </div>
            <template v-if="accountType === 1">
                <p class="form_title">{{ $c("pages.Login.org_form.account_info") }}</p>
                <div class="user_name_box">
                    <div class="form_item">
                        <div class="label">{{ $c("form.form.first_name") }}{{ labelSuffix }}</div>
                        <div class="inp_box">
                            <input
                                class="is_new"
                                type="text"
                                aria-label="first_name"
                                :class="{ error_input: errors.first_name && isDeSite }"
                                @focus.stop="titlePoint('First Name')"
                                v-model="form.first_name"
                                @blur.stop="inputCheck('first_name')"
                                @input.stop="inputCheck('first_name')"
                                nls_fa_el_name="first_name" />
                            <validate-error :error="errors.first_name"></validate-error>
                        </div>
                    </div>
                    <div class="form_item">
                        <div class="label">{{ $c("form.form.last_name") }}{{ labelSuffix }}</div>
                        <div class="inp_box">
                            <input
                                class="is_new"
                                type="text"
                                aria-label="last_name"
                                :class="{ error_input: errors.last_name && isDeSite }"
                                @focus.stop="titlePoint('Last Name')"
                                v-model="form.last_name"
                                @blur.stop="inputCheck('last_name')"
                                @input.stop="inputCheck('last_name')"
                                nls_fa_el_name="last_name" />
                            <validate-error :error="errors.last_name"></validate-error>
                        </div>
                    </div>
                </div>
                <div class="form_item">
                    <div class="label">{{ $c("form.form.email_ddress") }}{{ labelSuffix }}</div>
                    <div class="inp_box">
                        <input
                            class="is_new"
                            type="text"
                            ref="email"
                            aria-label="Email"
                            @focus.stop="titlePoint('Email')"
                            :class="{ error_input: errors.email && isDeSite }"
                            v-model="form.email"
                            @input.stop="inputCheck('email')"
                            @blur.stop="emailBlur"
                            nls_fa_el_name="email" />
                        <validate-error :error="errors.email"></validate-error>
                    </div>
                    <div class="action" v-show="actionType">
                        {{ $c("pages.Login.FYS") }}
                    </div>
                </div>
                <div class="form_item">
                    <div class="label">{{ $c("form.form.password") }}{{ labelSuffix }}</div>
                    <PwdVerify ref="pwdVerify" v-model="form.password" @focus="titlePoint('Password')" :showRedBorder="isDeSite"></PwdVerify>
                </div>
                <!-- 新加坡手机验证 -->
                <SingaporeBox ref="singapore" v-if="isSingaporeRegister" />
            </template>
            <div class="form_item agreement_bd">
                <label class="agreement_box">
                    <input type="checkbox" tabindex="0" class="chk" aria-label="agreen" v-model="form.checked" @change="inputCheck('checked')" nls_fa_el_name="checked" />
                    <p class="agreement" v-html="policyHtml"></p>
                </label>
                <validate-error :error="errors.checked"></validate-error>
            </div>
            <div class="form_item agreement_bd mt8" v-if="accountType === 1">
                <label class="agreement_box">
                    <input type="checkbox" tabindex="0" class="chk" aria-label="agreen" v-model="form.checkedOrg" @change="inputCheck('checkedOrg')" nls_fa_el_name="checked" />
                    <p class="agreement" v-html="policyHtmlOrg"></p>
                </label>
                <validate-error :error="errors.checkedOrg"></validate-error>
            </div>
            <fs-button class="btn-submit" tabindex="0" :text="submitBtnLabel" :loading="btn_loading" htmlType="submit"></fs-button>
            <!-- <p class="reg_bottom">
                {{ $c("pages.Login.Already_have_an_account") }} <nuxt-link @click.native="linkPoint" tabindex="0" :to="localePath({ name: 'login' })">{{ $c("pages.Login.Sign_In") }}</nuxt-link>
            </p> -->
            <div class="sign_btn">
                <p class="reg_bottom">
                    {{ $c("pages.Login.Already_have_an_account") }} <nuxt-link @click.native="linkPoint" tabindex="0" :to="localePath({ name: 'login' })">{{ $c("pages.Login.Sign_In") }}</nuxt-link>
                </p>
            </div>
        </form>
        <RegistPop :show="isshowforbidden" @close="closeForbidden" />
    </div>
</template>

<script>
import { mapState, mapGetters, mapActions } from "vuex"
import ValidateError from "@/components/ValidateError/ValidateError"
import FsButton from "@/components/FsButton/FsButton"
import SelectCountry from "@/components/SelectCountry/SelectCountry"
import TelCode from "@/components/TelCode/TelCode"
import { email_valdate, company_name_validate } from "@/constants/validate"
import AES from "@/util/AES.js"
import PwdVerify from "./PwdVerify.vue"
import GRecaptcha from "@/components/GRecaptcha/GRecaptcha"
import RegionSelect from "@/components/RegionSelect/RegionSelect"
import { setCookieOptions } from "@/util/util"
import { SG_AREA_COUNTRY_CODE } from "@/constants/common"
import SingaporeBox from "./SingaporeBox.vue"
import { getRecaptchaToken } from "@/util/grecaptchaHost"
import TelCodeNew from "@/components/TelCode/TelCodeNew.vue"
import FsSelect from "@/components/FsSelect/FsSelect.vue"
import RegisterEntry from "./RegisterEntry.vue"
import RegistPop from "./RegistPop.vue"

export default {
    layout: "account",
    name: "regist",
    components: {
        ValidateError,
        FsButton,
        SelectCountry,
        TelCode,
        GRecaptcha,
        PwdVerify,
        RegionSelect,
        SingaporeBox,
        TelCodeNew,
        FsSelect,
        RegisterEntry,
        RegistPop,
    },
    data() {
        return {
            loading: false,
            regStep: 1,
            accountType: 0, //0:个人 1:企业
            btn_loading: false,
            is_regist: 0,
            is_company_email: 0,
            isAsyncCheck: false,
            isCheckOrganizationName: false,
            isshowforbidden: false,
            companySizeOption: [
                // { name: this.$c("pages.Login.org_form.organization_size[0]"), value: 1 },
                { name: this.$c("pages.Login.org_form.organization_size[1]"), value: 2 },
                { name: this.$c("pages.Login.org_form.organization_size[2]"), value: 3 },
                { name: this.$c("pages.Login.org_form.organization_size[3]"), value: 4 },
            ],
            form: {
                first_name: "",
                last_name: "",
                email: "",
                password: "",
                customers_company: "",
                checked: false,
                checkedOrg: false,
                account_number: "",
                company_name: "",
                street_address: "",
                street_address2: "",
                city: "",
                state_province: "",
                zip_code: "",
                company_phone: "",
                tax_number: "",
                tel_prefix: "",
                organization_size: 1,
            },
            errors: {
                first_name: "",
                last_name: "",
                email: "",
                password: "",
                country: "",
                customers_company: "",
                checked: "",
                checkedOrg: "",
                customer_state: "",
                account_number: "",
                company_name: "",
                address_one: "", //street_address
                address_two: "", // street_address2
                city: "",
                state_province: "",
                entry_postcode: "", // zip_code
                company_phone: "",
                tax_number: "",
                organization_size: "",
            },
            actionType: false,
            OptionalType: true,
            accountNumberFocus: false,
            isValidCompanyNumber: true, // 是否是有效企业账号,可选，所以默认true
        }
    },
    asyncData({ app, $axios, redirect }) {
        return $axios.post("/api/user/info?getMore=1").then((res) => {
            if (res.data.isLogin) {
                redirect(app.localePath({ name: "my-account" }))
            }
        })
    },
    watch: {
        isShowCitySelect: {
            handler(val) {
                if (this.form.city) {
                    this.form.city = ""
                }
            },
        },
    },
    computed: {
        ...mapState({
            select_country_id: (state) => state.selectCountry.select_country_id,
            select_country_code: (state) => state.selectCountry.select_country_code,
            current_country_name: (state) => state.selectCountry.current_country_name,
            website: (state) => state.webSiteInfo.website,
            countries_id: (state) => state.webSiteInfo.countries_id,
            pageGroup: (state) => state.ga.pageGroup,
            reg_filter_country_list: (state) => {
                let arr = []
                state.selectCountry.country_list.forEach((item) => {
                    if (item.iso_code !== "CN") {
                        arr.push(item.iso_code)
                    }
                })
                return arr
            },
            city_list: (state) => state.selectCountry.city_list,
        }),
        ...mapGetters({
            isShowState: "selectCountry/isShowState",
            you_country_label: "selectCountry/you_country_label",
            isSingapore: "webSiteInfo/isSingapore",
            isShowCitySelect: "selectCountry/isShowCitySelect",
        }),
        isDeSite() {
            return this.website == "de"
        },
        labelSuffix() {
            const msg = this.website === "jp" ? "（必須）" : " *"
            return msg
        },
        policyHtml() {
            return this.$c("pages.Login.I_agree_to_FSs")
                .replace("XXX1", this.localePath({ name: "privacy-policy" }))
                .replace("XXX2", this.localePath({ name: "terms-of-use" }))
        },
        policyHtmlOrg() {
            return this.$c("pages.Login.i_agree_to_fs_business").replace("XXX", this.localePath("/policies/business_accounts_terms_conditions"))
        },
        isSingaporeRegister() {
            return this.isSingapore && SG_AREA_COUNTRY_CODE.includes(this.select_country_code)
        },
        taxLabel() {
            const vatCountryList = [
                "AT",
                "DE",
                "LU",
                "CH",
                "NL",
                "MT",
                "DK",
                "FI",
                "SE",
                "CY",
                "HR",
                "CZ",
                "LT",
                "PL",
                "SK",
                "SI",
                "BG",
                "EE",
                "GR",
                "HU",
                "RO",
                "LV",
                "NO",
                "BE",
                "FR",
                "IT",
                "ES",
                "PT",
                "GB",
                "IE",
                "IS",
            ]
            if (vatCountryList.includes(this.select_country_code)) {
                return "VAT"
            } else if (this.select_country_code === "MD") {
                return "IDNO"
            } else if (this.select_country_code === "MC") {
                return "NIE"
            } else if (this.select_country_code === "BR") {
                return "CNPJ"
            } else if (this.select_country_code === "MX") {
                return "RFC"
            } else if (this.select_country_code === "PA") {
                return "RUC"
            } else if (["PR", "US"].includes(this.select_country_code)) {
                return "EIN"
            } else if (this.select_country_code === "CA") {
                return "BN"
            } else if (this.select_country_code === "ID") {
                return "NIB"
            } else if (["MY", "MM", "FJ"].includes(this.select_country_code)) {
                return "CRN"
            } else if (this.select_country_code === "SG") {
                return "UEN"
            } else if (this.select_country_code === "VN") {
                return "MSD"
            } else if (this.select_country_code === "JP") {
                return "CN"
            } else if (this.select_country_code === "AU") {
                return "ABN"
            } else if (this.select_country_code === "NZ") {
                return "NZBN"
            } else {
                return ""
            }
        },
        submitBtnLabel() {
            const { type = "0" } = this.$route.query
            console.log(type, "type")
            return type === "1" ? this.$c("pages.Login.create_account_title.business") : this.$c("pages.Login.create_account_title.person")
        },
    },
    created() {
        this.form.checked = false
        this.$watch("$route", (to) => {
            if (to.query && to.query?.type) {
                console.log("url query", to.query?.type)
                this.regStep = Number(to.query.step)
                this.accountType = Number(to.query.type)
                return
            }
        })
        if (this.$route.query && this.$route.query?.type) {
            console.log("url query", this.$route.query?.type)
            this.regStep = Number(this.$route.query.step)
            this.accountType = Number(this.$route.query.type)
        }
    },
    methods: {
        validateCompanyName() {
            // 验证公司名称

            if (!this.form.company_name.replace(/\s+/g, "")) {
                this.errors.company_name = this.$c("pages.Login.org_form.name.error.required")
                return
            }
            const params = {
                company_name: this.form.company_name,
            }
            this.$axios
                .post("api/user/checkOrganizationName", params)
                .then((res) => {
                    console.log("res", res)
                    if (!res.data.result) {
                        this.$message.error(this.$c("pages.Login.org_form.name.error.checkOrganizationName"))
                        this.errors.company_name = this.$c("pages.Login.org_form.name.error.required")
                        this.isCheckOrganizationName = false
                    } else {
                        this.errors.company_name = ""
                        this.isCheckOrganizationName = true
                    }
                })
                .catch((err) => {})
        },

        closeForbidden() {
            this.isshowforbidden = false
        },
        ...mapActions({
            getCart: "cart/getCart",
        }),
        entryClick(index) {
            // 0:个人 1:企业
            this.accountType = index
        },
        backOtherType() {
            this.loading = true
            this.accountType = this.accountType === 0 ? 1 : 0
            this.$router.replace(this.localePath({ path: "/register.html", query: { step: 2, type: this.accountType } })).then(() => {
                this.$router.go(0)
            })
        },
        phoneError(error) {
            console.log("init", error)
            this.errors.company_phone = error
        },
        emailBlur() {
            // this.actionType = false
            if (!this.form.email.replace(/\s+/g, "")) {
                this.errors.email = this.$c("form.validate.email.email_required")
            }
            if (this.errors.email || !this.form.email.replace(/\s+/g, "")) {
                return
            }
            this.is_regist = 0
            this.is_company_email = 0
            const params = {
                email_address: this.form.email,
            }
            if (this.accountType === 1) {
                params.customer_is_business_type = 1
            }
            this.$axios
                .post("/api/user/checkEmail", params)
                .then((res) => {
                    this.is_regist = res.data.is_registered
                    this.is_company_email = res.data.is_company_email
                    if (res.data.is_registered === 1) {
                        this.errors.email = this.$c("pages.Login.Account_already_exists").replace("XXX", this.localePath({ name: "login" }))
                    } else if (res.data.is_company_email === 0) {
                        this.errors.email = this.$c("pages.Login.please_use_corporate_email")
                    } else {
                        this.errors.email = ""
                    }
                    this.isAsyncCheck = true
                })
                .catch((err) => {})
        },
        async accountNumberBlur() {
            try {
                if (!this.form.account_number) {
                    this.isValidCompanyNumber = true
                    this.errors.account_number = ""
                    return
                }
                this.isValidCompanyNumber = false
                const params = {
                    account_number: this.form.account_number,
                }
                await this.$axios.get("/api/user/verifyAccountNumber", { params })
                this.isValidCompanyNumber = true
                this.errors.account_number = ""
            } catch (error) {
                this.errors.account_number = error.message
            }
        },
        inputCheck(attr, step) {
            let flag = false
            if (attr === "first_name") {
                if (!this.form[attr].replace(/\s+/g, "")) {
                    this.errors[attr] = this.$c("form.validate.first_name.first_name_required")
                    flag = true
                } else {
                    if (this.form[attr].replace(/\s+/g, "").length > 40) {
                        this.errors[attr] = this.$c("form.validate.first_name.first_name_max")
                        flag = true
                    } else if (this.form[attr].replace(/\s+/g, "").length < 1 && this.website === "jp") {
                        this.errors[attr] = this.$c("form.validate.first_name.first_name_min")
                        flag = true
                    } else {
                        this.errors[attr] = ""
                    }
                }
            } else if (attr === "last_name") {
                if (!this.form[attr].replace(/\s+/g, "")) {
                    this.errors[attr] = this.$c("form.validate.last_name.last_name_required")
                    flag = true
                } else {
                    if (this.form[attr].replace(/\s+/g, "").length > 40) {
                        this.errors[attr] = this.$c("form.validate.last_name.last_name_max")
                        flag = true
                    } else if (this.form[attr].replace(/\s+/g, "").length < 2 && this.website !== "jp") {
                        this.errors[attr] = this.$c("form.validate.last_name.last_name_min")
                        flag = true
                    } else if (this.form[attr].replace(/\s+/g, "").length < 1 && this.website === "jp") {
                        this.errors[attr] = this.$c("form.validate.last_name.last_name_min")
                        flag = true
                    } else {
                        this.errors[attr] = ""
                    }
                }
            } else if (attr === "email") {
                if (step && this.isAsyncCheck) {
                    if (this.is_regist === 1) {
                        return (flag = true)
                    } else if (this.is_company_email === 0) {
                        return (flag = true)
                    }
                } else {
                    if (!this.form[attr].replace(/\s+/g, "")) {
                        this.errors[attr] = this.$c("form.validate.email.email_required")
                        flag = true
                    } else {
                        if (!email_valdate.test(this.form[attr].replace(/\s+/g, ""))) {
                            this.errors[attr] = this.$c("form.validate.email.email_valid")
                            flag = true
                        } else {
                            this.errors[attr] = ""
                        }
                    }
                }
            } else if (attr === "password") {
                flag = this.$refs.pwdVerify.externalTriggerVerify()
            } else if (attr === "customers_company") {
                this.OptionalType = true
                if (!this.form[attr].replace(/\s+/g, "")) {
                    this.errors[attr] = ""
                } else {
                    if (!company_name_validate.test(this.form[attr].replace(/\s+/g, ""))) {
                        this.errors[attr] = this.$c("pages.confirmOrder.form.company_name_validate")
                        flag = true
                    } else {
                        this.errors[attr] = ""
                    }
                }
            } else if (attr === "checked") {
                if (this.form[attr]) {
                    this.errors[attr] = ""
                } else {
                    this.errors[attr] = this.$c("pages.Login.Please_make_sure_you_agree_Privacy_Policy")
                    flag = true
                }
                // }
            } else if (attr === "checkedOrg") {
                if (this.form[attr]) {
                    this.errors[attr] = ""
                } else {
                    this.errors[attr] = this.$c("pages.Login.place_agree_to_fs_business")
                    flag = true
                }
                // }
            } else if (attr === "customer_state") {
                if (this.isShowState) {
                    if (!this.$refs.regionSelect.state) {
                        this.errors[attr] = this.$c("pages.Login.rquired_State")
                        flag = true
                    } else {
                        this.errors[attr] = ""
                    }
                }
            } else if (attr === "state_province") {
                if (this.isShowState) {
                    if (!this.$refs.regionSelect.state) {
                        this.errors[attr] = this.$c("pages.Login.rquired_State")
                        flag = true
                    } else {
                        this.errors[attr] = ""
                    }
                }
            } else if (attr === "company_name") {
                if (!this.form[attr].replace(/\s+/g, "")) {
                    this.errors[attr] = this.$c("pages.Login.org_form.name.error.required")
                    flag = true
                } else {
                    // 长度大于2小于120
                    console.log("isCheckOrganizationName", this.isCheckOrganizationName)
                    if (this.isCheckOrganizationName) {
                        if (this.form[attr].replace(/\s+/g, "").length < 2 || this.form[attr].replace(/\s+/g, "").length > 120) {
                            this.errors[attr] = this.$c("pages.Login.org_form.name.error.min_max")
                            flag = true
                        } else {
                            this.errors[attr] = ""
                        }
                    } else {
                        this.errors[attr] = this.$c("pages.Login.org_form.name.error.required")
                        flag = true
                    }
                }
            } else if (attr === "street_address") {
                if (!this.form[attr].replace(/\s+/g, "")) {
                    this.errors["address_one"] = this.$c("form.validate.address.address_required")
                    flag = true
                } else {
                    // 长度大于2小于35
                    if (this.form[attr].replace(/\s+/g, "").length < 2 || this.form[attr].replace(/\s+/g, "").length > 35) {
                        this.errors["address_one"] = this.$c("form.validate.address.address_validate")
                        flag = true
                    } else {
                        this.errors["address_one"] = ""
                    }
                }
            } else if (attr === "city") {
                if (!this.form[attr].replace(/\s+/g, "")) {
                    this.errors[attr] = this.$c("form.validate.city.city_required")
                    flag = true
                } else {
                    if (this.form[attr].replace(/\s+/g, "").length < 2 || this.form[attr].replace(/\s+/g, "").length > 40) {
                        this.errors[attr] = this.$c("form.validate.city.city_validate")
                        flag = true
                    } else {
                        this.errors[attr] = ""
                    }
                }
            } else if (attr === "state") {
                if (this.isShowState) {
                    if (!this.$refs.regionSelect.state) {
                        this.errors[attr] = this.$c("pages.Login.rquired_State")
                        flag = true
                    } else {
                        this.errors[attr] = ""
                    }
                }
            } else if (attr === "company_phone") {
                this.errors[attr] = this.$refs.telCode ? this.$refs.telCode.validFn() : ""
                // if (!this.form[attr].replace(/\s+/g, '')) {
                //   // console.log('company_phone1111');
                //   // this.errors[attr] = this.$c("form.validate.phone.phone_required")
                //   // console.log(this.errors[attr]);
                //   flag = true
                // }
                if (this.errors[attr]) {
                    flag = true
                }
            } else if (attr === "zip_code") {
                //选填，文本框，字段长度2<x<10
                if (!this.form[attr].replace(/\s+/g, "")) {
                    this.errors[attr] = ""
                } else {
                    if (this.form[attr].replace(/\s+/g, "").length < 2 || this.form[attr].replace(/\s+/g, "").length > 10) {
                        this.errors["entry_postcode"] = this.$c("pages.Login.org_form.zip_code.min_max")
                        flag = true
                    } else {
                        this.errors["entry_postcode"] = ""
                    }
                }
            } else if (attr === "tax_number") {
                // 选填。文本框，字段长度2<x<40。
                if (!this.form[attr].replace(/\s+/g, "")) {
                    this.errors[attr] = ""
                } else {
                    if (this.form[attr].replace(/\s+/g, "").length < 2 || this.form[attr].replace(/\s+/g, "").length > 40) {
                        this.errors[attr] = this.$c("pages.Login.org_form.tax_id.min_max")
                        flag = true
                    } else {
                        this.errors[attr] = ""
                    }
                }
            }

            return flag
        },

        async submit() {
            let arr = []
            let attr = ["first_name", "last_name", "email", "password", "customers_company", "checked", "customer_state", "state_province"]
            const orgFields = ["company_name", "street_address", "city", "state", "company_phone"]
            const validFields = this.accountType === 0 ? attr : [...attr, ...orgFields, "checkedOrg"] // ★加上checkedOrg
            validFields.map((item) => {
                let f = this.inputCheck(item, "submit-check")
                arr.push(f)
            })
            const singaporeParams = this.$refs.singapore ? this.$refs.singapore.singaporeBoxSubmit() : {}
            if (arr.includes(true) || !singaporeParams || this.is_regist || !this.isValidCompanyNumber || this.btn_loading) {
                this.buriedPointMixin("undefined", "sign_up_fail")
                return
            }

            this.btn_loading = true
            const { recaptchaTp, headers = {}, errorMsg = "grecaptcha_error" } = await getRecaptchaToken()
            if (!recaptchaTp) {
                this.btn_loading = false
                this.$message.error(this.$c(`pages.Login.regist.${errorMsg}`))
                return
            }
            let obj = {
                first_name: this.form.first_name,
                last_name: this.form.last_name,
                email: this.form.email,
                password: AES.encrypt(this.form.password, "_-yu_xuan_3507-_", "fs_com_phone2016"),
                country: this.select_country_id,
                customers_company: this.form.customers_company || undefined,
                account_number: this.form.account_number,
                city: this.form.city || undefined,
                ...singaporeParams,
            }
            let orgObj = {
                customer_is_business_type: 1, //标识
                first_name: this.form.first_name,
                last_name: this.form.last_name,
                email: this.form.email,
                password: AES.encrypt(this.form.password, "_-yu_xuan_3507-_", "fs_com_phone2016"),
                company_name: this.form.company_name,
                entry_country_id: this.select_country_id,
                address_one: this.form.street_address,
                address_two: this.form.street_address2,
                city: this.form.city,
                entry_postcode: this.form.zip_code,
                company_phone: this.form.company_phone,
                tax_number: this.form.tax_number,
                organization_size: this.form.organization_size,
            }
            if (this.$refs.regionSelect) {
                obj.customer_state = this.$refs.regionSelect.state || ""
                orgObj.state = this.$refs.regionSelect.state || ""
            }
            const params = this.accountType === 0 ? obj : orgObj // 0:个人 1:企业
            console.log(params)
            this.$axios
                .post("/api/user/register", params, { headers })
                .then((res) => {
                    if (res.data.access_token) {
                        this.$cookies.set("token_new", res.data.access_token)
                    }
                    this.getCart()
                    this.buriedPointMixin("undefined", this.accountType === 0 ? "personal_regist_success" : "org_regist_success")
                    if (window.yaCounter71412688) {
                        yaCounter71412688.reachGoal("registrations", function () {})
                    }
                    if (window.VWO) {
                        window.VWO = window.VWO || []
                        let formInstance = document.querySelector(".en_regist_box .form")
                        window.VWO.push(["nls.formAnalysis.markSuccess", formInstance, 1])
                    }
                    this.$router.replace(this.localePath({ name: "state-feedback", query: { type: 1, regEmail: AES.encrypt(this.form.email, "_-yu_xuan_3507-_", "fs_com_phone2016") } }))
                })
                .catch((err) => {
                    this.btn_loading = false
                    this.buriedPointMixin("undefined", "sign_up_fail")
                    if (window.VWO) {
                        window.VWO = window.VWO || []
                        let formInstance = document.querySelector(".en_regist_box .form")
                        window.VWO.push(["nls.formAnalysis.markSuccess", formInstance, 0])
                    }
                    if (err.code === 422) {
                        if (err.errors) {
                            for (let attr in err.errors) {
                                this.errors[attr] = err.errors[attr]
                            }
                        }
                    }

                    if (err.code === 403) {
                        // this.$message.error(err.message)
                        this.isshowforbidden = true //注册验证报错，显示提示弹窗
                    }
                    if (err.code === 400) {
                        this.$message.error(err.message)
                    }
                })
        },
        titlePoint(title) {
            switch (title) {
                case "Email":
                    this.actionType = true
                    break
                case "Company Name":
                    this.OptionalType = false
                    break
                case "Account Number":
                    this.accountNumberFocus = true
                    break
            }

            this.buriedPointMixin(`Regist-${title} Input`, "regist_information")
        },
        countryPoint(show) {
            if (show) {
                this.buriedPointMixin(`Country Drop-Down_${this.current_country_name}`, "regist_information")
            }
        },
        linkPoint() {
            this.buriedPointMixin("Sign In", "login_button")
        },
        buriedPointMixin(eventLabel, eventAction) {
            window.dataLayer &&
                window.dataLayer.push({
                    event: "uaEvent",
                    eventCategory: this.pageGroup,
                    eventAction,
                    eventLabel,
                    nonInteraction: false,
                })
        },
    },
}
</script>

<style lang="scss" scoped>
.form {
    .form_title {
        color: $textColor1;
        @include font14;
        font-weight: 600;
        margin-bottom: 16px;
    }
    // margin-top: 20px;
    .back_btn_wrap {
        margin-bottom: 26px;
        display: flex;
        align-items: center;
        cursor: pointer;
        .icon {
            display: block;
            width: 12px;
            height: 12px;
            margin-right: 4px;
        }
        .btn_text {
            @include font13;
            color: $textColor1;
            &:hover {
                text-decoration: underline;
                // 下划线的距离
                // text-decoration-thickness: 1px;
                // text-underline-offset: 2px;
            }
        }
    }
    // .user_name_box,
    .country_box {
        display: flex;
        flex-direction: column;
        .form_item {
            flex: 1;
            min-width: 0;
            // & + .form_item {
            //     margin-left: 12px;
            // }
        }
    }
    .user_name_box {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        column-gap: 12px;
    }
    .country_box {
        @include mediaM {
            display: block;
            .form_item + .form_item {
                margin-left: 0;
            }
        }
    }
    .country_select_box {
        // .select-country {
        //     &::v-deep {
        //         .select-country-active {
        //             background-color: #f6f6f8;
        //             border-color: transparent;
        //             &:hover {
        //                 background: linear-gradient(0deg, rgba(25, 25, 26, 0.04), rgba(25, 25, 26, 0.04)), #f6f6f8;
        //             }
        //             &.select-country-active-border {
        //                 border-color: #707070;
        //             }
        //         }
        //     }
        // }
    }
    .org_form_wrap {
        .info_title {
            margin-bottom: 16px;
            @include font16;
            color: #19191a;
            font-weight: 600;
        }
        .state_zip_wrap {
            display: flex;
            align-items: flex-start;
            // gap: 16px;
            flex-direction: column;
            .form_item {
                flex: 1;
                width: 100%;
            }
        }
    }
    .form_item {
        margin-bottom: 16px;
        .label {
            display: flex;
            align-items: center;
            color: $textColor3;
            margin-bottom: 4px;
            @include font12;
        }
        .action {
            @include font13();
            color: #707070;
            margin-top: 4px;
        }
        .agreement_box {
            display: flex;
            align-items: center;
            cursor: pointer;
            .chk {
                margin-top: 1px;
                margin-right: 8px;
                width: 14px;
                height: 14px;
                font-size: 14px;
            }
            .agreement {
                @include font13;
                color: $textColor3;
                &::v-deep {
                    a {
                        color: #0060bf;
                        &:hover {
                            text-decoration: underline;
                            // color: #707070;
                        }
                        &:focus-visible {
                            @include focusVisible;
                        }
                    }
                }
            }
            &:hover {
                .chk::before {
                    color: #707070;
                }
            }
        }
        .error_input {
            // @include errorInput;
            // border: 1px solid $textColor4;
        }
        // .fs-select {
        //     &::v-deep {
        //         .fs-select-active {
        //             background-color: #f6f6f8;
        //         }
        //     }
        // }
    }
    .agreement_bd {
        margin-bottom: 0px;
    }
    .mt8 {
        margin-top: 8px;
    }
    .mt16 {
        margin-top: 16px;
    }
    .btn-submit {
        margin: 16px 0;
        width: 100%;
        // border-radius: 21px;
        // border-radius: 9999px;

        &:hover {
            &::before {
                // border-radius: 9999px;
            }
        }
    }
    .sign_btn {
        width: 100%;
    }
    .reg_bottom {
        background-color: #f6f6f8;
        padding: 11px 0;
        border-radius: 4px;
        @include font12;
        color: $textColor1;
        // font-weight: 600;
        text-align: center;
        // @media (max-width: 960px) {
        //     margin-bottom: 12px;
        // }
    }
}
// ::v-deep {
//     .inp_box {
//         input {
//             background-color: #f6f6f8;
//             border-color: transparent;
//             &:focus {
//                 border-color: #707070;
//             }
//             &:hover {
//                 background: linear-gradient(0deg, rgba(25, 25, 26, 0.04), rgba(25, 25, 26, 0.04)), #f6f6f8;
//             }
//         }
//         .select-country-active,
//         .select-label {
//             background-color: #f6f6f8;
//             border-color: transparent;
//             &:hover {
//                 background: linear-gradient(0deg, rgba(25, 25, 26, 0.04), rgba(25, 25, 26, 0.04)), #f6f6f8;
//             }
//             &:focus {
//                 border-color: #707070;
//             }
//             &.select-country-active-border {
//                 border-color: #707070;
//             }
//         }
//         .select-label {
//             &.active {
//                 border-color: #707070;
//             }
//         }
//     }
//     .fs-select {
//         .fs-select-active {
//             border-color: transparent;
//             &:hover {
//                 background: linear-gradient(0deg, rgba(25, 25, 26, 0.04), rgba(25, 25, 26, 0.04)), #f6f6f8;
//             }
//             &.fs-select-active-border {
//                 border-color: #707070;
//             }
//         }
//     }
//     .tel-code-new {
//         .code {
//             border-color: transparent;
//         }
//     }
// }
</style>
