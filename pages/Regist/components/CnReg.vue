<template>
    <div class="cn_reg">
        <!-- <h2 class="welcome">{{ $c("pages.Login.Create_an_account") }}</h2> -->
        <div class="reg">
            {{ $c("pages.Login.Already_have_an_account") }}
            <nuxt-link :to="localePath({ name: 'login' })">{{ $c("pages.Login.Sign_In") }}</nuxt-link>
        </div>
        <ul class="cn_reg_type">
            <li v-for="(t, i) in reg_type_list" :key="i" @click.stop="selectRegType(t)" :class="{ active: reg_type == t.type }">{{ t.name }}</li>
        </ul>
        <form class="form" @submit.prevent="submit">
            <div class="user_name_box">
                <div class="form_item">
                    <div class="label">{{ $c("form.form.last_name01") }}</div>
                    <div class="inp_box">
                        <input
                            type="text"
                            class="is_new"
                            :class="{ error_input: errors.last_name }"
                            @focus.stop="titlePoint('Last Name')"
                            v-model="form.last_name"
                            @blur.stop="inputCheck('last_name')"
                            nls_fa_el_name="last_name" />
                        <!-- <validate-error :error="errors.last_name"></validate-error> -->
                        <p class="new_err_tips" v-show="errors.last_name">
                            <i class="icon iconfont">&#xe66a;</i>
                            <span>{{ errors.last_name }}</span>
                        </p>
                    </div>
                </div>
                <div class="form_item">
                    <div class="label">{{ $c("form.form.first_name01") }}</div>
                    <div class="inp_box">
                        <input
                            type="text"
                            class="is_new"
                            :class="{ error_input: errors.first_name }"
                            @focus.stop="titlePoint('First Name')"
                            v-model="form.first_name"
                            @blur.stop="inputCheck('first_name')"
                            nls_fa_el_name="first_name" />
                        <!-- <validate-error :error="errors.first_name"></validate-error> -->
                        <p class="new_err_tips" v-show="errors.first_name">
                            <i class="icon iconfont">&#xe66a;</i>
                            <span>{{ errors.first_name }}</span>
                        </p>
                    </div>
                </div>
            </div>
            <div class="form_item" v-if="reg_type == 'email_reg'">
                <div class="label">{{ $c("form.form.email_ddress") }}</div>
                <div class="inp_box">
                    <input
                        type="text"
                        class="is_new"
                        @focus.stop="titlePoint('Email')"
                        :class="{ error_input: errors.email }"
                        v-model="form.email"
                        @input.stop="inputCheck('email')"
                        @blur.stop="emailBlur"
                        nls_fa_el_name="email" />
                    <!-- <validate-error :error="errors.email"></validate-error> -->
                    <p class="new_err_tips" v-show="errors.email">
                        <i class="icon iconfont">&#xe66a;</i>
                        <span v-html="errors.email"></span>
                    </p>
                </div>
                <div class="action" v-show="actionType">
                    {{ $c("pages.Login.FYS") }}
                </div>
            </div>
            <div class="form_item" v-if="reg_type == 'mobile_reg'">
                <!-- <div class="label">{{$c('pages.Login.Email_Address')}}</div> -->
                <div class="label">{{ $c("form.form.phone_number") }}</div>
                <div class="inp_box">
                    <input
                        type="text"
                        class="is_new"
                        :class="{ error_input: errors.phone_number }"
                        @focus.stop="titlePoint('Phone Number')"
                        v-model="form.phone_number"
                        @blur.stop="inputCheck('phone_number')"
                        nls_fa_el_name="phone_number" />
                    <!-- <validate-error :error="errors.phone_number"></validate-error> -->
                    <p class="new_err_tips" v-show="errors.phone_number">
                        <i class="icon iconfont">&#xe66a;</i>
                        <span>{{ errors.phone_number }}</span>
                    </p>
                </div>
            </div>
            <div class="form_item form_item_verification">
                <!-- <div class="label">{{$c('pages.Login.Email_Address')}}</div> -->
                <div class="label">{{ $c("form.validate.verification.name") }}</div>
                <div class="inp_box input_box_verification">
                    <span @click.stop="getVerification" :class="{ disabled: verification.loading }">{{ verification.txt }}</span>
                    <input type="text" class="is_new" :class="{ err_input: errors.verificationCode }" v-model.trim="form.verificationCode" @input="inputCheck('verificationCode')" />
                    <!-- <validate-error :error="errors.verificationCode"></validate-error> -->
                    <p class="new_err_tips" v-show="errors.verificationCode">
                        <i class="icon iconfont">&#xe66a;</i>
                        <span>{{ errors.verificationCode }}</span>
                    </p>
                </div>
            </div>
            <div class="form_item">
                <div class="label">{{ $c("form.form.password") }}</div>
                <div class="inp_box">
                    <PwdVerify ref="pwdVerify" v-model="form.password" @focus="titlePoint('Password')"></PwdVerify>
                    <p class="new_err_tips" v-show="errors.password">
                        <i class="icon iconfont">&#xe66a;</i>
                        <span>{{ errors.password }}</span>
                    </p>
                </div>
            </div>
            <div class="form_item">
                <div class="label">
                    {{ $c("form.form.company_name") }}
                </div>
                <div class="inp_box">
                    <input
                        type="text"
                        class="is_new"
                        :class="{ error_input: errors.customers_company }"
                        @focus.stop="titlePoint('Company Name')"
                        v-model="form.customers_company"
                        @blur.stop="validateCompanyName"
                        nls_fa_el_name="company_company" />
                    <!-- <validate-error :error="errors.customers_company"></validate-error> -->
                    <p class="new_err_tips" v-show="errors.customers_company">
                        <i class="icon iconfont">&#xe66a;</i>
                        <span>{{ errors.customers_company }}</span>
                    </p>
                </div>
            </div>
            <!-- <div class="form_item">
                <div class="label">{{ $c("pages.Login.businessAccount.title") }}</div>
                <div class="inp_box">
                    <input type="text" class="is_new" @focus.stop="titlePoint('Account Number')" :class="{ error_input: errors.account_number }" v-model="form.account_number" @blur.stop="accountNumberBlur" />
                    <validate-error :error="errors.account_number"></validate-error>
                </div>
                <div class="action" v-show="accountNumberFocus">
                    {{ $c("pages.Login.businessAccount.tips") }}
                </div>
            </div> -->
            <div class="form_item agreement_bd">
                <div class="agreement_box">
                    <input type="checkbox" class="chk" v-model="form.checked" @change.stop="inputCheck('checked')" nls_fa_el_name="checked" />
                    <div class="agreement">
                        <p
                            class="info1"
                            v-html="
                                $c('pages.Login.I_agree_to_FSs')
                                    .replace('XXX1', localePath({ name: 'privacy-policy' }))
                                    .replace('XXX2', localePath({ name: 'terms-of-use' }))
                            "></p>
                        <p
                            class="info2"
                            v-html="
                                $c('pages.Login.By_clicking_the_button_below')
                                    .replace('XXX1', localePath({ name: 'privacy-policy' }))
                                    .replace('XXX2', localePath({ name: 'terms-of-use' }))
                            "></p>
                    </div>
                </div>

                <!-- <validate-error :error="errors.checked"></validate-error> -->
                <p class="new_err_tips" v-show="errors.checked">
                    <i class="icon iconfont">&#xe66a;</i>
                    <span>{{ errors.checked }}</span>
                </p>
            </div>
            <fs-button id="regist_success" :text="$c('pages.Login.Create_an_account')" :loading="btn_loading" htmlType="submit"></fs-button>
            <button type="button" class="bd_click" @click.stop="bdClick">click</button>
            <fs-button class="reg_bottom" type="gray login_btn">
                {{ $c("pages.Login.Already_have_an_account") }} <nuxt-link @click.native="linkPoint" :to="localePath({ name: 'login' })">{{ $c("pages.Login.Sign_In") }}</nuxt-link>
            </fs-button>
        </form>
        <RegistPop :show="isshowforbidden" @close="closeForbidden" />
    </div>
</template>

<script>
import { mapState, mapActions, mapGetters } from "vuex"
import ValidateError from "@/components/ValidateError/ValidateError"
import FsButton from "@/components/FsButton/FsButton"
import FsPopover from "@/components/FsPopover"
// import SelectCountry from "@/components/SelectCountry/SelectCountry"
import TelCode from "@/components/TelCode/TelCode"
import { email_valdate, password_validate, company_name_validate, cn_mobile_tel, cn_email, hk_phone } from "@/constants/validate"
import AES from "@/util/AES.js"
import GRecaptcha from "@/components/GRecaptcha/GRecaptcha"
import { setCookieOptions } from "@/util/util"
import PwdVerify from "./PwdVerify.vue"
import { getRecaptchaToken } from "@/util/grecaptchaHost"
import RegistPop from "./RegistPop.vue"
export default {
    layout: "account",
    name: "regist",
    components: {
        ValidateError,
        FsButton,
        FsPopover,
        // SelectCountry,
        TelCode,
        GRecaptcha,
        PwdVerify,
        RegistPop,
    },
    data() {
        return {
            isshowforbidden: false,
            btn_loading: false,
            eye: false,
            is_regist: 0,
            isCheckOrganizationName: false,
            form: {
                first_name: "",
                last_name: "",
                email: "",
                password: "",
                phone_number: "",
                customers_company: "",
                checked: false,
                verificationCode: "",
                account_number: "",
            },
            errors: {
                first_name: "",
                last_name: "",
                email: "",
                password: "",
                // country: "",
                phone_number: "",
                customers_company: "",
                checked: "",
                verificationCode: "",
                account_number: "",
            },
            actionType: false,
            OptionalType: true,
            reg_type: "mobile_reg",
            reg_type_list: [
                { name: "手机注册", type: "mobile_reg" },
                { name: "邮箱注册", type: "email_reg" },
            ],
            verification: {
                time: 60,
                txt: this.$c("form.validate.verification.get"),
                loading: false,
                timeOut: null,
                ticket: "",
                randstr: "",
                key: "",
            },
            API: {
                registerCn: `/api/user/registerCn`, // 注册接口
                checkVerification: `/api/user/checkVerification`, //验证是否需要滑动验证
                checkAloneVerification: `/api/user/checkAloneVerification`, //  验证码单独验证接口
                verificationCodes: `/api/user/verificationCodes`, //发送验证码
            },
            accountNumberFocus: false,
            isValidCompanyNumber: true, // 是否是有效企业账号,可选，所以默认true
        }
    },
    // asyncData({ app, $axios, $cookies, redirect }) {
    //     return $axios.post("/api/user/info?getMore=1").then((res) => {
    //         if (res.data.isLogin) {
    //             redirect(app.localePath({ name: "my-account" }))
    //         }
    //     })
    // },
    computed: {
        ...mapState({
            isMobile: (state) => state.device.isMobile,
            country: (state) => state.selectCountry.select_country_code,
            select_country_id: (state) => state.selectCountry.select_country_id,
            current_country_name: (state) => state.selectCountry.current_country_name,
            website: (state) => state.webSiteInfo.website,
        }),
        ...mapGetters({
            isCnTr: "webSiteInfo/isCnTr",
        }),
    },
    mounted() {},
    created() {
        this.form.checked = false
    },
    methods: {
        validateCompanyName() {
            // 验证公司名称

            if (this.form.customers_company.replace(/\s+/g, "")) {
                const params = {
                    company_name: this.form.customers_company,
                }
                this.$axios
                    .post("api/user/checkOrganizationName", params)
                    .then((res) => {
                        console.log("res", res)
                        if (!res.data.result) {
                            this.$message.error(this.$c("pages.Login.org_form.name.error.checkOrganizationName"))
                            this.errors.customers_company = this.$c("pages.Login.org_form.name.error.required")
                            this.isCheckOrganizationName = false
                        } else {
                            this.errors.customers_company = ""
                            this.isCheckOrganizationName = true
                        }
                    })
                    .catch((err) => {})
            }
        },
        closeForbidden() {
            this.isshowforbidden = false
        },
        ...mapActions({
            getUserInfo: "userInfo/getUserInfo",
            getCart: "cart/getCart",
        }),
        toogleEye() {
            this.eye = !this.eye
        },
        emailBlur() {
            // this.actionType = false
            if (!this.form.email.replace(/\s+/g, "")) {
                this.errors.email = this.$c("form.validate.email.email_required")
            }
            if (this.errors.email || !this.form.email.replace(/\s+/g, "")) {
                return
            }
            this.is_regist = 0
            this.$axios
                .post("/api/user/checkEmail", { email_address: this.form.email })
                .then((res) => {
                    this.is_regist = res.data.is_registered
                    if (res.data.is_registered === 1) {
                        this.errors.email = this.$c("pages.Login.Account_already_exists").replace("XXX", this.localePath({ name: "login" }))
                    } else {
                        this.errors.email = ""
                    }
                })
                .catch((err) => {})
        },
        async accountNumberBlur() {
            console.log("accountNumberBlur", this.form)
            try {
                if (!this.form.account_number) {
                    this.isValidCompanyNumber = true
                    this.errors.account_number = ""
                    return
                }
                this.isValidCompanyNumber = false
                const params = {
                    account_number: this.form.account_number,
                }
                await this.$axios.get("/api/user/verifyAccountNumber", { params })
                this.isValidCompanyNumber = true
                this.errors.account_number = ""
            } catch (error) {
                this.errors.account_number = error.message
            }
        },
        inputCheck(attr) {
            let flag = false
            if (attr === "first_name") {
                if (!this.form[attr].replace(/\s+/g, "")) {
                    this.errors[attr] = this.$c("form.validate.first_name.first_name_required")
                    flag = true
                } else {
                    if (this.form[attr].replace(/\s+/g, "").length > 40) {
                        this.errors[attr] = this.$c("form.validate.first_name.first_name_max")
                        flag = true
                    } else if (this.form[attr].replace(/\s+/g, "").length < 1) {
                        this.errors[attr] = this.$c("form.validate.first_name.first_name_min")
                        flag = true
                    } else {
                        this.errors[attr] = ""
                    }
                }
            } else if (attr === "last_name") {
                if (!this.form[attr].replace(/\s+/g, "")) {
                    this.errors[attr] = this.$c("form.validate.last_name.last_name_required")
                    flag = true
                } else {
                    if (this.form[attr].replace(/\s+/g, "").length > 40) {
                        this.errors[attr] = this.$c("form.validate.last_name.last_name_max")
                        flag = true
                    } else if (this.form[attr].replace(/\s+/g, "").length < 1) {
                        this.errors[attr] = this.$c("form.validate.last_name.last_name_min")
                        flag = true
                    } else {
                        this.errors[attr] = ""
                    }
                }
            } else if (attr === "email") {
                if (!this.form[attr].replace(/\s+/g, "")) {
                    this.errors[attr] = this.$c("form.validate.email.email_required")
                    flag = true
                } else {
                    if (!email_valdate.test(this.form[attr].replace(/\s+/g, ""))) {
                        this.errors[attr] = this.$c("form.validate.email.email_valid")
                        flag = true
                    } else {
                        // let str = this.form[attr].toLowerCase().split('@')[0]
                        // if(str.toLowerCase()==='admin'||str==='support'||str==='postmaster'||str==='abuse'){
                        //     this.errors[attr] = this.$c("form.validate.email.email_validate3")
                        //     flag = true
                        // }else{
                        this.errors[attr] = ""
                        // }
                    }
                }
            } else if (attr === "password") {
                if (!this.form[attr].replace(/\s+/g, "")) {
                    this.errors[attr] = this.$c("form.validate.password.password_required")
                    flag = true
                } else {
                    if (!password_validate.test(this.form[attr].replace(/\s+/g, ""))) {
                        this.errors[attr] = this.$c("form.validate.password.password_validate")
                        flag = true
                    } else {
                        this.errors[attr] = ""
                        // }
                    }
                }
            } else if (attr === "phone_number") {
                if (!this.form[attr].replace(/\s+/g, "")) {
                    this.errors[attr] = this.$c("form.validate.phone.phone_required")
                    flag = true
                } else {
                    if (this.isCnTr) {
                        if (!hk_phone.test(this.form[attr])) {
                            this.errors[attr] = this.$c("form.validate.phone.phone_min")
                            flag = true
                        } else {
                            this.errors[attr] = ""
                        }
                    } else {
                        if (!cn_mobile_tel.test(this.form[attr])) {
                            this.errors[attr] = this.$c("form.validate.phone.phone_min")
                            flag = true
                        } else {
                            this.errors[attr] = ""
                        }
                    }
                }
            } else if (attr === "verificationCode") {
                if (!this.form[attr].replace(/\s+/g, "")) {
                    this.errors[attr] = this.$c("form.validate.verification.required")
                    flag = true
                } else {
                    if (this.form[attr].length > 4) {
                        this.errors[attr] = this.errors[attr] = this.$c("form.validate.verification.validate")
                        flag = true
                    } else {
                        this.errors[attr] = ""
                    }
                }
            } else if (attr === "checked") {
                // if (!this.isMobile) {
                if (this.form[attr]) {
                    this.errors[attr] = ""
                } else {
                    this.errors[attr] = this.$c("pages.Login.Please_make_sure_you_agree_Privacy_Policy")
                    flag = true
                }
                // }
            } else if (attr === "customers_company") {
                this.OptionalType = true
                if (!this.form[attr].replace(/\s+/g, "")) {
                    this.errors[attr] = ""
                } else {
                    if (!this.isCheckOrganizationName) {
                        this.errors[attr] = this.$c("pages.Login.org_form.name.error.required")
                        flag = true
                    }
                }
                // }
            }
            return flag
        },

        phoneChange(p) {
            this.form.phone_number = p
            this.inputCheck("phone_number")
        },

        bdClick() {
            this.$bdRequest({
                conversionTypes: [
                    {
                        logidUrl: location.href,
                        newType: 25,
                    },
                ],
            })
        },
        async submit() {
            let arr = [],
                attr = []

            if (this.reg_type == "mobile_reg") {
                attr = ["first_name", "last_name", "phone_number", "password", "verificationCode", "checked"]
            } else {
                attr = ["first_name", "last_name", "email", "password", "verificationCode", "checked"]
            }
            attr.map((item) => {
                let f = this.inputCheck(item)
                arr.push(f)
            })
            if (arr.includes(true) || this.is_regist || !this.isValidCompanyNumber || this.btn_loading) {
                if (window.dataLayer) {
                    window.dataLayer.push({
                        event: "uaEvent",
                        eventCategory: "Regist Page",
                        eventAction: "sign_up_fail",
                        eventLabel: "undefined",
                        nonInteraction: false,
                    })
                }
                return
            }

            this.btn_loading = true

            const { recaptchaTp, headers = {}, errorMsg = "grecaptcha_error" } = await getRecaptchaToken()
            if (!recaptchaTp) {
                this.btn_loading = false
                this.$message.error(this.$c(`pages.Login.regist.${errorMsg}`))
                return
            }

            let formData = new FormData()

            formData.append("register_type", 1)
            formData.append("first_name", this.form.first_name)
            formData.append("last_name", this.form.last_name)
            formData.append("customers_company", this.form.customers_company)
            formData.append("customers_name", this.reg_type == "mobile_reg" ? this.form.phone_number : this.form.email)
            formData.append("verification_key", this.verification.key)
            formData.append("verification_code", this.form.verificationCode)
            formData.append("password", AES.encrypt(this.form.password, "_-yu_xuan_3507-_", "fs_com_phone2016"))
            // formData.append("type", 'verification_number');
            if (this.form.account_number) {
                formData.append("account_number", this.form.account_number)
            }

            this.$axios
                .post(this.API.registerCn, formData, { headers })
                .then((res) => {
                    // this.btn_loading = false;
                    if (res.code === 200 && res.status === "sensiWords" && this.website === "cn") {
                        for (let key in res.errors) {
                            this.errors[key] = this.$c("form.form.errors.sensiWords")
                        }
                        this.btn_loading = false
                        return
                    }
                    if (res.data.access_token) {
                        this.$cookies.set("token_new", res.data.access_token)
                    }
                    clearTimeout(this.verification.timeOut)
                    //this.getUserInfo();
                    this.getCart()
                    if (window.dataLayer) {
                        window.dataLayer.push({
                            event: "uaEvent",
                            eventCategory: "Regist Page",
                            eventAction: "sign_up",
                            eventLabel: "undefined",
                            nonInteraction: false,
                        })
                    }
                    if (window.yaCounter71412688) {
                        yaCounter71412688.reachGoal("registrations", function () {})
                    }
                    if (window.VWO) {
                        window.VWO = window.VWO || []
                        let formInstance = document.querySelector(".regist_box .form")
                        window.VWO.push(["nls.formAnalysis.markSuccess", formInstance, 1])
                    }

                    // if (this.$route.query.redirect) {
                    //     if (Array.isArray(this.$route.query.redirect)) {
                    //         this.$router.replace(this.localePath({ path: this.$route.query.redirect[this.$route.query.redirect.length - 1] }))
                    //     } else {
                    //         this.$router.replace(this.localePath({ path: this.$route.query.redirect }))
                    //     }
                    // } else {
                    //     this.$router.replace(this.localePath({ name: "home" }))
                    // }
                    this.$router.replace(this.localePath({ name: "reg-success" }))
                })
                .catch((err) => {
                    this.btn_loading = false
                    if (window.dataLayer) {
                        window.dataLayer.push({
                            event: "uaEvent",
                            eventCategory: "Regist Page",
                            eventAction: "sign_up_fail",
                            eventLabel: "undefined",
                            nonInteraction: false,
                        })
                    }
                    if (window.VWO) {
                        window.VWO = window.VWO || []
                        let formInstance = document.querySelector(".regist_box .form")
                        window.VWO.push(["nls.formAnalysis.markSuccess", formInstance, 0])
                    }
                    if (err.code === 422) {
                        if (err.errors) {
                            for (let attr in err.errors) {
                                console.log(attr)
                                if (attr == "verification_key") {
                                    this.errors.verificationCode = err.errors[attr]
                                } else {
                                    this.errors[attr] = err.errors[attr]
                                }
                            }
                        }
                    }
                    if (err.code === 403) {
                        // this.$message.error(err.message)
                        this.isshowforbidden = true //注册验证报错，显示提示弹窗
                    }
                    if (err.code === 400) {
                        this.$message.error(err.message)
                    }
                })
        },
        titlePoint(title) {
            switch (title) {
                case "Email":
                    this.actionType = true
                    break
                case "Company Name":
                    this.OptionalType = false
                    break
                case "Account Number":
                    this.accountNumberFocus = true
                    break
            }
            console.log(title)
            if (window.dataLayer) {
                window.dataLayer.push({
                    event: "uaEvent",
                    eventCategory: "Regist Page",
                    eventAction: "regist_information",
                    eventLabel: `Regist-${title} Input`,
                    nonInteraction: false,
                })
            }
        },
        linkPoint() {
            if (window.dataLayer) {
                window.dataLayer.push({
                    event: "uaEvent",
                    eventCategory: "Regist Page",
                    eventAction: "login_button",
                    eventLabel: "Sign In",
                    nonInteraction: false,
                })
            }
        },
        selectRegType(item) {
            if (item.type == this.reg_type) return
            this.reg_type = item.type
            this.errors = {
                first_name: "",
                last_name: "",
                email: "",
                password: "",
                phone_number: "",
                customers_company: "",
                checked: "",
                verificationCode: "",
            }
        },
        getVerification() {
            if (this.verification.loading) return
            if (this.reg_type == "mobile_reg") {
                if (this.inputCheck("phone_number")) return
            } else {
                if (this.inputCheck("email")) return
            }
            let params = {
                type: "verification_number",
                mobile: this.reg_type == "mobile_reg" ? this.form.phone_number : this.form.email,
            }
            this.verification.loading = true
            this.$axios
                .post(this.API.checkVerification, params)
                .then((res) => {
                    // this.waitVerification()
                    console.log(res)
                    if (res.code == 200 && res.data && res.data.status && res.data.status == 1) {
                        this.varify()
                        // this.verification.loading = false;
                    } else {
                        this.verification.loading = false
                        this.errors.verificationCode = res.message
                        setTimeout(() => {
                            this.errors.verificationCode = ""
                        }, 3000)
                    }
                })
                .catch((err) => {
                    console.log(err)
                    this.verification.loading = false
                    if (err.code === 400) {
                        this.errors.verificationCode = err.message
                        setTimeout(() => {
                            this.errors.verificationCode = ""
                        }, 3000)
                    }
                })
            // }
        },
        waitVerification() {
            // let time = this.verification.time;
            let _ts = this
            console.log(_ts.verification)
            if (_ts.verification.time == 0) {
                _ts.verification.loading = false
                clearTimeout(_ts.verification.timeOut)
                _ts.verification.txt = _ts.$c("form.validate.verification.get")
                _ts.verification.time = 60
            } else {
                _ts.verification.time = _ts.verification.time - 1
                _ts.verification.txt = `${_ts.$c("form.validate.verification.again")}${_ts.verification.time}s`

                _ts.verification.loading = true
                _ts.verification.timeOut = setTimeout(() => {
                    _ts.waitVerification()
                }, 1000)
            }
        },
        varify() {
            let _this = this
            let appid = "2088583036" // 腾讯云控制台中对应这个项目的 appid
            //生成一个滑块验证码对象
            let captcha = new TencentCaptcha(appid, function (res) {
                // 用户滑动结束或者关闭弹窗，腾讯返回的内容
                console.log(res, res.ret)

                if (res.ret === 0) {
                    //成功，传递数据给后台进行验证
                    console.log(1)
                    _this.verification.ticket = res.ticket
                    _this.verification.randstr = res.randstr
                    _this.$axios
                        .post(_this.API.verificationCodes, {
                            mobile: _this.reg_type == "mobile_reg" ? _this.form.phone_number : _this.form.email,
                            ticket: _this.verification.ticket,
                            is_register: 1,
                            randstr: _this.verification.randstr,
                        })
                        .then((r) => {
                            // this.waitVerification()
                            console.log(r)
                            if (r.code == 200) {
                                // _this.verification.loading = false;
                                _this.verification.key = r.data.key
                                _this.waitVerification()
                            } else {
                                console.log(r.message)
                                _this.errors.verificationCode = r.message
                                setTimeout(function () {
                                    _this.errors.verificationCode = ""
                                }, 3000)
                            }
                        })
                        .catch((err) => {
                            console.log(err)
                            _this.verification.loading = false
                            if (err.code === 400) {
                                _this.errors.verificationCode = err.message
                                setTimeout(() => {
                                    _this.errors.verificationCode = ""
                                }, 3000)
                            }
                        })
                } else {
                    _this.verification.loading = false
                }
            })
            // 滑块显示
            captcha.show()
        },
    },
}
</script>

<style lang="scss" scoped>
.cn_reg {
    width: 100%;
}
.new_err_tips {
    margin-top: 4px;
    color: $textColor4;
    @include font13;
    display: flex;
    align-items: center;
    .icon {
        margin-right: 4px;
    }
}
.cn_reg_type {
    // padding-top: 16px;
    display: flex;
    margin-bottom: 24px;
    justify-content: center;
    li {
        @include font16;
        color: $textColor1;
        position: relative;
        padding-bottom: 8px;
        cursor: pointer;
        &:first-child {
            margin-right: 48px;
        }
        &.active {
            font-weight: 600;
            &::after {
                content: "";
                position: absolute;
                width: 100%;
                height: 3px;
                background-color: #c00000;
                bottom: -3px;
                border-radius: 3px;
                left: 0;
            }
        }
    }
}
.a {
    width: 100%;
    text-align: center;
    .iconfont {
        font-size: 50px;
    }
}

.welcome {
    text-align: center;
    margin: 16px 0 20px 0;
    display: none;
    @include font26;
}
.reg {
    @include font13;
    color: $textColor1;
    text-align: center;
    display: none;
    > a {
        color: $textColor8;
    }
}
.form {
    margin-top: 20px;
}
.user_name_box {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    .form_item {
        width: calc((100% - 12px) / 2);
    }
}
.form_item {
    margin-bottom: 16px;
    div.label {
        display: flex;
        align-items: center;
        justify-content: space-between;
        color: $textColor3;
        margin-bottom: 4px;
        @include font12;
        a {
            color: $textColor8;
        }
    }
    .action {
        font-size: 13px;
        color: #707070;
        line-height: 20px;
        margin-top: 4px;
    }
    .pwd {
        padding-right: 44px;
    }
    .inp_box {
        position: relative;
        // .eye {
        //     position: absolute;
        //     @include font13;
        //     color: $textColor1;
        //     right: 10px;
        //     top: 8px;
        //     cursor: pointer;
        // }
        .eye {
            position: absolute;
            font-size: 16px;
            color: $textColor3;
            right: 8px;
            width: 22px;
            text-align: left;
            top: 12px;
            cursor: pointer;
            user-select: none;
            &.show {
                font-size: 16px;
            }
        }
        // input {
        //     border-color: transparent;
        //     background-color: #f6f6f8;
        //     &:hover {
        //         background: linear-gradient(0deg, rgba(25, 25, 26, 0.04), rgba(25, 25, 26, 0.04)), #f6f6f8;
        //     }
        //     &:focus {
        //         border-color: #707070;
        //     }
        //     // &.error_input {
        //     //     border-color: #c00000;
        //     // }
        // }
    }
    .input_box_verification {
        position: relative;
        > span {
            position: absolute;
            color: #0060bf;
            @include font14;
            padding: 9px 16px;
            background-color: #f6f6f6;
            right: 1px;
            border-top-right-radius: 2px;
            border-bottom-right-radius: 2px;
            top: 1px;
            cursor: pointer;
            &:hover {
                text-decoration: underline;
            }
            &.disabled {
                cursor: default;
                pointer-events: none;
                color: #ccc;
            }
        }
    }
    .agreement_box {
        display: flex;
        align-items: flex-start;
        .chk {
            margin-top: 1px;
            margin-right: 8px;
            width: 18px;
            height: 18px;
            font-size: 18px;
            // @media (max-width: 960px) {
            //     display: none;
            // }
        }
        .agreement {
            @include font12;
            color: $textColor3;
            &::v-deep {
                a {
                    color: $textColor1;
                    text-decoration: underline;
                }
            }
            .info1 {
                // @media (max-width: 960px) {
                //     display: none;
                // }
            }
            .info2 {
                display: none;
                // @media (max-width: 960px) {
                //     display: inline-block;
                // }
            }
        }
        &:hover {
            cursor: pointer;
            input[type="checkbox"] {
                &:before {
                    color: #707070;
                }
            }
        }
    }
    .validate_error {
        &::v-deep {
            .login {
                color: $textColor4;
                text-decoration: underline;
            }
        }
    }
    .error_input {
        @include errorInput;
    }
}
// ::v-deep {
//     .inp_box input {
//         border-color: transparent;
//         background-color: #f6f6f8;
//         &:hover {
//             background: linear-gradient(0deg, rgba(25, 25, 26, 0.04), rgba(25, 25, 26, 0.04)), #f6f6f8;
//         }
//         &:focus {
//             border-color: #707070;
//         }
//     }
// }
.agreement_bd {
    margin-bottom: 0px;
}
.bd_click {
    display: none;
}
.cn_reg .fs-button {
    margin: 16px 0;
    width: 100%;
    &.login_btn {
        margin: 0;
    }
    // border-radius: 9999px;
}
.form .reg_bottom {
    @include font14;
    color: $textColor1;
    font-weight: 400;
    text-align: center;
    @media (max-width: 960px) {
        margin-bottom: 12px;
    }
}

@media (max-width: 960px) {
    .welcome {
        display: block;
    }
}
@media (max-width: 680px) {
    .cn_reg_type {
        justify-content: center;
        // padding-top: 36px;
        margin-bottom: 24px;
        .form {
            margin-top: 24px;
        }
    }
    .form {
        margin-top: 24px;
    }
}
</style>
