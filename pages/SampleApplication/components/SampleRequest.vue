<template>
    <div class="sample-request" :class="{ fff: showResult, 'sample-request-products': isProducts }" v-loading="loading">
        <div class="sample_main" v-show="!showResult">
            <div class="left-info-box" v-if="isSpecial">
                <div class="top-box">
                    <div class="tit">{{ $c("single.sampleApplication.trial.title") }}</div>
                    <ul>
                        <li v-for="(v, i) in $c('single.sampleApplication.trial.list')" :key="v">
                            <div class="index"></div>
                            <div class="txt">{{ v }}</div>
                        </li>
                    </ul>
                </div>
                <div class="bottom-box" v-if="false">
                    <div class="tit">{{ $c("single.sampleApplication.advantages.title") }}</div>
                    <ul>
                        <li v-for="(v, i) in $c('single.sampleApplication.advantages.list')" :key="v.title">
                            <div class="index">
                                <img :src="`https://img-en.fs.com/includes/templates/fiberstore/images/specials/sample_request/new_img/icon-${i + 1}.svg`" alt="" />
                            </div>
                            <div class="txt-box">
                                <div class="tit">{{ v.title }}</div>
                                <div class="des">{{ v.des }}</div>
                            </div>
                        </li>
                    </ul>
                </div>
            </div>
            <div class="sample_form_item">
                <div class="sample-box">
                    <div class="box-title">{{ $c("single.sampleApplication.form.title") }}</div>
                    <div class="pro_tabBox" v-if="isSpecial">
                        <ul class="pro-list" v-if="product_list && product_list.length > 0">
                            <li v-for="(item, index) in product_list" :key="index">
                                <div class="lt">
                                    <div class="img-box">
                                        <img :src="item.images_arr" />
                                    </div>
                                    <div class="txt-box">
                                        <div class="tit">{{ item.products_name }}</div>
                                        <p class="txt">
                                            <span>#{{ item.product_ids }}</span>
                                        </p>
                                    </div>
                                </div>
                                <div class="rt">
                                    <qty-box :num="item.product_nums" :attr="index" @change="qtyChange"></qty-box>
                                    <div class="del-box" :style="delBtnStyle">
                                        <i class="iconfont" @click="deletePro(index)">&#xe65f;</i>
                                    </div>
                                </div>
                            </li>
                        </ul>
                        <div class="add-pro-box" :class="{ 'has-pro': product_list && product_list.length > 0 }">
                            <div class="lt">
                                <div class="img-box">
                                    <img src="https://resource.fs.com/mall/generalImg/20221202150721yd8s3b.png" alt="" />
                                </div>
                                <div class="inp-box">
                                    <div class="inp-content-box">
                                        <input
                                            type="text"
                                            :placeholder="$c('single.sampleApplication.form.addPl')"
                                            :class="{ error_input: products_id_error }"
                                            @keyup.enter="getproFu(product_sample.products_id, product_sample.products_num)"
                                            @focus.stop="focusInput('products_id', true, 'Sample Product Input')"
                                            @blur="blurInput('products_id')"
                                            v-model.trim="product_sample.products_id"
                                            class="inp" />
                                        <validate-error class="p_error" :error="products_id_error"></validate-error>
                                    </div>
                                    <qty-box :num="product_sample.products_num" :attr="'sample'" @change="qtyChange"></qty-box>
                                </div>
                            </div>
                            <div class="rt">
                                <qty-box :num="product_sample.products_num" :attr="'sample'" @change="qtyChange"></qty-box>
                                <div class="btn-box" ref="addBtn">
                                    <fs-button :text="btntxt" type="red" @click="getproFu(product_sample.products_id, product_sample.products_num)" :loading="sbtn_pro_loading" htmlType="submit"></fs-button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="product-box">
                        <div class="add-pro-box">
                            <img src="https://resource.fs.com/mall/generalImg/20230503154732odi7ou.png" alt="" />
                            <div class="add-pro-content">
                                <p class="input-title">{{ $c("form.form.enterProID") }}</p>
                                <div class="input-box">
                                    <span class="prefix">#</span>
                                    <input
                                        type="text"
                                        :placeholder="$c('single.sampleApplication.form.addPl')"
                                        :class="{ error_input: products_id_error }"
                                        @keyup.enter="getproFu(product_sample.products_id, product_sample.products_num)"
                                        @focus.stop="focusInput('products_id', true, 'Sample Product Input')"
                                        @blur="blurInput('products_id')"
                                        v-model.trim="product_sample.products_id"
                                        class="inp" />
                                </div>
                                <validate-error class="p_error" :error="products_id_error"></validate-error>
                            </div>
                            <fs-button :text="btntxt" type="red" @click="getproFu(product_sample.products_id, product_sample.products_num)" :loading="sbtn_pro_loading" htmlType="submit"></fs-button>
                        </div>
                        <div class="product-list" v-if="isProducts">
                            <div class="product-item" v-for="(item, index) in product_list" :key="index">
                                <img :src="item.images_arr" class="product-img" />
                                <div class="txt-box">
                                    <div class="tit">{{ item.products_name }}</div>
                                    <p class="txt">
                                        <span>#{{ item.product_ids }}</span>
                                    </p>
                                </div>
                                <div class="handle-box">
                                    <qty-box :num="item.product_nums" :attr="index" @change="qtyChange"></qty-box>
                                    <div class="del-box" @click="deletePro(index)">
                                        <i class="iconfont">&#xe65f;</i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="sample-box">
                    <div class="box-title" v-if="isProducts">{{ $c("form.form.letUsContactYou") }}</div>
                    <div class="sample-content" :class="{ 'sample-content-cn': ['cn', 'hk', 'tw', 'mo'].includes(website) }">
                        <div class="sample-item">
                            <div class="sample-item-title">
                                <span>{{ $c("form.form.first_name") }}</span>
                            </div>
                            <input
                                v-model.trim="form.entry_firstname"
                                :class="{ error_input: errors.entry_firstname_error }"
                                @focus.stop="focusInput('entry_firstname', false, 'First Name Input')"
                                @blur="blurInput('entry_firstname')"
                                type="text"
                                class="inp" />
                            <validate-error :error="errors.entry_firstname_error"></validate-error>
                        </div>
                        <div class="sample-item">
                            <div class="sample-item-title">
                                <span>{{ $c("form.form.last_name") }}</span>
                            </div>
                            <input
                                v-model.trim="form.entry_lastname"
                                :class="{ error_input: errors.entry_lastname_error }"
                                @focus.stop="focusInput('entry_lastname', false, 'Last Name Input')"
                                @blur="blurInput('entry_lastname')"
                                type="text"
                                class="inp" />
                            <validate-error :error="errors.entry_lastname_error"></validate-error>
                        </div>
                    </div>
                    <div class="sample-content">
                        <div class="sample-item">
                            <div class="sample-item-title">
                                <span>{{ $c("form.form.email_business") }}</span>
                            </div>
                            <input
                                type="text"
                                :disabled="disabled"
                                :class="{ error_input: errors.email_address_error }"
                                v-model.trim="form.email_address"
                                @focus.stop="focusInput('email_address', false, 'Email Address Input')"
                                @blur="blurInput('email_address')"
                                class="inp" />
                            <validate-error :error="errors.email_address_error.replace('XXXX', localePath({ name: 'login', query: { redirect: $route.fullPath } }))"></validate-error>
                        </div>
                        <!-- Products和Special的位置有改变 -->
                        <div class="sample-item" v-if="isSpecial">
                            <div class="sample-item-title">
                                <span>{{ $c("form.form.country") }}</span>
                            </div>
                            <select-country position="absolute" @change="changeCountry" v-model.trim="form.country_code" @click.native="handleBuried('Country/Region Drop-Down')"></select-country>
                            <validate-error :error="errors.country_code_error"></validate-error>
                        </div>
                        <div class="sample-item" v-if="isProducts">
                            <div class="sample-item-title">
                                <span>{{ $c("form.form.phone_number") }}</span>
                            </div>
                            <tel-code code="1" @changeCode="changeCode" :error="errors.entry_telephone_error" :phone="form.entry_telephone" @change="telChange" @point="handleBuried('Phone Number Input')"></tel-code>
                            <validate-error :error="errors.entry_telephone_error.replace('XXXX', localePath({ name: 'login', query: { redirect: $route.fullPath } }))"></validate-error>
                        </div>
                    </div>
                    <div class="sample-content" v-if="isSpecial">
                        <div class="sample-item">
                            <div class="sample-item-title">
                                <span>{{ $c("form.form.company_name") }}</span>
                            </div>
                            <input
                                v-model.trim="form.company_name"
                                @blur="blurInput('company_name')"
                                :class="{ error_input: errors.company_name_error }"
                                @focus.stop="focusInput('company_name', false, 'Company Name Input')"
                                type="text"
                                class="inp" />
                            <validate-error :error="errors.company_name_error"></validate-error>
                        </div>
                        <div class="sample-item">
                            <div class="sample-item-title">
                                <span>{{ $c("form.form.job_title") }}</span>
                            </div>
                            <input
                                v-model.trim="form.job_title"
                                :class="{ error_input: errors.job_title_error }"
                                @focus.stop="focusInput('job_title', false, 'Job Title Input')"
                                @blur="blurInput('job_title')"
                                type="text"
                                class="inp" />
                            <validate-error :error="errors.job_title_error"></validate-error>
                        </div>
                    </div>
                    <div class="sample-content">
                        <!-- Products和Special的位置有改变 -->
                        <div class="sample-item" v-if="isSpecial">
                            <div class="sample-item-title">
                                <span>{{ $c("form.form.phone_number") }}</span>
                            </div>
                            <tel-code code="1" @changeCode="changeCode" :error="errors.entry_telephone_error" :phone="form.entry_telephone" @change="telChange" @point="handleBuried('Phone Number Input')"></tel-code>
                            <validate-error :error="errors.entry_telephone_error.replace('XXXX', localePath({ name: 'login', query: { redirect: $route.fullPath } }))"></validate-error>
                        </div>
                        <div class="sample-item" v-if="isProducts">
                            <div class="sample-item-title">
                                <span>{{ $c("form.form.country") }}</span>
                            </div>
                            <select-country position="absolute" @change="changeCountry" v-model.trim="form.country_code" @click.native="handleBuried('Country/Region Drop-Down')"></select-country>
                            <validate-error :error="errors.country_code_error"></validate-error>
                        </div>
                        <div class="sample-item" v-if="isSpecial">
                            <div class="sample-item-title">
                                <span>{{ $c("form.form.zip_code") }}</span>
                            </div>
                            <input
                                v-model.trim="form.entry_postcode"
                                :class="{ error_input: errors.entry_postcode_error }"
                                @focus.stop="focusInput('entry_postcode', false, 'Zip Code Input')"
                                @blur="blurInput('entry_postcode')"
                                type="text"
                                class="inp" />
                            <validate-error :error="errors.entry_postcode_error"></validate-error>
                        </div>
                    </div>
                    <template v-if="isSpecial">
                        <div class="sample-content" v-if="['CN'].includes(form.country_code)">
                            <div class="sample-item width100">
                                <div class="sample-item-title">
                                    <span>{{ $c("form.form.address") }}</span>
                                </div>
                                <CnArea v-model="cnAddress" @change="cnChange" placeholder="请选择省 / 市 / 区"></CnArea>
                                <validate-error :error="cnErrorsAdress"></validate-error>
                            </div>
                        </div>
                        <div class="sample-content" v-if="['CN'].includes(form.country_code)">
                            <div class="sample-item width100">
                                <div class="sample-item-title">
                                    <span>{{ $c("form.form.detail_address") }}</span>
                                </div>
                                <input
                                    v-model.trim="cn_entry_street_address"
                                    :class="{ error_input: cn_entry_street_address_error }"
                                    @focus.stop="focusInput('cn_entry_street_address', false, 'Address Input')"
                                    @blur="blurInput('cn_entry_street_address')"
                                    type="text"
                                    class="inp"
                                    :placeholder="$c('form.form.address_ph')" />
                                <validate-error :error="cn_entry_street_address_error"></validate-error>
                            </div>
                        </div>
                        <div class="sample-content" v-if="!['CN'].includes(form.country_code)">
                            <div class="sample-item width100">
                                <div class="sample-item-title">
                                    <span>{{ $c("form.form.address") }}</span>
                                </div>
                                <input
                                    v-model.trim="form.entry_street_address"
                                    :class="{ error_input: errors.entry_street_address_error }"
                                    @focus.stop="focusInput('entry_street_address', false, 'Address Input')"
                                    @blur="blurInput('entry_street_address')"
                                    type="text"
                                    class="inp"
                                    :placeholder="$c('form.form.address_ph')" />
                                <validate-error :error="errors.entry_street_address_error"></validate-error>
                            </div>
                        </div>
                        <div class="sample-content" v-if="!['CN'].includes(form.country_code)">
                            <div class="sample-item width100">
                                <div class="sample-item-title">
                                    <span>{{ $c("form.form.address_2") }} ({{ $c("form.form.Optional") }})</span>
                                </div>
                                <input
                                    v-model.trim="form.entry_suburb"
                                    :class="{ error_input: errors.entry_suburb_error }"
                                    @focus.stop="focusInput('entry_suburb', false, 'Address 2 (Optional) Input')"
                                    @blur="blurInput('entry_suburb')"
                                    type="text"
                                    class="inp"
                                    :placeholder="$c('form.form.address_ph')" />
                                <!--                            <validate-error :error="errors.entry_suburb_error"></validate-error>-->
                            </div>
                        </div>
                        <div class="sample-content" v-if="!['CN'].includes(form.country_code)">
                            <div class="sample-item">
                                <div class="sample-item-title">
                                    <span>{{ $c("form.form.city") }}</span>
                                </div>
                                <input
                                    v-model.trim="form.entry_city"
                                    :class="{ error_input: errors.entry_city_error }"
                                    @focus.stop="focusInput('entry_city', false, 'City Input')"
                                    @blur="blurInput('entry_city')"
                                    type="text"
                                    class="inp" />
                                <validate-error :error="errors.entry_city_error"></validate-error>
                            </div>
                            <div class="sample-item" v-show="state_list && state_list.length > 0">
                                <div class="sample-item-title">
                                    <span>{{ $c("form.form.state_province_region") }}</span>
                                </div>
                                <div class="select-box">
                                    <p class="ph-box" v-show="!form.state">{{ $c("form.form.please_select") }}</p>
                                    <fs-select :options="state_list" @change="getstateFu(form.state, form.countryName)" v-model="form.state" :placeholder="$c('form.form.please_select')" />
                                    <!-- <select class="select" v-model.trim="form.state" @focus.stop="focusInput('state', false, 'State/Province/Region Drop-Down')" @change="getstateFu(form.state, form.countryName)">
                                    <option v-for="(item, index) in state_list" :key="index" :value="item.states_code">{{ item.states }}</option>
                                </select> -->
                                </div>
                                <validate-error :error="errors.state_error"></validate-error>
                            </div>
                        </div>
                    </template>
                    <div class="sample-content">
                        <div class="sample-item width100" v-show="interest_type_options && interest_type_options.length > 0">
                            <div class="sample-item-title">
                                <span>{{ isProducts ? $c("form.form.interest_type_products") : $c("form.form.interest_type") }}</span>
                            </div>
                            <div class="select-box">
                                <p class="ph-box" v-show="!form.interest_type">{{ $c("form.form.please_select") }}</p>
                                <fs-select
                                    :options="interest_type_options"
                                    v-model.trim="form.interest_type"
                                    @change="errors.interest_type_error = ''"
                                    :placeholder="$c('form.form.please_select')"
                                    @focus.stop="focusInput('interest_type', false, 'Interest Type Drop-Down')" />
                                <!-- <select class="select" v-model.trim="form.interest_type" @focus.stop="focusInput('interest_type', false, 'Interest Type Drop-Down')">
                                    <option v-for="(item, index) in interest_type_options" :key="index" :value="index + 1 + ''">{{ item }}</option>
                                </select> -->
                            </div>
                            <validate-error :error="errors.interest_type_error"></validate-error>
                        </div>
                    </div>
                    <div class="sample-content">
                        <div class="sample-item sample-item-comments">
                            <div class="sample-item-title">
                                <span>{{ $c("form.form.comment") }} ({{ $c("form.form.Optional") }})</span>
                            </div>
                            <textarea
                                v-model.trim="form.comments"
                                :class="{ error_input: errors.comments_error }"
                                :placeholder="$c('form.form.comments_placeholder')"
                                @change="errors.comments_error = ''"
                                maxlength="5000"
                                name=""
                                class="textarea"
                                @blur="blurInput('comments')"
                                @focus.stop="focusInput('comments', false, 'Comments(Optional) Input')"></textarea>
                            <div :class="errors.comments_error ? 'input-item-flex' : 'input-item-number'">
                                <validate-error :error="errors.comments_error"></validate-error>
                                <p class="textarea-num">
                                    <em :class="{ active: form.comments.length === 5000 }">{{ form.comments.length }}</em
                                    >/5000
                                </p>
                            </div>
                        </div>
                    </div>
                    <div class="agreement-box">
                        <div class="agreement">
                            <input v-model="check1" @change="agreecheckFu(1)" type="checkbox" class="chk" />
                            <p v-if="website == 'jp'">
                                <a @click.stop="openAgreement" href="javascript:;">{{ $c("single.sampleApplication.agreement_choose.link01") }}</a
                                >{{ $c("single.sampleApplication.agreement.tit") }}
                            </p>
                            <p v-else-if="website == 'de'" v-html="agree_txt01"></p>
                            <p v-else>
                                {{ $c("form.form.agree_txt01") }} <a @click.stop="openAgreement" href="javascript:;">{{ $c("single.sampleApplication.agreement.tit") }}</a
                                >.
                            </p>
                        </div>
                        <validate-error :error="errors.check1_error"></validate-error>
                    </div>
                    <div class="agreement-box">
                        <div class="agreement">
                            <input v-model="check2" @change="agreecheckFu(2)" type="checkbox" class="chk" />
                            <p v-show="website == 'jp'" v-html="jp_agreement_choose"></p>
                            <p v-show="website == 'de' || website == 'fr'" v-html="agree_txt02"></p>
                            <p v-show="!(website == 'de' || website == 'fr' || website == 'jp')">
                                {{ $c("form.form.agree_txt02") }}
                                <nuxt-link :to="localePath({ name: 'privacy-policy' })" target="_blank">{{ $c("form.form.privacy_policy") }}</nuxt-link>
                                {{ $c("form.form.and") }}
                                <nuxt-link :to="localePath({ name: 'terms-of-use' })" target="_blank">{{ $c("form.form.terms_of_use") }}</nuxt-link
                                >.
                            </p>
                        </div>
                        <validate-error :error="errors.check2_error"></validate-error>
                    </div>
                    <div>
                        <g-recaptcha ref="grecaptcha" @getValidateCode="getValidateCode"></g-recaptcha>
                    </div>
                    <div class="sbtn-box">
                        <fs-button id="sample_submit" :text="$c('form.form.submit')" @click="submitFu" :loading="sbtn_loading" htmlType="submit"></fs-button>
                    </div>
                </div>
            </div>
        </div>
        <div class="third-step-wrap" v-show="showResult">
            <div class="iconfont success-logo">&#xe710;</div>
            <div class="success-tit">{{ $c("single.sampleApplication.success.title").replace("XXX", caseNumber) }}</div>
            <div class="success-des" v-html="replaceURL($c('single.sampleApplication.success.txt'))"></div>
            <fs-button
                type="gray"
                :text="$c('form.form.success.btn_txt')"
                @click="
                    $router.push({ path: localePath({ name: 'home' }) })
                    gaEvent('home_entrance', `Return to Homepage`)
                "></fs-button>
        </div>
        <login-regist ref="loginRegist" :fillData="fillData"></login-regist>
        <!--        <success-popup :showResult="showResult" @hideresultFu="close">-->
        <!--            <div class="success_main">-->
        <!--                <div class="lt">-->
        <!--                    <i class="iconfont icon">&#xf060;</i>-->
        <!--                </div>-->
        <!--                <div class="rt">-->
        <!--                    <div class="tit">-->
        <!--                        {{ $c("single.sampleApplication.success.title").replace("XXX", caseNumber) }}-->
        <!--                    </div>-->
        <!--                    <p class="txt">-->
        <!--                        {{ $c("single.sampleApplication.success.txt") }}-->
        <!--                    </p>-->
        <!--                </div>-->
        <!--            </div>-->
        <!--        </success-popup>-->
        <fs-popup class="agreement" :show="agreementShow" :title="$c('single.sampleApplication.agreement.tit')" @close="closeAgreement">
            <div class="agreement-content">
                <ul>
                    <li>
                        <span></span>
                        <p>{{ $c("single.sampleApplication.agreement.txt01") }}</p>
                    </li>
                    <li>
                        <span></span>
                        <p>{{ $c("single.sampleApplication.agreement.txt02") }}</p>
                    </li>
                    <li>
                        <span></span>
                        <p>{{ $c("single.sampleApplication.agreement.txt03") }}</p>
                    </li>
                    <li>
                        <span></span>
                        <p>{{ $c("single.sampleApplication.agreement.txt04") }}</p>
                    </li>
                    <li>
                        <span></span>
                        <p>{{ $c("single.sampleApplication.agreement.txt05") }}</p>
                    </li>
                </ul>
            </div>
        </fs-popup>
    </div>
</template>

<script>
import { mapState, mapMutations } from "vuex"
import { email_valdate, phone_validate, cn_mobile_tel } from "@/constants/validate.js"
import FsButton from "@/components/FsButton/FsButton.vue"
import FsSelect from "@/components/FsSelect/FsSelect.vue"
import ValidateError from "@/components/ValidateError/ValidateError.vue"
import TelCode from "@/components/TelCode/TelCode.vue"
import SelectCountry from "@/components/SelectCountry/SelectCountry.vue"
import QtyBox from "@/components/QtyBox/QtyBox.vue"
import SuccessPopup from "@/popup/SuccessPopup/SuccessPopup.vue"
import fixScroll from "@/util/fixScroll"
import LoginRegist from "@/popup/LoginRegist/LoginRegist.vue"
import FsPopup from "@/components/FsPopup/FsPopup.vue"
import BreadCrumb from "@/components/BreadCrumb/BreadCrumb"
import GRecaptcha from "@/components/GRecaptcha/GRecaptcha"
import { setCookieOptions } from "@/util/util"
import { isNeedGrecaptcha } from "@/util/grecaptchaHost"
import CnArea from "@/components/CnArea/CnArea"
export default {
    name: "SampleRequest",
    components: {
        FsButton,
        FsSelect,
        ValidateError,
        QtyBox,
        TelCode,
        SelectCountry,
        SuccessPopup,
        LoginRegist,
        FsPopup,
        GRecaptcha,
        BreadCrumb,
        CnArea,
    },
    props: {
        // 现有两种情况，专题页 Special(已废弃)  和  商品页 Products
        pageType: {
            type: String,
            default: "Special",
        },
        productId: {
            type: [String, Number],
            default: "",
        },
    },
    data() {
        return {
            product_list: [],
            obj: null,
            sbtn_loading: false,
            sbtn_pro_loading: false,
            product_sample: {
                products_id: "",
                products_num: 1,
            },
            products_id_error: "",
            qty: 1,
            codeNum: 1,
            form: {
                entry_firstname: "",
                entry_lastname: "",
                email_address: "",
                company_name: "",
                job_title: "",
                entry_telephone: "",
                entry_postcode: "",
                entry_street_address: "",
                entry_city: "",
                product_ids: [],
                product_nums: [],
                country_code: "us",
                interest_type: "",
                comments: "",
                entry_suburb: "",
                state: "",
                countryName: "",
                resource_page: "",
            },
            check1: false,
            check2: false,
            errors: {
                entry_firstname_error: "",
                entry_lastname_error: "",
                email_address_error: "",
                company_name_error: "",
                job_title_error: "",
                entry_telephone_error: "",
                entry_postcode_error: "",
                entry_street_address_error: "",
                entry_city_error: "",
                product_ids_error: "",
                product_nums_error: "",
                country_code_error: "",
                interest_type_error: "",
                comments_error: "",
                entry_suburb_error: "",
                state_error: "",
                check1_error: "",
                check2_error: "",
            },
            showResult: false,
            timer: null,
            disabled: false,
            agreementShow: false,
            notThePro: false,
            fillData: {},
            jp_agreement_choose: this.$c("single.sampleApplication.agreement_choose.txt02"),
            agree_txt01: this.$c("form.form.agree_txt01"),
            agree_txt02: this.$c("form.form.agree_txt02"),
            caseNumber: "",
            regExp: /^\d{6,}$/,
            recaptchaTp: false,
            recaptchaVal: "",
            interest_type_options: this.$c("form.form.interest_type_options"),
            delBtnStyle: {},
            crumbList: [
                { name: this.$c("single.sampleApplication.crumbList[0]"), url: "/home" },
                { name: this.$c("single.sampleApplication.crumbList[1]"), url: "/service/fs_support.html" },
                { name: this.$c("single.sampleApplication.crumbList[2]"), url: "" },
            ],
            code: "",
            cnAddress: [],
            cnErrorsAdress: "",
            cn_entry_street_address: "",
            cn_entry_street_address_error: "",
            loading: false,
        }
    },

    methods: {
        ...mapMutations({
            setSelectStateName: "selectCountry/setSelectStateName",
            setSelectStateCode: "selectCountry/setSelectStateCode",
            show: "loginRegist/show",
            setActive: "loginRegist/setActive",
        }),
        changeCode(code) {
            this.code = code
        },
        gaEvent(eventAction, eventLabel) {
            window.dataLayer &&
                window.dataLayer.push({
                    event: "uaEvent",
                    eventCategory: this.pageGroup,
                    eventAction: eventAction,
                    eventLabel: eventLabel,
                    nonInteraction: false,
                })
        },
        subStr(str) {
            return str.replace("%XXXX%", `<a class="case_btn" href="${this.localePath({ path: `/support_ticket` })}">`).replace("%ZZZZ%", "</a>")
        },
        handleBuried(label) {
            window.dataLayer &&
                window.dataLayer.push({
                    event: "uaEvent",
                    eventCategory: "Product Service_Sample Application Page",
                    eventAction: "sample_application",
                    eventLabel: label,
                    nonInteraction: false,
                })
        },
        close() {
            this.showResult = false
            fixScroll.unfixed()
        },
        getproFu(pid, num, isInit = false) {
            if (!this.product_sample.products_id && this.isSpecial) {
                this.products_id_error = this.$c("form.form.errors.products_id_error")
                return false
            }
            if (pid) {
                this.qty = num
                if (this.isSpecial) {
                    for (let i = 0; i < this.form.product_ids.length; i++) {
                        if (this.form.product_ids[i] == pid) {
                            this.product_list[i].product_nums += parseInt(this.qty)
                            this.$set(this.product_list[i], "product_nums", this.product_list[i].product_nums)
                            this.form.product_nums[i] = this.product_list[i].product_nums
                            if (parseInt(this.product_list[i].product_nums) > 9999) {
                                this.product_list[i].product_nums = 9999
                            }
                            this.notThePro = false
                            this.product_sample = { products_id: "", products_num: 1 }
                            this.setSampleCookies()
                            this.qty = 1
                            return
                        }
                    }
                }
                if (isInit) {
                    this.loading = true
                } else {
                    this.sbtn_pro_loading = true
                }
                this.$axios
                    .post("/api/addSample", {
                        products_id: pid,
                        products_num: num,
                    })
                    .then((res) => {
                        this.obj = {
                            product_ids: "",
                            product_nums: 1,
                            images_arr: "",
                            products_name: "",
                        }
                        if (isInit) {
                            this.loading = false
                        } else {
                            this.sbtn_pro_loading = false
                        }
                        this.notThePro = true
                        this.obj.product_ids = res.data.products_id
                        this.obj.products_name = res.data.products_desc.products_name
                        this.obj.images_arr = res.data.images_arr
                        this.obj.product_nums = this.qty
                        if (this.notThePro) {
                            this.form.product_ids.push(this.obj.product_ids)
                            this.form.product_nums.push(this.obj.product_nums)
                        }
                        this.product_list.push(this.obj)
                        this.product_sample = {
                            products_id: "",
                            products_num: 1,
                        }
                        console.log(JSON.stringify(this.product_list))

                        this.setSampleCookies()
                        console.log(this.$cookies.get("sample"))
                        this.qty = 1
                    })
                    .catch((err) => {
                        if (isInit) {
                            this.loading = false
                        } else {
                            this.sbtn_pro_loading = false
                        }
                        if (err.code == 400) {
                            this.products_id_error = err.message
                        } else if (err.code == 422) {
                            this.products_id_error = err.errors.products_id
                        }
                    })
            }
        },
        setSampleCookies() {
            if (this.isSpecial) {
                this.$cookies.set("sample", JSON.stringify(this.product_list))
            }
        },
        changeCountry(item) {
            console.log(item)
            this.form.countryName = item.countries_name
            this.form.country_code = item.iso_code
            console.log(this.form)
        },
        getstateFu(code, name) {
            console.log(code, name)
            this.setSelectStateName(name)
            this.setSelectStateCode(code)
        },
        telChange(inp) {
            this.form.entry_telephone = inp
            // 判断是否为6位数字
            if (!inp.replace(/^\s+|\s+$/g, "")) {
                this.errors.entry_telephone_error = this.$c("form.form.errors.entry_telephone_error")
            } else {
                if (!["cn", "cn"].includes(this.website)) {
                    if (!this.regExp.test(inp)) {
                        this.errors.entry_telephone_error = this.$c("form.form.errors.entry_telephone_error01")
                    } else {
                        this.errors.entry_telephone_error = ""
                    }
                } else {
                    if (!cn_mobile_tel.test(inp)) {
                        this.errors.entry_telephone_error = this.$c("form.form.errors.entry_telephone_error01")
                        this.$forceUpdate()
                    } else {
                        if (this.is_login != 1) {
                            this.$axios
                                .post("/api/user/isHasRegister", { customers_name: inp })
                                .then((res) => {
                                    if (res.code != 200) return
                                    const data = res.data
                                    if (data && data.is_has) {
                                        this.errors.entry_telephone_error = "账户已存在，单击此处<a href=XXXX title='登录'>登录</a>。"
                                    } else {
                                        this.errors.entry_telephone_error = ""
                                    }
                                })
                                .catch((e) => {
                                    this.errors.entry_telephone_error = ""
                                })
                        } else {
                            this.errors.entry_telephone_error = ""
                        }
                    }
                }
            }
            this.$forceUpdate()
        },
        qtyChange(num, index) {
            this.qty = num
            if (index === "sample") {
                this.product_sample.products_num = num
            } else {
                this.product_list[index].product_nums = num
                this.form.product_nums[index] = num
                console.log(this.product_list, 12313)
            }
        },
        deletePro(i) {
            this.product_list.splice(i, 1)
            this.form.product_ids.splice(i, 1)
            this.form.product_nums.splice(i, 1)
            this.setSampleCookies()
        },
        agreecheckFu(num) {
            if (num == 1) {
                if (this.check1 == false) {
                    this.errors.check1_error = this.$c("form.form.errors.check1_error")
                } else {
                    this.errors.check1_error = ""
                }
            }
            if (num == 2) {
                if (this.check2 == false) {
                    this.errors.check2_error = this.$c("form.form.errors.check2_error")
                } else {
                    this.errors.check2_error = ""
                }
            }
        },
        focusInput(attr, type, label) {
            if (attr == "cn_entry_street_address") {
                this.cn_entry_street_address_error = ""
            } else {
                type ? (this[attr + "_error"] = "") : (this.errors[attr + "_error"] = "")
            }
            label && this.handleBuried(label)
        },
        blurInput(attr) {
            if (attr === "products_id") {
                if (!this.product_sample.products_id && this.product_list.length === 0) {
                    this.products_id_error = this.$c("form.form.errors.products_id_error")
                }
            }

            if (attr === "entry_firstname") {
                if (!this.form.entry_firstname.replace(/^\s+|\s+$/g, "")) {
                    this.errors.entry_firstname_error = this.$c("form.form.errors.entry_firstname_error")
                    return
                } else if (this.form.entry_firstname.length > 40) {
                    this.errors.entry_firstname_error = this.$c("form.validate.first_name.first_name_max")
                    return
                }
            }
            if (attr === "entry_lastname") {
                if (!this.form.entry_lastname.replace(/^\s+|\s+$/g, "")) {
                    this.errors.entry_lastname_error = this.$c("form.form.errors.entry_lastname_error")
                    return
                } else if (this.form.entry_firstname.length > 40) {
                    this.errors.entry_lastname_error = this.$c("form.validate.last_name.last_name_max")
                    return
                }
            }
            if (attr === "email_address") {
                if (!["cn", "cn"].includes(this.website)) {
                    if (!this.form.email_address.replace(/^\s+|\s+$/g, "")) {
                        this.errors.email_address_error = this.$c("form.form.errors.email_business_error")
                    } else if (!email_valdate.test(this.form.email_address)) {
                        this.errors.email_address_error = this.$c("form.form.errors.email_address_error01")
                    }
                } else {
                    if (!this.form.email_address.replace(/^\s+|\s+$/g, "")) {
                        this.errors.email_address_error = this.$c("form.form.errors.email_business_error")
                    } else if (!email_valdate.test(this.form.email_address)) {
                        this.errors.email_address_error = this.$c("form.form.errors.email_address_error01")
                    } else {
                        if (this.is_login != 1) {
                            this.$axios
                                .post("/api/user/isHasRegister", { customers_name: this.form.email_address })
                                .then((res) => {
                                    if (res.code != 200) return
                                    const data = res.data
                                    if (data && data.is_has) {
                                        this.errors.email_address_error = "账户已存在，单击此处<a href=XXXX title='登录'>登录</a>。"
                                    } else {
                                        this.errors.email_address_error = ""
                                    }
                                })
                                .catch((e) => {
                                    this.errors.email_address_error = ""
                                })
                        } else {
                            this.errors.email_address_error = ""
                        }
                    }
                    // if (!this.form.email_address) {
                    //     this.errors.email_address_error = ""
                    // } else {

                    // }
                }
            }
            if (attr === "company_name") {
                if (!this.form.company_name.replace(/^\s+|\s+$/g, "")) {
                    this.errors.company_name_error = this.$c("form.form.errors.company_name_error")
                    return
                } else if (this.form.company_name < 3 || this.form.company_name > 120) {
                    this.errors.company_name_error = this.$c("pages.confirmOrder.form.company_name_validate")
                    return
                }
            }
            if (attr === "job_title") {
                if (!this.form.job_title.replace(/^\s+|\s+$/g, "")) {
                    this.errors.job_title_error = this.$c("form.form.errors.job_title_error")
                    return
                }
            }
            if (attr === "entry_telephone") {
                if (!this.form.entry_telephone.replace(/^\s+|\s+$/g, "")) {
                    this.errors.entry_telephone_error = this.$c("form.form.errors.entry_telephone_error")
                } else if (!this.regExp.test(this.form.entry_telephone)) {
                    this.errors.entry_telephone_error = this.$c("form.form.errors.entry_telephone_error01")
                }
            }
            if (attr === "entry_postcode") {
                if (!this.form.entry_postcode.replace(/^\s+|\s+$/g, "")) {
                    this.errors.entry_postcode_error = this.$c("form.form.errors.entry_postcode_error")
                } else if (this.form.entry_postcode.split("").length < 3) {
                    this.errors.entry_postcode_error = this.$c("form.form.errors.entry_postcode_error01")
                } else if (this.form.entry_postcode.split("").length > 10) {
                    this.errors.entry_postcode_error = this.$c("pages.confirmOrder.form.zipCodeMaxLength")
                }
            }
            if (attr === "entry_street_address") {
                if (!this.form.entry_street_address.replace(/^\s+|\s+$/g, "")) {
                    this.errors.entry_street_address_error = this.$c("form.form.errors.entry_street_address_error")
                } else if (this.form.entry_street_address.split("").length < 4 || this.form.entry_street_address.split("").length > 35) {
                    this.errors.entry_street_address_error = this.$c("form.form.errors.entry_street_address_line1_error")
                }
            }
            if (attr === "cn_entry_street_address") {
                this.form.entry_street_address = this.cnAddress.join("") + this.cn_entry_street_address
                console.log(this.form.entry_street_address)
                if (!this.cn_entry_street_address.replace(/^\s+|\s+$/g, "")) {
                    this.cn_entry_street_address_error = this.$c("form.form.errors.entry_street_address_error")
                } else if (this.cn_entry_street_address.split("").length < 4 || this.cn_entry_street_address.split("").length > 35) {
                    this.cn_entry_street_address_error = this.$c("form.form.errors.entry_street_address_line1_error")
                }
            }
            // if (attr === "entry_suburb") {
            //     if (this.form.entry_suburb.split("").length < 2) {
            //         console.log(this.form.entry_suburb, this.form.entry_suburb.split("").length);
            //         this.errors.entry_suburb_error = this.$c('form.form.errors.entry_street_address_line2_error');
            //     };
            // }
            if (attr === "entry_city") {
                if (!this.form.entry_city.replace(/^\s+|\s+$/g, "")) {
                    this.errors.entry_city_error = this.$c("form.form.errors.entry_city_error")
                } else if (this.form.entry_city > 40) {
                    this.errors.entry_city_error = this.$c("pages.confirmOrder.form.cityMaxLength")
                }
            }
            // if (attr === "comments") {
            //     if (!this.form.comments.replace(/^\s+|\s+$/g, "")) {
            //         this.errors.comments_error = this.$c("form.form.errors.comments_error")
            //     }
            // }
        },
        submitFu() {
            if (this.sbtn_loading) {
                return
            }
            console.log(this.is_login)
            this.form.resource_page = this.resourcePage
            let flag = false
            if (!this.form.product_ids || this.form.product_ids.length <= 0) {
                this.products_id_error = this.$c("form.form.errors.products_id_error")
                flag = true
            }
            if (!this.form.entry_firstname.replace(/^\s+|\s+$/g, "")) {
                this.errors.entry_firstname_error = this.$c("form.form.errors.entry_firstname_error")
                flag = true
            }
            if (!this.form.entry_lastname.replace(/^\s+|\s+$/g, "")) {
                this.errors.entry_lastname_error = this.$c("form.form.errors.entry_lastname_error")
                flag = true
            }
            if (!["cn", "cn"].includes(this.website)) {
                if (!this.form.email_address.replace(/^\s+|\s+$/g, "")) {
                    this.errors.email_address_error = this.$c("form.form.errors.email_business_error")
                    flag = true
                } else if (!email_valdate.test(this.form.email_address)) {
                    this.errors.email_address_error = this.$c("form.form.errors.email_address_error01")
                    flag = true
                }

                if (!this.form.entry_telephone.replace(/^\s+|\s+$/g, "")) {
                    this.errors.entry_telephone_error = this.$c("form.form.errors.entry_telephone_error")
                    flag = true
                } else if (!phone_validate.test(this.form.entry_telephone)) {
                    this.errors.entry_telephone_error = this.$c("form.form.errors.entry_telephone_error01")
                    flag = true
                }
            } else {
                if (this.errors.email_address_error) {
                    flag = true
                }
                if (this.errors.entry_telephone_error) {
                    flag = true
                }
            }
            if (this.isSpecial) {
                if (!this.form.company_name.replace(/^\s+|\s+$/g, "")) {
                    this.errors.company_name_error = this.$c("form.form.errors.company_name_error")
                    flag = true
                }
                if (!this.form.job_title.replace(/^\s+|\s+$/g, "")) {
                    this.errors.job_title_error = this.$c("form.form.errors.job_title_error")
                    flag = true
                }
            }

            // if (!this.form.comments.replace(/^\s+|\s+$/g, "")) {
            //     this.errors.comments_error = this.$c("form.form.errors.comments_error")
            //     flag = true
            // }

            if (this.isSpecial) {
                if (!this.form.entry_postcode.replace(/^\s+|\s+$/g, "")) {
                    this.errors.entry_postcode_error = this.$c("form.form.errors.entry_postcode_error")
                    flag = true
                } else if (this.form.entry_postcode.split("").length < 3) {
                    this.errors.entry_postcode_error = this.$c("form.form.errors.entry_postcode_error01")
                    flag = true
                }
                if (!this.form.entry_street_address.replace(/^\s+|\s+$/g, "")) {
                    this.errors.entry_street_address_error = this.$c("form.form.errors.entry_street_address_error")
                    flag = true
                } else if (this.form.entry_street_address.split("").length < 4 || this.form.entry_street_address.split("").length > 35) {
                    this.errors.entry_street_address_error = this.$c("form.form.errors.entry_street_address_line1_error")
                    flag = true
                }
                if (["cn", "cn"].includes(this.website)) {
                    if (this.cnAddress.length == 0) {
                        this.cnErrorsAdress = "请选择您的地址。"
                        flag = true
                    }
                    if (!this.cn_entry_street_address.replace(/^\s+|\s+$/g, "")) {
                        this.cn_entry_street_address_error = this.$c("form.form.errors.entry_street_address_error")
                        flag = true
                    } else if (this.cn_entry_street_address.split("").length < 4 || this.cn_entry_street_address.split("").length > 35) {
                        this.cn_entry_street_address_error = this.$c("form.form.errors.entry_street_address_line1_error")
                        flag = true
                    }
                }
                if (!this.form.entry_city.replace(/^\s+|\s+$/g, "")) {
                    this.errors.entry_city_error = this.$c("form.form.errors.entry_city_error")
                    flag = true
                }
                if (!this.form.state.replace(/^\s+|\s+$/g, "") && this.state_list.length > 0) {
                    this.errors.state_error = this.$c("form.form.errors.state_error")
                    flag = true
                }
            }
            if (!this.form.interest_type.replace(/^\s+|\s+$/g, "")) {
                this.errors.interest_type_error = this.$c("form.form.errors.interest_type_error")
                flag = true
            } else {
                this.errors.interest_type_error = ""
                flag = false
            }

            if (!this.check1) {
                this.errors.check1_error = this.$c("form.form.errors.check1_error")
                flag = true
            }
            if (!this.check2) {
                this.errors.check2_error = this.$c("form.form.errors.check2_error")
                flag = true
            }
            if (flag) {
                if (this.isProducts) {
                    this.buriedPointWrapper("Submit Trial_Fail", "policy_and_service")
                }
                return
            }
            if (this.is_login !== 1) {
                this.sbtn_loading = true
                // 验证邮箱是否存在
                this.$axios
                    .post("/api/user/checkEmail", { email_address: this.form.email_address })
                    .then((res) => {
                        this.sbtn_loading = false
                        // console.log(res,'qq');
                        if (res.code === 200) {
                            if (res.data.is_registered !== 1) {
                                this.show()
                                this.setActive("register")
                                this.fillData = this.form
                                this.$refs.loginRegist.init()
                                // console.log(this.form,this.fillData);
                            } else {
                                this.show()
                                this.setActive("login")
                                this.fillData = this.form
                            }
                        }
                    })
                    .catch((err) => {
                        this.sbtn_loading = false
                    })
                return
            }

            if (isNeedGrecaptcha(window.location.hostname)) {
                if (!this.recaptchaTp && this.$refs.grecaptcha) {
                    this.$refs.grecaptcha.clickRecaptcha()
                    return
                }
            }

            this.sbtn_loading = true
            if (this.isSpecial) {
                this.fetchSpecialSubmit()
            }
            if (this.isProducts) {
                this.fetchProductsSubmit()
            }
        },
        fetchSpecialSubmit() {
            this.$axios
                .post("/api/submit_sample_apply", { ...this.form, entry_telephone: `${this.code.replace("+", "")} ${this.form.entry_telephone}` }, { headers: { "g-recaptcha-response": this.recaptchaVal } })
                .then((res) => {
                    this.sbtn_loading = false
                    this.showResult = true
                    this.form = {
                        entry_firstname: "",
                        entry_lastname: "",
                        email_address: this.userInfo.customers_email_address,
                        entry_telephone: "",
                        entry_postcode: "",
                        entry_street_address: "",
                        entry_city: "",
                        product_ids: [],
                        product_nums: [],
                        country_code: "us",
                        comments: "",
                        entry_suburb: "",
                        state: "",
                    }
                    this.caseNumber = res.data.caseNumber
                    this.product_list = []
                    this.obj = null
                    this.$cookies.remove("sample")
                    window.dataLayer &&
                        window.dataLayer.push({
                            event: "uaEvent",
                            eventCategory: "Product Service_Sample Application Page",
                            eventAction: "submit_application",
                            eventLabel: `Submit Success_${res.data.caseNumber}`,
                            nonInteraction: false,
                        })
                    // setTimeout(() => {
                    //     this.close()
                    //     this.caseNumber = ""
                    // }, 3000)

                    this.initGrecaptcha()
                    if (this.$refs.grecaptcha) {
                        this.$refs.grecaptcha.grecaptchaError = ""
                    }
                })
                .catch((err) => {
                    this.sbtn_loading = false
                    this.initGrecaptcha()
                    if (isNeedGrecaptcha(window.location.hostname)) {
                        if (err.code === 409 && this.$refs.grecaptcha) {
                            this.$refs.grecaptcha.grecaptchaError = this.$c("pages.Login.regist.grecaptcha_error")
                        }
                    }
                    if (err.code === 403) {
                        this.$message.error(err.message)
                    }

                    window.dataLayer &&
                        window.dataLayer.push({
                            event: "uaEvent",
                            eventCategory: "Product Service_Sample Application Page",
                            eventAction: "submit_application",
                            eventLabel: "Submit Fail",
                            nonInteraction: false,
                        })
                })
        },
        async fetchProductsSubmit() {
            try {
                const res = await this.$axios.post(
                    "/api/submit_tw_sample_apply",
                    { ...this.form, entry_telephone: `${this.code.replace("+", "")} ${this.form.entry_telephone}` },
                    { headers: { "g-recaptcha-response": this.recaptchaVal } }
                )
                this.buriedPointWrapper("Submit Trial_Success", "policy_and_service")
                this.$emit("submitSuccess", res.data.caseNumber)
            } catch (error) {
                this.buriedPointWrapper("Submit Trial_Fail", "policy_and_service")
            }
            this.sbtn_loading = false
        },
        closeAgreement() {
            this.agreementShow = false
        },
        openAgreement() {
            this.agreementShow = true
        },
        // 谷歌人机校验
        getValidateCode(val) {
            this.recaptchaTp = val.recaptchaTp
            if (val.recaptchaTp) {
                this.recaptchaVal = val.recaptchaVal
                this.submitFu()
            }
        },
        // 初始化谷歌验证
        initGrecaptcha() {
            this.recaptchaTp = false
            this.recaptchaVal = ""
        },
        // 替换链接
        replaceURL(txt) {
            return txt.replace("XXXX", this.localePath({ path: "/support_ticket" }))
        },
        cnChange(e) {
            this.cnErrorsAdress = ""
            this.form.entry_city = e[0] + e[1]
            this.form.entry_street_address = e.join("") + this.cn_entry_street_address
        },
        buriedPointWrapper(eventLabel, eventAction) {
            if (window.dataLayer) {
                window.dataLayer.push({
                    event: "uaEvent",
                    eventCategory: `productDetailPage_${this.productId}`,
                    eventAction,
                    eventLabel,
                    nonInteraction: false,
                })
            }
        },
    },
    computed: {
        ...mapState({
            isMobile: (state) => state.device.isMobile,
            state_list: (state) =>
                state.selectCountry.state_list.map((item) => {
                    return {
                        ...item,
                        name: item.states,
                        value: item.states_code,
                    }
                }),
            is_login: (state) => state.userInfo.isLogin,
            userInfo: (state) => state.userInfo.userInfo,
            warehouse: (state) => state.webSiteInfo.warehouse,
            website: (state) => state.webSiteInfo.website,
            iso_code: (state) => state.webSiteInfo.iso_code,
            resourcePage: (state) => state.device.resource_page,
            pageGroup: (state) => state.ga.pageGroup,
            country_code: (state) => state.webSiteInfo.iso_code,
        }),
        btntxt() {
            return this.isMobile ? this.$c("form.form.add") : this.$c("single.sampleApplication.form.addBtn")
            // return this.$c("form.form.add")
        },
        isProducts() {
            return this.pageType === "Products"
        },
        isSpecial() {
            return this.pageType === "Special"
        },
    },
    created() {
        if (this.$route.query.type) {
            this.crumbList = [
                { name: this.$c("single.sampleApplication.crumbList[0]"), url: "/" },
                { name: this.$c("pages.MyAccount.myAccount"), url: "/my-account" },
                { name: this.$c("pages.MyAccount.menu.requestSample"), url: "" },
            ]
        }
        if (this.userInfo) {
            this.form.email_address = this.userInfo.customers_email_address
            this.disabled = true
        }
        if (this.isSpecial) {
            let oldState = this.$cookies.get("sample")
            if (oldState) {
                this.product_list = oldState
                console.log(this.product_list, this.form.product_ids)
                for (let i = 0; i < this.product_list.length; i++) {
                    this.form.product_ids.push(this.product_list[i].product_ids)
                    this.form.product_nums.push(this.product_list[i].product_nums)
                }
            }
        }

        if (this.isProducts && this.productId) {
            this.getproFu(this.productId, 1, true)
        }

        if (this.website == "jp") {
            this.jp_agreement_choose = this.jp_agreement_choose
                .replace("/policies/privacy_policy.html", this.localePath({ path: "/policies/privacy_policy.html" }))
                .replace("/policies/terms_of_use.html", this.localePath({ path: "/policies/terms_of_use.html" }))
        }
        if (this.website == "de" || this.website == "fr") {
            this.agree_txt01 = this.agree_txt01.replace(/xxxxx/, `<a onclick="openAgreement()" href="javascript:;">${this.$c("single.sampleApplication.agreement.tit")}</a>`)
            this.agree_txt02 = this.agree_txt02.replace(/xxxxx/, `<a href="${this.localePath({ name: "privacy-policy" })}" target="_blank">${this.$c("form.form.privacy_policy")}</a>`)
            this.agree_txt02 = this.agree_txt02.replace(/vvvvv/, `<a href="${this.localePath({ name: "terms-of-use" })}" target="_blank">${this.$c("form.form.terms_of_use")}</a>`)
        }
    },
    mounted() {
        let _this = this
        window.openAgreement = _this.openAgreement
        if (this.isSpecial) {
            this.delBtnStyle = {
                width: this.$refs["addBtn"].clientWidth + "px",
            }
        }

        this.form.country_code = this.country_code
        console.log(this.form)
    },
}
</script>

<style lang="scss" scoped>
.sample-request {
    padding: 36px 0 60px 0;
    background-color: #fafafb;
    color: $textColor1;
    &.sample-request-products {
        padding: 0;
        .sample_form_item {
            padding: 20px 32px;
        }
        .sample-box .box-title {
            margin-bottom: 16px;
            @include font16();
        }
        .sbtn-box {
            text-align: right;
        }
        @include mediaM {
            .sample_form_item {
                padding: 20px 16px;
                .sample-box {
                    .sample-content .sample-item {
                        flex: 0 0 100%;
                        margin-left: 0;
                    }
                    .product-box {
                        .add-pro-box {
                            display: block;
                            > img {
                                display: none;
                            }
                            .add-pro-content {
                                margin: 0;
                            }
                            ::v-deep .fs-button {
                                margin-top: 20px;
                                width: 100%;
                            }
                        }
                        .product-list {
                            padding: 0 20px;
                            .product-item {
                                flex-wrap: wrap;
                                .txt-box {
                                    margin-right: 0;
                                }
                                .handle-box {
                                    flex: 0 0 100%;
                                    padding-left: 100px;
                                    margin-top: 12px;
                                }
                            }
                        }
                    }
                }
            }
        }
    }
    .sample_main {
        max-width: 1200px;
        margin: 0 auto;
        display: flex;
    }
    .left-info-box {
        width: 322px;
        margin-right: 36px;
        padding-top: 36px;
        .top-box,
        .bottom-box {
            > .tit {
                @include font16;
                font-weight: 400;
                margin-bottom: 24px;
            }
            ul {
                li {
                    display: flex;
                    position: relative;
                    margin-bottom: 40px;
                    &::before {
                        content: "";
                        display: block;
                        position: absolute;
                        left: 7px;
                        top: 16px;
                        width: 1px;
                        height: calc(100% + 40px);
                        background-color: #10a300;
                    }
                    &:last-child {
                        margin-bottom: 0;
                        &::before {
                            display: none;
                        }
                    }
                    .index {
                        flex-shrink: 0;
                        width: 16px;
                        height: 16px;
                        border: none;
                        border-radius: 50%;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        font-weight: 600;
                        margin-right: 25px;
                        background-color: #10a300;
                        img {
                            display: block;
                            width: 24px;
                            height: auto;
                        }
                    }
                    .txt {
                        @include font12;
                        color: $textColor3;
                    }
                    .txt-box {
                        .tit {
                            @include font14;
                            font-weight: 600;
                            margin-bottom: 8px;
                        }
                        .des {
                            @include font14;
                            color: $textColor3;
                        }
                    }
                }
            }
        }
        .top-box {
            padding-left: 24px;
            margin-bottom: 80px;
        }
    }
    .sample_form_item {
        flex: 1;
        background: $textColor7;
        padding: 40px;
    }

    ::v-deep .validate_error .error_info {
        @include font12;
    }

    .sample-box {
        .error_input {
            @include errorInput;
        }
        .box-title {
            @include font20;
            font-weight: 600;
            color: $textColor1;
            margin: 0 0 24px 0;
        }
        .product-box {
            .add-pro-box {
                display: flex;
                align-items: flex-start;
                > img {
                    width: 104px;
                    height: 42px;
                    margin-top: 22px;
                }
                .add-pro-content {
                    flex: 1;
                    margin: 0 16px;
                    .input-title {
                        @include font13();
                        margin-bottom: 4px;
                        color: #19191a;
                    }
                    .input-box {
                        display: flex;
                        align-items: center;
                        .prefix {
                            border-radius: 2px 0px 0px 2px;
                            top: 1px;
                            left: 1px;
                            background: #f7f7f7;
                            width: 48px;
                            height: 42px;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            border: 1px solid #e5e5e5;
                            border-right: 0;
                            flex-shrink: 0;
                        }
                        > input {
                            border-radius: 0 2px 2px 0;
                        }
                    }
                }
                ::v-deep .fs-button {
                    margin-top: 22px;
                }
            }
            .product-list {
                background: #fafafb;
                padding: 0 24px;
                margin-top: 20px;
                .product-item {
                    display: flex;
                    align-items: flex-start;
                    padding: 20px 0;
                    &:not(:first-child) {
                        border-top: 1px solid #e5e5e5;
                    }
                    img {
                        width: 80px;
                        height: 80px;
                    }
                    .txt-box {
                        flex: 1;
                        margin: 0 16px 0 20px;
                        .tit {
                            @include font14();
                            color: #19191a;
                            @include txt-more-hid(3);
                        }
                        .txt {
                            @include font14();
                            color: #707070;
                            margin-top: 12px;
                        }
                    }

                    .handle-box {
                        display: flex;
                        align-items: center;
                        justify-content: space-between;
                        ::v-deep .qty-box {
                            background-color: #fff;
                        }

                        .del-box {
                            padding: 1px 10px;
                            margin: 0 -10px 0 40px;
                            cursor: pointer;
                            .iconfont {
                                color: #707070;
                            }
                        }
                    }
                }
            }
        }

        .pro_tabBox {
            .lt {
                height: 80px;
                padding-right: 16px;
                flex: 1;
                display: flex;
                align-items: center;
                .img-box {
                    width: 80px;
                    height: 80px;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    margin-right: 20px;
                    img {
                        display: block;
                        width: 80px;
                        height: 80px;
                    }
                }
                .inp-box {
                    flex: 1;
                    .inp-content-box {
                        display: flex;
                        position: relative;
                        .p_error {
                            width: 165%;
                            position: absolute;
                            bottom: -26px;
                            left: 0;
                        }
                    }
                    .qty-box {
                        display: none;
                    }
                }
                .txt-box {
                    height: 100%;
                    flex: 1;
                    display: flex;
                    flex-direction: column;
                    justify-content: space-between;
                    .tit {
                        @include font14;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        display: -webkit-box;
                        -webkit-line-clamp: 2;
                        -webkit-box-orient: vertical;
                    }
                    .txt {
                        @include font14;
                        color: #707070;
                    }
                }
            }
            .rt {
                max-width: 400px;
                height: 80px;
                display: flex;
                justify-content: flex-end;
                align-items: center;
                .qty-box {
                    margin-right: 16px;
                }
                .del-box {
                    height: 42px;
                    line-height: 42px;
                    text-align: center;
                    .iconfont {
                        cursor: pointer;
                        color: #707070;
                    }
                }
            }
            .pro-list {
                display: flex;
                flex-direction: column;
                li {
                    display: flex;
                    padding: 24px 0;
                    border-bottom: 1px solid #e5e5e5;
                    &:first-child {
                        padding-top: 0;
                    }
                    .rt {
                        align-items: flex-start;
                    }
                }
            }
            .add-pro-box {
                display: flex;
                padding-bottom: 24px;
                border-bottom: 1px solid #e5e5e5;
                &.has-pro {
                    padding-top: 24px;
                }
            }
        }
        &:last-child {
            margin-top: 24px;
        }
    }
    .sample-content {
        width: 100%;
        display: flex;
        align-items: flex-start;
        justify-content: space-between;
        &.sample-content-cn {
            flex-direction: row-reverse;
            .sample-item {
                margin-left: 0;
                &:first-child {
                    margin-left: 20px;
                }
            }
        }
        select {
            height: 42px;
            color: $textColor1;
        }
        .sample-item {
            flex: 1;
            margin-left: 20px;
            margin-bottom: 16px;
            &:first-child {
                margin-left: 0;
            }
            display: flex;
            flex-direction: column;
            ::v-deep {
                .error_info {
                    a {
                        color: $textColor4;
                        text-decoration: underline;
                    }
                }
                .cn_area_wrap {
                    .p_cn_area_wrap,
                    .m_cn_area_wrap {
                        width: 100%;
                    }
                    .area_value {
                        @include font13;
                        color: $textColor3;
                    }
                }
            }

            .sample-item-title {
                display: flex;
                justify-content: space-between;
                align-items: center;
                span {
                    @include font12;
                    color: $textColor1;
                    margin-bottom: 4px;
                }
                .textarea-num {
                    color: $textColor3;
                    @include font12;
                    text-align: right;
                    em {
                        font-style: normal;
                        &.active {
                            color: $textColor4;
                        }
                    }
                }
            }
            input,
            select,
            textarea,
            ::v-deep .tel-code .code,
            ::v-deep .select-country .select-country-active,
            ::v-deep .select-country .country-wrap .inp,
            ::v-deep .select-country .country-wrap .country-box .item .country-name {
                @include font13;
            }
            ::v-deep.tel-code .tel:focus {
                border: 1px solid #19191a;
            }
            .select-box {
                position: relative;
                cursor: pointer;
                .ph-box {
                    width: 100%;
                    height: 42px;
                    padding: 0 32px 0 12px;
                    position: absolute;
                    left: 0;
                    @include font12;
                    color: $textColor3;
                    display: flex;
                    align-items: center;
                }
            }
            &.sample-item-comments,
            &.width100 {
                width: 100%;
            }
            ::v-deep {
                .fs-select .options-wrap {
                    .options-box .item .list-name {
                        @include font13;
                    }
                }
                .fs-select .fs-select-active {
                    .select-active-box {
                        @include font13;
                    }
                    .placeholder {
                        @include font13;
                    }
                }
            }
            textarea {
                &::placeholder {
                    @include font13;
                }
            }
            .input-item-flex {
                display: flex;
                justify-content: space-between;
                align-items: center;
            }
            .input-item-number {
                display: flex;
                justify-content: flex-end;
                width: 100%;
            }
            .textarea-num {
                color: $textColor3;
                @include font12;
                margin-top: 4px;
                em {
                    font-style: normal;
                    &.active {
                        color: $textColor4;
                    }
                }
            }
        }
    }
    .agreement-box {
        margin-bottom: 12px;
        .agreement {
            width: 100%;
            display: flex;
            align-items: flex-start;
            input {
                // font-size: 16px;
                margin: 1px 8px 0 0;
                @media (max-width: 960px) {
                    margin-top: 1px;
                }
            }
            > p {
                @include font12;
                color: $textColor3;
                a {
                    color: #0060bf;
                }
            }
            &:hover {
                cursor: pointer;
                input[type="checkbox"] {
                    &:before {
                        color: #707070;
                    }
                }
            }
        }
        .validate_error {
            padding-left: 26px;
        }
    }
    .sbtn-box {
        margin-top: 32px;
        .fs-button {
            width: 110px;
        }
    }
    .success_main {
        padding: 20px 68px 20px 32px;
        display: flex;
        .lt {
            // padding: 1px 0 0;
            height: 24px;
            .icon {
                color: #18a209;
                font-size: 20px;
            }
        }
        .rt {
            padding: 0 0 0 8px;
            .tit {
                @include font16;
                color: $textColor1;
            }
            .txt {
                @include font14;
                color: $textColor3;
                margin: 12px 0 0;
            }
        }
    }
    .agreement {
        ::v-deep .fs-popup-ctn {
            width: 680px;
            border-radius: 3px;
            height: auto;
            .fs-popup-header {
                @include font20;
                padding: 20px 68px 20px 32px;
                .iconfont_close {
                    right: 32px;
                    line-height: 30px;
                }
            }
            @media (max-width: 414px) {
                width: 100%;
                .fs-popup-body .agreement-content {
                    padding: 16px 16px 40px;
                }
            }
            .title_box {
                .title {
                    // @include font16;
                    font-weight: normal;
                    padding-right: 0;
                }
            }
            .agreement-content {
                padding: 20px 32px 40px;
                li {
                    display: flex;
                    margin-bottom: 10px;
                    color: $textColor3;
                    &:last-child {
                        margin-bottom: 0;
                    }
                    span {
                        width: 4px;
                        height: 4px;
                        background-color: $textColor3;
                        border-radius: 50%;
                        flex-shrink: 1;
                        margin: 10px 14px 0 0;
                    }
                    p {
                        @include font14;
                        flex-shrink: 1;
                        flex: 1;
                    }
                }
            }
        }
    }

    &.fff {
        background-color: #ffffff;
    }
    .third-step-wrap {
        padding: 100px 48px;
        display: flex;
        flex-direction: column;
        align-items: center;
        transition: all 0.3s;
        .success-logo {
            display: block;
            font-size: 50px;
            color: #10a300;
        }
        .success-tit {
            @include font24;
            font-weight: 600;
            margin: 16px 0 8px 0;
            text-align: center;
        }
        .success-des {
            max-width: 660px;
            @include font14;
            color: $textColor3;
            margin-bottom: 16px;
            text-align: center;
        }
    }
    @media (max-width: 768px) {
        .third-step-wrap {
            padding: 50px 24px;
        }
    }
}
@media (max-width: 1200px) {
    .sample-request {
        padding: 36px 24px 40px 24px;
        .sample-box {
            .pro_tabBox {
                .lt {
                    .inp-box {
                        .inp-content-box {
                            .p_error {
                                width: 200%;
                            }
                        }
                    }
                }
            }
        }
    }
}
@media (max-width: 1024px) {
    .sample-request {
        .sample-box {
            .pro_tabBox {
                .lt {
                    padding-right: 20px;
                    .inp-box {
                        .inp-content-box {
                            .p_error {
                                width: 300%;
                            }
                        }
                    }
                }
                .rt {
                    max-width: 326px;
                }
            }
            .sample-content {
                flex-wrap: wrap;
            }
        }
    }
    .success_popup {
        &::v-deep > .fs-popup {
            .fs-popup-ctn {
                width: 680px !important;
                height: initial !important;
            }
        }
    }
}
@media (max-width: 980px) {
    .sample-request {
        .sample-box {
            .pro_tabBox {
                .lt {
                    .inp-box {
                        .inp-content-box {
                            .p_error {
                                width: 350%;
                            }
                        }
                    }
                }
            }
        }
    }
}
@media (max-width: 960px) {
    .sample-request {
        padding: 0;
        background-color: $bgColor3;
        .sample_main {
            width: 100%;
            flex-direction: column-reverse;
        }
        .left-info-box {
            width: 100%;
            padding: 0 24px 40px 24px;
            margin-right: 0;
            .top-box,
            .bottom-box {
                > .tit {
                    @include font20;
                    font-weight: 600;
                    margin-bottom: 24px;
                }
                ul {
                    li {
                        margin-bottom: 32px;
                        &::before {
                            content: "";
                            display: block;
                            position: absolute;
                            left: 8px;
                            top: 16px;
                            width: 1px;
                            height: calc(100% + 32px - 16px);
                            background-color: #cccccc;
                        }
                        .index {
                            width: 16px;
                            height: 16px;
                            img {
                                display: block;
                                width: 20px;
                                height: auto;
                            }
                        }
                        .txt-box {
                            .tit {
                                margin-bottom: 4px;
                            }
                        }
                    }
                }
            }
            .top-box {
                margin-bottom: 36px;
            }
        }
        .sample_form_item {
            padding: 36px 24px;
            .sbtn-box {
                .fs-button {
                    width: 100%;
                }
            }
        }
        .sample-box {
            .box-title {
                @include font20;
                margin: 0 0 24px 0;
            }
            .pro_tabBox {
                .lt {
                    height: auto;
                    align-items: flex-start;
                    margin-bottom: 12px;
                    .img-box {
                        margin-right: 24px;
                    }
                    .txt-box {
                        min-height: 80px;
                        .tit {
                            -webkit-line-clamp: 3;
                            margin-bottom: 8px;
                        }
                    }
                }
                .rt {
                    padding-left: 104px;
                    width: 100%;
                    max-width: initial;
                    height: auto;
                    display: flex;
                    justify-content: space-between;
                    .qty-box {
                        margin-right: 0;
                    }
                    .del-box {
                        text-align: right;
                    }
                }
                .pro-list {
                    li {
                        flex-direction: column;
                    }
                }
                .add-pro-box {
                    flex-direction: column;
                    .lt {
                        margin-bottom: 20px;
                        padding-right: 0;
                        .img-box {
                            display: none;
                        }
                        .inp-box {
                            display: flex;
                            .inp-content-box {
                                flex: 1;
                                flex-direction: column;
                                .p_error {
                                    position: relative;
                                    bottom: initial;
                                    left: initial;
                                }
                            }
                            .qty-box {
                                display: flex;
                                margin-left: 20px;
                            }
                        }
                    }
                    .rt {
                        padding-left: 0;
                        width: 100%;
                        .qty-box {
                            display: none;
                        }
                        .btn-box {
                            width: 100%;
                            .fs-button {
                                width: 100%;
                            }
                        }
                    }
                }
            }
            .sample-content {
                &:nth-of-type(1),
                &:nth-of-type(2) {
                    flex-wrap: wrap;
                    .sample-item {
                        width: 100%;
                    }
                }
            }
        }
    }
}
@media (max-width: 768px) {
    .sample-request {
        .left-info-box {
            padding: 0 16px 40px 16px;
        }
        .sample_form_item {
            padding: 36px 16px;
        }
    }
    .success_popup {
        &::v-deep > .fs-popup {
            .fs-popup-ctn {
                width: calc(100% - 40px) !important;
            }
        }
    }
    .success_main {
        padding: 4px 20px 40px;
        .rt {
            .txt {
                margin: 14px 0 0;
            }
        }
    }
}
@media screen and (max-width: 728px) {
    .sample-request {
        .agreement {
            ::v-deep .fs-popup-ctn {
                width: calc(100% - 48px);
            }
        }
    }
    ::v-deep .fs-alert {
        .fs-alert-wrapper {
            width: calc(100% - 48px);
        }
    }
}
@media screen and (max-width: 414px) {
    .sample-request {
        .agreement {
            ::v-deep .fs-popup-ctn {
                width: 100%;
                height: 100%;
                border-radius: 0;
                .fs-popup-header {
                    @include font16;
                    padding: 12px 52px 12px 16px;
                    .iconfont_close {
                        right: 16px;
                        top: 12px;
                        line-height: 24px;
                    }
                }
                .fs-popup-body {
                    .agreement-content {
                        padding: 20px 16px 0 16px;
                    }
                }
            }
        }
    }
    ::v-deep .fs-alert {
        .fs-alert-wrapper {
            width: calc(100% - 64px);
            .success_main {
                flex-direction: column;
                padding: 32px 36px 24px;
                align-items: center;
                .lt {
                    height: 32px;
                    margin-bottom: 12px;
                    .icon {
                        font-size: 32px;
                    }
                }
                .rt {
                    padding: 0;
                    text-align: center;
                    .txt {
                        margin-top: 8px;
                    }
                }
            }
        }
    }
}
</style>
