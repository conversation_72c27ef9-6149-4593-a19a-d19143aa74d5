<template>
    <fs-popup :show="show" :title="title" @close="closePopup">
        <div class="report_sub">
            <div class="main_content">
                <!-- <p class="title_m">{{ title }}</p> -->
                <slot>
                    <!-- <p></p> -->
                </slot>
            </div>
            <div class="popup_footer">
                <div class="btn">
                    <fs-button type="grayline" @click="closePopup">{{ $c("pages.OrderReport.poupe.btn.cancel") }}</fs-button>
                    <fs-button @click="continueSub" :loading="loading">{{ $c("pages.OrderReport.poupe.btn.countinue") }}</fs-button>
                </div>
                <!-- <div class="btn_m">
                    <div @click="closePopup">{{ $c("pages.OrderReport.poupe.btn.cancel") }}</div>
                    <div @click="continueSub">{{ $c("pages.OrderReport.poupe.btn.countinue") }}</div>
                </div> -->
            </div>
        </div>
    </fs-popup>
</template>

<script>
import FsPopup from "../../../components/FsPopupNew/FsPopupNew.vue"
import FsButton from "../../../components/FsButton/FsButton.vue"
export default {
    components: {
        FsPopup,
        FsButton,
    },
    props: {
        loading: {
            type: Boolean,
            default: false,
        },
        show: {
            type: Boolean,
            default: false,
        },
        title: {
            type: String,
            default: "",
        },
    },
    data() {
        return {}
    },
    methods: {
        continueSub() {
            this.$emit("submit")
        },
        closePopup() {
            this.$emit("close")
        },
    },
}
</script>

<style lang="scss" scoped>
.report_sub {
    width: 480px;
    padding: 16px 24px 0;
    .main_content {
        > p {
            margin-bottom: 40px;
            @include font14;
            text-align: left;
        }
        .title_m {
            display: none;
        }
    }
    .popup_footer {
        display: flex;
        justify-content: flex-end;
        .btn {
            margin-bottom: 24px;
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            column-gap: 12px;
            @media (max-width: 768px) {
                margin-bottom: 16px;
            }
        }
        // .btn_m {
        //     display: none;
        // }
    }
}
@media (max-width: 1024px) {
    ::v-deep {
        .fs-popup {
            width: 480px;
            height: fit-content;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%);
        }
    }
    .report_sub {
        width: 480px;
    }
}
@media (max-width: 768px) {
    .report_sub {
        width: 350px;
        padding: 16px 16px 0;
    }
    ::v-deep {
        .fs-popup {
            width: 350px;
            .fs-popup-ctn {
                border-radius: 8px;
            }
            .fs-popup-header {
                padding: 16px 16px 0;
                .iconfont_close {
                    right: 20px;
                }
            }
        }
    }
}
</style>
