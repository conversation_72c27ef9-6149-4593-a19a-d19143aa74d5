<template>
    <div>
        <div class="report_table" :class="{ show_mask: list.length > 5 }" v-if="list.length > 0">
            <div class="report_table_pc">
                <div class="report_table_item thead">
                    <div class="left">
                        <span>{{ $c("pages.OrderReport.reportPage.name") }}</span>
                        <span>{{ $c("pages.OrderReport.reportPage.savedBy") }}</span>
                        <!-- <span>{{ $c("pages.OrderReport.reportPage.savedDate") }}</span> -->
                        <span>{{ $c("pages.OrderReport.subscriptionReports.lastModified") }}</span>
                    </div>
                    <div class="right"></div>
                </div>
                <div class="tbody">
                    <div class="report_table_item" v-for="(item, index) in list" :key="index">
                        <div class="left">
                            <span>{{ item.reporting_name }}</span>
                            <span>{{ item.customers_name }}</span>
                            <span>{{ item.created_at }}</span>
                        </div>
                        <div class="right">
                            <div class="run-link">
                                <span class="iconfont">&#xe730;</span>
                                <a href="javascript:;" @click="runReport(item)">{{ $c("pages.OrderReport.reportPage.run") }}</a>
                            </div>
                            <div class="run-link">
                                <span class="iconfont">&#xe745;</span>
                                <a href="javascript:;" @click="editReport(item.id)">{{ $c("pages.OrderReport.reportPage.edit") }}</a>
                            </div>
                            <div class="run-link">
                                <span class="iconfont">&#xe676;</span>
                                <a href="javascript:;" @click="renameReport(item)">{{ $c("pages.OrderReport.reportPage.rename") }}</a>
                            </div>
                            <div class="run-link">
                                <span class="iconfont">&#xe65f;</span>
                                <a href="javascript:;" @click="deleteReport(item)">{{ $c("pages.OrderReport.reportPage.delete") }}</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="report_table_m">
                <ul v-for="(item, index) in list" :key="index">
                    <li class="name_box">
                        <b>{{ $c("pages.OrderReport.reportPage.name") }}:</b>
                        <span>{{ item.reporting_name }}</span>
                    </li>
                    <li>
                        <b>{{ $c("pages.OrderReport.reportPage.savedBy") }}:</b>
                        <span>{{ item.customers_name }}</span>
                    </li>
                    <li>
                        <b>{{ $c("pages.OrderReport.reportPage.savedDate") }}:</b>
                        <span>{{ item.created_at }}</span>
                    </li>
                    <li>
                        <div class="tip">
                            <span class="iconfont" @click="showTIPs(index)">&#xf113;</span>
                            <ul class="tip_box" v-show="showTipsID === index">
                                <li @click.prevent="renameReport(item)">{{ $c("pages.OrderReport.reportPage.rename") }}</li>
                                <li @click.prevent="deleteReport(item)">{{ $c("pages.OrderReport.reportPage.delete") }}</li>
                            </ul>
                        </div>
                        <div class="btn">
                            <fs-button @click="runReport(item)" type="black">{{ $c("pages.OrderReport.reportPage.run") }}</fs-button>
                            <fs-button @click="editReport(item.id)" type="blackline">{{ $c("pages.OrderReport.reportPage.edit") }}</fs-button>
                        </div>
                    </li>
                </ul>
            </div>
        </div>
        <AccountEmpty v-else isMShadow :title="$c('pages.OrderReport.reportPage.empty.title')">
            <a @click="createNow" class="empty-link" href="javascript:;">{{ $c("pages.OrderReport.reportPage.empty.content1") }}</a>
        </AccountEmpty>
        <fs-popup-new :show="showPoup" @close="initPoup" :title="$c('pages.OrderReport.poupe.deleteReport')" :isMDrawer="true">
            <div class="save_report_poup">
                <div class="popu_content">
                    <p class="poup_title">{{ $c("pages.OrderReport.poupe.tips.type1") }}</p>
                    <span class="del_tips">{{ $c("pages.OrderReport.poupe.tips.text") }}</span>
                </div>
                <div class="option_btn">
                    <fs-button type="grayline" @click="cancelDelet">{{ $c("pages.OrderReport.poupe.btn.cancel") }}</fs-button>
                    <fs-button type="red" :loading="popupLoading" @click="delReport">{{ $c("pages.OrderReport.poupe.btn.countinue") }}</fs-button>
                </div>
            </div>
        </fs-popup-new>
        <!-- 重命名弹出层 -->
        <fs-popup-new :show="showRenamePoup" :loading="popupLoading" @close="initPoup" :title="$c('pages.OrderReport.reportPage.customReport')" :isMDrawer="true">
            <div class="save_report_poup">
                <div class="popu_content">
                    <p class="poup_title">{{ $c("pages.OrderReport.reportPage.name") }}</p>
                    <p>
                        <input type="text" v-model.trim="reportInfo.name" maxlength="51" @input="validateName" />
                        <validate-error :error="reportInfo.errTExt"></validate-error>
                    </p>
                </div>
                <div class="option_btn">
                    <fs-button type="grayline" @click="handeCancel">{{ $c("pages.OrderReport.poupe.btn.cancel") }}</fs-button>
                    <fs-button @click="continueReport" type="red">{{ $c("pages.OrderReport.poupe.btn.countinue") }}</fs-button>
                </div>
            </div>
        </fs-popup-new>
    </div>
</template>

<script>
import FsButton from "../../../components/FsButton/FsButton.vue"
import FsTips from "../../../components/FsTip/FsTip.vue"
import FsPopupNew from "../../../components/FsPopupNew/FsPopupNew.vue"
import ValidateError from "../../../components/ValidateError/ValidateError.vue"
import AccountEmpty from "@/components/AccountEmpty/AccountEmpty"
export default {
    components: {
        FsButton,
        FsTips,
        FsPopupNew,
        ValidateError,
        AccountEmpty,
    },
    props: {
        list: {
            type: Array,
            default: () => [],
        },
        reportType: {
            type: Number,
            default: 1,
        },
    },
    data() {
        return {
            showTipsID: -1,
            custom_reports: [],
            showPoup: false,
            showRenamePoup: false,
            reportInfo: {
                errTExt: "",
                name: "",
            },
            editReportItem: {},
            loading: false,
            popupLoading: false,
        }
    },
    methods: {
        showTIPs(index) {
            this.showTipsID = this.showTipsID === index ? -1 : index
        },
        renameReport(target) {
            this.GaOpratin("Rename")
            this.showPoup = false
            this.showRenamePoup = true
            this.reportInfo.name = target.reporting_name
            this.editReportItem = target
        },
        deleteReport(target) {
            this.GaOpratin("Delete")
            this.showPoup = true
            this.editReportItem = target
        },
        //确认删除报告
        delReport() {
            this.GaOpratin("Confirm Delete")
            const query = {
                id: this.editReportItem.id,
                isDelete: 1,
            }
            this.updataReport(query)
        },
        runReport(target) {
            this.GaOpratin("Run")
            // this.$router.push(this.localePath(`/reporting-detail.html?id=${id}`))
            this.$emit("run", target)
        },
        editReport(id) {
            this.GaOpratin("Edit")
            this.$emit("edit", { id })
            // this.$router.push(this.localePath(`/reporting-criteria.html?id=${id}`))
        },
        cancelDelet() {
            this.initPoup()
            this.GaOpratin("Cancel Delete")
        },
        validateName() {
            const target = this.reportInfo.name
            if (target.length < 1) {
                this.reportInfo.errTExt = this.$c("pages.OrderReport.poupe.text1")
                return false
            }
            if (target.length > 50) {
                this.reportInfo.errTExt = this.$c("pages.OrderReport.poupe.text4").replace("xx", "50")
                return false
            }
            this.reportInfo.errTExt = ""
            return true
        },
        // 初始化弹窗
        initPoup() {
            this.reportInfo.name = ""
            this.reportInfo.errTExt = ""
            this.showPoup = false
            this.showRenamePoup = false
            this.editReportItem = {}
        },
        continueReport() {
            this.GaOpratin("Confirm Rename")
            if (this.validateName()) {
                const query = {
                    id: this.editReportItem.id,
                    reporting_name: this.reportInfo.name,
                }
                this.updataReport(query)
            }
        },
        //更新报告
        updataReport(query) {
            this.popupLoading = true
            const parmas = { ...query, isPersonal: this.reportType }
            // 发起重命名请求
            this.$axios
                .post("/api/reporting/update", parmas)
                .then((res) => {
                    console.log(res, "update")
                    if (res.status === "success") {
                        this.initPoup()
                        this.$emit("updata", { type: 0 })
                    }
                })
                .catch((err) => {
                    const r = err.data.result
                    if (r == 2) {
                        this.reportInfo.errTExt = this.$c("pages.OrderReport.poupe.text3").replace("xxxx", query.reporting_name)
                    }
                    if (r == 1) {
                        this.reportInfo.errTExt = this.$c("pages.OrderReport.poupe.text6")
                    }
                })
                .finally(() => {
                    this.popupLoading = false
                })
        },
        // 数据埋点
        GaEmpty(val) {
            window.dataLayer &&
                window.dataLayer.push({
                    event: "uaEvent",
                    eventCategory: "Personal Hub_Order Reporting Page",
                    eventAction: "create_report",
                    eventLabel: val,
                    nonInteraction: false,
                })
        },
        GaOpratin(val) {
            window.dataLayer &&
                window.dataLayer.push({
                    event: "uaEvent",
                    eventCategory: "Personal Hub_Order Reporting Page",
                    eventAction: "custom_reports_operate",
                    eventLabel: val,
                    nonInteraction: false,
                })
        },
        // 取消重命名
        handeCancel() {
            this.initPoup()
            this.GaOpratin("Cancel Rename")
        },
        //过滤输入框的空格
        trimStr(o, tar) {
            this[o][tar] = this[o][tar].replace(/\s+/g, "")
        },
        createNow() {
            // this.GaEmpty("Custom Reports Empty")
            localStorage.setItem("reportCriteria", JSON.stringify({}))
            // this.$router.push(this.localePath(`/reporting-criteria.html`))
            this.$emit("createNow")
        },
    },
}
</script>

<style lang="scss" scoped>
.report_table {
    .report_table_m {
        display: none;
    }
    .report_table_item {
        @include font14;
        display: grid;
        // grid-template-columns: 1.5fr 1fr;
        grid-template-columns: 50% 40%;
        justify-content: space-between;
        padding: 20px 0px;
        margin: 0 24px;
        &.thead {
            background-color: #f7f7f7;
            margin: 0;
            padding: 12px 24px;
            font-weight: 600;
        }
        > div {
            justify-content: space-between;
            column-span: 5px;
        }
        .left {
            padding-right: 48px;
            display: grid;
            //    grid-template-columns: 2fr 1fr 1fr;
            grid-template-columns: 50% 25% 25%;
            column-gap: 30px;
            > span {
                min-width: max-content;
                &:first-child {
                    min-width: auto;
                    word-break: break-all;
                }
                &:nth-child(2) {
                    min-width: auto;
                    word-break: break-all;
                }
            }
        }
        .right {
            padding-left: 24px;
            display: flex;
            justify-content: space-between;
            .run-link {
                display: flex;
                align-items: center;
                .iconfont {
                    font-size: 16px;
                    margin-right: 4px;
                    color: #19191a;
                }
                a {
                    @include blackLinkTwo;
                }
            }
        }
    }
    .tbody {
        max-height: 314px;
        overflow-y: scroll;
        .report_table_item {
            border-bottom: 1px solid #e5e5e5;
            &:last-child {
                border: none;
            }
        }
        scrollbar-width: none; /* Firefox */
        -ms-overflow-style: none; /* IE 10+ */
        //Chrome和Safari浏览器
        &::-webkit-scrollbar {
            display: none;
        }
    }
}
.save_report_poup {
    width: 680px;
    padding: 20px 32px;
    .poup_title {
        @include font12;
        margin-bottom: 4px;
    }
    .del_tips {
        color: #707070;
        @include font14;
    }
    > span {
        margin-top: 8px;
        @include font14;
        color: $textColor3;
    }
    .option_btn {
        display: flex;
        justify-content: flex-end;
        padding-top: 20px;
        margin-top: 20px;
        .fs-button {
            margin-left: 12px;
        }
    }
    .input_error {
        border-color: #c00000;
    }
}
::v-deep {
    .fs-popup-ctn {
        .fs-popup-header {
            .iconfont_close {
                // top: 50%;
                // transform: translateY(-50%);
            }
        }
    }
}
@media (max-width: 1024px) {
    .report_table {
        .report_table_item {
            .left {
                padding-right: 0;
            }
            .right {
                // padding-left: 87px;
                padding-left: 0px;
            }
        }
    }
}
@media (max-width: 760px) {
    .report_table {
        .report_table_pc {
            display: none;
        }
        .report_table_m {
            display: block;
            > ul {
                box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.1);
                border-radius: 12px;
                margin-bottom: 20px;
                padding: 0 16px;
                &:last-child {
                    margin-bottom: 0;
                }
                > li {
                    padding: 16px 0;
                    display: flex;
                    justify-content: space-between;
                    border-bottom: 1px solid #e5e5e5;
                    @include font14;
                    &:last-child {
                        border: none;
                    }
                    &:last-child {
                        display: flex;
                        flex-direction: column;
                        padding: 20px 0;
                        > .tip {
                            text-align: right;
                            position: relative;
                            .tip_box {
                                position: absolute;
                                width: 148px;
                                right: 27px;
                                top: 50%;
                                transform: translateY(-50%);
                                z-index: 2;
                                background-color: #fff;
                                box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.1);
                                text-align: center;
                                padding: 0 16px;
                                border-radius: 3px;
                                @include font13;
                                &:before {
                                    content: "";
                                    width: 15px;
                                    height: 15px;
                                    background-color: #fff;
                                    position: absolute;
                                    top: 28px;
                                    right: -7px;
                                    box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.1);
                                    transform: rotate(-45deg);
                                }
                                &:after {
                                    content: "";
                                    width: 16px;
                                    height: 24px;
                                    background-color: #fff;
                                    position: absolute;
                                    top: 50%;
                                    right: 0px;
                                    transform: translateY(-50%);
                                }
                                li {
                                    padding: 8px 0;
                                    border-bottom: 1px solid #e5e5e5;
                                    &:last-child {
                                        border: none;
                                    }
                                }
                            }
                        }
                        .btn {
                            display: flex;
                            flex-direction: column;
                            .fs-button {
                                &:first-child {
                                    margin-bottom: 12px;
                                    margin-top: 16px;
                                }
                            }
                        }
                    }
                    &.name_box {
                        background-color: #f7f7f7;
                        margin: 0 -16px;
                        padding: 16px 16px;
                        // border: none;
                        border-radius: 12px 12px 0 0;
                        > span {
                            display: inline-block;
                            word-break: break-all;
                            text-align: right;
                        }
                        > b {
                            display: inline-block;
                            min-width: max-content;
                            margin-right: 24px;
                        }
                    }
                }
            }
        }
    }
    .save_report_poup {
        width: 100%;
        display: flex;
        flex-direction: column;
        // height: calc(100vh - 50px);
        padding: 20px 16px;
        .popu_content {
            flex: 1;
            overflow: scroll;
        }
        .option_btn {
            justify-self: flex-end;
            display: flex;
            flex-direction: column-reverse;
            > .fs-button {
                margin-left: 0;
                margin-top: 12px;
                &:last-child {
                    margin-bottom: 0;
                }
            }
        }
    }
}
</style>
