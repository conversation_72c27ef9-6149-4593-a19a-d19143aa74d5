<template>
    <div class="company_report">
        <div class="title title_pc">
            <p>
                <b>{{ $c("pages.OrderReport.companyReport.companyReports").replace("xxxx", pageConfig.total) }}</b>
            </p>
            <div class="year_select">
                <span>{{ $c("pages.OrderReport.criteria.selectTime") }}:</span>
                <fs-select :options="yearList" v-model="yearSelect" trigger="hover" @change="filterYear"></fs-select>
            </div>
        </div>
        <div class="title title_m">
            <b>{{ $c("pages.OrderReport.companyReport.companyReports").replace("xxxx", pageConfig.total) }}</b>
            <div @click="showMFilter">
                <span class="iconfont">{{ hasSelectUser.length > 0 ? "&#xf239;" : "&#xe712;" }}</span
                ><span>{{ $c("pages.Bills.filter") }}</span>
            </div>
        </div>
        <div class="content" v-if="list.length > 0">
            <div class="table">
                <div class="thead report_table_item">
                    <div class="left">
                        <div>
                            <b>{{ $c("pages.OrderReport.criteria.table.account") }}</b>
                        </div>
                        <div>
                            <b>{{ $c("pages.OrderReport.companyReport.userName") }}</b>
                        </div>
                        <div>
                            <div class="filter_by">
                                <b>{{ $c("pages.OrderReport.companyReport.reportBy") }}</b>
                                <fs-popover :icon="false" :closeIcon="false" position="bottom" trigger="click" ref="popover">
                                    <template #trigger>
                                        <!-- <span class="iconfont filter_icon">{{ hasFilterUser ? "&#xf239;" : "&#xe712;" }}</span> -->
                                        <span class="iconfont filter_icon" :class="{ active: hasSelectUser.length > 0 }">{{ hasSelectUser.length > 0 ? "&#xf239;" : "&#xe712;" }}</span>
                                    </template>
                                    <div class="popover_main">
                                        <label class="popover_main_title" :class="selecAllStatus" @click="selectAll">
                                            <span class="iconfont" :class="selectIcon[selecAllStatus]"></span>
                                            <span>{{ $c("pages.OrderDetail.newDetail.all") }}</span>
                                        </label>
                                        <ul>
                                            <li v-for="item in userList" :key="item.id" class="popover_main_item">
                                                <label class="checkbox">
                                                    <input type="checkbox" v-model="item.selected" />
                                                    <span>{{ item.name }}</span>
                                                </label>
                                                <!-- <span class="iconfont" @click="selectItem(item)">{{ item.selected ? "&#xf186;" : "&#xf043;" }}</span>
                                                <span>{{ item.name }}</span> -->
                                            </li>
                                        </ul>
                                        <div class="popover_main_footer">
                                            <fs-button type="grayline" @click="handCancelPopover">{{ $c("pages.OrderReport.poupe.btn.cancel") }}</fs-button>
                                            <fs-button type="blackline" @click="handFilter">{{ $c("pages.RMAReturn.ok") }}</fs-button>
                                        </div>
                                    </div>
                                </fs-popover>
                            </div>
                        </div>
                    </div>
                    <div class="right">
                        <div></div>
                        <div></div>
                    </div>
                </div>
                <div class="tbody" ref="obversver">
                    <div class="tr report_table_item" v-for="item in list" :key="item.id">
                        <div class="left">
                            <span>{{ item.customers_number_new }}</span>
                            <span>{{ item.customers_name }}</span>
                            <span>{{ item.reporting_name }}</span>
                        </div>
                        <div class="right">
                            <div>
                                <p>
                                    <a href="javascript:;" @click="runReport($event, item)">{{ $c("pages.OrderReport.reportPage.run") }}</a>
                                </p>
                            </div>
                            <div>
                                <p>
                                    <!-- <a href="javascript:;" v-if="role !== 3 && item.is_subscribe !== 1" @click="subscribeReport($event, item)">{{ $c("pages.OrderReport.companyReport.subscribe") }}</a> -->
                                    <a href="javascript:;" v-if="['AA', 'TA'].includes(roleCode) && item.is_subscribe !== 1" @click="subscribeReport($event, item)">{{
                                        $c("pages.OrderReport.companyReport.subscribe")
                                    }}</a>
                                </p>
                            </div>
                        </div>
                    </div>
                    <p ref="loadingMore" v-show="isContinuPage" class="loading_more">&nbsp;</p>
                </div>
            </div>
            <ul class="m_content">
                <li class="m_content_item" v-for="item in list" :key="item.id">
                    <p>
                        <b>{{ $c("pages.OrderReport.criteria.table.account") }}</b> <span>{{ item.customers_number_new }}</span>
                    </p>
                    <p>
                        <b>{{ $c("pages.OrderReport.companyReport.userName") }}</b> <span>{{ item.customers_name }}</span>
                    </p>
                    <p>
                        <b>{{ $c("pages.OrderReport.subscriptionReports.report") }}</b> <span>{{ item.reporting_name }}</span>
                    </p>
                    <div class="m_content_btn">
                        <fs-button type="black" @click="runReport($event, item)">{{ $c("pages.OrderReport.reportPage.run") }}</fs-button>
                        <!-- <fs-button type="blackline" v-if="role !== 3 && item.is_subscribe !== 1" @click="subscribeReport($event, item)">{{ $c("pages.OrderReport.companyReport.subscribe") }}</fs-button> -->
                        <fs-button type="blackline" v-if="['AA', 'TA'].includes(roleCode) && item.is_subscribe !== 1" @click="subscribeReport($event, item)">{{
                            $c("pages.OrderReport.companyReport.subscribe")
                        }}</fs-button>
                    </div>
                </li>
            </ul>
            <div class="pagination_m">
                <span v-show="isContinuPage" @click="pageChange">{{ $c("pages.OrderReport.seeMore") }}<i class="iconfont">&#xe704;</i></span>
                <span v-show="showhiddenClose" @click="initList">{{ $c("pages.OrderReport.seeLess") }}<i class="iconfont">&#xe700;</i></span>
            </div>
        </div>
        <div class="empty" v-if="!list.length && !isLoading">
            <h3>{{ $c("pages.OrderReport.companyReport.empty.title") }}</h3>
            <p>{{ $c("pages.OrderReport.companyReport.empty.tips") }}</p>
        </div>
        <!-- <fs-pagination :pageConfig="pageConfig" @changeCurrentPage="changeCurrentPage"></fs-pagination> -->
        <new-popup :show="showPopup" :loading="loading" :title="$c('pages.OrderReport.companyReport.popupTitle')" @close="closePopup" @submit="submit">
            <div class="popup_content">{{ $c("pages.OrderReport.companyReport.popupTip") }}</div>
        </new-popup>
        <m-filter v-if="show" @close="closeFilter" @submit="submitFilter" :userList="userList" :yearList="yearList" :yearSelect="yearSelect"></m-filter>
    </div>
</template>

<script>
// import FsSelect from "../../../components/FsSelect/FsSelect.vue"
import FsSelect from "./NewFsSelect.vue"
import FsButton from "../../../components/FsButton/FsButton.vue"
import FsPopover from "../../../components/FsPopover/index.vue"
import FsPagination from "@/components/FsPagination/FsPagination"
import NewPopup from "./Popup.vue"
import MFilter from "./Mfilter.vue"
import fixScroll from "@/util/fixScroll.js"
import { mapState } from "vuex"
export default {
    props: {
        info: {
            type: Object,
            default: () => {
                return {
                    count: 0,
                    data: [],
                    nameArr: [],
                    yearArr: [],
                }
            },
        },
        role: {
            type: Number | String,
            default: 3,
        },
        roleCode: {
            type: String,
            default: "",
        },
        isLoading: {
            type: Boolean,
            default: false,
        },
    },
    components: {
        FsSelect,
        NewPopup,
        FsButton,
        FsPopover,
        MFilter,
        FsPagination,
    },
    data() {
        return {
            yearList: [],
            list: [],
            userList: [],
            showPopup: false,
            selectIcon: {
                all: "iconfs_2022010501icon",
                half: "iconfs_2022111101icon",
                none: "iconfs_2020091143icon",
            },
            show: false,
            loading: false,
            pauseReportInfo: {},
            yearSelect: "",
            pageConfig: {
                pageSize: 5,
                pageNo: 1,
                total: 0,
            },
            intersectionObserver: null,
            //已经选择的user
            hasSelectUser: [],
            timer: null,
        }
    },
    methods: {
        closePopup(confirm) {
            this.pauseReportInfo = {}
            this.showPopup = false
            window.dataLayer &&
                window.dataLayer.push({
                    event: "uaEvent",
                    eventCategory: this.pageGroup,
                    eventAction: "company_reports_operate",
                    eventLabel: confirm ? "Confirm Subscribe" : "Cancel Subscribe",
                    nonInteraction: false,
                })
        },
        // 订阅报告
        subscribeReport(e, val) {
            e?.preventDefault()
            this.showPopup = true
            this.pauseReportInfo = val
            window.dataLayer &&
                window.dataLayer.push({
                    event: "uaEvent",
                    eventCategory: this.pageGroup,
                    eventAction: "company_reports_operate",
                    eventLabel: "Subscribe",
                    nonInteraction: false,
                })
        },
        runReport(e, val) {
            e?.preventDefault()
            this.$emit("run", val)
            window.dataLayer &&
                window.dataLayer.push({
                    event: "uaEvent",
                    eventCategory: this.pageGroup,
                    eventAction: "company_reports_operate",
                    eventLabel: "Run",
                    nonInteraction: false,
                })
        },
        submit() {
            this.loading = true
            const { id } = this.pauseReportInfo
            // 发送请求
            this.$axios
                .post("/api/reporting/subscribe", { id })
                .then((res) => {
                    this.loading = false
                    const { code, status } = res
                    if (code === 200 && status === "success") {
                        this.pageConfig.pageNo = 1
                        this.$emit("update")
                        this.closePopup(true)
                    }
                })
                .catch((err) => {
                    this.loading = false
                    console.log(err)
                })
        },
        selectItem(target) {
            this.userList.map((item) => {
                if (item.id === target.id) {
                    item.selected = !item.selected
                }
                return item
            })
        },
        selectAll() {
            if (this.selecAllStatus !== "all") {
                this.userList.map((item) => {
                    item.selected = true
                    return item
                })
            } else {
                this.userList.map((item) => {
                    item.selected = false
                    return item
                })
            }
        },
        handCancelPopover(confirm = false) {
            const popDom = this.$refs.popover
            if (popDom) {
                popDom.handleClose()
            }
            window.dataLayer &&
                window.dataLayer.push({
                    event: "uaEvent",
                    eventCategory: this.pageGroup,
                    eventAction: "company_reports_operate",
                    eventLabel: confirm ? "Confirm Select Creator" : "Cancel Select Creator",
                    nonInteraction: false,
                })
        },
        handFilter() {
            this.pageConfig.pageNo = 1
            const arr = []
            this.userList.forEach((item) => {
                if (item.selected) {
                    arr.push(item.id)
                }
            })
            this.hasSelectUser = arr
            this.$emit("filterUser", arr)
            this.handCancelPopover(true)
        },
        showMFilter() {
            this.show = true
            fixScroll.fixed()
        },
        closeFilter() {
            this.show = false
            fixScroll.unfixed()
        },
        submitFilter(val) {
            console.log(val, "valll")
            const { newUserList, yearIndex } = val
            this.hasSelectUser = newUserList
            //修改日期
            this.yearSelect = yearIndex
            this.closeFilter()
            //发送请求
            this.$emit("update", { customersSelect: newUserList, yearSelect: yearIndex, type: 2 })
        },
        // changeCurrentPage(page) {
        //     console.log(page)
        // },
        filterYear(val) {
            const params = {
                yearSelect: val * 1,
                type: 2,
            }
            this.$emit("update", params)
            window.dataLayer &&
                window.dataLayer.push({
                    event: "uaEvent",
                    eventCategory: this.pageGroup,
                    eventAction: "company_reports_operate",
                    eventLabel: "Data Select_" + (val == "0" ? "All Year" : `Year ${val}`),
                    nonInteraction: false,
                })
        },
        pageChange() {
            if (this.timer) return
            if (this.isContinuPage) {
                //    发送请求
                this.$emit("pageChange", { page: this.pageConfig.pageNo + 1, type: 2 })
                this.timer = setTimeout(() => {
                    this.pageConfig.pageNo += 1
                    this.timer = null
                }, 1000)
            }
        },
        initList() {
            if (this.timer) return
            this.$emit("update", { pgae: 1, type: 2 })
            this.timer = setTimeout(() => {
                this.pageConfig.pageNo = 1
                this.timer = null
            }, 1000)
        },
    },
    computed: {
        ...mapState({
            pageGroup: (state) => state.ga.pageGroup,
            website: (state) => state.webSiteInfo.website,
        }),
        selecAllStatus() {
            if (this.userList.every((item) => item.selected)) {
                return "all"
            } else {
                if (this.userList.some((item) => item.selected)) {
                    return "half"
                } else {
                    return "none"
                }
            }
        },
        showhiddenClose() {
            const { pageSize, pageNo, total } = this.pageConfig
            return total > pageSize && pageNo > 1
        },
        //是否继续翻页
        isContinuPage() {
            const { pageSize, pageNo, total } = this.pageConfig
            const res = total > pageSize && pageSize * pageNo <= total
            if (!res && this.intersectionObserver) {
                this.intersectionObserver.disconnect()
                this.intersectionObserver = null
            }
            return res
        },
    },
    watch: {
        info: {
            handler(val) {
                const { data, nameArr, yearArr, count } = val
                this.pageConfig.total = count
                // 处理报告列表
                this.list = data || []
                //处理用户列表
                this.userList = nameArr?.map((item) => {
                    const selected = this.hasSelectUser.includes(item.customers_id)
                    return {
                        name: item.customers_name,
                        id: item.customers_id,
                        selected,
                    }
                })
                //处理年月选择列表
                let arr = [
                    { name: this.$c("pages.OrderReport.companyReport.allReports"), value: "0" },
                    // { name: "Last Sⅸ Full Months", value: 2 },
                ]
                if (yearArr && yearArr.length > 0) {
                    yearArr.forEach((item) => {
                        arr.push({
                            name: this.$c("pages.OrderReport.criteria.year").replace("xxxx", item),
                            value: item,
                        })
                    })
                }
                this.yearList = arr
                // 添加触底加载事件
                if (!val && this.intersectionObserver) return
                this.$nextTick(() => {
                    const loadingDom = this.$refs.loadingMore
                    const obversver = this.$refs.obversver
                    if (!(loadingDom && obversver)) return
                    this.intersectionObserver = new IntersectionObserver(
                        (entrys) => {
                            const { isIntersecting } = entrys[0]
                            if (isIntersecting) {
                                this.pageChange()
                            }
                        },
                        {
                            root: obversver || null,
                        }
                    )
                    // 开启观测
                    if (this.intersectionObserver) {
                        this.intersectionObserver.observe(loadingDom)
                    }
                })
            },
            immediate: true,
            deep: true,
        },
    },
}
</script>

<style lang="scss" scoped>
.company_report {
    .title {
        @include font14;
        display: flex;
        justify-content: space-between;
        margin-bottom: 12px;
        .year_select {
            display: flex;
            align-items: center;
            > span {
                min-width: max-content;
            }
            &::v-deep {
                .fs-select {
                    .fs-select-active {
                        border: none;
                        height: 22px;
                        padding: 0;
                        .select-active-box {
                            padding: 0 8px;
                        }
                        .icofnont-down {
                            font-size: 16px;
                        }
                    }
                    .options-wrap.options-wrap-absolute {
                        top: 22px;
                        right: 0;
                        left: auto;
                        min-width: max-content;
                        border: none;
                        box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.1);
                    }
                }
            }
        }
        &.title_m {
            display: none;
        }
    }
    .content {
        box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.1);
        margin-bottom: 20px;
        border-radius: 12px;
        overflow: hidden;
        .table {
            .report_table_item {
                @include font14;
                display: grid;
                grid-template-columns: 50% 40%;
                padding: 20px 0px;
                margin: 0 24px;
                justify-content: space-between;
                border-bottom: 1px solid #e5e5e5;
                align-items: center;
                .left {
                    display: grid;
                    grid-template-columns: 50% 25% 25%;
                    column-gap: 30px;
                    padding-right: 48px;
                    align-items: center;
                }
                .right {
                    padding-left: 50px;
                    display: grid;
                    grid-template-columns: repeat(4, 1fr);
                    column-gap: 8px;
                }
                &:last-of-type {
                    border: none;
                }
            }
            .thead {
                background-color: #f7f7f7;
                margin: 0 0;
                padding: 12px 24px;
                &.report_table_item {
                    border: none;
                }
            }
            .tbody {
                max-height: 314px;
                overflow-y: scroll;
            }
            .loading_more {
                text-align: center;
                min-height: 10px;
            }
        }
        .m_content {
            display: none;
        }
        .pagination_m {
            display: none;
        }
    }
}
.popup_content {
    margin-bottom: 20px;
    @include font14;
}
.empty {
    height: 290px;
    box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.1);
    text-align: center;
    display: flex;
    justify-content: center;
    flex-direction: column;
    margin-bottom: 20px;
    border-radius: 12px;
    padding: 0 24px;
    > h3 {
        @include font16;
        margin-bottom: 4px;
        font-weight: normal;
    }
    > p {
        @include font14;
        color: $textColor3;
    }
}
.filter_by {
    display: flex;
    align-items: center;
    .filter_icon {
        @include font18;
        font-weight: 400;
        color: #707070;
        &:hover {
            color: $textColor1;
        }
        &.active {
            color: $textColor1;
        }
    }
}
.popover_main {
    width: 260px;
    .popover_main_title {
        margin-bottom: 12px;
        display: flex;
        align-items: center;
        cursor: pointer;
        .iconfont {
            font-size: 14px;
            width: 14px;
            height: 14px;
            margin-right: 8px;
            color: rgba(25, 25, 26, 0.3);
            display: inline-flex;
            align-items: center;
        }
        &.all {
            .iconfont {
                color: #707070;
                &:hover {
                    color: #707070;
                }
            }
        }
        &.half {
            .iconfont {
                color: #707070;
                &:hover {
                    color: #707070;
                }
            }
        }
        &:hover {
            .iconfont {
                color: #707070;
            }
        }
    }
    > ul {
        border-top: 1px solid #e5e5e5;
        border-bottom: 1px solid #e5e5e5;
        padding: 12px 0;
        display: flex;
        flex-direction: column;
        row-gap: 8px;
    }
    .popover_main_item {
        @include font14;
        display: flex;
        align-items: center;
        .checkbox {
            font-size: 14px;
            display: flex;
            align-items: center;
            cursor: pointer;
        }
        input[type="checkbox"] {
            font-size: 14px;
            width: 14px;
            height: 14px;
            margin-right: 8px;
            &::before {
                font-size: 14px;
            }
        }
    }
    .popover_main_footer {
        margin-top: 12px;
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        column-gap: 12px;
    }
}
.fs-pagination {
    margin-top: -4px;
    margin-bottom: 20px;
}
@media (max-width: 768px) {
    .company_report {
        .title {
            &.title_pc {
                display: none;
            }
            &.title_m {
                display: flex;
            }
        }
        .content {
            border-radius: none;
            box-shadow: none;
            overflow: inherit;
            .table {
                display: none;
            }
            .m_content {
                display: block;
                > li {
                    box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.1);
                    border-radius: 12px;
                    margin-bottom: 20px;
                    overflow: hidden;
                    > p {
                        display: flex;
                        @include font14;
                        justify-content: space-between;
                        padding: 16px 0;
                        margin: 0 16px;
                        border-bottom: 1px solid #e5e5e5;
                        &:first-of-type {
                            background-color: #f7f7f7;
                            padding: 16px;
                            margin: 0;
                        }
                    }
                    .m_content_btn {
                        padding: 20px 16px;
                        display: flex;
                        flex-direction: column;
                        row-gap: 12px;
                    }
                }
            }
            .pagination_m {
                display: flex;
                > span {
                    flex: 1;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    color: #0060bf;
                    column-gap: 4px;
                    @include font14;
                    .iconfont {
                        @include font12;
                        line-height: 7px;
                    }
                }
            }
        }
    }
    .empty {
        height: 312px;
    }
    .fs-pagination {
        margin-top: -8px;
    }
}
</style>
