<template>
    <div class="m_soft">
        <div class="software_header">
            <div class="software_header_title">
                <h2>{{ $c("pages.MyAccount.menu.softwareDownload") }}</h2>
            </div>
            <FsInlineTips class="software_header_tip">
                <div v-html="$c('pages.SoftwareDownload.headerTip').replace('xxxx', localePath({ path: '/products_support.html' }))"></div>
            </FsInlineTips>
        </div>
        <div class="filter_box">
            <div class="form">
                <!-- <input type="text" class="inp" :placeholder="$c('pages.SoftwareDownload.itemID')" v-model.trim="searchInp" @keyup.enter="searchHandle" @blur="searchHandle" />
                <span class="iconfont iconfont-clear" v-if="searchInp" @click.stop="clearSearch">&#xf30a;</span>
                <button class="iconfont iconfont-search" @click.prevent="searchHandle">&#xe694;</button> -->
                <!-- <AccountSearchInput :placeholder="$c('pages.SoftwareDownload.itemID')" v-model="searchInp" @search="searchHandle" @blur="searchHandle" /> -->
                <FsSelectSearch @search="searchHandle" :placeholder="$c('pages.SoftwareDownload.itemID')" @focus="searchHandle" @blur="searchHandle" v-model="searchInp" @clear="clearSearch" />
            </div>
            <AccountMFilterBtn :isMFilter="isMFilter" :isUnfold="filterVisiable" @emitClick="handleOpenFilter"></AccountMFilterBtn>
        </div>
        <div class="result">
            <AccountEmpty v-if="list.length == 0" isShadow :title="$c('pages.SoftwareDownload.noFund')" :sub-title="$c('pages.SoftwareDownload.emptyTxt')">
                <nuxt-link class="empty-link" :to="localePath({ path: '/c/enterprise-switches-3079' })" tag="span">{{ $c("pages.SoftwareDownload.startShopping") }}</nuxt-link>
            </AccountEmpty>
            <div class="result_content" v-else>
                <ul>
                    <li v-for="(item, index) in list" :key="index" class="result_item">
                        <ul>
                            <li class="item_li">
                                <div class="left">{{ $c("pages.SoftwareDownload.releaseDate") }}:</div>
                                <div class="right">
                                    <p>{{ item.file_add_time_format }}</p>
                                </div>
                            </li>
                            <li class="item_li">
                                <div class="left">{{ $c("pages.SoftwareDownload.latestFileInformation") }}:</div>
                                <div class="right">
                                    <p>{{ item.file_title }}</p>
                                    <a :href="item.pdf_url" class="note">{{ $c("pages.SoftwareDownload.releaseNote") }}</a>
                                </div>
                            </li>
                            <li class="item_li">
                                <div class="left">{{ $c("pages.SoftwareDownload.productID") }}:</div>
                                <div class="right">
                                    <a :href="localePath({ path: `/products/${item.product_id}.html` })" class="products_id">#{{ item.product_id }}</a>
                                </div>
                            </li>
                            <li class="item_li">
                                <div class="left">{{ $c("pages.SoftwareDownload.size") }}:</div>
                                <div class="right">
                                    <p>{{ item.file_size }}</p>
                                </div>
                            </li>
                            <li class="item_li">
                                <div class="left">{{ $c("pages.SoftwareDownload.software") }}:</div>
                                <div class="right">
                                    <a :href="item.file_url" class="download"> <span class="iconfont">&#xe653;</span>{{ $c("pages.SoftwareDownload.download") }} </a>
                                </div>
                            </li>
                            <li class="item_li">
                                <div class="left">{{ $c("pages.SoftwareDownload.softwareNotification") }}</div>
                                <div class="right">
                                    <p>
                                        <a href="javascript:;" @click="onSubscribe(item)">{{ item.is_sub ? $c("pages.SoftwareDownload.unsubscribe") : $c("pages.SoftwareDownload.subscribe") }}</a>
                                    </p>
                                    <!-- <FsPopover :icon="false" trigger="click" class="btn_right" :mobileConventional="false">
                                        <div slot="trigger" :style="{ width: `100%` }">
                                            <p>
                                                <a href="javascript:;">{{ $c("pages.SoftwareDownload.subscribe") }}</a>
                                            </p>
                                        </div>
                                        <section class="btn_section">
                                            <p v-if="item.is_sub">{{ $c("pages.SoftwareDownload.unsubscribeTxt") }}?</p>
                                            <p v-else>{{ $c("pages.SoftwareDownload.subscribeTxt") }}?</p>
                                            <div class="btn_section_box">
                                                <div class="btn_section_cancel" @click="nBtnHandle">{{ $c("pages.MyAccount.address.cancel") }}</div>
                                                <div class="btn_section_confirm" @click="yBtnHandle(item.product_id, item.is_sub)">{{ $c("pages.MyAccount.address.delete") }}</div>
                                            </div>
                                        </section>
                                    </FsPopover> -->
                                </div>
                            </li>
                            <!-- <li class="item_li">
								
                                <a :href="item.pdf_url" class="btn_left">
									<fs-button type="blackline">{{$c("pages.SoftwareDownload.download")}}</fs-button>
								</a> -->
                            <!--                                <fs-button type="blackline" ref="row" @click="clickSubscribe(index)" class="btn_right">{{ item.is_sub ? "Unsubscribe" : " Subscribe" }}</fs-button>-->
                            <!--                                <m-choose-popup @yesBtn="yBtnHandle(item.product_id,item.is_sub )" @noBtn="nBtnHandle" v-if="flag == index">-->
                            <!--                                    <p v-if="item.is_sub">-->
                            <!--                                        {{$c("pages.SoftwareDownload.unsubscribeTxt")}}??-->
                            <!--                                    </p>-->
                            <!--                                    <p v-else>-->
                            <!--                                        {{$c("pages.SoftwareDownload.subscribeTxt")}}?-->
                            <!--                                    </p>-->
                            <!--                                </m-choose-popup>-->
                            <!-- <fs-popover :icon="false" trigger="click" position="top-end" class="btn_right">
                                    <div slot="trigger" :style="{width:`100%`}">
                                        <fs-button type="blackline" :stopPropagation="false" :style="{width:`100%`}" ref="row">{{ item.is_sub ? "Unsubscribe" : " Subscribe" }}</fs-button>
                                    </div>
                                    <section class="">
                                        <p v-if="item.is_sub">{{ $c("pages.SoftwareDownload.unsubscribeTxt") }}?</p>
                                        <p v-else>{{ $c("pages.SoftwareDownload.subscribeTxt") }}?</p>
                                        <div>
                                            <fs-button :text="$c('pages.MyAccount.address.cancel')" type="blackline" @click="nBtnHandle"></fs-button>
                                            <fs-button :text="$c('pages.MyAccount.address.delete')" type="blackline" @click="yBtnHandle(item.product_id, item.is_sub)"></fs-button>
                                        </div>
                                    </section>
                                </fs-popover>
                            </li> -->
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
        <filters v-model="filterVisiable" @submit="filtersSubmit" :dataList="dataList" />
        <!-- <subscribe-tip :show="isSubscribeTip" :type="'success'" :loading="isLoading" :height="'142px'" :hideHeader="false" :isSubscribe="list.subscribe" @closeSubscribeTip="closeSubscribeTip"></subscribe-tip> -->
        <fs-popup-new class="delete_popup" transition="fade" :show="delete_popup_show_all" :mobileWidth="mobileWidth" @close="nBtnHandle" :title="$c('pages.ShoppingCart.Confirmation')">
            <div class="delete_popup_ctn">
                <p class="delete_info">{{ selectItem.is_sub ? $c("pages.SoftwareDownload.unsubscribeTxt") : $c("pages.SoftwareDownload.subscribeTxt") }}?</p>
                <div class="delete_btn_box">
                    <fs-button :loading="delete_btn_loading_all" @click="yBtnHandle" :text="$c('pages.SoftwareDownload.yes')"></fs-button>
                    <fs-button type="grayline" @click="nBtnHandle" :text="$c('pages.SoftwareDownload.no')"></fs-button>
                </div>
            </div>
        </fs-popup-new>
    </div>
</template>
<script>
import FsSelect from "@/components/FsSelect/FsSelect"
import Filters from "@/components/MFilters/MFilters"
import MChoosePopup from "@/popup/ChoosePopup/MChoosePopup"
import FsButton from "@/components/FsButton/FsButton"
import FsPopover from "@/components/FsPopover"
import FsPopupNew from "@/components/FsPopupNew/FsPopupNew.vue"
import FsInlineTips from "@/components/FsInlineTips/FsInlineTips.vue"
import AccountMFilterBtn from "@/components/AccountMFilterBtn/AccountMFilterBtn"
import AccountEmpty from "@/components/AccountEmpty/AccountEmpty"
import FsSelectSearch from "@/components/FsSelectSearch"
const DEFAULT_FILTER_VALUE = {
    oto: "1",
    time_type: "0",
    status: "0",
    purchased_by: [], //组织用户
    teamIds: [],
}
export default {
    components: {
        FsSelect,
        MChoosePopup,
        FsButton,
        FsPopover,
        Filters,
        FsPopupNew,
        FsInlineTips,
        AccountMFilterBtn,
        AccountEmpty,
        FsSelectSearch,
    },
    props: {
        list: {
            type: Array,
            default: [],
        },
        params: {
            type: Object,
            default: () => {},
        },
        selectList: {
            type: Array,
            default: () => [
                { value: "all", name: "Network Switches" },
            ],
        },
    },
    data() {
        return {
            searchInp: "",
            flag: -1,
            mobileWidth: "calc(100% - 64px)",
            isShowSearchHistory: false,
            filterVisiable: false,
            // 显示是否订阅提示
            isSubscribeTip: false,
            isLoading: true,
            dataList: [],
            delete_popup_show_all: false,
            delete_btn_loading_all: false,
            delete_popup_delete_index: 0,
            selectItem: {},
        }
    },
    computed: {
        isMFilter() {
            return this.params.category_id != "all"
        },
    },
    methods: {
        onSubscribe(item) {
            this.selectItem = item
            this.delete_popup_show_all = true
        },
        clearSearch() {
            this.searchInp = ""
            this.searchHandle()
        },
        handleOpenFilter() {
            this.dataList = [
                {
                    title: "Software Type",
                    list: this.selectList,
                    defaultValue: this.params.category_id || "all",
                    currentValue: this.params.category_id || "all",
                    returnKey: "category_id",
                },
            ]
            this.filterVisiable = true
        },

        clickSubscribe(index) {
            if (index == this.flag) {
                this.flag = -1
            } else {
                this.flag = index
            }
        },
        closeSubscribeTip(val) {
            this.isSubscribeTip = val
        },
        nBtnHandle() {
            // this.flag = -1;
            // this.isSubscribeTip = false;
            this.delete_popup_show_all = false
            document.body.click()
        },
        yBtnHandle() {
            console.log(this.selectItem)
            this.delete_btn_loading_all = true
            var type = this.selectItem.is_sub ? 1 : 2
            this.$emit("yBtnHandle", { id: this.selectItem.product_id, type: type })
            this.delete_btn_loading_all = false
            this.delete_popup_show_all = false
            //发送请求更改订阅，弹出修改订阅成功提示框，5秒后自动关闭
            // this.resultList.subscribe = !this.resultList.subscribe;
            // this.flag = -1;
            // this.isSubscribeTip = true;
        },
        filtersSubmit(e) {
            // console.log(e);
            this.$emit("mFilter", e.category_id)
            this.filterVisiable = false
        },
        //搜索操作
        searchHandle() {
            console.log(this.searchInp)
            this.$emit("search", this.searchInp)
        },
    },
}
</script>
<style lang="scss" scoped>
.m_soft {
    background-color: #fff;
    display: none;
}
.software_header_title {
    padding-bottom: 8px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    h2 {
        @include font20;
        font-weight: 600;
        color: $textColor1;
    }
}
.software_header_tip {
    margin-bottom: 20px;
}

.filter_box {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin: 20px 0;
    .form {
        display: block;
        width: 29.2%;
        min-width: 260px;
        position: relative;
        .inp {
            padding-right: 80px;
        }
        .iconfont {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            width: 40px;
            height: 40px;
            padding: 12px 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            color: $textColor3;
            &:hover {
                color: $textColor1;
            }
        }
        .iconfont-search {
            font-size: 16px;
            right: 0;
        }
        .iconfont-clear {
            font-size: 14px;
            right: 40px;
        }
        .iconfont:hover {
            color: $textColor1;
        }
    }
}
.result {
    .result_item {
        background-color: $bgColor3;
        // padding: 16px;
        margin-bottom: 12px;
        line-height: 22px;
        @include font14;
        box-shadow: 0 2px 8px 0 rgb(0 0 0 / 10%);
        border-radius: 12px;
        .item_li {
            margin: 0 16px;
            display: flex;
            // padding-bottom: 16px;
            padding: 16px 0;
            border-bottom: 1px solid #e5e5e5;
            &:first-child {
                background-color: #f7f7f7;
                border-radius: 12px 12px 0 0;
                margin: 0;
                padding: 16px;
                border-bottom: none;
            }
            &:last-child {
                border: none;
            }
            .left {
                // width: 50%;
                width: max-content;
                // flex: 1;
                margin-right: 16px;
                font-weight: 600;
                color: $textColor1;
            }
            .right {
                // width: 50%;
                flex: 1;
                text-align: right;
                .iconfont {
                    margin-right: 4px;
                }
                p {
                    // display: -webkit-box;
                    // overflow: hidden;
                    // -webkit-line-clamp: 2;
                    // -webkit-box-orient: vertical;
                    // text-overflow: ellipsis;
                    // white-space: nowrap;
                    // overflow: hidden;
                    // text-overflow: ellipsis;
                }
                .note {
                    font-size: 14px;
                    font-weight: 400;
                    color: $textColor6;
                    line-height: 22px;
                    padding-top: 5px;
                }
                .download {
                    color: $textColor1;
                }
            }
        }
        .products_id {
            color: $textColor1;
            cursor: pointer;
            text-decoration: none;
        }
        .subscribe {
            color: $textColor6;
        }
    }
}

@include mediaM {
    .m_soft {
        display: block;
    }
    .filter_box {
        ::v-deep .account-m-filter-btn {
            width: 40px;
            height: 40px;
            padding: 10px;
        }
        .form {
            min-width: 0;
            width: auto;
            flex: 1;
        }
    }
}

.btn_section {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    p {
        color: #707070;
        @include font14;
        padding: 20px 0;
    }
    .btn_section_box {
        width: 100%;
        display: flex;
        justify-content: space-between;
    }
    .btn_section_cancel,
    .btn_section_confirm {
        flex: 1;
        padding: 13px 0;
        color: #19191a;
        text-align: center;
        border-top: 1px solid #e5e5e5;
    }
    .btn_section_confirm {
        border-left: 1px solid #e5e5e5;
    }
}
.delete_popup {
    ::v-deep .fs-popup-header .iconfont_close {
        display: flex;
        justify-content: center;
        align-items: center;
        /* right: 20px; */
        // width: 32px;
        // height: 32px;
        &:hover {
            background: rgba($color: #19191a, $alpha: 0.04);
        }
    }
    &::v-deep {
        .mask {
            position: fixed;
        }
        .fs-popup-ctn {
            @media (max-width: 960px) {
                height: auto;
                width: calc(100% - 64px);
                border-radius: 3px;
            }
        }
    }

    .delete_popup_ctn,
    .delete_popup_ctn_m {
        width: 480px;
        padding: 20px 32px;
        @media (max-width: 960px) {
            width: 100%;
            padding: 20px;
        }
        .delete_info {
            @include font14;
            color: $textColor1;
            margin-bottom: 40px;
        }
        .delete_btn_box {
            display: flex;
            justify-content: flex-end;
            .fs-button {
                margin-left: 12px;
                width: 120px;
            }
        }
    }
    ::v-deep .delete_popup_ctn_m {
        padding: 0;
        p {
            display: flex;
            align-items: center;
            justify-content: center;
        }
        > p:first-child {
            margin: 32px 0 8px;
            color: $textColor1;
        }
        .delete_info_m {
            padding: 0 36px;
            margin-bottom: 24px;
            color: $textColor3;
        }
        .delete_btn_box_m {
            display: flex;
            align-items: center;
            justify-content: space-between;
            border-top: 1px solid #e5e5e5;
            span {
                flex: 1;
                padding: 13px 0;
                text-align: center;
                color: $textColor1;
                &:first-child {
                    border-right: 1px solid #e5e5e5;
                }
            }
        }
    }
}
</style>
