<template>
    <div class="software_download" v-if="SoftwareData">
        <AccountLayout :breads="breadCrumbList">
            <div class="software_body" :style="SoftwareData.length == 0 ? '' : 'padding-bottom: 78px;'" v-loading="loading">
                <div class="software_header">
                    <div class="software_header_title">
                        <h2>{{ $c("pages.MyAccount.menu.softwareDownload") }}</h2>
                    </div>
                    <FsInlineTips class="software_header_tip">
                        <div v-html="$c('pages.SoftwareDownload.headerTip').replace('xxxx', $localeLink('/technical_documents.html'))"></div>
                    </FsInlineTips>
                </div>
                <div class="software_top">
                    <fs-select isNewStyle :options="selectList" class="form-item" @change="selectHandle($event)"></fs-select>
                    <FsSelectSearch
                        class="form-item search-item"
                        @search="SearchBtnClick(searchInp)"
                        :placeholder="$c('pages.SoftwareDownload.itemID')"
                        @focus="searchInput(searchInp)"
                        v-model="searchInp"
                        @clear="clearSearch" />
                    <!-- <AccountSearchInput
                        width="280px"
                        class="form-item search-item"
                        @search="SearchBtnClick(searchInp)"
                        :placeholder="$c('pages.SoftwareDownload.itemID')"
                        @focus="searchInput(searchInp)"
                        v-model="searchInp" /> -->
                </div>
                <div class="result" v-if="SoftwareData">
                    <div class="result_content" v-if="SoftwareData.length > 0">
                        <table class="content_table" v-if="SoftwareData.length > 0">
                            <thead>
                                <tr>
                                    <th>{{ $c("pages.SoftwareDownload.latestFileInformation") }}</th>
                                    <th>{{ $c("pages.SoftwareDownload.productID") }}</th>
                                    <th>{{ $c("pages.SoftwareDownload.releaseDate") }}</th>
                                    <th>{{ $c("pages.SoftwareDownload.size") }}</th>
                                    <th>{{ $c("pages.SoftwareDownload.software") }}</th>
                                    <th>{{ $c("pages.SoftwareDownload.softwareNotification") }}</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr v-for="(item, index) in SoftwareData" :key="index">
                                    <td>
                                        <p>
                                            {{ item.file_name }} <br /><a :href="item.pdf_url" class="note" target="_blank">{{ $c("pages.SoftwareDownload.releaseNote") }}</a>
                                        </p>
                                    </td>
                                    <td>
                                        <a :href="localePath({ path: `/products/${item.product_id}.html` })" class="product_id">#{{ item.product_id }}</a>
                                    </td>
                                    <td>{{ item.file_add_time_format }}</td>
                                    <td>{{ item.file_size }}</td>
                                    <td>
                                        <a :href="item.file_url" target="_blank" :download="item.file_title">
                                            <fs-button type="download-icon-text">
                                                <span class="iconfont" slot="icon">&#xe653;</span>
                                                {{ $c("pages.SoftwareDownload.download") }}
                                            </fs-button>
                                        </a>
                                    </td>
                                    <td>
                                        <span @click="onSubscribe(item)" class="trigger_info">{{ item.is_sub ? $c("pages.SoftwareDownload.unsubscribe") : $c("pages.SoftwareDownload.subscribe") }}</span>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div class="pagination" v-if="pageConfig.total > 0">
                        <FsPagination :pageConfig="pageConfig" @changeCurrentPage="changeCurrentPage" />
                    </div>
                </div>
                <AccountEmpty v-if="SoftwareData.length == 0 && !loading" width="350px" isShadow :title="$c('pages.SoftwareDownload.noFund')" :sub-title="$c('pages.SoftwareDownload.emptyTxt')">
                    <span class="empty-link" @click.stop="gaStartShopping">{{ $c("pages.SoftwareDownload.startShopping") }}</span>
                </AccountEmpty>
            </div>
            <m-software :list="SoftwareData" @yBtnHandle="yBtnHandle" @mFilter="selectHandle" :params="params" @search="SearchBtnClick($event)" :selectList="selectList"></m-software>
        </AccountLayout>
        <fs-popup-new :show="isSuccess" @close="closeSuccessPopup" class="successPop">
            <div class="title" slot="header">
                <span class="iconfont iconfont_success">&#xf060;</span>
                <div>
                    <div>
                        <p v-if="type === 1">{{ $c("pages.SoftwareDownload.subscribedSuccess") }}</p>
                        <p v-else>{{ $c("pages.SoftwareDownload.unsubscribedSuccess") }}</p>
                    </div>
                    <div>
                        <p v-if="type === 1">{{ $c("pages.SoftwareDownload.subscribedSuccessTxt") }}</p>
                        <p v-else>{{ $c("pages.SoftwareDownload.unsubscribedSuccessTxt") }}</p>
                    </div>
                </div>
            </div>
        </fs-popup-new>
        <fs-popup-new class="delete_popup" transition="fade" type="float" isNew width="480" mobileWidth="calc(100% - 64px)" :show="delete_popup_show_all" @close="cancel" :title="$c('pages.ShoppingCart.Confirmation')">
            <div class="delete_popup_ctn">
                <p class="delete_info">{{ selectItem.is_sub ? $c("pages.SoftwareDownload.unsubscribeTxt") : $c("pages.SoftwareDownload.subscribeTxt") }}?</p>
            </div>
            <template slot="footer">
                <div class="delete_btn_box">
                    <fs-button type="white" :text="$c('pages.SoftwareDownload.no')" @click="cancel"></fs-button>
                    <fs-button :text="$c('pages.SoftwareDownload.yes')" :loading="delete_btn_loading_all" @click="yBtnHandle({ id: selectItem.product_id, type: selectItem.is_sub ? 1 : 2 })"></fs-button>
                </div>
            </template>
        </fs-popup-new>
    </div>
</template>

<script>
import FsSelect from "@/components/FsSelect/FsSelect"
import MSoftware from "./components/MSoftware"
import FsPopover from "@/components/FsPopover"
import FsPagination from "@/components/FsPagination/FsPagination"
import FsSuccessPopup from "@/components/FsSuccessPopup/FsSuccessPopup.vue"
import FsButton from "@/components/FsButton/FsButton.vue"
import FsPopupNew from "@/components/FsPopupNew/FsPopupNew.vue"
import AccountLayout from "@/components/AccountLayout/AccountLayout.vue"
import FsInlineTips from "@/components/FsInlineTips/FsInlineTips.vue"
import AccountEmpty from "@/components/AccountEmpty/AccountEmpty"
import FsSelectSearch from "@/components/FsSelectSearch"
import { mapState } from "vuex"
export default {
    head() {
        return {
            meta: [...this.robotsMeta],
        }
    },
    components: {
        FsSelect,
        MSoftware,
        FsPopover,
        FsSuccessPopup,
        FsButton,
        FsPagination,
        FsPopupNew,
        AccountLayout,
        FsInlineTips,
        AccountEmpty,
        FsSelectSearch,
    },
    data() {
        return {
            loading: true,
            searchInp: "",
            flag: -1,
            isShowSearchHistory: false,
            // 显示是否订阅提示
            isSuccess: false,
            isLoading: true,
            SoftwareData: [],
            pageConfig: {
                pageSize: 15,
                pageNo: 1,
                total: 1,
            },
            breadCrumbList: [
                { name: this.$c("pages.SoftwareDownload.myAccount"), url: "/my-account" },
                { name: this.$c("pages.MyAccount.menu.softwareDownload"), url: "" },
            ],
            product_id: "",
            // 订阅
            type: "",
            params: {
                category_id: "all",
                page: 1,
                search: "",
            },
            //远程接口获取选项
            selectList: [
                { value: "all", name: this.$c("pages.SoftwareDownload.networkSwitches") },
            ],
            // 默认选项作为降级方案
            defaultSelectList: [
                { value: "all", name: this.$c("pages.SoftwareDownload.networkSwitches") },
                { value: "3256", name: this.$c("pages.SoftwareDownload.ten") },
                { value: "1071", name: this.$c("pages.SoftwareDownload.twentyFive") },
                { value: "3257", name: this.$c("pages.SoftwareDownload.forty") },
                { value: "3258", name: this.$c("pages.SoftwareDownload.oneHundred") },
                { value: "3255", name: this.$c("pages.SoftwareDownload.fourHundred") },
            ],
            delete_popup_show_all: false,
            delete_btn_loading_all: false,
            delete_popup_delete_index: 0,
            selectItem: {},
        }
    },
    computed: {
        ...mapState({
            isMobile: (state) => state.device.isMobile,
            robotsMeta: (state) => state.meta.robotsMeta,
        }),
    },
    created() {
        if (this.$route.query.search) {
            this.searchInp = Number(this.$route.query.search)
            this.params.search = Number(this.$route.query.search)
            // this.SearchBtnClick(Number(this.$route.query.search))
        }
        if (process.client) {
            this.init()
        }
    },
    mounted() {
        this.$nextTick(() => {
            let downloadCenter = document.querySelector(".software_download .download_center")
            downloadCenter.onclick = function () {
                console.log("download_center")
                if (window.dataLayer) {
                    window.dataLayer.push({
                        event: "uaEvent",
                        eventCategory: "Personal Hub_Software Download",
                        eventAction: "download_center",
                        eventLabel: "undefined",
                        nonInteraction: false,
                    })
                }
            }
        })
    },
    methods: {
        onSubscribe(item) {
            console.log(item)
            this.selectItem = item
            this.delete_popup_show_all = true
        },
        init() {
            this.loading = true
            // 并行请求软件数据和分类数据
            Promise.all([
                this.$axios.get("/api/software_download", { params: this.params }),
                this.$axios.get('/api/cateSwitchList')
            ]).then(([softwareRes, categoryRes]) => {
                // 处理软件数据
                this.SoftwareData = softwareRes.data.data
                this.pageConfig.total = softwareRes.data.data.length

                // 处理分类数据
                this.processSelectListData(categoryRes)

                this.loading = false
            }).catch((err) => {
                this.loading = false
                this.$message.error(err.message)
                // 使用默认数据作为降级方案
                this.selectList = [...this.defaultSelectList]
            })
        },
        // 处理选择列表数据
        processSelectListData(categoryRes) {
            try {
                if (categoryRes?.code === 200 && categoryRes.data && Array.isArray(categoryRes.data)) {
                    // 转换接口数据为组件需要的格式
                    const remoteOptions = categoryRes.data.map(item => ({
                        value: item.id || item.value,
                        name: item.name || item.label
                    }))

                    // 确保 "all" 选项始终在第一位
                    this.selectList = [
                        { value: "all", name: this.$c("pages.SoftwareDownload.networkSwitches") },
                        ...remoteOptions
                    ]
                } else {
                    // 数据格式不正确，使用默认数据
                    this.selectList = [...this.defaultSelectList]
                }
            } catch (error) {
                console.error('处理选择列表数据时出错:', error)
                // 出错时使用默认数据
                this.selectList = [...this.defaultSelectList]
            }
        },
        closeSuccessPopup() {
            this.isSuccess = false
            this.init()
        },
        cancel() {
            this.delete_popup_show_all = false
            document.body.click()
        },
        yBtnHandle(v) {
            this.loading = true
            this.delete_btn_loading_all = true
            this.cancel()
            console.log(v)
            if (v.type == 1) {
                //发送请求更改订阅，弹出修改订阅成功提示框，5秒后自动关闭
                this.$axios
                    .post("/api/software_subscribe", { product_id: v.id, type: v.type })
                    .then((res) => {
                        // console.log("s",res);
                        if (res.status == "success") {
                            this.isSuccess = true
                            setTimeout(() => {
                                this.closeSuccessPopup()
                            }, 5000)
                        }
                        this.type = 2
                    })
                    .catch((err) => {
                        // console.log("e",res);
                        this.$message.error(err.message)
                    })
            } else {
                this.$axios
                    .post("/api/software_subscribe", { product_id: v.id, type: v.type })
                    .then((res) => {
                        // console.log("s",res);
                        if (res.status == "success") {
                            this.isSuccess = true
                            setTimeout(() => {
                                this.closeSuccessPopup()
                            }, 5000)
                        }
                        this.type = 1
                    })
                    .catch((err) => {
                        // console.log("e",res);
                        this.$message.error(err.message)
                    })
            }
            this.delete_btn_loading_all = false
            this.loading = false
        },
        changeCurrentPage(e) {
            ;(this.params.page = e), this.init()
        },
        searchInput() {
            console.log("searchInput")
            window.dataLayer &&
                window.dataLayer.push({
                    event: "uaEvent",
                    eventCategory: `${this.$store.state.ga.pageGroup}`,
                    eventAction: "search_input",
                    eventLabel: "Software Download_Search Input",
                    nonInteraction: false,
                })
        },
        clearSearch() {
            this.params.search = ""
            this.init()
        },
        //搜索操作
        SearchBtnClick(e) {
            console.log(e, "SearchBtnClick")
            // 发送搜索请求，
            this.params.search = e
            window.dataLayer &&
                window.dataLayer.push({
                    event: "uaEvent",
                    eventCategory: `${this.$store.state.ga.pageGroup}`,
                    eventAction: "search",
                    eventLabel: `Normal_Software Download_${this.params.search}`,
                    nonInteraction: false,
                })
            if (this.params.search.length) {
                this.init()
            }
        },

        selectHandle(e) {
            this.params.category_id = e

            // 动态获取选中项的显示名称
            const selectedOption = this.selectList.find(option => option.value === e)
            const type = selectedOption ? selectedOption.name : this.$c("pages.SoftwareDownload.networkSwitches")

            console.log(type)
            if (window.dataLayer) {
                window.dataLayer.push({
                    event: "uaEvent",
                    eventCategory: "Personal Hub_Software Download",
                    eventAction: "software_filter",
                    eventLabel: `Switches_Drop-Down_${type}`,
                    nonInteraction: false,
                })
            }
            this.init()
        },
        gaCustomerService() {
            console.log("Contact Customer Service")
            if (window.dataLayer) {
                window.dataLayer.push({
                    event: "uaEvent",
                    eventCategory: "Personal Hub_Software Download",
                    eventAction: "contact_customer_service",
                    eventLabel: "Contact Customer Service",
                    nonInteraction: false,
                })
            }
        },
        gaStartShopping() {
            console.log("start_shopping")
            if (window.dataLayer) {
                window.dataLayer.push({
                    event: "uaEvent",
                    eventCategory: "Personal Hub_Software Download",
                    eventAction: "start_shopping",
                    eventLabel: "Software Download_Start Shopping",
                    nonInteraction: false,
                })
            }
            this.$router.push(this.localePath({ path: "/c/enterprise-switches-3079" }))
        },
    },
}
</script>

<style lang="scss" scoped>
.software_download {
    background-color: $bgColor3;

    a {
        font-size: 14px;
        color: $textColor1;
        text-decoration: 0;
    }
}
.successPop {
    ::v-deep .fs-popup {
        .iconfont_close {
            /* top: 24px;
            transform: translateY(0); */
        }
        .title {
            display: flex;
            padding-right: 36px;
            > div {
                div:first-child {
                    margin-bottom: 12px;
                    color: $textColor1;
                }
                div:last-child {
                    @include font14();
                    color: $textColor3;
                }
            }
            .iconfont_success {
                color: #339933;
                margin-right: 8px;
                margin-top: 2px;
                width: 20px;
                height: 20px;
            }
        }
        @media (max-width: 960px) {
            .iconfont_close {
                /* top: 20px;
                right: 20px; */
            }
            height: auto;
            .fs-popup-ctn {
                height: auto;
            }
            .fs-popup-header {
                padding: 20px;
            }
            .title {
                text-align: center;
                padding-right: 0;
                flex-direction: column;
                > div {
                    text-align: center;
                    div:first-child {
                        margin: 12px 0 8px;
                    }
                }
                .iconfont_success {
                    margin: 0 auto;
                }
            }
        }
    }
}

.software_header_title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    h2 {
        @include font20();
        font-weight: 600;
        color: $textColor1;
    }
}
.software_header_tip {
    margin: 24px 0 20px;
    > .iconfont {
        margin-right: 8px;
        color: #0060bf;
    }
    p {
        padding-right: 20px;
    }
    ::v-deep a {
        font-size: 13px;
        color: $textColor6;
    }
}
.main {
    display: flex;
    .left {
        font-size: 14px;
        width: 250px;
        flex-shrink: 0;
        align-self: self-start;
        position: sticky;
        z-index: 2;
        top: 0;
    }
}

.software_body {
    position: relative;
    flex: 1;
    background-color: $bgColor3;
    display: flex;
    flex-direction: column;
    min-height: calc(50vh + 100px);
}
.software_top {
    display: flex;
    margin-bottom: 14px;
    .form-item {
        width: 220px;
        margin: 6px 12px 6px 0;
    }
    .search-item {
        width: 280px;
        margin-right: 0;
    }
}

.result {
    width: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.result_content {
    width: 100%;
}

.content_table {
    width: 100%;
    text-align: left;
    color: $textColor1;
    box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.1);
    border-radius: 12px;
    a {
        color: $textColor6;
        &:hover {
            text-decoration: underline;
        }
    }
    thead th {
        font-size: 14px;
        font-weight: 600;
        height: 40px;
        width: 120px;
        padding-right: 20px;
        background: $bgColor1;
        text-align: left;
        &:first-child {
            width: 300px;
            padding-left: 24px;
            border-radius: 12px 0 0 0;
        }
        &:nth-child(6) {
            // width: 180px;
            min-width: 173px;
            // text-align: right;
            padding-right: 24px;
            border-radius: 0 12px 0 0;
        }
    }
    tbody {
        position: relative;
        &::after {
            content: "";
            width: 24px;
            height: 100%;
            position: absolute;
            top: 0;
            right: 0;
            background-color: #fff;
            border-radius: 0 0 12px 0;
        }
        &::before {
            content: "";
            width: 24px;
            height: 100%;
            position: absolute;
            top: 0;
            left: 0;
            background-color: #fff;
            border-radius: 0 0 0 12px;
        }
        tr {
            td {
                max-height: 100px;
                font-size: 14px;
                font-weight: normal;
                border-bottom: 1px solid $borderColor2;
                padding-right: 20px;
                &:first-child {
                    padding-left: 24px;
                    font-size: 14px;
                    p {
                        padding: 28px 0;
                        line-height: 22px;
                        white-space: normal;
                    }
                    a {
                        padding-top: 5px;
                        font-weight: 400;
                    }
                }
                &:nth-child(6) {
                    padding-right: 24px;
                    &::v-deep {
                        .trigger_info {
                            color: #0060bf;
                            cursor: pointer;
                            &:hover {
                                text-decoration: underline;
                            }
                        }
                    }
                }
            }
            &:last-child {
                > td {
                    border: none;
                }
            }
            .subscribe {
                color: $textColor6;
            }
            .product_id {
                color: $textColor1;
                cursor: pointer;
                a {
                    text-decoration: $textColor1;
                }
            }
        }
    }
}

::v-deep .fs-popover {
    margin-left: 0;
}
.pagination {
    position: absolute;
    bottom: 24px;
}
@include mediaM {
    .software_top {
        .form-item:not(.search-item) {
            display: none;
        }

        .search-item {
            flex: 1;
        }
    }
    .software_body {
        display: none;
    }
    .software_wrap {
        .software_header {
            padding: 16px;
            padding-bottom: 0;
        }
    }
}

.delete_info {
    @include font14;
    color: $textColor1;
    // margin-bottom: 16px;
}
.delete_btn_box {
    display: flex;
    justify-content: flex-end;
    .fs-button {
        margin-left: 16px;
        @include mobile {
            margin-left: 12px;
        }
    }
}
</style>
