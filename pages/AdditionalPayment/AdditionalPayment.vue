<template>
    <div class="payment">
        <header class="header">
            <div class="main">
                <nuxt-link class="logo" :to="localePath({ name: 'home' })"></nuxt-link>
                <span>{{ $c("pages.additionalPayment.title") }}</span>
            </div>
        </header>
        <section class="body">
            <div class="main">
                <div class="basic">
                    <div class="left">
                        <p class="order">
                            <span>{{ $c("pages.additionalPayment.order_name") }} </span>{{ information.number }}
                        </p>
                        <p class="for">
                            <span>{{ $c("pages.additionalPayment.create_for") }} </span>{{ information.customerEmail }}
                        </p>
                    </div>
                    <div class="right">
                        <p class="date">
                            <span>{{ $c("pages.additionalPayment.date") }} </span>{{ information.date }}
                        </p>
                        <p class="by">
                            <span>{{ $c("pages.additionalPayment.created_by") }} </span>{{ information.name }}
                        </p>
                    </div>
                </div>

                <div class="tips" v-if="!getUserInfo">
                    <i class="icon iconfont">&#xe66a;</i>
                    <span>{{ $c("pages.additionalPayment.tips01") }}</span>
                </div>

                <div class="tips" v-if="!validateEmail">
                    <i class="icon iconfont">&#xe66a;</i>
                    <span>{{ $c("pages.additionalPayment.tips02") }}</span>
                </div>

                <div class="table">
                    <div>
                        <div class="con">
                            <h3 class="tit">{{ $c("pages.additionalPayment.payment_for_order") }}</h3>
                            <p class="txt">{{ information.forOrder }}</p>
                        </div>
                        <div class="con">
                            <h3 class="tit">{{ $c("pages.additionalPayment.payment_term") }}</h3>
                            <p class="txt">{{ information.term }}</p>
                        </div>
                    </div>
                    <div>
                        <div class="con">
                            <h3 class="tit">{{ $c("pages.additionalPayment.currency") }}</h3>
                            <p class="txt">{{ information.currency }}</p>
                        </div>
                        <div class="con">
                            <h3 class="tit">{{ $c("pages.additionalPayment.amount") }}</h3>
                            <p class="txt" v-html="information.amount"></p>
                        </div>
                    </div>
                </div>

                <div class="remark">
                    <h2 class="tit">{{ $c("pages.additionalPayment.remark") }}</h2>
                    <p class="txt">{{ information.remark }}</p>
                </div>

                <div class="total">
                    <h2>{{ $c("pages.additionalPayment.total_amount") }} <span v-html="information.total"></span></h2>
                </div>

                <div class="proceed">
                    <fs-button @click="proceed" v-if="validateEmail">
                        <i class="icon iconfont">&#xf104;</i>
                        <span>{{ $c("pages.additionalPayment.button") }}</span>
                    </fs-button>
                </div>

                <div class="question">
                    <div class="left">
                        <span>{{ $c("pages.additionalPayment.question") }} </span>
                        <a href="javascript:;" @click="fsLiveChat">{{ $c("pages.additionalPayment.live_chat") }}</a>
                    </div>
                    <div class="right">
                        <div class="types"></div>
                        <div class="types"></div>
                        <div class="types"></div>
                        <div class="types"></div>
                        <div class="types"></div>
                        <div class="types"></div>
                    </div>
                </div>
            </div>
        </section>

        <footer class="footer">
            <div class="left">
                <span>{{ $c("pages.additionalPayment.fs") }}</span>
                <nuxt-link :to="localePath({ name: 'terms-of-use' })">{{ $c("pages.additionalPayment.terms") }}</nuxt-link> |
                <nuxt-link :to="localePath({ name: 'privacy-policy' })">{{ $c("pages.additionalPayment.policy") }}</nuxt-link>
            </div>
            <div class="right">
                <img style="width: 65px; margin-right: 10px" src="https://img-en.fs.com/includes/templates/fiberstore/images/new-pc-img/norton-poweredby.png" />
                <a
                    href="javascript:;"
                    onclick="javascript:window.open('https://www.mcafeesecure.com/verify?host=www.fs.com', 'SealVerification', 'width=568,height=546,left=0,top=0,toolbar=no,location=yes,scrollbars=yes,status=yes,resizable=yes,fullscreen=no');">
                    <img src="https://img-en.fs.com/includes/templates/fiberstore/images/new-pc-img/McAfee.svg" />
                </a>
            </div>
        </footer>

        <login-regist @callback="proceed"></login-regist>
        <fs-message :show="showErrorTip" @change="closeError">
            <p>{{ errmsg }}</p>
        </fs-message>
    </div>
</template>

<script>
import FsButton from "@/components/FsButton/FsButton"
import FsMessage from "@/components/FsMessage/FsMessage"
import { mapState, mapMutations } from "vuex"
import { liveChat } from "@/util/util.js"
import LoginRegist from "@/popup/LoginRegist/LoginRegist.vue"
export default {
    layout: "empty",
    components: {
        FsButton,
        FsMessage,
        LoginRegist,
    },
    data() {
        return {
            information: {
                number: "",
                customerEmail: "",
                name: "",
                date: "",
                term: "",
                currency: "",
                amount: "",
                total: "",
                remark: "",
                saleStatus: "",
                forOrder: "",
                customerID: "",
            },
            validateEmail: true,
            showErrorTip: false,
            errmsg: "",
        }
    },
    inject: ["fsLiveChat"],
    methods: {
        proceed() {
            // this.$axios.post('/api/user/info', {}).then(res => {
            //     console.log(res);
            //     if (res.data.isLogin == 0) {
            //         this.showLogin()
            //     } else {

            //     }
            // }).catch(error => {

            // })
            if (this.getLogin < 1) {
                console.log(123)
                this.$router.push(this.localePath({ name: "login", query: { redirect: this.$route.fullPath } }))
                // this.show()
            } else {
                if (this.information.customerID !== this.getUserInfo.customers_id) {
                    alert(this.$c("pages.additionalPayment.tips02"))
                } else {
                    this.$axios
                        .get("/api/pay/pay/supplement", {
                            params: { orders_id: this.$route.params.id },
                        })
                        .then((res) => {
                            console.log(res)
                            if (res.data.code === 1) {
                                window.location.href = location.origin + "/" + res.data.url
                            } else {
                                window.location.href = res.data.url
                            }
                        })
                        .catch((err) => {
                            alert(err.message)
                        })
                }
            }
        },
        ...mapMutations({
            show: "loginRegist/show",
        }),
        closeError() {
            this.showErrorTip = false
            window.location.href = this.$localeLink("/")
        },
    },
    computed: {
        ...mapState({
            getUserInfo: (state) => state.userInfo.userInfo,
            getLogin: (state) => state.userInfo.isLogin,
            website: (state) => state.webSiteInfo.website,
        }),
    },
    asyncData({ app, route, redirect }) {
        let params = route.params.id
        return app.$axios
            .get(`/api/products_payment/${params}`)
            .then((res) => {
                const { data } = res
                if (data.result) {
                    return {
                        information: {
                            number: data.order_number,
                            customerEmail: data.customer_email,
                            name: data.admin_name,
                            date: data.date,
                            term: data.payment_term,
                            currency: data.currency,
                            amount: data.amount,
                            total: data.total_amount,
                            remark: data.remarks,
                            saleStatus: data.sale_status,
                            forOrder: data.payment_for_order,
                            customerID: data.customer_id,
                        },
                        showErrorTip: false,
                        errmsg: "",
                    }
                } else {
                    return {
                        information: {},
                        showErrorTip: true,
                        errmsg: res.message || app.$c("pages.additionalPayment.systemFailure"),
                    }
                }
            })
            .catch((error) => {
                return {
                    information: {},
                    showErrorTip: true,
                    errmsg: error.message || app.$c("pages.additionalPayment.systemFailure"),
                }
            })
    },
    created() {
        console.log(this.getUserInfo)
        if (this.getUserInfo) {
            if (this.information.customerID !== this.getUserInfo.customers_id) {
                this.validateEmail = false
            }
        }
    },
}
</script>

<style lang="scss" scoped>
.payment {
    background-color: $bgColor1;
    overflow-x: hidden;
    .header {
        padding: 40px 0;
        background-color: $bgColor3;
        .main {
            width: 84vw;
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            align-items: center;
            @media (max-width: 1200px) {
                width: 94vw;
            }
            .logo {
                width: 72px;
                height: 36px;
                background-repeat: no-repeat;
                background-size: 100% 100%;
                background-position: center;
                margin-right: 20px;
                background-image: url(https://img-en.fs.com/includes/templates/fiberstore/images/new-pc-img/fs-logo-pc.png);
            }
            span {
                @include font20;
                color: $textColor1;
                font-weight: 600;
            }
        }
    }
    .body {
        padding-top: 25px;
        .main {
            width: 84vw;
            max-width: 1200px;
            margin: 0 auto;
            background-color: $bgColor3;
            min-height: 840px;
            padding: 40px 48px 0;
            @media (max-width: 1200px) {
                width: 94vw;
            }
            .basic {
                display: flex;
                justify-content: space-between;
                margin-bottom: 20px;
                p {
                    @include font14;
                    color: $textColor1;
                    span {
                        font-weight: 600;
                    }
                    &:first-child {
                        margin-bottom: 4px;
                    }
                }
            }
            .tips {
                display: flex;
                align-items: center;
                padding: 10px 6px;
                border: 1px solid #b2d0ec;
                margin-bottom: 20px;
                .icon {
                    font-size: 16px;
                    color: $textColor6;
                    margin-right: 8px;
                }
                span {
                    @include font13;
                    color: $textColor3;
                }
            }
            .table {
                display: flex;
                margin-bottom: 20px;
                > div {
                    width: 100%;
                    display: flex;
                }
                .con {
                    flex: 1;
                    .tit {
                        background-color: $bgColor1;
                        font-weight: 400;
                        color: $textColor1;
                        @include font14;
                        height: 46px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        padding: 0 10px;
                    }
                    .txt {
                        @include font14;
                        color: $textColor3;
                        border-bottom: 1px solid $borderColor2;
                        align-items: center;
                        justify-content: center;
                        height: 46px;
                        display: flex;
                        padding: 0 10px;
                    }
                }
            }
            .remark {
                margin-bottom: 32px;
                .tit {
                    @include font14;
                    font-weight: 600;
                    margin-bottom: 6px;
                }
                .txt {
                    @include font14;
                    color: $textColor3;
                }
            }
            .total {
                margin-bottom: 12px;
                h2 {
                    @include font20;
                    color: $textColor1;
                    font-weight: 600;
                    text-align: right;
                }
            }
            .proceed {
                display: flex;
                justify-content: flex-end;
                margin-bottom: 30px;
                .fs-button {
                    width: auto;
                    padding: 0 20px;
                    height: 48px;
                    .icon {
                        margin-right: 10px;
                    }
                }
            }
            .question {
                display: flex;
                justify-content: space-between;
                align-items: center;
                .left {
                    @include font14;
                    span {
                        color: $textColor1;
                    }
                }
                .right {
                    display: flex;
                    align-items: center;
                    .types {
                        background: url(https://img-en.fs.com/includes/templates/fiberstore/images/icon001.jpg) no-repeat;
                        width: 46px;
                        height: 40px;
                        background-position: -3px 6px;
                        margin-left: 2px;
                        &:first-child {
                            margin-left: 0;
                        }
                        &:nth-child(2) {
                            background-position: -51px 6px;
                            width: 60px;
                        }
                        &:nth-child(3) {
                            background-position: -110px 6px;
                            width: 40px;
                        }
                        &:nth-child(4) {
                            background-position: -153px 6px;
                        }
                        &:nth-child(5) {
                            background-position: -202px 6px;
                            width: 42px;
                        }
                        &:nth-child(6) {
                            background-position: -246px 6px;
                            width: 42px;
                        }
                    }
                }
            }
        }
    }
    .footer {
        width: 94vw;
        max-width: 1200px;
        margin: 26px auto 0;
        padding-bottom: 40px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        .left {
            @include font13;
            color: $textColor1;
            a {
                color: $textColor1;
            }
        }
        .right {
            display: flex;
            align-items: center;
        }
    }
}
@media (max-width: 600px) {
    .payment {
        .body {
            .main {
                padding: 20px;
                display: flex;
                flex-direction: column;
                .basic {
                    display: flex;
                    flex-direction: column;
                    justify-content: flex-start;
                    margin-bottom: 8px;
                    p:first-child {
                        margin-bottom: 8px;
                    }
                    .right {
                        margin-top: 8px;
                    }
                }
                .tips {
                    margin-bottom: 8px;
                }
                .table {
                    display: flex;
                    flex-direction: column;
                    margin-bottom: 20px;
                    > div {
                        display: flex;
                        .con {
                            flex: initial;
                            width: 50%;
                        }
                        &:first-child {
                            .con .txt {
                                border: none;
                            }
                        }
                    }
                }
                .remark {
                    margin-bottom: 8px;
                }
                .total {
                    h2 {
                        text-align: left;
                    }
                }
                .proceed {
                    .fs-button {
                        width: 100%;
                    }
                }
                .question {
                    display: flex;
                    flex-direction: column;
                    justify-content: space-between;
                    align-items: flex-start;
                    flex: 1;
                }
            }
        }
        .footer {
            flex-direction: column;
            .left {
                margin-bottom: 4px;
            }
        }
    }
}
</style>
