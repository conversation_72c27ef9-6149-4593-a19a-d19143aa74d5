<template>
    <div class="product_attr_box" v-if="!isOffline" v-loading="loading.attributes_loading">
        <div class="attributes_wrap" v-if="hasAttributes">
            <!-- 长度 -->
            <ProductLengthAttribute
                :attributes="attributes"
                :length="length"
                :change_product_loading="change_product_loading"
                :popperStyle="popperStyle"
                :add_cart_error="add_cart_error"
                :products_id="products_id"
                @length-change="handleLengthChange" />

            <!-- 定制 -->
            <client-only>
                <ProductCustomAttributes
                    :attributes="attributes"
                    :products_id="products_id"
                    :is_show_spool="is_show_spool"
                    :change_product_loading="change_product_loading"
                    :popperStyle="popperStyle"
                    :add_cart_error_dev="add_cart_error_dev"
                    @attr-change="handleCustomAttrChange"
                    @show-change-product-loading="showChangeProductLoading"
                    @hide-change-product-loading="hideChangeProductLoading" />
            </client-only>
            <!-- 定制交换机 -->
            <ProductCustomSwitch
                v-if="is_custom_server == 2"
                :attributes="attributes"
                :products_id="products_id"
                :change_product_loading="change_product_loading"
                :popperStyle="popperStyle"
                :is_custom_server="is_custom_server"
                :o_r_id="o_r_id"
                @setCusCup="setCusCup" />
            <!-- 定制服务器 -->
            <ProductCustomServer v-else :attributes="attributes" :products_id="products_id" :change_product_loading="change_product_loading" :popperStyle="popperStyle" @setCusCup="setCusCup" />
            <div @click="getAttr" style="opacity: 0">Click</div>
        </div>
        <!-- 修改条件判断逻辑和显示内容 -->
        <div v-else-if="!loading.attributes_loading && (invalidProductId || productNotExists || tokenMissing || !hasAttributes)" class="no_attributes">
            {{ invalidProductId ? "请输入正确的产品id" : productNotExists ? errorMessage : tokenMissing ? "Token参数缺失" : "定制属性为空" }}
        </div>
    </div>
</template>

<script>
import FsButton from "@/components/FsButton/FsButton"
import { mapState } from "vuex"
import { debounce } from "@/util/util"
import { isNumber } from "@/util/types"
import ProductLengthAttribute from "./components/ProductLengthAttribute/ProductLengthAttribute"
import ProductCustomAttributes from "./components/ProductCustomAttributes/ProductCustomAttributes"
import ProductCustomSwitch from "./components/ProductCustomSwitch/ProductCustomSwitch"
import ProductCustomServer from "./components/ProductCustomServer/ProductCustomServer"

function handleAttr(a, type, id) {
    let arr = JSON.parse(JSON.stringify(a))
    if (arr && arr.length) {
        for (let i = 0; i < arr.length; i++) {
            if (id && id === 318) {
                arr[i].private_type = "customLabel"
            }
            arr[i].current_attr = []
            if (arr[i].options_type === 0) {
                if (arr[i].related_options && arr[i].related_options.options_values_id) {
                    if (arr[i].related_options.options_type === 1) {
                        arr[i].current_text = ""
                    }
                }
                if (arr[i].options_values && arr[i].options_values.length) {
                    for (let j = 0; j < arr[i].options_values.length; j++) {
                        if (arr[i].options_values[j].is_default) {
                            arr[i].current_value = arr[i].options_values[j].options_values_id
                            let o = {
                                products_options_value_id: arr[i].options_values[j].options_values_id,
                            }
                            if (type === 2) {
                                o.column_id = arr[i].options_values[j].column_id
                            }
                            arr[i].current_attr.push(o)
                            break
                        }
                    }
                }
            } else if (arr[i].options_type === 1) {
                arr[i].current_value = ""
            } else if (arr[i].options_type === 3) {
                arr[i].current_value = []
                for (let j = 0; j < arr[i].options_values.length; j++) {
                    if (arr[i].options_values[j].is_default) {
                        arr[i].current_value.push(arr[i].options_values[j].options_values_id)
                        let o = {
                            products_options_value_id: arr[i].options_values[j].options_values_id,
                        }
                        if (type === 2) {
                            o.column_id = arr[i].options_values[j].column_id
                        }
                        arr[i].current_attr.push(o)
                    }
                    if (arr[i].options_values[j].is_none) {
                        arr[i].none_value = arr[i].options_values[j].options_values_id
                    }
                }
            } else if (arr[i].options_type === 4) {
                arr[i].current_value = ""
            }
        }
    }
    return arr
}

export default {
    layout: "empty",
    name: "ProductAttributes",
    components: {
        FsButton,
        ProductLengthAttribute,
        ProductCustomAttributes,
        ProductCustomSwitch,
        ProductCustomServer,
    },
    data() {
        return {
            isOffline: false,
            popperStyle: {
                padding: "12px 16px",
            },
            is_show_spool: 0,
            loading: {
                add_cart_loading: false,
                attributes_loading: true,
            },
            products_id: this.$route.params.id || "",
            invalidProductId: false, // 新增：标识产品ID是否无效
            productNotExists: false, // 新增：标识产品ID不存在
            tokenMissing: false, // 新增：标识Token缺失
            errorMessage: "", // 新增：存储错误信息
            parentData: null, // 新增：存储父组件传入的data
            label_products: {
                label_products_id: "",
                products_name: "",
                products_img: "",
            },
            qty: 1,
            add_cart_error: "",
            add_cart_error_dev: "",
            has_file: false,
            attributes: null,
            related_test_tool_products: [],
            change_product_loading: false,
            is_custom_server: false,
            isCustomAttribute: {},
            custom_server_attribute_title: "",
            custom_server_attr_limit: {},
            customize_server: [],
            is_customized: 0,
            productDevAttr: [],
            productDevList: [],
            server_attributes: {},
            o_r_id: [],
            length: {
                max: 0,
                min: 0,
                cid: "",
                has_length: false,
                custom_length: "",
                length_id: "",
                length_id_name: "",
                length_error: "",
                length_units: ["m", "ft"],
                length_unit: "m",
                length_select_show: false,
                custom_length_focus: false,
                length_mouse: "",
            },
        }
    },
    computed: {
        // 判断是否显示属性
        hasAttributes() {
            if (!this.attributes) return false

            return [this.attributes.length?.title_name, this.attributes.custom_list?.length, this.attributes.server?.attributes?.length, this.attributes.customized_server?.options?.length].some(Boolean)
        },
        // 新增：根据环境获取兼容的 options_id
        compatibleOptionsIds() {
            // 统一使用新的 options_id，测试和生产环境都使用471/472
            return {
                option471: 471, // 统一使用471
                option472: 472, // 统一使用472
            }
        },
        ...mapState({
            isMobile: (state) => state.device.isMobile,
            screenWidth: (state) => state.device.screenWidth,
            iso_code: (state) => state.webSiteInfo.iso_code,
            website: (state) => state.webSiteInfo.website,
            currency: (state) => state.webSiteInfo.currency,
            language_id: (state) => state.webSiteInfo.language_id,
            description: (state) => state.meta.description,
            language: (state) => state.webSiteInfo.language,
        }),
        serverCusList() {
            if (this.attributes && this.attributes.customized_server && this.attributes.customized_server.options && this.attributes.customized_server.options.length) {
                let arr = []
                this.attributes.customized_server.options.forEach((item) => {
                    item.option_values.forEach((t) => {
                        if (t.is_default == 1) {
                            arr.push({ option_id: t.option_id, value_id: t.value_id, value_type: t.value_type })
                        }
                    })
                })
                return arr
            } else {
                return []
            }
        },
    },
    async mounted() {
        await this.loadProductAttributes()
        // 监听来自父组件的消息
        window.addEventListener("message", this.handleMessage)
    },
    methods: {
        // 新增：检查是否为 option471（兼容测试和生产环境）
        isOption471(optionsId) {
            return optionsId === this.compatibleOptionsIds.option471
        },

        // 新增：检查是否为 option472（兼容测试和生产环境）
        isOption472(optionsId) {
            return optionsId === this.compatibleOptionsIds.option472
        },

        // 新增：检查是否为 option471 或 option472（兼容测试和生产环境）
        isOption471Or472(optionsId) {
            return this.isOption471(optionsId) || this.isOption472(optionsId)
        },

        // 新增：加载产品属性数据
        async loadProductAttributes() {
            try {
                this.loading.attributes_loading = true
                this.invalidProductId = false // 重置错误状态
                this.productNotExists = false // 重置产品不存在状态
                this.tokenMissing = false // 重置Token缺失状态
                this.errorMessage = "" // 重置错误信息

                // 从路由参数获取id
                let id = parseInt(this.$route.params.id)
                // 从链接查询参数获取token
                let token = this.$route.query.token

                // 校验id和token是否为空
                if (!isNumber(id)) {
                    // 不再跳转404，而是设置错误状态
                    this.invalidProductId = true
                    this.loading.attributes_loading = false
                    return
                }

                if (!token) {
                    console.error("Token参数缺失")
                    this.tokenMissing = true // 设置Token缺失状态
                    this.loading.attributes_loading = false
                    return
                }

                let attribute_id = this.$route.query.attribute ? parseInt(this.$route.query.attribute) : undefined
                let relate_attribute_id = this.$route.query.id ? parseInt(this.$route.query.id) : undefined

                const res = await this.$axios.post("/api/modify/product", {
                    products_id: id,
                    attribute: attribute_id,
                    id: relate_attribute_id,
                    token: token, // 使用从链接获取的token
                })

                // 新增：判断API响应状态
                if (res.code === 200) {
                    if (res.status === "success") {
                        // 成功情况，走现有逻辑
                        const data = res.data.data
                        let { length, no_hie, hie, relate, server, customized_server } = data.attributes
                        let custom_type // 0 无定制属性   1 非层级属性   2 层级属性
                        let custom_list = no_hie.concat(hie)

                        // 设置长度相关数据
                        if (data.is_customized === 1 && data.bread_crumbs.length && data.bread_crumbs[0].id === 209 && this.$store.state.webSiteInfo.website === "en") {
                            // 跳线定制产品默认单位ft（主站本地化）
                            this.length.length_unit = "ft"
                        }

                        if (data.bread_crumbs.length && data.bread_crumbs.length > 2 && data.bread_crumbs[data.bread_crumbs.length - 2].id) {
                            this.length.cid = data.bread_crumbs[data.bread_crumbs.length - 2].id
                        }

                        if (no_hie.length || hie.length) {
                            if (no_hie.length) {
                                custom_type = 1
                            } else {
                                custom_type = 2
                            }
                        } else {
                            custom_type = 0
                        }

                        if (length && length.title_name) {
                            this.length.min = length.min_length
                            this.length.max = length.max_length
                            this.length.min_ft = parseFloat((length.min_length / 0.3048).toFixed(2))
                            this.length.max_ft = parseFloat((length.max_length / 0.3048).toFixed(2))
                            this.length.has_length = true
                        }

                        let attrs = handleAttr(custom_list, custom_type)

                        let o_r_id = []
                        if (data.is_custom_server == 2) {
                            if (server.attributes) {
                                server.attributes.map((item) => {
                                    item.category_option_values.map((t) => {
                                        t.option_values.map((o) => {
                                            if (o.is_default) {
                                                o_r_id = o_r_id.concat(o.related_option_value_id)
                                            }
                                        })
                                    })
                                })
                            }
                        }

                        // 设置组件数据
                        this.products_id = id
                        this.is_custom_server = data.is_custom_server
                        this.is_customized = data.is_customized
                        this.attributes = {
                            custom_type,
                            relate,
                            custom_list: attrs,
                            custom_list_default_number: attrs ? attrs.length : 0,
                            length,
                            server,
                            customized_server,
                        }
                        this.custom_server_attribute_title = data.custom_server_attribute_title
                        this.custom_server_attr_limit = data.custom_server_attr_limit
                        this.o_r_id = o_r_id
                    } else if (res.status === "error") {
                        // 错误情况，从接口返回的message获取错误信息
                        this.productNotExists = true
                        this.errorMessage = res.message || "产品ID不存在" // 使用接口返回的message，如果没有则使用默认信息
                    }
                }
            } catch (error) {
                console.error("加载产品属性失败:", error)
                // 可以添加错误处理逻辑
            } finally {
                this.loading.attributes_loading = false
            }
        },
        /**
         * 公共方法
         */
        showChangeProductLoading() {
            this.change_product_loading = true
        },
        hideChangeProductLoading() {
            this.change_product_loading = false
        },

        /**
         * 长度组件
         */
        handleLengthChange(eventData) {
            // 处理长度组件的所有事件
            const { type, data } = eventData

            switch (type) {
                case "setLengthId":
                    this.setLengthId(data)
                    break
                case "inputLengchClick":
                    this.inputLengchClick()
                    break
                case "toogleLength":
                    this.toogleLength()
                    break
                case "lengthMouseEnter":
                    this.lengthMouseEnter(data)
                    break
                case "lengthBlur":
                    this.lengthBlur()
                    break
                case "changeLengthUnit":
                    this.changeLengthUnit(data)
                    break
            }
        },

        /**
         * 定制组件
         */
        handleCustomAttrChange(eventData) {
            // 处理定制属性组件的所有事件
            const { type, cus, cus_i, value_text, l, event, params } = eventData

            switch (type) {
                case "select":
                    this.checkRequired()
                    if (cus.private_type !== "customLabel") {
                        this.updateAttributes(cus, null, cus_i)
                    }
                    break
                case "select-input":
                    // 已在组件内处理
                    break
                case "input-focus":
                    // 已在组件内处理
                    break
                case "input":
                    if (cus.options_id === 159 || cus.options_id === 350 || cus.options_id === 352) {
                        let { addProducts, custom_index } = this.getAttributes(cus)
                        if (addProducts && addProducts.length && addProducts[0].attributes) {
                            let values = Object.values(addProducts[0].attributes)
                            let attrs = values && values.length ? Object.values(addProducts[0].attributes).flat() : []
                            attrs.map((item) => {
                                if (item.products_options_value_id === 3868) {
                                    this.updateAttributes(cus)
                                }
                            })
                        }
                    }
                    break
                case "input-blur":
                    this.checkRequired()
                    break
                case "check":
                    this.checkRequired(true)
                    if (this.add_cart_error) {
                        return
                    }
                    this.updateAttributes(cus)
                    break
                case "file":
                    this.checkRequired()
                    break
            }
        },

        attrInputChange: debounce(function (cus) {
            if (cus.options_values && cus.options_values.length) {
                let options_values = cus.options_values
                let o = {
                    products_options_value_text: cus.current_value,
                    products_options_value_id: 0,
                }
                if (this.attributes.custom_type === 2) {
                    o.column_id = options_values[0].column_id
                }
                if (cus.current_value) {
                    cus.current_attr.splice(0, cus.current_attr.length, o)
                }
                if (cus.options_id === 159 || cus.options_id === 350 || cus.options_id === 352) {
                    let { addProducts, custom_index } = this.getAttributes(cus)
                    if (addProducts && addProducts.length && addProducts[0].attributes) {
                        let values = Object.values(addProducts[0].attributes)
                        let attrs = values && values.length ? Object.values(addProducts[0].attributes).flat() : []
                        attrs.map((item) => {
                            if (item.products_options_value_id === 3868) {
                                this.updateAttributes(cus)
                            }
                        })
                    }
                }
            }
        }, 400),

        setLengthId(l) {
            this.length.custom_length = ""
            if (this.length.length_id == l.products_length_id) {
                return
            }
            this.is_show_spool = false
            this.length.length_id = l.products_length_id
            this.length.length_id_name = l.length_str
            this.length.length_error = ""
            this.checkRequired()
            this.updateAttributes(null, "length_change")
        },
        inputLengchClick() {
            this.length.length_id = ""
            this.length.length_id_name = ""
            this.length.custom_length_focus = true
        },

        lengthBlur() {
            this.length.custom_length_focus = false
            if (this.length.custom_length) {
                this.is_show_spool = false
                let length_error = this.$c("pages.Products.length_error")
                let length_error_max = this.website == "mx" ? this.$c("pages.Products.mx_length_error_max") : this.$c("pages.Products.length_error_max")
                let cable_length_error = this.$c("pages.Products.cable_length_error")
                let cable_length_error_200 = this.$c("pages.Products.cable_length_error_200")

                let err = ""
                let length_m
                //1ft = 0.3048m
                if (this.length.length_unit === "m") {
                    length_m = parseFloat(this.length.custom_length)
                } else if (this.length.length_unit === "ft") {
                    length_m = parseFloat(this.length.custom_length) * 0.3048
                }
                if (length_m) {
                    if (this.length.min || this.length.max) {
                        if (this.length.min && length_m < this.length.min) {
                            err = length_error.replace("XX", this.length.min)
                            if (this.website !== "jp") {
                                err = `${err}(${this.length.min_ft}ft)`
                            }
                            if (this.website === "fr" || this.website === "de") {
                                err = err.replace(".", ",")
                            }
                            this.length.length_error = err
                            this.length.custom_length = ""
                            return
                        }
                        if (this.length.max && length_m > this.length.max) {
                            let err_max = `${this.length.max}'m (${this.length.max_ft}ft)`
                            if (this.website === "fr" || this.website === "de") {
                                err_max = err_max.replace(".", ",")
                            }
                            if (this.website === "au") {
                                err = length_error_max.replace("<EMAIL>", "<EMAIL>")
                            }
                            if (this.website == "ru") {
                                err = length_error_max.replace("XX", err_max).replace("'m", " м ").replace("ft)", " фута)")
                            } else if (this.website == "jp") {
                                err = length_error_max.replace("XX", err_max).replace("'m ", "m")
                            } else {
                                err = length_error_max.replace("XX", err_max).replace("'", "")
                            }

                            this.length.length_error = err
                            this.length.custom_length = ""
                            return
                        }
                    }
                    if (this.length.cid === 2875) {
                        if ([30775, 30793, 30809, 30746].includes(this.products_id)) {
                            if (this.products_id === 30809) {
                                if (length_m < 1 || length_m > 100) {
                                    if (["de", "fr", "es"].includes(this.website)) {
                                        err = cable_length_error.replace("0.5", "1")
                                        err = cable_length_error.replace("1.64", "3,28")
                                    } else {
                                        err = cable_length_error.replace("0.5", "1")
                                        err = cable_length_error.replace("1.64", "3.28")
                                    }
                                    this.length.length_error = err
                                    this.length.custom_length = ""
                                    return
                                }
                            } else if (this.products_id === 30775) {
                                if (length_m < 0.5 || length_m > 200) {
                                    err = cable_length_error_200
                                    this.length.length_error = cable_length_error_200
                                    this.length.custom_length = ""
                                    return
                                }
                            } else {
                                if (length_m < 0.5 || length_m > 100) {
                                    err = cable_length_error
                                    this.length.length_error = err
                                    this.length.custom_length = ""
                                    return
                                }
                            }
                        } else {
                            if (length_m > 100) {
                                if (this.length.length_unit === "m") {
                                    this.length.custom_length = 100
                                } else {
                                    this.length.custom_length = 328.084
                                }
                            }
                        }
                    }
                    this.length.length_error = ""
                    this.checkRequired()
                    this.updateAttributes(null, "length_change")
                } else {
                    this.length.length_error = ""
                    this.length.custom_length = ""
                }
            } else {
                this.is_show_spool = true
            }
        },
        toogleLength() {
            this.length.length_select_show = !this.length.length_select_show
            if (!this.length.length_select_show) {
                this.length.length_mouse = ""
            }
            if (this.length.length_select_show) {
                this.initLengthUnit()
            }
        },
        initLengthUnit() {
            if (!this.isOffline && this.isMobile) {
                this.$nextTick(() => {
                    let allLength = document.querySelectorAll(".length_input_box")
                    if (allLength && allLength.length) {
                        for (let i = 0; i < allLength.length; i++) {
                            let dom = allLength[i]
                            let top = dom.getBoundingClientRect().top
                            let right = dom.getBoundingClientRect().right
                            let h = dom.offsetHeight
                            let u = dom.querySelector(".length_unit_select")
                            u.style.position = "fixed"
                            u.style.left = `${right - 39}px`
                            u.style.top = `${top + h}px`
                            u.style.width = `40px`
                            u.style.zIndex = `10`
                        }
                    }
                })
            }
        },
        lengthMouseEnter(u) {
            this.length.length_mouse = u
        },
        changeLengthUnit(u) {
            // if (u !== this.length.length_unit) {
            this.length.length_unit = u
            this.toogleLength()
            this.lengthBlur()
            //}
        },

        // 获取属性
        getAttributes(cus) {
            // 当属性切换时，取消所有options_type === 3的选项值，同时取消318和471选项并清除它们新增的选项（但当前操作的选项类型为3时不执行）
            if (cus && cus.options_type !== 3 && cus.options_type !== 1 && this.attributes && this.attributes.custom_list && this.attributes.custom_list.length) {
                console.log("getAttributes清除逻辑执行 - 当前操作选项:", cus.options_id, "类型:", cus.options_type)

                // 先清除选项值，再处理扩展选项删除
                for (let i = 0; i < this.attributes.custom_list.length; i++) {
                    // 取消选中options_type为3的选项
                    if (this.attributes.custom_list[i].options_type === 3) {
                        console.log("清除options_type=3的选项:", this.attributes.custom_list[i].options_id)
                        this.attributes.custom_list[i].current_value = []
                        this.attributes.custom_list[i].current_attr = []
                    }
                    // 取消318和471/472选项
                    if (this.attributes.custom_list[i].options_id === 318 || this.isOption471Or472(this.attributes.custom_list[i].options_id)) {
                        console.log("清除318/471/472选项:", this.attributes.custom_list[i].options_id, "类型:", this.attributes.custom_list[i].options_type, "当前值:", this.attributes.custom_list[i].current_value)
                        // 清除选项的当前值
                        this.attributes.custom_list[i].current_value = this.attributes.custom_list[i].options_type === 3 ? [] : ""
                        this.attributes.custom_list[i].current_attr = this.attributes.custom_list[i].options_type === 3 ? [] : ""
                    }
                }

                // 从后往前删除扩展选项，避免索引错乱
                for (let i = this.attributes.custom_list.length - 1; i >= 0; i--) {
                    if ((this.attributes.custom_list[i].options_id === 318 || this.isOption471Or472(this.attributes.custom_list[i].options_id)) && this.extensionOptionsCount && this.extensionOptionsCount[i]) {
                        const countToRemove = this.extensionOptionsCount[i]
                        console.log("删除扩展选项数量:", countToRemove, "选项ID:", this.attributes.custom_list[i].options_id)
                        if (countToRemove > 0) {
                            this.attributes.custom_list.splice(i + 1, countToRemove)
                        }
                        delete this.extensionOptionsCount[i]
                    }
                }
            }

            let custom_product = {
                products_id: this.products_id,
                qty: this.qty,
                isChecked: 1,
                current_options_id: cus ? cus.options_type : 0,
                attr: [],
                attributes: {},
            }
            // let custom_product_popup = JSON.parse(JSON.stringify(custom_product));
            // custom_product_popup.attr = []
            custom_product.product_info = {
                loading: false,
                // products_name: this.product_info.products_name,
                // products_img: this.getProductsInfoImg(this.images).big,
            }

            // 定义标签属性ID列表
            const labelAttributeIds = [326, 327, 328, 329, 330]

            // 判断是否为标签属性的函数
            const isLabelAttribute = (optionsId) => {
                return labelAttributeIds.includes(optionsId)
            }

            let label_product = {
                qty: 1,
                isChecked: 1,
                attr: [],
                attributes: {},
            }
            // let label_product_popup = JSON.parse(JSON.stringify(label_product));
            // label_product_popup.attr = [];
            if (this.label_products.label_products_id) {
                label_product.products_id = this.label_products.label_products_id
                label_product.product_info = {
                    loading: false,
                    products_name: this.label_products.products_name,
                    products_img: this.label_products.products_img,
                }
            }
            let custom_type = this.attributes.custom_type
            let custom_index
            if (this.length.has_length) {
                let len = {
                    custom_length: "",
                    product_length_id: "",
                }
                if (this.length.custom_length || this.length.length_id) {
                    if (this.length.custom_length) {
                        len.custom_length = `${parseFloat(this.length.custom_length)}${this.length.length_unit}`
                        len.product_length_id = ""
                        custom_product.attr.push(`${this.$c("pages.Products.Length")} - ${parseFloat(this.length.custom_length)}${this.length.length_unit}`)
                    } else {
                        len.custom_length = ""
                        len.product_length_id = this.length.length_id
                        custom_product.attr.push(`${this.$c("pages.Products.Length")} - ${this.length.length_id_name}`)
                    }
                }
                custom_product["attributes"]["length"] = len
            }

            if (this.attributes && this.attributes.custom_list && this.attributes.custom_list.length) {
                let custom_list = this.attributes.custom_list
                for (let i = 0; i < custom_list.length; i++) {
                    if (custom_list[i].options_type === 0) {
                        if (custom_list[i].current_attr && custom_list[i].current_attr.length) {
                            if (this.label_products.label_products_id && isLabelAttribute(custom_list[i].options_id)) {
                                label_product.attributes[custom_list[i].options_id] = custom_list[i].current_attr
                                for (let j = 0; j < custom_list[i].options_values.length; j++) {
                                    if (custom_list[i].options_values[j].options_values_id === custom_list[i].current_value) {
                                        custom_list[i].current_value = custom_list[i].options_values[j].options_values_id
                                        label_product.attr.push(`${custom_list[i].options_name} - ${custom_list[i].options_values[j].options_values_name}`)
                                    }
                                }
                                if (custom_list[i].current_value === 4262 && custom_list[i].current_text) {
                                    label_product.attr.push(`${custom_list[i].options_name} - ${custom_list[i].current_text}`)
                                }
                            } else {
                                if ((custom_list[i].options_id === 341 && this.is_show_spool) || custom_list[i].options_id !== 341) {
                                    custom_product.attributes[custom_list[i].options_id] = custom_list[i].current_attr
                                    for (let j = 0; j < custom_list[i].options_values.length; j++) {
                                        if (custom_list[i].options_values[j].options_values_id === custom_list[i].current_value) {
                                            custom_list[i].current_value = custom_list[i].options_values[j].options_values_id
                                            custom_product.attr.push(`${custom_list[i].options_name} - ${custom_list[i].options_values[j].options_values_name}`)
                                        }
                                    }
                                }
                                if (custom_list[i].current_value === 4262 && custom_list[i].current_text) {
                                    label_product.attr.push(`${custom_list[i].options_name} - ${custom_list[i].current_text}`)
                                }
                            }
                        }
                    }

                    if (custom_list[i].options_type === 1) {
                        if (custom_list[i].current_attr && custom_list[i].current_attr.length) {
                            if (this.label_products.label_products_id && isLabelAttribute(custom_list[i].options_id)) {
                                label_product.attributes[custom_list[i].options_id] = custom_list[i].current_attr
                                label_product.attr.push(`${custom_list[i].options_name} - ${custom_list[i].current_value}`)
                            } else {
                                custom_product.attributes[custom_list[i].options_id] = custom_list[i].current_attr
                                custom_product.attr.push(`${custom_list[i].options_name} - ${custom_list[i].current_value}`)
                            }
                        }
                    }

                    if (custom_list[i].options_type === 4) {
                        if (custom_list[i].current_attr && custom_list[i].current_attr.length) {
                            if (this.label_products.label_products_id && isLabelAttribute(custom_list[i].options_id)) {
                                label_product.attributes[custom_list[i].options_id] = custom_list[i].current_attr

                                label_product.attr.push(`${custom_list[i].options_name} - ${custom_list[i].current_value}`)
                            } else {
                                custom_product.attributes[custom_list[i].options_id] = custom_list[i].current_attr
                                custom_product.attr.push(`${custom_list[i].options_name} - ${custom_list[i].current_value}`)
                            }
                        }
                    }
                    if (custom_list[i].options_type === 3) {
                        if (custom_list[i].current_attr && custom_list[i].current_attr.length) {
                            if (this.label_products.label_products_id && isLabelAttribute(custom_list[i].options_id)) {
                                label_product.attributes[custom_list[i].options_id] = custom_list[i].current_attr
                                for (let j = 0; j < custom_list[i].current_value.length; j++) {
                                    for (let k = 0; k < custom_list[i].options_values.length; k++) {
                                        if (custom_list[i].current_value[j] === custom_list[i].options_values[k].options_values_id) {
                                            label_product.attr.push(`${custom_list[i].options_name} - ${custom_list[i].options_values[k].options_values_name}`)
                                        }
                                    }
                                }
                            } else {
                                for (let j = 0; j < custom_list[i].current_value.length; j++) {
                                    custom_product.attributes[custom_list[i].options_id] = custom_list[i].current_attr
                                    for (let k = 0; k < custom_list[i].options_values.length; k++) {
                                        if (custom_list[i].current_value[j] === custom_list[i].options_values[k].options_values_id) {
                                            custom_product.attr.push(`${custom_list[i].options_name} - ${custom_list[i].options_values[k].options_values_name}`)
                                        }
                                    }
                                }
                            }
                        } else {
                            // 如果是318选项且传入了cus参数，设置custom_index
                            if (cus && cus.options_id === 318) {
                                if (custom_list[i].options_id === 318) {
                                    custom_index = i
                                }
                            } else if (cus && this.isOption471(cus.options_id)) {
                                if (this.isOption471(custom_list[i].options_id)) {
                                    custom_index = i
                                }
                            } else if (cus && this.isOption472(cus.options_id)) {
                                if (this.isOption472(custom_list[i].options_id)) {
                                    custom_index = i
                                }
                            }
                            // 如果没有传入cus参数，记录第一个未选中的选项索引
                            if (!cus && custom_index === undefined) {
                                custom_index = i
                            }
                        }
                        // 318选项的位置标识已通过 isLabelAttribute 函数处理
                    }
                    if (cus && custom_type === 2 && cus.options_id === custom_list[i].options_id) {
                        // if (cus  && cus.options_id === custom_list[i].options_id) {
                        custom_index = i
                        break
                    }
                }
            }
            if (cus && custom_type) {
                custom_product.current_options_id = cus.options_id
            }
            // if (!cus && this.match_products_id) {
            // 	custom_product.products_id = this.match_products_id;
            // 	custom_product.current_options_id = 0;
            // 	custom_product.attributes = {};
            // }
            let addProducts = []
            addProducts.push(custom_product)
            if (!cus && this.label_products.label_products_id) {
                addProducts.push(label_product)
            }

            if (!cus && this.related_test_tool_products && this.related_test_tool_products.length) {
                for (let i = 0; i < this.related_test_tool_products.length; i++) {
                    let o = this.related_test_tool_obj[this.related_test_tool_products[i]]
                    addProducts.push(o)
                }
            }
            if (addProducts[0]) {
                let productDevList = []
                let productDevAttr = []
                let alxAry = []
                productDevList = addProducts[0].attr
                for (let wb in addProducts[0].attributes) {
                    if (wb != "length") {
                        alxAry.push({
                            option_id: wb,
                            value_id: addProducts[0].attributes[wb][0].products_options_value_id,
                        })
                    }
                }
                // 只处理到custom_index为止的选项，或者如果没有custom_index则处理所有选项
                const endIndex = cus && cus.options_id === 318 ? custom_index + 1 : this.attributes.custom_list.length
                for (let xz = 0; xz < endIndex; xz++) {
                    if (this.attributes.custom_list[xz].options_show != false) {
                        productDevAttr.push({
                            option_id: this.attributes.custom_list[xz].options_id,
                        })
                    }
                }
                productDevAttr.forEach((item) => {
                    alxAry.forEach((t) => {
                        if (item.option_id == t.option_id) {
                            item.value_id = t.value_id
                        }
                    })
                })
                if (addProducts[0].attributes["length"]) {
                    if (addProducts[0].attributes["length"].custom_length || addProducts[0].attributes["length"].product_length_id) {
                        productDevAttr.unshift({
                            option_id: 0,
                            value_id: addProducts[0].attributes["length"].custom_length ? addProducts[0].attributes["length"].custom_length : addProducts[0].attributes["length"].product_length_id,
                        })
                    }
                }
                productDevAttr = productDevAttr.filter((item) => {
                    return !isNaN(item.value_id)
                })
                for (let y = 0; y < productDevAttr.length; y++) {
                    productDevAttr[y].option_name = `${addProducts[0].attr[y].split(` - `)[0]}`
                    productDevAttr[y].value_name = `${addProducts[0].attr[y].split(` - `)[1]}`
                }
                this.productDevList = productDevList
                this.productDevAttr = productDevAttr
            }
            return { addProducts, custom_index }
        },
        // 更新选项
        updateAttributes(cus, type, cus_i) {
            let _this = this
            let { addProducts, custom_index } = this.getAttributes(cus)
            // 使用传入的cus_i作为实际的索引，而不是getAttributes返回的custom_index
            if (cus_i !== undefined) {
                custom_index = cus_i
            }

            // 判断操作类型：检查当前选项是否被选中
            let isOptionSelected = false
            if (cus && cus.current_value !== undefined && cus.current_value !== null) {
                // 根据不同的选项类型判断是否被选中
                if (cus.options_type === 0) {
                    // 下拉选择框：检查是否有选中值
                    isOptionSelected = cus.current_value !== "" && cus.current_value !== null
                } else if (cus.options_type === 1) {
                    // 文本输入框：检查是否有输入内容
                    isOptionSelected = cus.current_value !== "" && cus.current_value !== null
                } else if (cus.options_type === 3) {
                    // 复选框：检查是否有选中项
                    isOptionSelected = cus.current_value && cus.current_value.length > 0
                } else if (cus.options_type === 4) {
                    // 单选框：检查是否有选中值
                    isOptionSelected = cus.current_value !== "" && cus.current_value !== null
                }
            }
            console.log("updateAttributes debug - options_id:", cus?.options_id, "isOptionSelected:", isOptionSelected, "current_value:", cus?.current_value)

            let new_products = JSON.parse(JSON.stringify(addProducts))
            if (new_products && new_products.length) {
                for (let n = 0; n < new_products.length; n++) {
                    delete new_products[n].attr
                    delete new_products[n].product_info
                }
            }
            if (type == "cupboard") {
                this.serverCusList.forEach((item) => {
                    let index = this.customize_server.findIndex((e) => e.option_id == item.option_id)
                    if (index != -1) {
                        // this.customize_server[index] = item
                    } else {
                        this.customize_server.push(item)
                    }
                })
                if (this.customize_server.length) {
                    let arr = JSON.parse(
                        JSON.stringify(
                            this.customize_server.filter((item) => {
                                return item.value_type == 1
                            })
                        )
                    )
                    let aee = arr.map((item) => {
                        delete item.value_type
                        return item
                    })
                    this.customize_serverList = aee
                    new_products[0].customize_server = aee
                }
            }
            this.showChangeProductLoading()
            this.loading.attributes_loading = true
            this.$axios
                .post("/api/products/change_attributes", { products: new_products })
                .then((res) => {
                    this.hideChangeProductLoading()
                    this.loading.attributes_loading = false
                    // this.product_info.products_price_str = res.data.price_str
                    // this.product_info.tax_price.price_str = res.data.price_str_tax
                    this.is_show_spool = res.data.is_show_spool
                    // this.match_products_id = res.data.match_products_id
                    // this.delivery_date = res.data.delivery_date
                    // this.inventory = res.data.inventory
                    // this.weight = res.data.products_weight_show
                    // if (res.data.shipping && res.data.shipping.default_shipping_free) {
                    //     this.shipping = res.data.shipping.default_shipping_free
                    // }
                    // this.need_customized_pop = res.data.need_customized_pop
                    if (res.data.label_products_info && res.data.label_products_info.products_id) {
                        this.label_products.label_products_id = res.data.label_products_info.products_id
                        this.label_products.products_name = res.data.label_products_info.products_desc
                        this.label_products.products_img = ""
                    } else {
                        this.label_products.label_products_id = ""
                        this.label_products.products_name = ""
                        this.label_products.products_img = ""
                    }
                    // if (this.iso_code === "JP" && this.language_id === 8 && this.currency === "USD" && res.data.price_express) {
                    //     this.product_info.price_express = res.data.price_express
                    // }
                    // 处理选项变化逻辑
                    console.log(
                        "接口返回处理 - isOptionSelected:",
                        isOptionSelected,
                        "has attributes_extension:",
                        !!(res.data.attributes_extension && res.data.attributes_extension.length),
                        "is_column:",
                        res.data.is_column
                    )

                    // 新增：检查是否是关联属性
                    if (res.data.is_column) {
                        if (res.data.attributes_extension && res.data.attributes_extension.length) {
                            // 如果产品是关联属性，则把当前选项下面的全部选项替换成res.data.attributes_extension里面的数据
                            console.log("产品是关联属性，替换所有后续选项")

                            // 检查是否是长度属性（current_options_id为0）
                            if (res.data.current_options_id === 0) {
                                // 长度属性：直接覆盖整个custom_list
                                let attrs = handleAttr(res.data.attributes_extension, this.attributes.custom_type, res.data.current_options_id || 0)
                                this.attributes.custom_list.splice(0, this.attributes.custom_list.length, ...attrs)
                            } else {
                                // 非长度属性：原有逻辑
                                // 如果有传入的cus_i，优先使用；否则根据current_options_id查找索引
                                if (cus_i === undefined && res.data.current_options_id) {
                                    for (let i = 0; i < this.attributes.custom_list.length; i++) {
                                        if (this.attributes.custom_list[i].options_id === res.data.current_options_id) {
                                            custom_index = i
                                            break
                                        }
                                    }
                                }

                                let attrs = handleAttr(res.data.attributes_extension, this.attributes.custom_type, res.data.current_options_id || 0)

                                // 删除当前选项下面的全部选项，然后替换成新的选项
                                this.attributes.custom_list.splice(custom_index + 1, this.attributes.custom_list.length - custom_index - 1, ...attrs)

                                // 更新扩展选项计数
                                if (!this.extensionOptionsCount) {
                                    this.extensionOptionsCount = {}
                                }
                                this.extensionOptionsCount[custom_index] = attrs.length
                            }
                        } else {
                            // res.data.is_column存在但res.data.attributes_extension不存在的情况
                            // 通过cus_i把this.attributes.custom_list里面大于cus_i的项都删除
                            if (cus_i !== undefined) {
                                this.attributes.custom_list.splice(cus_i + 1)
                            }
                        }
                    }
                    // 原有逻辑：非关联属性的处理
                    else if (isOptionSelected && res.data.attributes_extension && res.data.attributes_extension.length) {
                        // 只有在选项被选中且接口返回扩展数据时才处理新增选项
                        console.log("执行新增选项逻辑")
                        // 如果有传入的cus_i，优先使用；否则根据current_options_id查找索引
                        if (cus_i === undefined && res.data.current_options_id) {
                            for (let i = 0; i < this.attributes.custom_list.length; i++) {
                                if (this.attributes.custom_list[i].options_id === res.data.current_options_id) {
                                    custom_index = i
                                    break
                                }
                            }
                        }

                        let attrs = handleAttr(res.data.attributes_extension, this.attributes.custom_type, res.data.current_options_id || 0)

                        // 记录新增的选项数量，用于后续删除
                        if (!this.extensionOptionsCount) {
                            this.extensionOptionsCount = {}
                        }
                        this.extensionOptionsCount[custom_index] = attrs.length

                        // if ((this.attributes.custom_type === 1 ||this.attributes.custom_type === 2) && (custom_index+1)) {
                        if (this.attributes.custom_type === 1) {
                            if (res.data.current_options_id === 318 && res.data.label_products_info) {
                                attrs.map((item) => {
                                    item.label_products_id = res.data.label_products_info.products_id
                                })
                                // 在选中项后面插入新选项
                                this.attributes.custom_list.splice(custom_index + 1, 0, ...attrs)
                            } else {
                                let n
                                if (this.attributes.custom_list && this.attributes.custom_list.length && res.data.current_options_id) {
                                    for (let m = 0; m < this.attributes.custom_list.length; m++) {
                                        if (this.attributes.custom_list[m].options_id === res.data.current_options_id) {
                                            n = m
                                        }
                                    }
                                    if (n > custom_index) {
                                        // 删除之前的扩展选项，插入新的扩展选项
                                        const prevCount = this.extensionOptionsCount[custom_index] || 0
                                        this.attributes.custom_list.splice(custom_index + 1, prevCount, ...attrs)
                                    } else {
                                        if (cus && cus.options_id === 2) {
                                            // 处理选项ID为2的特殊逻辑
                                            if (cus && cus.current_value === 6452) {
                                                // 当选择6452时，删除ID为159的选项，添加4个新选项（349、350、351、352）
                                                // 找到并删除ID为159的选项
                                                const option159Index = this.attributes.custom_list.findIndex((item) => item.options_id === 159)
                                                if (option159Index > -1) {
                                                    this.attributes.custom_list.splice(option159Index, 1)
                                                }
                                                // 在ID为2的选项后面插入新的4个选项
                                                const option2Index = this.attributes.custom_list.findIndex((item) => item.options_id === 2)
                                                if (option2Index > -1) {
                                                    this.attributes.custom_list.splice(option2Index + 1, 0, ...attrs)
                                                }
                                            } else {
                                                // 当选择其他值时，删除4个选项（349、350、351、352），添加ID为159的选项
                                                const optionsToRemove = [349, 350, 351, 352]
                                                // 从后往前删除，避免索引变化影响
                                                for (let i = this.attributes.custom_list.length - 1; i >= 0; i--) {
                                                    if (optionsToRemove.includes(this.attributes.custom_list[i].options_id)) {
                                                        this.attributes.custom_list.splice(i, 1)
                                                    }
                                                }
                                                // 在ID为2的选项后面插入返回的选项（应该是ID为159的选项）
                                                const option2Index = this.attributes.custom_list.findIndex((item) => item.options_id === 2)
                                                if (option2Index > -1) {
                                                    this.attributes.custom_list.splice(option2Index + 1, 0, ...attrs)
                                                }
                                            }
                                        } else {
                                            const prevCount = this.extensionOptionsCount[custom_index] || 0
                                            this.attributes.custom_list.splice(custom_index + 1, prevCount, ...attrs)
                                        }
                                    }
                                }
                            }
                        }
                        if (this.attributes.custom_type === 2) {
                            // 删除之前的扩展选项，插入新的扩展选项
                            const prevCount = this.extensionOptionsCount[custom_index] || 0
                            this.attributes.custom_list.splice(custom_index + 1, prevCount, ...attrs)
                        }
                        if (type && type === "length_change") {
                            this.attributes.custom_list.splice(0, this.attributes.custom_list.length, ...attrs)
                        }
                    }

                    // 取消选中时，删除对应数量的扩展选项
                    if (!isOptionSelected) {
                        console.log("执行删除选项逻辑 - options_id:", cus?.options_id)
                        if (cus && (cus.options_id === 318 || this.isOption471(cus.options_id))) {
                            // 对于318和471选项，只删除记录的扩展选项数量，而不是所有后续选项
                            if (this.extensionOptionsCount && this.extensionOptionsCount[custom_index]) {
                                const countToRemove = this.extensionOptionsCount[custom_index]
                                if (countToRemove > 0) {
                                    this.attributes.custom_list.splice(custom_index + 1, countToRemove)
                                }
                                delete this.extensionOptionsCount[custom_index]
                            }
                        } else {
                            // 其他选项的常规删除逻辑
                            if (this.extensionOptionsCount && this.extensionOptionsCount[custom_index]) {
                                const countToRemove = this.extensionOptionsCount[custom_index]
                                this.attributes.custom_list.splice(custom_index + 1, countToRemove)
                                delete this.extensionOptionsCount[custom_index]
                            }

                            if (this.attributes.custom_type === 2) {
                                // 如果没有记录的扩展选项数量，则删除所有后续选项（保持原有逻辑作为兜底）
                                if (!this.extensionOptionsCount || !this.extensionOptionsCount[custom_index]) {
                                    this.attributes.custom_list.splice(custom_index + 1, this.attributes.custom_list.length - custom_index - 1)
                                }
                            }
                        }
                    }
                })
                .catch((err) => {
                    this.loading.global_loading = false
                    this.$message(err.message)
                })
        },
        // 校验选项
        checkRequired(show) {
            let flag = false
            let option_id = ""
            let err_example = this.$c("pages.Products.please_select_attribute")
            let err_example_dev = this.$c("pages.Products.please_select_attribute_dev")
            if (this.length && this.length.has_length) {
                if (!this.length.custom_length && !this.length.length_id) {
                    flag = true
                    if (show) {
                        this.add_cart_error = err_example
                    }
                }
            }
            if (this.attributes.custom_list && this.attributes.custom_list.length) {
                let custom_list = this.attributes.custom_list
                for (let i = 0; i < custom_list.length; i++) {
                    if (custom_list[i].options_type === 4) {
                        this.has_file = true
                    }
                    if (custom_list[i].is_required) {
                        if (custom_list[i].options_type === 0 || custom_list[i].options_type === 1 || custom_list[i].options_type === 4) {
                            if (!custom_list[i].current_value) {
                                if (custom_list[i].options_id === 159) {
                                    option_id = 159
                                } else if (custom_list[i].options_id === 350) {
                                    option_id = 350
                                } else if (custom_list[i].options_id === 352) {
                                    option_id = 352
                                }
                                flag = true
                                break
                            }
                            if (custom_list[i].related_options && custom_list[i].related_options.options_values_id) {
                                if (custom_list[i].related_options.is_required && !custom_list[i].current_text) {
                                    flag = true
                                    break
                                }
                            }
                        } else if (custom_list[i].options_type === 3) {
                            if (!custom_list[i].current_value.length) {
                                flag = true
                                break
                            }
                        }
                    }
                }
            }
            if (flag) {
                if (show) {
                    if (option_id === 159 || option_id === 350 || option_id === 352) {
                        this.add_cart_error_dev = err_example_dev
                    } else {
                        this.add_cart_error = err_example
                    }
                } else {
                    this.add_cart_error_dev = ""
                    this.add_cart_error = ""
                }
            } else {
                this.add_cart_error = ""
                this.add_cart_error_dev = ""
            }
        },

        // 切换服务器属性
        changeServerAttributes(obj) {
            this.isCustomAttribute = JSON.parse(JSON.stringify(obj))
            delete this.isCustomAttribute.isRequest
            if (!obj.isRequest && obj.isRequest != 0) {
                return
            }
            if (this.is_custom_server == 2) {
                this.loading.global_loading = true
            }
            this.$axios
                .post("/api/products/change_server_attributes", this.isCustomAttribute)
                .then((res) => {
                    if (this.is_custom_server == 1) {
                        this.$refs.customAttr.forEach((item) => {
                            item.attr_loading = false
                        })
                    } else if (this.is_custom_server == 2) {
                        this.loading.add_cart_loading = false
                    }
                    // this.product_info.products_price_str = res.data.price_str
                    // this.product_info.tax_price.price_str = res.data.price_str_tax
                    this.is_show_spool = res.data.is_show_spool
                    // this.match_products_id = res.data.match_products_id
                    // this.delivery_date = res.data.delivery_date
                    // this.inventory = res.data.inventory
                    // this.weight = res.data.products_weight_show
                    // if (res.data.shipping && res.data.shipping.default_shipping_free) {
                    //     this.shipping = res.data.shipping.default_shipping_free
                    // }
                    if (res.data.label_products_info && res.data.label_products_info.products_id) {
                        this.label_products.label_products_id = res.data.label_products_info.products_id
                        this.label_products.products_name = res.data.label_products_info.products_desc
                        this.label_products.products_img = ""
                    } else {
                        this.label_products.label_products_id = ""
                        this.label_products.products_name = ""
                        this.label_products.products_img = ""
                    }
                    // if (this.iso_code === "JP" && this.language_id === 8 && this.currency === "USD" && res.data.price_express) {
                    //     this.product_info.price_express = res.data.price_express
                    // }
                    if (res.data.attributes_extension && res.data.attributes_extension.length) {
                        // 获取当前选中项的索引
                        let custom_index = 0
                        if (res.data.current_options_id) {
                            for (let i = 0; i < this.attributes.custom_list.length; i++) {
                                if (this.attributes.custom_list[i].options_id === res.data.current_options_id) {
                                    custom_index = i
                                    break
                                }
                            }
                        }

                        let attrs = handleAttr(res.data.attributes_extension, this.attributes.custom_type, res.data.current_options_id || 0)

                        // 记录新增的选项数量，用于后续删除
                        if (!this.extensionOptionsCount) {
                            this.extensionOptionsCount = {}
                        }
                        this.extensionOptionsCount[custom_index] = attrs.length

                        // if ((this.attributes.custom_type === 1 ||this.attributes.custom_type === 2) && (custom_index+1)) {
                        if (this.attributes.custom_type === 1) {
                            if (res.data.current_options_id === 318 && res.data.label_products_info) {
                                attrs.map((item) => {
                                    item.label_products_id = res.data.label_products_info.products_id
                                })
                                // 在选中项后面插入新选项
                                this.attributes.custom_list.splice(custom_index + 1, 0, ...attrs)
                            } else {
                                let n
                                if (this.attributes.custom_list && this.attributes.custom_list.length && res.data.current_options_id) {
                                    for (let m = 0; m < this.attributes.custom_list.length; m++) {
                                        if (this.attributes.custom_list[m].options_id === res.data.current_options_id) {
                                            n = m
                                        }
                                    }
                                }
                                if (n > custom_index) {
                                    // 删除之前的扩展选项，插入新的扩展选项
                                    const prevCount = this.extensionOptionsCount[custom_index] || 0
                                    this.attributes.custom_list.splice(custom_index + 1, prevCount, ...attrs)
                                } else {
                                    // 在选中项后面插入新选项
                                    const prevCount = this.extensionOptionsCount[custom_index] || 0
                                    this.attributes.custom_list.splice(custom_index + 1, prevCount, ...attrs)
                                }
                            }
                        }
                        if (this.attributes.custom_type === 2) {
                            // 删除之前的扩展选项，插入新的扩展选项
                            const prevCount = this.extensionOptionsCount[custom_index] || 0
                            this.attributes.custom_list.splice(custom_index + 1, prevCount, ...attrs)
                        }
                        if (type && type === "length_change") {
                            this.attributes.custom_list.splice(0, this.attributes.custom_list.length, ...attrs)
                        }
                    } else {
                        // 取消选中时，删除对应数量的扩展选项
                        console.log("attrInputChange删除逻辑执行 - cus:", cus, "custom_index:", custom_index, "custom_list长度:", this.attributes.custom_list.length)
                        if (cus && cus.options_id === 318) {
                            // 对于318选项，强制删除所有后续选项，确保完全清理
                            const remainingCount = this.attributes.custom_list.length - custom_index - 1
                            console.log("attrInputChange 318选项删除 - remainingCount:", remainingCount)
                            if (remainingCount > 0) {
                                console.log("attrInputChange执行删除操作，删除位置:", custom_index + 1, "删除数量:", remainingCount)
                                this.attributes.custom_list.splice(custom_index + 1, remainingCount)
                                console.log("attrInputChange删除后custom_list长度:", this.attributes.custom_list.length)
                            }
                            // 清理扩展选项计数记录
                            if (this.extensionOptionsCount && this.extensionOptionsCount[custom_index]) {
                                delete this.extensionOptionsCount[custom_index]
                            }
                        } else {
                            // 其他选项的常规删除逻辑
                            if (this.extensionOptionsCount && this.extensionOptionsCount[custom_index]) {
                                const countToRemove = this.extensionOptionsCount[custom_index]
                                this.attributes.custom_list.splice(custom_index + 1, countToRemove)
                                delete this.extensionOptionsCount[custom_index]
                            }

                            if (this.attributes.custom_type === 2) {
                                // 如果没有记录的扩展选项数量，则删除所有后续选项（保持原有逻辑作为兜底）
                                if (!this.extensionOptionsCount || !this.extensionOptionsCount[custom_index]) {
                                    this.attributes.custom_list.splice(custom_index + 1, this.attributes.custom_list.length - custom_index - 1)
                                }
                            }
                        }
                    }
                })
                .catch((err) => {
                    if (this.is_custom_server == 1) {
                        this.$refs.customAttr.forEach((item) => {
                            item.attr_loading = false
                        })
                    }
                    this.loading.global_loading = false
                })
        },

        setCusCup(data) {
            if (this.is_custom_server == 2) {
                this.server_attributes = {}
                let server_attributes = {}
                this.attributes.server.attributes.map((item) => {
                    let arr = []
                    item.category_option_values.map((ite) => {
                        let lag = false
                        let isrela = false
                        let nex = ite.option_values.map((t) => {
                            return String(t.server_value_id)
                        })
                        let section =
                            data &&
                            data.related &&
                            data.related.filter((item) => {
                                return nex.includes(item)
                            })
                        if (section && section.length) {
                            lag = true
                        }
                        ite.option_values.map((t) => {
                            if (data && item.option_id == data.option_id) {
                                if (t.server_value_products_id == data.value_id) {
                                    t.is_default = 1
                                    arr.push({ server_value_id: t.server_value_id, server_value_products_id: t.server_value_products_id, server_value_products_qty: t.server_value_products_qty })
                                } else {
                                    t.is_default = 0
                                }
                            } else {
                                if (data && data.related.indexOf(String(t.server_value_id)) != -1) {
                                    if (!isrela) {
                                        t.is_default = 1
                                        isrela = true
                                        arr.push({ server_value_id: t.server_value_id, server_value_products_id: t.server_value_products_id, server_value_products_qty: t.server_value_products_qty })
                                    } else {
                                        t.is_default = 0
                                    }
                                } else {
                                    if (t.is_default == 1 && !lag) {
                                        arr.push({ server_value_id: t.server_value_id, server_value_products_id: t.server_value_products_id, server_value_products_qty: t.server_value_products_qty })
                                    } else {
                                        t.is_default = 0
                                    }
                                }
                            }
                        })
                        server_attributes[item.option_id] = arr
                    })
                })
                this.server_attributes = server_attributes
                if (data) {
                    this.o_r_id = []
                    if (this.is_custom_server == 2) {
                        this.attributes.server.attributes.map((item) => {
                            item.category_option_values.map((t) => {
                                t.option_values.map((o) => {
                                    if (o.is_default) {
                                        this.o_r_id = this.o_r_id.concat(o.related_option_value_id)
                                    }
                                })
                            })
                        })
                    }
                    this.changeServerAttributes({ products: [{ isChecked: 1, products_id: this.products_id, qty: 1, server_attributes: server_attributes }], isRequest: data.value_id })
                }
            } else {
                if (this.customize_server.length) {
                    if (data.option_id && !data.value_id) {
                        this.customize_server = this.customize_server.filter((item) => {
                            return item.option_id != data.option_id
                        })
                    } else {
                        if (
                            this.customize_server.filter((item) => {
                                return item.option_id == data.option_id
                            }).length
                        ) {
                            this.customize_server.forEach((item) => {
                                if (item.option_id == data.option_id) {
                                    if (item.value_id != data.value_id) {
                                        item.value_id = data.value_id
                                        item.value_type = data.value_type
                                    }
                                }
                            })
                        } else {
                            this.customize_server.push(data)
                        }
                        this.updateAttributes(null, "cupboard")
                    }
                } else {
                    if (!(data.option_id && !data.value_id)) {
                        this.customize_server.push(data)
                        this.updateAttributes(null, "cupboard")
                    }
                }
            }
        },
        // 获取属性
        getAttr() {
            const result = []

            // 检查长度选项是否需要处理
            if (this.length && this.length.has_length) {
                // 如果有长度选项但没有选择或输入长度，执行lengthBlur方法
                if (!this.length.custom_length && !this.length.length_id) {
                    this.lengthBlur()
                    return
                }
            }

            if (this.attributes && this.attributes.custom_list && this.attributes.custom_list.length) {
                this.attributes.custom_list.forEach((option) => {
                    // 检查选项是否有选中的值
                    let hasSelectedValues = false
                    let selectedValues = []

                    if (option.options_type === 0) {
                        // 下拉选择
                        if (option.current_value && option.current_value !== "") {
                            hasSelectedValues = true
                            const selectedOption = option.options_values.find((val) => val.options_values_id === option.current_value)
                            if (selectedOption) {
                                selectedValues.push({
                                    options_values_id: selectedOption.options_values_id,
                                    options_values_name: selectedOption.options_values_name,
                                })
                            }
                        }
                    } else if (option.options_type === 1) {
                        // 文本输入
                        if (option.current_value && option.current_value !== "") {
                            hasSelectedValues = true
                            selectedValues.push({
                                options_values_id: 0, // 文本输入没有固定ID
                                options_values_name: option.current_value,
                            })
                        }
                    } else if (option.options_type === 3) {
                        // 复选框
                        if (option.current_value && option.current_value.length > 0) {
                            hasSelectedValues = true
                            option.current_value.forEach((valueId) => {
                                const selectedOption = option.options_values.find((val) => val.options_values_id === valueId)
                                if (selectedOption) {
                                    selectedValues.push({
                                        options_values_id: selectedOption.options_values_id,
                                        options_values_name: selectedOption.options_values_name,
                                    })
                                }
                            })
                        }
                    } else if (option.options_type === 4) {
                        // 单选框或文件
                        if (option.current_value && option.current_value !== "") {
                            hasSelectedValues = true
                            selectedValues.push({
                                options_values_id: 0, // 文件或单选可能没有固定ID
                                options_values_name: option.current_value,
                            })
                        }
                    }

                    // 如果有选中的值，添加到结果中
                    if (hasSelectedValues && selectedValues.length > 0) {
                        result.push({
                            options_id: option.options_id,
                            options_name: option.options_name,
                            options_values: selectedValues,
                        })
                    }
                })
            }

            // 如果有长度选择且已选择，添加到结果中
            if (this.length && this.length.has_length) {
                if (this.length.custom_length || this.length.length_id) {
                    let lengthValue = ""
                    let lengthId = 0

                    if (this.length.custom_length) {
                        lengthValue = `${parseFloat(this.length.custom_length)}${this.length.length_unit}`
                        lengthId = 0
                    } else if (this.length.length_id) {
                        lengthValue = this.length.length_id_name
                        lengthId = this.length.length_id
                    }

                    if (lengthValue) {
                        result.push({
                            options_id: 0, // 长度选项使用特殊ID 0
                            options_name: this.attributes.length?.title_name || "",
                            options_values: [
                                {
                                    options_values_id: lengthId,
                                    options_values_name: lengthValue,
                                },
                            ],
                        })
                    }
                }
            }

            console.log("选中的属性数据:", result)
            return result
        },
        // 处理来自父组件的消息
        handleMessage(event) {
            // 检查消息格式，支持新旧两种格式
            let messageType = ""
            let messageData = null

            if (typeof event.data === "string") {
                // 旧格式：直接是字符串
                messageType = event.data
            } else if (typeof event.data === "object" && event.data.type) {
                // 新格式：包含type和data的对象
                messageType = event.data.type
                messageData = event.data.data
            }

            if (messageType === "GET_ATTRIBUTES") {
                // 存储父组件传入的data
                if (messageData) {
                    this.parentData = messageData
                }

                // 先进行属性验证
                this.checkRequired(true)

                // 检查是否有验证错误
                if (this.add_cart_error || this.add_cart_error_dev) {
                    // 如果有验证错误，不执行 getAttr
                    return // 中断操作，不继续执行
                }

                // 验证通过，获取属性数据
                const result = this.getAttr()
                // 将结果发送回父组件，携带父组件传入的data
                window.parent.postMessage(
                    {
                        type: "amis:detail",
                        data: {
                            product: result,
                            // 携带父组件传入的data
                            ...(this.parentData && { parentData: this.parentData }),
                        },
                    },
                    "*"
                )
            }
        },
    },

    beforeDestroy() {
        // 清理事件监听器
        window.removeEventListener("message", this.handleMessage)
    },
}
</script>

<style lang="scss" scoped>
.product_attr_box {
    width: 100%;
    min-height: 300px;
    .attributes_wrap {
    }
    .no_attributes {
        @include font13;
        color: $textColor1;
        text-align: center;
    }
}
</style>
