<template>
    <div
        class="visualize-select"
        ref="visualizeSelect"
        @keyup.esc.stop="click_show = false"
        @keydown.tab.stop="handleAccessibleTab"
        @mouseover="triggerMouseover"
        @mouseleave="triggerMouseleave"
        :class="{ disabled: disabled }">
        <div class="visualize-select-active" tabindex="0" @keyup.enter.stop="toogleShow" @click="toogleShow" :class="{ 'visualize-select-active-border': show }">
            <div class="bdy">
                <img v-if="select_img" :src="select_img" />
                <div v-if="type === 'Color'" class="color" :class="select_color_name" :style="{ background: colors[select_color_name] }"></div>
                <div class="select-active-box" :class="{ 'select-active-placeholder': !select_name }" v-html="select_name ? select_name : placeholder"></div>
            </div>
            <span class="iconfont icofnont-down" :class="{ 'icofnont-down-up': show }">&#xe704;</span>
        </div>
        <slide-down>
            <div class="options-wrap" v-show="show && options && options.length" :class="[`options-wrap-${position}`, { scrollOver: overHidden }, search ? 'options-wrap-search' : 'options-wrap-normal']">
                <div class="inp-box" v-if="search">
                    <input type="text" class="inp" :placeholder="inpPlaceholder" v-model.trim="inp" @focus.stop="focus" @blur="blur" />
                    <span v-if="!focusValue && !inp" class="iconfont iconfont_search">&#xe694;</span>
                    <div v-else class="iconfont-clear" @click.stop="clearSearch()">
                        <span class="iconfont iconfont-close">&#xf30a;</span>
                    </div>
                </div>
                <ul class="options-box">
                    <li
                        class="item"
                        v-for="(item, index) in options"
                        @mouseenter="mouseMove(index)"
                        @mouseleave="mouseIndex = ''"
                        tabindex="0"
                        @keyup.enter.stop="click(item)"
                        :class="[mouseIndex === index ? 'options-box-bgc' : item.name === selectName || item === selectName ? 'options-box-bgc' : '']"
                        :key="item.value"
                        v-if="item.name ? item.name.toString().toLowerCase().indexOf(inp.toLowerCase()) !== -1 : item.toString().toLowerCase().indexOf(inp.toLowerCase()) !== -1"
                        @click.stop="click(item)">
                        <fs-popover :transfer="false" :icon="false" position="left" v-if="item.img_arr && item.img_arr.small && !isMobile">
                            <div slot="trigger">
                                <img class="list-view" :src="item.img_arr.small" @mouseenter="mouseImg(index)" />
                            </div>
                            <img class="mImg" v-if="isIndex == index" :src="item.img_arr.big" />
                        </fs-popover>
                        <div v-if="item.img_arr && item.img_arr.big && isMobile" class="m_pic_box" @click.stop>
                            <PhotoGallery galleryID="demo_gallery" :images="[item.img_arr.big]" :fullScreen="true" />
                        </div>
                        <div v-if="type === 'Color'" class="color" :class="item.options_values_name_en" :style="{ background: colors[item.options_values_name_en] }"></div>
                        <span class="list-name" v-html="item.name"></span>
                    </li>
                    <li v-if="filteredOptions.length === 0" class="item">{{ $c("pages.eos.content.noResult") }}</li>
                </ul>
            </div>
        </slide-down>
    </div>
</template>
<script>
/*
    选择组件 使用  value/v-model="value"  value为默认值  非必须  如果options为[{name: "aaa",value: "aaa"}]格式   value值取value字段
    @parmas options 下拉框数据  [Array]  必须   ["a","b","c"] 或者 [{name: "aaa",value: "aaa"}]
    @parmas position 定位 [String]     ('relative', 'absolute')
    @parmas trigger  触发方式 [String]     ('click', 'hover')   默认 click
    @parmas expand 是否展开   [Boolean] 
    @parmas search 是否显示搜索框   [Boolean]   
    @parmas placeholder 占位字段placeholder   [String]
    @parmas inpPlaceholder input占位字段placeholder   [String]
    @parmas clickclose 点击其他区域关闭     [Boolean]
    @params specialHeight [Boolean] 兼容解决苹果盒子两个滚动条的样式问题, 去除超过10条显示滚动条的计算属性 默认为true
  事件 
  @parmas change 监听select的change事件
    @parmas disabled [boolean]     是否可以更改选项  默认false 允许更改
*/
import FsPopover from "@/components/FsPopover"
import SlideDown from "@/components/SlideDown/SlideDown.vue"
import { isString, isObject, isNumber } from "@/util/types.js"
import { accessibleFocusout } from "@/util/accessible.js"
import colors from "@/constants/product_color.js"
import { mapState } from "vuex"
import PhotoGallery from "@/components/PhotoGallery/PhotoGallery.vue"

export default {
    name: "VisualizeSelect",
    props: {
        options: {
            type: Array,
            required: true,
            default: () => [],
        },
        value: {
            type: [String, Number],
        },
        placeholder: {
            type: String,
            default: "",
        },
        position: {
            type: String,
            default: "absolute",
        },
        trigger: {
            type: String,
            default: "click",
        },
        expand: {
            type: Boolean,
            default: false,
        },
        search: {
            type: Boolean,
            default: false,
        },
        inpPlaceholder: {
            type: String,
            default: "",
        },
        clickclose: {
            type: Boolean,
            default: true,
        },
        specialHeight: {
            type: Boolean,
            default: true,
        },
        disabled: {
            type: Boolean,
            default: false,
        },
        type: {
            type: String,
            default: "",
        },
    },
    components: {
        SlideDown,
        FsPopover,
        PhotoGallery,
    },
    data: () => ({
        click_show: false,
        active_show: false,
        options_show: false,
        inp: "",
        select_name: "",
        select_color_name: "",
        selectName: "",
        mouseIndex: "",
        select_img: "",
        isIndex: null,
        colors,
        focusValue: false,
    }),
    computed: {
        show() {
            return this.click_show || this.active_show || this.options_show
        },
        overHidden() {
            return this.specialHeight ? (this.options && this.options.length > 10) || (this.options && this.options.length > 10 && this.search) : false
        },
        filteredOptions() {
            if (!this.inp || !this.options) return this.options || []
            return this.options.filter((item) => {
                return item.name ? item.name.toString().toLowerCase().indexOf(this.inp.toLowerCase()) !== -1 : item.toString().toLowerCase().indexOf(this.inp.toLowerCase()) !== -1
            })
        },
        ...mapState({
            isMobile: (state) => state.device.isMobile,
        }),
    },
    created() {
        if (this.trigger === "click") {
            this.click_show = this.expand
        } else if (this.trigger === "hover") {
            this.active_show = this.expand
        }
        if (this.options && this.options.length) {
            if (this.value) {
                if (isObject(this.options[0])) {
                    let flag = false
                    this.options.map((item) => {
                        if (this.value === item.value) {
                            this.select_name = item.name
                            if (this.type === "Color") {
                                this.select_color_name = item.options_values_name_en
                            }
                            if (item.img_arr && item.img_arr.small) {
                                this.select_img = item.img_arr.small
                            }
                            flag = true
                        }
                    })
                    if (!flag) {
                        this.select_name = this.options[0].name || ""
                        if (this.type === "Color") {
                            this.select_color_name = this.options[0].options_values_name_en
                        }
                        if (this.options[0].img_arr && this.options[0].img_arr.small) {
                            this.select_img = this.options[0].img_arr.small
                        }
                        this.$emit("input", this.options[0].value)
                    }
                } else {
                    this.select_name = this.options[0].name || ""
                    if (this.type === "Color") {
                        this.select_color_name = this.options[0].options_values_name_en
                    }
                    if (this.options[0].img_arr && this.options[0].img_arr.small) {
                        this.select_img = this.options[0].img_arr.small
                    }
                    this.$emit("input", this.options[0].value)
                }
            } else {
                if (this.placeholder) {
                    return (this.select_name = "")
                }
                this.select_name = this.options[0].name || ""
                if (this.type === "Color") {
                    this.select_color_name = this.options[0].options_values_name_en
                }
                if (this.options[0].img_arr && this.options[0].img_arr.small) {
                    this.select_img = this.options[0].img_arr.small
                }
                this.$emit("input", this.options[0].value)
            }
        }
    },
    mounted() {
        if (this.trigger === "click" && typeof document !== "undefined") {
            document.addEventListener("click", this.handleOtherClick)
        }
        this.selectName = this.select_name
    },
    destroyed() {
        if (this.trigger === "click" && typeof document !== "undefined") {
            document.removeEventListener("click", this.handleOtherClick)
        }
    },
    methods: {
        mouseMove(index) {
            this.mouseIndex = index
            this.selectName = ""
        },
        triggerMouseover() {
            if (this.trigger === "hover") {
                this.active_show = true
                this.options_show = false
            }
        },
        triggerMouseleave() {
            if (this.trigger === "hover") {
                setTimeout(() => {
                    this.active_show = false
                }, 100)
            }
        },
        ctnMouseenter() {
            if (this.trigger === "hover") {
                this.active_show = false
                this.options_show = true
            }
        },
        ctnMouseleave() {
            if (this.trigger === "hover") {
                setTimeout(() => {
                    this.options_show = false
                }, 100)
            }
        },
        click(item) {
            this.click_show = false
            this.active_show = false
            this.options_show = false
            /*
             * 优化重复点击已选中值 取消提交事件
             */
            if (this.select_name === item.name || this.select_name === item) {
                return
            }
            if (item.img_arr) {
                this.select_img = item.img_arr.small
            }
            this.select_name = item.name
            if (this.type === "Color") {
                this.select_color_name = item.options_values_name_en
            }
            this.$emit("input", item.value)
            this.$emit("change", item.value)
            this.assignmentData()
        },
        toogleShow() {
            if (this.disabled) return false
            if (this.trigger === "click") {
                this.click_show = !this.click_show
            }
            this.$emit("toogleExpand", this.click_show)
            this.assignmentData()
        },
        handleOtherClick(e) {
            e.stopPropagation()
            if (this.clickclose && typeof document !== "undefined") {
                // 检查点击的元素是否在PhotoSwipe容器内
                const pswpElement = document.querySelector(".pswp")
                const pswpBackdrop = document.querySelector(".pswp__bg")

                // 如果点击的是PhotoSwipe相关元素，不关闭选项列表
                if ((pswpElement && pswpElement.contains(e.target)) || (pswpBackdrop && pswpBackdrop.contains(e.target)) || e.target.closest(".pswp")) {
                    return
                }

                if (!this.$refs.visualizeSelect.contains(e.target)) {
                    this.click_show = false
                    this.$nextTick(() => {
                        this.assignmentData()
                    })
                }
            }
        },
        assignmentData() {
            this.mouseIndex = ""
            this.selectName = this.select_name
        },
        mouseImg(index) {
            this.isIndex = index
        },
        handleAccessibleTab(event) {
            accessibleFocusout(event, this.click_show, () => {
                this.click_show = false
            })
        },
        focus() {
            this.focusValue = true
        },
        blur() {
            this.focusValue = false
        },
        clearSearch() {
            this.inp = ""
        },
    },
    watch: {
        value(n, o) {
            if (this.options && this.options.length) {
                if (n) {
                    if (isObject(this.options[0])) {
                        let flag = false
                        this.options.map((item) => {
                            if (n === item.value) {
                                this.select_name = item.name
                                if (this.type === "Color") {
                                    this.select_color_name = item.options_values_name_en
                                }
                                if (item.img_arr) {
                                    this.select_img = item.img_arr.small
                                }
                                flag = true
                            }
                        })
                        if (!flag) {
                            this.select_name = this.options[0].name || ""
                            this.$emit("input", this.options[0].value || "")
                        }
                    } else {
                        this.select_name = n
                    }
                } else {
                    this.select_name = this.options[0].name || ""
                    this.$emit("input", this.options[0].value)
                }
            }
        },
    },
}
</script>
<style lang="scss" scoped>
.visualize-select {
    width: 100%;
    position: relative;
    &.disabled .visualize-select-active {
        cursor: default;
        background-color: #f7f7f7;
    }
    .slide-down-leave-active,
    .slide-down-enter-active {
        transition: max-height 0.3s;
    }
    .slide-down-enter,
    .slide-down-leave-to {
        height: 0;
    }
    .slide-down-enter-to,
    .slide-down-leave {
        height: 227px;
    }

    .visualize-select-active {
        cursor: pointer;
        border: 1px solid #e5e5e5;
        position: relative;
        border-radius: 4px;
        padding: 0 8px;
        @include font13;
        color: $textColor1;
        width: 100%;
        height: 30px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        cursor: pointer;
        user-select: none;
        background: #fff;
        .bdy {
            display: flex;
            align-items: center;
            > img {
                margin-right: 8px;
                mix-blend-mode: multiply; // 正片叠底
            }
            .color {
                width: 14px;
                height: 14px;
                border-radius: 4px;
                margin-right: 8px;
                &.White {
                    border: 1px solid #e5e5e5;
                }
            }
        }
        .icofnont-down {
            transition: all 0.2s;
            color: $textColor3;
            font-size: 12px;
            &.icofnont-down-up {
                color: #19191a;
                transform: rotateX(-180deg);
            }
        }
        .select-active-box {
            display: flex;
            align-items: center;
        }
        .select-active-placeholder {
            color: #707070;
        }
        &:hover {
            background-color: #f2f2f2;
            .iconfont {
                color: #19191a;
            }
        }
    }
    .visualize-select-active-border {
        border: 1px solid $textColor1;
    }
    .options-wrap {
        width: 100%;
        background: #fff;
        border-radius: 0px 0px 4px 4px;
        border: solid 1px #e5e5e5;
        top: 4px;
        position: relative;
        // overflow-y: auto;
        &.options-wrap-relative {
            position: relative;
        }
        &.options-wrap-absolute {
            position: absolute;
            left: 0;
            border: 1px solid #e5e5e5;
            border-radius: 4px;
            top: 36px;
            z-index: 2;
            box-shadow: 0px 3px 6px -2px rgba(0, 0, 0, 0.1);
        }
        .inp-box {
            width: 100%;
            padding: 10px 8px;
            position: absolute;
            top: 0;
            left: 0;
            .inp {
                padding: 0 32px 0 12px;
                width: 100%;
                height: 32px;
                @include font13;
                color: $textColor3;
            }
            .iconfont_search {
                display: inline-block;
                width: 16px;
                height: 16px;
                font-size: 12px;
                position: absolute;
                left: 12px;
                top: 50%;
                transform: translate3d(0, -50%, 0);
                cursor: pointer;
                text-align: center;
                line-height: 16px;
                color: $textColor2;

                &:hover {
                    color: $textColor1;
                    transition: all 0.3s;
                }
            }
            .iconfont-clear {
                width: 28px;
                height: 28px;
                position: absolute;
                right: 7px;
                top: 50%;
                transform: translateY(-50%);
                display: flex;
                justify-content: center;
                align-items: center;
                cursor: pointer;
                .iconfont-close {
                    display: block;
                    width: 16px;
                    height: 16px;
                    line-height: 16px;
                    font-size: 16px;
                    text-align: center;
                    color: $textColor3;
                }
                &:hover {
                    background: rgba(25, 25, 26, 0.04);
                    border-radius: 3px;

                    .iconfont {
                        color: $textColor3;
                    }
                }
            }
        }
    }

    .options-wrap-search {
        max-height: 384px;
        &.scrollOver {
            overflow: hidden;
        }
        .inp-box {
            background-color: #fff;
        }
        .options-box {
            overflow: auto;
            max-height: 332px;
            padding-top: 52px;
        }
    }
    .options-wrap-normal {
        max-height: 370px;
    }
    .scrollOver {
        overflow-y: auto;
    }
    .options-box {
        margin: 4px auto;
        .item {
            display: flex;
            align-items: center;
            // height: 30px;
            @include font13;
            color: $textColor1;
            cursor: pointer;
            padding: 8px 12px;
            &:focus-visible {
                @include focusVisibleIn(2);
            }
            ::v-deep .fs-popover {
                margin-left: 0;
                .popper-computer {
                    padding: 12px;
                    cursor: default;
                }
                .popper-computer-left {
                    right: 14px !important;
                }
                .popper-computer-left::after {
                    width: 34px;
                    right: -26px;
                }
                .mImg {
                    width: 200px;
                    height: 200px;
                    display: block;
                }
                @media (max-width: 960px) {
                    display: none;
                }
            }
            .list-view {
                width: 24px;
                height: 24px;
                display: block;
                margin-right: 8px;
                mix-blend-mode: multiply;
            }
            &:hover {
                background: $bgColor1;
                // .list-name {
                // 	text-decoration: underline;
                // }
            }
            &:last-child {
                margin-bottom: 0;
            }
            .color {
                width: 14px;
                height: 14px;
                border-radius: 4px;
                margin-right: 8px;
                &.White {
                    border: 1px solid #e5e5e5;
                }
            }
            .m_pic_box {
                display: none;
                @media (max-width: 960px) {
                    width: 24px;
                    height: 24px;
                    margin-right: 8px;
                    display: flex;
                    position: relative;
                    mix-blend-mode: multiply;
                }
            }
        }
    }
    .options-box-bgc {
        background-color: #f7f7f7;
    }
}
</style>
