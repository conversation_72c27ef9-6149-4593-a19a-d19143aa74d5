<template>
    <div class="warranty_finder" v-loading.fullscreen="loading">
        <div class="banner">
            <p>
                <b>{{ $c("pages.spareOrder.sparePartRequest") }}</b>
            </p>
        </div>
        <div class="main_page">
            <div class="slide">
                <h3>{{ $c("single.ProductReturnForm.left_content.title") }}</h3>
                <ul>
                    <li v-for="(item, index) in this.$c('single.SparePartsService.after_sales_policy.list')" :key="index">
                        <h4>{{ item.title }}</h4>
                        <p>{{ item.name }}</p>
                    </li>
                </ul>
            </div>
            <div class="main">
                <h3>{{ $c("single.ProductReturnForm.orderInformation") }}</h3>
                <div class="form">
                    <div class="form_item form_item_input">
                        <div>
                            <label>{{ $c("pages.Checkout.orderNumber") }}</label>
                            <div class="input_item">
                                <input type="text" class="is_new" v-model="searchOrderNumber" @blur="validate('orderNumber')" />
                                <span class="iconfont iconfont-search" @click="handlerSearchOrder" v-show="searchOrderNumber.length">&#xe694;</span>
                            </div>
                            <!-- <validate-error :error="errors.orderNumber"></validate-error> -->
                        </div>
                        <div>
                            <label>{{ $c("single.SparePartsService.after_sales_policy.selectRMANumber") }}</label>
                            <fs-select :isNewStyle="true" :options="rmaList" v-model="formData.rmaNumber" @change="handleRMAChange" :disabled="!rmaList.length"></fs-select>
                        </div>
                    </div>
                    <div class="product_list" v-if="productList.length">
                        <label>{{ $c("pages.Products.installationPopup.SelectProduct") }}</label>
                        <ul>
                            <li v-for="p in productList" :key="p.products_id" class="product_list_item">
                                <span class="iconfont" @click="handleProductSelect(p.products_id)">{{ p.hasSelect ? "&#xf186;" : "&#xf043;" }}</span>
                                <img :src="p.product_image" alt="" />
                                <div class="product_des">
                                    <div>
                                        <p>{{ p.products_name }}</p>
                                        <span class="product_id"># {{ p.products_id }}</span>
                                    </div>
                                    <qty-box :num="p.products_num" @change="(val) => handleNumChange(p.products_id, val)" :max="p.spare_part_total"></qty-box>
                                </div>
                            </li>
                        </ul>
                    </div>
                    <div class="form_item content_box">
                        <div class="tit-num">
                            <label>{{ $c("single.SparePartsService.after_sales_policy.form.content") }}</label>
                            <p class="num">{{ formData.content.length }}/5000</p>
                        </div>
                        <textarea class="is_new" v-model="formData.content" maxlength="5000" @blur="validate('content')"></textarea>
                        <validate-error :error="errors.content"></validate-error>
                    </div>
                    <upload-file :isNewStyle="true" @change="uploadFile" ref="uploadFile" type="file" :accept="upload.accept" :text="upload.text" :multiple="true" :limit="5" :maxSize="5 * 1024 * 1024">
                        <fs-popover position="right" slot="tip">
                            <div class="tipFont">
                                <p>{{ $c("pages.answerQuestion.upload.accept") }}</p>
                                <p>{{ $c("pages.answerQuestion.upload.max") }}</p>
                            </div>
                        </fs-popover>
                    </upload-file>
                    <div class="contact_detail">
                        <div class="form">
                            <div class="form_item">
                                <label>{{ $c("form.form.first_name") }}</label>
                                <input type="text" class="is_new" v-model="formData.firstName" @blur="validate('firstName')" />
                                <validate-error :error="errors.firstName"></validate-error>
                            </div>
                            <div class="form_item">
                                <label>{{ $c("form.form.last_name") }}</label>
                                <input type="text" class="is_new" v-model="formData.lastName" @blur="validate('lastName')" />
                                <validate-error :error="errors.lastName"></validate-error>
                            </div>
                            <div class="form_item">
                                <label>{{ $c("form.form.email_ddress") }}</label>
                                <input type="text" class="is_new" v-model="formData.email" @blur="validate('email')" />
                                <validate-error :error="errors.email"></validate-error>
                            </div>
                            <div class="form_item">
                                <label>{{ $c("pages.CaseDetail.detailsPage.emailUs.phoneNumber") }}</label>
                                <tel-code :isNewStyle="true" :telPrefix="formData.telPrefix" v-model="formData.phoneNumber" @change="validate('phoneNumber')"></tel-code>
                                <validate-error :error="errors.phoneNumber"></validate-error>
                            </div>
                            <div class="form_item">
                                <label>{{ $c("single.ProductReturnForm.address.tit") }}</label>
                                <input type="text" class="is_new" v-model="formData.addressLine1" @blur="validate('addressLine1')" />
                                <validate-error :error="errors.addressLine1"></validate-error>
                            </div>
                            <div class="form_item">
                                <label>{{ $c("single.SparePartsService.after_sales_policy.form.addressLine2") }}</label>
                                <input type="text" class="is_new" v-model="formData.addressLine2" @blur="validate('addressLine2')" />
                                <validate-error :error="errors.addressLine2"></validate-error>
                            </div>
                            <div class="form_item">
                                <label>{{ $c("single.ProductReturnForm.zipCode") }}</label>
                                <input type="text" class="is_new" v-model="formData.postCode" @blur="validate('postCode')" />
                                <validate-error :error="errors.postCode"></validate-error>
                            </div>
                            <div class="form_item">
                                <label>{{ $c("single.ProductReturnForm.city.tit") }}</label>
                                <fs-select :isNewStyle="true" v-if="isShowCitySelect" :showDefaultValue="true" :options="city_list" v-model="formData.city" @change="validate('city')"></fs-select>
                                <input v-else type="text" class="is_new" v-model="formData.city" @blur="validate('city')" />
                                <validate-error :error="errors.city"></validate-error>
                            </div>
                            <div class="form_item">
                                <label>{{ $c("single.SparePartsService.after_sales_policy.form.country") }}</label>
                                <!-- <fs-select :search="true" :options="country_list" v-model="formData.country" @change="countryChange"></fs-select> -->
                                <select-country :isNewStyle="true" v-model="formData.country" @change="countryChange"></select-country>
                            </div>
                            <div class="form_item" v-if="isShowState">
                                <label>{{ $c("single.SparePartsService.after_sales_policy.form.state") }}</label>
                                <!-- <fs-select :options="stateOptions" :disabled="!formData.state" v-model="formData.state"></fs-select> -->
                                <RegionSelect :isNewStyle="true" ref="regionSelect" />
                            </div>
                        </div>
                    </div>
                    <ul class="plicy">
                        <li>
                            <label class="policy_item" @click="handlePolicyChange('agreement')" :class="{ active: formData.agreement }">
                                <i class="iconfont">{{ formData["agreement"] ? "&#xf186;" : "&#xf043;" }}</i
                                ><span @click.prevent="handleAgreement" v-html="policies.text"></span>
                            </label>
                            <validate-error :error="errors.agreement"></validate-error>
                        </li>
                        <li>
                            <PolicyCheck v-model="formData.privacy" @change="validate('privacy')" :error="errors.privacy"></PolicyCheck>
                        </li>
                    </ul>
                    <fs-button @click="handleSubmit" :disabled="!hasSelectProduct.length">{{ $c("common.basic.submit") }}</fs-button>
                </div>
            </div>
        </div>
        <fs-popup-tip :show="showSuccessTip" :clickHide="false" @change="closeSuccessTip">
            <div class="success_tip">
                <span class="iconfont">&#xf060;</span>
                <p v-html="successInfo"></p>
            </div>
        </fs-popup-tip>
        <fs-popup class="agreement" :show="agreementShow" :title="$c('single.ProductReturnForm.agree_title')" @close="closeAgreement">
            <div class="agreement-content">
                <ul>
                    <li>
                        <h2>{{ $c("single.ProductReturnForm.sparePopup[0].tit") }}</h2>
                        <p>{{ $c("single.ProductReturnForm.sparePopup[0].txt") }}</p>
                    </li>
                    <li>
                        <h2>{{ $c("single.ProductReturnForm.sparePopup[1].tit") }}</h2>
                        <p>{{ $c("single.ProductReturnForm.sparePopup[1].txt") }}</p>
                    </li>
                </ul>
            </div>
        </fs-popup>
    </div>
</template>

<script>
import FsSelect from "@/components/FsSelect/FsSelect.vue"
import QtyBox from "@/components/QtyBox/QtyBox.vue"
import UploadFile from "@/components/UploadFile/UploadFile.vue"
import FsPopover from "@/components/FsPopover/"
import TelCode from "@/components/TelCode/TelCode.vue"
import FsButton from "@/components/FsButton/FsButton.vue"
import FsPopupTip from "@/components/FsPopupTip/FsPopupTip.vue"
import FsPopup from "@/components/FsPopup/FsPopup"
import ValidateError from "@/components/ValidateError/ValidateError.vue"
import SelectCountry from "@/components/SelectCountry/SelectCountry.vue"
import RegionSelect from "@/components/RegionSelect/RegionSelect"
import PolicyCheck from "@/components/PolicyCheck/PolicyCheck.vue"

import { mapState, mapGetters } from "vuex"
export default {
    name: "sparePartRequest",
    layout: "default",
    components: {
        FsSelect,
        QtyBox,
        UploadFile,
        FsPopover,
        TelCode,
        FsButton,
        FsPopupTip,
        ValidateError,
        FsPopup,
        SelectCountry,
        RegionSelect,
        PolicyCheck,
    },
    data() {
        return {
            loading: false,
            upload: {
                accept: ".pdf,.jpg,.png,.doc,.docx,.xls,.xlsx,.txt",
                text: this.$c("pages.answerQuestion.upload.txt"),
            },
            searchOrderNumber: "",
            agreementShow: false,
            showSuccessTip: false,
            successInfo: "",
            formData: {
                orderNumber: "",
                rmaNumber: "",
                productList: [],
                content: "",
                firstName: "",
                lastName: "",
                email: "",
                phoneNumber: "",
                telPrefix: "",
                addressLine1: "",
                addressLine2: "",
                postCode: "",
                city: "",
                country: "",
                countryName: "",
                state: "",
                stateName: "",
                agreement: false,
                privacy: false,
            },
            errors: {
                // orderNumber: "",
                content: "",
                firstName: "",
                lastName: "",
                email: "",
                phoneNumber: "",
                addressLine1: "",
                addressLine2: "",
                postCode: "",
                city: "",
                agreement: "",
                privacy: "",
            },
            policies: { text: this.$c("pages.spareOrder.policy.read_and_understood").replace("XXXXX", this.$c("single.ProductReturnForm.agree_title")).replace("onClick='sparePopup()'", ""), key: "agreement" },
            files: [],
            stateOptions: [],
            rmaList: [],
            productList: [],
        }
    },
    computed: {
        ...mapState({
            country_list: (state) => {
                let resultArr = []
                if (state.selectCountry.country_list && state.selectCountry.country_list.length) {
                    state.selectCountry.country_list.forEach((item) => {
                        resultArr.push({ name: item.countries_name, value: item.countries_id, country: item })
                    })
                }
                return resultArr
            },
            countries_id: (state) => state.webSiteInfo.countries_id,
            city_list: (state) => state.selectCountry.city_list,
        }),
        ...mapGetters({
            isShowState: "selectCountry/isShowState",
            isShowCitySelect: "selectCountry/isShowCitySelect",
        }),
        hasSelectProduct() {
            let res = []
            if (this.productList.length) {
                this.productList.map((i) => {
                    if (i.hasSelect) {
                        console.log(i, "@@@@")
                        const { products_id, products_num } = i
                        res.push({ product_id: products_id, product_num: products_num })
                    }
                })
            }
            return res
        },
    },
    methods: {
        closeAgreement() {
            this.agreementShow = false
        },
        handleAgreement({ target }) {
            console.dir(target, "handleAgreementhandleAgreement")
            if (target.tagName === "A") {
                this.agreementShow = true
            }
        },
        uploadFile({ error, files }) {
            if (!error) {
                this.files = files
            }
        },
        closeSuccessTip(val) {
            console.log("closeSuccessTip")
            this.showSuccessTip = false
        },
        countryChange(val) {
            this.formData.countryName = val.countries_name || ""
            this.formData.telPrefix = val.tel_prefix || "+1"
            // const [{ country }] = this.country_list.filter((i) => i.value === val)
            // if (!country) return
            // if (country.states && country.states.length > 0) {
            //     this.stateOptions = country.states.map(({ states: name, states_code: value }) => ({ name, value }))
            // } else {
            //     this.stateOptions = [{ name: this.$c("pages.NetTermsApplication.noSelect"), value: "0" }]
            // }
            // this.formData.countryName = country.countries_name || ""
            // this.formData.telPrefix = country.tel_prefix || "+1"
            // this.$nextTick(() => {
            //     this.formData.state = this.stateOptions[0].value
            //     this.formData.stateName = this.stateOptions[0].value === "0" ? "" : this.stateOptions[0].name
            // })
        },
        handleProductSelect(val) {
            this.productList.forEach((i) => {
                if (i.products_id === val) {
                    i.hasSelect = !i.hasSelect
                }
            })
        },
        handlePolicyChange(key) {
            if (["agreement", "privacy"].includes(key)) {
                this.formData[key] = !this.formData[key]
            }
            this.validate(key)
        },
        handleRMAChange(val) {
            this.formData.rmaNumber = val
            const [{ products = [], orders_number = "" }] = this.rmaList.filter((i) => i.value === val)
            this.productList = products.map((i) => ({ hasSelect: false, ...i }))
            this.formData.orderNumber = orders_number
        },
        handleNumChange(id, val) {
            this.productList.forEach((i) => {
                if (i.product_id === id) {
                    i.product_num = val
                }
            })
        },
        validate(target) {
            const value = this.formData[target]
            // 判断是否需要验证
            const needValidate = Object.keys(this.errors).includes(target)
            if (needValidate) {
                this.errors[target] = this.getValidateResult(value, target)
            }
        },
        getValidateResult(value = "", key) {
            let errStr = ""
            if (key === "orderNumber") {
                if (!value) {
                    errStr = this.$c("pages.NetTermsApplication.validate.phone.phone_required")
                }
                return errStr
            }
            if (key === "content") {
                if (!value) {
                    errStr = this.$c("pages.NetTermsApplication.validate.phone.phone_required")
                }
                return errStr
            }
            if (key === "firstName") {
                if (value.length < 1) {
                    errStr = this.$c("pages.NetTermsApplication.validate.first_name.first_name_required")
                } else if (value.length > 40) {
                    errStr = this.$c("pages.NetTermsApplication.validate.first_name.first_name_max")
                }
                return errStr
            }
            if (key === "lastName") {
                if (value.length < 1) {
                    errStr = this.$c("pages.NetTermsApplication.validate.last_name.last_name_required")
                } else if (value.length > 40) {
                    errStr = this.$c("pages.NetTermsApplication.validate.last_name.last_name_max")
                }
                return errStr
            }
            if (key === "email") {
                if (!value) {
                    errStr = this.$c("pages.NetTermsApplication.validate.title.title_require")
                }
                return errStr
            }
            if (key === "phoneNumber") {
                if (value.length < 1) {
                    errStr = this.$c("pages.NetTermsApplication.validate.phone.phone_required")
                } else if (value.length > 0 && value.length < 6) {
                    errStr = this.$c("pages.NetTermsApplication.validate.phone.phone_min")
                } else if (value.length > 40) {
                    errStr = this.$c("pages.NetTermsApplication.validate.phone.phone_validate")
                } else {
                    errStr = ""
                }
                return errStr
            }
            if (key === "addressLine1") {
                if (value.length < 1) {
                    errStr = this.$c("pages.NetTermsApplication.validate.address.address_required")
                } else if (value.length < 4 || value.length > 35) {
                    errStr = this.$c("pages.NetTermsApplication.validate.address.address_validate1")
                }
                return errStr
            }
            if (key === "addressLine2") {
                if (value.length < 4 || value.length > 35) {
                    errStr = this.$c("pages.NetTermsApplication.validate.address.address_validate1")
                }
                return errStr
            }
            if (key === "postCode") {
                if (value.length < 1) {
                    errStr = this.$c("pages.NetTermsApplication.validate.zip_code.zip_code_required")
                } else if (value.length > 10) {
                    errStr = this.$c("form.form.zipCodeMax")
                }
                return errStr
            }
            if (key === "city") {
                if (value.length < 2) {
                    errStr = this.$c("pages.NetTermsApplication.validate.city.city_required")
                } else if (value.length > 40) {
                    errStr = this.$c("pages.NetTermsApplication.validate.city.city_validate")
                }
                return errStr
            }
            if (key === "privacy") {
                if (!value) {
                    errStr = this.$c("pages.NetTermsApplication.validate.policys.agreePolicy")
                }
                return errStr
            }
            if (key === "agreement") {
                if (!value) {
                    errStr = this.$c("pages.NetTermsApplication.validate.policys.policys_required")
                }
                return errStr
            }
            return errStr
        },
        handlerSearchOrder() {
            this.loading = true
            const orders_number = this.searchOrderNumber
            this.$axios
                .get("/api/account/spare_part_rma_list", { params: { orders_number } })
                .then((res) => {
                    if (res.code === 200 && res.data) {
                        if (res.data.length) {
                            this.rmaList = res.data?.map((i) => {
                                return {
                                    name: `#${i.service_number}`,
                                    value: i.service_number,
                                    ...i,
                                }
                            })
                            this.handleRMAChange(this.rmaList[0].value)
                        }
                    }
                })
                .catch((err) => {
                    console.log(err, "@@@@")
                })
                .finally(() => {
                    this.loading = false
                })
        },
        validateAll() {
            const arr = Object.keys(this.errors)
            arr.forEach((k) => {
                this.validate(k)
            })
            this.validate("agreement")
            this.validate("privacy")
            const result = Object.values(this.errors).every((i) => !i)
            return result
        },
        handleSubmit() {
            // 校验所有的输入项
            const res = this.validateAll()
            if (res) {
                this.loading = true
                const params = new FormData()
                if (this.files && this.files.length > 0) {
                    this.files.forEach((i) => {
                        params.append("reviews_img[]", i)
                    })
                }
                params.append("source_from", "/spare_part_request.html")
                params.append("order_number", this.formData.orderNumber)
                params.append("products_info", JSON.stringify(this.hasSelectProduct))
                params.append("content", this.formData.content)
                params.append("service_number", this.formData.rmaNumber)
                const address = {
                    company_type: "",
                    entry_suburb: "",
                    entry_country_id: this.formData.country,
                    entry_country_name: this.formData.countryName,
                    entry_firstname: this.formData.firstName,
                    entry_lastname: this.formData.firstName,
                    entry_city: this.formData.city,
                    entry_company: "",
                    entry_address1: this.formData.addressLine1,
                    entry_address2: this.formData.addressLine1,
                    entry_postcode: this.formData.postCode,
                    entry_state_name: this.$refs?.regionSelect.stateName || "",
                    entry_state_id: this.$refs?.regionSelect.state || "",
                    entry_telephone: this.formData.phoneNumber,
                }
                params.append("address", JSON.stringify(address))
                this.$axios
                    .post("/api/spare_part", params)
                    .then((res) => {
                        const {
                            code,
                            data: { info, success },
                        } = res
                        if (code === 200 && success) {
                            this.successInfo = info
                            this.showSuccessTip = true
                        } else {
                            this.$message.error(info)
                        }
                    })
                    .catch((err) => {
                        console.log(err, "err")
                    })
                    .finally(() => {
                        this.loading = false
                    })
            }
        },
    },
    watch: {
        countries_id: {
            handler(val) {
                if (val) {
                    this.formData.country = val
                }
            },
        },
        isShowCitySelect: {
            handler(val) {
                if (this.formData.city) {
                    this.formData.city = ""
                }
            },
        },
    },
    asyncData({}) {},
}
</script>

<style lang="scss" scoped>
.warranty_finder {
    color: $textColor1;
    .banner {
        width: 100%;
        height: 200px;
        max-width: 1920px;
        margin: 0 auto;
        background-color: #ccc;
        background-image: url("https://resource.fs.com/mall/generalImg/20231121113514xwiohr.png");
        background-position: center;
        background-repeat: no-repeat;
        display: flex;
        align-items: center;
        justify-content: center;
        background-size: cover;

        > p {
            color: #fff;
            @include font34;
            text-align: center;
        }
    }
    .main_page {
        display: flex;
        justify-content: center;
        column-gap: 36px;
        padding: 36px 0 48px;
        background-color: #fafafa;
        .slide {
            flex: 0 0 322px;
            padding-top: 36px;
            @include font12;
            h3 {
                @include font16;
                margin-bottom: 24px;
            }
            > ul {
                li {
                    position: relative;
                    padding-left: 12px;
                    color: #707070;
                    &::before {
                        content: "";
                        width: 4px;
                        height: 4px;
                        border-radius: 50%;
                        background-color: #707070;
                        position: absolute;
                        top: 6px;
                        left: 0;
                    }
                    h4 {
                        margin-bottom: 4px;
                        color: $textColor1;
                    }
                    &:not(:last-of-type) {
                        margin-bottom: 20px;
                    }
                }
            }
        }
        .main {
            flex: 1;
            max-width: 842px;
            background-color: #fff;
            padding: 40px;
            > h3 {
                @include font20;
            }
            .form {
                margin-top: 12px;
                .form_item {
                    margin-bottom: 16px;
                    .tit-num {
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                        .num {
                            @include font12;
                            color: #797979;
                        }
                    }
                    label {
                        display: block;
                        @include font12;
                        color: $textColor3;
                        margin-bottom: 4px;
                    }
                    &.content_box {
                        margin-bottom: 12px;
                    }
                }
                .form_item_input {
                    display: grid;
                    grid-template-columns: repeat(2, 1fr);
                    column-gap: 20px;
                }
                .product_list {
                    margin-bottom: 16px;
                    > label {
                        @include font12;
                        margin-bottom: 8px;
                    }
                    > ul {
                        display: flex;
                        flex-direction: column;
                        row-gap: 8px;
                    }
                    .product_list_item {
                        display: grid;
                        grid-template-columns: 18px 80px auto;
                        column-gap: 20px;
                        align-items: center;
                        > .iconfont {
                            cursor: pointer;
                        }
                        img {
                            width: 80px;
                            height: 80px;
                        }
                        .product_des {
                            display: flex;
                            column-gap: 20px;
                            justify-content: space-between;
                            @include font14;
                            .product_id {
                                @include font13;
                                color: #707070;
                                margin-top: 8px;
                            }
                        }
                    }
                }
            }
            .contact_detail {
                margin-top: 20px;
                .form {
                    display: grid;
                    grid-template-columns: repeat(2, 1fr);
                    column-gap: 20px;
                }
            }
            .plicy {
                display: flex;
                flex-direction: column;
                // row-gap: 8px;
                margin-bottom: 20px;
                .policy_item {
                    display: flex;
                    align-items: center;
                    column-gap: 8px;
                    @include font12;
                    color: $textColor3;
                    cursor: pointer;
                    .iconfont {
                        font-size: 14px;
                        width: 14px;
                        height: 14px;
                        display: inline-flex;
                        align-items: center;
                        color: rgba(25, 25, 26, 0.3);
                        cursor: pointer;
                    }
                    &.active {
                        .iconfont {
                            color: #4b4b4d;
                        }
                    }
                    &:hover {
                        .iconfont {
                            color: #707070;
                        }
                    }
                    @media (max-width: 414px) {
                        align-items: baseline;
                    }
                }
            }

            .input_item {
                position: relative;
                .iconfont {
                    position: absolute;
                    top: 50%;
                    transform: translateY(-50%);
                    right: 12px;
                    cursor: pointer;
                    color: $textColor3;
                    &:hover {
                        color: $textColor1;
                    }
                }
                input {
                    padding-right: 40px;
                }
            }
        }
    }
}
::v-deep {
    .fs-popup-tip {
        .content {
            padding: 24px 24px 32px 40px;
        }
    }
}
.success_tip {
    padding: 8px 54px 0 0;
    display: flex;
    column-gap: 12px;
    @include font16;
    .iconfont {
        color: #10a300;
    }
}
.agreement {
    &::v-deep .fs-popup-ctn {
        .fs-popup-header {
            padding-top: 20px;
            padding-bottom: 20px;
        }
        .fs-popup-body {
            .agreement-content {
                padding: 40px;
                h2 {
                    @include font14;
                    font-weight: 600;
                    color: $textColor1;
                    margin-bottom: 8px;
                }
                p {
                    @include font14;
                    color: $textColor2;
                }
                ul {
                    li {
                        &:last-child {
                            margin-top: 20px;
                        }
                    }
                }
            }
        }
    }
}
@media (max-width: 1920px) {
    .warranty_finder {
        .banner {
            background-image: url("https://resource.fs.com/mall/generalImg/202311211135216hqjon.png");
        }
    }
}

@media (max-width: 1024px) {
    .warranty_finder {
        .banner {
            background-image: url("https://resource.fs.com/mall/generalImg/20231121113528g6z42r.png");
        }
        .main_page {
            padding: 36px 24px 48px;
            .qty-box {
                flex: 0 0 auto;
            }
        }
    }
}

@media (max-width: 769px) {
    .warranty_finder {
        .banner {
            width: 100vw;
            height: 233px;
            background-image: url("https://resource.fs.com/mall/generalImg/202311211135337sog8c.png");

            > p {
                @include font24;
            }
        }
        .main_page {
            flex-direction: column-reverse;
            padding: 0;
            background-color: #fff;
            .slide {
                padding: 36px 16px;
                flex: 0 0 auto;
            }
            .main {
                padding: 36px 16px 0;
                .form {
                    .form_item_input {
                        grid-template-columns: repeat(1, 1fr);
                        row-gap: 16px;
                    }
                    .product_list {
                        .product_list_item {
                            .product_des {
                                flex-direction: column;
                                row-gap: 12px;
                            }
                        }
                    }
                }
                .contact_detail {
                    .form {
                        grid-template-columns: repeat(1, 1fr);
                    }
                }
                .fs-button {
                    width: 100%;
                }
            }
        }
    }
    ::v-deep {
        .fs-popup-tip {
            .content {
                padding: 0;
            }
            .iconfont_close {
                display: block;
                top: 20px;
                right: 20px;
            }
            .body {
                padding: 0;
            }
        }
    }
    .success_tip {
        padding: 20px;
        flex-direction: column;
        row-gap: 12px;
        text-align: center;
        .iconfont {
            font-size: 32px;
            line-height: 32px;
        }
    }
}
</style>
