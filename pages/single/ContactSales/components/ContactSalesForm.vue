<template>
    <form class="lt_wrap" @submit.prevent="submitFu">
        <div class="support_alone_txt">
            <template v-if="isFromQualityControl">
                <div role="heading" aria-level="heading">{{ $c("single.QualityControl.compliance.formTitle") }}</div>
                <p>{{ $c("single.QualityControl.compliance.formDesc") }}</p>
            </template>
            <template v-else>
                <div role="heading" aria-level="heading">{{ $c("single.ContactSales.LetUs") }}</div>
                <p v-if="website === 'sg'" @click="buriedLink($event)">
                    Complete the form below and our exclusive business advisor will get back to you soon. Or chat with sales via <a href="https://wa.me/6564437951" target="_blank">WhatsApp</a>.
                </p>
                <p v-else>{{ $c("single.ContactSales.LetUsTxt") }}</p>
            </template>
        </div>
        <div class="inpbox" :class="{ inpbox_cn: ['cn', 'hk', 'tw', 'mo'].includes(website) }">
            <div class="lt">
                <p class="txt">{{ $c("form.form.first_name") }}{{ website === "jp" ? "（必須）" : " *" }}</p>
                <input
                    aria-label="firstname"
                    v-model.trim="form.entry_firstname"
                    :class="{ error_input: errors.entry_firstname_error }"
                    :placeholder="countries_id === 107 ? $c('form.form.jpFormPlaceHolder.firstName') : ''"
                    @focus="focusInput('entry_firstname', 'First Name')"
                    @blur="blurInput('entry_firstname')"
                    @input="blurInput('entry_firstname')"
                    class="inp is_new"
                    type="text" />
                <validate-error :error="errors.entry_firstname_error"></validate-error>
            </div>
            <div class="rt">
                <p class="txt">{{ $c("form.form.last_name") }}{{ website === "jp" ? "（必須）" : " *" }}</p>
                <input
                    aria-label="lastname"
                    v-model.trim="form.entry_lastname"
                    :class="{ error_input: errors.entry_lastname_error }"
                    :placeholder="countries_id === 107 ? $c('form.form.jpFormPlaceHolder.lastName') : ''"
                    @focus="focusInput('entry_lastname', 'Last Name')"
                    class="inp is_new"
                    type="text"
                    @blur="blurInput('entry_lastname')"
                    @input="blurInput('entry_lastname')" />
                <validate-error :error="errors.entry_lastname_error"></validate-error>
            </div>
        </div>
        <div class="inpbox01" v-if="isCn">
            <p class="txt">{{ $c("form.form.phone_business") }}{{ " *" }}</p>
            <input
                v-model.trim="form.entry_telephone"
                :class="{ error_input: errors.entry_telephone_error }"
                @focus="focusInput('entry_telephone', 'Entry Telephone')"
                class="inp is_new"
                type="text"
                @blur="telChange(form.entry_telephone)" />
            <validate-error v-if="!isSg" :error="errors.entry_telephone_error.replace('XXXX', localePath({ name: 'login', query: { redirect: $route.fullPath } }))"></validate-error>
        </div>
        <div class="inpbox">
            <div class="lt">
                <p class="txt">{{ isCn ? "邮箱" : isSg ? "Email address" : $c("single.ContactSales.BusinessEmail") }}{{ website === "jp" ? "（必須）" : website === "cn" ? "" : " *" }}</p>
                <div class="emailInput">
                    <input
                        aria-label="email_address"
                        v-model.trim="form.email_address"
                        :class="{ error_input: errors.email_address_error }"
                        :placeholder="countries_id === 107 ? $c('form.form.jpFormPlaceHolder.email') : ''"
                        @focus="focusInput('email_address', 'Business Email')"
                        class="inp is_new"
                        type="text"
                        @blur="blurInput('email_address')"
                        @input="blurInput('email_address')" />
                    <div class="fsSelectSearch__box" @click.stop="clearSearch" v-show="form.email_address">
                        <span class="iconfont iconfont-clear">&#xf30a;</span>
                    </div>
                </div>

                <validate-error :error="errors.email_address_error.replace('XXXX', localePath({ name: 'login', query: { redirect: $route.fullPath } }))"></validate-error>
            </div>
            <div class="rt">
                <p class="txt">{{ $c("form.form.company_name") }}{{ website === "jp" ? "（必須）" : "*" }}</p>
                <input
                    v-model.trim="form.company_name"
                    :class="{ error_input: errors.company_name_error }"
                    @blur="blurInput('company_name')"
                    @focus="focusInput('company_name', 'Company Name')"
                    class="inp is_new"
                    type="text"
                    :placeholder="$c('form.form.jpFormPlaceHolder.company')" />
                <validate-error :error="errors.company_name_error"></validate-error>
            </div>
        </div>

        <!-- <div class="inpbox01" v-if="!isSg">
            <p class="txt">
                {{ $c("form.form.industry") }}<span v-show="countries_id === 107">{{ website === "jp" ? "（必須）" : "*" }}</span>
            </p>
            <fs-select :options="industryList" v-model="form.industry" :placeholder="$c('single.ContactSales.selectSize')" @change="focusInput('industry', 'Industry')"></fs-select>
            <validate-error :error="errors.industry_error"></validate-error>
        </div>
        <div class="inpbox01" v-if="!isSg">
            <p class="txt">
                {{ $c("form.form.company_size") }}<span v-show="countries_id === 107">{{ website === "jp" ? "（必須）" : "*" }}</span>
            </p>
            <fs-select :options="sizeList" v-model="form.company_size" :placeholder="$c('single.ContactSales.selectSize')" @change="focusInput('company_size', 'Company Size')"></fs-select>
            <validate-error :error="errors.company_size_error"></validate-error>
        </div>
        <div class="inpbox01" v-if="!isSg">
            <p class="txt">
                {{ $c("single.ContactSales.JobTitle") }}<span v-show="countries_id === 107">{{ website === "jp" ? "（必須）" : "*" }}</span>
            </p>
            <input
                v-model.trim="form.job_title"
                :class="{ error_input: errors.job_title_error }"
                @blur="blurInput('job_title')"
                @focus="focusInput('job_title', 'Job Title')"
                class="inp is_new"
                type="text"
                :placeholder="countries_id === 107 ? $c('form.form.jpFormPlaceHolder.position') : ''" />
            <validate-error :error="errors.job_title_error"></validate-error>
        </div>
        <div class="inpbox01" v-if="!isSg">
            <p class="txt">
                {{ $c("form.form.company_name") }}<span v-show="countries_id === 107">{{ website === "jp" ? "（必須）" : "*" }}</span>
            </p>
            <input
                v-model.trim="form.company_name"
                :class="{ error_input: errors.company_name_error }"
                @blur="blurInput('company_name')"
                @focus="focusInput('company_name', 'Company Name')"
                class="inp is_new"
                type="text"
                :placeholder="countries_id === 107 ? $c('form.form.jpFormPlaceHolder.company') : ''" />
            <validate-error :error="errors.company_name_error"></validate-error>
        </div> -->
        <div class="inpbox01 last_inpbox">
            <div class="input-item-flex">
                <p class="txt">{{ website === "sg" ? "Details" : $c("form.form.comment") }}{{ website === "jp" ? "（必須）" : " *" }}</p>
                <span class="textarea-num">
                    <em :class="{ active: form.comments.length === 5000 }">{{ form.comments.length }}</em
                    >/5000
                </span>
            </div>
            <textarea
                aria-label="comments"
                v-model.trim="form.comments"
                :class="{ error_input: errors.comments_error }"
                @focus="focusInput('comments', 'comments')"
                maxlength="5000"
                name=""
                class="textarea is_new"
                :placeholder="$c('single.ContactSales.whatProducts')"
                @blur="blurInput('comments')"
                @input="blurInput('comments')"></textarea>
            <div class="input-item-number">
                <validate-error :error="errors.comments_error"></validate-error>
            </div>
        </div>
        <div class="inpbox01 upload_box" v-if="isSg">
            <div class="upload_main">
                <upload-file
                    :isNewStyle="true"
                    ref="uploadFile"
                    accept=".pdf,image/jpeg,image/jpg,image/png"
                    :maxSize="5 * 1024 * 1024"
                    type="file"
                    :multiple="true"
                    :limit="5"
                    :text="$c('form.form.upload_file')"
                    @change="handleChange">
                    <fs-popover slot="tip">
                        <p>{{ $c("form.form.allow_files_of_type") }}</p>
                        <p>{{ $c("form.form.maximum_size_5M") }}</p>
                    </fs-popover>
                </upload-file>
            </div>
        </div>

        <!-- <div v-if="isSg" class="agreement_wrap_sg_div">
            <div class="agreement_wrap_sg">
                <div class="agreement_lt" :class="{ agreement_lt_color: agreement_lt }">
                    <input type="checkbox" class="icon" @change="changeAgreeFu" />
                </div>
                <div>
                    <span @mouseenter="agreement_lt = true" @mouseleave="agreement_lt = false">{{ $c("form.form.agree_txt02") }}</span>
                    <nuxt-link :to="localePath({ name: 'privacy-policy' })" target="_blank">{{ $c("form.form.privacy_policy") }}</nuxt-link>
                    <span @mouseenter="agreement_lt = true" @mouseleave="agreement_lt = false">{{ $c("form.form.and") }}</span>
                    <nuxt-link :to="localePath({ name: 'terms-of-use' })" target="_blank">{{ $c("form.form.terms_of_use") }}</nuxt-link
                    >.
                </div>
            </div>
            <validate-error :error="checked_error"></validate-error>
        </div>
        <div
            v-else
            class="agreement_wrap"
            @click.stop="clickLink($event)"
            v-html="
                $c('pages.Login.I_agree_to_FSs')
                    .replace('XXX1', localePath({ name: 'privacy-policy' }))
                    .replace('XXX2', localePath({ name: 'terms-of-use' }))
            "></div> -->
        <template v-if="newForm">
            <div class="inpbox01 last_inpbox">
                <div class="policy_box">
                    <input v-model="form.isAgreePolicy" @change="blurInput('isAgreePolicy')" type="checkbox" class="chk" />
                    <div
                        class="form_agreement"
                        @click.stop="clickLink($event)"
                        v-html="
                            $c('form.validate.aggree_policy_new')
                                .replace('AAAA', localePath({ name: 'privacy-notice' }))
                                .replace('BBBB', localePath({ name: 'terms-of-use' }))
                        "></div>
                </div>
                <validate-error :error="errors.isAgreePolicy"></validate-error>
            </div>
            <div class="sbtn-box">
                <fs-button id="service_chat" :text="$c('single.ContactSales.Submit')" :loading="sbtn_loading" htmlType="submit"></fs-button>
            </div>
        </template>
        <template v-else>
            <div class="inpbox01 last_inpbox">
                <div class="policy_box">
                    <input v-model="form.isAgreePolicy" @change="blurInput('isAgreePolicy')" type="checkbox" class="chk" />
                    <div
                        class="form_agreement"
                        @click.stop="clickLink($event)"
                        v-html="
                            $c('form.validate.aggree_policy_new')
                                .replace('AAAA', localePath({ name: 'privacy-notice' }))
                                .replace('BBBB', localePath({ name: 'terms-of-use' }))
                        "></div>
                </div>
                <validate-error :error="errors.isAgreePolicy"></validate-error>
            </div>
            <div class="sbtn-box">
                <fs-button id="service_chat" :text="$c('single.ContactSales.Submit')" :loading="sbtn_loading" htmlType="submit"></fs-button>
            </div>
        </template>
    </form>
</template>

<script>
import { email_valdate, cn_all_phone } from "@/constants/validate.js"
import ValidateError from "@/components/ValidateError/ValidateError.vue"
import SelectCountry from "@/components/SelectCountry/SelectCountry.vue"
import TelCode from "@/components/TelCode/TelCode.vue"
import FsPopover from "@/components/FsPopover"
import UploadFile from "@/components/UploadFile/UploadFile"
import FsTip from "@/components/FsTip/FsTip"
import FsButton from "@/components/FsButton/FsButton.vue"
import FsSelect from "@/components/FsSelect/FsSelect"
import fixScroll from "@/util/fixScroll"
import { mapState, mapGetters } from "vuex"
import RegionSelect from "@/components/RegionSelect/RegionSelect"
import { getRecaptchaToken } from "@/util/grecaptchaHost"
export default {
    name: "supportLt",
    components: {
        FsSelect,
        ValidateError,
        SelectCountry,
        TelCode,
        FsPopover,
        UploadFile,
        FsTip,
        FsButton,
        RegionSelect,
    },
    props: {
        isFromQualityControl: {
            // 用于判断会否展示的证书页面
            type: Boolean,
            default: false,
        },
        newForm: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {
            form: {
                entry_firstname: this.$store.state.userInfo.userInfo && this.$store.state.userInfo.userInfo.customers_firstname ? this.$store.state.userInfo.userInfo.customers_firstname : "",
                entry_lastname: this.$store.state.userInfo.userInfo && this.$store.state.userInfo.userInfo.customers_lastname ? this.$store.state.userInfo.userInfo.customers_lastname : "",
                email_address: this.$store.state.userInfo.userInfo && this.$store.state.userInfo.userInfo.customers_email_address ? this.$store.state.userInfo.userInfo.customers_email_address : "",
                countries_id: "",
                comments: "",
                industry: "",
                job_title: "",
                company_size: "",
                company_name: "",
                entry_telephone: this.$store.state.userInfo.userInfo && this.$store.state.userInfo.userInfo.customers_telephone ? this.$store.state.userInfo.userInfo.customers_telephone : "",
                reviews_newImg: [],
                isAgreePolicy: false,
            },
            errors: {
                entry_firstname_error: "",
                entry_lastname_error: "",
                email_address_error: "",
                countries_id_error: "",
                industry_error: "",
                comments_error: "",
                job_title_error: "",
                company_size_error: "",
                company_name_error: "",
                entry_telephone_error: "",
                isAgreePolicy: "",
            },
            sbtn_loading: false,
            regExp: /^\d{6,}$/,
            industryList: this.$c("single.ContactSales.industry"),
            sizeList: this.$c("single.ContactSales.commpanySize"),
            code: "",
            checked: false,
            agreement_lt: false,
            checked_error: "",
        }
    },
    computed: {
        ...mapState({
            isLogin: (state) => state.userInfo.isLogin,
            select_country_id: (state) => state.selectCountry.select_country_id,
            website: (state) => state.webSiteInfo.website,
            countries_id: (state) => state.webSiteInfo.countries_id,
            language: (state) => state.webSiteInfo.language,
            pageGroup: (state) => state.ga.pageGroup,
            resourcePage: (state) => state.device.resource_page,
        }),
        ...mapGetters({
            isShowState: "selectCountry/isShowState",
            isCn: "webSiteInfo/isCn",
            country_label: "selectCountry/country_label",
        }),
        isSg() {
            return this.website === "sg"
        },
    },
    mounted() {},
    created() {},
    methods: {
        buriedLink(e) {
            window.dataLayer &&
                window.dataLayer.push({
                    event: "uaEvent",
                    eventCategory: "Send A Message Page",
                    eventAction: "Contact_Sales",
                    eventLabel: "WhatsApp Clicks",
                    nonInteraction: false,
                })
        },
        clickLink(e) {
            if (e.target && e.target.href && e.target.href.includes("privacy_policy")) {
                window.dataLayer &&
                    window.dataLayer.push({
                        event: "uaEvent",
                        eventCategory: "Send A Message Page",
                        eventAction: "contact_sales",
                        eventLabel: "privacy_policy",
                        nonInteraction: false,
                    })
            } else {
                window.dataLayer &&
                    window.dataLayer.push({
                        event: "uaEvent",
                        eventCategory: "Send A Message Page",
                        eventAction: "contact_sales",
                        eventLabel: "terms_of_use",
                        nonInteraction: false,
                    })
            }
        },
        selectCountry() {
            console.log(`Country / Region_Drop-Down`)
            window.dataLayer &&
                window.dataLayer.push({
                    event: "uaEvent",
                    eventCategory: "Send A Message Page",
                    eventAction: "contact_sales",
                    eventLabel: `Country / Region_Drop-Down`,
                    nonInteraction: false,
                })
        },
        telClick() {
            window.dataLayer &&
                window.dataLayer.push({
                    event: "uaEvent",
                    eventCategory: "Send A Message Page",
                    eventAction: "contact_sales",
                    eventLabel: `Phone Number_Input`,
                    nonInteraction: false,
                })
        },

        telChange(inp) {
            this.form.entry_telephone = inp
            // 判断是否为6位数字
            if (!inp.replace(/^\s+|\s+$/g, "")) {
                this.errors.entry_telephone_error = this.$c("form.form.errors.entry_telephone_error")
            } else {
                if (!this.isCn) {
                    // if (!this.regExp.test(inp)) {
                    //     this.errors.entry_telephone_error = this.$c("form.form.errors.entry_telephone_error01")
                    // } else {
                    //     this.errors.entry_telephone_error = ""
                    // }
                } else {
                    if (!cn_all_phone.test(inp)) {
                        this.errors.entry_telephone_error = this.$c("form.form.errors.entry_telephone_error01")
                    } else {
                        this.errors.entry_telephone_error = ""
                        if (this.isLogin == 1) return
                        this.$axios
                            .post("/api/user/isHasRegister", { customers_name: inp })
                            .then((res) => {
                                if (res.code != 200) return
                                const data = res.data
                                if (data && data.is_has) {
                                    this.errors.entry_telephone_error = "账户已存在，单击此处<a href=XXXX title='登录'>登录</a>。"
                                } else {
                                    this.errors.entry_telephone_error = ""
                                }
                            })
                            .catch((e) => {
                                this.errors.entry_telephone_error = ""
                            })
                    }
                }
            }
        },
        changeCode(code) {
            this.code = code
        },
        focusInput(attr, label) {
            // this.errors[attr + "_error"] = ""
            if (label == "Industry" || label == "Company Size") {
                window.dataLayer &&
                    window.dataLayer.push({
                        event: "uaEvent",
                        eventCategory: "Send A Message Page",
                        eventAction: "contact_sales",
                        eventLabel: `${label}_Drop-Down`,
                        nonInteraction: false,
                    })
            } else {
                window.dataLayer &&
                    window.dataLayer.push({
                        event: "uaEvent",
                        eventCategory: "Send A Message Page",
                        eventAction: "contact_sales",
                        eventLabel: `${label}_Input`,
                        nonInteraction: false,
                    })
            }
        },
        blurInput(attr) {
            if (attr === "entry_firstname") {
                if (!this.form.entry_firstname.replace(/^\s+|\s+$/g, "")) {
                    this.errors.entry_firstname_error = this.$c("form.form.errors.entry_firstname_error")
                    return
                } else if (this.form.entry_firstname.length > 40) {
                    this.errors.entry_firstname_error = this.$c("form.validate.first_name.first_name_max")
                    return
                }
                this.errors.entry_firstname_error = ""
            }
            if (attr === "entry_lastname") {
                if (!this.form.entry_lastname.replace(/^\s+|\s+$/g, "")) {
                    this.errors.entry_lastname_error = this.$c("form.form.errors.entry_lastname_error")
                    return
                } else if (this.form.entry_lastname.length > 40) {
                    this.errors.entry_lastname_error = this.$c("form.validate.last_name.last_name_max")
                    return
                }
                this.errors.entry_lastname_error = ""
            }
            if (attr === "entry_telephone") {
            }
            if (attr === "company_name") {
                if (!this.form.company_name.replace(/^\s+|\s+$/g, "")) {
                    this.errors.company_name_error = this.$c("single.ContactSales.fieldRequired")
                    return
                } else if (this.form.company_name.length < 3 || this.form.company_name.length > 120) {
                    this.errors.company_name_error = this.$c("pages.confirmOrder.form.company_name_validate")
                    return
                }
                this.errors.company_name_error = ""
            }
            if (attr === "job_title") {
                if (!this.form.job_title.replace(/^\s+|\s+$/g, "")) {
                    this.errors.job_title_error = this.$c("single.ContactSales.fieldRequired")
                    return
                }
                this.errors.job_title_error = ""
            }
            if (attr === "email_address") {
                if (!this.isCn) {
                    if (!this.form.email_address.replace(/^\s+|\s+$/g, "")) {
                        this.errors.email_address_error = this.isSg ? this.$c("form.form.errors.email_address_error") : this.$c("form.form.errors.email_business_error")
                    } else if (!email_valdate.test(this.form.email_address)) {
                        this.errors.email_address_error = this.isSg ? this.$c("form.form.errors.email_address_error01") : this.$c("form.form.errors.email_business_error01")
                    } else {
                        this.errors.email_address_error = ""
                    }
                } else {
                    if (!this.form.email_address.replace(/^\s+|\s+$/g, "")) {
                        // this.errors.email_address_error = this.isSg ? this.$c("form.form.errors.email_address_error") :  this.$c("form.form.errors.email_business_error")
                    } else if (!email_valdate.test(this.form.email_address)) {
                        this.errors.email_address_error = this.isSg ? this.$c("form.form.errors.email_address_error01") : this.$c("form.form.errors.email_business_error01")
                    } else {
                        this.errors.email_address_error = ""
                        if (this.isLogin == 1) return
                        this.$axios
                            .post("/api/user/isHasRegister", { customers_name: this.form.email_address })
                            .then((res) => {
                                if (res.code != 200) return
                                const data = res.data
                                if (data && data.is_has) {
                                    this.errors.email_address_error = "账户已存在，单击此处<a href=XXXX title='登录'>登录</a>。"
                                } else {
                                    this.errors.email_address_error = ""
                                }
                            })
                            .catch((e) => {
                                this.errors.email_address_error = ""
                            })
                    }
                }
            }
            if (attr === "comments") {
                if (!this.form.comments.replace(/^\s+|\s+$/g, "")) {
                    this.errors.comments_error = this.$c("single.ContactSales.fieldRequired")
                } else {
                    this.errors.comments_error = ""
                }
            }
            if (attr === "isAgreePolicy") {
                this.errors.isAgreePolicy = this.form.isAgreePolicy ? "" : this.$c("form.form.errors.check2_error")
            }
        },
        handleChange(params) {
            this.form.reviews_newImg.splice(0, this.form.reviews_newImg.length, ...params.files)
        },
        changeAgreeFu() {
            this.checked = !this.checked
            if (!this.checked) {
                this.checked_error = this.$c("form.form.errors.check2_error")
            } else {
                this.checked_error = ""
            }
        },
        async submitFu() {
            console.log("1111111", this.sbtn_loading, window.location.hostname)
            if (this.sbtn_loading) {
                return
            }
            let flag = false
            if (!this.form.entry_firstname.replace(/^\s+|\s+$/g, "")) {
                this.errors.entry_firstname_error = this.$c("form.form.errors.entry_firstname_error")
                flag = true
            }
            if (!this.form.entry_lastname.replace(/^\s+|\s+$/g, "")) {
                this.errors.entry_lastname_error = this.$c("form.form.errors.entry_lastname_error")
                flag = true
            }
            if (!this.isCn) {
                if (!this.form.email_address.replace(/^\s+|\s+$/g, "")) {
                    this.errors.email_address_error = this.isSg ? this.$c("form.form.errors.email_address_error") : this.$c("form.form.errors.email_business_error")
                    flag = true
                } else if (!email_valdate.test(this.form.email_address)) {
                    this.errors.email_address_error = this.isSg ? this.$c("form.form.errors.email_address_error01") : this.$c("form.form.errors.email_business_error01")
                    flag = true
                }
            } else {
                if (this.errors.email_address_error) {
                    flag = true
                }
                if (this.errors.entry_telephone_error) {
                    flag = true
                }
            }
            if (this.isCn) {
                if (!this.form.entry_telephone.replace(/^\s+|\s+$/g, "")) {
                    this.errors.entry_telephone_error = this.$c("form.form.errors.entry_telephone_error")
                    flag = true
                } else if (!cn_all_phone.test(this.form.entry_telephone)) {
                    this.errors.entry_telephone_error = this.$c("form.form.errors.entry_telephone_error01")
                    flag = true
                }
            }
            if (!this.form.company_name.replace(/^\s+|\s+$/g, "")) {
                this.errors.company_name_error = this.$c("single.ContactSales.fieldRequired")
                flag = true
            }
            // if (!this.isSg) {
            //     if (!this.form.entry_telephone.replace(/^\s+|\s+$/g, "")) {
            //         this.errors.entry_telephone_error = this.$c("form.form.errors.entry_telephone_error")
            //         flag = true
            //     } else if (!this.regExp.test(this.form.entry_telephone)) {
            //         this.errors.entry_telephone_error = this.$c("form.form.errors.entry_telephone_error01")
            //         flag = true
            //     }
            // }

            // if (!this.isSg) {
            //     if (!this.form.industry.replace(/^\s+|\s+$/g, "")) {
            //         this.errors.industry_error = this.$c("single.ContactSales.fieldRequired")
            //         flag = true
            //     }
            //     if (!this.form.job_title.replace(/^\s+|\s+$/g, "")) {
            //         this.errors.job_title_error = this.$c("single.ContactSales.fieldRequired")
            //         flag = true
            //     }
            //     if (!this.form.company_name.replace(/^\s+|\s+$/g, "")) {
            //         this.errors.company_name_error = this.$c("single.ContactSales.fieldRequired")
            //         flag = true
            //     }
            //     if (!this.form.company_size.replace(/^\s+|\s+$/g, "")) {
            //         this.errors.company_size_error = this.$c("single.ContactSales.fieldRequired")
            //         flag = true
            //     }
            // }
            if (!this.form.comments.replace(/^\s+|\s+$/g, "")) {
                this.errors.comments_error = this.$c("single.ContactSales.fieldRequired")
                flag = true
            }
            if (this.isFromQualityControl) {
                this.checked = true
            }
            if (!this.checked && this.isSg) {
                this.checked_error = this.$c("form.form.errors.check2_error")
                flag = true
            }
            if (!this.form.isAgreePolicy) {
                this.errors.isAgreePolicy = this.$c("form.form.errors.check2_error")
                flag = true
            }
            console.log("22222222", flag)
            if (flag) return
            const data = new FormData()
            data.append("entry_firstname", this.form.entry_firstname)
            data.append("entry_telephone", this.form.entry_telephone)
            data.append("entry_lastname", this.form.entry_lastname)
            data.append("company_name", this.form.company_name)
            if (this.form.email_address) {
                data.append("email_address", this.form.email_address)
            }

            if (this.form.reviews_newImg.length && this.isSg) {
                for (let i = 0; i < this.form.reviews_newImg.length; i++) {
                    data.append("reviews_newImg[]", this.form.reviews_newImg[i])
                }
            }

            data.append("comments", this.form.comments)
            // if (!this.isSg) {
            //     data.append("countries_id", this.select_country_id)
            //     data.append("industry", this.form.industry)
            //     data.append("company_size", this.form.company_size)
            //     data.append("company_name", this.form.company_name)
            //     data.append("job_title", this.form.job_title)
            // if (this.$refs.regionSelect) {
            //     data.append("state", this.$refs.regionSelect.state || "")
            // }
            // }
            // data.append("entry_telephone", `${this.code.replace("+", "")} ${this.form.entry_telephone}`)
            data.append("resource_page", this.isFromQualityControl ? "13" : "3")
            data.append("website_link", location.href)
            const { recaptchaTp, headers = {}, errorMsg = "grecaptcha_error" } = await getRecaptchaToken()
            if (!recaptchaTp) {
                this.loading = false
                this.$message.error(this.$c(`pages.Login.regist.${errorMsg}`))
                return
            }

            this.sbtn_loading = true
            this.$axios
                .post("/api/contact_sales", data, { headers })
                .then((res) => {
                    this.sbtn_loading = false
                    if (res.code == 200 && res.data.result == true) {
                        window.dataLayer &&
                            window.dataLayer.push({
                                event: "uaEvent",
                                eventCategory: "Send A Message Page",
                                eventAction: "contact_sales",
                                eventLabel: `Submit Success_${res.data.data.caseNumber}`,
                                nonInteraction: false,
                            })
                        this.form = {
                            entry_firstname: "",
                            entry_lastname: "",
                            email_address: "",
                            //countries_id: "223",
                            industry: "",
                            comments: "",
                            job_title: "",
                            company_size: "",
                            company_name: "",
                            entry_telephone: "",
                        }
                        this.$bdRequest({
                            conversionTypes: [
                                {
                                    logidUrl: location.href,
                                    newType: 18,
                                },
                            ],
                        })
                        this.$emit("success")
                        this.checked = false
                    } else if (res.code === 200 && res.status === "sensiWords" && this.website === "cn") {
                        for (let key in res.errors) {
                            this.errors[`${key}_error`] = this.$c("form.form.errors.sensiWords")
                        }
                    } else {
                        this.$message.error("There was an error processing your request. Please try again or contact support.")
                    }
                })
                .catch((err) => {
                    this.sbtn_loading = false
                    window.dataLayer &&
                        window.dataLayer.push({
                            event: "uaEvent",
                            eventCategory: "Send A Message Page",
                            eventAction: "contact_sales",
                            eventLabel: "Submit Fail",
                            nonInteraction: false,
                        })
                    if (err.code === 422) {
                        this.errors.entry_firstname_error = err.errors.entry_firstname ? err.errors.entry_firstname : ""
                        this.errors.entry_lastname_error = err.errors.entry_lastname ? err.errors.entry_lastname : ""
                        this.errors.email_address_error = err.errors.email_address ? err.errors.email_address : ""
                        this.errors.countries_id_error = err.errors.countries_id ? err.errors.countries_id : ""
                        this.errors.industry_error = err.errors.industry ? err.errors.industry : ""
                        this.errors.job_title_error = err.errors.job_title ? err.errors.job_title : ""
                        this.errors.company_size_error = err.errors.company_size ? err.errors.company_size : ""
                        this.errors.company_name_error = err.errors.company_name ? err.errors.company_name : ""
                        this.errors.comments_error = err.errors.comments ? err.errors.comments : ""
                        this.errors.entry_telephone_error = err.errors.entry_telephone ? err.errors.entry_telephone : ""
                    }
                    if (err.code === 403) {
                        this.$message.error(err.message)
                    }
                })
        },

        clearSearch() {
            this.form.email_address = ""
        },
    },
    beforeDestroy() {},
}
</script>
<style scoped lang="scss">
.lt_wrap {
    .inp,
    select {
        height: 42px;
        line-height: 42px;
    }
    ::v-deep .error_info {
        a {
            color: $textColor4;
            text-decoration: underline;
        }
    }
    /* .error_input {
        @include errorInput;
    } */
    ::v-deep {
        .select-country .select-country-active,
        .tel-code .tel,
        .code {
            line-height: 42px;
            height: 42px;
            // @include font12;
        }
        .select-country .country-wrap {
            .inp {
                // @include font12;
            }
            .country-box {
                .item .country-name {
                    // @include font12;
                }
            }
        }
        .select-country .select-country-active .icofnont-down {
            height: auto;
        }
        .validate_error .error_info {
            @include font13;
        }
        .fs-select .options-wrap {
            .options-box .item .list-name {
                @include font13;
            }
        }
        .fs-select .fs-select-active .select-active-box {
            @include font13;
        }
    }

    .inp,
    .textarea,
    select {
        // @include font12;
    }
    select {
        background-size: 12px;
        background-position-x: calc(100% - 12px);
    }

    .txt {
        @include font14;
        color: $textColor1;
        margin: 0 0 4px 0;
        display: flex;
        align-items: center;
        // justify-content: space-between;
        // span {
        //     @include font13;
        //     color: $textColor3;
        //     i {
        //         font-style: normal;
        //         &.active {
        //             color: #c00000;
        //         }
        //     }
        // }
    }

    .support_alone_txt {
        color: $textColor1;
        margin: 0 0 12px 0;
        div {
            font-weight: 600;
            @include font20;
            padding-bottom: 4px;
        }
        p {
            @include font12;
        }
    }
    .inpbox,
    .inpbox01 {
        .txt {
            @include font12;
            color: #707070;
        }
    }
    .inpbox {
        display: flex;
        justify-content: space-between;
        margin: 0 0 16px 0;
        &.inpbox_cn {
            flex-direction: row-reverse;
        }
        > div {
            width: calc(50% - 10px);
        }
        .emailInput {
            width: 100%;
            position: relative;
            .fsSelectSearch__box {
                width: 28px;
                height: 28px;
                display: flex;
                align-items: center;
                justify-content: center;
                cursor: pointer;
                position: absolute;
                right: 7px;
                top: 7px;

                &:hover {
                    background: rgba(25, 25, 26, 0.04);
                    border-radius: 3px;

                    .iconfont {
                        color: #19191a;
                    }
                }

                @media (max-width: 768px) {
                    display: none;
                }
            }
        }
    }

    .big_box {
        display: flex;
        justify-content: space-between;
        > div {
            width: calc(50% - 6px);
        }
    }
    .country-box {
        display: flex;
        .inpbox01 {
            flex: 1;
            & + .inpbox01 {
                margin-left: 12px;
            }
        }
        @include mediaM {
            display: block;
            .inpbox01 + .inpbox01 {
                margin-left: 0;
            }
        }
    }
    .inpbox01 {
        margin: 0 0 16px 0;
        &.last_inpbox {
            margin: 0;
        }
        .input-item-flex {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .input-item-number {
            width: 100%;
        }
        .textarea-num {
            color: $textColor3;
            @include font12;
            em {
                font-style: normal;
                &.active {
                    color: $textColor4;
                }
            }
        }
    }

    .upload_box {
        color: $textColor6;
        display: flex;
        align-items: center;
        margin: 12px 0 0;
        .upload_main {
            position: relative;
            display: flex;
            align-items: center;
            ::v-deep .upload-file-wrap {
                &:hover {
                    .file-btn {
                        background: #f7f7f7;
                    }
                }
                .file-btn {
                    align-items: center;
                    padding: 10px 24px;
                    background: #fafafb;
                    border-radius: 3px;
                    border: 1px solid #e5e5e5;
                }
                .iconfont-upload {
                    margin-right: 8px;
                    height: 16px;
                    line-height: 16px;
                }
                .file-info {
                    display: flex;
                    align-items: center;
                    .info-txt {
                        @include font14;
                        color: $textColor1;
                    }
                    .upload-tip-wrap {
                        width: 0;
                        display: flex;
                        align-items: center;
                    }
                }
                .fs-popover {
                    margin-left: 12px;
                }
            }
        }
        .upload_content {
            cursor: pointer;
            display: flex;
            .clip_icon {
                font-size: 14px;
                color: $textColor6;
                padding-right: 5px;
            }
            i {
                cursor: pointer;
                font-style: normal;
                @include font14;
            }
        }
    }
    ::v-deep .tel-code {
        .tel {
            // border-left: 1px solid #e5e5e5;
            // &:focus {
            //     border-left: 1px solid #000;
            // }
        }
        .code {
            border: 1px solid #e5e5e5;
            border-right: none;
            background: #f7f7f7;
            line-height: 42px;
            height: 42px;
        }
    }

    select,
    textarea {
        color: $textColor1;
    }
    textarea {
        @include font14;
        /* &::placeholder {
            color: $textColor3;
            @include font13;
        } */
    }
    ::v-deep .fs-tip .tip-ctn .info {
        width: 300px;
        white-space: inherit;
        padding: 10px;
    }
    .agreement_wrap_sg {
        @include font13;
        display: flex;
        color: $textColor3;
        margin: 16px 0 0;
        .agreement_lt {
            margin: 0 8px 0 0;
            cursor: pointer;
            display: flex;
            align-items: center;
        }
        @media (min-width: 768px) {
            .agreement_lt_color {
                cursor: pointer;
                input[type="checkbox"] {
                    &::before {
                        color: #707070;
                    }
                }
            }
        }
    }

    .agreement_wrap {
        @include font12;
        padding-top: 16px;
        color: $textColor3;
    }
    .sbtn-box {
        margin-top: 20px;
        .fs-button {
            width: initial;
            height: 42px;
        }
    }
    .success_main {
        padding: 4px 40px 40px;
        display: flex;
        .lt {
            padding: 1px 0 0;
            .icon {
                color: #18a209;
            }
        }
        .rt {
            padding: 0 0 0 8px;
            .tit {
                @include font16;
                color: $textColor1;
            }
            .txt {
                @include font12;
                color: $textColor3;
                margin: 20px 0 0;
                display: inline-block;
            }
        }
    }
}
.policy_box {
    margin-top: 16px;
    display: flex;
    .chk {
        margin-top: 4px;
        margin-right: 8px;
        width: 14px;
        height: 14px;
        font-size: 14px;
    }
    .form_agreement {
        @include font12;
        // margin: 16px 0 20px;
        color: $textColor3;
    }
}
@media (max-width: 1024px) {
}
@media (max-width: 768px) {
    .lt_wrap {
        border-bottom: 1px solid #e5e5e5;
        padding-bottom: 24px;
        .support_alone_txt {
            text-align: left;
            div {
                @include font20;
            }
        }
        .sbtn-box .fs-button {
            width: 100%;
        }
        .inpbox {
            display: block;
            &.inpbox_cn {
                display: flex;
                flex-direction: column-reverse;
                .lt {
                    padding-bottom: 0;
                }
                .rt {
                    padding-bottom: 16px;
                }
            }
            > div {
                width: 100%;
                &:first-child {
                    padding-bottom: 16px;
                }
            }
        }
        .big_box {
            display: block;
            > div {
                width: 100%;
            }
        }
        .fs-tip {
            &::v-deep .tip-ctn {
                .info {
                    width: 100%;
                }
            }
        }
    }
}
</style>
