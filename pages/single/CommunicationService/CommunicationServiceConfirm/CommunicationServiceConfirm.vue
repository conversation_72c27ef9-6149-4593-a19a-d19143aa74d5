<template>
    <section class="communication-service-confirm">
        <div class="confirm-content">
            <!-- <p class="confirm-title">{{ $c("single.communicationService.confirm.title") }}</p> -->
            <div class="confirm-item confirm-detail">
                <p class="confirm-item-title">{{ $c("single.communicationService.confirm.detail.title") }}</p>
                <div class="confirm-detail-content">
                    <div class="user-info">
                        <div class="user-info-item">
                            <img :src="userInfoIcons[0]" :alt="name" />
                            <ul class="user-info-ul">
                                <li>{{ name }}</li>
                                <li>{{ company }}</li>
                                <li>{{ address }}</li>
                            </ul>
                        </div>
                        <div class="user-info-item">
                            <img :src="userInfoIcons[1]" :alt="planningName" />
                            <ul class="user-info-ul">
                                <li>{{ planningName }}</li>
                                <li>{{ threeLevelName }}</li>
                                <li>{{ planningComments }}</li>
                            </ul>
                        </div>
                        <div class="user-info-item">
                            <img :src="userInfoIcons[2]" :alt="timeGroup" />
                            <ul class="user-info-ul">
                                <li>{{ timeGroup }}</li>
                                <li>{{ timeRange }}</li>
                            </ul>
                        </div>
                    </div>
                    <div class="add-calendar">
                        <p class="add-calendar-title">{{ $c("single.communicationService.confirm.detail.addCalendar.title") }}</p>
                        <div class="add-calendar-list">
                            <a :href="outlookHref" download="Calendar.ics">Outlook</a>
                            <a :href="googleHref" target="_blank">Google</a>
                            <a :href="iCalHref" download="Calendar.ics">iCal</a>
                        </div>
                    </div>
                </div>
            </div>
            <!-- 移动端 -->
            <div class="add-calendar-m">
                <p class="add-calendar-title">{{ $c("single.communicationService.confirm.detail.title") }}</p>
                <div class="add-calendar-list">
                    <a :href="outlookHref" download="Calendar.ics">Outlook</a>
                    <a :href="googleHref" target="_blank">Google</a>
                    <a :href="iCalHref" download="Calendar.ics">iCal</a>
                </div>
            </div>
            <div class="confirm-item confirm-next">
                <p class="confirm-item-title">{{ $c("single.communicationService.confirm.next.title") }}</p>
                <div class="confirm-next-content">
                    <div class="confirm-next-item" v-for="(item, index) in confirmNextList" :key="index">
                        <i class="iconfont">&#xe710;</i>
                        <dl>
                            <dt>{{ item.title }}</dt>
                            <dd class="dd-label" v-for="label in item.labels" :key="label" v-html="label"></dd>
                            <dd class="dd-links" v-if="index === 0">
                                <a href="https://play.google.com/store/apps/details?id=us.zoom.zrc" @click="handlePoint('Google Play')" target="_blank">
                                    <img src="https://resource.fs.com/mall/generalImg/202302211207126oexrt.png" alt="Get it on Google Play" />
                                </a>
                                <a href="https://apps.apple.com/us/app/zoompresence/id900259081?ls=1" @click="handlePoint('Apple Store')" target="_blank">
                                    <img src="https://resource.fs.com/mall/generalImg/2023022112070010jtbp.png" alt="Download on the App Store" />
                                </a>
                            </dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>
    </section>
</template>

<script>
import { mapState } from "vuex"
import { setCookieOptions } from "@/util/util"
export default {
    name: "CommunicationServiceConfirm",
    data() {
        return {
            userInfoIcons: [
                require("@/assets/communication-service-icon/confirm-icon-01.svg"),
                require("@/assets/communication-service-icon/confirm-icon-02.svg"),
                require("@/assets/communication-service-icon/confirm-icon-03.svg"),
            ],
            confirmNextList: this.$c("single.communicationService.confirm.next.list"),
            location: null,
            planning: null,
            dataTime: null,
            personalDetail: null,
            outlookHref: "",
            googleHref: "",
            iCalHref: "",
        }
    },
    computed: {
        ...mapState({
            website: (state) => state.webSiteInfo.website,
        }),
        name() {
            let name = ""
            if (this.personalDetail) {
                const { firstName, lastName } = this.personalDetail
                if (["cn", "hk", "tw", "mo"].includes(this.website)) {
                    name = `${lastName} ${firstName}`
                } else {
                    name = `${firstName}, ${lastName}`
                }
            }
            return name
        },
        company() {
            return this.personalDetail?.company || ""
        },
        address() {
            const { personalDetail } = this
            let address = ""
            if (personalDetail) {
                address = `${personalDetail.currentCountryName}`
            }
            return address
        },
        planningName() {
            let name = ""
            if (this.planning && this.location) {
                const { meetingContent = {} } = this.planning
                const { meetingName = "" } = this.location
                const { meetingContentName = "" } = meetingContent
                if (this.website === "ru") {
                    name = `${meetingName} ; ${meetingContentName}`
                } else if (this.website === "jp") {
                    name = `${meetingName}、 ${meetingContentName}`
                } else {
                    name = `${meetingName}, ${meetingContentName}`
                }
            }
            return name
        },
        threeLevelName() {
            let name = ""
            if (this.planning) {
                const { meetingContent = {} } = this.planning
                const { meetingContentNextName = [], meetingContentProducts = "" } = meetingContent
                if (meetingContentNextName.length) {
                    name = meetingContentNextName.join(", ")
                }
                if (meetingContentProducts) {
                    name = meetingContentProducts
                }
            }
            return name
        },
        planningComments() {
            return this.planning?.comments || ""
        },
        timeRange() {
            return this.dataTime?.timeRang.replace("-", " - ").replaceAll(":", " : ") || ""
        },
        timeGroup() {
            return this.dataTime?.timeGroup || ""
        },
    },
    created() {
        this.initData()
        this.initGoogleLink()
        this.clearLocalStorageData()
    },
    mounted() {
        this.initICSHref()
        this.initZoomPointEvent()
    },
    methods: {
        initZoomPointEvent() {
            this.$nextTick(() => {
                const zoom = document.getElementById("zoom")
                if (!zoom) return
                zoom.onclick = () => {
                    this.handlePoint("zoom")
                    window.open(zoom.dataset.href)
                }
            })
        },
        initData() {
            try {
                let localStorageData = localStorage.getItem("communication_service")
                if (localStorageData) {
                    localStorageData = JSON.parse(localStorageData)
                    const [location, planning, personalDetail, dataTime] = localStorageData
                    this.location = location || {}
                    this.planning = planning
                    this.dataTime = dataTime
                    this.personalDetail = personalDetail
                }
            } catch (error) {
                console.error(error.message)
            }
        },
        initGoogleLink() {
            try {
                let googleLink = localStorage.getItem("googleLink")
                if (googleLink) {
                    this.googleHref = googleLink
                }
            } catch (error) {
                console.error(error.message)
            }
        },
        clearLocalStorageData() {
            try {
                // this.$cookies.remove("communication_service_step_index")
                // localStorage.removeItem("communication_service")
                // localStorage.removeItem("googleLink")
            } catch (error) {
                console.error(error.message)
            }
        },
        getUID() {
            return new Date().getTime() + Math.random().toString(36).substr(2)
        },
        getISOTime(date) {
            let name = date.toISOString().replaceAll("-", "").replaceAll(":", "")
            const index = name.indexOf(".")
            name = name.substring(0, index) + "Z"
            return name
        },
        initICSHref() {
            try {
                const DTSTAMP = this.getISOTime(new Date())
                const DTSTART = this.getISOTime(new Date(this.dataTime?.meetingStartTime || ""))
                const DTEND = this.getISOTime(new Date(this.dataTime?.meetingEndTime || ""))
                const UID = this.getUID()
                const { address, threeLevelName, planningComments } = this
                var icsMSG = `BEGIN:VCALENDAR\nVERSION:2.0\nPRODID:https://www.fs.com;\nBEGIN:VEVENT\nUID:${UID}\nDTSTAMP:${DTSTAMP}\nDTSTART:${DTSTART}\nDTEND:${DTEND}\nSUMMARY:FS Schedule a Consultation\nLOCATION:${address}\nDESCRIPTION:${threeLevelName},${planningComments}\nEND:VEVENT\nEND:VCALENDAR`
                const blob = new Blob([icsMSG], { type: "text/calendar" })
                const url = window.URL.createObjectURL(blob)
                this.outlookHref = url
                this.iCalHref = url
            } catch (error) {
                console.error(error.message)
            }
        },
        handlePoint(eventLabel) {
            console.log(eventLabel)
            this.buriedPointWrapper("Confirm", eventLabel)
        },
        buriedPointWrapper(eventAction, eventLabel) {
            if (window.dataLayer) {
                window.dataLayer.push({
                    event: "uaEvent",
                    eventCategory: "Resource_ schedule meeting Page",
                    eventAction,
                    eventLabel,
                    nonInteraction: false,
                })
            }
        },
    },
}
</script>

<style scoped lang="scss">
.communication-service-confirm {
    // padding: 28px 0 48px;
    .confirm-content {
        width: 960px;
        margin: 0 auto;
        // .confirm-title {
        //     font-weight: 600;
        //     color: #19191a;
        //     @include font30();
        //     @include sourceSansPro;
        // }
        .confirm-item {
            padding: 48px 32px;
            border-radius: 3px;
            border: 1px solid #f2f2f2;
            .confirm-item-title {
                font-weight: 600;
                color: #19191a;
                @include font16();
                padding-bottom: 24px;
                border-bottom: 1px solid #e5e5e5;
            }
        }
        .confirm-detail {
            .confirm-detail-content {
                margin-top: 32px;
                display: flex;
                flex-wrap: wrap;
                .user-info {
                    flex: 1;
                    margin-right: 16px;
                    .user-info-item {
                        display: flex;

                        &:not(:first-child) {
                            margin-top: 32px;
                        }
                        > img {
                            width: 48px;
                            height: 48px;
                            margin-right: 32px;
                        }
                        .user-info-ul {
                            flex: 1;
                            > li {
                                @include font14();
                                font-weight: 600;
                                color: #19191a;
                                word-break: break-word;
                                &:not(:first-child) {
                                    margin-top: 4px;
                                }
                            }
                        }
                    }
                }
            }
        }
        .add-calendar,
        .add-calendar-m {
            width: 326px;
            .add-calendar-title {
                @include font16();
                font-weight: 600;
                color: #19191a;
            }
            .add-calendar-list {
                display: flex;
                a {
                    margin: 24px 80px 0 0;
                    font-weight: 600;
                    @include font13();
                    &:nth-child(3n) {
                        margin-right: 0;
                    }
                }
            }
        }
        .add-calendar-m {
            display: none;
            width: 100%;
            padding: 0 20px;
            margin-top: 48px;
        }
        .confirm-next {
            margin-top: 48px;
            .confirm-next-content {
                padding-top: 8px;
                .confirm-next-item {
                    margin-top: 24px;
                    display: flex;
                    .iconfont {
                        font-size: 36px;
                        color: #cccccc;
                    }
                    > dl {
                        flex: 1;
                        margin-left: 16px;
                        > dt {
                            @include font14();
                            font-weight: 600;
                            color: #19191a;
                            margin-bottom: 12px;
                        }
                        .dd-label {
                            @include font14();
                            color: #707070;
                            margin-top: 4px;
                        }
                        .dd-links {
                            display: flex;
                            align-items: center;
                            margin-top: 12px;
                            > a {
                                margin-right: 16px;
                                &:last-child {
                                    margin-right: 0;
                                }
                            }
                            img {
                                height: 40px;
                                display: block;
                            }
                        }
                    }
                }
            }
        }
    }
    @include mediaIpad {
        .confirm-content {
            width: auto;
            margin: 0 32px;
        }
    }
    @include mediaM {
        // padding: 32px 0 64px;
        .confirm-content {
            margin: 0 16px;
            .confirm-title {
                @include font20();
            }

            .confirm-item {
                padding: 48px 20px;
            }

            .confirm-detail .confirm-detail-content .user-info {
                margin-right: 0;
            }

            .add-calendar {
                display: none;
            }
            .add-calendar-m {
                display: block;
            }
        }
    }
}
</style>
