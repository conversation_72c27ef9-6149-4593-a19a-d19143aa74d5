<!-- 沟通服务 -->
<template>
    <section class="communication-service">
        <div class="back-m" v-if="stepIndex === 1" @click.stop="emitChangeStepIndex(0)"><span class="iconfont">&#xe702;</span>{{ $c("single.communicationService.back") }}</div>
        <StepWrapper :class="{ noPadd: stepIndex === 1 }" :stepIndex="stepIndex" ref="stepWrapper" :titleList="titleList" :stepList="stepList" @stepItemClick="handleStepItemClick" :sticky="sticky"></StepWrapper>
        <component :is="componentInstance" @changeStepIndex="emitChangeStepIndex" @keepData="emitKeepData"></component>
    </section>
</template>

<script>
import BreadCrumb from "@/components/BreadCrumb/BreadCrumb"
import StepWrapper from "./components/StepWrapper.vue"
import CommunicationServiceLocation from "./CommunicationServiceLocation/CommunicationServiceLocation.vue"
import CommunicationServicePlanning from "./CommunicationServicePlanning/CommunicationServicePlanning.vue"
import CommunicationServiceDateTime from "./CommunicationServiceDateTime/CommunicationServiceDateTime.vue"
import CommunicationServicePersonalDetails from "./CommunicationServicePersonalDetails/CommunicationServicePersonalDetails.vue"
import CommunicationServiceConfirm from "./CommunicationServiceConfirm/CommunicationServiceConfirm.vue"
import { setCookieOptions } from "@/util/util"

const PATHS = [CommunicationServicePlanning, CommunicationServiceDateTime, CommunicationServiceConfirm]
const EXPIRES_TIME = 60 * 1000 * 60 * 24
export default {
    components: { BreadCrumb, StepWrapper },
    name: "CommunicationService",
    data() {
        return {
            titleList: this.$c("single.communicationService.titleList"),
            stepList: this.$c("single.communicationService.stepList"),
            stepIndex: 0,
            sticky: false,
        }
    },
    computed: {
        // 动态加载太慢 ，而且没有比较好的加载过度组件
        componentInstance() {
            const path = PATHS[this.stepIndex]
            if (path) {
                return path
            } else {
                return ""
            }
        },
    },
    mounted() {
        let stickyOffset = this.$refs.stepWrapper.$el.offsetTop
        window.addEventListener("scroll", () => {
            if (window.scrollY >= stickyOffset) {
                this.sticky = true
            } else {
                this.sticky = false
            }
        })
    },
    provide() {
        return {
            provideEchoData: this.provideEchoData,
        }
    },
    asyncData({ app }) {
        const currentDate = +new Date()
        const keepDate = app.$cookies.get("communication_service_expires")
        let isClean = false,
            stepIndex = app.$cookies.get("communication_service_step_index") || 0
        if (keepDate && currentDate - keepDate > EXPIRES_TIME) {
            app.$cookies.remove("communication_service_step_index")
            isClean = true
            stepIndex = 0
        }
        return {
            stepIndex,
            isClean,
        }
    },
    created() {
        this.clearLocalStorageData()
        // this.adaptiveDataKeepDiff()
        // this.dataExpiresDispose()
    },
    methods: {
        // 动态子组件回显数据获取
        provideEchoData(isAll) {
            try {
                let localStorageData = localStorage.getItem("communication_service")
                if (localStorageData) {
                    localStorageData = JSON.parse(localStorageData)
                    return isAll ? localStorageData : localStorageData[this.stepIndex]
                }
            } catch (error) {
                console.log(error.msg)
            }
        },
        // keepStepIndex() {
        //     try {
        //         this.$cookies.set("communication_service_step_index", this.stepIndex,setCookieOptions())
        //     } catch (error) {
        //         console.log(error.msg)
        //     }
        // },
        clearCommunicationServiceStepData(index) {
            try {
                let localStorageData = localStorage.getItem("communication_service")
                if (localStorageData) {
                    localStorageData = JSON.parse(localStorageData) || []
                    localStorage.setItem("communication_service", JSON.stringify(localStorageData.filter((i, idx) => idx <= index)))
                }
            } catch (error) {
                console.log(error.msg)
            }
        },
        // adaptiveDataKeepDiff() {
        //     try {
        //         const $cookies = this.$cookies.get("communication_service_step_index")
        //         if ($cookies === undefined) {
        //             localStorage.removeItem("communication_service")
        //         }
        //     } catch (error) {
        //         console.log(error.msg)
        //     }
        // },
        // dataExpiresDispose() {
        //     try {
        //         if (this.isClean) {
        //             this.clearLocalStorageData()
        //         }
        //     } catch (error) {
        //         console.log(error.msg)
        //     }
        // },
        // keepExpires() {
        //     this.$cookies.set("communication_service_expires", +new Date())
        // },
        clearLocalStorageData() {
            try {
                localStorage.removeItem("communication_service")
                // this.$cookies.remove("communication_service_expires")
            } catch (error) {
                console.log(error.msg)
            }
        },
        handleStepItemClick(index) {
            if (this.stepIndex > index) {
                this.stepIndex = index
                // this.keepStepIndex()
                // this.clearCommunicationServiceStepData(index)
            }
        },
        emitChangeStepIndex(index) {
            if (this.stepIndex > index) {
                // this.clearCommunicationServiceStepData(index)
            }
            this.stepIndex = index !== undefined ? index : this.stepIndex + 1
            // this.keepStepIndex()
        },
        emitKeepData(data, index) {
            console.log(index, "index")
            try {
                let localStorageData = localStorage.getItem("communication_service")
                localStorageData = localStorageData ? JSON.parse(localStorageData) : []
                if (index) {
                    localStorageData[index] = data
                } else {
                    localStorageData[this.stepIndex] = data
                }
                localStorage.setItem("communication_service", JSON.stringify(localStorageData))
                // this.keepExpires()
            } catch (error) {
                console.log(error.msg)
            }
        },
    },
}
</script>

<style scoped lang="scss">
@import "./scss/localStyle.scss";
.communication-service {
    padding-bottom: 36px;

    @include mediaIpad {
        .com-box {
            width: 100%;
            max-width: 100%;
            padding: 0 $IpadPadding;
        }
    }

    @include mediaM {
        .com-box {
            padding: 0 $MPadding;
        }
        ::v-deep .noPadd {
            .step-bar-box {
                padding-top: 0;
            }
        }
    }
    .back-m {
        display: none;
        @media (max-width: 768px) {
            display: block;
            padding: 20px 16px;
            font-size: 14px;
            color: $textColor1;
            cursor: pointer;
            .iconfont {
                padding-right: 8px;
                font-size: 16px;
            }
        }
    }
}
</style>
