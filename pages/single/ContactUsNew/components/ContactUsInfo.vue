<template>
    <div class="contact_us">
        <h1 class="title">{{ contactUs.title }}</h1>
        <p class="desc" v-html="contactUs.description"></p>
        <div class="info" :class="{ sg_info: website === 'sg' }">
            <div class="item" v-for="(item, index) in contactUs.content" :key="index" @click="choosemethod(item, index)">
                <img :src="item.icon" alt="" />
                <div class="content-box">
                    <div class="top-box">
                        <h5 class="tit">
                            <span>{{ item.title }}</span>
                            <i class="iconfont iconfs_2024120401icon"></i>
                        </h5>
                        <p class="txt" v-html="item.content"></p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import { mapState } from "vuex"

export default {
    name: "ContactUsInfo",
    props: {
        contactUs: {
            type: Object,
            default: () => {},
        },
    },
    inject: ["fsLiveChat"],
    components: {},
    data() {
        return {}
    },
    computed: {
        ...mapState({
            website: (state) => state.webSiteInfo.website,
        }),
    },
    watch: {},
    created() {},
    mounted() {},
    methods: {
        chat() {
            this.fsLiveChat()
        },
        choosemethod(item, index) {
            // if (item.href && ["sg", "au"].includes(this.website)) {
            //     if (index == 1) {
            //         this.chat()
            //     } else if ([2, 3, 4].includes(index)) {
            //         this.handleGa(item)
            //         if (this.website === "sg" && index === 4) {
            //             window.open(item.href.href, "_blank")
            //         } else {
            //             this.$router.push(this.localePath({ path: item.href.href }))
            //         }
            //     } else if (index === 5) {
            //         this.feedbackShow()
            //     }
            //     return
            // }
            if (item.href) {
                window.dataLayer &&
                    window.dataLayer.push({
                        event: "uaEvent",
                        eventCategory: "ContactUs Page",
                        eventAction: "ContactUs_Module",
                        eventLabel: item.title,
                        nonInteraction: false,
                    })
                if (index == 0) {
                    window.open(`tel:${item.title}`, "_self")
                    this.$bdRequest({
                        conversionTypes: [
                            {
                                logidUrl: location.href,
                                newType: 2,
                            },
                        ],
                    })
                    return
                }
                if (index == 1) {
                    window.open(`mailto:<EMAIL>`, "_self")
                    this.$bdRequest({
                        conversionTypes: [
                            {
                                logidUrl: location.href,
                                newType: 27,
                            },
                        ],
                    })
                    return
                }
                if (index == 2) {
                    this.chat()
                    this.$bdRequest({
                        conversionTypes: [
                            {
                                logidUrl: location.href,
                                newType: 1,
                            },
                        ],
                    })
                    return
                }
                window.open(item.href.href, "_blank")
                // this.$router.push(this.localePath({ path: item.href.href }))
            }
        },
        setId(index) {
            let s = ""
            if (index === 0 || index === 2) {
                s = "bdtj_lxdh"
            } else if (index === 1) {
                s = "service_chat"
            }
            return s
        },
    },
}
</script>

<style scoped lang="scss">
.contact_us {
    width: 100%;
    display: flex;
    flex-direction: column;
    .title {
        margin-top: 40px;
        margin-left: 0px;
        font-family: Open Sans;
        @include font20;
        font-weight: 600;
        letter-spacing: 0em;
        color: $textColor1;
    }
    .desc {
        margin-top: 12px;
        margin-left: 0px;
        margin-right: 0px;
        font-family: Open Sans;
        @include font14;
        font-weight: normal;
        letter-spacing: 0em;
        color: #707070;
        max-width: 700px;
    }
    .info {
        margin-top: 24px;
        width: 100%;
        margin-bottom: 40px;
        display: grid;
        grid-template-columns: 1fr 1fr 1fr;
        gap: 20px;
        &.sg_info {
            grid-template-columns: 1fr 1fr 1fr 1fr;
        }
        @media (max-width: 768px) {
            grid-template-columns: 1fr;
            &.sg_info {
                grid-template-columns: 1fr;
            }
        }
        .item {
            display: flex;
            //height: 100%;
            background-color: #fafbfb;
            // border: 1px solid #e5e5e5;
            border-radius: 8px;
            padding: 32px;
            display: flex;
            gap: 16px;
            transition: 0.3s all;
            &:hover {
                cursor: pointer;
                box-shadow: 0px 15px 15px -10px rgba(0, 0, 0, 0.15);
                @media (max-width: 414px) {
                    box-shadow: none;
                }
                .tit {
                    span {
                        text-decoration: underline;
                    }
                }
            }
            img {
                width: 36px;
                height: 36px;
            }
            .content-box {
                display: flex;
                flex-direction: column;
                justify-content: space-between;
            }
            .tit {
                @include font14;
                color: $textColor1;
                font-weight: 600;
                margin-bottom: 4px;
                display: flex;
                align-items: center;
                gap: 4px;
                .iconfont {
                    font-size: 12px;
                    font-weight: 400;
                    line-height: 1;
                }
            }
            .txt {
                @include font12;
                color: $textColor3;
            }
            ::v-deep .blacktext-link {
                margin-top: 12px;
                @include font13;
                a {
                    color: $textColor1;
                    margin-bottom: 20px;
                }
                .txt {
                    @include font12;
                    color: $textColor3;
                    font-weight: 400;
                }
            }
        }
    }
}
@media (max-width: 768px) {
    .contact_us {
        .title {
            margin-top: 36px;
        }
        .info {
            margin-bottom: 36px;
        }
    }
}
</style>
