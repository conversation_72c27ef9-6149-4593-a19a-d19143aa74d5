<template>
    <div class="lt_wrap">
        <div class="inpbox_head" v-if="products_id.trim().length > 0">
            <div class="lt">
                <p class="txt">{{ $c("pages.CaseDetail.detailsPage.Solution.formlist.productID") }} *</p>
                <input class="is_new" type="text" v-model.trim="products_id" readonly />
                <validate-error :error="errors.entry_product_id_error"></validate-error>
            </div>
            <div class="rt">
                <p class="txt">&nbsp;</p>
                <qty-box class="qty-box" :attr="index" @change="qtyChange" :max="99" :isNewStyle="true"></qty-box>
            </div>
        </div>
        <div class="inpbox" :class="{ flex_cn: ['cn', 'hk', 'tw', 'mo'].includes(website) }">
            <div class="lt">
                <p class="txt">{{ $c("form.form.first_name") }}{{ website === "jp" ? "（必須）" : " *" }}</p>
                <input
                    aria-label="firstname"
                    v-model.trim="form.entry_firstname"
                    :class="{ error_input: errors.entry_firstname_error }"
                    :placeholder="countries_id === 107 ? $c('form.form.jpFormPlaceHolder.firstName') : ''"
                    @focus="focusInput('entry_firstname', 'First Name Input')"
                    @blur="blurInput('entry_firstname')"
                    @input="changeInput('entry_firstname')"
                    class="inp is_new"
                    type="text" />
                <validate-error :error="errors.entry_firstname_error"></validate-error>
            </div>
            <div class="rt">
                <p class="txt">{{ $c("form.form.last_name") }}{{ website === "jp" ? "（必須）" : " *" }}</p>
                <input
                    aria-label="lastname"
                    v-model.trim="form.entry_lastname"
                    :class="{ error_input: errors.entry_lastname_error }"
                    :placeholder="countries_id === 107 ? $c('form.form.jpFormPlaceHolder.lastName') : ''"
                    @focus="focusInput('entry_lastname', 'Last Name Input')"
                    @input="changeInput('entry_lastname')"
                    class="inp is_new"
                    type="text"
                    @blur="blurInput('entry_lastname')" />
                <validate-error :error="errors.entry_lastname_error"></validate-error>
            </div>
        </div>
        <div :class="{ big_box: isSg }">
            <div class="country-box">
                <div class="inpbox01">
                    <p class="txt">{{ $c("form.form.email") }}{{ website === "cn" ? " " : " *" }}</p>
                    <input
                        aria-label="email_address"
                        v-model.trim="form.email_address"
                        :class="{ error_input: errors.email_address_error }"
                        :placeholder="countries_id === 107 ? $c('form.form.jpFormPlaceHolder.email') : ''"
                        @focus="focusInput('email_address', 'Email Address Input')"
                        @input="changeInput('email_address')"
                        class="inp is_new"
                        type="text"
                        @blur="blurInput('email_address')"
                        v-if="language == 'English'" />
                    <input
                        aria-label="email_address"
                        v-model.trim="form.email_address"
                        :class="{ error_input: errors.email_address_error }"
                        :placeholder="countries_id === 107 ? $c('form.form.jpFormPlaceHolder.email') : ''"
                        @focus="focusInput('email_address', 'Email Address Input')"
                        @input="changeInput('email_address')"
                        class="inp is_new"
                        type="text"
                        @blur="blurInput('email_address')"
                        v-else />
                    <validate-error :error="errors.email_address_error"></validate-error>
                </div>
                <div class="inpbox01">
                    <p class="txt">
                        {{ ["cn", "hk", "tw", "mo"].includes(website) ? $c("form.form.phone_business") : $c("form.form.phone_number") }}&nbsp;<span v-if="isSg"> (optional)</span
                        >{{ website === "jp" ? "（必須）" : isSg ? "" : " *" }}
                    </p>
                    <tel-code
                        code="1"
                        :phone="form.entry_telephone"
                        :isNewStyle="true"
                        @changeCode="changeCode"
                        @change="telChange"
                        @input="telInput"
                        @point="InfoBuried('Phone Number Input')"
                        :error="errors.entry_telephone_error"
                        :placeholder="countries_id === 107 ? $c('form.form.jpFormPlaceHolder.telephone') : ''"></tel-code>
                    <validate-error :error="errors.entry_telephone_error"></validate-error>
                </div>
            </div>
            <div v-if="!isSg" class="country-box">
                <div class="inpbox01">
                    <p class="txt">{{ $c(country_label) }}{{ website === "jp" ? "（必須）" : " *" }}</p>
                    <select-country :isNewStyle="true" position="absolute" @click.native="InfoBuried('Your Country/Region Drop-Down')"></select-country>
                    <validate-error :error="errors.countries_id_error"></validate-error>
                </div>
                <div class="inpbox01">
                    <p class="txt">{{ $c("form.form.company_name") }}</p>
                    <input aria-label="company_name" v-model.trim="form.entry_company_name" maxlength="120" class="inp is_new" type="text" @blur="blurInput('entry_company_name')" />
                </div>
            </div>
        </div>
        <div class="inpbox01 last_inpbox">
            <div class="title">
                <p class="txt">{{ isSg ? "Details" : $c("form.form.inquriy_details") }}{{ website === "jp" ? "（必須）" : " *" }}</p>
                <span class="textarea-num">
                    <em :class="{ active: form.comments.length === 5000 }">{{ form.comments.length }}</em
                    >/5000
                </span>
            </div>
            <textarea
                aria-label="comments"
                :class="{ error_input: errors.comments_error }"
                v-model.trim="form.comments"
                @focus="focusInput('comments', 'Comments Input')"
                @input="changeInput('comments')"
                maxlength="5000"
                name=""
                class="textarea is_new"
                :placeholder="$c('single.ContactSales.whatProducts')"
                @blur="blurInput('comments')"
                v-if="language == 'English'"></textarea>
            <textarea
                :class="{ error_input: errors.comments_error }"
                v-model.trim="form.comments"
                @focus="focusInput('comments', 'Comments Input')"
                @input="changeInput('comments')"
                maxlength="5000"
                name=""
                class="textarea is_new"
                :placeholder="$c('single.ContactSales.whatProducts')"
                @blur="blurInput('comments')"
                v-else></textarea>
            <div :class="errors.comments_error ? 'input-item-flex' : 'input-item-number'">
                <validate-error :error="errors.comments_error"></validate-error>
            </div>
        </div>
        <div class="upload_box">
            <div class="upload_main">
                <upload-file :isNewStyle="true" ref="uploadFile" accept=".pdf,image/jpeg,image/jpg,image/png" :maxSize="5 * 1024 * 1024" type="file" :multiple="true" :limit="5" :text="text" @change="handleChange">
                    <fs-popover slot="tip">
                        <p>{{ $c("form.form.allow_files_of_type") }}</p>
                        <p>{{ $c("form.form.maximum_size_5M") }}</p>
                    </fs-popover>
                </upload-file>
            </div>
        </div>
        <p class="texthint">{{ $c("form.form.how_would") }}</p>
        <div class="inpbox_check">
            <label class="inpbox_check">
                <input type="checkbox" class="icon" v-model="form.contact_type" value="1" @change="changeType" />
                <p>{{ $c("form.form.email") }}</p>
            </label>
            <label class="inpbox_check2">
                <input type="checkbox" class="icon" v-model="form.contact_type" value="2" @change="changeType" />
                <p>{{ $c("form.form.phone") }}</p>
            </label>
        </div>
        <validate-error :error="checked_type_error"></validate-error>
        <div>
            <g-recaptcha ref="grecaptcha" @getValidateCode="getValidateCode"></g-recaptcha>
        </div>
        <PolicyCheck v-model="agree_policy" @change="inputCheck" :error="agree_policy_error" />
        <div class="sbtn-box">
            <fs-button id="solution_support" :text="$c('form.form.submit')" @click="submitFu" :loading="sbtn_loading" htmlType="submit"></fs-button>
        </div>
    </div>
</template>

<script>
import { email_valdate, cn_mobile_tel } from "@/constants/validate.js"
import ValidateError from "@/components/ValidateError/ValidateError.vue"
import SelectCountry from "@/components/SelectCountry/SelectCountry.vue"
import TelCode from "@/components/TelCode/TelCode.vue"
import UploadFile from "@/components/UploadFile/UploadFile"
import FsPopover from "@/components/FsPopover"
import FsSelect from "@/components/FsSelect/FsSelect.vue"
import FsButton from "@/components/FsButton/FsButton.vue"
import SuccessPopup from "@/popup/SuccessPopup/SuccessPopup.vue"
import fixScroll from "@/util/fixScroll"
import { mapState, mapGetters } from "vuex"

import GRecaptcha from "@/components/GRecaptcha/GRecaptcha"
import { isNeedGrecaptcha } from "@/util/grecaptchaHost"
import { getRecaptchaToken } from "@/util/grecaptchaHost"
import RegionSelect from "@/components/RegionSelect/RegionSelect"
import QtyBox from "@/components/QtyBox/QtyBox.vue"
import PolicyCheck from "@/components/PolicyCheck/PolicyCheck.vue"
export default {
    name: "supportLt",
    components: {
        FsSelect,
        ValidateError,
        SelectCountry,
        TelCode,
        UploadFile,
        FsButton,
        SuccessPopup,
        GRecaptcha,
        FsPopover,
        RegionSelect,
        QtyBox,
        PolicyCheck,
    },

    data() {
        return {
            demoType: "customer",
            agreement_lt: false,
            form: {
                entry_firstname: this.$store.state.userInfo.userInfo && this.$store.state.userInfo.userInfo.customers_firstname ? this.$store.state.userInfo.userInfo.customers_firstname : "",
                entry_lastname: this.$store.state.userInfo.userInfo && this.$store.state.userInfo.userInfo.customers_lastname ? this.$store.state.userInfo.userInfo.customers_lastname : "",
                email_address: this.$store.state.userInfo.userInfo && this.$store.state.userInfo.userInfo.customers_email_address ? this.$store.state.userInfo.userInfo.customers_email_address : "",
                //countries_id: "223",
                subject_type: "",
                comments: "",
                reviews_newImg: [],
                entry_telephone: this.$store.state.userInfo.userInfo && this.$store.state.userInfo.userInfo.customers_telephone ? this.$store.state.userInfo.userInfo.customers_telephone : "",
                type: "",
                entry_company_name: "",
                contact_type: ["1"],
                check2: false,
            },
            errors: {
                entry_firstname_error: "",
                entry_lastname_error: "",
                email_address_error: "",
                countries_id_error: "",
                subject_type_error: "",
                comments_error: "",
                entry_telephone_error: "",
                entry_company_name_errors: "",
                check2: "",
            },
            sbtn_loading: false,
            checked: false,
            // checked_error: "",
            checked_type_error: "",
            showResult: false,
            timer: null,
            caseNumber: "",
            text: this.$c("form.form.upload_file"),
            regExp: /^\d{6,}$/,
            terms_txt: this.$c("form.form.terms_of_use"),
            agree_txt02: this.$c("form.form.agree_txt02"),
            isLivechat: true,

            recaptchaTp: false,
            recaptchaVal: "",
            code: "",
            products_id: this.$route.query.product_id || "",
            qty: 1,
            dataSource: [
                {
                    name: this.$c("form.form.select_subject_options[1]"),
                    value: "1",
                },
                {
                    name: this.$c("form.form.select_subject_options[2]"),
                    value: "2",
                },
                {
                    name: this.$c("form.form.select_subject_options[3]"),
                    value: "3",
                },
                {
                    name: this.$c("form.form.select_subject_options[4]"),
                    value: "4",
                },
                {
                    name: this.$c("form.form.select_subject_options[5]"),
                    value: "5",
                },
            ],
            agree_policy: false,
            agree_policy_error: "",
        }
    },
    computed: {
        ...mapState({
            isLogin: (state) => state.userInfo.isLogin,
            select_country_id: (state) => state.selectCountry.select_country_id,
            website: (state) => state.webSiteInfo.website,
            countries_id: (state) => state.webSiteInfo.countries_id,
            language: (state) => state.webSiteInfo.language,
            pageGroup: (state) => state.ga.pageGroup,
            resourcePage: (state) => state.device.resource_page,
        }),
        ...mapGetters({
            isShowState: "selectCountry/isShowState",
            country_label: "selectCountry/country_label",
        }),
        isSg() {
            return this.website === "sg"
        },
        isDifferentArgreement() {
            return ["de", "de-en", "dn", "uk", "fr", "it", "es"].includes(this.website)
        },
    },
    mounted() {
        this.demoType == "customer" ? (this.form.type = 1) : (this.form.type = 2)
        console.log(this.$store)
        console.log(this.isShowState)
    },
    created() {
        this.terms_txt = this.terms_txt.slice(20, 31).replace("利用規約", `<a href="${this.localePath({ name: "terms-of-use" })}">利用規約</a>`)
        if (this.website == "de" || this.website == "fr") {
            this.agree_txt02 = this.agree_txt02.replace(/xxxxx/, `<a href="${this.localePath({ name: "privacy-policy" })}" target="_blank">${this.$c("form.form.privacy_policy")}</a>`)
            this.agree_txt02 = this.agree_txt02.replace(/vvvvv/, `<a href="${this.localePath({ name: "terms-of-use" })}" target="_blank">${this.$c("form.form.terms_of_use")}</a>`)
        }
        this.isLivechat = this.$route.path === "/live_chat_service_mail.html"
    },
    methods: {
        resetForm() {
            this.form = {
                entry_firstname: this.$store.state.userInfo.userInfo && this.$store.state.userInfo.userInfo.customers_firstname ? this.$store.state.userInfo.userInfo.customers_firstname : "",
                entry_lastname: this.$store.state.userInfo.userInfo && this.$store.state.userInfo.userInfo.customers_lastname ? this.$store.state.userInfo.userInfo.customers_lastname : "",
                email_address: this.$store.state.userInfo.userInfo && this.$store.state.userInfo.userInfo.customers_email_address ? this.$store.state.userInfo.userInfo.customers_email_address : "",
                //countries_id: "223",
                subject_type: "",
                comments: "",
                reviews_newImg: [],
                entry_telephone: this.$store.state.userInfo.userInfo && this.$store.state.userInfo.userInfo.customers_telephone ? this.$store.state.userInfo.userInfo.customers_telephone : "",
                type: "",
                entry_company_name: "",
                contact_type: ["1"],
                check2: false,
            }
        },
        inputCheck() {
            this.agree_policy_error = this.agree_policy ? "" : this.$c("form.form.errors.check2_error")
            return this.agree_policy_error.length
        },
        // 埋点
        loginBuried() {
            window.dataLayer &&
                window.dataLayer.push({
                    event: "uaEvent",
                    eventCategory: this.isLivechat ? this.pageGroup : "Tech Support_Technical Support Application Page",
                    eventAction: "login_button",
                    eventLabel: "Sign In",
                    nonInteraction: false,
                })
        },
        changeCode(code) {
            this.code = code
        },
        registBuried() {
            window.dataLayer &&
                window.dataLayer.push({
                    event: "uaEvent",
                    eventCategory: this.isLivechat ? this.pageGroup : "Tech Support_Technical Support Application Page",
                    eventAction: "create_account",
                    eventLabel: "Create an account",
                    nonInteraction: false,
                })
        },
        InfoBuried(label) {
            console.log(label)
            window.dataLayer &&
                window.dataLayer.push({
                    event: "uaEvent",
                    eventCategory: this.isLivechat ? this.pageGroup : "Tech Support_Technical Support Application Page",
                    eventAction: this.isLivechat ? "live_chat_service" : "solution_support",
                    eventLabel: label,
                    nonInteraction: false,
                })
        },
        close() {
            this.showResult = false
            fixScroll.unfixed()
        },
        telInput(inp) {
            this.telChange(inp)
        },
        telChange(inp) {
            this.form.entry_telephone = inp
            if (this.isSg) {
                this.errors.entry_telephone_error = ""
                return
            }
            // 判断是否为6位数字
            if (["cn"].includes(this.website)) {
                this.regExp = cn_mobile_tel
            }
            if (!inp.replace(/^\s+|\s+$/g, "")) {
                this.errors.entry_telephone_error = this.$c("form.form.errors.entry_telephone_error")
            } else if (inp.length > 0 && inp.length < 6) {
                this.errors.entry_telephone_error = this.$c("pages.NetTermsApplication.validate.phone.phone_min")
            } else if (inp.length > 40) {
                this.errors.entry_telephone_error = this.$c("pages.NetTermsApplication.validate.phone.phone_validate")
            } else {
                this.errors.entry_telephone_error = ""
            }
        },
        // countryFu(item) {
        //     this.form.countries_id = item.countries_id;
        // },
        handleChange(params) {
            //this.form.reviews_newImg.push(params.files);
            console.log(params)
            this.form.reviews_newImg.splice(0, this.form.reviews_newImg.length, ...params.files)

            console.log(this.form.reviews_newImg)
        },
        // changeAgreeFu() {
        //     this.checked = !this.checked
        //     if (!this.checked) {
        //         this.checked_error = this.$c("form.form.errors.check2_error")
        //     } else {
        //         this.checked_error = ""
        //     }
        // },
        changeType() {
            console.log(this.form.contact_type.length, "长度")
            if (!this.form.contact_type.length > 0) {
                this.checked_type_error = this.$c("form.form.select_contact")
            } else {
                this.checked_type_error = ""
            }
        },
        changeInput(attr) {
            this.blurInput(attr)
        },
        focusInput(attr, label) {
            // this.errors[attr + "_error"] = ""
            if (attr == "subject_type") {
                this.errors.subject_type_error = ""
            }
            if (label) {
                this.InfoBuried(label)
            }
        },
        blurInput(attr) {
            if (attr === "entry_firstname") {
                if (!this.form.entry_firstname.replace(/^\s+|\s+$/g, "")) {
                    this.errors.entry_firstname_error = this.$c("form.form.errors.entry_firstname_error")
                    return
                } else if (this.form.entry_firstname.length > 40) {
                    this.errors.entry_firstname_error = this.$c("form.validate.first_name.first_name_max")
                    return
                }

                this.errors.entry_firstname_error = ""
            }
            if (attr === "entry_lastname") {
                if (!this.form.entry_lastname.replace(/^\s+|\s+$/g, "")) {
                    this.errors.entry_lastname_error = this.$c("form.form.errors.entry_lastname_error")
                    return
                } else if (this.form.entry_lastname.length > 40) {
                    this.errors.entry_lastname_error = this.$c("form.validate.last_name.last_name_max")
                    return
                }
                this.errors.entry_lastname_error = ""
            }
            if (attr === "email_address") {
                if (["cn"].includes(this.website)) {
                    this.errors.email_address_error = ""
                } else {
                    if (!this.form.email_address.replace(/^\s+|\s+$/g, "")) {
                        this.errors.email_address_error = this.$c("form.form.errors.email_address_error")
                    } else if (!email_valdate.test(this.form.email_address)) {
                        this.errors.email_address_error = this.$c("form.form.errors.email_address_error01")
                    } else {
                        this.errors.email_address_error = ""
                    }
                }
            }
            if (attr === "comments") {
                if (!this.form.comments.replace(/^\s+|\s+$/g, "")) {
                    this.errors.comments_error = this.$c("form.form.errors.comments_error")
                } else {
                    this.errors.comments_error = ""
                }
            }
            if (attr === "check2") {
                if (!this.form.check2) {
                    this.errors.check2 = this.$c("form.form.errors.check2_error")
                } else {
                    this.errors.check2 = ""
                }
            }
        },
        qtyChange(n, attr, t) {
            this.qty = n
        },
        async submitFu() {
            if (this.sbtn_loading) {
                return
            }
            let flag = false
            if (!this.form.entry_firstname.replace(/^\s+|\s+$/g, "")) {
                this.errors.entry_firstname_error = this.$c("form.form.errors.entry_firstname_error")
                flag = true
            }
            if (!this.form.entry_lastname.replace(/^\s+|\s+$/g, "")) {
                this.errors.entry_lastname_error = this.$c("form.form.errors.entry_lastname_error")
                flag = true
            }
            if (["cn"].includes(this.website)) {
                this.errors.email_address_error = ""
            } else if (!this.form.email_address.replace(/^\s+|\s+$/g, "")) {
                this.errors.email_address_error = this.$c("form.form.errors.email_address_error")
                flag = true
            } else if (!email_valdate.test(this.form.email_address)) {
                this.errors.email_address_error = this.$c("form.form.errors.email_address_error01")
                flag = true
            } else {
                this.errors.email_address_error = ""
            }
            if (!this.form.entry_telephone.replace(/^\s+|\s+$/g, "") && !this.isSg) {
                this.errors.entry_telephone_error = this.$c("form.form.errors.entry_telephone_error")
                flag = true
            } else if (!this.regExp.test(this.form.entry_telephone) && !this.isSg) {
                this.errors.entry_telephone_error = this.$c("form.form.errors.entry_telephone_error01")
                flag = true
            }

            // if (!this.form.check2 && this.isDifferentArgreement) {
            //     console.log(this.form.check2)
            //     this.errors.check2 = this.$c("form.form.errors.check2_error")
            //     flag = true
            // }

            // if (this.demoType == "customer") {
            //     if (!this.form.subject_type.replace(/^\s+|\s+$/g, "") || +this.form.subject_type === 0) {
            //         this.errors.subject_type_error = this.$c("form.form.errors.subject_type_error")
            //         flag = true
            //     }
            // } else {
            //     delete this.form.subject_type
            // }
            if (!this.form.comments.replace(/^\s+|\s+$/g, "")) {
                this.errors.comments_error = this.$c("form.form.errors.comments_error")
                flag = true
            }
            if (!this.form.contact_type.length > 0) {
                this.checked_type_error = this.$c("form.form.select_contact")
                flag = true
            }
            if (!this.agree_policy) {
                this.agree_policy_error = this.$c("form.form.errors.check2_error")
                flag = true
            }
            if (flag) return
            const data = new FormData()
            if (this.form.reviews_newImg.length) {
                for (let i = 0; i < this.form.reviews_newImg.length; i++) {
                    data.append("reviews_newImg[]", this.form.reviews_newImg[i])
                }
            }
            data.append("entry_firstname", this.form.entry_firstname)
            data.append("entry_lastname", this.form.entry_lastname)
            data.append("email_address", this.form.email_address)
            if (!this.isSg) {
                data.append("countries_id", this.select_country_id)
                // if (this.$refs.regionSelect) {
                //     data.append("state", this.$refs.regionSelect.state || "")
                // }
            }
            data.append("resource_page", this.resourcePage)
            // if (this.form.type == 1) {
            //     data.append("subject_type", this.form.subject_type)
            // }
            data.append("contact_type", this.form.contact_type.join(","))
            data.append("company_name", this.form.entry_company_name)
            data.append("type", this.form.type)
            data.append("comments", this.form.comments)
            data.append("service_type", 25)
            console.log(this.products_id.trim().length, "产品长度")
            if (this.products_id.trim().length > 0) {
                data.append("product_id", this.products_id)
                data.append("product_num", this.qty)
            }
            data.append("entry_telephone", `${this.code.replace("+", "")} ${this.form.entry_telephone}`)

            // if (isNeedGrecaptcha(window.location.hostname)) {
            //     if (!this.recaptchaTp && this.$refs.grecaptcha) {
            //         this.$refs.grecaptcha.clickRecaptcha()
            //         return
            //     }
            // }
            const { recaptchaTp, headers = {}, errorMsg = "grecaptcha_error" } = await getRecaptchaToken()
            if (!recaptchaTp) {
                this.sbtn_loading = false
                this.$message.error(this.$c(`pages.Login.regist.${errorMsg}`))
                return
            }

            this.sbtn_loading = true
            this.$axios
                .post("/api/addLiveChatCase", data, { headers })
                .then((res) => {
                    this.sbtn_loading = false
                    if (res.code === 200 && res.status === "sensiWords" && this.website === "cn") {
                        for (let key in res.errors) {
                            this.errors[`${key}_error`] = this.$c("form.form.errors.sensiWords")
                        }
                        return
                    }
                    this.form = {
                        entry_firstname: "",
                        entry_lastname: "",
                        email_address: "",
                        //countries_id: "223",
                        subject_type: "",
                        comments: "",
                        reviews_newImg: [],
                        entry_telephone: "",
                    }
                    this.$refs.uploadFile.clear()
                    this.checked = false
                    this.showResult = true
                    this.$emit("success")
                    this.caseNumber = res.data.caseNumber
                    window.dataLayer &&
                        window.dataLayer.push({
                            event: "uaEvent",
                            eventCategory: this.isLivechat ? this.pageGroup : "Tech Support_Technical Support Application Page",
                            eventAction: this.isLivechat ? "live_chat_service" : "solution_support",
                            eventLabel: `Submit Success_${res.data.caseNumber}`,
                            nonInteraction: false,
                        })

                    this.$bdRequest({
                        conversionTypes: [
                            {
                                logidUrl: location.href,
                                newType: 18,
                            },
                        ],
                    })
                    setTimeout(() => {
                        this.close()
                    }, 3000)
                    this.initGrecaptcha()
                    if (this.$refs.grecaptcha) {
                        this.$refs.grecaptcha.grecaptchaError = ""
                    }
                })
                .catch((err) => {
                    this.initGrecaptcha()
                    this.sbtn_loading = false
                    if (err.status_code === 422) {
                        this.errors.entry_firstname_error = err.errors.entry_firstname ? err.errors.entry_firstname : ""
                        this.errors.entry_lastname_error = err.errors.entry_lastname ? err.errors.entry_lastname : ""
                        this.errors.email_address_error = err.errors.email_address ? err.errors.email_address : ""
                        if (!this.isSg) {
                            this.errors.countries_id_error = err.errors.countries_id ? err.errors.countries_id : ""
                        }
                        if (this.demoType == "customer") {
                            this.errors.subject_type_error = err.errors.subject_type ? err.errors.subject_type : ""
                        }
                        this.errors.comments_error = err.errors.comments ? err.errors.comments : ""
                        this.errors.entry_telephone_error = err.errors.entry_telephone ? err.errors.entry_telephone : ""
                    }
                    if (isNeedGrecaptcha(window.location.hostname)) {
                        if (err.code === 409 && this.$refs.grecaptcha) {
                            this.$refs.grecaptcha.grecaptchaError = this.$c("pages.Login.regist.grecaptcha_error")
                        }
                    }
                    if (err.code === 403) {
                        this.$message.error(err.message)
                    }

                    window.dataLayer &&
                        window.dataLayer.push({
                            event: "uaEvent",
                            eventCategory: this.isLivechat ? this.pageGroup : "Tech Support_Technical Support Application Page",
                            eventAction: this.isLivechat ? "live_chat_service" : "solution_support",
                            eventLabel: "Submit Fail",
                            nonInteraction: false,
                        })
                })
        },
        // 谷歌人机校验
        getValidateCode(val) {
            this.recaptchaTp = val.recaptchaTp
            if (val.recaptchaTp) {
                this.recaptchaVal = val.recaptchaVal
                this.submitFu()
            }
        },
        // 初始化谷歌验证
        initGrecaptcha() {
            this.recaptchaTp = false
            this.recaptchaVal = ""
        },
    },
    beforeDestroy() {
        // clearInterval(this.timer);
        // this.timer = null;
    },
}
</script>
<style scoped lang="scss">
.lt_wrap {
    /* .error_input {
        @include errorInput;
    } */

    ::v-deep .qty-box.is_new {
        width: 110px;
        height: 42px;
        .qty {
            width: 42px;
            font-size: 14px;
        }
        span {
            width: 30px;
            height: 30px;
            padding: 10px;
            .iconfont {
                font-size: 10px;
            }
        }
    }
    .txt {
        @include font14;
        color: $textColor3;
        margin: 0 0 4px 0;
        display: flex;
        // justify-content: space-between;
        align-items: center;
        // span {
        //     @include font12;
        //     color: $textColor3;
        //     i {
        //         font-style: normal;
        //         &.active {
        //             color: #c00000;
        //         }
        //     }
        // }
    }
    textarea {
        @include font13;
        &::placeholder {
            @include font13;
        }
    }

    ::v-deep .fs-select {
        // z-index: 99;
    }

    .input-item-flex {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    .input-item-number {
        display: flex;
        justify-content: flex-end;
        width: 100%;
    }
    .textarea-num {
        color: $textColor3;
        @include font12;
        margin-top: 4px;
        em {
            font-style: normal;
            &.active {
                color: $textColor4;
            }
        }
    }
    .support_alone_txt_top {
        @include font14;
        color: $textColor1;
        margin: 0 0 0 0;
    }
    .agreement-item {
        margin-top: 16px;
        .agreement {
            margin-top: 16px;
            width: 100%;
            display: flex;
            align-items: flex-start;
            input {
                margin: 0 8px 0 0;
                @media (max-width: 960px) {
                    margin-top: 1px;
                }
            }
            > p {
                @include font14;
                color: $textColor3;
                a {
                    color: #0060bf;
                }
            }
            &:hover {
                cursor: pointer;
                input[type="checkbox"] {
                    &:before {
                        color: #707070;
                    }
                }
            }
        }
        .agreement_wrap {
            @include font12;
            color: $textColor3;
            display: flex;
            align-items: center;
            margin: 16px 0;
            column-gap: 6px;
            .checkbox {
                width: 14px;
                height: 14px;
                font-size: 14px;
            }
            a {
                color: #0060bf !important;
            }
        }
    }
    .support_alone_txt {
        @include font14;
        color: $textColor1;
        margin: 12px 0 0 0;
    }
    .qty-box {
        height: 42px;
        width: 114px;
        > span {
            width: 38px;
        }
    }
    .inpbox_check {
        display: flex;
        align-items: center;
        // margin-bottom: 12px;
        cursor: pointer;
        p {
            @include font13;
            color: $textColor1;
        }
        .icon {
            margin-right: 8px;
            width: 14px;
            height: 14px;
            font-size: 14px;
        }
    }
    .inpbox_check2 {
        display: flex;
        margin-left: 24px;
        align-items: center;
        // margin-bottom: 12px;
        cursor: pointer;
        p {
            @include font13;
            color: $textColor1;
        }
        .icon {
            margin-right: 8px;
            width: 14px;
            height: 14px;
            font-size: 14px;
        }
    }
    .placeholder {
        height: 12px;
    }
    .inpbox {
        display: flex;
        justify-content: space-between;
        margin: 0 0 12px 0;
        > div {
            width: calc(50% - 6px);
        }
        &.flex_cn {
            flex-direction: row-reverse;
        }
    }
    .inpbox .inp.is_new {
        background-color: #f6f6f8; // 设置背景色
    }
    .inpbox01 .inp.is_new {
        background-color: #f6f6f8; // 设置背景色
    }

    .inpbox_head {
        display: flex;
        justify-content: space-between;
        margin: 0 0 12px 0;
        > div {
            width: calc(50% - 6px);
        }

        &.flex_cn {
            flex-direction: row-reverse;
        }
    }
    .inpbox_head .rt {
        margin-left: 12px;
    }
    .big_box {
        display: flex;
        justify-content: space-between;
        .country-box {
            width: 100%;
            > div {
                width: calc(50% - 6px);
                @include mediaM {
                    width: 100%;
                }
            }
        }
    }
    .country-box {
        display: flex;
        .inpbox01 {
            flex: 1;
            & + .inpbox01 {
                margin-left: 12px;
            }
        }
        @include mediaM {
            display: block;
            .inpbox01 + .inpbox01 {
                margin-left: 0;
            }
        }
        .selectCountry {
            ::v-deep .select-country {
                width: calc(50% - 6px);
                @media (max-width: 768px) {
                    width: 100%;
                }
            }
        }
        ::v-deep .country-wrap {
            z-index: 999;
        }
        ::v-deep .select-country .country-wrap.country-wrap-absolute {
            z-index: 999;
        }
    }
    .inpbox01 {
        margin: 0 0 12px 0;
        &.last_inpbox {
            margin: 0;
        }
        // ::v-deep .select-country {
        //     width: calc(50% - 6px);
        // }
        &::v-deep .select-country-active {
            // border-color: $borderColor6 !important;
        }
    }
    .select_half {
        width: calc(50% - 6px);
        @media (max-width: 768px) {
            width: 100%;
        }
    }
    .title {
        display: flex;
        justify-content: space-between;
    }
    .texthint {
        @include font14;
        color: $textColor1;
        margin-top: 12px;
        margin-bottom: 4px;
    }
    .upload_box {
        color: $textColor6;
        display: flex;
        align-items: center;
        margin: 12px 0 0;
        .upload_main {
            position: relative;
            display: flex;
            align-items: center;
            // &::v-deep .upload-file {
            // 	.file-box {
            // 		display: none;
            // 	}
            // }
            ::v-deep .upload-file-wrap {
                &:hover {
                    .file-btn {
                        background: #f7f7f7;
                    }
                }
                .file-btn {
                    align-items: center;
                    padding: 10px 24px;
                    background: #fafafb;
                    border-radius: 3px;
                    border: 1px solid #e5e5e5;
                }
                .iconfont-upload {
                    margin-right: 8px;
                    height: 16px;
                    line-height: 16px;
                }
                .file-info {
                    display: flex;
                    align-items: center;
                    .info-txt {
                        @include font14;
                        color: $textColor1;
                    }
                    .upload-tip-wrap {
                        width: 0;
                        display: flex;
                        align-items: center;
                    }
                }
                .fs-popover {
                    margin-left: 12px;
                }
            }
        }
        .upload_content {
            cursor: pointer;
            display: flex;
            .clip_icon {
                font-size: 14px;
                color: $textColor6;
                padding-right: 5px;
            }
            i {
                cursor: pointer;
                font-style: normal;
                @include font14;
            }
        }
    }
    ::v-deep #hidden_input_block {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        cursor: pointer;
        opacity: 0;
        .upload-file {
            cursor: pointer;
            .upload-btn {
                height: 22px;
            }
        }
    }
    ::v-deep .fs-popover {
        position: relative;
        z-index: 3;
    }
    ::v-deep .fs-popover .popper-computer {
        width: 300px;
        white-space: inherit;
        // padding: 10px;
    }
    ::v-deep .fs-button {
        width: 95px;
    }
    .files_wrap {
        display: flex;
        margin: 3px 0 0;
        > div.item {
            border: 1px solid #aeaeae;
            width: auto;
            border-radius: 3px;
            padding: 2px 8px;
            cursor: pointer;
            height: auto;
            text-align: center;
            display: inline-block;
            max-height: 100px;
            margin: 10px 15px 0 0;
            position: relative;
            span {
                display: inline-block;
                padding: 4px;
                font-weight: 400;
                @include font12;
            }
            .icon {
                display: inline-block;
                font-size: 12px;
                color: $textColor3;
                width: 12px;
                height: 12px;
                margin: 0;
                line-height: 12px;
            }
        }
    }
    .agreement_wrap {
        @include font12;
        color: $textColor3;
        display: flex;
        align-items: center;
        margin: 16px 0 0;
        column-gap: 6px;
        .checkbox {
            width: 14px;
            height: 14px;
            font-size: 14px;
        }
        ::v-deep a {
            color: $textColor6 !important;
        }
    }
    .sbtn-box {
        margin-top: 16px;
        margin-bottom: 40px;
        @media (max-width: 768px) {
            margin-bottom: 36px;
            .fs-button {
                width: 100%;
            }
        }
    }
    .success_main {
        padding: 20px 32px;
        display: flex;
        .lt {
            padding: 1px 0 0;
            .icon {
                color: #18a209;
            }
        }
        .rt {
            padding: 0 0 0 8px;
            .tit {
                @include font16;
                color: $textColor1;
            }
            .txt {
                @include font13;
                color: $textColor1;
                margin: 12px 0 0;
                display: inline-block;
            }
        }
    }
}
@media (max-width: 1024px) {
    .success_popup {
        &::v-deep > .fs-popup {
            .fs-popup-ctn {
                width: 680px !important;
                height: initial !important;
            }
        }
    }
}
@media (max-width: 768px) {
    .lt_wrap {
        .support_alone_txt {
            text-align: left;
        }
        .big_box,
        .inpbox {
            display: flex;
            gap: 12px;
            > div {
                width: 100%;
            }
        }
        .inpbox_head {
            display: flex;
        }
        .inpbox_head .lt {
            margin-bottom: 16px;
            width: 70%;
            margin-right: 16px;
        }
        .inpbox_head .rt {
            margin-bottom: 16px;
            width: 30%;
        }
    }
    .success_popup {
        &::v-deep > .fs-alert {
            .fs-alert-wrapper {
                width: calc(100% - 64px);
            }
        }
    }
}
@media (max-width: 414px) {
    .success_main {
        padding: 32px 36px 24px !important;
        flex-direction: column;
        .lt {
            padding: 0;
            text-align: center;
            .icon {
                font-size: 32px;
            }
        }
        .rt {
            padding: 12px 0 0 0 !important;
            text-align: center;
        }
    }
    .sbtn-box {
        ::v-deep .fs-button {
            width: 100%;
        }
    }
    .inpbox_head {
        display: flex;
    }
}
</style>
