<template>
    <div class="second-step-wrap">
        <!-- <div class="second-tit">{{ $c("single.transceiverTestForm.tellTit") }}</div> -->
        <div class="second-form-wrap">
            <div class="input-block" :class="{ 'input-block-cn': ['cn', 'hk', 'tw', 'mo'].includes(website) }">
                <div class="lt">
                    <p class="txt">{{ $c("form.form.first_name") }}</p>
                    <input
                        v-model.trim="form.entry_firstname"
                        :class="{ error_input: errors.entry_firstname_error }"
                        @click.stop="focusInput('entry_firstname', 'First Name')"
                        @blur="blurInput('entry_firstname')"
                        class="inp"
                        type="text" />
                    <validate-error :error="errors.entry_firstname_error"></validate-error>
                </div>
                <div class="rt">
                    <p class="txt">{{ $c("form.form.last_name") }}</p>
                    <input
                        v-model.trim="form.entry_lastname"
                        :class="{ error_input: errors.entry_lastname_error }"
                        @click.stop="focusInput('entry_lastname', 'Last Name')"
                        class="inp"
                        type="text"
                        @blur="blurInput('entry_lastname')" />
                    <validate-error :error="errors.entry_lastname_error"></validate-error>
                </div>
            </div>
            <div class="input-block">
                <div class="lt">
                    <p class="txt">{{ $c("single.ContactSales.BusinessEmail") }}</p>
                    <input
                        v-model.trim="form.email_address"
                        :class="{ error_input: errors.email_address_error }"
                        @click.stop="focusInput('email_address', 'Business Email')"
                        class="inp"
                        type="text"
                        @blur="blurInput('email_address')" />
                    <validate-error :error="errors.email_address_error.replace('XXXX', localePath({ name: 'login', query: { redirect: $route.fullPath } }))"></validate-error>
                </div>
                <div class="rt">
                    <p class="txt">{{ $c("form.form.company_name") }}</p>
                    <input
                        v-model.trim="form.company_name"
                        :class="{ error_input: errors.company_name_error }"
                        @click.stop="focusInput('company_name', 'Company Name')"
                        @blur="blurInput('company_name')"
                        class="inp"
                        type="text" />
                    <validate-error :error="errors.company_name_error"></validate-error>
                </div>
            </div>
            <div class="input-block">
                <div class="lt">
                    <p class="txt">{{ $c("single.ContactSales.CountryRegion") }}</p>
                    <select-country position="absolute" :class="{ error_input: errors.countries_id_error }" v-model="form.countries_id" @change="selectCountry"></select-country>
                    <validate-error :error="errors.countries_id_error"></validate-error>
                </div>
                <div class="rt">
                    <p class="txt">{{ $c("form.form.phone_number") }}</p>
                    <tel-code code="1" @changeCode="changeCode" :phone="form.entry_telephone" :error="errors.entry_telephone_error" @change="telChange" @point="telClick"></tel-code>
                    <validate-error :error="errors.entry_telephone_error.replace('XXXX', localePath({ name: 'login', query: { redirect: $route.fullPath } }))"></validate-error>
                </div>
            </div>
            <div class="input-block last">
                <div class="lt">
                    <p class="txt">{{ $c("form.form.industry") }}</p>
                    <select v-model="form.industry" @change="focusInput('industry', 'Industry')">
                        <option value="">{{ $c("single.ContactSales.selectIndustry") }}{{ website === "jp" ? "。" : "" }}</option>
                        <option :value="v" v-for="(v, i) in industryList" :key="i">{{ v }}</option>
                    </select>
                    <validate-error :error="errors.industry_error"></validate-error>
                </div>
                <div class="rt">
                    <p class="txt">{{ $c("form.form.company_size") }}</p>
                    <select v-model="form.company_size" @change="focusInput('company_size', 'Company Size')">
                        <option value="">{{ $c("single.ContactSales.selectSize") }}{{ website === "jp" ? "。" : "" }}</option>
                        <option :value="v" v-for="(v, i) in sizeList" :key="i">{{ v }}</option>
                    </select>
                    <validate-error :error="errors.company_size_error"></validate-error>
                </div>
            </div>
            <div
                class="agreement_wrap"
                @click.stop="clickLink($event)"
                v-html="
                    $c('single.ContactSales.submitTip')
                        .replace('XXXX1', localePath({ name: 'privacy-policy' }))
                        .replace('XXXX2', localePath({ name: 'terms-of-use' }))
                "></div>
        </div>
    </div>
</template>

<script>
import ValidateError from "@/components/ValidateError/ValidateError.vue"
import SelectCountry from "@/components/SelectCountry/SelectCountry.vue"
import TelCode from "@/components/TelCode/TelCode.vue"
import { email_valdate, cn_mobile_tel } from "@/constants/validate.js"
import { mapState } from "vuex"
export default {
    name: "UserInfo",
    components: { ValidateError, SelectCountry, TelCode },
    data() {
        return {
            industryList: this.$c("single.ContactSales.industry"),
            sizeList: this.$c("single.ContactSales.commpanySize"),
            form: {
                entry_firstname: "",
                entry_lastname: "",
                email_address: "",
                company_name: "",
                countries_id: "",
                code: "",
                entry_telephone: "",
                industry: "",
                company_size: "",
            },
            errors: {
                entry_firstname_error: "",
                entry_lastname_error: "",
                email_address_error: "",
                company_name_error: "",
                countries_id_error: "",
                entry_telephone_error: "",
                industry_error: "",
                company_size_error: "",
            },
            regExp: /^\d{6,}$/,
        }
    },
    computed: {
        ...mapState({
            isLogin: (state) => state.userInfo.isLogin,
            select_country_id: (state) => state.selectCountry.select_country_id,
            website: (state) => state.webSiteInfo.website,
            userInfo: (state) => state.userInfo.userInfo,
        }),
    },
    created() {
        if (this.userInfo) {
            this.form.entry_firstname = this.userInfo.customers_firstname || ""
            this.form.entry_lastname = this.userInfo.customers_lastname || ""
            this.form.email_address = this.userInfo.customers_email_address || ""
            this.form.entry_telephone = this.userInfo.customers_telephone || ""
        }
    },
    methods: {
        // 国家选择
        selectCountry() {
            this.$emit("gaEvent", "basic_information", `Country / Region Drop-Down`)
        },
        // 电话输入
        changeCode(code) {
            this.form.code = code
        },
        telClick() {
            this.$emit("gaEvent", "basic_information", `Phone Number Input`)
        },
        telChange(inp) {
            this.form.entry_telephone = inp
            console.log(inp)
            // 判断是否为6位数字
            if (!inp.replace(/^\s+|\s+$/g, "")) {
                this.errors.entry_telephone_error = this.$c("form.form.errors.entry_telephone_error")
            } else {
                if (!["cn", "cn"].includes(this.website)) {
                    if (!this.regExp.test(inp)) {
                        this.errors.entry_telephone_error = this.$c("form.form.errors.entry_telephone_error01")
                    } else {
                        this.errors.entry_telephone_error = ""
                    }
                } else {
                    if (!cn_mobile_tel.test(inp)) {
                        this.errors.entry_telephone_error = this.$c("form.form.errors.entry_telephone_error01")
                    } else {
                        if (this.isLogin == 1) {
                            this.errors.entry_telephone_error = ""
                        } else {
                            this.$axios
                                .post("/api/user/isHasRegister", { customers_name: inp })
                                .then((res) => {
                                    if (res.code != 200) return
                                    const data = res.data
                                    if (data && data.is_has) {
                                        this.errors.entry_telephone_error = "账户已存在，单击此处<a href=XXXX title='登录'>登录</a>。"
                                    } else {
                                        this.errors.entry_telephone_error = ""
                                    }
                                })
                                .catch((e) => {
                                    this.errors.entry_telephone_error = ""
                                })
                        }
                    }
                }
            }
        },
        clickLink(e) {
            if (!e.target.href) return
            if (e.target.href.includes("privacy_policy")) {
                this.$emit("gaEvent", "basic_information", `Privacy Policy and Notice at Collection`)
            } else {
                this.$emit("gaEvent", "basic_information", `Terms of Use`)
            }
        },
        focusInput(attr, label) {
            this.errors[attr + "_error"] = ""
            if (attr === "industry" || attr === "company_size") {
                this.$emit("gaEvent", "basic_information", `${label} Drop-Down`)
            } else {
                this.$emit("gaEvent", "basic_information", `${label} Input`)
            }
        },
        blurInput(attr) {
            if (attr === "email_address") {
                if (!["cn", "cn"].includes(this.website)) {
                    if (!this.form.email_address.replace(/^\s+|\s+$/g, "")) {
                        this.errors.email_address_error = this.$c("form.form.errors.email_address_error")
                    } else if (!email_valdate.test(this.form.email_address)) {
                        this.errors.email_address_error = this.$c("form.form.errors.email_address_error01")
                    }
                } else {
                    if (this.form.email_address.replace(/^\s+|\s+$/g, "")) {
                        if (this.isLogin) {
                            this.errors.email_address_error = ""
                        } else {
                            this.$axios
                                .post("/api/user/isHasRegister", { customers_name: this.form.email_address })
                                .then((res) => {
                                    if (res.code != 200) return
                                    const data = res.data
                                    if (data && data.is_has) {
                                        this.errors.email_address_error = "账户已存在，单击此处<a href=XXXX title='登录'>登录</a>。"
                                    } else {
                                        this.errors.email_address_error = ""
                                    }
                                })
                                .catch((e) => {
                                    this.errors.email_address_error = ""
                                })
                        }
                    }
                }
            } else {
                if (!this.form[attr].replace(/^\s+|\s+$/g, "")) {
                    this.errors[attr + "_error"] = this.$c("form.form.errors.interest_type_error")
                    return
                }
            }
        },
        validateFn() {
            let flag = false
            if (!this.form.entry_firstname.replace(/^\s+|\s+$/g, "")) {
                this.errors.entry_firstname_error = this.$c("form.form.errors.interest_type_error")
                flag = true
            }
            if (!this.form.entry_lastname.replace(/^\s+|\s+$/g, "")) {
                this.errors.entry_lastname_error = this.$c("form.form.errors.interest_type_error")
                flag = true
            }
            if (!["cn", "cn"].includes(this.website)) {
                if (!this.form.email_address.replace(/^\s+|\s+$/g, "")) {
                    this.errors.email_address_error = this.$c("form.form.errors.email_address_error")
                    flag = true
                } else if (!email_valdate.test(this.form.email_address)) {
                    this.errors.email_address_error = this.$c("form.form.errors.email_address_error01")
                    flag = true
                }

                if (!this.form.entry_telephone.replace(/^\s+|\s+$/g, "")) {
                    this.errors.entry_telephone_error = this.$c("form.form.errors.entry_telephone_error")
                    flag = true
                } else if (!this.regExp.test(this.form.entry_telephone)) {
                    this.errors.entry_telephone_error = this.$c("form.form.errors.entry_telephone_error01")
                    flag = true
                }
            } else {
                if (!this.form.email_address.replace(/^\s+|\s+$/g, "")) {
                    this.errors.email_address_error = this.$c("form.form.errors.email_address_error")
                    flag = true
                } else if (!email_valdate.test(this.form.email_address)) {
                    this.errors.email_address_error = this.$c("form.form.errors.email_address_error01")
                    flag = true
                } else if (this.errors.email_address_error) {
                    flag = true
                }
                if (!this.form.entry_telephone.replace(/^\s+|\s+$/g, "")) {
                    this.errors.entry_telephone_error = this.$c("form.form.errors.entry_telephone_error")
                    flag = true
                } else if (!cn_mobile_tel.test(this.form.entry_telephone)) {
                    this.errors.entry_telephone_error = this.$c("form.validate.email.email_valid")
                    flag = true
                } else if (this.errors.entry_telephone_error) {
                    flag = true
                }
            }
            if (!this.form.company_name.replace(/^\s+|\s+$/g, "")) {
                this.errors.company_name_error = this.$c("form.form.errors.interest_type_error")
                flag = true
            }

            if (!this.form.industry.replace(/^\s+|\s+$/g, "")) {
                this.errors.industry_error = this.$c("form.form.errors.interest_type_error")
                flag = true
            }
            if (!this.form.company_size.replace(/^\s+|\s+$/g, "")) {
                this.errors.company_size_error = this.$c("form.form.errors.interest_type_error")
                flag = true
            }
            return flag
        },
    },
}
</script>

<style lang="scss" scoped>
.second-step-wrap {
    transition: all 0.3s;
    .second-tit {
        @include font24;
        font-weight: 600;
        margin-bottom: 24px;
    }
    .second-form-wrap {
        .input-block {
            display: flex;
            justify-content: space-between;
            margin-bottom: 16px;
            &.input-block-cn {
                flex-direction: row-reverse;
            }
            .fs-popover {
                position: relative;
                z-index: 3;
            }
            ::v-deep .error_info {
                a {
                    color: $textColor4;
                    text-decoration: underline;
                }
            }
            &.last {
                margin-bottom: 12px;
            }
            .lt,
            .rt {
                width: calc((100% - 20px) / 2);
                .txt {
                    @include font12;
                    margin-bottom: 4px;
                }
            }
        }
        .agreement_wrap {
            @include font14;
            color: $textColor3;
        }
    }
}
@media (max-width: 768px) {
    .second-step-wrap {
        .second-form-wrap {
            .input-block {
                flex-direction: column;
                justify-content: flex-start;
                margin-bottom: 16px;
                &.input-block-cn {
                    flex-direction: column-reverse;
                    .rt {
                        margin-bottom: 16px;
                    }
                    .lt {
                        margin-bottom: 0;
                    }
                }
                .lt,
                .rt {
                    width: 100%;
                    .txt {
                        @include font12;
                        margin-bottom: 4px;
                    }
                }
                .lt {
                    margin-bottom: 16px;
                }
            }
        }
    }
}
</style>
