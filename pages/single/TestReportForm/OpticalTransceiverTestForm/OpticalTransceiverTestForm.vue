<template>
    <div class="form-wrap">
        <div class="banner-wrap">
            <div class="common-wrap">
                <h2 class="tit" v-html="$c('single.transceiverTestForm.bannerTit')"></h2>
            </div>
        </div>
        <div class="crumb_list">
            <bread-crumb :list="crumbList" size="small"></bread-crumb>
        </div>
        <div class="step-wrap">
            <div class="common-wrap">
                <step-bar :list="stepList" :isCheck="stepIndex" @toggleStep="toggleStep"></step-bar>
            </div>
        </div>
        <div class="step-content-wrap">
            <div class="common-wrap">
                <div class="first-step-wrap" v-show="stepIndex === 1">
                    <div class="input-block">
                        <p class="tit">1. {{ firstStepForm[0].tit }}</p>
                        <div class="gray-input-box">
                            <div class="input-list">
                                <div class="input-item" v-for="(v, i) in firstStepForm[0].options" :key="i">
                                    <input type="checkbox" @change="typeCheckChange($event, i + 1, v.txt)" />
                                    <span class="input-txt">{{ v.txt }}</span>
                                    <fs-popover class="ctn_tip" position="bottom" :icon="false">
                                        <div slot="trigger" class="ctn_tip_trigger">
                                            <span class="iconfont">&#xe66a;</span>
                                        </div>
                                        <div class="ctn_tip_tip">
                                            {{ v.tip }}
                                        </div>
                                    </fs-popover>
                                </div>
                            </div>
                            <validate-error :error="errors.test_type_error"></validate-error>
                        </div>
                    </div>
                    <div class="input-block">
                        <p class="tit">2. {{ firstStepForm[1].tit }}</p>
                        <div class="gray-input-box">
                            <div class="input-list" :class="{ 'check-input-error': errors.specific_want_txt_error }">
                                <div class="radio-box">
                                    <div class="input-item radio">
                                        <input v-model="form.specific_want" type="radio" name="specific_radio" :value="1" @change="gaEvent('request_details', 'Select Detail_is_specific_want')" />
                                        <span class="input-txt">{{ website === "jp" ? "あり" : $c("single.transceiverTestForm.yesTxt") }}</span>
                                    </div>
                                    <div class="input-item radio marginR0">
                                        <input v-model="form.specific_want" type="radio" name="specific_radio" :value="2" @change="gaEvent('request_details', 'Select Detail_is_specific_want')" />
                                        <span class="input-txt">{{ website === "jp" ? "なし" : $c("single.transceiverTestForm.noTxt") }}</span>
                                    </div>
                                </div>
                                <div class="input-item txt" :class="website">
                                    <input
                                        :class="{ error_input: errors.specific_want_txt_error }"
                                        v-model.trim="form.specific_want_txt"
                                        type="text"
                                        :placeholder="form.specific_want === 1 ? firstStepForm[1].yesPl : firstStepForm[1].noPl"
                                        @click.stop="focusInput('specific_want_txt', 'specific_want')"
                                        @blur="blurInput('specific_want_txt')" />
                                    <textarea
                                        :class="{ error_input: errors.specific_want_txt_error }"
                                        v-model.trim="form.specific_want_txt"
                                        maxlength="5000"
                                        :placeholder="form.specific_want === 1 ? firstStepForm[1].yesPl : firstStepForm[1].noPl"
                                        @click.stop="focusInput('specific_want_txt', 'specific_want')"
                                        @blur="blurInput('specific_want_txt')"></textarea>
                                    <validate-error :error="errors.specific_want_txt_error"></validate-error>
                                </div>
                            </div>
                            <validate-error :error="errors.specific_want_error"></validate-error>
                        </div>
                    </div>
                    <div class="input-block">
                        <p class="tit">3. {{ firstStepForm[2].tit }}</p>
                        <div class="gray-input-box">
                            <div class="device-item" :class="{ last: i === form.issue_report.length - 1 }" v-for="(v, i) in form.issue_report" :key="i">
                                <div class="input-item">
                                    <span class="label-txt">{{ firstStepForm[2].txt01 }}</span>
                                    <div class="right-box">
                                        <select
                                            class="select"
                                            v-model="v.brand"
                                            @change="
                                                selectChange('brand', i)
                                                gaEvent('request_details', `Brand Drop-Down`)
                                            ">
                                            <option v-for="(item, index) in brandDeviceList" :key="index" :value="item.label" :disabled="selectBrandArr.includes(item.label)">{{ item.label }}</option>
                                        </select>
                                        <span class="iconfont del-icon" v-show="i !== 0" @click="showDelIssueReportPop(i)">&#xe65f;</span>
                                        <validate-error :error="errors.issue_report_error[i].brand_error"></validate-error>
                                    </div>
                                </div>
                                <div class="input-item">
                                    <span class="label-txt">{{ firstStepForm[2].txt02 }}</span>
                                    <div class="right-box">
                                        <div class="select-list">
                                            <div class="select-item" v-for="(item, index) in brandDeviceList[v.activeBrandIndex].list" :key="item.label">
                                                <input :ref="`device_model_check_${v.activeBrandIndex}_${index}`" type="checkbox" @change="deviceModelCheckChange($event, i, item.label)" />
                                                <span class="input-txt">{{ item.label }}</span>
                                            </div>
                                        </div>
                                        <div class="select-other" :class="{ 'check-input-error': errors.issue_report_error[i].other_detail_error }">
                                            <div class="other-check-box">
                                                <input :ref="`device_model_check_${v.activeBrandIndex}_other`" type="checkbox" @change="deviceModelCheckChange($event, i, 'other')" />
                                                <span class="input-txt">{{ $c("single.transceiverTestForm.otherTxt") }}</span>
                                            </div>
                                            <div class="other-input-box" v-show="v.device_model.includes('other')">
                                                <input
                                                    :class="{ error_input: errors.issue_report_error[i].other_detail_error }"
                                                    type="text"
                                                    v-model.trim="v.other_detail"
                                                    :placeholder="firstStepForm[2].placeholder"
                                                    @click.stop="focusInput('other_detail', 'other_detail', i)"
                                                    @blur="blurInput('other_detail', i)" />
                                                <validate-error :error="errors.issue_report_error[i].other_detail_error"></validate-error>
                                            </div>
                                        </div>
                                        <validate-error :error="errors.issue_report_error[i].device_model_error"></validate-error>
                                    </div>
                                </div>
                            </div>
                            <div class="add-btn">
                                <span class="btn" :class="{ disabled: selectBrandArr.length === brandDeviceList.length }" @click="addIssueReport()">
                                    <span class="iconfont">&#xe711;</span>
                                    <span class="txt">{{ firstStepForm[2].btnTxt }}</span>
                                </span>
                            </div>
                        </div>
                    </div>
                    <div class="input-block" v-show="isShowEquipment">
                        <p class="tit">4. {{ firstStepForm[3].tit }}</p>
                        <div class="gray-input-box">
                            <div class="input-list">
                                <div class="radio-box">
                                    <div class="input-item radio">
                                        <input v-model="form.equipment" type="radio" name="equipment_radio" :value="1" @change="gaEvent('request_details', 'Select Detail_is_accept_same_brand')" />
                                        <span class="input-txt">{{ $c("single.transceiverTestForm.yesTxt") }}</span>
                                    </div>
                                    <div class="input-item radio">
                                        <input v-model="form.equipment" type="radio" name="equipment_radio" :value="2" @change="gaEvent('request_details', 'Select Detail_is_accept_same_brand')" />
                                        <span class="input-txt">{{ $c("single.transceiverTestForm.noTxt") }}</span>
                                    </div>
                                </div>
                            </div>
                            <validate-error :error="errors.equipment_error"></validate-error>
                        </div>
                    </div>
                    <div class="input-block">
                        <p class="tit">{{ isShowEquipment ? 5 : 4 }}. {{ firstStepForm[4].tit }}</p>
                        <div class="input-item area">
                            <span class="textarea-num">
                                <em :class="{ active: form.requirements.length === 5000 }">{{ form.requirements.length }}</em
                                >/5000
                            </span>
                            <textarea
                                :class="{ error_input: errors.requirements_error }"
                                v-model.trim="form.requirements"
                                maxlength="5000"
                                class="textarea"
                                :placeholder="firstStepForm[4].placeholder"
                                @click.stop="focusInput('requirements', 'test_require')"
                                @blur="blurInput('requirements')"></textarea>
                            <validate-error :error="errors.requirements_error"></validate-error>
                            <div class="remark-list">
                                <div v-for="(v, i) in firstStepForm[4].remarks">{{ i + 1 + ". " + v }}</div>
                            </div>
                        </div>
                        <div class="input-item">
                            <upload-file
                                ref="uploadFile"
                                accept=".pdf,.doc,.docx,.xls,.xlsx,.txt,image/jpeg,image/jpg,image/png"
                                :text="$c('pages.RequestTechSupport.uploadFile')"
                                :maxSize="20 * 1024 * 1024"
                                type="file"
                                :multiple="true"
                                :limit="5"
                                @change="handleChange">
                                <fs-popover class="upload_tip" slot="tip">
                                    <p v-html="website === 'es' ? 'Formatos admitidos: PDF, JPG, PNG, DOC, DOCX, XLS, XLSX, TXT. <br> Tamaño máximo: 20M.' : $c('pages.RequestTechSupport.uploadFileTip')"></p>
                                </fs-popover>
                            </upload-file>
                        </div>
                    </div>
                </div>
                <div class="second-step-wrap" v-show="stepIndex === 2">
                    <div class="second-tit">{{ $c("single.transceiverTestForm.tellTit") }}</div>
                    <div class="second-form-wrap">
                        <div class="input-block">
                            <div class="lt">
                                <p class="txt">{{ $c("form.form.first_name") }}</p>
                                <input
                                    v-model.trim="form.entry_firstname"
                                    :class="{ error_input: errors.entry_firstname_error }"
                                    :placeholder="$c('single.ContactSales.firstPH')"
                                    @click.stop="focusInput('entry_firstname', 'First Name')"
                                    @blur="blurInput('entry_firstname')"
                                    class="inp"
                                    type="text" />
                                <validate-error :error="errors.entry_firstname_error"></validate-error>
                            </div>
                            <div class="rt">
                                <p class="txt">{{ $c("form.form.last_name") }}</p>
                                <input
                                    v-model.trim="form.entry_lastname"
                                    :class="{ error_input: errors.entry_lastname_error }"
                                    :placeholder="$c('single.ContactSales.lastPH')"
                                    @click.stop="focusInput('entry_lastname', 'Last Name')"
                                    class="inp"
                                    type="text"
                                    @blur="blurInput('entry_lastname')" />
                                <validate-error :error="errors.entry_lastname_error"></validate-error>
                            </div>
                        </div>
                        <div class="input-block">
                            <div class="lt">
                                <p class="txt">{{ $c("single.ContactSales.BusinessEmail") }}</p>
                                <input
                                    v-model.trim="form.email_address"
                                    :class="{ error_input: errors.email_address_error }"
                                    :placeholder="$c('single.ContactSales.BusinessEmailPH')"
                                    @click.stop="focusInput('email_address', 'Business Email')"
                                    class="inp"
                                    type="text"
                                    @blur="blurInput('email_address')" />
                                <validate-error :error="errors.email_address_error"></validate-error>
                            </div>
                            <div class="rt">
                                <p class="txt">{{ $c("form.form.company_name") }}</p>
                                <input
                                    v-model.trim="form.company_name"
                                    :class="{ error_input: errors.company_name_error }"
                                    :placeholder="$c('single.ContactSales.CompanyNamePH')"
                                    @click.stop="focusInput('company_name', 'Company Name')"
                                    @blur="blurInput('company_name')"
                                    class="inp"
                                    type="text" />
                                <validate-error :error="errors.company_name_error"></validate-error>
                            </div>
                        </div>
                        <div class="input-block">
                            <div class="lt">
                                <p class="txt">{{ $c("single.ContactSales.CountryRegion") }}</p>
                                <select-country position="absolute" :class="{ error_input: errors.countries_id_error }" v-model="form.countries_id" @change="selectCountry"></select-country>
                                <validate-error :error="errors.countries_id_error"></validate-error>
                            </div>
                            <div class="rt">
                                <p class="txt">{{ $c("form.form.phone_number") }}</p>
                                <!-- 已被弃置了，所以tel-code的改动这里还没有改 -->
                                <tel-code code="1" :phone="form.entry_telephone" :error="errors.entry_telephone_error" @change="telChange" @point="telClick"></tel-code>
                                <validate-error :error="errors.entry_telephone_error"></validate-error>
                            </div>
                        </div>
                        <div class="input-block last">
                            <div class="lt">
                                <p class="txt">{{ $c("form.form.industry") }}</p>
                                <select v-model="form.industry" @change="focusInput('industry', 'Industry')">
                                    <option value="">{{ $c("single.ContactSales.selectIndustry") }}{{ website === "jp" ? "。" : "" }}</option>
                                    <option :value="v" v-for="(v, i) in industryList" :key="i">{{ v }}</option>
                                </select>
                                <validate-error :error="errors.industry_error"></validate-error>
                            </div>
                            <div class="rt">
                                <p class="txt">{{ $c("form.form.company_size") }}</p>
                                <select v-model="form.company_size" @change="focusInput('company_size', 'Company Size')">
                                    <option value="">{{ $c("single.ContactSales.selectSize") }}{{ website === "jp" ? "。" : "" }}</option>
                                    <option :value="v" v-for="(v, i) in sizeList" :key="i">{{ v }}</option>
                                </select>
                                <validate-error :error="errors.company_size_error"></validate-error>
                            </div>
                        </div>
                        <div
                            class="agreement_wrap"
                            @click.stop="clickLink($event)"
                            v-html="
                                $c('single.ContactSales.submitTip')
                                    .replace('XXXX1', localePath({ name: 'privacy-policy' }))
                                    .replace('XXXX2', localePath({ name: 'terms-of-use' }))
                            "></div>
                    </div>
                </div>
                <div class="third-step-wrap" v-show="stepIndex === 3">
                    <div class="success-logo iconfont">&#xe710;</div>
                    <div class="success-tit">{{ $c("single.transceiverTestForm.success.tit") }}</div>
                    <div class="success-des" v-html="$c('single.transceiverTestForm.success.txt').replace('XXXXX', localePath({ path: `/support_ticket` }))"></div>
                    <fs-button
                        type="gray"
                        :text="$c('single.transceiverTestForm.success.btnTxt')"
                        @click="
                            $router.push({ path: localePath({ name: 'home' }) })
                            gaEvent('home_entrance', `Return to Homepage`)
                        "></fs-button>
                </div>
                <div>
                    <g-recaptcha ref="grecaptcha" @getValidateCode="getValidateCode"></g-recaptcha>
                </div>
                <div class="btn-wrap" :class="{ 'has-pre': stepIndex === 2 }" v-if="stepIndex === 1 || stepIndex === 2">
                    <fs-button class="prev-btn" v-show="stepIndex === 2" type="blackline" :text="$c('single.transceiverTestForm.preBtnTxt')" @click="stepIndex--"></fs-button>
                    <fs-button
                        :text="stepIndex === 1 ? $c('single.transceiverTestForm.nextBtnTxt') : $c('form.form.submit')"
                        @click="nextStepSubmit()"
                        :loading="loading"
                        :class="['next-btn']"
                        htmlType="submit"></fs-button>
                </div>
            </div>
        </div>
        <fs-popup-new class="delete_popup" :show="deletePopupShow" @close="closeDeletePopup" :autoContent="isMobile" :mobileWidth="mobileWidth" :title="$c('pages.Quote.Confirmation')" width="480" transition="fade">
            <template v-if="!isMobile">
                <div class="delete_popup_ctn">
                    <p class="delete_info">{{ $c("single.transceiverTestForm.delPopInfo") }}</p>
                </div>
                <template slot="footer">
                    <div class="delete_btn_box">
                        <fs-button type="blackline" @click="closeDeletePopup()">{{ $c("pages.Quote.pop.cancel") }}</fs-button>
                        <fs-button @click="delIssueReport()">{{ $c("pages.Quote.delete") }}</fs-button>
                    </div>
                </template>
            </template>
            <template v-else>
                <div class="delete_popup_ctn_m">
                    <p>{{ $c("pages.Quote.Confirmation") }}</p>
                    <p class="delete_info_m">{{ $c("single.transceiverTestForm.delPopInfo") }}</p>
                    <div class="delete_btn_box_m">
                        <span @click="closeDeletePopup()">{{ $c("pages.Quote.pop.cancel") }}</span>
                        <span @click="delIssueReport()">{{ $c("pages.Quote.delete") }}</span>
                    </div>
                </div>
            </template>
        </fs-popup-new>
    </div>
</template>

<script>
import BreadCrumb from "@/components/BreadCrumb/BreadCrumb"
import FsButton from "@/components/FsButton/FsButton"
import FsPopover from "@/components/FsPopover"
import UploadFile from "@/components/UploadFile/UploadFile"
import ValidateError from "@/components/ValidateError/ValidateError.vue"
import SelectCountry from "@/components/SelectCountry/SelectCountry.vue"
import TelCode from "@/components/TelCode/TelCode.vue"
import GRecaptcha from "@/components/GRecaptcha/GRecaptcha"
import FsPopupNew from "@/components/FsPopupNew/FsPopupNew.vue"
import StepBar from "@/components/StepBar/StepBar"
import BrandDeviceData from "./constants/BrandDeviceData"
import { mapState } from "vuex"
import { email_valdate } from "@/constants/validate.js"
import { isNeedGrecaptcha } from "@/util/grecaptchaHost"
export default {
    name: "OpticalTransceiverTestForm",
    components: { BreadCrumb, FsButton, FsPopover, UploadFile, ValidateError, SelectCountry, TelCode, GRecaptcha, FsPopupNew, StepBar },
    data() {
        return {
            loading: false,
            mobileWidth: "calc(100% - 64px)",
            crumbList: [
                {
                    name: this.$c("single.transceiverTestForm.crumbList[0]"),
                    url: "/",
                },
                {
                    name: this.$c("single.transceiverTestForm.crumbList[1]"),
                    url: "/service.html",
                },
                {
                    name: this.$c("single.transceiverTestForm.crumbList[2]"),
                    url: "",
                },
            ],
            stepIndex: 1,
            stepList: [
                {
                    text: this.$c("single.transceiverTestForm.stepList[0]"),
                },
                {
                    text: this.$c("single.transceiverTestForm.stepList[1]"),
                },
                {
                    text: this.$c("single.transceiverTestForm.stepList[2]"),
                },
            ],
            brandDeviceList: BrandDeviceData.brandDeviceList,
            firstStepForm: this.$c("single.transceiverTestForm.form"),
            industryList: this.$c("single.ContactSales.industry"),
            sizeList: this.$c("single.ContactSales.commpanySize"),
            form: {
                // first
                test_type: [],
                specific_want: 1,
                specific_want_txt: "",
                issue_report: [
                    {
                        brand: BrandDeviceData.brandDeviceList[0] && BrandDeviceData.brandDeviceList[0].label,
                        activeBrandIndex: 0,
                        device_model: [],
                        other_detail: "",
                    },
                ],
                equipment: 0,
                requirements: "",
                poFile: "",

                // second
                entry_firstname: "",
                entry_lastname: "",
                email_address: "",
                company_name: "",
                countries_id: "",
                entry_telephone: "",
                industry: "",
                company_size: "",
            },
            errors: {
                // first
                test_type_error: "",
                specific_want_error: "",
                specific_want_txt_error: "",
                issue_report_error: [
                    {
                        brand_error: "",
                        device_model_error: "",
                        other_detail_error: "",
                    },
                ],
                equipment_error: "",
                requirements_error: "",

                // second
                entry_firstname_error: "",
                entry_lastname_error: "",
                email_address_error: "",
                company_name_error: "",
                countries_id_error: "",
                entry_telephone_error: "",
                industry_error: "",
                company_size_error: "",
            },
            regExp: /^\d{6,}$/,
            recaptchaTp: false,
            recaptchaVal: "",
            deletePopupShow: false,
            delIssueReportIndex: 0,
            case_number: "",
        }
    },
    computed: {
        ...mapState({
            isLogin: (state) => state.userInfo.isLogin,
            isMobile: (state) => state.device.isMobile,
            select_country_id: (state) => state.selectCountry.select_country_id,
            website: (state) => state.webSiteInfo.website,
            language: (state) => state.webSiteInfo.language,
            pageGroup: (state) => state.ga.pageGroup,
            resourcePage: (state) => state.device.resource_page,
            userInfo: (state) => state.userInfo.userInfo,
        }),
        isShowEquipment() {
            let flag = false
            this.form.issue_report.forEach((item) => {
                if (item.device_model.includes("other")) {
                    flag = true
                }
            })
            return flag
        },
        selectBrandArr() {
            // 当前已选择过的brand值
            return this.form.issue_report.reduce((total, item, index) => {
                return (total = total.concat(item.brand))
            }, [])
        },
        isNotAllowNextStep() {
            let flag = false
            if (this.stepIndex === 1) {
                if (this.form.test_type.length === 0) {
                    flag = true
                }
                if (!this.form.specific_want_txt.replace(/^\s+|\s+$/g, "")) {
                    flag = true
                }
                this.form.issue_report.forEach((item, index) => {
                    if (item.device_model.length === 0) {
                        flag = true
                    }
                    if (!this.errors.issue_report_error[index].device_model_error.replace(/^\s+|\s+$/g, "") && item.device_model.includes("other") && !item.other_detail.replace(/^\s+|\s+$/g, "")) {
                        flag = true
                    }
                })
                if (this.isShowEquipment) {
                    if (!this.form.equipment) {
                        flag = true
                    }
                }
                if (!this.form.requirements.replace(/^\s+|\s+$/g, "")) {
                    flag = true
                }
            } else if (this.stepIndex === 2) {
                if (!this.form.entry_firstname.replace(/^\s+|\s+$/g, "")) {
                    flag = true
                }
                if (!this.form.entry_lastname.replace(/^\s+|\s+$/g, "")) {
                    flag = true
                }
                if (!this.form.email_address.replace(/^\s+|\s+$/g, "")) {
                    flag = true
                }
                if (!this.form.company_name.replace(/^\s+|\s+$/g, "")) {
                    flag = true
                }
                if (!this.form.entry_telephone.replace(/^\s+|\s+$/g, "")) {
                    flag = true
                }
                if (!this.form.industry.replace(/^\s+|\s+$/g, "")) {
                    flag = true
                }
                if (!this.form.company_size.replace(/^\s+|\s+$/g, "")) {
                    flag = true
                }
            }
            return flag
        },
    },
    created() {
        if (this.userInfo) {
            this.form.entry_firstname = this.userInfo.customers_firstname || ""
            this.form.entry_lastname = this.userInfo.customers_lastname || ""
            this.form.email_address = this.userInfo.customers_email_address || ""
            this.form.entry_telephone = this.userInfo.customers_telephone || ""
        }
    },
    methods: {
        gaEvent(eventAction, eventLabel) {
            window.dataLayer &&
                window.dataLayer.push({
                    event: "uaEvent",
                    eventCategory: this.pageGroup,
                    eventAction: eventAction,
                    eventLabel: eventLabel,
                    nonInteraction: false,
                })
        },
        toggleStep(index) {
            this.stepIndex = index
        },
        typeCheckChange(e, val, name) {
            if (e.target.checked) {
                this.form.test_type.push({
                    id: val,
                    value: name,
                })
                this.errors.test_type_error = ""
                this.gaEvent("request_details", "Select Detail_type")
            } else {
                let index = this.form.test_type.findIndex((item) => item.id === val)
                index !== -1 && this.form.test_type.splice(index, 1)
            }
        },
        // IssueReport
        selectChange(attr, i) {
            this.errors.issue_report_error[i][attr + "_error"] = ""
            let activeIndex = this.brandDeviceList.findIndex((item) => item.label === this.form.issue_report[i].brand)
            this.form.issue_report[i].activeBrandIndex = activeIndex !== -1 ? activeIndex : ""

            // 重置选中数据
            this.form.issue_report[i].device_model = []
            this.$nextTick(() => {
                this.$refs[`device_model_check_${activeIndex}_other`][0].checked = false
            })
        },
        deviceModelCheckChange(e, pIndex, val) {
            // let activeBrandIndex = this.form.issue_report[pIndex].activeBrandIndex
            if (e.target.checked) {
                this.errors.issue_report_error[pIndex]["device_model_error"] = ""
                // if (val === "other") {
                //     this.form.issue_report[pIndex].device_model = []
                //     this.brandDeviceList[activeBrandIndex].list.forEach((item, index) => {
                //         this.$refs[`device_model_check_${activeBrandIndex}_${index}`][0].checked = false
                //     })
                // } else {
                //     if (this.form.issue_report[pIndex].device_model[0] === "other") {
                //         this.form.issue_report[pIndex].device_model = []
                //         this.$refs[`device_model_check_${activeBrandIndex}_other`][0].checked = false
                //     }
                // }
                this.form.issue_report[pIndex].device_model.push(val)
                this.gaEvent("request_details", "Select Detail_Device Model")
            } else {
                let index = this.form.issue_report[pIndex].device_model.indexOf(val)
                index !== -1 && this.form.issue_report[pIndex].device_model.splice(index, 1)
            }
        },
        addIssueReport() {
            if (this.selectBrandArr.length === this.brandDeviceList.length) return

            let nextLabel = ""
            let nextIndex = ""
            this.brandDeviceList.forEach((item, index) => {
                if (!nextLabel && !this.selectBrandArr.includes(item.label)) {
                    nextLabel = item.label
                    nextIndex = index
                }
            })
            this.form.issue_report.push({
                brand: nextLabel,
                activeBrandIndex: nextIndex,
                device_model: [],
                other_detail: "",
            })
            this.errors.issue_report_error.push({
                brand_error: "",
                device_model_error: "",
                other_detail_error: "",
            })
        },
        showDelIssueReportPop(i) {
            this.delIssueReportIndex = i
            this.deletePopupShow = true
        },
        delIssueReport() {
            this.deletePopupShow = false
            this.form.issue_report.splice(this.delIssueReportIndex, 1)
            this.errors.issue_report_error.splice(this.delIssueReportIndex, 1)
        },
        closeDeletePopup() {
            this.deletePopupShow = false
        },
        // uploadFile
        handleChange(params) {
            this.form.poFile = params.files
            this.gaEvent("request_details", "Upload File")
        },
        // 国家选择
        selectCountry() {
            this.gaEvent("basic_information", `Country / Region Drop-Down`)
        },
        // 电话输入
        telClick() {
            this.gaEvent("basic_information", `Phone Number Input`)
        },
        telChange(inp) {
            this.form.entry_telephone = inp
            // 判断是否为6位数字
            if (!inp.replace(/^\s+|\s+$/g, "")) {
                this.errors.entry_telephone_error = this.$c("form.form.errors.entry_telephone_error")
            } else if (!this.regExp.test(inp)) {
                this.errors.entry_telephone_error = this.$c("form.form.errors.entry_telephone_error01")
            } else {
                this.errors.entry_telephone_error = ""
            }
        },
        clickLink(e) {
            if (e.target.href.includes("privacy_policy")) {
                this.gaEvent("basic_information", `Privacy Policy and Notice at Collection`)
            } else {
                this.gaEvent("basic_information", `Terms of Use`)
            }
        },
        focusInput(attr, label, i) {
            let eventAction = this.stepIndex === 1 ? "request_details" : "basic_information"
            if (attr === "other_detail") {
                this.errors.issue_report_error[i].other_detail_error = ""
            } else {
                this.errors[attr + "_error"] = ""
            }
            if (attr === "industry" || attr === "company_size") {
                this.gaEvent(eventAction, `${label} Drop-Down`)
            } else {
                this.gaEvent(eventAction, `${label} Input`)
            }
        },
        blurInput(attr) {
            if (attr === "other_detail") {
                let flag = true
                this.form.issue_report.forEach((item, index) => {
                    if (!this.errors.issue_report_error[index].device_model_error.replace(/^\s+|\s+$/g, "") && item.device_model.includes("other") && !item.other_detail.replace(/^\s+|\s+$/g, "")) {
                        this.errors.issue_report_error[index].other_detail_error = this.$c("single.ContactSales.fieldRequired")
                        flag = false
                    }
                })
                if (!flag) return
            } else if (attr === "email_address") {
                if (!this.form.email_address.replace(/^\s+|\s+$/g, "")) {
                    this.errors.email_address_error = this.$c("form.form.errors.email_address_error")
                } else if (!email_valdate.test(this.form.email_address)) {
                    this.errors.email_address_error = this.$c("form.form.errors.email_address_error01")
                }
            } else {
                if (!this.form[attr].replace(/^\s+|\s+$/g, "")) {
                    this.errors[attr + "_error"] = this.$c("single.ContactSales.fieldRequired")
                    return
                }
            }
        },
        // 谷歌人机校验
        getValidateCode(val) {
            this.recaptchaTp = val.recaptchaTp
            if (val.recaptchaTp) {
                this.recaptchaVal = val.recaptchaVal
                this.nextStepSubmit()
            }
        },
        // 下一步/提交
        nextStepSubmit() {
            if (this.loading) return
            if (this.validateFn()) {
                if (this.stepIndex === 1) {
                    this.gaEvent("request_details", `Next_Fail`)
                }
                return
            }

            if (this.stepIndex === 1) {
                this.loading = true
                setTimeout(() => {
                    this.loading = false
                    this.stepIndex++
                    this.gaEvent("request_details", `Next_Success`)
                }, 300)
            } else if (this.stepIndex === 2) {
                const data = new FormData()
                // first
                // data.append("type", this.form.test_type.join())
                this.form.test_type.forEach((v, i) => {
                    data.append(`type[${i}][id]`, v.id)
                    data.append(`type[${i}][value]`, v.value)
                })
                data.append("is_specific_want", this.form.specific_want)
                data.append("specific_want", this.form.specific_want_txt)
                let brand_device_models = this.form.issue_report.reduce((total, item, index) => {
                    return (total = total.concat(
                        item.device_model.reduce((cTotal, cItem) => {
                            return (cTotal = cTotal.concat({
                                brand: item.brand,
                                device_model: cItem,
                                other_detail: item.other_detail,
                            }))
                        }, [])
                    ))
                }, [])
                brand_device_models.forEach((v, i) => {
                    data.append(`brand_device_models[${i}][brand]`, v.brand)
                    data.append(`brand_device_models[${i}][device_model]`, v.device_model)
                    data.append(`brand_device_models[${i}][other_detail]`, v.other_detail)
                })
                data.append("is_accept_same_brand", this.form.equipment)
                data.append("test_require", this.form.requirements)
                this.form.poFile &&
                    this.form.poFile.forEach((v) => {
                        data.append("test_require_file[]", v)
                    })
                // second
                data.append("first_name", this.form.entry_firstname)
                data.append("last_name", this.form.entry_lastname)
                data.append("business_email", this.form.email_address)
                data.append("company_name", this.form.company_name)
                data.append("country", this.select_country_id)
                data.append("phone", this.form.entry_telephone)
                data.append("industry", this.form.industry)
                data.append("company_size", this.form.company_size)

                data.append("resource_page", this.resourcePage)

                if (isNeedGrecaptcha(window.location.hostname)) {
                    if (!this.recaptchaTp && this.$refs.grecaptcha) {
                        this.$refs.grecaptcha.clickRecaptcha()
                        return
                    }
                }

                this.loading = true
                this.$axios
                    .post("/api/opticsTestRequestPost", data)
                    .then((res) => {
                        this.loading = false
                        this.initGrecaptcha()
                        if (res.code === 200) {
                            this.case_number = res.data.case_number
                            this.stepIndex++
                            this.gaEvent("basic_information", `Submit_Success`)
                            if (this.$refs.grecaptcha) {
                                this.$refs.grecaptcha.grecaptchaError = ""
                            }
                        } else {
                            this.$message.error(res.message)
                            this.gaEvent("basic_information", `Submit_Fail_${res.message}`)
                        }
                    })
                    .catch((err) => {
                        this.loading = false
                        this.initGrecaptcha()
                        this.gaEvent("basic_information", `Submit_Fail_${err.message}`)
                        if (isNeedGrecaptcha(window.location.hostname)) {
                            if (err.code === 409 && this.$refs.grecaptcha) {
                                this.$refs.grecaptcha.grecaptchaError = this.$c("pages.Login.regist.grecaptcha_error")
                            }
                        }
                        if (err.code === 403) {
                            this.$message.error(err.message)
                        }
                    })
            }
        },
        validateFn() {
            let flag = false
            if (this.stepIndex === 1) {
                if (this.form.test_type.length === 0) {
                    this.errors.test_type_error = this.$c("single.transceiverTestForm.errors.selectError")
                    flag = true
                }
                if (!this.form.specific_want_txt.replace(/^\s+|\s+$/g, "")) {
                    this.errors.specific_want_txt_error = this.$c("single.ContactSales.fieldRequired")
                    flag = true
                }
                this.form.issue_report.forEach((item, index) => {
                    if (item.device_model.length === 0) {
                        this.errors.issue_report_error[index].device_model_error = this.$c("single.transceiverTestForm.errors.selectError")
                        flag = true
                    }
                    if (!this.errors.issue_report_error[index].device_model_error.replace(/^\s+|\s+$/g, "") && item.device_model.includes("other") && !item.other_detail.replace(/^\s+|\s+$/g, "")) {
                        this.errors.issue_report_error[index].other_detail_error = this.$c("single.ContactSales.fieldRequired")
                        flag = true
                    }
                })
                if (this.isShowEquipment) {
                    if (!this.form.equipment) {
                        this.errors.equipment_error = this.$c("single.transceiverTestForm.errors.selectError")
                        flag = true
                    }
                }
                if (!this.form.requirements.replace(/^\s+|\s+$/g, "")) {
                    this.errors.requirements_error = this.$c("single.ContactSales.fieldRequired")
                    flag = true
                }
            } else if (this.stepIndex === 2) {
                if (!this.form.entry_firstname.replace(/^\s+|\s+$/g, "")) {
                    this.errors.entry_firstname_error = this.$c("single.ContactSales.fieldRequired")
                    flag = true
                }
                if (!this.form.entry_lastname.replace(/^\s+|\s+$/g, "")) {
                    this.errors.entry_lastname_error = this.$c("single.ContactSales.fieldRequired")
                    flag = true
                }
                if (!this.form.email_address.replace(/^\s+|\s+$/g, "")) {
                    this.errors.email_address_error = this.$c("form.form.errors.email_address_error")
                    flag = true
                } else if (!email_valdate.test(this.form.email_address)) {
                    this.errors.email_address_error = this.$c("form.form.errors.email_address_error01")
                    flag = true
                }
                if (!this.form.company_name.replace(/^\s+|\s+$/g, "")) {
                    this.errors.company_name_error = this.$c("single.ContactSales.fieldRequired")
                    flag = true
                }
                if (!this.form.entry_telephone.replace(/^\s+|\s+$/g, "")) {
                    this.errors.entry_telephone_error = this.$c("form.form.errors.entry_telephone_error")
                    flag = true
                } else if (!this.regExp.test(this.form.entry_telephone)) {
                    this.errors.entry_telephone_error = this.$c("form.form.errors.entry_telephone_error01")
                    flag = true
                }
                if (!this.form.industry.replace(/^\s+|\s+$/g, "")) {
                    this.errors.industry_error = this.$c("single.ContactSales.fieldRequired")
                    flag = true
                }
                if (!this.form.company_size.replace(/^\s+|\s+$/g, "")) {
                    this.errors.company_size_error = this.$c("single.ContactSales.fieldRequired")
                    flag = true
                }
            }
            return flag
        },
        // 初始化谷歌验证
        initGrecaptcha() {
            this.recaptchaTp = false
            this.recaptchaVal = ""
        },
    },
}
</script>

<style lang="scss" scoped>
.form-wrap {
    color: $textColor1;
    .crumb_list {
        background: #f7f7f7;
        ::v-deep.bread-crumb {
            // width: 84vw;
            max-width: 960px;
            margin: 0 auto;
        }
    }
    .common-wrap {
        width: 84vw;
        max-width: 1200px;
        margin: 0 auto;
    }
    .banner-wrap {
        height: 200px;
        @include bgcover("https://img-en.fs.com/images/single/fs_form/common/banner-pc.jpg");
        display: flex;
        flex-direction: column;
        justify-content: center;
        .common-wrap {
            h2.tit {
                max-width: 960px;
                margin: 0 auto;
                @include font34;
                font-weight: 600;
                text-align: center;
            }
        }
    }
    .step-wrap {
        border-bottom: 1px solid #f2f2f2;
        .common-wrap {
            padding: 36px 0;
        }
    }
    .step-content-wrap {
        .common-wrap {
            max-width: 960px;
            padding: 56px 0 72px 0;
        }
        .error_input {
            @include errorInput;
        }
        ::v-deep .upload-file-wrap {
            &:hover {
                .file-btn {
                    background: #f7f7f7;
                }
            }
            .file-btn {
                padding: 10px 24px;
                background: #fafafb;
                border-radius: 3px;
                border: 1px solid #e5e5e5;
            }
            .iconfont-upload {
                margin-right: 8px;
            }
            .file-info {
                display: flex;
                align-items: center;
                .info-txt {
                    @include font14;
                    color: $textColor1;
                }
                .upload-tip-wrap {
                    width: 0;
                    display: flex;
                    align-items: center;
                }
            }
            .file-box {
                margin-top: 12px;
                .file-item {
                    height: initial;
                    padding: 8px 24px;
                    @include font14;
                    color: $textColor1;
                    background: #f2f2f2;
                }
                .iconfont-delete {
                    font-size: 16px;
                    color: #707070;
                    margin-left: 12px;
                    line-height: 20px;
                }
            }
        }
        .check-input-error {
            padding-bottom: 26px;
            ::v-deep .validate_error {
                position: absolute;
                left: 0;
                bottom: -26px;
            }
        }
        .first-step-wrap {
            .input-block {
                margin-bottom: 36px;
                &:last-child {
                    margin-bottom: 0;
                }
                > .tit {
                    @include font20;
                    font-weight: 600;
                    margin-bottom: 16px;
                }
                .gray-input-box {
                    padding: 24px;
                    background-color: #fafafb;
                    .input-list {
                        display: flex;
                        .input-item {
                            margin-right: 84px;
                            display: flex;
                            align-items: center;
                            .input-txt {
                                margin: 0 4px 0 8px;
                                @include font14;
                            }
                            .ctn_tip {
                                .iconfont {
                                    color: #8d8d8f;
                                }
                                .ctn_tip_tip {
                                    min-width: 300px;
                                    padding: 10px 20px;
                                }
                            }
                            input[type="text"] {
                                max-width: 795px;
                            }
                            input[type="radio"] {
                                margin-right: 0;
                            }
                            &:last-child {
                                margin-right: 0;
                            }
                            &.radio {
                                margin-right: 50px;
                                .input-txt {
                                    margin: 0 0 0 8px;
                                }
                            }
                            &.marginR0 {
                                margin-right: 0;
                            }
                            &.txt {
                                flex: 1;
                                position: relative;
                                textarea {
                                    display: none;
                                }
                                &.fr,
                                &.ru,
                                &.it {
                                    input[type="text"] {
                                        max-width: initial;
                                    }
                                }
                            }
                        }
                        .radio-box {
                            min-width: 164px;
                            display: flex;
                        }
                    }
                    .device-item {
                        .input-item {
                            display: flex;
                            margin-bottom: 24px;
                            &:last-child {
                                .right-box {
                                    padding-bottom: 24px;
                                    border-bottom: 1px solid #e5e5e5;
                                }
                            }
                            .label-txt {
                                min-width: 164px;
                                @include font14;
                                display: flex;
                                align-items: center;
                            }
                            .right-box {
                                flex: 1;
                                position: relative;
                                .select {
                                    max-width: 360px;
                                }
                                .del-icon {
                                    position: absolute;
                                    right: 0;
                                    top: 50%;
                                    transform: translateY(-50%);
                                    cursor: pointer;
                                }
                                .select-list {
                                    max-width: 795px;
                                    display: flex;
                                    flex-wrap: wrap;
                                    .select-item {
                                        width: calc(100% / 3);
                                        margin-bottom: 16px;
                                        display: flex;
                                        align-items: center;
                                        .input-txt {
                                            margin-left: 8px;
                                            @include font14;
                                        }
                                    }
                                }
                                .select-other {
                                    max-width: 795px;
                                    display: flex;
                                    align-items: center;
                                    .other-check-box {
                                        display: flex;
                                        align-items: center;
                                    }
                                    .other-input-box {
                                        flex: 1;
                                        position: relative;
                                    }
                                    .input-txt {
                                        margin-left: 8px;
                                        margin-right: 20px;
                                        @include font14;
                                    }
                                }
                            }
                            &:last-child {
                                margin-bottom: 20px;
                                .label-txt {
                                    align-items: flex-start;
                                }
                            }
                        }
                        &.last {
                            .input-item {
                                &:last-child {
                                    .right-box {
                                        padding-bottom: 0;
                                        border-bottom: none;
                                    }
                                }
                            }
                        }
                    }
                    .add-btn {
                        .btn {
                            cursor: pointer;
                            display: inline-flex;
                            align-items: center;
                            .iconfont {
                                font-size: 18px;
                            }
                            .txt {
                                @include font14;
                                margin-left: 8px;
                            }
                            &.disabled {
                                cursor: not-allowed;
                            }
                        }
                    }
                }
                > .input-item {
                    &.area {
                        position: relative;
                        .textarea-num {
                            position: absolute;
                            right: 0;
                            top: -20px;
                            color: $textColor3;
                            @include font13;
                            text-align: right;
                            em {
                                font-style: normal;
                                &.active {
                                    color: $textColor4;
                                }
                            }
                        }
                    }
                    .remark-list {
                        margin-top: 16px;
                        > div {
                            margin-bottom: 8px;
                            @include font14;
                            color: $textColor3;
                            &:last-child {
                                margin-bottom: 16px;
                            }
                        }
                    }
                    ::v-deep .upload_tip .info {
                        min-width: 300px;
                        padding: 10px 20px;
                    }
                }
            }
        }
        .second-step-wrap {
            transition: all 0.3s;
            .second-tit {
                @include font24;
                font-weight: 600;
                margin-bottom: 24px;
            }
            .second-form-wrap {
                .input-block {
                    display: flex;
                    justify-content: space-between;
                    margin-bottom: 16px;
                    &.last {
                        margin-bottom: 12px;
                    }
                    .lt,
                    .rt {
                        width: calc((100% - 20px) / 2);
                        .txt {
                            @include font14;
                            margin-bottom: 4px;
                        }
                    }
                }
                .agreement_wrap {
                    @include font14;
                    color: $textColor3;
                }
            }
        }
        .third-step-wrap {
            padding: 100px 48px;
            display: flex;
            flex-direction: column;
            align-items: center;
            transition: all 0.3s;
            .success-logo {
                display: block;
                font-size: 50px;
                color: #10a300;
            }
            .success-tit {
                @include font24;
                font-weight: 600;
                margin: 16px 0 8px 0;
            }
            .success-des {
                max-width: 660px;
                @include font14;
                color: $textColor3;
                margin-bottom: 16px;
                text-align: center;
            }
        }
        .btn-wrap {
            margin-top: 32px;
            .prev-btn {
                display: none;
            }
            .next-btn {
                // &.disabled {
                //     cursor: not-allowed;
                //     background: rgba(192, 0, 0, 0.3);
                //     &:before:hover {
                //         opacity: 0;
                //     }
                // }
            }
        }
    }
    .delete_popup {
        &::v-deep {
            .fs-popup-ctn {
                @media (max-width: 960px) {
                    height: auto;
                    width: 94%;
                }
            }
        }
        .delete_popup_ctn,
        .delete_popup_ctn_m {
            padding: 20px 32px;
            @media (max-width: 960px) {
                width: 100%;
                padding: 28px 3% 40px 3%;
            }
            .delete_info {
                @include font14;
                color: $textColor1;
            }
        }
        ::v-deep .delete_popup_ctn_m {
            padding: 0;
            p {
                display: flex;
                align-items: center;
                justify-content: center;
            }
            > p:first-child {
                margin: 32px 0 8px;
                color: $textColor1;
            }
            .delete_info_m {
                padding: 0 36px;
                margin-bottom: 24px;
                color: $textColor3;
            }
            .delete_btn_box_m {
                display: flex;
                align-items: center;
                justify-content: space-between;
                border-top: 1px solid #e5e5e5;
                > span {
                    flex: 1;
                    padding: 13px 0;
                    text-align: center;
                    color: $textColor1;
                    &:first-child {
                        border-right: 1px solid #e5e5e5;
                    }
                }
            }
        }
        .delete_btn_box {
            display: flex;
            justify-content: flex-end;
            padding: 20px 32px;
            .fs-button {
                width: auto;
                margin-left: 12px;
            }
        }
    }
}
@media (max-width: 1350px) {
    .form-wrap {
        .step-content-wrap {
            .first-step-wrap {
                .input-block {
                    .gray-input-box {
                        .input-list {
                            .input-item {
                                &.txt {
                                    &.fr,
                                    &.ru,
                                    &.it {
                                        input[type="text"] {
                                            display: none;
                                        }
                                        textarea {
                                            display: block;
                                            height: 64px;
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
@media (max-width: 1200px) {
    .form-wrap {
        .crumb_list {
            ::v-deep.bread-crumb {
                width: 94%;
            }
        }
        .common-wrap {
            width: 94%;
        }
    }
}
@media (max-width: 1024px) {
    .form-wrap {
        .common-wrap {
            width: 100%;
            padding-left: 24px;
            padding-right: 24px;
        }
        .banner-wrap {
            @include bgcover("https://img-en.fs.com/images/single/fs_form/common/banner-pad.jpg");
            .common-wrap {
                h2.tit {
                    padding-left: 24px;
                    box-sizing: content-box;
                }
            }
        }
        .step-wrap {
            border-bottom: 1px solid #f2f2f2;
            .common-wrap {
                padding: 36px 24px;
            }
        }
        .step-content-wrap {
            .common-wrap {
                padding: 56px 24px 72px 24px;
            }
            .first-step-wrap {
                .input-block {
                    .gray-input-box {
                        .device-item {
                            .input-item {
                                .right-box {
                                    .select-list {
                                        .select-item {
                                            width: calc(100% / 2);
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
@media (max-width: 960px) {
    .form-wrap {
        .step-content-wrap {
            .first-step-wrap {
                .input-block {
                    .gray-input-box {
                        .input-list {
                            .input-item {
                                .ctn_tip {
                                    .ctn_tip_tip {
                                        padding: 30px 20px;
                                    }
                                }
                            }
                        }
                    }
                    > .input-item {
                        ::v-deep .upload_tip .info {
                            padding: 30px 20px;
                        }
                    }
                }
            }
        }
    }
}
@media (max-width: 768px) {
    .form-wrap {
        .common-wrap {
            padding-left: 16px;
            padding-right: 16px;
        }
        .banner-wrap {
            height: 233px;
            @include bgcover("https://img-en.fs.com/images/single/fs_form/common/banner-m.jpg");
            .common-wrap {
                h2.tit {
                    width: 100%;
                    max-width: initial;
                    padding-left: 0;
                    text-align: center;
                    @include font24;
                }
            }
        }
        .step-wrap {
            border-bottom: 1px solid #f2f2f2;
            .common-wrap {
                padding: 36px 16px;
            }
        }
        .step-content-wrap {
            .common-wrap {
                padding: 56px 16px 72px 16px;
            }
            .first-step-wrap {
                .input-block {
                    .gray-input-box {
                        padding: 16px 8px;
                        .input-list {
                            flex-direction: column;
                            .input-item {
                                margin-right: 0;
                                margin-bottom: 24px;
                                input[type="text"] {
                                    max-width: 795px;
                                }
                                input[type="radio"] {
                                    margin-right: 0;
                                }
                                &:last-child {
                                    margin-right: 0;
                                    margin-bottom: 0;
                                }
                                &.radio {
                                    margin-bottom: 0;
                                    .input-txt {
                                        margin: 0 0 0 8px;
                                    }
                                }
                                &.txt {
                                    margin-top: 16px;
                                    input[type="text"] {
                                        display: none;
                                    }
                                    textarea {
                                        display: block;
                                        height: 64px;
                                    }
                                }
                            }
                        }
                        .device-item {
                            .input-item {
                                flex-direction: column;
                                .label-txt {
                                    margin-bottom: 12px;
                                }
                                .right-box {
                                    .select {
                                        max-width: initial;
                                    }
                                    .del-icon {
                                        top: -50%;
                                    }
                                    .select-list {
                                        .select-item {
                                            width: 100%;
                                        }
                                    }
                                    .select-other {
                                        align-items: flex-start;
                                        flex-direction: column;
                                        .other-check-box {
                                            margin-bottom: 4px;
                                        }
                                        .other-input-box {
                                            width: 100%;
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
            .second-step-wrap {
                .second-form-wrap {
                    .input-block {
                        flex-direction: column;
                        justify-content: flex-start;
                        margin-bottom: 16px;
                        .lt,
                        .rt {
                            width: 100%;
                            .txt {
                                @include font14;
                                margin-bottom: 4px;
                            }
                        }
                        .lt {
                            margin-bottom: 16px;
                        }
                    }
                }
            }
            .third-step-wrap {
                padding: 50px 24px;
            }
            .btn-wrap {
                margin-top: 32px;
                ::v-deep .fs-button {
                    width: 100%;
                }
                &.has-pre {
                    display: flex;
                    justify-content: space-between;
                    .prev-btn {
                        display: inline-block;
                    }
                    ::v-deep .fs-button {
                        width: calc((100% - 16px) / 2);
                    }
                }
            }
        }
    }
}
.input-item {
    .upload_tip {
        position: relative;
        left: 26px;
    }
}
</style>
