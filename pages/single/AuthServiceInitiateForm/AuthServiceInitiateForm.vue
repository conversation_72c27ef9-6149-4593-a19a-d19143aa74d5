<!-- 认证服务发起表单 -->
<template>
    <section class="auth-service-initiate-form" :class="{ noPading: stepIndex === 1 }">
        <!-- <div class="bread-crumb-box">
            <bread-crumb class="com-box" :list="langPackData.crumbs"></bread-crumb>
        </div> -->
        <!-- <div class="page-title">
            <h1 class="page-title-label com-box">{{ langPackData.banner.tit }}</h1>
        </div> -->
        <div class="back-m" v-if="stepIndex === 1" @click="handleBack(0)"><span class="iconfont">&#xe702;</span>{{ $c("single.communicationService.back") }}</div>
        <div :class="{ sticky: sticky }">
            <step-bar :stepIndex="stepIndex" ref="stepWrapper" @stepItemClick="stepItemClick" :stepList="stepList"></step-bar>
        </div>
        <h1 class="page-title-label com-box">{{ subtitle[stepIndex] }}</h1>
        <div class="component-box">
            <component :is="componentInstance" @handleNext="handleNext" @handleBack="handleBack" :stepIndex="stepIndex"></component>
        </div>
    </section>
</template>

<script>
// import BreadCrumb from "@/components/BreadCrumb/BreadCrumb"
import StepBar from "./components/StepBar.vue"
import RequestDetail from "./RequestDetail.vue"
import BasicInformation from "./BasicInformation.vue"
import SubmittedSuccessfully from "./SubmittedSuccessfully.vue"
import { setCookieOptions } from "@/util/util"

const PATHS = [RequestDetail, BasicInformation, SubmittedSuccessfully]
const EXPIRES_TIME = 60 * 60 * 24
export default {
    components: {
        // BreadCrumb,
        StepBar,
    },
    name: "AuthServiceInitiateForm",
    data() {
        return {
            stepIndex: 0,
            sticky: false,
        }
    },
    computed: {
        // 动态加载太慢 ，而且没有比较好的加载过度组件
        componentInstance() {
            const path = PATHS[this.stepIndex]
            if (path) {
                return path
            } else {
                return ""
            }
        },
    },
    provide() {
        return {
            langPackData: this.langPackData,
            getCacheData: this.getCacheData,
        }
    },
    asyncData({ app }) {
        const stepIndex = app.$cookies.get("auth_form_step_index") || 0
        const isExpire = app.$cookies.get("auth_form_step_index") === undefined
        return app.$axios
            .get(`/api/certificationRequest`)
            .then((res) => {
                return {
                    langPackData: res.data,
                    stepList: res.data.bar,
                    stepIndex,
                    isExpire,
                    subtitle: res.data.subtitle,
                }
            })
            .catch((error) => {})
    },
    created() {
        this.dataExpiresDispose()
        console.log(this.langPackData, 123)
    },
    mounted() {
        let stickyOffset = this.$refs.stepWrapper.$el.offsetTop
        window.addEventListener("scroll", () => {
            if (window.scrollY >= stickyOffset) {
                this.sticky = true
            } else {
                this.sticky = false
            }
        })
    },
    methods: {
        getCacheData() {
            try {
                let localStorageData = localStorage.getItem("auth_form_request_detail")
                if (localStorageData) {
                    localStorageData = JSON.parse(localStorageData)
                    return localStorageData
                }
            } catch (error) {
                console.log(error.msg)
            }
        },
        dataExpiresDispose() {
            try {
                if (this.isExpire) {
                    this.clearLocalStorageData()
                }
            } catch (error) {
                console.log(error.msg)
            }
        },
        clearLocalStorageData() {
            localStorage.removeItem("auth_form_request_detail")
        },
        keepStepIndex() {
            try {
                this.$cookies.set("auth_form_step_index", this.stepIndex)
            } catch (error) {
                console.log(error.msg)
            }
        },
        stepItemClick(index) {
            if (this.stepIndex > index) {
                this.stepIndex = index
                this.keepStepIndex()
            }
        },
        handleNext(index) {
            this.stepIndex = index
            this.keepStepIndex()
            this.scrollToYZero()
        },
        handleBack(index) {
            this.stepIndex = index
            this.keepStepIndex()
            this.scrollToYZero()
        },
        scrollToYZero() {
            this.$nextTick(() => {
                window.scrollTo(0, 0)
            })
        },
    },
}
</script>

<style lang="scss" scoped>
.auth-service-initiate-form {
    .back-m {
        display: none;
    }
    ::v-deep .com-box {
        width: 84vw;
        max-width: 960px;
        margin: 0 auto;
    }

    // .page-title {
    //     background: url("https://img-en.fs.com/images/single/fs_form/common/banner-pc.jpg") center center no-repeat;
    //     background-size: cover;
    //     .page-title-label {
    //         padding: 76px 0;
    //         @include font34();
    //         font-weight: 600;
    //         text-align: center;
    //     }
    // }
    .page-title-label {
        padding: 12px 0 36px;
        @include font24();
        font-weight: 600;
        text-align: center;
    }
    .component-box {
        padding: 0 0 72px 0;
    }

    @include mediaIpad {
        ::v-deep .com-box {
            width: 100%;
            max-width: 100%;
            padding: 0 24px;
        }
        .bread-crumb-box {
            display: none;
        }
        .page-title {
            background: url("https://img-en.fs.com/images/single/fs_form/common/banner-pad.jpg") center center no-repeat;
        }
        .page-title .page-title-label {
            padding: 76px 48px;
        }
    }
    @include mediaM {
        &.noPading {
            .step-box {
                padding-top: 0;
            }
        }
        .back-m {
            display: block;
            padding: 20px;
            font-size: 14px;
            color: $textColor1;
            cursor: pointer;
            .iconfont {
                padding-right: 8px;
                font-size: 16px;
            }
        }
        ::v-deep .com-box {
            padding: 0 16px;
        }
        .page-title {
            background: url("https://img-en.fs.com/images/single/fs_form/common/banner-m.jpg") center center no-repeat;
        }
        .page-title .page-title-label {
            padding: 76px 0;
            text-align: center;
            @include font24();
        }
        .page-title-label {
            @include font20();
        }
        .component-box {
            padding: 32px 0;
        }
    }
}
.sticky {
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 999;
    background-color: #fff;
    box-shadow: 0px 1px 5px 0px #cccccc;
    ::v-deep .step-box {
        padding: 20px 0;
    }
    @media (max-width: 768px) {
        // position: static;
        top: 48px;
        ::v-deep .step-box {
            padding: 20px 16px;
            .M-label {
                display: none;
            }
        }
    }
}
</style>
