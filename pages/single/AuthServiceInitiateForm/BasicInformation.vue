<template>
    <section class="basic-information com-box">
        <form @submit.prevent="handleNextClick" class="form">
            <!-- <p class="basic-info-title">{{ langPackDataSecond.tell_us_you }}</p> -->
            <div class="form-box" :class="{ 'form-box-cn': ['cn', 'hk', 'tw', 'mo'].includes(website) }">
                <div class="form-item">
                    <p class="label">{{ langPackDataSecond.first_name }}{{ website === "jp" ? "（必須）" : " *" }}</p>
                    <div class="inp_box">
                        <input
                            class="is_new"
                            type="text"
                            @focus.stop="buriedPointInput('First Name')"
                            @blur.stop="inputCheck('first_name')"
                            @input="inputCheck('first_name')"
                            v-model="form.first_name"
                            :placeholder="countries_id === 107 ? $c('form.form.jpFormPlaceHolder.firstName') : ''" />
                        <validate-error :error="errors.first_name"></validate-error>
                    </div>
                </div>
                <div class="form-item">
                    <p class="label">{{ langPackDataSecond.last_name }}{{ website === "jp" ? "（必須）" : " *" }}</p>
                    <div class="inp_box">
                        <input
                            class="is_new"
                            type="text"
                            @focus.stop="buriedPointInput('Last Name')"
                            @blur.stop="inputCheck('last_name')"
                            @input="inputCheck('last_name')"
                            v-model="form.last_name"
                            :placeholder="countries_id === 107 ? $c('form.form.jpFormPlaceHolder.lastName') : ''" />
                        <validate-error :error="errors.last_name"></validate-error>
                    </div>
                </div>
            </div>
            <div class="form-box">
                <div class="form-item">
                    <p class="label">{{ $c("single.ContactSales.BusinessEmail") }}{{ website === "jp" ? "（必須）" : website === "cn" ? "" : " *" }}</p>
                    <div class="inp_box">
                        <input
                            class="is_new"
                            type="text"
                            @focus.stop="buriedPointInput('Email')"
                            @blur.stop="inputCheck('business_email')"
                            @input="inputCheck('business_email')"
                            v-model="form.business_email"
                            :placeholder="countries_id === 107 ? $c('form.form.jpFormPlaceHolder.email') : ''" />
                        <validate-error :error="errors.business_email.replace('XXXX', localePath({ name: 'login', query: { redirect: $route.fullPath } }))"></validate-error>
                    </div>
                </div>
                <div class="form-item">
                    <p class="label">{{ langPackDataSecond.company_name }}</p>
                    <div class="inp_box">
                        <input
                            class="is_new"
                            type="text"
                            @focus.stop="buriedPointInput('Company Name')"
                            v-model="form.company_name"
                            maxlength="120"
                            :placeholder="countries_id === 107 ? $c('form.form.jpFormPlaceHolder.company') : ''"
                            @blur.stop="inputCheck('company_name')" />
                        <validate-error :error="errors.company_name"></validate-error>
                    </div>
                </div>
            </div>
            <div class="form-box">
                <div class="country-box">
                    <div class="form-item">
                        <p class="label">{{ $c(country_label) }}</p>
                        <div class="inp_box">
                            <select-country :isNewStyle="true" @toogleClick="buriedPointCountry" position="absolute"></select-country>
                            <validate-error :error="errors.country"></validate-error>
                        </div>
                    </div>
                    <div class="form-item" v-if="isShowState">
                        <p class="label">{{ $c("single.ProductReturnForm.state.tit") }}</p>
                        <div class="inp_box">
                            <RegionSelect :isNewStyle="true" ref="regionSelect" />
                        </div>
                    </div>
                </div>
                <div class="form-item">
                    <p class="label">{{ langPackDataSecond.phone }}{{ website === "jp" ? "（必須）" : " *" }}</p>
                    <div class="inp_box">
                        <tel-code
                            :isNewStyle="true"
                            :phone="form.phone"
                            @changeCode="changeCode"
                            @point="buriedPointInput('Phone Number')"
                            @change="phoneChange"
                            @input="telInput"
                            :placeholder="countries_id === 107 ? $c('form.form.jpFormPlaceHolder.telephone') : ''"></tel-code>
                        <validate-error :error="errors.phone.replace('XXXX', localePath({ name: 'login', query: { redirect: $route.fullPath } }))"></validate-error>
                    </div>
                </div>
            </div>

            <div class="form-box">
                <div class="form-item">
                    <p class="label">{{ langPackDataSecond.job_title }}</p>
                    <div class="inp_box">
                        <input
                            class="is_new"
                            type="text"
                            @focus.stop="buriedPointInput('Job Title')"
                            v-model="form.job_title"
                            :placeholder="countries_id === 107 ? $c('form.form.jpFormPlaceHolder.position') : ''"
                            @blur.stop="inputCheck('job_title')" />
                        <validate-error :error="errors.job_title"></validate-error>
                    </div>
                </div>
                <div class="form-item">
                    <p class="label">{{ langPackDataSecond.industry }}</p>
                    <div class="inp_box">
                        <fs-select :isNewStyle="true" :options="industryList" v-model="form.industry" @change="handleSelectClick" :other="true"></fs-select>
                        <!-- <select :placeholder="langPackDataSecond.industry_tip" @click.stop="handleSelectClick" v-model="form.industry">
                            <option value="">{{ langPackDataSecond.industry_tip }}</option>
                            <option :value="v" :data-index="i" v-for="(v, i) in industryList" :key="i">{{ v }}</option>
                        </select> -->
                    </div>
                </div>
            </div>
            <div class="form-box">
                <div class="policy_box">
                    <input v-model="form.isAgreePolicy" @change="handleBlur('isAgreePolicy')" type="checkbox" class="chk" />
                    <div
                        class="agreement_wrap"
                        @click.stop="clickLink($event)"
                        v-html="
                            $c('form.validate.aggree_policy_new')
                                .replace('AAAA', localePath({ name: 'privacy-notice' }))
                                .replace('BBBB', localePath({ name: 'terms-of-use' }))
                        "></div>
                </div>
                <validate-error :error="errors.isAgreePolicy"></validate-error>
            </div>
            <!-- <p class="foot-tips" v-html="langPackDataSecond.tip"></p> -->
            <div>
                <g-recaptcha ref="grecaptcha" @getValidateCode="getValidateCode"></g-recaptcha>
            </div>
            <div class="solution_submit">
                <!-- next -->
                <div class="back-btn" @click="handleBackClick">
                    <i class="iconfont">&#xe726;</i> <span>{{ $c("single.communicationService.back") }}</span>
                </div>

                <!-- <fs-button type="blackline" class="back-btn-m" @click="handleBackClick">{{ $c("single.communicationService.back") }}</fs-button> -->
                <fs-button class="sub_btn" :loading="btnLoading" htmlType="submit">{{ $c("single.authServiceInitiateForm.submit") }}</fs-button>
            </div>
        </form>
    </section>
</template>

<script>
import FsButton from "@/components/FsButton/FsButton"
import FsSelect from "@/components/FsSelect/FsSelect"
import ValidateError from "@/components/ValidateError/ValidateError"
import SelectCountry from "@/components/SelectCountry/SelectCountry"
import TelCode from "@/components/TelCode/TelCode"
import { email_valdate, cn_mobile_tel } from "@/constants/validate"
import { mapState, mapGetters } from "vuex"
import { setCookieOptions } from "@/util/util"
import RegionSelect from "@/components/RegionSelect/RegionSelect"
import GRecaptcha from "@/components/GRecaptcha/GRecaptcha"
import { isNeedGrecaptcha } from "@/util/grecaptchaHost"
import { mixin } from "./mixin"
export default {
    name: "BasicInformation",
    mixins: [mixin],
    components: {
        FsSelect,
        FsButton,
        ValidateError,
        SelectCountry,
        TelCode,
        RegionSelect,
        GRecaptcha,
    },
    data() {
        return {
            form: {
                first_name: "",
                last_name: "",
                business_email: "",
                company_name: "",
                country: "",
                phone: "",
                job_title: "",
                industry: "",
                isAgreePolicy: false,
            },
            errors: {
                first_name: "",
                last_name: "",
                business_email: "",
                phone: "",
                company_name: "",
                job_title: "",
                isAgreePolicy: "",
            },
            industryList: this.$c("single.ContactSales.industry"),
            btnLoading: false,
            keepData: null,
            code: "",
            recaptchaTp: false,
            recaptchaVal: "",
        }
    },
    inject: ["langPackData", "getCacheData"],
    computed: {
        ...mapState({
            select_country_id: (state) => state.selectCountry.select_country_id,
            select_country_name: (state) => state.selectCountry.select_country_name,
            website: (state) => state.webSiteInfo.website,
            userInfo: (state) => state.userInfo.userInfo,
            resource_page: (state) => state.device.resource_page,
            is_login: (state) => state.userInfo.isLogin,
            countries_id: (state) => state.webSiteInfo.countries_id,
        }),
        langPackDataSecond() {
            return this.langPackData.form.second
        },
        ...mapGetters({
            isShowState: "selectCountry/isShowState",
            country_label: "selectCountry/country_label",
        }),
    },
    watch: {
        userInfo: {
            handler() {
                this.initLoginData()
            },
            immediate: true,
        },
    },
    mounted() {
        this.getKeepData()
    },
    methods: {
        changeCode(code) {
            this.code = code
        },
        handleBackClick() {
            this.$emit("handleBack", 0)
        },
        getKeepData() {
            this.keepData = this.getCacheData()
            if (!this.keepData) {
                this.triggerHandleNext(0)
            }
        },
        initLoginData() {
            const { userInfo } = this
            if (userInfo) {
                const { customers_firstname, customers_telephone, customers_lastname, customers_email_address } = userInfo
                this.form.first_name = customers_firstname
                this.form.last_name = customers_lastname
                this.form.phone = customers_telephone
                this.form.business_email = customers_email_address
            }
        },
        telInput(inp) {
            this.phoneChange(inp)
        },
        phoneChange(p) {
            this.form.phone = p
            this.inputCheck("phone")
        },
        submitVerify() {
            let arr = []
            let attr = ["first_name", "last_name", "business_email", "phone"]
            attr.map((item) => {
                let f = this.inputCheck(item)
                arr.push(f)
            })
            this.handleBlur()
            if (this.errors.isAgreePolicy) {
                arr.push(true)
            }
            return arr.includes(true)
        },
        handleBlur() {
            this.errors.isAgreePolicy = this.form.isAgreePolicy ? "" : this.$c("form.form.errors.check2_error")
        },

        inputCheck(attr) {
            let msg = ""
            const { form, website } = this
            const val = attr === "type" ? form[attr] : form[attr]?.replace(/\s+/g, "")
            const len = val?.length
            const isJP = website === "jp"

            if (attr === "first_name") {
                if (!val) {
                    msg = this.$c("form.validate.first_name.first_name_required")
                } else {
                    if (len > 40) {
                        msg = this.$c("form.validate.first_name.first_name_max")
                    }
                    if (!isJP && len < 2) {
                        msg = this.$c("form.validate.first_name.first_name_min")
                    }
                }
            }

            if (attr === "last_name") {
                if (!val) {
                    msg = this.$c("form.validate.last_name.last_name_required")
                } else {
                    if (len > 40) {
                        msg = this.$c("form.validate.last_name.last_name_max")
                    }
                    if (!isJP && len < 2) {
                        msg = this.$c("form.validate.last_name.last_name_min")
                    }
                }
            }

            if (attr === "phone") {
                if (!val) {
                    msg = this.$c("form.validate.phone.phone_required")
                } else {
                    console.log("222========", val)
                    if (!["cn"].includes(this.website)) {
                        console.log("22222222@@@@@@")

                        const maxMinVal = website === "ru" ? 10 : 6
                        const phone_validate = website === "ru" ? /^\d{10,}$/ : /^\d{6,}$/
                        if (val.length > 0 && val.length < 6) {
                            msg = this.$c("pages.NetTermsApplication.validate.phone.phone_min")
                        } else if (val.length > 40) {
                            msg = this.$c("pages.NetTermsApplication.validate.phone.phone_validate")
                        } else {
                            msg = ""
                        }
                    } else {
                        const phone = (form[attr] || "").toString().replace(/\s+/g, "")
                        if (!cn_mobile_tel.test(phone)) {
                            msg = this.$c("form.validate.phone.phone_min")
                        } else {
                            if (this.is_login == 1) {
                                msg = ""
                            } else {
                                // 先清空错误
                                this.errors[attr] = ""
                                if (this.errors[attr]) {
                                    msg = this.errors[attr]
                                } else {
                                    this.$axios
                                        .post("/api/user/isHasRegister", { customers_name: val })
                                        .then((res) => {
                                            if (res.code != 200) return
                                            const data = res.data
                                            if (data && data.is_has) {
                                                this.errors[attr] = "账户已存在，单击此处<a href=XXXX title='登录'>登录</a>。"
                                            } else {
                                                this.errors[attr] = ""
                                            }
                                        })
                                        .catch((e) => {
                                            this.errors[attr] = ""
                                        })
                                }
                            }
                        }
                    }
                }
            }

            if (attr === "business_email") {
                if (!["cn", "cn"].includes(this.website)) {
                    if (!val) {
                        msg = this.$c("form.validate.email.email_required")
                    } else {
                        if (!email_valdate.test(val)) {
                            msg = this.$c("form.validate.email.email_valid")
                        }
                    }
                } else {
                    if (!val) {
                        this.errors[attr] = ""
                    } else if (!email_valdate.test(val)) {
                        if (this.is_login == 1) {
                            msg = ""
                        } else {
                            if (this.errors[attr]) {
                                msg = this.errors[attr]
                            } else {
                                this.$axios
                                    .post("/api/user/isHasRegister", { customers_name: val })
                                    .then((res) => {
                                        if (res.code != 200) return
                                        const data = res.data
                                        if (data && data.is_has) {
                                            x
                                            this.errors[attr] = "账户已存在，单击此处<a href=XXXX title='登录'>登录</a>。"
                                        } else {
                                            this.errors[attr] = ""
                                        }
                                    })
                                    .catch((e) => {
                                        this.errors[attr] = ""
                                    })
                            }
                        }
                    }
                }
            }

            if (attr === "job_title") {
                msg = ""
            }
            if (attr === "company_name") {
                msg = ""
            }
            this.errors[attr] = msg
            console.log(this.errors)

            return !!msg
        },
        handleSelectClick() {
            this.buriedPointWrapper(`Industry Drop-Down`)
        },
        async handleNextClick() {
            const flag = this.submitVerify()

            if (flag || this.btnLoading) {
                return
            }
            if (isNeedGrecaptcha(window.location.hostname)) {
                if (!this.recaptchaTp) {
                    this.$refs.grecaptcha.clickRecaptcha()
                    return
                }
            }
            this.btnLoading = true

            if (this.website == "cn") {
                let next = await this.sensite(this.form)
                if (next) {
                    this.btnLoading = false
                    return
                }
            }

            this.fetchSubmit()
        },
        buriedPointInput(label) {
            console.log(label, "label")

            this.buriedPointWrapper(`${label} Input`)
        },
        buriedPointCountry(show) {
            if (show) {
                this.buriedPointWrapper(`Country/Region Drop-Down`)
            }
        },
        buriedPointSubmitStatus(label) {
            this.buriedPointWrapper(`Next_${label}`)
        },
        buriedPointWrapper(eventLabel) {
            if (window.dataLayer) {
                window.dataLayer.push({
                    event: "uaEvent",
                    eventCategory: "Form Page_Certification Request Page",
                    eventAction: "basic_information",
                    eventLabel,
                    nonInteraction: false,
                })
            }
        },
        async fetchSubmit() {
            try {
                const { resource_page, select_country_id, form, keepData } = this

                let params = {
                    ...keepData,
                    type: keepData.type.map((i) => i.key).join(","),
                    ...form,
                    country: select_country_id,
                    resource_page: resource_page === 1 ? 13 : resource_page, // 默认13·
                    phone: `${this.code.replace("+", "")} ${form.phone}`,
                }
                if (this.$refs.regionSelect) {
                    params.state = this.$refs.regionSelect.state || ""
                }
                const res = await this.$axios.post("/api/certificationRequestPost", params, { headers: { "g-recaptcha-response": this.recaptchaVal } })
                if (res.code === 200) {
                    this.buriedPointSubmitStatus("Success")
                    this.triggerHandleNext()
                    this.cleanCacheData()

                    this.initGrecaptcha()
                    this.setGrecaptchaError("")
                }
            } catch (error) {
                this.initGrecaptcha()
                this.buriedPointSubmitStatus(`Fail_${error.code || error.message}`)
                if (isNeedGrecaptcha(window.location.hostname)) {
                    if (error.code === 409) {
                        this.setGrecaptchaError(this.$c("pages.Login.regist.grecaptcha_error"))
                    }
                }
                if (error.code === 403 || error.code === 400) {
                    this.$message.error(error.message)
                }
            }
            this.btnLoading = false
        },
        setGrecaptchaError(msg) {
            if (this.$refs.grecaptcha) {
                this.$refs.grecaptcha.grecaptchaError = msg
            }
        },
        triggerHandleNext(index = 2) {
            this.$emit("handleNext", index)
        },
        cleanCacheData() {
            localStorage.removeItem("auth_form_request_detail")
            this.$cookies.remove("auth_form_step_index")
        },
        // 谷歌人机校验
        getValidateCode(val) {
            this.recaptchaTp = val.recaptchaTp
            if (val.recaptchaTp) {
                this.recaptchaVal = val.recaptchaVal
                this.handleNextClick()
            }
        },
        // 初始化谷歌验证
        initGrecaptcha() {
            this.recaptchaTp = false
            this.recaptchaVal = ""
        },
    },
}
</script>
<style scoped lang="scss">
.basic-information {
    .basic-info-title {
        @include font20();
        font-weight: 600;
    }
    .form-box {
        display: flex;
        justify-content: space-between;
        &.form-box-cn {
            display: flex;
            justify-content: space-between;
            flex-direction: row-reverse;
            grid-template-columns: unset;
            grid-column-gap: unset;
        }
        .country-box {
            display: flex;
            width: calc((100% - 4px) / 2);
            .form-item {
                & + .form-item {
                    margin-left: 12px;
                }
            }
            @include mediaM {
                display: block;
                width: 100%;
                .form-item + .form-item {
                    margin-left: 0;
                }
            }
        }
        .form-item {
            width: calc((100% - 20px) / 2);

            margin-top: 16px;
            max-width: 588px;

            .label {
                @include font12();
                color: #707070;
                margin-bottom: 4px;
            }
            ::v-deep .error_info {
                a {
                    color: $textColor4;
                    text-decoration: underline;
                }
            }
        }
        ::v-deep {
            .tel-code .menu .menu-list,
            .select-country .country-wrap .country-box {
                max-height: 275px;
            }
        }
        ::v-deep .select-menu .menu-list {
            max-height: 270px;
        }
        &:has(.policy_box) {
            flex-direction: column;
        }
        .policy_box {
            margin-top: 16px;
            display: flex;
            .chk {
                margin-top: 4px;
                margin-right: 8px;
                width: 14px;
                height: 14px;
                font-size: 14px;
            }
            .agreement_wrap {
                @include font14;
                color: $textColor3;
            }
        }
    }
    .foot-tips {
        color: #707070;
        @include font12();
        margin-top: 12px;
        a {
            color: #0060bf;
            &:hover {
                text-decoration: underline;
            }
        }
    }
    .solution_submit {
        display: flex;
        align-items: center;
        justify-content: space-between;
        max-width: 960px;
        margin: 32px auto 36px;
        @media (max-width: 960px) {
            margin: 0;
        }
        .back-btn {
            display: flex;
            align-items: center;
            @include font14();
            color: #0060bf;
            cursor: pointer;
            &:hover {
                span {
                    text-decoration: underline;
                }
            }
            .iconfont {
                @include font16();
                transform: rotate(-180deg);
                margin-right: 4px;
            }
        }
        .back-btn-m {
            display: none;
        }

        @media (max-width: 768px) {
            ::v-deep .fs-button {
                width: calc(50% - 8px);
            }
            .back-btn-m {
                display: block;
            }

            .back-btn {
                display: none;
            }
            .sub_btn {
                width: 100%;
            }
        }
    }
    ::v-deep .fs-button {
        margin-top: 32px;
        @media (max-width: 768px) {
            width: 100%;
        }
    }
    @include mediaM {
        .form-box {
            display: block;
            &.form-box-cn {
                flex-direction: column-reverse;
                .form-item {
                    width: 100%;
                }
            }
            .form-item {
                width: 100%;
                max-width: none;
            }
        }
    }
}
</style>
