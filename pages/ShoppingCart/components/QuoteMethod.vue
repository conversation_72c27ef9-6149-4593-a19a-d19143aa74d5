<template>
    <fs-popup-new class="quote_method" v-bind="$attrs" v-on="$listeners" isNew isMDrawer :title="$c('pages.Quote.quoteProcess.quote')" width="680" :isMDrawer="true">
        <div class="content">
            <div class="top">
                <h4>{{ $c("pages.Quote.quoteProcess.getQuoteTit") }}</h4>
                <p>{{ $c("pages.Quote.quoteProcess.getQuoteDes") }}</p>
                <fs-button :text="$c('pages.Quote.quoteProcess.getQuoteNow')" type="black" @click="getQuoteNow"></fs-button>
            </div>
            <div class="bot">
                <h4>{{ $c("pages.Quote.quoteProcess.requestQuoteTit") }}</h4>
                <p>{{ $c("pages.Quote.quoteProcess.requestQuoteDes") }}</p>
                <fs-button :text="$c('pages.Quote.quoteProcess.requestQuote')" type="blackline" @click="requestQuoteChange"></fs-button>
            </div>
        </div>
    </fs-popup-new>
</template>

<script>
import FsPopupNew from "@/components/FsPopupNew/FsPopupNew.vue"
import FsButton from "@/components/FsButton/FsButton.vue"
export default {
    name: "QuoteMethod",
    components: {
        FsPopupNew,
        FsButton,
    },
    props: {
        requestQuoteShow: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {}
    },
    methods: {
        getQuoteNow() {
            this.burryPoint("Creat Quote")
            this.$emit("getQuoteNow")
        },
        requestQuoteChange() {
            this.$emit("update:requestQuoteShow", true)
            this.$emit("update:show", false)
            this.burryPoint("Request Quote")
        },
        burryPoint(eventLabel) {
            window.dataLayer &&
                window.dataLayer.push({
                    event: "uaEvent",
                    eventCategory: "Cart Page",
                    eventAction: "create_quote",
                    eventLabel,
                    nonInteraction: false,
                })
        },
    },
}
</script>

<style lang="scss" scoped>
.quote_method {
    ::v-deep .fs-popup {
        .content {
            padding-bottom: 24px;
            @include mediaM {
                padding-bottom: 20px;
                button {
                    width: 100%;
                }
            }
            h4 {
                @include font16();
                color: $textColor1;
            }
            p {
                @include font14();
                color: $textColor3;
                margin: 8px 0 16px 0;
            }
            .top {
                padding-bottom: 16px;
            }
            .bot {
                padding-top: 16px;
                border-top: 1px solid #e5e5e5;
            }
        }
    }
}
</style>
