<template>
    <div class="recently_viewed" v-if="list && list.length" :class="`recently_viewed_${type}`">
        <div class="recently_viewed_box">
            <div class="title">
                <span>{{ $c("components.smallComponents.recentlyViewed") }}</span>
                <span class="iconfont iconfont_down" :class="{ iconfont_down_up: show_list }" @click="show_list = !show_list">&#xe704;</span>
            </div>
            <!-- PC -->
            <div class="content">
                <swiper ref="recently" :options="swiperOption" v-if="list && list.length">
                    <swiper-slide v-for="(item, index) in list" :key="item.product_id">
                        <div class="item_box">
                            <nuxt-link target="_blank" class="img_box" :to="localePath({ name: 'products', params: { id: item.products_id } })">
                                <img :src="item.image" />
                            </nuxt-link>
                            <div class="bottom_box">
                                <nuxt-link target="_blank" class="name" :to="localePath({ name: 'products', params: { id: item.products_id } })">{{ item.products_name }}</nuxt-link>
                                <div class="other">
                                    <div class="price" v-html="item.products_price_str"></div>
                                    <fs-button type="red" class="item_add" @click="addToCart(item)">
                                        <i class="icon iconfont">&#xe64c;</i>
                                        <span class="label">{{ $c("pages.ShoppingCart.newCart.add") }}</span>
                                    </fs-button>
                                </div>
                            </div>
                        </div>
                    </swiper-slide>
                    <div class="swiper-pagination" slot="pagination" v-show="list.length > 5"></div>
                </swiper>
                <div class="swiper-btn-box">
                    <div class="swiper-button-prev RecentlyPrev" v-show="list.length > 5" slot="button-prev"></div>
                    <div class="swiper-button-next RecentlyNext" v-show="list.length > 5" slot="button-next"></div>
                </div>
            </div>
            <!-- M -->
            <div class="content_m" v-if="!show_list">
                <div class="list_box">
                    <div class="item_box" v-for="(item, index) in list_show_more ? list : list.slice(0, 6)" :key="item.product_id">
                        <nuxt-link target="_blank" class="img_box" :to="localePath({ name: 'products', params: { id: item.products_id } })">
                            <img :src="item.image" />
                        </nuxt-link>
                        <div class="bottom_box">
                            <nuxt-link target="_blank" class="name" :to="localePath({ name: 'products', params: { id: item.products_id } })">{{ item.products_name }}</nuxt-link>
                            <div class="other">
                                <div class="price" v-html="item.products_price_str"></div>
                                <fs-button type="red" class="item_add" @click="addToCart(item)">
                                    <i class="icon iconfont">&#xe64c;</i>
                                    <span class="label">{{ $c("pages.ShoppingCart.newCart.add") }}</span>
                                </fs-button>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="spotlights_box" v-if="list.length > 6">
                    <span class="spotlights_btn" @click.stop="toogleList">
                        <span class="spotlights_btn_info">{{ list_show_more ? $c("pages.Products.See_less") : $c("pages.Products.See_more") }}</span>
                        <span class="iconfont iconfont_arrow" :class="{ iconfont_arrow_down: list_show_more }">&#xe704;</span>
                    </span>
                </div>
            </div>
        </div>
    </div>
</template>
<script>
import SlideDown from "@/components/SlideDown/SlideDown.vue"
import FsButton from "@/components/FsButton/FsButton.vue"
import { Swiper, SwiperSlide } from "vue-awesome-swiper"
import { setCookieOptions } from "@/util/util"
export default {
    name: "RecentlyViewed",
    components: {
        Swiper,
        SwiperSlide,
        SlideDown,
        FsButton,
    },
    props: {
        type: {
            type: String,
            default: "cart",
        },
    },
    data() {
        return {
            list: [],
            swiperOption: {
                slidesPerView: 5,
                spaceBetween: 12,
                slidesPerGroup: 5,
                loop: false,
                loopFillGroupWithBlank: true,
                pagination: {
                    el: ".swiper-pagination",
                    clickable: true,
                },
                navigation: {
                    nextEl: ".RecentlyNext",
                    prevEl: ".RecentlyPrev",
                },
                noSwipingClass: "swiper-no-swiping",
            },
            list_show_more: false,
            show_list: false,
        }
    },
    computed: {
        swiper() {
            return this.$refs.recently.$swiper
        },
    },
    created() {
        if (process.client) {
            let recentlyProducts = JSON.parse(window.localStorage.getItem("recentlyProducts"))
            if (recentlyProducts && recentlyProducts.length) {
                this.$axios
                    .post("/api/special_index", {
                        products_ids: recentlyProducts,
                        products_status: 1,
                        page: this.type !== "cart" ? "product_info" : undefined,
                    })
                    .then((res) => {
                        if (res.data && res.data.length) {
                            this.list.splice(0, this.list.length, ...res.data)
                            let arr = []
                            res.data.map((item) => {
                                arr.push(item.products_id)
                            })
                            this.$cookies.set("recentlyProducts", arr)
                        }
                    })
            }
        }
    },
    mounted() {},
    methods: {
        // 展开收起
        toogleList() {
            this.list_show_more = !this.list_show_more
        },
        // 加购
        addToCart(data) {
            this.$emit("quickAdd", data)
        },
        // 格式化评分
        format(num) {
            if (num) {
                return Math.ceil(num)
            } else {
                return 0
            }
        },
    },
}
</script>
<style lang="scss" scoped>
.recently_viewed {
    margin-top: 48px;
    .recently_viewed_box {
        .title {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 20px 0 0;
            > span {
                @include font16;
                color: $textColor1;
                font-weight: 600;
            }
            .iconfont_down {
                display: none;
                color: $textColor3;
                @include font14;
                transition: all 0.3s;
                transform: rotateX(-180deg);
                &.iconfont_down_up {
                    transform: rotateX(0);
                }
            }
        }
        .content,
        .content_m {
            position: relative;
            padding-bottom: 40px;
            &:hover {
                ::v-deep {
                    .swiper-button-prev,
                    .swiper-button-next {
                        opacity: 1;
                    }
                }
            }
            .item_box {
                height: 100%;
                border-radius: 4px;
                background: #fafbfb;
                padding: 8px 20px 20px;
                display: flex;
                flex-direction: column;
                position: relative;
                transition: all 0.2s;
                &:hover {
                    background: #ffffff;
                    box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.1);
                    .bottom_box .other .item_add {
                        display: block;
                    }
                }
                .img_box {
                    > img {
                        width: 140px;
                        max-width: 100%;
                        height: 140px;
                        display: block;
                        margin: 0 auto;
                        mix-blend-mode: multiply;
                    }
                }
                .bottom_box {
                    flex: 1;
                    display: flex;
                    flex-direction: column;
                    justify-content: space-between;

                    @include font14;
                    color: $textColor1;
                    .name {
                        font-weight: 600;
                        color: $textColor1;
                        @include txt-more-hid;
                    }
                    .other {
                        margin-top: 7px;
                        display: flex;
                        align-items: center;
                        justify-content: space-between;
                        min-height: 32px;
                        gap: 8px;
                        .price {
                            font-weight: 600;
                            word-break: break-word;
                        }
                        .item_add {
                            display: none;
                            height: 32px;
                            padding: 0 8px;
                            position: absolute;
                            bottom: 20px;
                            right: 20px;
                            .iconfont {
                                font-size: 12px;
                                line-height: 1;
                            }
                            .label {
                                margin-left: 4px;
                            }
                        }
                    }
                }
            }
        }
        .content_m {
            display: none;
            .spotlights_box {
                display: flex;
                justify-content: center;
                margin-top: 12px;
                .spotlights_btn {
                    display: flex;
                    color: $textColor6;
                    cursor: pointer;
                    align-items: center;
                    justify-content: center;
                    &:hover {
                        .spotlights_btn_info {
                            text-decoration: underline;
                        }
                    }
                    .spotlights_btn_info {
                        @include font13;
                    }
                    .iconfont_arrow {
                        display: inline-block;
                        width: 12px;
                        height: 12px;
                        font-size: 12px;
                        margin: 0 0 0 8px;
                        transition: all 0.3s;
                        &.iconfont_arrow_down {
                            transform: rotateX(180deg);
                        }
                    }
                }
            }
        }
    }
}
::v-deep {
    .swiper-btn-box {
        width: 100%;
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        .swiper-button-prev {
            color: #fff;
            font-size: 20px;
            width: 40px;
            height: 40px;
            text-align: center;
            background: rgba(0, 0, 0, 0.2);
            border-radius: 50%;
            transition: all 0.3s;
            left: -80px;
            right: auto;
            margin-top: -54px;
            opacity: 0;
            z-index: 10;
            pointer-events: auto;

            @media (max-width: 1024px) {
                // display: none;
                left: 12px;
                opacity: 1;
            }

            &:after {
                content: "\f048";
                font-family: "iconfont";
                font-size: 20px;
            }

            &:hover {
                background: rgba(0, 0, 0, 0.4);
            }
        }

        .swiper-button-next {
            color: #fff;
            font-size: 20px;
            width: 40px;
            height: 40px;
            margin-top: -54px;
            text-align: center;
            background: rgba(0, 0, 0, 0.2);
            right: -80px;
            left: auto;
            border-radius: 50%;
            transition: all 0.3s;
            z-index: 10;
            opacity: 0;
            pointer-events: auto;

            @media (max-width: 1024px) {
                // display: none;
                right: 12px;
                opacity: 1;
            }

            &:after {
                content: "\f047";
                font-family: "iconfont";
                font-size: 20px;
            }

            &:hover {
                background: rgba(0, 0, 0, 0.4);
            }
        }
        .swiper-button-disabled {
            background: rgba(0, 0, 0, 0.05);
            &:hover {
                background: rgba(0, 0, 0, 0.05);
            }
        }
    }
    .swiper-container {
        z-index: 0;
        padding: 12px 12px 28px;
        margin: 0 -12px;
        .swiper-slide {
            height: auto;
            > a {
                text-decoration: none;
            }
        }
        .swiper-pagination {
            bottom: 0;
            display: flex;
            justify-content: center;

            .swiper-pagination-bullet {
                opacity: 0.4;
                transition: all 0.3s;
                background: #707070;
                vertical-align: bottom;

                &.swiper-pagination-bullet-active {
                    background: #707070;
                    width: 20px;
                    border-radius: 4px;
                    opacity: 1;
                }
            }
        }
    }
}
@include padMobile {
    .recently_viewed .recently_viewed_box {
        .content,
        .content_m {
            .item_box .bottom_box .other {
                min-height: 26px;
                .item_add {
                    height: 26px;
                    .iconfont {
                        font-size: 14px;
                    }
                    .label {
                        display: none;
                    }
                }
            }
        }
    }
}
@include mobile {
    .recently_viewed .recently_viewed_box {
        .title {
            padding: 12px 0;
            .iconfont_down {
                display: block;
            }
        }
        .content {
            display: none;
        }
        .content_m {
            display: block;
            .list_box {
                display: grid;
                grid-template-columns: repeat(2, 1fr);
                grid-gap: 12px;
                padding-bottom: 0;
            }
            .item_box .bottom_box .other .item_add {
                position: static;
                top: 0;
                right: 0;
                display: block;
            }
        }
    }
}
</style>
