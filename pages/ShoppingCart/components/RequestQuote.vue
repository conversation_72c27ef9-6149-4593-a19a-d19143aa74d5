<template>
    <fs-popup-new class="request_quote" v-bind="$attrs" v-on="$listeners" width="650" :show="show" isNew isMDrawer @close="handleClose">
        <div slot="header" class="header">
            <p class="title">{{ isSubmitSuccess ? $c("pages.Quote.quoteProcess.successHead") : $c("pages.Quote.quoteProcess.requestQuote") }}</p>
        </div>
        <div v-if="!isSubmitSuccess">
            <div class="content">
                <div class="product-list">
                    <div class="product-item" v-for="(item, index) in productList" :key="index">
                        <div class="product-item-de">
                            <img :src="item.products_info.image" class="product-img" />
                            <div class="txt-box">
                                <div class="tit">{{ item.products_info.productName }}</div>
                                <p class="txt">
                                    <span class="pn">{{ $c("pages.ShoppingCart.FS_PN") }}{{ item.products_info.productsModel }}</span>
                                    <span class="product-id"
                                        >{{ Array.isArray(item.products_info?.products_extended_warranty) && item.products_info?.products_extended_warranty.length === 0 ? "SKU" : "SVC" }}:{{
                                            parseInt(item.products_id)
                                        }}</span
                                    >
                                </p>
                            </div>
                            <div class="handle-box">
                                <qty-box :num="item.qty" isNewStyle :attr="index" @change="qtyChange" :max="item.fs_qty_max"></qty-box>
                                <div class="del-box" :class="{ abled: productList.length === 1 }" @click="deletePro(index)">
                                    <i class="iconfont">&#xe65f;</i>
                                </div>
                            </div>
                        </div>
                        <!-- 定制产品属性 -->
                        <FoldWrapper v-show="item.fs_cart_attr && item.fs_cart_attr.length" class="common-fold-box" :foldedLabel="$c('pages.Products.See_more')" :unfoldLabel="$c('pages.Products.See_less')">
                            <div class="attrs-content">
                                <p class="attrs-item" v-for="a in item.fs_cart_attr" :key="a">{{ a }}</p>
                            </div>
                        </FoldWrapper>

                        <!-- 服务器定制属性 -->
                        <FoldWrapper
                            v-if="item.server_attributes && item.server_attributes.length"
                            class="common-fold-box"
                            :foldedLabel="$c('pages.ShoppingCart.serverViewFillSpecs')"
                            :unfoldLabel="$c('pages.ShoppingCart.serverSeeMore')">
                            <server-attributes isNewStyle :list="item.server_attributes"></server-attributes>
                        </FoldWrapper>

                        <!-- 组合产品 -->
                        <FoldWrapper
                            v-show="item.is_show_composite_related && item.compositeRelated && item.compositeRelated.length"
                            class="common-fold-box"
                            :foldedLabel="$c('pages.ShoppingCart.item_includes_following_products')"
                            :unfoldLabel="$c('pages.ShoppingCart.item_includes_following_products')">
                            <div class="related-box">
                                <div class="related-item" v-for="rel in item.compositeRelated" :key="rel.id">
                                    <div class="releated-item-img" :style="{ backgroundImage: `url(${rel.productInfo.images_arr})` }"></div>
                                    <div class="related-detail">
                                        <div class="related-flex">
                                            <div class="related-title" :style="item.is_show_composite_product_price && rel.productInfo && rel.productInfo.products_price_str ? 'max-width: 206px;' : 'max-width: 262px;'">
                                                {{ rel.productInfo.products_desc.products_name }}
                                            </div>
                                            <div class="related-id" v-if="item.is_show_composite_product_id">SKU:{{ rel.productInfo.products_desc.products_id }}</div>
                                        </div>
                                        <div class="related-num" v-if="item.is_show_composite_related">
                                            <span
                                                class="related-price"
                                                v-if="item.is_show_composite_product_price && rel.productInfo && rel.productInfo.products_price_str"
                                                v-html="item.is_show_composite_product_price && rel.productInfo && rel.productInfo.products_price_str ? rel.productInfo.products_price_str : ''"></span>
                                            {{ item.is_show_composite_related ? `x${rel.num}` : "" }}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </FoldWrapper>
                    </div>
                </div>
                <div class="box-title">{{ $c("form.form.letUsContactYou") }}</div>
                <div class="form" ref="quoteForm">
                    <div class="sample-item">
                        <div class="sample-item-title">
                            <span>{{ $c("form.form.first_name") }} </span>
                        </div>
                        <input
                            v-model.trim="form.first_name"
                            :class="{ error_input: errors.first_name }"
                            @focus.stop="focusInput('first_name', 'First Name Input')"
                            @blur="blurInput('first_name')"
                            type="text"
                            class="inp is_new" />
                        <validate-error :error="errors.first_name"></validate-error>
                    </div>
                    <div class="sample-item">
                        <div class="sample-item-title">
                            <span>{{ $c("form.form.last_name") }} </span>
                        </div>
                        <input
                            v-model.trim="form.last_name"
                            :class="{ error_input: errors.last_name }"
                            @focus.stop="focusInput('last_name', 'Last Name Input')"
                            @blur="blurInput('last_name')"
                            type="text"
                            class="inp is_new" />
                        <validate-error :error="errors.last_name"></validate-error>
                    </div>
                    <div class="sample-item">
                        <div class="sample-item-title">
                            <span>{{ $c("form.form.email_business") }} </span>
                        </div>
                        <input type="text" :class="{ error_input: errors.email }" v-model.trim="form.email" @focus.stop="focusInput('email', 'Email Address Input')" @blur="blurInput('email')" class="inp is_new" />
                        <validate-error :error="errors.email.replace('XXXX', localePath({ name: 'login', query: { redirect: $route.fullPath } }))"></validate-error>
                    </div>
                    <div class="sample-item">
                        <div class="sample-item-title">
                            <span>{{ $c("form.form.phone_number") }} </span>
                        </div>
                        <tel-code code="1" isNewStyle @changeCode="changeCode" :error="errors.phone" :phone="form.phone" @change="telChange"></tel-code>
                        <validate-error :error="errors.phone.replace('XXXX', localePath({ name: 'login', query: { redirect: $route.fullPath } }))"></validate-error>
                    </div>
                    <div class="sample-item">
                        <div class="sample-item-title">
                            <span>{{ $c(country_label) }} </span>
                        </div>
                        <select-country isNewStyle position="absolute" @change="changeCountry" v-model.trim="form.countries_id"></select-country>
                        <validate-error :error="errors.countries_id"></validate-error>
                    </div>
                    <div class="sample-item line sample-item-comments">
                        <div class="sample-item-title input-item-number">
                            <span>{{ $c("pages.Quote.quoteProcess.content") }} </span>
                            <div class="num">
                                <span :class="{ active: form.description.length === 5000 }"> {{ form.description.length }} </span>
                                <span>/5000</span>
                            </div>
                        </div>
                        <textarea
                            v-model.trim="form.description"
                            :placeholder="$c('pages.Quote.quoteProcess.contentTip')"
                            maxlength="5000"
                            class="textarea is_new"
                            :class="{ error_input: errors.description }"
                            @focus.stop="focusInput('description', 'Comments(Optional) Input')"
                            @blur="blurInput('description')"></textarea>

                        <validate-error :error="errors.description"></validate-error>
                    </div>
                    <div class="sample-item line">
                        <div class="upload_main">
                            <upload-file
                                ref="uploadFile"
                                accept=".pdf,image/jpeg,image/jpg,image/png"
                                :maxSize="5 * 1024 * 1024"
                                type="file"
                                :multiple="true"
                                :limit="5"
                                :text="$c('form.form.upload_file')"
                                isNewStyle
                                @change="handleChange">
                                <fs-popover slot="tip" class="upload-tips">
                                    <p>{{ $c("form.form.allow_files_of_type") }}</p>
                                    <p>{{ $c("form.form.maximum_size_5M") }}</p>
                                </fs-popover>
                            </upload-file>
                        </div>
                    </div>
                    <PolicyCheck class="sample-item line protocol" v-model="check2" @change="blurInput('check2')" :error="errors.check2" />
                </div>
            </div>
        </div>
        <div class="submit-success" v-else>
            <span class="iconfont">&#xe710;</span>
            <p class="title">{{ $c("pages.Quote.quoteProcess.successTit") }}</p>
            <p class="describe" v-html="describeHtml" @click="gotoCase()"></p>
        </div>
        <div v-if="!isSubmitSuccess" slot="footer" class="sbtn-box">
            <fs-button id="sample_submit" :text="$c('form.form.submit')" @click="submitFu" :loading="sbtn_loading" htmlType="submit"></fs-button>
        </div>
    </fs-popup-new>
</template>

<script>
import FsPopupNew from "@/components/FsPopupNew/FsPopupNew.vue"
import FsButton from "@/components/FsButton/FsButton.vue"
import QtyBox from "@/components/QtyBox/QtyBox.vue"
import TelCode from "@/components/TelCode/TelCode.vue"
import ValidateError from "@/components/ValidateError/ValidateError.vue"
import SelectCountry from "@/components/SelectCountry/SelectCountry.vue"
import ServerAttributes from "@/components/ServerAttributes/ServerAttributes"
import SlideDown from "@/components/SlideDown/SlideDown.vue"
import UploadFile from "@/components/UploadFile/UploadFile"
import FsPopover from "@/components/FsPopover"
import FoldWrapper from "./base/FoldWrapper.vue"
import { getRecaptchaToken } from "@/util/grecaptchaHost"
import { mapState, mapGetters } from "vuex"
import { email_valdate, phone_validate } from "@/constants/validate.js"
import PolicyCheck from "@/components/PolicyCheck/PolicyCheck.vue"
export default {
    name: "RequestQuote",
    components: {
        FsPopupNew,
        FsButton,
        QtyBox,
        TelCode,
        ValidateError,
        SelectCountry,
        ServerAttributes,
        SlideDown,
        UploadFile,
        FsPopover,
        FoldWrapper,
        PolicyCheck,
    },
    model: {
        prop: "show",
        event: "change",
    },
    props: {
        show: {
            type: Boolean,
            default: false,
        },
    },
    computed: {
        ...mapState({
            list: (state) => state.cart.list,
            userInfo: (state) => state.userInfo.userInfo,
            countries_id: (state) => state.webSiteInfo.countries_id,
            country_code: (state) => state.webSiteInfo.iso_code,
            website: (state) => state.webSiteInfo.website,
        }),
        ...mapGetters({
            country_label: "selectCountry/country_label",
        }),
        describeHtml() {
            return this.$c("pages.Quote.quoteProcess.successDes").replace("XXXXX", this.localePath(`/support_ticket_detail?type=2&number=${this.ticketNum}`))
            // /support_ticket_detail?type=2&number=CN2307130025
        },
    },
    watch: {
        list: {
            deep: true,
            immediate: true,
            handler(v) {
                if (v.length) {
                    this.productList = JSON.parse(JSON.stringify(v))
                }
                console.log(66666, this.productList)
            },
        },
    },
    data() {
        return {
            isSubmitSuccess: false,
            productList: [],
            sbtn_loading: false,
            form: {
                first_name: "",
                last_name: "",
                email: "",
                phone: "",
                countries_id: "",
                state: "",
                description: "",
                reviews_newImg: [],
            },
            check2: false,
            errors: {
                first_name: "",
                last_name: "",
                email: "",
                phone: "",
                countries_id: "",
                check2: "",
                description: "",
            },
            code: "",
            ticketNum: "",
            more_list: [],
        }
    },
    created() {
        if (this.userInfo) {
            this.form.email = this.userInfo.customers_email_address || ""
            this.form.first_name = this.userInfo.customers_firstname || ""
            this.form.last_name = this.userInfo.customers_lastname || ""
        }
    },
    mounted() {
        this.form.countries_id = this.countries_id
        this.form.state = this.country_code
    },
    methods: {
        qtyChange(num, index) {
            this.productList[index].qty = num
        },
        deletePro(i) {
            if (this.productList.length === 1) return
            this.productList.splice(i, 1)
        },
        changeCountry(item) {
            this.form.countries_id = item.countries_id
            this.form.state = item.iso_code
        },
        changeCode(code) {
            this.code = code
        },
        telChange(inp) {
            this.form.phone = inp
            this.blurInput("phone")
        },
        focusInput(attr, label) {
            this.errors[attr + "_error"] = ""
        },
        blurInput(attr) {
            const val = this.form[attr] || ""
            const map = {
                first_name() {
                    if (!val.replace(/^\s+|\s+$/g, "")) {
                        return this.$c("form.form.errors.entry_firstname_error")
                    } else if (val.length > 40) {
                        return this.$c("form.validate.first_name.first_name_max")
                    }
                },
                last_name() {
                    if (!val.replace(/^\s+|\s+$/g, "")) {
                        return this.$c("form.form.errors.entry_lastname_error")
                    } else if (val.length > 40) {
                        return this.$c("form.validate.last_name.last_name_max")
                    }
                },
                email() {
                    if (!val.replace(/^\s+|\s+$/g, "")) return this.$c("form.form.errors.email_business_error")
                    if (!email_valdate.test(val)) return this.$c("form.form.errors.email_address_error01")
                },
                phone() {
                    if (!val.replace(/^\s+|\s+$/g, "")) return this.$c("form.form.errors.entry_telephone_error")
                    if (this.isCn) {
                        if (!cn_mobile_tel.test(val)) return this.$c("form.form.errors.entry_telephone_error01")
                    } else {
                        if (val.length > 0 && val.length < 6) {
                            return this.$c("pages.NetTermsApplication.validate.phone.phone_min")
                        } else if (val.length > 40) {
                            return this.$c("pages.NetTermsApplication.validate.phone.phone_validate")
                        } else {
                            return ""
                        }
                    }
                },
                description() {
                    if (!this.form.description) return this.$c("pages.Quote.quoteProcess.contentTip")
                },
                check2() {
                    if (!this.check2) return this.$c("form.form.errors.check2_error")
                },
            }
            this.errors[attr] = (map[attr] && map[attr].apply(this)) || ""
        },
        verifyWrapper() {
            const errorMap = Object.keys(this.errors)
            errorMap.forEach((key) => {
                this.blurInput(key)
            })

            return errorMap.some((attr) => this.errors[attr])
        },
        focusFirstInvalidField() {
            this.$nextTick(() => {
                const invalidFields = document.querySelectorAll(".error_input")
                if (invalidFields.length > 0) {
                    invalidFields[0].focus()
                }
            })
        },
        submitFu() {
            if (this.sbtn_loading || this.verifyWrapper()) {
                this.scrollToBottom()
                this.focusFirstInvalidField()
                return
            }
            this.sbtn_loading = true
            this.fetchProductsSubmit()
        },

        scrollToBottom() {
            this.$refs.quoteForm?.scrollIntoView({ behavior: "smooth" })
        },
        async fetchProductsSubmit() {
            try {
                let products = {}
                this.productList.forEach((item) => {
                    products[item.products_id] = item.qty
                })
                let phone = `${this.code.substring(1, this.code.length)} ${this.form.phone}`
                let params = { ...this.form, products, phone }
                const formdata = new FormData()
                Object.keys(params).forEach((key) => {
                    if (key !== "reviews_newImg") {
                        formdata.append(key, JSON.stringify(params[key]))
                    } else if (key === "reviews_newImg" && params.reviews_newImg.length) {
                        params.reviews_newImg.forEach((file) => {
                            formdata.append("network_file[]", file)
                        })
                    }
                })
                const { recaptchaTp, headers = {}, errorMsg = "grecaptcha_error" } = await getRecaptchaToken()
                if (!recaptchaTp) {
                    this.sbtn_loading = false
                    this.$message.error(this.$c(`pages.Login.regist.${errorMsg}`))
                    return
                }
                const res = await this.$axios.post("/api/quoteSubmit", formdata, { headers })
                if (res.code == 200) {
                    this.ticketNum = res.data.case_number
                    this.isSubmitSuccess = true
                    this.burryPoint(`Submit_Success`)
                }
            } catch (err) {
                console.log(err)
                this.burryPoint(`Submit_Fail`)
            }
            this.sbtn_loading = false
        },
        handleClose() {
            this.$emit("change", false)
            this.reset()
            this.$nextTick(() => {
                this.isSubmitSuccess = false
                this.ticketNum = ""
            })
        },
        reset() {
            this.sbtn_loading = false
            this.form = {
                first_name: this.userInfo.customers_firstname || "",
                last_name: this.userInfo.customers_lastname || "",
                email: this.userInfo.customers_email_address || "",
                phone: "",
                countries_id: this.countries_id,
                state: this.country_code,
                description: "",
            }
            this.check2 = false
            this.errors = {
                first_name: "",
                last_name: "",
                email: "",
                phone: "",
                countries_id: "",
                check2: "",
                description: "",
            }
            this.code = ""
            this.ticketNum = ""
            if (this.list.length) {
                this.productList = JSON.parse(JSON.stringify(this.list))
            }
        },
        gotoCase() {
            this.$emit("change", false)
        },
        burryPoint(eventLabel) {
            window.dataLayer &&
                window.dataLayer.push({
                    event: "uaEvent",
                    eventCategory: "Cart Page",
                    eventAction: "request_quote",
                    eventLabel,
                    nonInteraction: false,
                })
        },
        setMore(data) {
            if (this.more_list.indexOf(data.products_id) == -1) {
                this.more_list.push(data.products_id)
            } else {
                this.more_list = this.more_list.filter((item) => {
                    return item != data.products_id
                })
            }
        },
        handleChange({ files }) {
            this.form.reviews_newImg = files
        },
    },
}
</script>

<style lang="scss" scoped>
.request_quote {
    ::v-deep .fs-popup {
        .header .title {
            @include font18();
            font-weight: 600;
        }
        &.is_new .fs-popup-body {
            padding-top: 16px;
        }
        .content {
            .product-list {
                background-color: #fafafb;
                padding: 0 16px;
                border-radius: 4px;
                .product-item {
                    padding: 16px 0;
                    &:not(:first-child) {
                        border-top: 1px solid #e5e5e5;
                    }
                }
                .product-item-de {
                    display: flex;
                    align-items: center;

                    img {
                        width: 68px;
                        height: 68px;
                        mix-blend-mode: multiply;
                    }
                    .txt-box {
                        flex: 1;
                        margin: 0 12px;
                        .tit {
                            @include font14();
                            color: #19191a;
                            font-weight: 600;
                            @include txt-more-hid(2);
                        }
                        .txt {
                            @include font12();
                            color: #707070;
                            margin-top: 4px;
                            span {
                                margin-right: 12px;
                            }
                        }
                    }

                    .handle-box {
                        display: flex;
                        align-items: center;
                        justify-content: space-between;
                        ::v-deep .qty-box {
                            background-color: #fff;
                        }

                        .del-box {
                            margin-left: 12px;
                            cursor: pointer;
                            .iconfont {
                                color: #707070;
                                font-size: 16px;
                                padding: 6px;
                            }
                            &:hover {
                                .iconfont {
                                    background-color: #f1f2f2;
                                    border-radius: 4px;
                                    color: #19191a;
                                }
                            }
                            &.abled {
                                display: none;
                                cursor: not-allowed;
                            }
                        }
                    }
                }
                .common-fold-box {
                    padding-left: 80px;
                    margin-top: 8px;
                }
                .attrs-content {
                    overflow: hidden;
                    width: 350px;
                    .attrs-item {
                        color: #707070;
                        @include font12();
                        margin-bottom: 4px;

                        &:first-of-type {
                            margin-top: 12px;
                        }

                        &:last-child {
                            margin-bottom: 0;
                        }
                    }
                }

                .related-box {
                    overflow: hidden;

                    &.popup {
                        .related-item .related-detail {
                            gap: 0 12px;
                        }
                    }

                    .related-item {
                        display: flex;
                        align-items: center;
                        margin-top: 12px;

                        .releated-item-img {
                            flex-shrink: 0;
                            display: block;
                            width: 48px;
                            height: 48px;
                            background-repeat: no-repeat;
                            background-position: center;
                            background-size: cover;
                            margin-right: 12px;
                            margin-top: -16px;
                            mix-blend-mode: multiply;
                        }

                        .related-detail {
                            flex: 1 1 auto;
                            display: flex;
                            align-items: center;
                            gap: 0 12px;
                            color: #707070;
                            margin-left: -148px;
                            @include font12;

                            .related-title {
                                @include txt-more-hid;
                            }

                            .related-flex {
                                overflow: hidden;
                                margin-left: 148px;
                            }

                            .related-id {
                                margin-top: 4px;
                            }
                            .related-num {
                                width: max-content;
                                white-space: nowrap;
                            }
                        }
                    }
                }
            }
            .box-title {
                margin: 20px 0 12px 0;
                border-top: 1px solid $borderColor2;
                padding-top: 16px;
                @include font16();
                font-weight: 600;
                color: $textColor1;
            }
            .form {
                display: grid;
                grid-template-columns: repeat(2, 1fr);
                grid-gap: 12px;
                .sample-item {
                    &.line {
                        grid-column: span 2;
                    }

                    .sample-item-title {
                        color: #19191a;
                        @include font12();
                        color: $textColor3;
                        margin-bottom: 4px;
                    }
                    .input-item-number {
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                    }
                    .num {
                        display: flex;
                        flex-wrap: nowrap;
                        margin-top: 4px;
                        padding-left: 10px;
                    }
                    .active {
                        color: $textColor4;
                    }
                    .upload_main {
                        .upload-tips {
                            margin-left: 8px;
                        }
                    }
                    .agreement {
                        width: 100%;
                        display: flex;
                        align-items: flex-start;
                        input {
                            // font-size: 16px;
                            margin: 0 8px 0 0;
                            @media (max-width: 960px) {
                                margin-top: 1px;
                            }
                        }
                        > p {
                            @include font12;
                            color: $textColor3;
                            a {
                                color: #0060bf;
                            }
                        }
                        // &:hover {
                        //     cursor: pointer;
                        //     input[type="checkbox"] {
                        //         &:before {
                        //             color: #707070;
                        //         }
                        //     }
                        // }
                    }
                }
            }
        }
        .sbtn-box {
            display: flex;
            justify-content: flex-end;
        }
        .submit-success {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding-bottom: 24px;
            .iconfont {
                font-size: 50px;
                color: #329a34;
            }
            .title {
                @include font16();
                font-weight: 600;
                color: #19191a;
                text-align: center;
                margin: 16px 0;
            }
            .describe {
                @include font14();
                color: #707070;
                text-align: center;
            }
        }
        @include mediaM {
            .pc-show {
                display: none;
            }
            .content {
                .product-list {
                    padding: 0 12px;
                    .product-item-de {
                        position: relative;
                        flex-wrap: wrap;
                        .txt-box {
                            margin-right: 32px;
                            .more {
                                ul li img {
                                    margin: 0 20px 0 20px;
                                }
                            }
                        }
                        .handle-box {
                            flex: 0 0 100%;
                            padding-left: 80px;
                            margin-top: 8px;
                            .del-box {
                                position: absolute;
                                top: 0;
                                right: 0;
                            }
                        }
                    }
                    .common-fold-box {
                        padding: 0 0 0 80px;
                    }
                    .related-box .related-item .related-detail {
                        display: flex;
                        justify-content: space-between;
                        .related-title {
                            max-width: 184px;
                        }
                    }
                    .attrs-content {
                        width: 100%;
                    }
                }
                .form {
                    .sample-item {
                        grid-column: span 2;
                    }
                }
            }
            .sbtn-box {
                button {
                    width: 100%;
                }
            }
        }
    }
}
</style>
