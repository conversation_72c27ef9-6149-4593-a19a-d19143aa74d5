<template>
    <div class="cart-summary" v-show="list.length">
        <div class="summary-top">
            <div class="summary_title">
                {{ $c("pages.Checkout.orderDetails") }}
            </div>
            <span class="summary_num"> {{ list.length }} {{ $c("pages.ShoppingCart.newCart.items") }} </span>
        </div>
        <!-- <div class="summary-price" v-show="cartData.isCorporateMembership"> 
          <span>{{$c('pages.ShoppingCart.Total_Saving')}}{{website==='fr'?' ':''}}:</span>
          <p v-html="cartData.checkedPriceInfo.preferentialPriceWithChecked.saveTotalFormat"></p>
      </div> -->
        <!-- <div class="summary-price-tax" v-if="website === 'au' && !isCn">
          <span>{{ $c("pages.ShoppingCart.newCart.total_gst") }}</span>
          <p v-html="cartData.checkedPriceInfo && cartData.checkedPriceInfo.taxWithChecked.taxFormat"></p>
      </div> -->
        <div class="summary-price" :class="{ summary_price_tax: isTax }">
            <span>{{ subTotalLabel }}</span>
            <p v-html="cartData.checkedPriceInfo && cartData.checkedPriceInfo.subTotalWithChecked.subTotalFormat"></p>
        </div>
        <templete v-if="isTax">
            <div v-if="isIndiaCountry && cartData.checkedPriceInfo?.taxWithChecked?.tax != '0.00'" class="summary-price summary_price_tax">
                <span> {{ cartData.rateInfo.rateDescription }}</span>
                <p v-html="cartData.checkedPriceInfo && `${cartData.checkedPriceInfo.taxWithChecked.taxFormat} `"></p>
            </div>
            <template v-else-if="!isIndiaCountry">
                <div class="summary-price summary_price_tax">
                    <span v-html="cartData.rateInfo && cartData.rateInfo.rateDescription"></span>
                    <p v-html="cartData.checkedPriceInfo && `${cartData.checkedPriceInfo.taxWithChecked.taxFormat} `"></p>
                </div>
            </template>

            <div class="summary-price">
                <span>{{ $c("pages.ShoppingCart.Total") }}</span>
                <p v-html="cartData.checkedPriceInfo && `${cartData.checkedPriceInfo.total.totalFormat} `"></p>
            </div>
        </templete>

        <p v-if="isCn" class="cn_shipping">免运费</p>
        <!-- <p class="summary-info" v-else>{{ $c("pages.ShoppingCart.newCart.summaryMsg") }}</p> -->
        <p class="line"></p>
        <div class="btn_box" :class="{ btn_box_cn: isCn }">
            <div class="nochecked_box" :class="{ nochecked_box_quote: show_nochecked_type === 'quote' }" v-show="show_nochecked">
                <span class="arrow-box"></span>
                <div class="tip-ctn">
                    <div class="info">
                        {{ $c("pages.ShoppingCart.Please_select_one_item") }}
                    </div>
                </div>
            </div>
            <fs-button v-if="!isRussia" tabindex="0" type="red" class="fs-button-secure" :loading="checkout_loading" :disabled="isCn ? !checked_qty : false" @mouseleave.native="btnMouseLeave" @click="checkoutClick">
                {{ cartData.loginType === "punchout" ? "Punchout" : $c("pages.ShoppingCart.Secure_Checkout") }}
            </fs-button>
            <fs-button v-if="!isCn" tabindex="0" type="gray" class="fs-button-quote" :loading="quote_loading" :gray="true" @mouseleave.native="btnMouseLeave" @click="quoteClick">
                {{ $c("pages.Quote.quoteProcess.quote") }}
            </fs-button>
        </div>
        <div class="abstract_box">
            <span class="isnLogin" v-if="!isLogin" v-html="`${$c('pages.ShoppingCart.newCart.descr4').replace('XXXX', localePath({ name: 'login', query: { redirect: $route.fullPath } }))}`"></span>
            <div class="isLogin" v-else>
                <span tabindex="0" @keyup.enter.stop="saveClick" @click.stop="saveClick">{{ $c("pages.ShoppingCart.Save_Cart_title") }}</span>
                <span class="line"></span>
                <span tabindex="0" @keyup.enter.stop="showSharePopup" @click.stop="showSharePopup">{{ $c("pages.ShoppingCart.newCart.email") }}</span>
            </div>
            <new-cart-payment v-if="!isCn"></new-cart-payment>
        </div>
    </div>
</template>

<script>
import NewCartPayment from "./NewCartPayment.vue"
import FsButton from "@/components/FsButton/FsButton.vue"
import { mapState, mapGetters, mapMutations, mapActions } from "vuex"
import FsTip from "@/components/FsTip/FsTip"
export default {
    name: "NewCartSummary",
    components: {
        NewCartPayment,
        FsButton,
        FsTip,
    },
    props: {
        quoteMethodShow: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {
            checkout_loading: false,
            quote_loading: false,
            save_loading: false,
            show_nochecked: false,
            show_nochecked_type: "quote",
        }
    },
    computed: {
        ...mapState({
            isMobile: (state) => state.device.isMobile,
            list: (state) => state.cart.list,
            cartData: (state) => state.cart.cartData,
            isLogin: (state) => state.userInfo.isLogin,
            qty: (state) => state.cart.qty,
            iso_code: (state) => state.webSiteInfo.iso_code,
            website: (state) => state.webSiteInfo.website,
            isLogin: (state) => state.userInfo.isLogin,
        }),
        ...mapGetters({
            checked_qty: "cart/checked_qty",
            isRussia: "webSiteInfo/isRussia",
            isCn: "webSiteInfo/isCn",
            isIndiaCountry: "webSiteInfo/isIndiaCountry",
            isSingaporeCountry: "webSiteInfo/isSingaporeCountry",
            isAustraliaCountry: "webSiteInfo/isAustraliaCountry",
            isJpCountry: "webSiteInfo/isJpCountry",
        }),
        isTax() {
            return this.cartData.checkedPriceInfo && this.cartData.checkedPriceInfo.taxWithChecked.rateExpress.rate != 0
        },
        subTotalLabel() {
            let label = this.$c("pages.ShoppingCart.Subtotal")
            if (this.isTax) {
                if (this.isSingaporeCountry || this.isAustraliaCountry) {
                    label = this.$c("pages.ShoppingCart.newCart.taxBefore2")
                }
                if (this.isJpCountry) {
                    label = this.$c("pages.ShoppingCart.newCart.taxBefore")
                }
            } else {
                label = this.$c("pages.ShoppingCart.Total")
            }
            return label
        },
    },
    methods: {
        ...mapMutations({ setSimplifyQtyTips: "cart/setSimplifyQtyTips" }),
        ...mapActions({
            getUserInfo: "userInfo/getUserInfo",
        }),
        btnMouseLeave() {
            this.show_nochecked = false
        },
        shopCartLimitShow() {
            this.$shopcartLimit.show({
                title: this.$c("pages.ShoppingCart.Confirmation"),
                message: this.cartData.addAllowFlagMessage,
                viewCartName: this.$c("pages.Products.View_Cart"),
                checkoutName: this.$c("pages.ShoppingCart.Secure_Checkout2"),
                isShowCheckOutBtn: false,
                checkOutMethod: () => {
                    // this.$router.push(this.localePath({ name: "confirm-order" }))
                    location.href = this.localePath({ name: "confirm-order" })
                },
                shopCartMethod: () => {
                    this.$router.push(this.localePath({ name: "shopping-cart" }))
                },
            })
        },

        checkoutClick() {
            // let _this = this;
            if (this.checkout_loading) {
                return
            }
            if (this.cartData.checkoutFlag == 0) {
                this.shopCartLimitShow()
                return
            }

            this.$bdRequest({
                conversionTypes: [
                    {
                        logidUrl: location.href,
                        newType: 45,
                    },
                ],
            })
            this.show_nochecked_type = "checkout"
            if (!this.checked_qty) {
                if (this.isMobile) {
                    this.$emit("showNochecked")
                } else {
                    this.show_nochecked = true
                }
                return
            }
            this.checkout_loading = true
            this.getUserInfo(() => {
                if (window.dataLayer) {
                    let arr = []
                    if (this.list && this.list.length) {
                        this.list.map((item, index) => {
                            if (item.isChecked) {
                                let variant = []
                                if (item.fs_cart_attr && item.fs_cart_attr.length) {
                                    item.fs_cart_attr.map((attr) => {
                                        variant.push(attr.replace(" - ", ":"))
                                    })
                                }
                                let cate = []
                                if (item.products_info.categories_info && item.products_info.categories_info.length) {
                                    item.products_info.categories_info.forEach((i) => {
                                        cate.push(i.name)
                                    })
                                    cate = cate.join("_")
                                }
                                arr.push({
                                    id: item.products_id,
                                    name: item.products_info.productName.substr(0, 100),
                                    price: item.products_info.finalPriceExchangeRate ? parseFloat(item.products_info.finalPriceExchangeRate).toFixed(2) : "",
                                    variant: variant.join("|"),
                                    position: index + 1,
                                    category: cate,
                                    quantity: item.qty,
                                })
                            }
                        })
                    }
                    console.log(arr, parseFloat(this.cartData.checkedPriceInfo.subTotalWithChecked.subTotalUsd).toFixed(2))
                    window.dataLayer.push({
                        event: "eeEvent",
                        eventCategory: "Cart Page",
                        eventAction: "begin_checkout",
                        eventLabel: "Secure Checkout",
                        eventValue: parseFloat(this.cartData.checkedPriceInfo.subTotalWithChecked.subTotalUsd).toFixed(2),
                        nonInteraction: false,
                        ecommerce: {
                            checkout: {
                                actionField: {
                                    step: 1,
                                    option: "",
                                },
                                products: arr,
                            },
                        },
                    })
                }
                if (window.yaCounter71412688) {
                    yaCounter71412688.reachGoal("checkout", function () {})
                }
                // this.checkout_loading = false;
                if (this.isLogin) {
                    if (this.cartData.loginType === "punchout") {
                        this.$axios
                            .get("/api/punchout/product")
                            .then((res) => {
                                let obj = {
                                    buyercookie: this.$cookies.get("punchout_buyer_cookie"),
                                    return_url: this.$cookies.get("punchout_return_url"),
                                    body: {
                                        items: res.data,
                                    },
                                }
                                let form = document.createElement("form")
                                form.action = this.$cookies.get("punchout_return_url")
                                form.method = "post"
                                form.style.display = "none"
                                let input = document.createElement("input")
                                input.type = "hidden"
                                input.name = "cartdata"
                                input.value = JSON.stringify(obj)
                                form.appendChild(input)
                                document.body.appendChild(form)
                                form.submit()
                            })
                            .catch((err) => {
                                alert(err.message)
                            })
                    } else {
                        // this.$router.push(this.localePath({ name: "confirm-order" }))
                        location.href = this.$localeLink("/confirm-order")
                    }
                } else {
                    if (this.cartData.loginType === "punchout") {
                        this.$router.push(this.localePath({ name: "home" }))
                    } else {
                        let fullpath = this.$route.fullPath
                        this.$router.push(this.localePath({ name: "login", query: { redirect: fullpath } }))
                    }
                }
            })
        },
        gaEventSave() {
            if (window.dataLayer) {
                window.dataLayer.push({
                    event: "uaEvent",
                    eventCategory: "Cart Page",
                    eventAction: "cart_operate",
                    eventLabel: "View Saved Cart",
                    nonInteraction: false,
                })
            }
        },
        quoteClick() {
            // let _this = this;
            if (this.quote_loading) {
                return
            }
            this.show_nochecked_type = "quote"
            if (!this.checked_qty) {
                if (this.isMobile) {
                    this.$emit("showNochecked")
                } else {
                    this.show_nochecked = true
                }
                return
            }
            this.quote_loading = true

            this.getUserInfo(() => {
                this.quote_loading = false
                if (this.isLogin) {
                    this.$emit("update:quoteMethodShow", true)
                    // this.fetchVerifyQty("quote_loading", () => {
                    //     this.$emit("update:quoteMethodShow", true)
                    // })
                } else {
                    let fullpath = this.$route.fullPath
                    this.$router.push(this.localePath({ name: "login", query: { redirect: fullpath } }))
                }
                if (window.dataLayer) {
                    window.dataLayer.push({
                        event: "uaEvent",
                        eventCategory: "Cart Page",
                        eventAction: "create_quote",
                        eventLabel: "Quote",
                        nonInteraction: false,
                    })
                }
            })
        },
        async fetchVerifyQty(loading, callBack) {
            try {
                const products = this.list
                    .filter((i) => i.isChecked)
                    .map(({ products_id, qty, isChecked }) => ({
                        products_id,
                        qty,
                        isChecked,
                    }))

                const res = await this.$axios.post("/api/cart/streamlineProductTips", { products })
                if (Array.isArray(res.data)) {
                    this.setSimplifyQtyTips({})
                    callBack()
                } else {
                    this.setSimplifyQtyTips(res.data)
                }
            } catch (error) {
                console.error(error.message)
            }
            this[loading] = false
        },
        // 保存购物
        saveClick() {
            if (this.save_loading) {
                return
            }
            if (!this.isLogin) {
                this.$router.push(this.localePath({ name: "login", query: { redirect: this.$route.fullPath } }))
            } else {
                this.save_loading = true
                this.getUserInfo(() => {
                    this.save_loading = false
                    if (window.dataLayer) {
                        window.dataLayer.push({
                            event: "uaEvent",
                            eventCategory: "Cart Page",
                            eventAction: "cart_operate",
                            eventLabel: "Save Cart",
                            nonInteraction: false,
                        })
                    }
                    if (this.isLogin) {
                        this.$emit("showSavePopup")
                    } else {
                        this.$router.push(this.localePath({ name: "login", query: { redirect: this.$route.fullPath } }))
                    }
                })
            }
        },
        // 分享购物车
        showSharePopup() {
            this.$emit("showSharePopup")
            if (window.dataLayer) {
                window.dataLayer.push({
                    event: "uaEvent",
                    eventCategory: "Cart Page",
                    eventAction: "cart_operate",
                    eventLabel: "Email Cart",
                    nonInteraction: false,
                })
            }
        },
    },
}
</script>

<style lang="scss" scoped>
.cart-summary {
    background-color: #fafafb;
    padding: 16px;
    border-radius: 12px;
    & > .line {
        background: #e5e5e5;
        height: 1px;
        width: 100%;
        margin: 12px 0;
    }
    @media (max-width: 768px) {
        padding: 20px 16px 16px;

        .summary-price {
            @include font14();
        }
        .summary-info {
            padding-bottom: 20px;
        }
        .btn_box {
            margin-top: 12px;
            background-color: #fff;
        }
        .btn_box_cn {
            padding-bottom: 20px;
        }
    }
}

.summary-top {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 12px;
    flex-wrap: wrap;
    gap: 4px;
    .summary_title {
        @include font16();
        color: $textColor1;
        font-weight: 600;
    }
    .summary_num {
        color: #707070;
        @include font12();
    }
}

.summary-price-tax {
    display: flex;
    align-items: center;
    font-size: 14px;
    line-height: 22px;
    justify-content: space-between;
    margin-bottom: 12px;
    span {
        color: #707070;
    }
    p {
        color: #19191a;
    }
}
.summary-price {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    @include font14();
    font-weight: 600;
    color: $textColor1;
    justify-content: space-between;
    margin-top: 4px;
}
.summary_price_tax {
    span {
        color: #19191a;
        font-weight: normal;
        font-size: 12px;
        line-height: 20px;
    }
    p {
        color: #19191a;
        font-weight: normal;
        font-size: 12px;
        line-height: 20px;
    }
}
.cn_shipping {
    @include font12;
    padding-bottom: 12px;
    border-bottom: 1px solid #e5e5e5;
}
.summary-info {
    @include font12();
    color: $textColor1;
    padding-bottom: 16px;
    border-bottom: 1px solid #e5e5e5;
}
.total-price {
    margin-top: 16px;
    border-top: 1px solid $borderColor2;
    padding-top: 16px;
    display: flex;
    justify-content: space-between;
    font-size: 20px;
    font-weight: 600;
    color: $textColor1;
}
.total-info {
    font-size: 13px;
    color: #707070;
    line-height: 1;
    margin: 10px 0 0 0;
}
.btn_box {
    margin-top: 12px;
    position: relative;
    :deep(.fs-button) {
        font-weight: 600;
        display: block;
        width: 100%;
        padding: 10px 24px;
        &:not(:first-child) {
            margin-top: 12px;
        }
    }
}
.nochecked_box {
    width: 100%;
    position: absolute;
    left: 0;
    top: -4px;
    &.nochecked_box_quote {
        top: 56px;
    }
    .arrow-box {
        display: inline-block;
        width: 0;
        height: 0;
        border-width: 10px 10px 10px 0;
        border-style: solid;
        border-color: transparent #e5e5e5 transparent transparent;
        position: absolute;
        z-index: 12;
        bottom: 100%;
        left: 50%;
        border-width: 10px 10px 0px 10px;
        border-color: #e5e5e5 transparent transparent transparent;
        transform: translateX(-50%);
        &:after {
            content: " ";
            display: block;
            width: 0;
            height: 0;
            border-style: solid;
            position: absolute;
            border-width: 9px 9px 0 9px;
            border-color: #fff transparent transparent transparent;
            top: -10px;
            left: -9px;
        }
    }
    .tip-ctn {
        position: absolute;
        color: $textColor3;
        cursor: default;
        z-index: 11;
        bottom: 100%;
        transform: translateX(-50%);
        left: 50%;
        padding-bottom: 10px;
        width: 100%;
        .info {
            @include font13;
            box-shadow: 0 1px 8px 0 rgba(120, 102, 102, 0.3);
            padding: 20px 20px;
            background-color: #fff;
            white-space: normal;
            text-align: center;
            color: $textColor1;
        }
    }
}

.iconfont-quote,
.iconfont-secure {
    font-size: 14px;
    margin-right: 6px;
}
.handle-cart {
    margin: 30px 0 25px 0;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 14px;
    color: #0060bf;
    line-height: 1;
    flex-wrap: wrap;
    a {
        padding: 0 10px;
        margin: 2px 0;
        &:first-child {
            border-right: 1px solid #cccccc;
        }
    }
}
.abstract_box {
    margin-top: 12px;
    padding-top: 12px;
    border-top: 1px solid #e5e5e5;
    .isnLogin {
        color: #707070;
        @include font12();
        text-align: center;
        display: block;
        ::v-deep {
            a:focus-visible {
                @include focusVisible;
            }
        }
    }
    .isLogin {
        display: flex;
        align-items: center;
        justify-content: center;
        flex-wrap: wrap;
        span {
            @include font12();
            color: #707070;
            cursor: pointer;
            &:hover {
                text-decoration: underline;
            }
        }
        .line {
            width: 1px;
            height: 16px;
            background: #eeeeee;
            margin: 0 8px;
        }
    }
}
</style>
