<template>
    <!-- bannerData banner图  sidebarData 侧边栏导航  sidebarTitle 侧边栏导航标题 -->
    <second-menu-side-page :bannerData="bannerData" :sidebarData="sidebarData" :sidebarTitle="sidebarTitle" :crumbList="crumbList" type="6">
        <div class="content">
            <div class="main">
                <div class="verify-table">
                    <div class="verify-table-right">
                        <img src="https://img-en.fs.com//includes/templates/fiberstore/images/security/security_fs1.jpeg" alt="QR code" />
                        <div>
                            <p class="txt01">
                                {{ $c("single.verify.explain.txt01") }}
                            </p>
                            <p class="txt01" v-html="$c('single.verify.explain.txt02').replace('/appdownload.html', localePath({ path: '/appdownload.html' }))"></p>
                            <p class="txt02">
                                {{ $c("single.verify.explain.txt03") }}
                            </p>
                        </div>
                    </div>
                    <div class="verify-table-left" v-if="enter">
                        <div class="table-con">
                            <h3 class="title">
                                {{ $c("single.verify.code") }}
                            </h3>
                            <input
                                type="text"
                                class="is_new"
                                :class="{ error_input: codeError }"
                                @input.stop="digitCodeFix"
                                @blur.stop="digitCodeResult"
                                v-model.trim="digitCodeValue"
                                :placeholder="$c('single.verify.placeholder.code')"
                                autocomplete="off"
                                maxlength="14"
                                @focus="handlePoint('Please enter the 12-digit security code.', 'code')" />
                            <validate-error :error="codeError"></validate-error>
                        </div>
                        <div class="table-con">
                            <h3 class="title">{{ $c("single.verify.captcha") }}</h3>
                            <div class="captcha-box">
                                <input
                                    type="text"
                                    class="is_new"
                                    :class="{ error_input: captchaCodeError }"
                                    @input.stop="watchCaptcha"
                                    @blur.stop="blurCaptcha"
                                    v-model.trim="captchaCode"
                                    :placeholder="$c('single.verify.placeholder.enter')"
                                    autocomplete="off"
                                    maxlength="4"
                                    @focus="handlePoint('Type the captcha'), 'captcha'" />
                                <canvas
                                    class="captcha"
                                    ref="captcha"
                                    width="147"
                                    height="38"
                                    @click.stop="change"
                                    :alt="$c('common.altText.Type_the_word_in_the_image')"
                                    :aria-label="$c('common.altText.Type_the_word_in_the_image')"></canvas>
                            </div>
                            <validate-error :error="captchaCodeError"></validate-error>
                        </div>
                        <fs-button class="subBtn" @click="click" :loading="loading" htmlType="submit">{{ $c("single.verify.submit") }}</fs-button>
                    </div>

                    <div class="verify-table-left error" v-if="verifyError">
                        <div class="verifyError-icon">
                            <span class="icon iconfont">&#xe718;</span>
                        </div>
                        <p class="tit">{{ $c("single.verify.verify_error.title") }}</p>
                        <p class="txt" v-if="this.website == 'jp'">{{ this.errorTxt }}</p>
                        <p class="txt" v-else>
                            {{ $c("single.verify.verify_error.txt01") }} <span>{{ errorResult }}</span> {{ $c("single.verify.verify_error.txt02") }}
                        </p>
                        <p class="btn_box">
                            <fs-button @click="hideVerifyError">{{ $c("single.verify.again") }}</fs-button>
                        </p>
                    </div>
                    <div class="verify-table-left success" v-if="verifySuccess">
                        <div class="verifySuccess-icon">
                            <span class="iconfont icon">&#xe710;</span>
                        </div>
                        <p class="successTit" v-html="$c('single.verify.verify_success')"></p>
                        <p class="btn_box">
                            <fs-button @click="hideVerifyError">{{ $c("single.verify.again") }}</fs-button>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </second-menu-side-page>
</template>

<script>
import SecondMenuSidePage from "@/components/SecondMenuSidePage/SecondMenuSidePage.vue"
import ValidateError from "@/components/ValidateError/ValidateError"
import { createCaptcha } from "@/util/util.js"
import FsButton from "@/components/FsButton/FsButton"
import { mapState } from "vuex"

export default {
    name: "verify",
    components: {
        SecondMenuSidePage,
        ValidateError,
        FsButton,
    },
    data() {
        return {
            crumbs: [
                {
                    name: this.$c("single.verify.crumbs.home"),
                    url: "/",
                },
                {
                    name: this.$c("pages.MyAccount.myAccount"),
                    url: "/my-account",
                },
                {
                    name: this.$c("pages.MyAccount.menu.productsVerification"),
                    url: "",
                },
            ],
            bannerData: {
                title: this.$c("pages.MyAccount.menu.productsVerification"),
                banner: {
                    m: "https://img-en.fs.com/includes/templates/fiberstore/images/security/banner-m.jpg",
                    pad: "https://img-en.fs.com/includes/templates/fiberstore/images/security/banner-pad.jpg",
                    pc: "https://img-en.fs.com/includes/templates/fiberstore/images/security/banner-pc.jpg",
                },
            },
            codeError: "",
            captchaCodeError: "",
            digitCodeValue: "",
            captchaCode: "",
            c: "",
            loading: false,
            isActive: false,
            digitActive: false,
            captchaActive: false,
            enter: true,
            verifyError: false,
            verifySuccess: false,
            errorResult: "************",
            errorTxt: this.$c("single.verify.verify_error.txt02"),
        }
    },
    computed: {
        ...mapState({
            website: (state) => state.webSiteInfo.website,
        }),
    },
    methods: {
        handlePoint(label, attr) {
            window.dataLayer &&
                window.dataLayer.push({
                    event: "uaEvent",
                    eventCategory: "Product Service_Verify Page",
                    eventAction: "product_verify",
                    eventLabel: `${label} Input`,
                    nonInteraction: false,
                })
            attr === "code" ? (this.codeError = "") : (this.captchaCodeError = "")
        },
        digitCodeResult() {
            let value = this.digitCodeValue
            if (value.length !== 14) {
                this.codeError = this.$c("single.verify.errors.code_error")
                return false
            } else {
                this.codeError = ""
                return true
            }
        },
        digitCodeFix() {
            this.digitCodeValue = this.digitCodeValue
                .replace(/[^0-9A-Za-z]/g, "")
                .replace(/(.{4})/g, "$1 ")
                .replace(/ $/g, "")
                .toUpperCase()
            console.log(this.digitCodeValue.length)
            if (this.digitCodeValue.length == 14) {
                this.digitActive = true
                console.log(this.digitActive)
                // if (this.digitActive && this.captchaActive) {
                //     this.isActive = true
                // }
            } else {
                this.digitActive = false
                // this.isActive = false
            }
        },
        watchCaptcha() {
            let value = this.captchaCode.toUpperCase()
            this.captchaCodeError = ""
            if (value != this.c) {
                if (this.captchaCode.length == 4) {
                    this.captchaCodeError = this.$c("single.verify.errors.captcha_error")
                }
                this.captchaActive = false
                // this.isActive = false
            } else {
                this.captchaCodeError = ""
                this.captchaActive = true
                // if (this.digitActive && this.captchaActive) {
                //     this.isActive = true
                // }
            }
        },
        blurCaptcha() {
            let value = this.captchaCode.toUpperCase()
            if (value != this.c) {
                this.captchaCodeError = this.$c("single.verify.errors.captcha_error")
                // this.isActive = false
                return false
            }
            return true
        },
        change() {
            this.c = createCaptcha(this.$refs.captcha)
            console.log(this.c)
            let value = this.captchaCode.toUpperCase()
            // if (value != this.c) {
            //     this.isActive = false
            // }
        },
        hideVerifyError() {
            this.verifyError = false
            this.verifySuccess = false
            this.enter = true
            this.digitCodeValue = ""
            this.captchaCode = ""
            this.$nextTick(() => {
                this.c = createCaptcha(this.$refs.captcha)
            })
        },
        click() {
            let flag1 = this.digitCodeResult()
            let flag2 = this.blurCaptcha()
            if (!flag1 || !flag2) {
                return
            }
            if (!this.loading) {
                this.loading = true
                // this.isActive = false
                this.$axios
                    .post("/api/verify", {
                        security_code: this.digitCodeValue.replace(/\s/g, ""),
                    })
                    .then((res) => {
                        let data = res.data
                        if (data.length != 0) {
                            this.enter = false
                            this.verifySuccess = true
                        } else {
                            this.enter = false
                            this.verifyError = true
                            this.errorResult = this.digitCodeValue
                            console.log(this.errorTxt.includes("XXXXX"))
                            this.errorTxt = this.errorTxt.replace(/XXXXX/, this.errorResult)
                            console.log(this.errorTxt)
                        }
                        window.dataLayer &&
                            window.dataLayer.push({
                                event: "uaEvent",
                                eventCategory: "Product Service_Verify Page",
                                eventAction: "product_verify",
                                eventLabel: "Verify Success",
                                nonInteraction: false,
                            })
                        this.loading = false
                    })
                    .catch((error) => {
                        console.log(error)
                        this.loading = false
                        // this.isActive = true
                        window.dataLayer &&
                            window.dataLayer.push({
                                event: "uaEvent",
                                eventCategory: "Product Service_Verify Page",
                                eventAction: "product_verify",
                                eventLabel: "Verify Fail",
                                nonInteraction: false,
                            })
                    })
            }
        },
        //获取数据
        getVerifyData() {
            const { id } = this.$route.params
            if (id) {
                this.$axios
                    .post("/api/verify", {
                        security_code: id,
                    })
                    .then(({ data }) => {
                        // let data = res.data
                        if (data.length != 0) {
                            this.enter = false
                            this.verifySuccess = true
                        } else {
                            this.enter = false
                            this.verifyError = true
                            this.errorResult = id
                        }
                    })
                    .catch((res) => {
                        console.log(res)
                    })
            }
        },
    },
    mounted() {
        this.getVerifyData()
        this.$nextTick(() => {
            this.c = createCaptcha(this.$refs.captcha)
        })
    },
    async asyncData({ app, route, $c }) {
        let [navRes] = await Promise.all([app.$axios.get(`/api/left_nav`, { params: { plate: "single_page", modular: "products_verification" } })])
        console.log("navRes333==", JSON.stringify(navRes))
        let sidebarData = []
        let bread = [
            { name: $c("pages.Products.Home"), url: app.localePath({ name: "home" }) },
            // { name: navRes.data.tit, url: app.localePath({ name: "fs_support" }) }, //
            { name: $c("pages.MyAccount.menu.productsVerification"), url: "" }, //
        ]
        navRes.data.children.forEach((item) => {
            sidebarData.push({
                title: item.tit,
                href: item.href,
                name: item.name,
            })
        })
        return {
            sidebarData,
            sidebarTitle: navRes.data.tit,
            crumbList: bread,
        }
    },
}
</script>

<style lang="scss" scoped>
::v-deep .nav-tree {
    display: none !important;
}
::v-deep .jp_page_left {
    display: none;
}
.content {
    padding: 0 0 36px 0;
    .main {
        margin: 0 auto;

        .verify-table {
            background-color: $bgColor3;
            // padding: 36px;
            display: flex;
            // flex-direction: column;

            .verify-table-left {
                border-left: 1px solid #e5e5e5;
                // padding-top: 36px;
                // padding-right: 348px;
                padding-left: 36px;
                width: 50%;
                &.success,
                &.error {
                    display: flex;
                    flex-direction: column;
                    justify-content: center;
                    .fs-button {
                        margin-top: 0;
                    }
                }
                .table-con:first-child {
                    margin-bottom: 16px;
                }
                input {
                    border-color: #f6f6f8;
                    background: #f6f6f8;
                    &:hover {
                        background: linear-gradient(0deg, rgba(25, 25, 26, 0.04), rgba(25, 25, 26, 0.04)), #f6f6f8;
                    }
                    &:focus {
                        background: linear-gradient(0deg, rgba(25, 25, 26, 0.04), rgba(25, 25, 26, 0.04)), #f6f6f8;
                        border-color: #707070;
                    }
                }
                .captcha-box {
                    display: flex;

                    .captcha {
                        margin-left: 12px;
                        cursor: pointer;
                    }
                }

                .fs-button {
                    margin-top: 20px;
                    // width: 100%;
                }

                .verifyError-icon {
                    text-align: center;

                    .icon {
                        font-size: 50px;
                        color: $textColor4;
                    }
                }

                .verifySuccess-icon {
                    text-align: center;

                    .icon {
                        font-size: 50px;
                        color: #00a653;
                    }
                }

                .tit {
                    @include font16;
                    margin: 16px 0 8px;
                    color: $textColor1;
                    text-align: center;
                    font-weight: 600;
                }

                .txt {
                    @include font14;
                    color: $textColor3;
                    text-align: center;
                }
                .btn_box {
                    margin-top: 16px;
                    text-align: center;
                }

                .successTit {
                    @include font16;
                    color: $textColor1;
                    margin-top: 16px;
                    text-align: center;
                    font-weight: 600;
                }
                .error_input {
                    // @include errorInput;
                }
            }

            .verify-table-right {
                display: flex;
                flex-direction: column;
                row-gap: 24px;
                // column-gap: 24px;
                // margin-bottom: 36px;
                padding-right: 36px;
                width: 50%;
                img {
                    display: block;
                    width: 216px;
                    height: 94px;
                }

                .txt01 {
                    @include font14;
                    color: $textColor1;
                }

                .txt02 {
                    @include font13;
                    margin-top: 12px;
                    color: $textColor3;
                }
            }

            .title {
                @include font12;
                font-weight: 400;
                color: $textColor3;
                margin-bottom: 4px;
            }
        }
    }
}
@media (max-width: 1024px) {
    .content {
        .main {
            .verify-table {
                .verify-table-left {
                    // padding-right: 182px;
                    // .captcha-box {
                    //     display: block;

                    //     .captcha {
                    //         margin: 12px 0 0 0;
                    //     }
                    // }
                }
            }
        }
    }
}

@media (max-width: 768px) {
    .content {
        padding: 0;
        .main {
            width: 100%;
            .verify-table {
                display: block;
                padding: 0px;

                .verify-table-left {
                    width: 100%;
                    padding: 20px 0 0 0;
                    border: none;
                    border-top: 1px solid $borderColor2;
                    margin-top: 20px;
                    &.success,
                    &.error {
                        padding: 56px 0 36px;
                    }

                    .captcha-box {
                        display: flex;

                        .captcha {
                            margin: 0 0 0 12px;
                        }
                    }
                    .fs-button {
                        // width: 100%;
                        padding: 10px 24px;
                    }
                }
                .subBtn {
                    width: 100%;
                }

                .verify-table-right {
                    // flex-direction: column;
                    // column-gap: 0;
                    // row-gap: 24px;
                    width: 100%;
                    margin-bottom: 0;
                    // padding-bottom: 36px;
                    padding-right: 0;
                    // border-bottom: 1px solid $borderColor2;
                }
            }
        }
    }
}
</style>
