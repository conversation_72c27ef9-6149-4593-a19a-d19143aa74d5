<template>
    <div class="support">
        <div class="title-wrap">
            <div class="title">
                {{ this.$c("pages.LivechatServicemail.breadCrumbs.third") }}
            </div>
            <div class="bread-crumb-wrap">
                <bread-crumb class="bread-crumb" :list="crumbs" size="small"></bread-crumb>
            </div>
        </div>

        <div class="step-wrap">
            <div class="step-box">
                <div class="m-step-text">
                    {{ mStepText }}
                </div>
                <div class="step-list-wrap">
                    <!-- 3是最后已提交了 -->
                    <div
                        class="step-list"
                        :class="{ 'step-over': stepIndex > index, 'now-step': stepIndex === index, 'step-end': stepIndex === 3 }"
                        v-for="(item, index) in stepList"
                        :key="index"
                        @click="index < stepIndex ? handleStepItemClick(index) : null">
                        <div class="step-top">
                            <div class="step-circle">
                                <div></div>
                            </div>
                            <div class="step-line"></div>
                            <div class="step-line"></div>
                        </div>
                        <div class="step-text" v-html="item"></div>
                    </div>
                </div>
            </div>
        </div>
        <div class="step-content">
            <component :is="componentInstance" :lang="lang" @changeStepIndex="emitChangeStepIndex" @keepData="emitKeepData" @prevStep="handleStepItemClick"></component>
        </div>
    </div>
</template>

<script>
import BreadCrumb from "@/components/BreadCrumb/BreadCrumb"
import RequirementsDefinition from "./components/RequirementsDefinition"
import ProblemDescription from "./components/ProblemDescription"
import BasicInfo from "./components/BasicInfo"
import submitWaitReply from "./components/submitWaitReply"
import { setCookieOptions } from "@/util/util"

const EXPIRES_TIME = 1000 * 60 * 60 * 24
const PATHS = [RequirementsDefinition, ProblemDescription, BasicInfo, submitWaitReply]
export default {
    name: "LivechatServicemail",
    components: { BreadCrumb },

    computed: {
        mStepText() {
            return this.stepList[this.stepIndex]
        },
        componentInstance() {
            const path = PATHS[this.stepIndex]
            if (path) {
                return path
            } else {
                return ""
            }
        },
    },
    data() {
        return {
            crumbs: [
                {
                    name: this.$c("pages.LivechatServicemail.breadCrumbs.first"),
                    url: "/",
                },
                {
                    name: this.$c("pages.LivechatServicemail.breadCrumbs.second"),
                    url: "/support.html",
                },
                {
                    name: this.$c("pages.LivechatServicemail.breadCrumbs.third"),
                    url: "",
                },
            ],
            stepIndex: 0,
            stepList: [
                this.$c("pages.LivechatServicemail.step.first"),
                this.$c("pages.LivechatServicemail.step.second"),
                this.$c("pages.LivechatServicemail.step.third"),
                this.$c("pages.LivechatServicemail.step.fourth"),
            ],
            stepComponent: RequirementsDefinition,
        }
    },
    provide() {
        return {
            provideEchoData: this.provideEchoData,
        }
    },
    asyncData({ app }) {
        const currentDate = +new Date()
        const keepDate = app.$cookies.get("solution_support_expires")
        let isClean = false,
            stepIndex = app.$cookies.get("solution_support_step") || 0
        if (keepDate && currentDate - keepDate > EXPIRES_TIME) {
            app.$cookies.remove("solution_support_step")
            isClean = true
            stepIndex = 0
        }
        return app.$axios
            .get(`/api/certificationRequest`)
            .then((res) => {
                return {
                    lang: res.data,
                    stepIndex,
                    isClean,
                }
            })
            .catch((error) => {})
    },
    created() {
        this.adaptiveDataKeepDiff()
        this.dataExpiresDispose()
    },
    methods: {
        // 动态子组件回显数据获取
        provideEchoData() {
            try {
                let localStorageData = localStorage.getItem("solution_support")
                if (localStorageData) {
                    localStorageData = JSON.parse(localStorageData)
                    return localStorageData
                } else {
                    this.stepIndex = 0
                    this.keepStepIndex()
                }
            } catch (error) {
                console.log(error.msg)
            }
        },
        keepStepIndex() {
            try {
                this.$cookies.set("solution_support_step", this.stepIndex)
            } catch (error) {
                console.log(error.msg)
            }
        },
        clearSolutionSupportStepData(index) {
            try {
                let localStorageData = localStorage.getItem("solution_support")
                if (localStorageData) {
                    localStorageData = JSON.parse(localStorageData) || []
                    localStorage.setItem("solution_support", JSON.stringify(localStorageData.filter((i, idx) => idx <= index)))
                }
            } catch (error) {
                console.log(error.msg)
            }
        },
        adaptiveDataKeepDiff() {
            try {
                const $cookies = this.$cookies.get("solution_support_step")
                if ($cookies === undefined) {
                    localStorage.removeItem("solution_support")
                }
            } catch (error) {
                console.log(error.msg)
            }
        },
        dataExpiresDispose() {
            try {
                if (this.isClean) {
                    this.clearLocalStorageData()
                }
            } catch (error) {
                console.log(error.msg)
            }
        },
        keepExpires() {
            this.$cookies.set("solution_support_expires", +new Date())
        },
        clearLocalStorageData() {
            localStorage.removeItem("solution_support")
            this.$cookies.remove("solution_support_expires")
        },
        // 点击进度条
        handleStepItemClick(index) {
            if (this.stepIndex >= 3) return
            if (this.stepIndex > index) {
                this.stepIndex = index
                this.keepStepIndex()
                this.clearSolutionSupportStepData(index)
                this.scrollToYZero()
            }
        },
        // 点击下一步保存当前的index
        emitChangeStepIndex(index) {
            this.stepIndex = index !== undefined ? index : this.stepIndex + 1
            this.keepStepIndex()
            this.scrollToYZero()
        },
        // 点击下一步保存当前的data
        emitKeepData(data) {
            try {
                let localStorageData = localStorage.getItem("solution_support")
                localStorageData = localStorageData ? JSON.parse(localStorageData) : []
                localStorageData[this.stepIndex] = data
                localStorage.setItem("solution_support", JSON.stringify(localStorageData))
                this.keepExpires()
            } catch (error) {
                console.log(error.msg)
            }
        },
        scrollToYZero() {
            this.$nextTick(() => {
                window.scrollTo(0, 0)
            })
        },
    },
}
</script>
<style scoped lang="scss">
.support {
    .bread-crumb-wrap {
        background-color: #f7f7f7;
        .bread-crumb {
            width: 84vw;
            max-width: 1200px;
            margin: 0 auto;
        }
    }
    .title-wrap {
        border-bottom: 1px solid #f2f2f2;

        .title {
            margin: 0 auto;
            display: flex;
            align-items: center;
            font-size: 34px;
            line-height: 42px;
            font-weight: 600;
            justify-content: center;
            height: 200px;
            @include bgcover("https://resource.fs.com/mall/generalImg/20221129100927yjyjks.jpg");
            @media (max-width: 768px) {
                font-size: 24px;
                line-height: 32px;
                height: 233px;
                @include bgcover("https://resource.fs.com/mall/generalImg/20221129100927ngrwhh.jpg");
            }
        }
    }
    .step-wrap {
        border-bottom: 1px solid #f2f2f2;
        .step-box {
            padding: 24px 0 20px 0;
            display: flex;
            justify-content: center;

            max-width: 800px;
            margin: 0 auto;
            .m-step-text {
                display: none;
                justify-content: center;
                font-size: 16px;
                font-weight: 600;
                line-height: 24px;
                margin-bottom: 21px;
            }
            .step-list-wrap {
                display: flex;
                // justify-content: center;
                width: 100%;
            }
            .step-list {
                // display: flex;
                // align-items: center;
                flex: 1;
                &:first-child {
                    .step-circle + .step-line {
                        display: none;
                    }
                }
                &:last-child {
                    .step-line + .step-line {
                        display: none;
                    }
                }
                .step-top {
                    position: relative;
                    margin-bottom: 8px;

                    .step-circle {
                        width: 16px;
                        height: 16px;
                        border-radius: 50%;
                        border: 1px solid #cccccc;
                        position: relative;
                        margin: 0 auto;
                        z-index: 2;
                        background: white;
                        > div {
                            position: absolute;
                            top: 0;
                            left: 0;
                            width: 100%;
                            height: 100%;
                            border-radius: 50%;
                        }
                    }
                    .step-line {
                        width: 50%;
                        position: absolute;
                        top: 50%;
                        transform: translateY(-50%);
                        height: 4px;
                        background: #cccccc;
                        &:first-of-type {
                            left: 0;
                        }
                        &:last-of-type {
                            right: 0;
                        }
                    }
                }

                .step-text {
                    @include font14;
                    color: #707070;
                    text-align: center;
                    white-space: pre-line;
                }
            }
            .step-over {
                cursor: pointer;
                .step-top {
                    .step-circle {
                        background: #10a300;
                        border: none;
                    }
                    .step-line {
                        background: #10a300;
                    }
                }
                .step-text {
                    color: #19191a;
                }
                &:hover {
                    text-decoration: underline;
                    .step-circle {
                        > div {
                            background: #19191a;
                            opacity: 0.25;
                        }
                    }
                }
            }
            .step-end {
                cursor: auto;
                &:hover {
                    text-decoration: none;
                    .step-circle {
                        > div {
                            background: none;
                        }
                    }
                }
            }
            .now-step {
                .step-top {
                    .step-circle {
                        width: 16px;
                        height: 16px;
                        background: #10a300;
                        border-radius: 50%;
                        position: relative;
                        border: none;
                        z-index: 2;

                        &::before {
                            content: "";
                            width: 24px;
                            height: 24px;
                            border-radius: 50%;
                            border: 2px solid #10a300;
                            position: absolute;
                            box-sizing: border-box;
                            left: -4px;
                            top: -4px;
                        }
                        & + .step-line {
                            background: #10a300;
                        }
                    }
                    .step-line {
                        width: calc(50% - 10px);
                    }
                }
                .step-text {
                    font-weight: 600;
                    color: #19191a;
                }
            }
        }
    }
}
@media (max-width: 414px) {
    .support {
        .title-wrap {
        }
        .step-box {
            padding: 20px 0 25px 0;
            flex-direction: column;
            max-width: 376px;
            .m-step-text {
                display: flex;
            }
            .step-list {
                .step-text {
                    display: none;
                }
            }
        }
    }
}
</style>
