<template>
    <div class="solution_step_2">
        <!-- <p class="title">{{ $c("single.SolutionDesign.public.two[0]") }}</p> -->
        <div class="form_list" :class="{ form_list_cn: ['cn', 'hk', 'tw', 'mo'].includes(website) }">
            <div class="form_item" @click="handleInputClick($event, 'First Name')">
                <p class="txt">{{ $c("form.form.first_name") }}{{ website === "jp" ? "（必須）" : " *" }}</p>
                <input
                    type="text"
                    class="inp is_new"
                    v-model="user.first_name"
                    @input="focusInput('first_name')"
                    @blur="blurInput('first_name')"
                    :placeholder="countries_id === 107 ? $c('form.form.jpFormPlaceHolder.firstName') : ''" />
                <div class="error-box">
                    <validate-error :error="errors.first_name"></validate-error>
                </div>
            </div>
            <div class="form_item" @click="handleInputClick($event, 'Last Name')">
                <p class="txt">{{ $c("form.form.last_name") }}{{ website === "jp" ? "（必須）" : " *" }}</p>
                <input
                    type="text"
                    class="inp is_new"
                    v-model="user.last_name"
                    @input="focusInput('last_name')"
                    @blur="blurInput('last_name')"
                    :placeholder="countries_id === 107 ? $c('form.form.jpFormPlaceHolder.lastName') : ''" />
                <div class="error-box">
                    <validate-error :error="errors.last_name"></validate-error>
                </div>
            </div>
        </div>
        <div class="form_list">
            <div class="form_item" @click="handleInputClick($event, 'Business Email')">
                <p class="txt">{{ $c("single.SolutionDesign.public.two[3]") }}{{ website === "jp" ? "（必須）" : website === "cn" ? "" : " *" }}</p>
                <input type="text" class="inp is_new" v-model="user.email" @input="focusInput('email')" @blur="blurInput('email')" :placeholder="countries_id === 107 ? $c('form.form.jpFormPlaceHolder.email') : ''" />
                <div class="error-box">
                    <validate-error :error="errors.email.replace('XXXX', localePath({ name: 'login', query: { redirect: $route.fullPath } }))"></validate-error>
                </div>
            </div>
            <div class="form_item" @click="handleInputClick($event, 'Company Name')">
                <p class="txt">{{ $c("single.SolutionDesign.public.two[5]") }}{{ website === "jp" ? "（必須）" : " *" }}</p>
                <input
                    type="text"
                    class="inp is_new"
                    v-model="user.company"
                    @input="focusInput('company')"
                    maxlength="120"
                    @blur="blurInput('company')"
                    :placeholder="countries_id === 107 ? $c('form.form.jpFormPlaceHolder.company') : ''" />
                <div class="error-box">
                    <validate-error :error="errors.company"></validate-error>
                </div>
            </div>
            <div class="country-box">
                <div class="form_item" @click="handleInputClick($event, 'Country/Region')">
                    <p class="txt">{{ $c(country_label) }}</p>
                    <select-country :isNewStyle="true" v-model="user.iso_code" @change="selectCountry"></select-country>
                    <div class="error-box">
                        <validate-error :error="errors.iso_code"></validate-error>
                    </div>
                </div>
                <div class="form_item" v-if="isShowState">
                    <p class="txt">{{ $c("single.ProductReturnForm.state.tit") }}</p>
                    <RegionSelect :isNewStyle="true" ref="regionSelect" />
                </div>
            </div>
            <div class="form_item" @click="handleInputClick($event, 'Phone Number')">
                <p class="txt">{{ $c("form.form.phone_number") }}{{ website === "jp" ? "（必須）" : " *" }}</p>
                <tel-code
                    :isNewStyle="true"
                    code="1"
                    @changeCode="changeCode"
                    :phone="user.phone_number"
                    @change="telChange"
                    @input="telInput"
                    :placeholder="countries_id === 107 ? $c('form.form.jpFormPlaceHolder.telephone') : ''"></tel-code>
                <div class="error-box">
                    <validate-error :error="errors.phone_number.replace('XXXX', localePath({ name: 'login', query: { redirect: $route.fullPath } }))"></validate-error>
                </div>
            </div>
            <div class="form_item" @click="handleInputClick($event, 'Job Title')">
                <p class="txt">{{ $c("single.SolutionDesign.right.job_title") }}</p>
                <!-- <select v-show="showInp1" class="select" v-model="user.job_title" @change="handleSelect1($event)">
                    <option value="">{{ $c("form.form.please_select") }}</option>
                    <option v-for="(item, index) in job_data" :value="item.option" :key="index">{{ item.option }}</option>
                </select> -->
                <!-- <fs-select v-if="showInp1" :options="job_data" v-model="user.job_title" @change="handleSelect1" customField="option"></fs-select> -->
                <input type="text" class="inp is_new" v-model="user.job_title" @input="focusInput('job_title')" />
                <div class="error-box">
                    <validate-error :error="errors.job_title"></validate-error>
                </div>
            </div>
            <div class="form_item" @click="handleInputClick($event, 'Industry')">
                <p class="txt">{{ $c("form.form.industry") }}</p>
                <!-- <select class="select" v-model="user.industry" @change="handleSelect2($event)" v-show="showInp2">
                    <option value="" class="first_option">{{ $c("form.form.please_select") }}</option>
                    <option v-for="(item, index) in industry_data" :value="item.option" :key="index">{{ item.option }}</option>
                </select> -->
                <fs-select :isNewStyle="true" :options="industry_data" v-model="user.industry" @change="handleSelect2" :other="true"> </fs-select>
                <!-- <input v-else type="text" class="inp" v-model="user.industry" /> -->
            </div>
        </div>
        <div class="policy_box">
            <input v-model="user.isAgreePolicy" @change="blurInput('isAgreePolicy')" type="checkbox" class="chk" />
            <div
                @click="handleGa"
                class="form_agreement"
                v-html="
                    $c('form.validate.aggree_policy_new')
                        .replace('AAAA', localePath({ name: 'privacy-notice' }))
                        .replace('BBBB', localePath({ name: 'terms-of-use' }))
                "></div>
        </div>
        <validate-error :error="errors.isAgreePolicy"></validate-error>
    </div>
</template>

<script>
import TelCode from "@/components/TelCode/TelCode"
import FsSelect from "@/components/FsSelect/FsSelect"
import ValidateError from "@/components/ValidateError/ValidateError"
import SelectCountry from "@/components/SelectCountry/SelectCountry.vue"
import { email_valdate, phone_validate, cn_mobile_tel } from "@/constants/validate.js"
import { mapState, mapGetters } from "vuex"
import RegionSelect from "@/components/RegionSelect/RegionSelect"
export default {
    components: {
        TelCode,
        FsSelect,
        ValidateError,
        SelectCountry,
        RegionSelect,
    },
    computed: {
        ...mapState({
            userInfo: (state) => state.userInfo.userInfo,
            isLogin: (state) => state.userInfo.isLogin,
            iso_code: (state) => state.webSiteInfo.iso_code,
            website: (state) => state.webSiteInfo.website,
            countries_id: (state) => state.webSiteInfo.countries_id,
        }),
        ...mapGetters({
            isShowState: "selectCountry/isShowState",
            country_label: "selectCountry/country_label",
        }),
    },
    watch: {
        user: {
            deep: true,
            handler(val) {
                this.$emit("isNextCheck", this.isNext())
            },
        },
    },
    data() {
        return {
            solutionTypeOptions: [
                {
                    name: "OTN Solution",
                    value: "1",
                },
            ],
            regExp: /^\d{6,}$/,
            user: {
                first_name: "",
                last_name: "",
                email: "",
                company: "",
                iso_code: "",
                phone_number: "",
                job_title: "",
                industry: "",
                isAgreePolicy: false,
            },
            errors: {
                type: null,
                first_name: "",
                last_name: "",
                email: "",
                company: "",
                iso_code: "",
                phone_number: "",
                job_title: "",
                industry: "",
                isAgreePolicy: "",
            },
            showInp1: true,
            showInp2: true,
            job_data: this.$c("single.SolutionDesign.right.job_select"),
            industry_data: this.$c("single.ContactSales.industry"),
            code: "",
        }
    },
    methods: {
        changeCode(code) {
            this.code = code
        },
        // 聚焦输入框
        focusInput(attr) {
            // this.errors[attr] = ""
            this.blurInput(attr)
        },
        // 输入框失焦
        blurInput(attr) {
            if (attr === "first_name") {
                if (!this.user.first_name.replace(/^\s+|\s+$/g, "")) {
                    this.errors.first_name = this.$c("form.validate.first_name.first_name_required")
                } else if (this.user.first_name.length > 40) {
                    this.errors.first_name = this.$c("pages.confirmOrder.form.first_name_max")
                } else {
                    this.errors.first_name = ""
                }
            }
            if (attr === "last_name") {
                if (!this.user.last_name.replace(/^\s+|\s+$/g, "")) {
                    this.errors.last_name = this.$c("form.validate.last_name.last_name_required")
                } else if (this.user.last_name.length > 40) {
                    this.errors.last_name = this.$c("pages.confirmOrder.form.last_name_max")
                } else {
                    this.errors.last_name = ""
                }
            }
            if (attr === "email") {
                if (!["cn"].includes(this.website)) {
                    if (!this.user.email.replace(/^\s+|\s+$/g, "")) {
                        this.errors.email = this.$c("form.form.errors.email_business_error")
                    } else if (!email_valdate.test(this.user.email)) {
                        this.errors.email = this.$c("form.form.errors.email_business_error01")
                    } else {
                        this.errors.email = ""
                    }
                } else {
                    if (this.user.email.replace(/^\s+|\s+$/g, "")) {
                        if (this.isLogin == 1) {
                            this.errors.email = ""
                        } else {
                            this.$axios
                                .post("/api/user/isHasRegister", { customers_name: this.user.email })
                                .then((res) => {
                                    if (res.code != 200) return
                                    const data = res.data
                                    if (data && data.is_has) {
                                        this.errors.email = "账户已存在，单击此处<a href=XXXX title='登录'>登录</a>。"
                                    } else {
                                        this.errors.email = ""
                                    }
                                })
                                .catch((e) => {
                                    this.errors.email = ""
                                })
                        }
                    }
                }
            }
            if (attr === "country") {
                if (!this.user.iso_code.replace(/^\s+|\s+$/g, "")) {
                    this.errors.iso_code = this.$c("single.SolutionDesign.right.errors.country_code_error")
                } else {
                    this.errors.iso_code = ""
                }
            }
            if (attr === "company") {
                if (!this.user.company.replace(/^\s+|\s+$/g, "")) {
                    this.errors.company = this.$c("single.SolutionDesign.right.errors.company_error")
                } else {
                    this.errors.company = ""
                }
            }
            if (attr === "isAgreePolicy") {
                this.errors.isAgreePolicy = this.user.isAgreePolicy ? "" : this.$c("form.form.errors.check2_error")
            }
        },
        selectCountry(item) {
            this.user.iso_code = item.iso_code
            this.errors.iso_code = ""
        },
        // telChange(inp) {
        //     this.user.phone_number = inp
        //     // 判断是否为6位数字
        //     if (!inp.replace(/^\s+|\s+$/g, "")) {
        //         this.errors.phone_number = this.$c("form.form.errors.entry_telephone_error")
        //     } else if (!this.regExp.test(inp)) {
        //         this.errors.phone_number = this.$c("form.form.errors.entry_telephone_error01")
        //     } else {
        //         this.errors.phone_number = ""
        //     }
        // },
        telInput(inp) {
            this.telChange(inp)
        },
        telChange(inp) {
            this.user.phone_number = inp
            // 判断是否为6位数字
            if (!inp.replace(/^\s+|\s+$/g, "")) {
                this.errors.phone_number = this.$c("form.form.errors.entry_telephone_error")
            } else {
                if (!["cn", "cn"].includes(this.website)) {
                    if (inp.length > 0 && inp.length < 6) {
                        this.errors.phone_number = this.$c("pages.NetTermsApplication.validate.phone.phone_min")
                    } else if (inp.length > 40) {
                        this.errors.phone_number = this.$c("pages.NetTermsApplication.validate.phone.phone_validate")
                    } else {
                        this.errors.phone_number = ""
                    }
                } else {
                    if (!cn_mobile_tel.test(inp)) {
                        this.errors.phone_number = this.$c("form.form.errors.entry_telephone_error01")
                    } else {
                        if (this.isLogin == 1) {
                            this.errors.phone_number = ""
                        } else {
                            this.$axios
                                .post("/api/user/isHasRegister", { customers_name: inp })
                                .then((res) => {
                                    if (res.code != 200) return
                                    const data = res.data
                                    if (data && data.is_has) {
                                        this.errors.phone_number = "账户已存在，单击此处<a href=XXXX title='登录'>登录</a>。"
                                    } else {
                                        this.errors.phone_number = ""
                                    }
                                })
                                .catch((e) => {
                                    this.errors.phone_number = ""
                                })
                        }
                    }
                }
            }
        },
        handleSelect1(value) {
            if (this.user.job_title) {
                this.errors.job_title = ""
            }
            if (value == "Other") {
                this.showInp1 = false
                this.user.job_title = ""
            }
        },
        handleSelect2(value) {
            if (this.user.industry) {
                this.errors.industry = ""
            }
            // if (value == "Other") {
            //     this.showInp2 = false
            //     this.user.industry = ""
            // }
        },
        checkUser(cb) {
            let flag = false
            if (!this.user.first_name.replace(/^\s+|\s+$/g, "")) {
                this.errors.first_name = this.$c("form.validate.first_name.first_name_required")
                flag = true
            }
            if (!this.user.last_name.replace(/^\s+|\s+$/g, "")) {
                this.errors.last_name = this.$c("form.validate.last_name.last_name_required")
                flag = true
            }
            if (!["cn", "cn"].includes(this.website)) {
                if (!this.user.email.replace(/^\s+|\s+$/g, "")) {
                    this.errors.email = this.$c("form.form.errors.email_business_error")
                    flag = true
                } else if (!email_valdate.test(this.user.email)) {
                    this.errors.email = this.$c("form.form.errors.email_business_error01")
                    flag = true
                } else if (this.errors.email) {
                    flag = true
                }
            }
            if (!this.user.phone_number.replace(/^\s+|\s+$/g, "")) {
                this.errors.phone_number = this.$c("form.form.errors.entry_telephone_error")
                flag = true
            } else if (!phone_validate.test(this.user.phone_number)) {
                this.errors.phone_number = this.$c("form.form.errors.entry_telephone_error01")
                flag = true
            } else if (this.errors.phone_number) {
                flag = true
            }
            if (!this.user.iso_code.replace(/^\s+|\s+$/g, "")) {
                this.errors.iso_code = this.$c("single.SolutionDesign.right.errors.country_code_error")
                flag = true
            }
            if (!this.user.company.replace(/^\s+|\s+$/g, "")) {
                this.errors.company = this.$c("single.SolutionDesign.right.errors.company_error")
                flag = true
            }
            if (!this.user.isAgreePolicy) {
                this.errors.isAgreePolicy = this.$c("form.form.errors.check2_error")
                flag = true
            }

            if (this.website == "cn") {
                this.sensite((next) => {
                    flag = flag ? flag : next
                    cb(flag)
                })
            } else {
                cb(flag)
            }

            return flag
        },
        sensite(cb) {
            let flag = false
            let data = JSON.parse(JSON.stringify(this.user))
            this.$axios
                .post("/api/sensi_words", data)
                .then((res) => {
                    if (res.code === 200 && res.status === "sensiWords") {
                        for (let key in res.errors) {
                            if (typeof key == "string") {
                                this.errors[key] = this.$c("form.form.errors.sensiWords")
                            }
                        }
                        flag = true
                    }
                    cb(flag)
                })
                .catch((err) => {
                    cb(flag)
                })
        },
        isNext() {
            let flag = false
            if (!this.user.first_name.replace(/^\s+|\s+$/g, "")) {
                flag = true
            }
            if (!this.user.last_name.replace(/^\s+|\s+$/g, "")) {
                flag = true
            }
            if (!this.user.email.replace(/^\s+|\s+$/g, "")) {
                flag = true
            } else if (!email_valdate.test(this.user.email)) {
                flag = true
            }
            if (!this.user.phone_number.replace(/^\s+|\s+$/g, "")) {
                flag = true
            } else if (!phone_validate.test(this.user.phone_number)) {
                flag = true
            }
            if (!this.user.iso_code.replace(/^\s+|\s+$/g, "")) {
                flag = true
            }
            if (!this.user.company.replace(/^\s+|\s+$/g, "")) {
                flag = true
            }
            return flag
        },
        handleInputClick({ target }, label) {
            if (target.nodeName === "INPUT" || target.nodeName === "SELECT" || target.className === "country-name") {
                const eventLabel = `${label} ${target.nodeName === "INPUT" ? "Input" : "Drop-Down"}`
                this.gaEvent(eventLabel)
            }
        },
        handleGa({ target: { href } }) {
            if (href) {
                const res = href.indexOf("privacy_policy") != -1
                const res2 = href.indexOf("/terms_of_use.html") != -1
                if (res) {
                    this.gaEvent("privacy_policy")
                } else if (res2) {
                    this.gaEvent("terms_of_use")
                }
            }
        },
        gaEvent(eventLabel) {
            window.dataLayer &&
                window.dataLayer.push({
                    event: "uaEvent",
                    eventCategory: "Solution Design Page",
                    eventAction: "basic_information",
                    eventLabel,
                    nonInteraction: false,
                })
        },
        resetError() {
            this.errors = {
                type: null,
                first_name: "",
                last_name: "",
                email: "",
                company: "",
                iso_code: "",
                phone_number: "",
                job_title: "",
                industry: "",
                isAgreePolicy: "",
            }
        },
    },
    mounted() {
        this.user.iso_code = this.iso_code
        if (this.isLogin) {
            this.user.first_name = this.userInfo.customers_firstname
            this.user.last_name = this.userInfo.customers_lastname
            this.user.email = this.userInfo.customers_email_address
            this.user.company = this.userInfo.customers_company ? this.userInfo.customers_company : ""
            this.user.phone_number = this.userInfo.customers_telephone
        }
        this.job_data = this.job_data.map((item) => {
            return {
                ...item,
                name: item.value,
            }
        })
        // this.industry_data = this.industry_data.map((item) => {
        //     return {
        //         ...item,
        //         name: item.value,
        //     }
        // })
    },
}
</script>

<style lang="scss" scoped>
.solution_step_2 {
    .title {
        @include font24;
        font-weight: 600;
        color: $textColor1;
        margin-bottom: 8px;
    }

    .form_list {
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;

        &.form_list_cn {
            flex-direction: row-reverse;
        }

        ::v-deep .error_info {
            a {
                color: $textColor4;
                text-decoration: underline;
            }
        }

        .country-box {
            display: flex;
            width: calc((100% - 20px) / 2);

            .form_item {
                // flex: 1;
                width: 50%;

                & + .form_item {
                    margin-left: 12px;
                }
            }

            @include mediaM {
                display: block;
                width: 100%;
                .form_item {
                    width: 100%;
                }
                .form_item + .form_item {
                    margin-left: 0;
                }
            }
        }

        .form_item {
            margin-bottom: 16px;
            width: calc((100% - 20px) / 2);

            @media (max-width: 768px) {
                width: 100%;
            }

            > p {
                @include font12;
                margin-bottom: 4px;
                color: $textColor3;
            }
        }

        ::v-deep {
            .tel-code .menu .menu-list,
            .select-country .country-wrap .country-box {
                max-height: 170px;
            }
        }

        ::v-deep .select-menu .menu-list {
            max-height: 166px;
        }
    }

    .form_agreement {
        @include font12;
        margin: 0 0;
        color: $textColor3;
    }
}
.policy_box {
    margin-top: 16px;
    display: flex;
    .chk {
        margin-top: 4px;
        margin-right: 8px;
        width: 14px;
        height: 14px;
        font-size: 14px;
    }
}

@media (max-width: 768px) {
    .solution_step_2 {
        .form_item {
            width: 100%;
        }

        .form_list {
            &.form_list_cn {
                flex-direction: column-reverse;
            }
        }
    }
}
</style>
