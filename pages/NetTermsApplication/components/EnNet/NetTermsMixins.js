import SlideDown from "@/components/SlideDown/SlideDown.vue"
import FsTip from "@/components/FsTip/FsTip.vue"
import FormItem from "../FormItem.vue"
import CkeckBoxs from "../CheckBox.vue"
import FsSelect from "@/components/FsSelect/FsSelect.vue"
import UploadFile from "@/components/UploadFile/UploadFile.vue"
import ValidateError from "@/components/ValidateError/ValidateError.vue"
import FillInformationFrom from "../FillInformationFrom.vue"
import ComponeyList from "../ComponeyList.vue"
import { scrollTo } from "@/util/util.js"
import { mapState, mapGetters } from "vuex"
import { email_valdate } from "@/constants/validate"
import FsButton from "@/components/FsButton/FsButton.vue"
import FsInlineTips from "@/components/FsInlineTips/FsInlineTips.vue"
export default {
    components: {
        SlideDown,
        FsTip,
        FormItem,
        UploadFile,
        FsSelect,
        ValidateError,
        CkeckBoxs,
        FillInformationFrom,
        ComponeyList,
        FsButton,
        FsInlineTips,
    },
    props: {
        info: {
            type: Object,
            default: () => {},
        },
        country_id: {
            type: Number | String,
            default: () => "",
        },
        titleListOption: {
            type: Array,
            default: () => [],
        },
    },
    data() {
        return {
            upload: {
                accept: ".pdf,.jpg,.png,.doc,.docx,.xls,.xlsx,.txt",
                text: this.$c("pages.answerQuestion.upload.txt"),
            },
            // 表单参数
            paramsForm: {
                businessInformation: {
                    companyName: "",
                    phone: "",
                    country: "",
                    state: "",
                    city: "",
                    address: "",
                    zipCode: "",
                    yearStated: "",
                    federalID: "",
                    ABN: "",
                    industry: "",
                    monthlySpend: "",
                    dunsNumber: "",
                    website: "",
                    address_book_id: "",
                    fileList: {},
                    prefnumber: "+1",
                },
                billingInfomation: {
                    isSameInfo: false,
                    companyName: "",
                    address: "",
                    phone: "",
                    city: "",
                    country: "",
                    state: "",
                    zipCode: "",
                    reason: "",
                    prefnumber: "+1",
                    billing_address_book_id: "",
                    federalID: "",
                },
                applicationType: {
                    net: "",
                    amount: "",
                    amountType: 1,
                    code: "USD",
                    bank_referen: {
                        fill: false,
                        upload: true,
                        files: {},
                        form: {
                            bank_name: "",
                            contact_name: "",
                            address: "",
                            phone: "",
                            city: "",
                            state: "",
                            country: "",
                            zip_code: "",
                            account_number: "",
                        },
                    },
                    busine_referen: {
                        fill: false,
                        upload: true,
                        files: {},
                    },
                    businessReference: [
                        {
                            title: this.$c("pages.NetTermsApplication.applicationType.contactInformation.companys.title1"),
                            form: {
                                companyName: "",
                                contactName: "",
                                email: "",
                                phone: "",
                            },
                        },
                        {
                            title: this.$c("pages.NetTermsApplication.applicationType.contactInformation.companys.title2"),
                            form: {
                                companyName: "",
                                contactName: "",
                                email: "",
                                phone: "",
                            },
                        },
                        {
                            title: this.$c("pages.NetTermsApplication.applicationType.contactInformation.companys.title3"),
                            form: {
                                companyName: "",
                                contactName: "",
                                email: "",
                                phone: "",
                            },
                        },
                    ],
                },
                contactInfo: {
                    first_name: "",
                    last_name: "",
                    title: "",
                    emali: "",
                    tel_phone: "",
                    prefnumber: "",
                },
                accountPay: {
                    isSameInfo: false,
                    first_name: "",
                    last_name: "",
                    title: "",
                    emali: "",
                    tel_phone: "",
                    prefnumber: "",
                },
            },
            //错误信息
            errors: {
                businessInformation: {
                    companyName: "",
                    address: "",
                    phone: "",
                    city: "",
                    state: "",
                    country: "",
                    zipCode: "",
                    yearStated: "",
                    federalID: "",
                    ABN: "",
                    industry: "",
                    monthlySpend: "",
                    dunsNumber: "",
                    website: "",
                },
                billingInfomation: {
                    companyName: "",
                    address: "",
                    phone: "",
                    city: "",
                    country: "",
                    state: "",
                    zipCode: "",
                    federalID: "",
                    reason: "",
                },
                applicationType: {
                    net: "",
                    amount: "",
                    amountType: "",
                    code: "",
                    bank_referen: {
                        files: "",
                    },
                    busine_referen: {
                        files: "",
                    },
                },
                contactInfo: {
                    first_name: "",
                    last_name: "",
                    title: "",
                    emali: "",
                    tel_phone: "",
                },
                accountPay: {
                    first_name: "",
                    last_name: "",
                    title: "",
                    emali: "",
                    tel_phone: "",
                    prefnumber: "",
                },
                policys: {
                    privacy: "",
                    authorizedSign: "",
                    agreeInfoUse: "",
                },
            },
            policys: [
                {
                    text: this.$c("form.validate.aggree_policy_new")
                        .replace("AAAA", this.localePath({ name: "privacy-notice" }))
                        .replace("BBBB", this.localePath({ name: "terms-of-use" })),
                    label: "privacy",
                    checked: false,
                },
                { text: this.$c("pages.NetTermsApplication.applicationType.policys.text2"), label: "authorizedSign", checked: false },
                {
                    text: this.$c("pages.NetTermsApplication.applicationType.policys.text3"),
                    label: "agreeInfoUse",
                    checked: false,
                },
            ],
            // titleListOption: [],
            countries: {
                businessInformation: {},
                billingInfomation: {},
            },
            newFormOptions: {
                billingInfomation: [
                    { key: "companyName", label: this.$c("pages.NetTermsApplication.businessInformation.form.companyName"), disabled: this.isEuropeCountry },
                    { key: "phone", label: this.$c("pages.NetTermsApplication.applicationType.from.phoneNumber"), type: "phone", prefnumber: true, disabled: this.isEuropeCountry },
                    { key: "country", label: this.$c("pages.NetTermsApplication.businessInformation.form.country"), type: "country", disabled: this.isEuropeCountry },
                    { key: "state", label: this.$c("pages.NetTermsApplication.businessInformation.form.state"), type: "state", disabled: this.isEuropeCountry },
                    { key: "city", label: this.$c("pages.NetTermsApplication.businessInformation.form.city"), disabled: this.isEuropeCountry },
                    { key: "address", label: this.$c("pages.NetTermsApplication.businessInformation.form.address"), maxleng: 60, disabled: this.isEuropeCountry },
                    { key: "zipCode", label: this.isDeEn ? "Zip Code*" : this.$c("pages.NetTermsApplication.businessInformation.form.zip"), maxleng: 10, disabled: this.isEuropeCountry },
                ],
                contactInfo: [
                    { key: "first_name", label: this.$c("pages.NetTermsApplication.applicationType.from.first_name") },
                    { key: "last_name", label: this.$c("pages.NetTermsApplication.applicationType.from.last_name") },
                    { key: "title", label: this.$c("pages.NetTermsApplication.applicationType.from.title"), type: "select" },
                    { key: "emali", label: this.$c("pages.NetTermsApplication.applicationType.from.businessEmail"), disabled: true },
                    { key: "tel_phone", label: this.$c("pages.NetTermsApplication.applicationType.from.phoneNumber"), type: "phone" },
                ],
                accountPay: [
                    { key: "first_name", label: this.$c("pages.NetTermsApplication.applicationType.from.first_name") },
                    { key: "last_name", label: this.$c("pages.NetTermsApplication.applicationType.from.last_name") },
                    { key: "title", label: this.$c("pages.NetTermsApplication.applicationType.from.title"), type: "select" },
                    { key: "emali", label: this.$c("pages.NetTermsApplication.applicationType.from.businessEmail") },
                    { key: "tel_phone", label: this.$c("pages.NetTermsApplication.applicationType.from.phoneNumber"), type: "phone" },
                ],
            },
        }
    },
    methods: {
        //将base64转为blob
        dataToBlob(baseString, filename) {
            let arr = baseString.split(","),
                mime = arr[0].match(/:(.*?);/)[1],
                bstr = atob(arr[1]),
                n = bstr.length,
                u8arr = new Uint8Array(n)
            while (n--) {
                u8arr[n] = bstr.charCodeAt(n)
            }

            return new File([u8arr], filename, { type: mime })
        },
        // 修改图片的大小
        changeImgSize(imgfile) {
            const _this = this
            return new Promise((res, rej) => {
                let img = new Image()
                const reader = new FileReader()
                const size = {
                    width: 410,
                    height: 100,
                }
                reader.readAsDataURL(imgfile)
                reader.onload = (e) => {
                    img.src = e.currentTarget.result
                    img.onload = () => {
                        if (img.height > 842) {
                            size.width = 520
                        }
                        size.height = Math.floor((img.height * size.width) / img.width)
                        const canvas = document.createElement("canvas")
                        canvas.width = size.width
                        canvas.height = size.height
                        const ctx = canvas.getContext("2d")
                        ctx.drawImage(img, 0, 0, canvas.width, canvas.height)
                        const smbase64 = canvas.toDataURL("image/jpeg", 1)
                        const resultFile = _this.dataToBlob(smbase64, imgfile.name)
                        canvas.remove()
                        return res(resultFile)
                    }
                }
            })
        },
        // 校验输入项
        validata(value = "", key) {
            if (!value) {
                value = ""
            }
            let errStr = ""
            if (key === "companyName") {
                if (value.length < 1) {
                    errStr = this.$c("pages.NetTermsApplication.validate.company.company_required")
                } else if (value.length > 120) {
                    errStr = this.$c("pages.NetTermsApplication.validate.company.company_validate")
                }
                return errStr
            }
            if (key === "address") {
                if (value.length < 1) {
                    errStr = this.$c("pages.NetTermsApplication.validate.address.address_required")
                    // } else if (value.length > 100) {
                    //     errStr = this.$c("pages.NetTermsApplication.validate.address.address_validate")
                    // }
                } else if (value.length < 4 || value.length > 35) {
                    errStr = this.$c("pages.NetTermsApplication.validate.address.address_validate1")
                }
                return errStr
            }
            if (key === "phone" || key === "tel_phone") {
                if (value.length < 1) {
                    errStr = this.$c("pages.NetTermsApplication.validate.phone.phone_required")
                } else if (value.length > 0 && value.length < 6) {
                    errStr = this.$c("pages.NetTermsApplication.validate.phone.phone_min")
                } else if (value.length > 40) {
                    errStr = this.$c("pages.NetTermsApplication.validate.phone.phone_validate")
                }
                return errStr
            }
            if (key === "city") {
                if (value.length < 2) {
                    errStr = this.$c("pages.NetTermsApplication.validate.city.city_required")
                } else if (value.length > 40) {
                    errStr = this.$c("pages.NetTermsApplication.validate.city.city_validate")
                }
                return errStr
            }
            if (key === "state") {
                // if (value.length < 1) {
                //     errStr = this.$c("pages.NetTermsApplication.validate.state.state_required")
                // } else if (value.length > 100) {
                //     errStr = this.$c("pages.NetTermsApplication.validate.state.state_validate")
                // }
                if (value.length > 100) {
                    errStr = this.$c("pages.NetTermsApplication.validate.state.state_validate")
                }
                return errStr
            }
            if (key === "country") {
                if (value.length < 1) {
                    errStr = this.$c("pages.NetTermsApplication.validate.country.country_required")
                }
                return errStr
            }
            if (key === "zipCode") {
                if (value.length < 1) {
                    errStr = this.$c("pages.NetTermsApplication.validate.zip_code.zip_code_required")
                    // } else if (!(value.length == 5 || value.length == 9)) {
                } else if (value.length > 10) {
                    // errStr = this.$c("pages.NetTermsApplication.validate.zip_code.zip_code_validate")
                    errStr = this.$c("form.form.zipCodeMax")
                }
                return errStr
            }
            if (key === "reason") {
                const { isSameInfo } = this.paramsForm.billingInfomation
                if (!isSameInfo) {
                    if (value.length < 1) {
                        errStr = this.$c("pages.NetTermsApplication.validate.reason.reason_required")
                    }
                }
                return errStr
            }
            if (key === "yearStated") {
                if (value.length < 1) {
                    errStr = this.$c("pages.NetTermsApplication.validate.yearStated.yearStated_required")
                } else if (value.length > 100) {
                    errStr = this.$c("pages.NetTermsApplication.validate.yearStated.yearStated_validate")
                }
                return errStr
            }
            if (key === "federalID") {
                // if (value.length > 0 && value.length != 9) {
                //     errStr = this.$c("pages.NetTermsApplication.validate.federal.federal_validate")
                // }
                return errStr
            }
            if (key === "dunsNumber") {
                if (value.length > 0 && value.length != 9) {
                    errStr = this.$c("pages.NetTermsApplication.validate.DUNS.DUNS_validate")
                }
                return errStr
            }
            if (key === "website") {
                if (!value || value.length < 1) {
                    errStr = this.$c("pages.NetTermsApplication.validate.website.website_required")
                } else if (value.length > 1280) {
                    errStr = this.$c("pages.NetTermsApplication.validate.website.website_validate")
                }
                return errStr
            }
            if (key === "first_name") {
                if (value.length < 1) {
                    errStr = this.$c("pages.NetTermsApplication.validate.first_name.first_name_required")
                } else if (value.length > 40) {
                    errStr = this.$c("pages.NetTermsApplication.validate.first_name.first_name_max")
                }
                return errStr
            }
            if (key === "last_name") {
                if (value.length < 1) {
                    errStr = this.$c("pages.NetTermsApplication.validate.last_name.last_name_required")
                } else if (value.length > 40) {
                    errStr = this.$c("pages.NetTermsApplication.validate.last_name.last_name_max")
                }
                return errStr
            }
            if (key === "title") {
                if (!value) {
                    errStr = this.$c("pages.NetTermsApplication.validate.title.title_require")
                }
                return errStr
            }
            if (key === "tel_phone") {
                if (!value) {
                    errStr = this.$c("pages.NetTermsApplication.validate.phone.phone_required")
                }
            }
            if (key === "ABN") {
                if (!value) {
                    errStr = this.$c("pages.NetTermsApplication.validate.phone.phone_required")
                } else if (value.toString().length !== 11) {
                    errStr = this.$c("pages.NetTermsApplication.validate.abn.validate")
                }
            }
            if (key === "industry") {
                if (!value) {
                    errStr = this.$c("pages.NetTermsApplication.validate.phone.phone_required")
                }
            }
            if (key === "monthlySpend") {
                if (!value) {
                    errStr = this.$c("pages.NetTermsApplication.validate.phone.phone_required")
                }
            }
            if (key === "emali") {
                if (value.length < 1) {
                    errStr = this.$c("pages.NetTermsApplication.validate.phone.phone_required")
                } else if (!email_valdate.test(value)) {
                    errStr = this.$c("pages.NetTermsApplication.validate.email.email_valid")
                } else {
                    errStr = ""
                }
                console.log(errStr, "errStr")
            }

            return errStr
        },
        // 校验所有输入项
        validataAll() {
            const resultArr = [this.validataBusinessInformation(), this.validataBillingInformation(), this.validataApplicationType(), this.validataContactInformation(), this.validataAccountPay(), this.validataPolicys()]
            let isPass = resultArr.every((item) => item)
            // 校验institution
            const institutionBox = this.$refs.institutionBox
            if (institutionBox) {
                const insPass = institutionBox.validator()
                isPass = isPass && insPass
            }
            if (!isPass) {
                this.$nextTick(() => {
                    const doms = document.querySelectorAll(".error_info")
                    if (doms.length > 0) {
                        const target = doms[0].parentNode?.parentElement
                        scrollTo(target, 9, () => {}, -50)
                    }
                })
            }
            return isPass
        },
        //校验 Business Information
        validataBusinessInformation() {
            let businessVals = Object.keys(this.paramsForm.businessInformation)
            let excludeList = []
            const passValidatas = ["prefnumber", "fileList"]
            const { isAUStralia, isIMEWE, isSg } = this
            //针对澳大利亚和非澳大利亚国家进行字段过滤校验
            if (!isAUStralia) {
                excludeList.push(...["ABN", "monthlySpend", "industry"])
            } else {
                excludeList.push("federalID")
            }
            if (isIMEWE) {
                excludeList.push("dunsNumber")
            }
            if (isSg) {
                excludeList.push(...["yearStated", "federalID"])
            }
            businessVals = businessVals.filter((item) => !excludeList.includes(item))
            businessVals.forEach((item) => {
                if (!passValidatas.includes(item)) {
                    this.handlerBlur("businessInformation", item)
                }
            })
            console.log("23333:", this.errors.businessInformation)
            return Object.values(this.errors.businessInformation).every((item) => !item)
        },
        // 校验 Billing Information
        validataBillingInformation() {
            const arr = Object.keys(this.paramsForm.billingInfomation)
            arr.forEach((item) => {
                if (!["isSameInfo", "prefnumber"].includes(item)) {
                    this.handlerBlur("billingInfomation", item)
                }
            })
            const { isSameInfo } = this.paramsForm.billingInfomation
            let errorlist = Object.entries(this.errors.billingInfomation)
            const result = []
            if (isSameInfo) {
                errorlist.forEach((item) => {
                    if (item[0] !== "reason") {
                        result.push(item[1])
                    }
                })
            } else {
                errorlist.forEach((item) => {
                    result.push(item[1])
                })
            }
            return result.every((item) => !item)
        },
        //校验Application Type
        validataApplicationType() {
            const arr = Object.keys(this.paramsForm.applicationType)
            let resultArr = []
            arr.forEach((item) => {
                if (item === "bank_referen") {
                    if (!this.isIMEWE) {
                        const { fill, files } = this.paramsForm.applicationType.bank_referen
                        if (fill) {
                            const form = this.$refs["fill_information"]
                            let res = true
                            if (form) {
                                res = form.validataAll()
                            }
                            resultArr.push(res)
                        } else {
                            this.errors.applicationType.bank_referen.files = files.files ? "" : this.$c("pages.NetTermsApplication.validate.file.file_required")
                            resultArr.push(this.errors.applicationType.bank_referen.files.length < 1)
                        }
                    }
                } else if (item === "busine_referen") {
                    if (!this.busine_referen_required) {
                        const { fill, files } = this.paramsForm.applicationType.busine_referen
                        if (fill) {
                            const list = this.paramsForm.applicationType.businessReference
                            let res = []
                            list.forEach((i, k) => {
                                if (this.$refs[`reference${k + 1}`]) {
                                    const result = this.$refs[`reference${k + 1}`][0].validataAll()
                                    res.push(result)
                                }
                            })
                            resultArr = [...resultArr, ...res]
                        } else {
                            this.errors.applicationType.busine_referen.files = files.files && files.files.length > 0 ? "" : this.$c("pages.NetTermsApplication.validate.file.file_required")
                            resultArr.push(this.errors.applicationType.busine_referen.files.length < 1)
                        }
                    }
                } else if (item === "amount") {
                    const r = this.amountBlur()
                    resultArr.push(r)
                }
            })
            return resultArr.every((item) => item)
        },
        //校验 Accounts Payable Contact Information
        validataAccountPay() {
            const arr = Object.keys(this.paramsForm.accountPay)
            arr.forEach((item) => {
                if (item !== "prefnumber") {
                    this.handlerBlur("accountPay", item)
                }
            })
            return Object.values(this.errors.accountPay).every((item) => !item)
        },
        validataContactInformation() {
            const arr = Object.keys(this.paramsForm.contactInfo)
            arr.forEach((item) => {
                if (item !== "prefnumber") {
                    this.handlerBlur("contactInfo", item)
                }
            })
            return Object.values(this.errors.contactInfo).every((item) => !item)
        },
        //校验隐私政策
        validataPolicys(target) {
            const arr = this.policys.map(({ label, checked }) => {
                return { label, checked }
            })
            const errList = [
                this.$c("pages.NetTermsApplication.validate.policys.agreePolicy"),
                this.$c("pages.NetTermsApplication.validate.policys.policys_required"),
                this.$c("pages.NetTermsApplication.validate.policys.policys_required"),
            ]
            for (let i = 0; i < arr.length; i++) {
                if (target) {
                    const { label, checked } = arr[i]
                    if (label === target.label) {
                        this.errors.policys[arr[i].label] = checked ? "" : errList[i]
                    }
                } else {
                    if (!arr[i].checked) {
                        this.errors.policys[arr[i].label] = errList[i]
                    } else {
                        this.errors.policys[arr[i].label] = ""
                    }
                }
            }
            return Object.values(this.errors.policys).every((item) => !item)
        },
        // 输入框失焦事件
        handlerBlur(target, key) {
            // 排除不需要验证邮编的国家
            // if (key === "zipCode") {
            //     const country = this.paramsForm[target]?.country
            //     if (country === 96) {
            //         this.errors[target][key] = result
            //         return
            //     }
            // }
            this.$nextTick(() => {
                const result = this.validata(this.paramsForm[target][key], key)
                this.errors[target][key] = result
            })
        },
        handleInput(target, key, val) {
            if (target === "contactInfo" && key === "tel_phone") {
                this.handlerBlur(target, key)
            }
            this.paramsForm[target][key] = val
        },
        //选择地址博中地址
        slectItem(target, key, val) {
            if (target === "businessInformation") {
                this.paramsForm.businessInformation.companyName = val.entry_company
                this.paramsForm.businessInformation.address = val.entry_street_address
                this.paramsForm.businessInformation.phone = val.entry_telephone
                this.paramsForm.businessInformation.city = val.entry_city
                this.paramsForm.businessInformation.country = val.entry_country_id
                this.paramsForm.businessInformation.state = val.entry_state
                this.paramsForm.businessInformation.zipCode = val.entry_postcode
                this.paramsForm.businessInformation.address_book_id = val.address_book_id
                this.validataBusinessInformation()
            } else if (target === "billingInfomation") {
                this.paramsForm.billingInfomation.companyName = val.entry_company
                this.paramsForm.billingInfomation.address = val.entry_street_address
                this.paramsForm.billingInfomation.phone = val.entry_telephone
                this.paramsForm.billingInfomation.city = val.entry_city
                this.paramsForm.billingInfomation.country = val.entry_country_id
                this.paramsForm.billingInfomation.state = val.entry_state
                this.paramsForm.billingInfomation.zipCode = val.entry_postcode
                this.paramsForm.billingInfomation.billing_address_book_id = val.address_book_id
                this.validataBillingInformation()
            }
        },
        hanBillingInfoSame(val) {
            this.paramsForm.billingInfomation.isSameInfo = val
            if (val) {
                const arr = Object.keys(this.paramsForm.billingInfomation)
                for (let key of arr) {
                    const res = this.paramsForm.businessInformation[key]
                    if (res) {
                        // 排除Country & company_name  国家和公司名必须同注册的国家和公司名一致且不许更改
                        if (key === "companyName" || key === "country" || key === "prefnumber") {
                            continue
                        } else if (key === "state") {
                            this.$nextTick(() => {
                                this.paramsForm.billingInfomation[key] = res
                            })
                        } else {
                            this.paramsForm.billingInfomation[key] = res
                        }
                    }
                }
                // this.paramsForm.billingInfomation.billing_address_book_id = 0
                //触发校验规则
                this.validataBillingInformation()
            } else {
                // 清空输入
                const arr = Object.keys(this.paramsForm.billingInfomation)
                for (let key of arr) {
                    // 排除Country & company_name  国家和公司名必须同注册的国家和公司名一致且不许更改
                    if (!["isSameInfo", "billing_address_book_id", "companyName", "country", "prefnumber"].includes(key)) {
                        this.paramsForm.billingInfomation[key] = ""
                    }
                }
                this.paramsForm.billingInfomation.billing_address_book_id = 0
            }
        },
        // 初始化货币
        getInitPay(val) {
            // let { currency } = this
            const currency = val || this.currency
            const res = this.amountList.filter((item) => item.key === currency)
            if (res.length > 0) {
                this.codeChange(res[0].value)
                this.paramsForm.applicationType.amountType = res[0].value
            }
            console.log(111111)
        },
        // 处理货币种类
        codeChange(val) {
            const { key } = this.amountList.filter((item) => item.value === val)[0]
            this.paramsForm.applicationType.code = key
        },
        amountBlur() {
            this.isAmountFocus = false
            const value = this.paramsForm.applicationType.amount
            this.errors.applicationType.amount = value ? "" : this.$c("pages.NetTermsApplication.validate.amount.amount_required")
            return this.errors.applicationType.amount.length < 1
        },
        amountFocus() {
            this.isAmountFocus = true
        },
        bankFormChange(val) {
            this.paramsForm.applicationType.bank_referen.form = val
        },
        handePlicy({ target }) {
            if (target.className == "policy") {
                const url = this.$router.resolve(this.localePath("/policies/privacy_policy.html"))
                window.open(url.href, "_blank")
            } else if (target.className == "trems") {
                const url = this.$router.resolve(this.localePath("/policies/terms_of_use.html"))
                window.open(url.href, "_blank")
            } else {
                return
            }
        },
        handelBank(target, key) {
            const value = this.paramsForm.applicationType[target][key]
            if (!value) {
                if (key === "fill") {
                    this.paramsForm.applicationType[target].upload = false
                    this.paramsForm.applicationType[target].fill = true
                    this.errors.applicationType[target].files = ""
                    // this.paramsForm.applicationType[target].files = {}  //切换方式时是否清空已经上传的文件
                } else if (key === "upload") {
                    this.paramsForm.applicationType[target].upload = true
                    this.paramsForm.applicationType[target].fill = false
                }
            }
        },
        // 选择国家
        countryChange(target, val) {
            console.log(target, val)
            if (val) {
                this.paramsForm[target].country = val.countries_id
                // this.paramsForm[target].state = states[0]?.states_code || ""
                this.handlerBlur(target, "country")
                this.countries[target] = val.countries_name

                if (val.countries_id === 96) {
                    // 当cuntry为HongKong,China时 邮编默认为0000
                    this.paramsForm[target].zipCode = "0000"
                }
                if (["businessInformation", "billingInfomation"]) {
                    this.paramsForm[target].prefnumber = val.tel_prefix || "+1"
                }
            }
        },
        fillFormValue(value) {
            if (!value) return
            this.institutionType = value.institutionType
            //处理数据
            const arr = Object.keys(value)
            arr.forEach((key) => {
                if (key === "businessInformation") {
                    this.paramsForm.businessInformation.companyName = value[key].company_name
                    this.paramsForm.businessInformation.address = value[key].company_address
                    this.paramsForm.businessInformation.phone = value[key].company_phone
                    this.paramsForm.businessInformation.city = value[key].company_city
                    this.paramsForm.businessInformation.state = value[key].company_state
                    // this.paramsForm.businessInformation.country = value[key].company_country
                    this.paramsForm.businessInformation.zipCode = value[key].zip_code
                    this.paramsForm.businessInformation.yearStated = value[key].year_stated
                    this.paramsForm.businessInformation.federalID = value[key].tax_id
                    this.paramsForm.businessInformation.dunsNumber = value[key].duns_number
                    this.paramsForm.businessInformation.website = value[key].website || ""
                    this.paramsForm.businessInformation.address_book_id = value[key].address_book_id
                    this.paramsForm.businessInformation.ABN = value[key].abn
                    this.paramsForm.businessInformation.industry = value[key].industry
                    this.paramsForm.businessInformation.monthlySpend = value[key].estimated_monthly_spend
                    if (value[key].company_country !== 107) {
                        this.paramsForm.businessInformation.country = value[key].company_country
                    }
                }
                if (key === "billingInformation") {
                    this.paramsForm.billingInfomation.isSameInfo = value[key].is_same
                    this.paramsForm.billingInfomation.companyName = value[key].billing_company_name
                    this.paramsForm.billingInfomation.address = value[key].billing_company_address
                    this.paramsForm.billingInfomation.phone = value[key].billing_company_phone
                    this.paramsForm.billingInfomation.city = value[key].billing_company_city
                    this.paramsForm.billingInfomation.state = value[key].billing_company_state
                    this.paramsForm.billingInfomation.country = value[key].billing_company_country
                    this.paramsForm.billingInfomation.zipCode = value[key].billing_zip_code
                    this.paramsForm.billingInfomation.reason = value[key].reason
                    this.paramsForm.billingInfomation.billing_address_book_id = value[key].billing_address_book_id
                }
                // return
                if (key === "applicationType") {
                    this.paramsForm.applicationType.net = value[key].net_term
                    // this.paramsForm.applicationType.amount = value[key].amount ? (value[key].amount * 1).toFixed(2) : 0
                    this.paramsForm.applicationType.amount = value[key].amount ? value[key].amount * 1 : 0.0
                    this.paramsForm.applicationType.amountType = value[key].currencies_id
                    this.paramsForm.applicationType.code = value[key].currencies_code
                    // bank_referen
                    this.paramsForm.applicationType.bank_referen.upload = value[key].bank_referen.is_upload
                    this.paramsForm.applicationType.bank_referen.fill = !value[key].bank_referen.is_upload
                    this.paramsForm.applicationType.bank_referen.form.bank_name = value[key].bank_referen.bank_name
                    this.paramsForm.applicationType.bank_referen.form.contact_name = value[key].bank_referen.contact_name
                    this.paramsForm.applicationType.bank_referen.form.address = value[key].bank_referen.address
                    this.paramsForm.applicationType.bank_referen.form.phone = value[key].bank_referen.phone
                    this.paramsForm.applicationType.bank_referen.form.city = value[key].bank_referen.city
                    this.paramsForm.applicationType.bank_referen.form.state = value[key].bank_referen.state
                    this.paramsForm.applicationType.bank_referen.form.country = value[key].bank_referen.country
                    this.paramsForm.applicationType.bank_referen.form.zip_code = value[key].bank_referen.zip_code
                    this.paramsForm.applicationType.bank_referen.form.account_number = value[key].bank_referen.account_number
                    // business_reference
                    this.paramsForm.applicationType.busine_referen.fill = !value[key].business_reference.is_upload
                    this.paramsForm.applicationType.busine_referen.upload = value[key].business_reference.is_upload
                    const { company_list } = value[key].business_reference
                    console.log(company_list, "company_list")
                    if (company_list && company_list.length > 0) {
                        this.paramsForm.applicationType.businessReference.forEach((item, index) => {
                            if (company_list[index]) {
                                item.form = { ...company_list[index] }
                            }
                        })
                    }
                }
                if (key === "contactInfo") {
                    this.paramsForm.contactInfo.first_name = value[key].contact_first_name
                    this.paramsForm.contactInfo.last_name = value[key].contact_last_name
                    this.paramsForm.contactInfo.tel_phone = value[key].contact_phone
                    this.paramsForm.contactInfo.emali = value[key].contact_email
                    this.paramsForm.contactInfo.title = value[key].contact_title * 1
                }
                if (key === "accountPay") {
                    this.paramsForm.accountPay.first_name = value[key].contact_first_name
                    this.paramsForm.accountPay.last_name = value[key].contact_last_name
                    this.paramsForm.accountPay.tel_phone = value[key].contact_phone
                    this.paramsForm.accountPay.emali = value[key].contact_email
                    this.paramsForm.accountPay.title = value[key].contact_title * 1
                }
                if (key === "companyAddress") {
                    // 填充数据
                    this.paramsForm.businessInformation.companyName = value[key].entry_company
                    this.paramsForm.businessInformation.address = value[key].entry_street_address
                    this.paramsForm.businessInformation.phone = value[key].entry_telephone
                    this.paramsForm.businessInformation.city = value[key].entry_city
                    // this.paramsForm.businessInformation.country = value[key].company_country
                    this.paramsForm.businessInformation.zipCode = value[key].entry_postcode
                    this.paramsForm.businessInformation.yearStated = value[key].year_start
                    this.paramsForm.businessInformation.federalID = value[key].entry_tax_number
                    this.paramsForm.businessInformation.dunsNumber = value[key].duns_number
                    this.paramsForm.businessInformation.website = value[key].customers_website
                    this.paramsForm.businessInformation.address_book_id = value[key].address_book_id
                    this.paramsForm.businessInformation.ABN = value[key].abn
                    this.paramsForm.businessInformation.industry = value[key].industry
                    this.paramsForm.businessInformation.monthlySpend = value[key].estimated_monthly_spend
                    // if (value[key].company_country !== 107) {
                    // this.paramsForm.businessInformation.country = value[key].entry_country_id
                    // }
                    //德国以及其余欧盟国家地区时，billing Info默认为business相同且不能修改
                    if (this.isEuropeCountry) {
                        this.paramsForm.billingInfomation.isSameInfo = true
                        const arr = Object.keys(this.paramsForm.billingInfomation)
                        for (let key of arr) {
                            const res = this.paramsForm.businessInformation[key]
                            if (res) {
                                if (key === "state") {
                                    this.$nextTick(() => {
                                        this.paramsForm.billingInfomation[key] = res
                                    })
                                } else {
                                    this.paramsForm.billingInfomation[key] = res
                                }
                            }
                        }
                    } else {
                        this.paramsForm.billingInfomation.companyName = value[key].entry_company
                    }
                    this.$nextTick(() => {
                        this.paramsForm.businessInformation.state = value[key].entry_state
                        this.paramsForm.businessInformation.country = value[key].entry_country_id
                        this.paramsForm.billingInfomation.country = value[key].entry_country_id
                        // 获取电话号码前缀
                        // const { entry_country_id } = value[key].entry_country_id
                        // console.log(entry_country_id, "entry_country_id")
                        this.paramsForm.businessInformation.prefnumber = value[key].prefnumber || "+1"
                        this.paramsForm.billingInfomation.prefnumber = value[key].prefnumber || "+1"
                    })
                }
            })
            this.paramsForm.policys?.map((item) => {
                return { ...item, checked: false }
            })
        },
    },
    computed: {
        ...mapState({
            userInfo: (state) => state.userInfo.userInfo,
            select_tel_prefix: (state) => state.selectCountry.select_tel_prefix,
            website: (state) => state.webSiteInfo.website,
            top_data: (state) => state.category.top_data,
            countries_id: (state) => state.webSiteInfo.countries_id,
            currency: (state) => state.webSiteInfo.currency,
            symbol: (state) => state.webSiteInfo.symbol,
            isPurchase: (state) => state.userInfo.purchaseStatus,
            country_list: (state) => state.selectCountry.country_list,
            // isAUStralia: (state) => state.webSiteInfo.countries_id === 13,
            iso_code: (state) => state.webSiteInfo.iso_code,
            language: (state) => state.webSiteInfo.language,
        }),
        ...mapGetters({
            isShowState: "selectCountry/isShowState",
        }),
        isSg() {
            return this.website === "sg"
        },
        isDe() {
            return this.website === "de"
        },
        isDeEn() {
            return this.website === "de-en"
        },
        attentionList() {
            let str = ""
            if (this.website === "ru") {
                str = "+49 (0) 8165 4099 260"
            } else {
                str = this.top_data.phone
            }
            const sgText1 = "1.Payment is due within the approved net term days from the date of shipment"
            const basicArr = [
                { text: this.isSg ? sgText1 : this.$c("pages.NetTermsApplication.applicationType.attentionList.text1") },
                { text: this.$c("pages.NetTermsApplication.applicationType.attentionList.text2") },
                { text: this.$c("pages.NetTermsApplication.applicationType.attentionList.text3") },
                { text: this.$c("pages.NetTermsApplication.applicationType.attentionList.text4") },
                { text: this.$c("pages.NetTermsApplication.applicationType.attentionList.text5") },
            ]
            if (this.website === "de") {
                return [
                    ...basicArr,
                    { text: this.$c("pages.NetTermsApplication.applicationType.attentionList.text7") },
                    { text: this.$c("pages.NetTermsApplication.applicationType.attentionList.text8").replace("xxxx", str) },
                    { text: this.$c("pages.NetTermsApplication.applicationType.attentionList.text9") },
                    { text: this.$c("pages.NetTermsApplication.applicationType.attentionList.text10") },
                ]
            } else if (this.website === "de-en") {
                return [
                    ...basicArr,
                    { text: this.$c("pages.NetTermsApplication.applicationType.attentionList.text11") },
                    { text: this.$c("pages.NetTermsApplication.applicationType.attentionList.text12").replace("xxxx", str) },
                    { text: this.$c("pages.NetTermsApplication.applicationType.attentionList.text13") },
                    { text: this.$c("pages.NetTermsApplication.applicationType.attentionList.text14") },
                ]
            } else {
                return [
                    ...basicArr,
                    { text: this.$c("pages.NetTermsApplication.applicationType.attentionList.text6") },
                    { text: this.$c("pages.NetTermsApplication.applicationType.attentionList.text7") },
                    { text: this.$c("pages.NetTermsApplication.applicationType.attentionList.text8").replace("xxxx", str) },
                    { text: this.$c("pages.NetTermsApplication.applicationType.attentionList.text9") },
                    { text: this.$c("pages.NetTermsApplication.applicationType.attentionList.text10") },
                ]
            }
        },
        // 当cuntry为HongKong,China时 邮编输入禁用
        isBussinessCountryDisabled() {
            return this.paramsForm.businessInformation.country === 96
        },
        isBillingCountryDisabled() {
            return this.paramsForm.billingInfomation.country === 96
        },
        formOption() {
            console.log(this.isEuropeCountry, "isEuropeCountry")
            // 美国&非欧盟国家   billingInfomation中 companyName和country和注册地址保持一致且不能修改
            // 欧盟国家中 billingInfomation默认和注册地址一致且不能修改

            const form = {
                billingInfomation: [
                    { key: "companyName", label: this.$c("pages.NetTermsApplication.businessInformation.form.companyName"), disabled: true },
                    { key: "phone", label: this.$c("pages.NetTermsApplication.applicationType.from.phoneNumber"), type: "phone", prefnumber: true },
                    { key: "country", label: this.$c("pages.NetTermsApplication.businessInformation.form.country"), type: "country", disabled: true },
                    { key: "state", label: this.$c("pages.NetTermsApplication.businessInformation.form.state"), type: "state" },
                    { key: "city", label: this.$c("pages.NetTermsApplication.businessInformation.form.city"), type: "city" },
                    { key: "address", label: this.$c("pages.NetTermsApplication.businessInformation.form.address"), maxleng: 60 },
                    { key: "zipCode", label: this.isDeEn ? "Zip Code*" : this.$c("pages.NetTermsApplication.businessInformation.form.zip"), maxleng: 10 },
                ],
                contactInfo: [
                    { key: "first_name", label: this.$c("pages.NetTermsApplication.applicationType.from.first_name") },
                    { key: "last_name", label: this.$c("pages.NetTermsApplication.applicationType.from.last_name") },
                    { key: "title", label: this.$c("pages.NetTermsApplication.applicationType.from.title"), type: "select" },
                    { key: "emali", label: this.$c("pages.NetTermsApplication.applicationType.from.businessEmail"), disabled: true },
                    { key: "tel_phone", label: this.$c("pages.NetTermsApplication.applicationType.from.phoneNumber"), type: "phone" },
                ],
                accountPay: [
                    { key: "first_name", label: this.$c("pages.NetTermsApplication.applicationType.from.first_name") },
                    { key: "last_name", label: this.$c("pages.NetTermsApplication.applicationType.from.last_name") },
                    { key: "title", label: this.$c("pages.NetTermsApplication.applicationType.from.title"), type: "select" },
                    { key: "emali", label: this.$c("pages.NetTermsApplication.applicationType.from.businessEmail") },
                    { key: "tel_phone", label: this.$c("pages.NetTermsApplication.applicationType.from.phoneNumber"), type: "phone" },
                ],
            }
            const { companyAddress } = this.info
            let businessInformation = []
            const duns = this.isIMEWE ? [] : [{ key: "dunsNumber", label: this.$c("pages.NetTermsApplication.businessInformation.form.duns"), type: "number", maxleng: 8, disabled: false }]
            const year = this.isSg ? [] : [{ key: "yearStated", label: this.$c("pages.NetTermsApplication.businessInformation.form.year"), disabled: false }]
            const federal = this.isSg
                ? []
                : [
                      {
                          key: "federalID",
                          label: this.isEuropeCountry ? this.$c("pages.NetTermsApplication.businessInformation.form.vat_number") : this.$c("pages.NetTermsApplication.businessInformation.form.federal"),
                          type: "text",
                          //   maxleng: 8,
                          disabled: false,
                          textTips: this.isEuropeCountry ? this.$c("pages.NetTermsApplication.inlineTips.vat_number") : "",
                      },
                  ]
            if (this.isAUStralia) {
                businessInformation = [
                    { key: "companyName", label: this.$c("pages.NetTermsApplication.businessInformation.form.companyName"), hiddenTips: true, disabled: companyAddress.entry_company },
                    { key: "phone", label: this.$c("pages.NetTermsApplication.applicationType.from.phoneNumber"), type: "phone", prefnumber: true, hiddenTips: true, disabled: false },
                    { key: "country", label: this.$c("pages.NetTermsApplication.businessInformation.form.country"), type: "country", hiddenTips: true, disabled: companyAddress.entry_country_id },
                    { key: "state", label: this.$c("pages.NetTermsApplication.businessInformation.form.state"), type: "state", hiddenTips: true, disabled: false },
                    { key: "city", label: this.$c("pages.NetTermsApplication.businessInformation.form.city"), type: "city", hiddenTips: true, disabled: false },
                    { key: "address", label: this.$c("pages.NetTermsApplication.businessInformation.form.address"), hiddenTips: false, maxleng: 60, hiddenTips: true, disabled: false },
                    { key: "zipCode", label: this.isDeEn ? "Zip Code*" : this.$c("pages.NetTermsApplication.institutionType.form.postcode"), maxleng: 10, hiddenTips: true, disabled: false },
                    ...year,
                    { key: "ABN", label: this.$c("pages.NetTermsApplication.institutionType.form.ABN"), type: "number", maxleng: 10, disabled: false },
                    {
                        key: "industry",
                        label: this.$c("pages.NetTermsApplication.institutionType.form.industryDivision"),
                        type: "custom",
                        placeholder: this.$c("pages.NetTermsApplication.institutionType.form.choose"),
                        disabled: false,
                    },
                    { key: "monthlySpend", label: this.$c("pages.NetTermsApplication.institutionType.form.estimated"), type: "custom", maxleng: 99, disabled: false },
                    ...duns,
                    { key: "website", label: this.$c("pages.NetTermsApplication.businessInformation.form.web"), disabled: false },
                ]
            } else {
                businessInformation = [
                    { key: "companyName", label: this.$c("pages.NetTermsApplication.businessInformation.form.companyName"), hiddenTips: true, disabled: companyAddress.entry_company },
                    { key: "phone", label: this.$c("pages.NetTermsApplication.applicationType.from.phoneNumber"), type: "phone", prefnumber: true, hiddenTips: true, disabled: false },
                    { key: "country", label: this.$c("pages.NetTermsApplication.businessInformation.form.country"), type: "country", hiddenTips: true, disabled: companyAddress.entry_country_id },
                    { key: "state", label: this.$c("pages.NetTermsApplication.businessInformation.form.state"), hiddenTips: false, type: "state", hiddenTips: true, disabled: false },
                    { key: "city", label: this.$c("pages.NetTermsApplication.businessInformation.form.city"), type: "city", hiddenTips: false, hiddenTips: true, disabled: false },
                    { key: "address", label: this.$c("pages.NetTermsApplication.businessInformation.form.address"), hiddenTips: false, maxleng: 60, hiddenTips: true, disabled: false },
                    { key: "zipCode", label: this.isDeEn ? "Zip Code*" : this.$c("pages.NetTermsApplication.businessInformation.form.zip"), maxleng: 10, hiddenTips: true, disabled: false },
                    ...year,
                    ...federal,
                    ...duns,
                    { key: "website", label: this.$c("pages.NetTermsApplication.businessInformation.form.web"), disabled: false },
                ]
            }
            if (this.isEuropeCountry) {
                form.billingInfomation = form.billingInfomation.concat(federal)
            }

            let arrlab = Object.values(form)
            arrlab.push(businessInformation)
            console.log(666, arrlab)
            arrlab.forEach((item) => {
                if (item.length) {
                    item.forEach((val) => {
                        if (val.label && /.*\*$/.test(val.label)) {
                            val.label = val.label.replace(/\*$/, " *")
                        }
                    })
                }
            })
            //处理表单配置项
            return { ...form, businessInformation }
        },
        amountList() {
            if (this.isAUStralia) {
                return [{ name: "AUD", value: 5, key: "AUD" }]
            } else {
                return [
                    { name: "USD", value: 1, key: "USD" },
                    { name: "EUR", value: 2, key: "EUR" },
                    { name: "GBP", value: 3, key: "GBP" },
                    { name: "AUD", value: 5, key: "AUD" },
                    { name: "CAD", value: 4, key: "CAD" },
                    { name: "CNY", value: 6, key: "CNY" },
                    { name: "CHF", value: 7, key: "CHF" },
                    { name: "HKD", value: 8, key: "HKD" },
                    { name: "JPY", value: 9, key: "JPY" },
                    { name: "BRL", value: 10, key: "BRL" },
                    { name: "NOK", value: 11, key: "NOK" },
                    { name: "DKK", value: 12, key: "DKK" },
                    { name: "SEK", value: 13, key: "SEK" },
                    { name: "MXN", value: 14, key: "MXN" },
                    { name: "NZD", value: 15, key: "NZD" },
                    { name: "SGD", value: 16, key: "SGD" },
                    { name: "RUB", value: 18, key: "RUB" },
                    { name: "MYR", value: 19, key: "MYR" },
                    { name: "THB", value: 20, key: "THB" },
                    { name: "PHP", value: 21, key: "PHP" },
                ]
            }
        },

        netList() {
            let result = [
                { name: this.$c("pages.NetTermsApplication.applicationType.netList.text1"), value: 17 },
                { name: this.$c("pages.NetTermsApplication.applicationType.netList.text2"), value: 4 },
            ]
            if (!this.isDe) {
                result.push(
                    ...[
                        { name: this.$c("pages.NetTermsApplication.applicationType.netList.text3"), value: 7 },
                        { name: this.$c("pages.NetTermsApplication.applicationType.netList.text4"), value: 10 },
                    ]
                )
            }
            return result
        },
    },
    watch: {
        userInfo: {
            handler: function (val) {
                if (val) {
                    const { customers_firstname, customers_lastname, title, customers_email_address, phone } = val
                    this.paramsForm.contactInfo.first_name = customers_firstname
                    this.paramsForm.contactInfo.last_name = customers_lastname
                    this.paramsForm.contactInfo.title = title || ""
                    this.paramsForm.contactInfo.emali = customers_email_address
                    this.paramsForm.contactInfo.tel_phone = phone
                }
            },
            immediate: true,
        },
        // "paramsForm.businessInformation.country": {
        //     handler(val) {
        //         this.$emit("changeCountry", val)
        //         // 当国家为澳大利亚时
        //         if (val === 13) {
        //             this.getInitPay("AUD")
        //         }
        //     },
        // },
        info: {
            handler(val) {
                if (val) {
                    this.fillFormValue(val)
                }
            },
            immediate: true,
        },
        country_id: {
            handler(val) {
                if (val) {
                    this.paramsForm.businessInformation.country = val
                }
            },
            immediate: true,
        },
    },
}
