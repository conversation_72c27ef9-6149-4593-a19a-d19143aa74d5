<template>
    <AccountLayout :breads="crumb">
        <section class="shopping-account" :class="{ 'is-company': isCompanyAccount, 'is-basic': !isCompanyAccount }" v-loading="loading || setDefaultLoading || carrierLoading">
            <!-- 团队选择 -->
            <team-select v-if="isCompanyAccount" @change="changeTeam" ref="teamSelect"></team-select>
            <div class="top-box">
                <p class="title">{{ $c("pages.ShoppingAccount.title") }}</p>
                <div v-if="isCompanyAccount" class="top-flex">
                    <fs-select isNewStyle class="form-item" :options="options" v-model="queryForm.share_type" @change="handleShareTypeChange"></fs-select>
                    <AccountSearchInput width="280px" class="search-item" @search="searchSubmit" placeholder="Name、Number" v-model="queryForm.search" />
                </div>
                <AccountMFilterBtn :isMFilter="isMFilter" :isUnfold="filterVisiable" @emitClick="handleOpenFilter"></AccountMFilterBtn>
                <fs-button type="blackline" @click="handleAddNewAccount">{{ $c("pages.ShoppingAccount.addNewAccount") }}</fs-button>
            </div>
            <div class="table-box">
                <div class="table">
                    <div class="header">
                        <div class="tr">
                            <div class="th account-name">{{ $c("pages.ShoppingAccount.accountName") }}</div>
                            <div class="th carrier">
                                {{ $c("pages.ShoppingAccount.carrier") }}
                                <MoreSelectTip v-if="carrierOptions.length" v-model="carrierVisible" @confirm="handleTipContentConfirm" :values="queryForm.shipping_group" :options="carrierOptions">
                                    <i class="iconfont icon-filter">{{ isCarrierFilter ? "&#xf239;" : "&#xe712;" }}</i>
                                </MoreSelectTip>
                            </div>
                            <div class="th account-num">{{ $c("pages.ShoppingAccount.accountNumber") }}</div>
                            <div class="th type" v-if="isCompanyAccount">{{ $c("pages.ShoppingAccount.type") }}</div>
                            <div class="th actions">{{ $c("pages.ShoppingAccount.actions") }}</div>
                            <div class="th handle">{{ $c("pages.ShoppingAccount.saveAsDefault") }}</div>
                        </div>
                    </div>
                    <div class="body">
                        <template v-for="(item, index) in dataList">
                            <div class="tr-box" :key="index">
                                <div class="tr">
                                    <div class="td account-name">{{ item.foldAccountName }}</div>
                                    <div class="td carrier">{{ item.shipping_group_title }}</div>
                                    <div class="td account-num">
                                        {{ item.isEye ? item.shipping_account : item.shipping_account_mask }}
                                        <span class="iconfont icon-eye" @click="item.isEye = !item.isEye">{{ item.isEye ? "&#xe748;" : "&#xe706;" }}</span>
                                    </div>
                                    <div class="td type" v-if="isCompanyAccount">{{ item.share_type_str }}</div>
                                    <div class="td actions">
                                        <div class="text-btn" v-if="item.is_can_opt">
                                            <i class="iconfont">&#xe745;</i>
                                            <span @click="handleActions(item, 'edit')">{{ $c("pages.MyAccount.address.edit") }}</span>
                                        </div>
                                        <div class="text-btn" v-if="isCompanyAccount && item.is_can_share">
                                            <i class="iconfont">&#xe734;</i>
                                            <span @click="handleActions(item, 'share')">{{ $c("pages.Products.Share") }}</span>
                                        </div>
                                        <div slot="trigger" class="text-btn" v-if="item.is_can_opt">
                                            <i class="iconfont">&#xe65f;</i>
                                            <span @click="handleActions(item, 'remove')">{{ $c("pages.MyAccount.address.delete") }}</span>
                                        </div>
                                    </div>
                                    <div class="td handle">
                                        <span class="tag" v-if="item.is_default">{{ $c("pages.MyAccount.address.default") }}</span>
                                        <span class="iconfont icon-default" v-else @click="fetchSetDefault(item)">&#xe630;</span>
                                    </div>
                                </div>
                            </div>
                            <div class="tr-box-m" :key="index + 'm'">
                                <dl class="m-head">
                                    <dt>{{ $c("pages.ShoppingAccount.accountName") }}:</dt>
                                    <dd>{{ item.foldAccountName }}</dd>
                                </dl>
                                <div class="m-list">
                                    <dl class="m-list-item">
                                        <dt>{{ $c("pages.ShoppingAccount.carrier") }}:</dt>
                                        <dd>{{ item.shipping_group_title }}</dd>
                                    </dl>
                                    <dl class="m-list-item">
                                        <dt>{{ $c("pages.ShoppingAccount.accountNumber") }}:</dt>
                                        <dd>
                                            {{ item.isEye ? item.shipping_account : item.shipping_account_mask }}
                                            <span class="iconfont icon-eye" @click="item.isEye = !item.isEye">{{ item.isEye ? "&#xe748;" : "&#xe706;" }}</span>
                                        </dd>
                                    </dl>

                                    <dl class="m-list-item" v-if="isCompanyAccount">
                                        <dt>{{ $c("pages.ShoppingAccount.type") }}:</dt>
                                        <dd>{{ item.share_type_str }}</dd>
                                    </dl>
                                </div>
                                <div class="m-footer">
                                    <span class="tag" v-if="item.is_default">{{ $c("pages.MyAccount.address.default") }}</span>
                                    <div class="text-btn" v-else>
                                        <i class="iconfont">&#xe630;</i>
                                        <span @click="fetchSetDefault(item)">{{ $c("pages.MyAccount.address.setDefault") }}</span>
                                    </div>
                                    <div class="actions">
                                        <div class="text-btn" v-if="item.is_can_opt">
                                            <i class="iconfont">&#xe745;</i>
                                            <span @click="handleActions(item, 'edit')">{{ $c("pages.MyAccount.address.edit") }}</span>
                                        </div>
                                        <div class="text-btn" v-if="isCompanyAccount && item.is_can_share">
                                            <i class="iconfont">&#xe734;</i>
                                            <span @click="handleActions(item, 'share')">{{ $c("pages.Products.Share") }}</span>
                                        </div>
                                        <div class="text-btn" v-if="item.is_can_opt">
                                            <i class="iconfont">&#xe65f;</i>
                                            <span @click="handleActions(item, 'remove')">{{ $c("pages.MyAccount.address.delete") }}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </template>
                    </div>
                </div>
                <AccountEmpty v-if="dataList.length === 0 && !loading">
                    <p class="empty-title" v-for="(item, index) in $c('pages.ShoppingAccount.emptyTitleList')" :key="index">{{ item }}</p>
                </AccountEmpty>
            </div>
            <fs-pagination class="pagination" :pageConfig="pageConfig" @changeCurrentPage="changeCurrentPage"></fs-pagination>
        </section>
        <filters v-model="filterVisiable" @submit="filtersSubmit" :dataList="mDataList" />
        <AddNewAccountPopup
            :show="addPopup"
            :title="addPopupTitle"
            :isDefaultAccount="isDefaultAccount"
            :editData="editData"
            :options="allCarrier"
            @close="addPopup = false"
            @success="handleAddSuccess"></AddNewAccountPopup>
        <!-- 删除和取消弹窗 -->
        <fs-popup-new
            transition="fade"
            :title="$c('pages.MyAccount.address.delete')"
            type="float"
            isNew
            width="480"
            :show="popupDeleteShow"
            @close="popupDeleteShow = false"
            :loading="deleteLoading"
            mobileWidth="calc(100% - 64px)">
            <div class="delete_popup_ctn">
                <div class="delete_info" :class="{ isDelateShare }">
                    <p>{{ $c("pages.ShoppingAccount.deleteConfirm") }}</p>
                    <span>{{ "This shipping account will be invisible to everyone oncee removed." }}</span>
                </div>
            </div>
            <template slot="footer">
                <div class="delete_btn_box">
                    <fs-button type="white" @click="popupDeleteShow = false">{{ $c("pages.SavedCarts.Cancel") }}</fs-button>
                    <fs-button :loading="deleteLoading" @click="fetchDelete">{{ $c("pages.SavedCarts.DeleteConfirm") }}</fs-button>
                </div></template
            >
        </fs-popup-new>
        <share-popup :show="sharePopup" @success="shareSuccess" pageType="account" @change="sharePopup = false" :dataItem="shareData" :popupTitle="sharePopupTitle"></share-popup>
    </AccountLayout>
</template>

<script>
import AccountLayout from "@/components/AccountLayout/AccountLayout.vue"
import FsSelect from "@/components/FsSelect/FsSelect"
import FsButton from "@/components/FsButton/FsButton"
import FsPagination from "@/components/FsPagination/FsPagination"
import Filters from "@/components/MFilters/MFilters"
import FsPopover from "@/components/FsPopover"
import AccountEmpty from "@/components/AccountEmpty/AccountEmpty"
import AccountMFilterBtn from "@/components/AccountMFilterBtn/AccountMFilterBtn"
import AccountSearchInput from "@/components/AccountSearchInput/AccountSearchInput"
import TeamSelect from "@/components/TeamSelect/TeamSelect.vue"
import MoreSelectTip from "@/components/MoreSelectTip/MoreSelectTip.vue"
import AddNewAccountPopup from "./components/AddNewAccountPopup.vue"
import FsPopupNew from "@/components/FsPopupNew/FsPopupNew.vue"
import SharePopup from "@/components/SharePopup/SharePopup.vue"
import { deepClone } from "@/util/util.js"
import { mapState } from "vuex"

const DEFAULT_QUERY_FORM = {
    search: "",
    shipping_group: [],
    share_type: 0,
}
export default {
    name: "ShoppingAccount",
    components: {
        AccountLayout,
        FsButton,
        FsPagination,
        Filters,
        FsPopover,
        AccountEmpty,
        AccountMFilterBtn,
        AccountSearchInput,
        TeamSelect,
        MoreSelectTip,
        FsSelect,
        AddNewAccountPopup,
        FsPopupNew,
        SharePopup,
    },
    data() {
        return {
            loading: false,
            carrierLoading: false,
            currentTeam: {},
            options: [
                { name: this.$c("pages.AddressBook.allTypes"), value: "0" },
                { name: this.$c("pages.AddressBook.shared"), value: "2" },
                { name: this.$c("pages.AddressBook.individual"), value: "1" },
            ],
            queryForm: deepClone(DEFAULT_QUERY_FORM),
            pageConfig: {
                pageSize: 10,
                pageNo: 1,
                total: 0,
            },
            allCarrier: [],
            filterVisiable: false,
            carrierVisible: false,
            carrierOptions: [],
            mDataList: [],
            dataList: [],
            addPopup: false,
            deleteLoading: false,
            popupDeleteShow: false,
            delateItem: null,
            sharePopup: false,
            shareData: null,
            deleteAccountId: "",
            setDefaultLoading: false,
            editData: null,
            isDefaultAccount: false,
            sharePopupTitle: this.$c("pages.ShoppingAccount.sharePopupTitle"),
        }
    },
    computed: {
        ...mapState({
            userInfo: (state) => state.userInfo.userInfo,
        }),
        isCompanyAccount() {
            return this.userInfo?.isCompanyOrganizationUser
        },
        crumb() {
            return [
                {
                    name: this.$c("pages.SavedCarts.My_Account"),
                    url: this.localePath({ name: "my-account" }),
                },
                {
                    name: this.$c("pages.ShoppingAccount.pageLabel"),
                    url: "",
                },
            ]
        },
        isMFilter() {
            return Object.entries(DEFAULT_QUERY_FORM).some((i) => {
                const [key, value] = i
                if (Array.isArray(value)) {
                    return String(this.queryForm[key]) !== String(value)
                }
                return this.queryForm[key] !== value
            })
        },
        isCarrierFilter() {
            return this.queryForm.shipping_group.length > 0
        },
        addPopupTitle() {
            let { share_type = 0 } = this.editData || {}
            const map = {
                0: this.$c("pages.ShoppingAccount.addTitle"),
                1: this.$c("pages.ShoppingAccount.editIndividual"),
                2: this.$c("pages.ShoppingAccount.editShare"),
            }
            if (![1, 2].includes(share_type)) {
                share_type = 0
            }
            return map[share_type]
        },
        isDelateShare() {
            const { share_type = 1 } = this.delateItem || {}
            return share_type === 2
        },
    },
    mounted() {
        this.getShippingList()
        this.getList(1)
    },
    methods: {
        handleAddNewAccount() {
            this.editData = null
            this.addPopup = true
        },

        handleOpenFilter() {
            if (this.mDataList.length === 0) {
                this.createMDataList()
            }
            this.updateMDataList()

            this.filterVisiable = true
        },
        updateMDataList() {
            this.mDataList.forEach((item) => {
                item.currentValue = this.queryForm[item.returnKey] || ""
            })
        },
        createMDataList() {
            const { options, carrierOptions } = this
            const { shareType, shipping_group } = DEFAULT_QUERY_FORM
            this.mDataList = [
                {
                    title: this.$c("pages.ShoppingAccount.carrier"),
                    list: carrierOptions,
                    defaultValue: shipping_group,
                    currentValue: shipping_group,
                    returnKey: "shipping_group",
                    type: "checkbox",
                },
            ]
            if (this.isCompanyAccount) {
                this.mDataList.unshift({
                    title: this.$c("pages.ShoppingAccount.type"),
                    list: options,
                    defaultValue: shareType,
                    currentValue: shareType,
                    returnKey: "shareType",
                })
            }
        },
        filtersSubmit(obj) {
            this.filterVisiable = false
            this.queryForm = { ...this.queryForm, ...obj }
            this.getList(1)
        },
        changeTeam({ value, type }) {
            window.dataLayer &&
                window.dataLayer.push({
                    event: "uaEvent",
                    eventCategory: this.pageGroup,
                    eventAction: "team_switch",
                    eventLabel: "Switch Team",
                    nonInteraction: false,
                })
            this.currentTeam = value
            this.getList(1)
        },
        searchSubmit() {
            this.getList(1)
        },
        handleShareTypeChange() {
            this.getList(1)
        },
        changeCurrentPage(p) {
            this.pageConfig.pageNo = p
            this.getList()
        },
        handleActions(item, type) {
            switch (type) {
                case "edit":
                    console.log(item, "item")
                    this.editData = item
                    this.addPopup = true
                    break
                case "share":
                    this.sharePopup = true
                    this.shareData = item
                    console.log(this.sharePopup, "@@@")
                    break
                case "remove":
                    this.deleteAccountId = item.id
                    this.popupDeleteShow = true
                    this.delateItem = item
                    break
            }
        },
        handleTipContentConfirm(values) {
            this.queryForm.shipping_group = values
            this.carrierVisible = false
            this.getList(1)
        },
        handleAddSuccess() {
            this.addPopup = false
            this.getList(1)
        },
        getMaxChatLength(str) {
            let len = 0
            let maxLen = 20
            let isFold = false
            for (let i = 0; i < str.length; i++) {
                const chat = str.charAt(i)
                len++
                //判断字符是否为双字节
                if (chat.match(/[^\x00-\xff]/gi) !== null) {
                    len++
                    maxLen--
                }
                if (len >= 20) {
                    isFold = len > 20 ? true : i < str.length - 1
                    break
                }
            }

            let suffix = isFold ? "..." : ""
            return `${str.slice(0, maxLen)}${suffix}`
        },
        async fetchSetDefault(item) {
            try {
                this.setDefaultLoading = true
                const params = {
                    account_id: item.id,
                }
                await this.$axios.post("/api/shipping_account/set_default", params)
                this.getList(1)
            } catch (error) {
                this.$message.error(error.message)
            }
            this.setDefaultLoading = false
        },
        async fetchDelete() {
            try {
                this.deleteLoading = true
                const { deleteAccountId: account_id } = this
                const params = {
                    account_id,
                }
                const res = await this.$axios.post("/api/shipping_account/delete", params)
                this.popupDeleteShow = false

                this.getList(1)
            } catch (error) {
                this.$message.error(error.message)
            }
            this.deleteLoading = false
        },
        async getShippingList() {
            try {
                this.carrierLoading = true
                const res = await this.$axios.get("/api/shipping_account/shippingList")
                this.allCarrier = res.data.map((i) => ({
                    name: i.title,
                    value: i.code,
                }))
            } catch (error) {
                console.error(error.message)
            }
            this.carrierLoading = false
        },
        async getList(v) {
            try {
                if (this.loading) return
                this.loading = true
                window && window.scrollTo({ top: 0, behavior: "smooth" })
                if (v) {
                    this.pageConfig.pageNo = v
                }
                const { pageNo: page } = this.pageConfig
                const { shipping_group, share_type, search } = this.queryForm
                const { id = "" } = this.currentTeam
                const [{ id: tid = 0 }] = (this.userInfo.companyInfo && this.userInfo.companyInfo?.allTeamList) || [{}]
                const bizTeamId = id || tid
                let params = { page, share_type, bizTeamId, search, shipping_group: shipping_group.join(",") }
                const res = await this.$axios.get("/api/shipping_account/list", { params })
                this.pageConfig.total = res.data.split_page.number_of_rows
                this.isDefaultAccount = res.data.has_default_account === 1
                const list = res.data.list || []
                this.dataList = list.map((i) => ({
                    ...i,
                    foldAccountName: this.getMaxChatLength(i.account_name),
                    isEye: false,
                }))
                this.carrierOptions = (res.data?.group_num || []).map((i) => ({
                    name: `${i.code} (${i.num})`,
                    value: i.code,
                }))
                console.log("dataList:", this.dataList)
            } catch (error) {
                console.error(error.message)
            }
            this.loading = false
        },
        shareSuccess() {
            this.getList()
        },
    },
    asyncData({ store, app, redirect }) {
        // 暂时隐藏企业账户  || (store.state.userInfo.userInfo && store.state.userInfo.userInfo.isCompanyOrganizationUser)
        // if (store.state.userInfo.userInfo && store.state.userInfo.userInfo.isCompanyOrganizationUser) {
        if (store.getters["webSiteInfo/isCn"] || store.getters["webSiteInfo/isCnTr"]) {
            redirect(app.localePath({ name: "my-account" }))
        }
    },
}
</script>

<style scoped lang="scss">
.shopping-account {
    &.is-company {
        .top-box {
            align-items: flex-start;
            .top-flex {
                flex: 1;
                display: flex;
                flex-wrap: wrap;
                .form-item {
                    width: 220px;
                    margin: 6px 12px 6px 0;
                }
                .search-item {
                    margin: 6px 12px 6px 0;
                }
            }
            .title {
                flex: 0 0 100%;
                margin-bottom: 24px;
            }
            ::v-deep .fs-button {
                margin-top: 6px;
            }
        }
        .table-box {
            margin-top: 18px;
        }
    }
    .top-box {
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: wrap;
        .title {
            @include font20();
            color: #19191a;
            font-weight: 600;
        }
    }
    .pagination {
        margin-top: 20px;
    }
    .text-btn {
        display: inline-flex;
        align-items: center;
        cursor: pointer;
        font-weight: 500;
        .iconfont {
            width: 16px;
            height: 16px;
            line-height: 16px;
            text-align: center;
            &:hover {
                text-decoration: none;
            }
        }
        > span {
            @include font14();
            color: #19191a;
            margin-left: 4px;
            cursor: pointer;
            &:hover {
                text-decoration: underline;
            }
        }
    }
    .tag {
        background-color: #e5e5e5;
        border-radius: 3px;
        color: #4b4b4d;
        @include font12();
        padding: 3px 10px;
    }
    .table-box {
        margin-top: 24px;
        @include emptyShadow;
        .table {
            word-break: break-word;
            width: 100%;
            .tr {
                display: flex;
                align-items: center;
            }
            .th {
                font-weight: 600;
            }
            .td,
            .th {
                @include font14();
                display: flex;
                align-items: center;
                padding-left: 24px;
                flex: 1;
                &:first-child {
                    padding-left: 0;
                }
            }
            .icon-filter {
                @include font14();
                color: #000;
                font-weight: 400;
            }
            .icon-eye {
                margin-left: 4px;
                cursor: pointer;
                color: #707070;
            }
            .handle {
                justify-content: flex-end;
                text-align: right;

                .icon-default {
                    transition: all 0.3s;
                    cursor: pointer;
                    padding: 6px;
                    border-radius: 3px;
                    &:hover {
                        background-color: rgba(25, 25, 26, 0.04);
                    }
                }
            }
            .account-name {
                flex: 2;
            }
            .account-num,
            .carrier {
                flex: 1.5;
            }

            .header {
                .tr {
                    padding: 12px 24px;
                    background-color: #f7f7f7;
                    transition: all 0.2s;
                    border-radius: 12px 12px 0 0;
                }
            }

            .body {
                .tr-box {
                    padding: 0 24px;
                    transition: all 0.2s;
                    &:first-child .tr {
                        border-top: 0;
                    }
                    .tr {
                        padding: 24px 0;
                        border-top: 1px solid #e5e5e5;
                    }
                    .actions {
                        flex-direction: column;
                        align-items: flex-start;
                        .text-btn:not(:first-child) {
                            margin-top: 4px;
                        }
                    }
                }

                .tr-box-m {
                    display: none;
                    @include emptyShadow;
                    overflow: hidden;
                    &:not(:last-child) {
                        margin-bottom: 20px;
                    }
                    .m-head {
                        background-color: #f7f7f7;

                        @include font14();
                        padding: 16px;
                        dd {
                            text-align: right;
                        }
                    }
                    .m-list {
                        padding: 0 16px;
                        .m-list-item {
                            padding: 16px 0;
                            border-bottom: 1px solid #e5e5e5;
                        }
                    }
                    .m-list-item,
                    .m-head {
                        display: flex;
                        justify-content: space-between;
                        dt {
                            font-weight: 600;
                        }
                    }

                    .m-content {
                        padding: 16px 16px 24px;
                        .title-label {
                            display: flex;
                            justify-content: space-between;
                            color: #707070;
                            @include font13();
                            margin-bottom: 12px;
                        }
                    }
                    .m-footer {
                        padding: 16px;
                        display: flex;
                        align-items: flex-start;
                        .actions {
                            flex: 1;
                            overflow: hidden;
                            display: flex;
                            justify-content: flex-end;
                            flex-wrap: wrap;
                            margin-top: 2px;
                            .text-btn:not(:first-child) {
                                margin-left: 16px;
                            }
                        }
                    }
                }
            }
        }
    }
    @include mediaM {
        &.is-company {
            ::v-deep .teams_box {
                margin-top: 0;
            }
            .top-box {
                align-items: center;
                .top-flex {
                    .form-item {
                        display: none;
                    }
                    .search-item {
                        flex: 1;
                        margin: 0;
                    }
                }
                ::v-deep .fs-button {
                    flex: 0 0 100%;
                    margin-top: 16px;
                }
            }
        }
        &.is-basic {
            .top-box {
                ::v-deep .account-m-filter-btn {
                    margin-left: 0;
                    width: 40px;
                    height: 40px;
                    padding: 10px;
                }
            }
        }
        .top-box {
            .title {
                flex: 0 0 100%;
                margin-bottom: 24px;
            }
        }
        .table-box {
            box-shadow: none;

            .table {
                .header {
                    display: none;
                }
                .body {
                    .tr-box {
                        display: none;
                    }
                    .tr-box-m {
                        display: block;
                    }
                }
            }
        }
    }
}
.delete_info {
    @include font14;
    color: $textColor1;
    // margin-bottom: 16px;
}
.delete_btn_box {
    display: flex;
    justify-content: flex-end;
    .fs-button {
        margin-left: 16px;
        @include mobile {
            margin-left: 12px;
        }
    }
}
// .delete_popup_ctn,
// .delete_popup_ctn_m {
//     width: 480px;
//     padding: 20px 32px;
//     @media (max-width: 960px) {
//         width: 100%;
//         padding: 20px;
//     }
//     .delete_info {
//         @include font14;
//         color: $textColor1;
//         margin-bottom: 40px;
//         > span {
//             display: none;
//         }
//         &.isDelateShare {
//             > p {
//                 font-weight: 600;
//                 margin-bottom: 10px;
//             }
//             > span {
//                 display: block;
//             }
//         }
//     }
//     .delete_btn_box {
//         display: flex;
//         justify-content: flex-end;
//         .fs-button {
//             margin-left: 12px;
//             width: 120px;
//         }
//     }
// }
</style>
