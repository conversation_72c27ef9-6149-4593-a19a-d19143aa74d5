<template>
    <fs-popup-new transition="slide-up" v-bind="$attrs" v-on="$listeners" width="750" type="drawer" isNew :title="popupTitle" @change="handleChange" :loading="btnLoading" :isMDrawer="true">
        <div class="form">
            <div class="form-item">
                <p class="form-label">{{ $c("pages.ShoppingAccount.accountName") }}*</p>
                <div class="inp_box">
                    <input class="is_new" type="text" v-model="form.account_name" maxlength="200" @blur="blurInput('account_name')" />
                    <validate-error :error="errors.account_name"></validate-error>
                </div>
            </div>
            <div class="form-item">
                <p class="form-label">{{ $c("pages.ShoppingAccount.carrier") }}*</p>
                <div class="inp_box">
                    <fs-select isNewStyle :options="options" v-model="form.carrier" @change="handleCarrierChange"></fs-select>
                </div>
            </div>
            <div class="form-item">
                <p class="form-label">{{ $c("pages.ShoppingAccount.accountNumber") }}*</p>
                <div class="inp_box">
                    <input class="is_new" type="text" v-model="form.account_number" maxlength="9" @blur="blurInput('account_number')" />
                    <validate-error :error="errors.account_number"></validate-error>
                </div>
            </div>
            <div class="form-item">
                <label class="from-checkout">
                    <input type="checkbox" v-model="form.set_default" />
                    {{ $c("pages.ShoppingAccount.saveAsDefaultLabel") }}
                </label>
            </div>
        </div>
        <template slot="footer">
            <div class="btn">
                <div class="btn_dobue">
                    <fs-button class="cancel_btn" type="white" :text="$c('pages.confirmOrder.address.cancel')" @click="close"></fs-button>
                    <fs-button type="red" :loading="btnLoading" @click="handleSubmit" :text="editData ? $c('pages.confirmOrder.address.save') : $c('pages.AddressBook.create')"></fs-button>
                </div>
            </div>
        </template>
    </fs-popup-new>
</template>

<script>
import FsPopupNew from "@/components/FsPopupNew/FsPopupNew.vue"
import FsButton from "@/components/FsButton/FsButton.vue"
import FsSelect from "@/components/FsSelect/FsSelect.vue"
import ValidateError from "@/components/ValidateError/ValidateError"
import { deepClone } from "@/util/util.js"
const DEFAULT_FORM = {
    account_name: "",
    carrier: "",
    account_number: "",
    set_default: false,
}
const DEFAULT_ERRORS = {
    account_name: "",
    account_number: "",
}
export default {
    name: "AddNewAccountPopup",
    components: {
        FsPopupNew,
        FsButton,
        FsSelect,
        ValidateError,
    },
    props: {
        options: {
            type: Array,
            default: () => [],
        },
        editData: {
            type: Object,
        },
        isDefaultAccount: {
            type: Boolean,
            default: false,
        },
        title: {
            type: String,
            default: "",
        },
    },
    data() {
        return {
            form: deepClone(DEFAULT_FORM),
            errors: deepClone(DEFAULT_ERRORS),
            btnLoading: false,
        }
    },
    computed: {
        popupTitle() {
            return this.title || (this.editData ? this.$c("pages.ShoppingAccount.editTitle") : this.$c("pages.ShoppingAccount.addTitle"))
        },
    },

    methods: {
        handleCarrierChange() {
            if (!this.form.account_number) return
            this.blurInput("account_number")
        },
        handleChange(bol) {
            if (!bol) return
            this.errors = deepClone(DEFAULT_ERRORS)
            if (this.editData) {
                const { shipping_account, shipping_group, account_name, is_default } = this.editData || {}
                this.form = {
                    account_name,
                    carrier: shipping_group,
                    account_number: shipping_account,
                    set_default: !!is_default,
                }
            } else {
                this.form = deepClone(DEFAULT_FORM)
                this.form.set_default = !this.isDefaultAccount
            }
        },
        close() {
            this.$emit("close")
        },
        checkAccountNumber(val) {
            let reg = /^\d{9}$/
            let error = this.$c("pages.confirmOrder.form.accountConsisted")
            const method = this.form.carrier
            if (method.includes("UPS")) {
                error = this.$c("pages.confirmOrder.form.accountConsisted2")
                reg = /^[A-Za-z0-9]{6}$/
            }
            if (method.includes("TNT") || method.includes("STARTRACK")) {
                error = this.$c("pages.confirmOrder.form.accountConsisted").replace("9", "8")
                reg = /^\d{8}$/
            }

            if (method.includes("AUPOST")) {
                error = this.$c("pages.confirmOrder.form.accountConsisted").replace("9", "7")
                reg = /^\d{7}$/
            }

            return !reg.test(val) ? error : ""
        },
        blurInput(attr) {
            let val = this.form[attr] || ""
            val = val.replace(/^\s+|\s+$/g, "")
            const map = {
                account_name() {
                    if (!val) return this.$c("form.form.errors.interest_type_error")
                },
                account_number() {
                    if (!val) return this.$c("form.form.errors.interest_type_error")
                    const curError = this.checkAccountNumber(val)
                    if (curError) return curError
                },
            }
            this.errors[attr] = (map[attr] && map[attr].apply(this)) || ""
        },
        verifyWrapper() {
            const errorMap = Object.keys(this.errors)
            errorMap.forEach((key) => {
                this.blurInput(key)
            })
            return errorMap.some((attr) => this.errors[attr])
        },
        handleSubmit() {
            if (this.btnLoading || this.verifyWrapper()) {
                return
            }
            this.btnLoading = true
            this.fetchSubmit()
        },
        async fetchSubmit() {
            try {
                this.btnLoading = true
                const params = {
                    ...this.form,
                    account_id: this.editData ? this.editData.id : undefined,
                    set_default: this.form.set_default ? 1 : 0,
                }
                await this.$axios.post("/api/shipping_account/create_or_update", params)
                this.$emit("success")
            } catch (error) {
                this.$message.error(error.message)
            }
            this.btnLoading = false
        },
    },
}
</script>

<style scoped lang="scss">
::v-deep .fs-select .options-wrap.options-wrap-absolute .border-box.is_new {
    margin-bottom: 20px;
}
.form {
    // padding: 20px 32px;
    .form-item {
        margin-top: 16px;
        &:first-child {
            margin-top: 0;
        }
        .form-label {
            margin-bottom: 4px;
            @include font12();
            color: #19191a;
            display: flex;
            align-items: center;
        }
        .from-checkout {
            display: flex;
            align-items: center;
            color: #707070;
            @include font13();
            cursor: pointer;
            > input {
                margin-right: 8px;
                width: 14px;
                height: 14px;
                font-size: 14px;
            }
        }
    }
}
::v-deep .fs-popup.is_new .fs-popup-body {
    padding: 16px 24px 24px;
    @media (max-width: 768px) {
        padding: 16px 16px 20px;
    }
}
::v-deep .fs-popup.is_new .fs-popup-footer {
    padding: 0 24px 24px;
    @media (max-width: 768px) {
        padding: 0 16px 16px;
    }
}
.btn {
    // padding: 20px 32px;
    display: flex;
    justify-content: flex-end;
    // border-top: 1px solid #e5e5e5;
    .btn_dobue {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        grid-gap: 16px;
    }
}
@include mediaM {
    .form {
        // padding: 20px 16px;
    }
    .btn {
        // padding: 20px 16px;
        .btn_dobue {
            width: 100%;
            grid-template-columns: repeat(1, 1fr);
            ::v-deep .fs-button-red {
                order: -1;
            }
        }
    }
}
</style>
