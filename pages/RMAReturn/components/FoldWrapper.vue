<template>
    <section class="fold_box">
        <p v-if="isFoldShow" class="fold_label" :class="{ expand: !isFold }" @click="onChangeFold">
            <span>{{ isFold ? foldedLabel : unfoldLabel }}</span>
            <i class="iconfont icon">&#xe704;</i>
        </p>
        <SlideDown>
            <div v-show="!isFold || !isFoldShow" class="fold_content">
                <div class="fold_line"></div>
                <slot />
                <div v-if="isEndLine" class="fold_line end_line"></div>
            </div>
        </SlideDown>
    </section>
</template>

<script>
import SlideDown from "@/components/SlideDown/SlideDown.vue"
export default {
    name: "FoldWrapper",
    components: {
        SlideDown,
    },
    props: {
        initIsFold: {
            type: Boolean,
            default: true,
        },
        isFoldShow: {
            type: Boolean,
            default: true,
        },
        foldedLabel: {
            type: String,
            default: "",
        },
        unfoldLabel: {
            type: String,
            default: "",
        },
        isEndLine: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {
            isFold: this.initIsFold,
        }
    },
    methods: {
        onChangeFold() {
            this.isFold = !this.isFold
            this.$emit("changeFold", this.isFold)
        },
    },
}
</script>

<style scoped lang="scss">
.fold_box {
    .fold_label {
        color: #0060bf;
        cursor: pointer;
        display: flex;
        align-items: center;
        gap: 0 4px;
        @include font12();
        &.expand .iconfont {
            transform: rotateX(-180deg);
        }
        .iconfont {
            font-size: 12px;
            line-height: 1;
            transition: all 0.2s;
        }
    }
    .fold_line {
        border-bottom: 1px solid #eeeeee;
        padding-bottom: 8px;
    }
}
@include mobile {
    .fold_box {
        padding: 12px 16px;
        background: #fafafb;
        border-radius: 3px;
    }
    .fold_line.end_line {
        display: none;
    }
}
</style>
