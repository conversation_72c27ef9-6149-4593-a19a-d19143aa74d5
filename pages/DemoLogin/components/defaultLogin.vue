<template>
    <div class="login_box">
        <h2 class="welcome">{{ $c("pages.Login.Sign_In") }}</h2>
        <div class="reg">
            {{ $c("pages.Login.Sign_in_to_FS_or") }}
            <nuxt-link :to="localePath({ path: '/register.html' })">{{ $c("pages.Login.Create_an_account") }}</nuxt-link>
        </div>
        <validate-message :message="actionsMsg" :type="actionsType"></validate-message>
        <form class="form" @submit.prevent="submit">
            <validate-message :message="err_msg"></validate-message>
            <div class="form_item">
                <div class="label">{{ $c("pages.Login.Email_Address") }}</div>
                <div class="inp_box">
                    <input type="text" :class="{ error_input: errors.email }" @focus="titlePoint('Email Address')" v-model.trim="form.email" @input="inputCheck('email')" @blur="inputCheck('email')" />
                    <validate-error :error="errors.email"></validate-error>
                </div>
            </div>
            <div class="form_item form_item_pwd">
                <div class="label">
                    {{ $c("pages.Login.Password") }}
                </div>
                <div class="inp_box">
                    <input :type="eye ? 'text' : 'password'" :class="{ error_input: errors.pwd }" @focus="titlePoint('Password')" class="pwd" v-model.trim="form.pwd" @blur="inputCheck('pwd')" />
                    <validate-error :error="errors.pwd"></validate-error>
                    <password-over class="hoverTip" :icon="false" position="top">
                        <span class="eye icon iconfont" :class="{ show: eye }" @click.stop="toogleEye" slot="trigger">{{ eye ? "&#xe748;" : "&#xe706;" }}</span>
                        <p>{{ eye ? $c("pages.Login.show_word") : $c("pages.Login.Hide_word") }}</p>
                    </password-over>
                </div>
            </div>
            <!-- <div>
                <g-recaptcha ref="grecaptcha" @getValidateCode="getValidateCode"></g-recaptcha>
            </div> -->
            <div class="remember_box">
                <div class="left-box">
                    <label class="chk_box"><input type="checkbox" @change="chkChange" v-model="form.chk" />{{ $c("pages.Login.Keep_me_signed_in") }}</label>
                    <fs-popover>
                        <p class="login_tip">
                            {{ $c("pages.Login.To_keep_your_account_secure") }}
                        </p>
                    </fs-popover>
                </div>
                <nuxt-link :to="localePath({ name: 'forgot_password' })">{{ $c("pages.Login.Forgot_password") }}</nuxt-link>
            </div>
            <fs-button class="sign_in_btn" type="red" :loading="true" htmlType="submit">{{ $c("pages.Login.Sign_In") }}</fs-button>
            <div class="third_login_box">
                <div class="third_title">
                    <span class="line"></span>
                    <span class="text">{{ $c("pages.Login.or_sign_in_with") }}</span>
                    <span class="line"></span>
                </div>
                <div class="third_login">
                    <a class="aicon" v-for="item in icon" :key="item.type" :title="item.title" @click="toThirdParty(item.type)">
                        <span class="icon iconfont" v-html="item.icon"></span>
                        <img :src="item.url" />
                    </a>
                </div>
            </div>
            <div class="reg_bottom">
                <div class="reg_bottom_title">
                    <span class="line"></span>
                    <span class="text">{{ $c("components.smallComponents.pHeader.newCustomer") }}</span>
                    <span class="line"></span>
                </div>
                <nuxt-link class="create_btn" :to="localePath({ path: '/register.html' })">
                    <fs-button type="gray" :style="{ width: `100%` }" @click="createPoint">{{ $c("pages.Login.Create_an_account") }}</fs-button>
                </nuxt-link>
                <!-- <nuxt-link  :to="localePath({ path: '/register.html' })">{{$c('pages.Login.Create_an_account')}}</nuxt-link> -->
            </div>
        </form>
    </div>
</template>

<script>
import { mapState, mapGetters, mapActions } from "vuex"
import ValidateError from "@/components/ValidateError/ValidateError"
import FsButton from "@/components/FsButton/FsButton"
import FsPopover from "@/components/FsPopover"
import PasswordOver from "./PasswordOver"
import ValidateMessage from "@/components/ValidateMessage/ValidateMessage"
import { email_valdate } from "@/constants/validate"
import AES from "@/util/AES.js"
import GRecaptcha from "@/components/GRecaptcha/GRecaptcha"
import { isNeedGrecaptcha, getRecaptchaToken } from "@/util/grecaptchaHost"
import { parseQueryString, setCookieOptions } from "@/util/util.js"

export default {
    layout: "account",
    name: "Login",
    components: {
        ValidateError,
        FsButton,
        FsPopover,
        PasswordOver,
        ValidateMessage,
        GRecaptcha,
    },
    data() {
        return {
            err_msg: "",
            eye: false,
            form: {
                email: "",
                pwd: "",
                chk: true,
                loading: false,
            },
            errors: {
                email: "",
                pwd: "",
            },
            icon: [
                {
                    type: "google",
                    icon: "&#xf302;",
                    url: "https://resource.fs.com/mall/generalImg/20230306142931wr2nor.svg",
                    title: this.$c("pages.Login.Sign_in_with_google"),
                },
                {
                    type: "paypal",
                    icon: "&#xf304;",
                    url: "https://resource.fs.com/mall/generalImg/20230306142931x9gb6k.svg",
                    title: this.$c("pages.Login.Sign_in_with_Paypal"),
                },
                {
                    type: "facebook",
                    icon: "&#xf303;",
                    url: "https://resource.fs.com/mall/generalImg/20230306142931z34y4h.svg",
                    title: this.$c("pages.Login.Sign_in_with_Facebook"),
                },
                {
                    type: "linkin",
                    icon: "&#xf301;",
                    url: "https://resource.fs.com/mall/generalImg/20230306142931tovzzt.svg",
                    title: this.$c("pages.Login.Sign_in_with_Linkedin"),
                },
            ],
            actionsType: null,
            actionsMsg: "",
            timer: null,
            isntLogin: true,
            recaptchaTp: false,
            recaptchaVal: "",
        }
    },
    computed: {
        ...mapState({
            isMobile: (state) => state.device.isMobile,
            isLogin: (state) => state.userInfo.isLogin,
            userInfo: (state) => state.userInfo.userInfo,
        }),
        ...mapGetters({
            gaLoginString: "userInfo/gaLoginString",
        }),
    },
    asyncData({ app, $axios, route, query, redirect }) {
        console.log(route)
        if (query.params) {
        } else if (query.pass) {
        } else {
            return $axios.post("/api/user/info?getMore=1").then((res) => {
                if (res.data.isLogin) {
                    redirect(app.localePath({ name: "my-account" }))
                } else {
                    if (query && query.redirect) {
                        if (Array.isArray(query.redirect)) {
                            redirect(app.localePath({ path: route.path, query: Object.assign({}, query, { redirect: query.redirect[query.redirect.length - 1] }) }))
                        }
                    }
                }
            })
        }
    },
    mounted() {
        if (window && window.localStorage && window.localStorage.getItem("fs_email_submitted")) {
            window.localStorage.removeItem("fs_email_submitted")
        }
        console.log(AES.encrypt("AaBbCc321", "_-yu_xuan_3507-_", "fs_com_phone2016"))
        if (this.$route.query.params && this.$route.query.params.length) {
            this.getActionType("email")
        }
        if (this.$route.query.pass && this.$route.query.pass.length) {
            this.getActionType("pass")
        }
    },
    methods: {
        ...mapActions({
            getUserInfo: "userInfo/getUserInfo",
            getCart: "cart/getCart",
        }),
        toogleEye() {
            this.eye = !this.eye
        },
        chkChange() {
            this.$cookies.set("keep_signed", this.form.chk)
            if (this.form.chk && window.dataLayer) {
                window.dataLayer.push({
                    event: "uaEvent",
                    eventCategory: "Login Page",
                    eventAction: "keep_sign_in",
                    eventLabel: "undefined",
                    nonInteraction: false,
                })
            }
        },
        inputCheck(attr) {
            let flag = false
            if (attr === "email") {
                if (!this.form[attr].replace(/\s+/g, "")) {
                    this.errors[attr] = this.$c("form.validate.email.email_required")
                    flag = true
                } else {
                    if (!email_valdate.test(this.form[attr])) {
                        this.errors[attr] = this.$c("form.validate.email.email_valid")
                        flag = true
                    } else {
                        this.errors[attr] = ""
                    }
                }
            } else if (attr === "pwd") {
                if (!this.form[attr].replace(/\s+/g, "")) {
                    this.errors[attr] = this.$c("form.validate.password.password_required")
                    flag = true
                } else {
                    this.errors[attr] = ""
                }
            }
            return flag
        },
        async submit() {
            let arr = [],
                attr = ["email", "pwd"]

            attr.map((item) => {
                let f = this.inputCheck(item)
                arr.push(f)
            })
            if (arr.includes(true)) {
                return
            }
            if (this.form.loading) {
                return
            }
            let requestHeader = {}
            if (this.isntLogin) {
                // if (isNeedGrecaptcha(window.location.hostname)) {
                //     if (!this.recaptchaTp && this.$refs.grecaptcha) {
                //         this.$refs.grecaptcha.clickRecaptcha()
                //         return
                //     }
                // }
                const { recaptchaTp, headers = {} } = await getRecaptchaToken()
                if (!recaptchaTp) {
                    return
                }
                if (headers) {
                    requestHeader = headers
                }
            }

            this.form.loading = true
            // this.$axios.post("/api/user/login", { customers_email_address: this.form.email, customers_password:
            // this.form.pwd, keep_signed: this.form.chk ? 1 : 0 }).then(res => {

            // 无线选择器处理
            let cartId = ""
            if (this.$route.query.redirect) {
                let query_query = parseQueryString(this.$route.query.redirect)
                query_query.wireless_product && (cartId = query_query.wireless_product)
            }

            this.$axios
                .post(
                    "/api/user/login",
                    { customers_email_address: this.form.email, customers_password: AES.encrypt(this.form.pwd, "_-yu_xuan_3507-_", "fs_com_phone2016"), keep_signed: this.form.chk ? 1 : 0, cartId },
                    // { headers: { "g-recaptcha-response": this.recaptchaVal } }
                    { headers: requestHeader }
                )
                .then((res) => {
                    this.$cookies.remove("login")
                    if (res.data.access_token) {
                        this.$cookies.set("token_new", res.data.access_token)
                    }
                    this.getUserInfo(() => {
                        console.log(this.userInfo)
                        if (window.dataLayer) {
                            window.dataLayer.push({
                                event: "uaEvent",
                                eventCategory: "Login Page",
                                eventAction: "login",
                                eventLabel: "undefined",
                                nonInteraction: false,
                                loginStatus: `Login_${this.gaLoginString}`,
                                userId: `${this.userInfo ? AES.encrypt(`${this.userInfo.customers_level}${this.userInfo.customers_id}`, `_-yu_xuan_3507-_`, `fs_com_phone2016`) : ""}`,
                            })
                        }
                    })
                    this.getCart()

                    if (this.$route.query.redirect) {
                        if (Array.isArray(this.$route.query.redirect)) {
                            this.$router.replace(this.localePath({ path: this.$route.query.redirect[this.$route.query.redirect.length - 1] }))
                        } else {
                            this.$router.replace(this.localePath({ path: this.$route.query.redirect }))
                        }
                    } else {
                        this.$router.replace(this.localePath({ name: "home" }))
                    }

                    this.recaptchaTp = false
                    this.recaptchaVal = ""
                    if (this.$refs.grecaptcha) {
                        this.$refs.grecaptcha.grecaptchaError = ""
                    }
                })
                .catch((err) => {
                    this.form.loading = false
                    if (window.dataLayer) {
                        window.dataLayer.push({
                            event: "uaEvent",
                            eventCategory: "Login Page",
                            eventAction: "login_fail",
                            eventLabel: "undefined",
                            nonInteraction: false,
                        })
                    }
                    if (err.code === 400) {
                        this.err_msg = err.message
                        setTimeout(() => {
                            this.err_msg = ""
                        }, 3000)
                    } else if (err.code === 422) {
                        this.errors.email = err.errors.customers_email_address
                        this.errors.pwd = err.errors.customers_password
                    }

                    if (err.code === 403) {
                        this.$message.error(err.message)
                        this.recaptchaTp = false
                        this.recaptchaVal = ""
                    }
                    if (isNeedGrecaptcha(window.location.hostname)) {
                        if (err.code === 409 && this.$refs.grecaptcha) {
                            this.recaptchaTp = false
                            this.recaptchaVal = ""
                            this.$refs.grecaptcha.grecaptchaError = this.$c("pages.Login.regist.grecaptcha_error")
                        }
                        this.isntLogin = true
                        if (this.timer) {
                            clearInterval(this.timer)
                        }
                        this.timer = setInterval(() => {
                            this.isntLogin = false
                            this.recaptchaTp = false
                            this.recaptchaVal = ""
                            if (this.$refs.grecaptcha) {
                                this.$refs.grecaptcha.grecaptchaError = ""
                            }
                        }, 10000)
                    }
                })
        },
        // 跳转到第三方
        async toThirdParty(type) {
            let url = ""
            if (type === "google") {
                url = "/api/login/socialite/google"
            } else if (type === "paypal") {
                url = "/api/login/socialite/paypal"
            } else if (type === "facebook") {
                url = "/api/login/socialite/facebook"
            } else if (type === "linkin") {
                url = "/api/login/socialite/linkedin"
            }
            if (window.dataLayer) {
                window.dataLayer.push({
                    event: "uaEvent",
                    eventCategory: "Login Page",
                    eventAction: "login_with_other_socialmethod",
                    eventLabel: type,
                    nonInteraction: false,
                })
            }
            const res = await this.$axios.get(url)
            window.location.href = res.data.redirectUrl
        },
        titlePoint(title) {
            if (window.dataLayer) {
                window.dataLayer.push({
                    event: "uaEvent",
                    eventCategory: "Login Page",
                    eventAction: "login_information",
                    eventLabel: `Login-${title} Input`,
                    nonInteraction: false,
                })
            }
        },
        createPoint() {
            if (window.dataLayer) {
                window.dataLayer.push({
                    event: "uaEvent",
                    eventCategory: "Login Page",
                    eventAction: "create_account",
                    eventLabel: "Create an account",
                    nonInteraction: false,
                })
            }
        },
        // 谷歌人机校验
        getValidateCode(val) {
            this.recaptchaTp = val.recaptchaTp
            if (val.recaptchaTp) {
                this.recaptchaVal = val.recaptchaVal
                this.submit()
            }
        },
        // 获取激活状态
        getActionType(str) {
            if (str == "email") {
                this.$axios
                    .post("/api/user/active", {
                        params: this.$route.query.params,
                    })
                    .then((res) => {
                        if (res.code === 200) {
                            this.actionsMsg = this.$c("pages.StateFeedback.success2")
                            this.actionsType = "success"
                            history.pushState({}, "", this.localePath({ name: "login" }))
                        }
                    })
                    .catch((error) => {
                        if (error.code === 400) {
                            this.actionsMsg = this.$c("pages.StateFeedback.error")
                            this.actionsType = "error"
                            history.pushState({}, "", this.localePath({ name: "login" }))
                        } else if (error.code === 422) {
                            this.actionsMsg = this.$c("pages.StateFeedback.error")
                            this.actionsType = "error"
                            history.pushState({}, "", this.localePath({ name: "login" }))
                        }
                    })
            } else {
                if (this.$route.query.pass == "yes") {
                    this.actionsMsg = this.$c("pages.StateFeedback.success")
                    this.actionsType = "success"
                } else {
                    this.actionsMsg = this.$c("pages.StateFeedback.error")
                    this.actionsType = "error"
                }
                history.pushState({}, "", this.localePath({ name: "login" }))
            }
        },
    },
}
</script>

<style lang="scss" scoped>
.login_box {
    width: 100%;
    &::v-deep {
        .validate-message {
            width: 100%;
        }
    }
}
.welcome {
    text-align: center;
    margin: 16px 0 20px 0;
    display: none;
    @include font26;
}
.reg {
    @include font13;
    color: $textColor1;
    text-align: center;
    display: none;
    > a {
        color: $textColor8;
    }
}
.form {
    margin-top: 20px;
    .validate-message {
        margin-bottom: 10px;
    }
}
.forgot_box {
    display: flex;
    justify-content: flex-end;
    a {
        color: $textColor3;
        @include font12;
    }
}

.form_item {
    margin-bottom: 16px;
    &.form_item_pwd {
    }
    .label {
        display: flex;
        align-items: center;
        justify-content: space-between;
        color: $textColor3;
        margin-bottom: 4px;
        @include font12;
        a {
            color: #4b4b4d;
        }
    }
    .pwd {
        padding-right: 44px;
    }
    .inp_box {
        position: relative;
        .eye {
            // position: absolute;
            // font-size: 16px;
            color: $textColor3;
            right: 12px;
            width: 16px;
            height: 16px;
            text-align: left;
            top: 12px;
            cursor: pointer;
            user-select: none;
            &.show {
                font-size: 16px;
            }
            &:hover {
                color: #19191a;
            }
        }
        .error_input {
            @include errorInput;
        }
        .fs-popover.hoverTip {
            position: absolute;
            top: 12px;
            right: 12px;
        }
    }
}
.sign_in_btn {
    width: 100%;
    margin: 32px 0 24px;
}

.remember_box {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .left-box {
        display: flex;
        align-items: center;
    }
    > a {
        @include font13;
        color: #707070;
    }
    .chk_box {
        display: flex;
        align-items: center;
        color: $textColor3;
        @include font13;
        > input {
            width: 18px;
            height: 18px;
            font-size: 18px;
            margin-right: 8px;
            &:before {
                display: block;
            }
        }
    }
    .login_tip {
        @include pc_tip;
        min-width: 300px;
        @media (max-width: 960px) {
            @include m_tip;
        }
    }
}

.third_login_box {
    margin-bottom: 32px;
    .third_title {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 12px;
        .text {
            color: $textColor3;
            @include font13;
            padding: 0;
        }
        .line {
            display: none;
            flex: 1 1 auto;
            height: 1px;
            background: $bgColor2;
        }
    }
    .third_login {
        display: flex;
        justify-content: flex-start;
        .aicon {
            display: flex;
            align-items: center;
            height: 16px;
            color: $textColor3;
            margin-right: 24px;
            cursor: pointer;
            &:last-child {
                margin-right: 0;
            }
            &:hover {
                text-decoration: none;
                .iconfont {
                    display: none;
                }
                img {
                    display: block;
                }
            }
            img {
                width: 16px;
                height: 16px;
                display: none;
            }
        }
    }
}
.reg_bottom {
    // @include font14;
    // color: $textColor1;
    // font-weight: 600;
    // text-align: center;
    // display: block;
    // margin-top: 24px;
    // margin-bottom: 12px;
    .reg_bottom_title {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 16px;
        .text {
            color: $textColor3;
            @include font14;
            padding: 0 12px;
        }
        .line {
            flex: 1 1 auto;
            height: 1px;
            background: $bgColor2;
        }
    }
}
@media (max-width: 768px) {
    .welcome {
        display: block;
    }
}
</style>
