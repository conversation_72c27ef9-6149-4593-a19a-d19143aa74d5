<template>
    <form class="form" @submit.prevent="submit">
        <h2 class="create_account">{{ $c("pages.Login.socialite_bind.create_account") }}</h2>
        <h2 class="third_type" v-html="subs($c('pages.Login.socialite_bind.third_type'), socialiteType, 1)"></h2>
        <p class="third_account">
            {{ socialiteType }} {{ $c("pages.Login.socialite_bind.third_account") }} <span>{{ userEmail }}</span>
        </p>
        <div class="form_item">
            <div class="label">{{ $c("pages.Login.socialite_bind.label1") }}</div>
            <div class="inp_box">
                <input :type="form.pwd_eye ? 'text' : 'password'" v-model.trim="form.pwd" @input="inputCheck('pwd')" />
                <validate-error :error="errors.pwd"></validate-error>
                <!-- <span class="eye" @click.stop="toogleEye('pwd_eye')">{{form.pwd_eye?$c('pages.Login.hide'):$c('pages.Login.show')}}</span> -->
                <span class="eye icon iconfont" :class="{ show: form.pwd_eye }" @click.stop="toogleEye('pwd_eye')">{{ form.pwd_eye ? "&#xe748;" : "&#xe706;" }}</span>
            </div>
        </div>
        <div class="form_item">
            <div class="label">{{ $c("pages.Login.socialite_bind.label2") }}</div>
            <div class="inp_box">
                <input :type="form.re_pwd_eye ? 'text' : 'password'" v-model.trim="form.re_pwd" @input="inputCheck('re_pwd')" />
                <validate-error :error="errors.re_pwd"></validate-error>
                <span class="eye" @click.stop="toogleEye('re_pwd_eye')">{{ form.re_pwd_eye ? $c("pages.Login.hide") : $c("pages.Login.show") }}</span>
            </div>
        </div>
        <div class="form_item">
            <div class="agreement_box">
                <input type="checkbox" class="chk" v-model="form.checked" @change.stop="inputCheck('checked')" />
                <div class="agreement" v-html="subs($c('pages.Login.socialite_bind.agreement'), '', 2)"></div>
            </div>
            <validate-error :error="errors.checked"></validate-error>
        </div>
        <fs-button class="email_sbtn" text="Submit" html-type="submit" :loading="form.btn_loading"></fs-button>
        <div class="create_account_bottom">
            {{ $c("pages.Login.socialite_bind.create_account_bottom_a") }}<nuxt-link :to="localePath({ name: 'third_party_link' })">{{ $c("pages.Login.socialite_bind.create_account_bottom_q") }}</nuxt-link>
        </div>
    </form>
</template>

<script>
import ValidateError from "@/components/ValidateError/ValidateError"
import FsButton from "@/components/FsButton/FsButton"
import { email_valdate, password_validate } from "@/constants/validate"
import { setCookieOptions } from "@/util/util"
import { Base64 } from "js-base64"
import { mapActions } from "vuex"
import AES from "@/util/AES.js"

export default {
    name: "ForgotPassword",
    layout: "account",
    components: {
        ValidateError,
        FsButton,
    },
    data() {
        return {
            type: "",
            customers_id: "",
            form: {
                pwd: "",
                re_pwd: "",
                pwd_eye: false,
                re_pwd_eye: false,
                checked: true,
                btn_loading: false,
            },
            errors: {
                email: "",
                code: "",
                pwd: "",
                re_pwd: "",
                checked: "",
            },
            accessToken: "",
            userData: "",
            userEmail: "",
            socialiteType: "",
        }
    },
    async asyncData({ app, route, redirect }) {
        if (app.$cookies.get("codeData") != route.query.code) {
            if (app.$cookies.get("userData")) {
                app.$cookies.remove("userData")
            }
        }
        if (app.$cookies.get("userData")) {
            return {
                userData: app.$cookies.get("userData"),
            }
        } else {
            if (route.query.code) {
                const res = await app.$axios.get("/api/login/callback", { params: { type: route.query.type, code: route.query.code, scope: route.query.scope, state: route.query.state } })
                if (res.code != 200) {
                    this.$message.error(res.message)
                    redirect(app.localePath({ name: "login" }))
                }
                if (res.data.access_token) {
                    app.$cookies.set("token_new", res.data.access_token)
                    redirect(app.localePath({ name: "home" }))
                } else {
                    app.$cookies.set("userData", res.data.userData)
                    app.$cookies.set("codeData", route.query.code)
                    return {
                        userData: res.data.userData,
                    }
                }
            } else {
                redirect(app.localePath({ name: "home" }))
            }
        }
    },
    // async created(){
    // 	if(this.$cookies.get('codeData')!=this.$route.query.code){
    // 		if(this.$cookies.get('userData')){
    // 			this.$cookies.remove('userData')
    // 		}
    // 	}
    // 	if(this.$cookies.get('userData')){
    // 		// return {
    // 			this.userData = this.$cookies.get('userData')
    // 		// }
    // 	}else{
    // 		const res = await this.$axios.get('/api/login/callback', {params: { type: this.$route.query.type, code: this.$route.query.code, scope: this.$route.query.scope, state: this.$route.query.state }})
    // 		if(res.code!=200){
    // 			if(res.code==201){console.log(res)}
    // 			return {}
    // 		}
    // 		if(res.data.access_token){
    // 			this.$cookies.set("token",res.data.access_token);
    // 			// redirect(app.localePath({ name: "home" }));
    // 			this.$router.replace(this.localePath({ name: "home" }))
    // 		}else{
    // 			this.$cookies.set('userData',res.data.userData)
    // 			this.$cookies.set('codeData',this.$route.query.code)
    // 			// return {
    // 				this.userData = res.data.userData
    // 			// }
    // 		}
    // 	}
    // },
    mounted() {
        let beginTime = 0 //开始时间
        let differTime = 0 //时间差
        window.onunload = () => {
            differTime = new Date().getTime() - beginTime
            if (differTime <= 5) {
                this.$cookies.remove("userData")
            }
        }
        window.onbeforeunload = () => {
            beginTime = new Date().getTime()
        }
        if (this.userData) {
            this.userEmail = JSON.parse(Base64.decode(this.userData)).socialite_data.email
            this.socialiteType = JSON.parse(Base64.decode(this.userData)).socialite_type
        }
    },
    methods: {
        ...mapActions({
            getUserInfo: "userInfo/getUserInfo",
            getCart: "cart/getCart",
        }),
        toogleEye(attr) {
            this.form[attr] = !this.form[attr]
        },
        inputCheck(attr) {
            let flag = false
            if (attr === "pwd") {
                if (!this.form[attr].replace(/\s+/g, "")) {
                    this.errors[attr] = this.$c("form.validate.password.password_required")
                    flag = true
                } else {
                    if (!password_validate.test(this.form[attr])) {
                        this.errors[attr] = this.$c("form.validate.password.password_validate")
                        flag = true
                    } else {
                        this.errors[attr] = ""
                    }
                }
            } else if (attr === "re_pwd") {
                if (!this.form[attr].replace(/\s+/g, "")) {
                    this.errors[attr] = this.$c("form.validate.password.password_confirm")
                    flag = true
                } else {
                    if (this.form.pwd && this.form.re_pwd && this.form.pwd.replace(/\s+/g, "") !== this.form.re_pwd.replace(/\s+/g, "")) {
                        this.errors[attr] = this.$c("form.validate.password.password_match")
                        flag = true
                    } else {
                        this.errors[attr] = ""
                    }
                }
            } else if (attr === "checked") {
                if (this.form[attr]) {
                    this.errors[attr] = ""
                } else {
                    this.errors[attr] = this.$c("form.validate.agreement_agree")
                }
            }
            return flag
        },
        submit() {
            let arr = [],
                attr = ["pwd", "re_pwd"]
            attr.map((item) => {
                let f = this.inputCheck(item)
                arr.push(f)
            })
            if (arr.includes(true) || this.form.btn_loading) {
                return
            }
            this.form.btn_loading = true
            this.$axios
                .post("/api/login/bind", {
                    password: AES.encrypt(this.form.re_pwd, "_-yu_xuan_3507-_", "fs_com_phone2016"),
                    social_data: this.userData,
                })
                .then((res) => {
                    this.form.btn_loading = false
                    this.$cookies.set("token_new", res.data.access_token)
                    this.getUserInfo()
                    this.getCart()
                    this.$cookies.remove("userData")
                    this.$router.replace(this.localePath({ name: "home" }))
                })
                .catch((err) => {
                    this.form.btn_loading = false
                    if (err.code === 400) {
                        this.error.email = err.message
                    }
                })
        },
        // 替换语言包中的变量
        subs(str, n, t) {
            if (t === 1) {
                return str.replace("xxxx", n)
            } else if (t === 2) {
                return str.replace("xxxx", this.localePath({ path: "/policies/privacy_policy.html" })).replace("yyyy", this.localePath({ path: "/policies/terms_of_use.html" }))
            }
        },
    },
}
</script>

<style lang="scss" scoped>
.create_account {
    padding: 35px 0 8px 0;
    @include font26;
    text-align: center;
    display: none;
    @media (max-width: 1024px) {
        display: block;
    }
}
.email_sbtn {
    margin-top: 20px;
}
.third_type {
    @include font14;
    color: $textColor1;
    margin: -5px 0 0 0;
    @media (max-width: 1024px) {
        text-align: center;
    }
}
.third_account {
    @include font13;
    color: $textColor3;
    margin: 28px 0 18px;
}

.form_item {
    margin-bottom: 16px;
    .label {
        display: flex;
        align-items: center;
        color: $textColor3;
        margin-bottom: 6px;
        @include font13;
        a {
            color: $textColor8;
        }
    }
    .pwd {
        padding-right: 44px;
    }
    .inp_box {
        position: relative;
        .eye {
            position: absolute;
            font-size: 16px;
            color: $textColor3;
            right: 8px;
            width: 22px;
            text-align: left;
            top: 12px;
            cursor: pointer;
            user-select: none;
            &.show {
                font-size: 16px;
            }
        }
    }
}
.agreement_box {
    display: flex;
    align-items: center;
    .chk {
        font-size: 16px;
    }
    .agreement {
        @include font13;
        color: $textColor3;
        ::v-deep a {
            color: $textColor1;
        }
    }
}
.create_account_bottom {
    display: none;
    text-align: center;
    @include font13;
    color: $textColor1;

    padding: 32px 0 28px 0;
    @media (max-width: 1024px) {
        display: block;
    }
}
</style>
