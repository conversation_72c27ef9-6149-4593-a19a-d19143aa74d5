<template>
    <form class="form" @submit.prevent="submit">
        <h2 class="create_account">{{ $c("pages.Login.third_party_link.create_account") }}</h2>
        <h2 class="third_type" v-html="subs($c('pages.Login.third_party_link.third_type'), socialiteType, 1)"></h2>
        <!-- <p class="third_account">Google Email Address: <span><EMAIL></span></p> -->
        <div class="form_item">
            <div class="label">{{ $c("pages.Login.third_party_link.label1") }}</div>
            <div class="inp_box">
                <input type="text" v-model.trim="form.email" @input="inputCheck('email')" />
                <validate-error :error="errors.email"></validate-error>
            </div>
        </div>
        <div class="form_item">
            <div class="label" v-html="subs($c('pages.Login.third_party_link.label2'), '', 2)"></div>
            <div class="inp_box">
                <input :type="form.eye ? 'text' : 'password'" class="pwd" v-model.trim="form.pwd" @input="inputCheck('pwd')" />
                <validate-error :error="errors.pwd"></validate-error>
                <!-- <span class="eye" @click.stop="toogleEye">{{form.eye?$c('pages.Login.hide'):$c('pages.Login.show')}}</span> -->
                <span class="eye icon iconfont" :class="{ show: form.eye }" @click.stop="toogleEye">{{ form.eye ? "&#xe748;" : "&#xe706;" }}</span>
            </div>
        </div>
        <!-- <div class="form_item">
			<div class="agreement_box">
				<input type="checkbox" class="chk" v-model="form.checked" @change.stop="inputCheck('checked')" />
				<div class="agreement">
					<span class="info1">I agree to FS's </span>
					<nuxt-link :to="localePath({ name: '' })">Privacy Policy and Notice at Collection</nuxt-link> and <nuxt-link :to="localePath({ name: '' })">Term of Use.</nuxt-link>
				</div>
			</div>
			<validate-error :error="errors.checked"></validate-error>
		</div> -->
        <fs-button class="email_sbtn" html-type="submit" :loading="form.btn_loading">{{ $c("pages.Login.third_party_link.email_sbtn") }}</fs-button>
    </form>
</template>

<script>
import ValidateError from "@/components/ValidateError/ValidateError"
import FsButton from "@/components/FsButton/FsButton"
import { email_valdate, password_validate } from "@/constants/validate"
import { setCookieOptions } from "@/util/util"
import { Base64 } from "js-base64"
import { mapActions } from "vuex"
import AES from "@/util/AES.js"

export default {
    name: "ForgotPassword",
    layout: "account",
    components: {
        ValidateError,
        FsButton,
    },
    data() {
        return {
            type: "",
            customers_id: "",
            form: {
                email: "",
                pwd: "",
                eye: false,
                btn_loading: false,
            },
            errors: {
                email: "",
                pwd: "",
            },
            userData: "",
            socialiteType: "",
        }
    },
    asyncData({ app, redirect }) {
        if (!app.$cookies.get("userData")) {
            redirect(app.localePath({ name: "login" }))
        } else {
            return {
                userData: app.$cookies.get("userData"),
                socialiteType: JSON.parse(Base64.decode(app.$cookies.get("userData"))).socialite_type,
            }
        }
    },
    mounted() {
        let beginTime = 0 //开始时间
        let differTime = 0 //时间差
        window.onunload = () => {
            differTime = new Date().getTime() - beginTime
            if (differTime <= 5) {
                this.$cookies.remove("userData")
            }
        }
        window.onbeforeunload = () => {
            beginTime = new Date().getTime()
        }
    },
    methods: {
        ...mapActions({
            getUserInfo: "userInfo/getUserInfo",
            getCart: "cart/getCart",
        }),
        toogleEye() {
            this.form.eye = !this.form.eye
        },
        inputCheck(attr) {
            let flag = false
            if (attr === "email") {
                if (!this.form[attr].replace(/\s+/g, "")) {
                    this.errors[attr] = this.$c("form.validate.email.email_required")
                    flag = true
                } else {
                    if (!email_valdate.test(this.form[attr])) {
                        this.errors[attr] = this.$c("form.validate.email.email_validate")
                        flag = true
                    } else {
                        this.errors[attr] = ""
                    }
                }
            } else if (attr === "pwd") {
                if (!this.form[attr].replace(/\s+/g, "")) {
                    this.errors[attr] = this.$c("form.validate.password.password_required")
                    flag = true
                } else {
                    if (!password_validate.test(this.form[attr])) {
                        this.errors[attr] = this.$c("form.validate.password.password_validate")
                        flag = true
                    } else {
                        this.errors[attr] = ""
                    }
                }
            }
            return flag
        },
        submit() {
            let arr = [],
                attr = ["email", "pwd"]
            attr.map((item) => {
                let f = this.inputCheck(item)
                arr.push(f)
            })
            if (arr.includes(true) || this.form.btn_loading) {
                return
            }
            this.form.btn_loading = true
            this.$axios
                .post("/api/login/bind", {
                    email_address: this.form.email,
                    password: AES.encrypt(this.form.pwd, "_-yu_xuan_3507-_", "fs_com_phone2016"),
                    social_data: this.userData,
                })
                .then((res) => {
                    this.form.btn_loading = false
                    this.$cookies.set("token_new", res.data.access_token)
                    this.getUserInfo()
                    this.getCart()
                    this.$router.replace(this.localePath({ name: "home" }))
                })
                .catch((err) => {
                    this.form.btn_loading = false
                    if (err.code === 400) {
                        this.error.email = err.message
                    }
                })
        },
        // 替换语言包中的变量
        subs(str, n) {
            if (t === 1) {
                return str.replace("xxxx", n)
            } else if (t === 2) {
                return str.replace("xxxx", this.localePath({ path: "/forgot_password.html" }))
            }
        },
    },
}
</script>

<style lang="scss" scoped>
.create_account {
    padding: 35px 0 8px 0;
    @include font26;
    text-align: center;
    display: none;
    @media (max-width: 1024px) {
        display: block;
    }
}
.email_sbtn {
    margin-top: 20px;
}
.third_type {
    @include font14;
    color: $textColor1;
    margin: -5px 0 28px 0;
    @media (max-width: 1024px) {
        text-align: center;
    }
}
// .third_account{
// 	@include font13;
//     color: $textColor3;
//     margin: 28px 0 18px;
// }

.form_item {
    margin-bottom: 16px;
    .label {
        display: flex;
        align-items: center;
        color: $textColor3;
        margin-bottom: 6px;
        justify-content: space-between;
        @include font13;
        ::v-deep .forgot {
            @include font12;
            color: $textColor3;
        }
    }
    .pwd {
        padding-right: 44px;
    }
    .inp_box {
        position: relative;
        .eye {
            position: absolute;
            font-size: 16px;
            color: $textColor3;
            right: 8px;
            width: 22px;
            text-align: left;
            top: 12px;
            cursor: pointer;
            user-select: none;
            &.show {
                font-size: 16px;
            }
        }
    }
}
.agreement_box {
    display: flex;
    align-items: center;
    .chk {
        font-size: 16px;
    }
    .agreement {
        @include font13;
        color: $textColor3;
        a {
            color: $textColor1;
        }
    }
}
.create_account_bottom {
    display: none;
    text-align: center;
    @include font13;
    color: $textColor1;

    padding: 32px 0 28px 0;
    @media (max-width: 1024px) {
        display: block;
    }
}
</style>
