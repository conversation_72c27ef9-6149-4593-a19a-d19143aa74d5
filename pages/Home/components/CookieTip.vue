<template>
    <div class="cookie" v-show="showCookieTip">
        <!-- <div class="main-notEU main" v-if="!EuUnion">
            <p v-html="validateFont($c('pages.home.cookie.txt01'))"></p>
            <a href="javascript:;" @click.stop="agreeCookie" class="icon iconfont">&#xf30a;</a>
        </div> -->
        <div class="main-EU main">
            <p @click="disAgreeGoogle($event)" v-html="validateFont($c('pages.home.cookie.txt02'))"></p>
            <fs-button type="black" class="accept" tabindex="0" @click="agreeCookie">{{ $c("pages.home.cookie.accept") }}</fs-button>
        </div>
    </div>
</template>

<script>
import FsButton from "@/components/FsButton/FsButton.vue"
import { mapMutations, mapState } from "vuex"
import { setCookieOptions } from "@/util/util"

export default {
    name: "cookie-tip",
    data() {
        return {
            // EuUnion: false,
            // showCookieTip: false
        }
    },
    components: {
        FsButton,
    },
    mounted() {
        // let cookie = this.$cookies.get("cookieconsent_dismissed")
        // if (cookie) {
        //     // this.showCookieTip = false;
        //     this.setShowCookieTip(false)
        // } else {
        //     // this.showCookieTip = true;
        //     this.setShowCookieTip(true)
        //     // console.log(this.EuUnion)
        // }
    },
    methods: {
        ...mapMutations({
            setShowCookieTip: "webSiteInfo/setShowCookieTip",
        }),
        agreeCookie() {
            // if (this.EuUnion) {
            this.$cookies.set("fs_google_analytics", "yes")
            this.$cookies.set("cookieconsent_dismissed", "yes")
            // } else {
            //     this.$cookies.set('cookieconsent_dismissed', 'yes', setCookieOptions({
            //         maxAge: 60 * 60 * 24 * 365
            //     }));
            // }
            // this.showCookieTip = false;
            this.setShowCookieTip(false)
        },
        disAgreeGoogle(e) {
            if (e.target.className === "disAgreeGoogle") {
                let googleAnalytics = this.$cookies.get("_ga")
                if (googleAnalytics) {
                    // $.removeCookie('_ga', {path:'/', domain:'.fs.com'});
                    // $.removeCookie('_gid', {path:'/', domain:'.fs.com'});
                    // $.removeCookie('AMP_TOKEN', {path:'/', domain:'.fs.com'});
                    // $.removeCookie('_ym_isad', {path:'/', domain:'.fs.com'});
                    // $.removeCookie('_ym_uid', {path:'/', domain:'.fs.com'});
                    // $.removeCookie('_ym_visorc_48770636', {path:'/', domain:'.fs.com'});
                    this.$cookies.remove("_ga")
                    this.$cookies.remove("_gid")
                    this.$cookies.remove("AMP_TOKEN")
                    this.$cookies.remove("_ym_isad")
                    this.$cookies.remove("_ym_uid")
                    this.$cookies.remove("_ym_visorc_48770636")
                }
            }
            // this.showCookieTip = false;
            this.setShowCookieTip(false)
        },
        validateFont(params) {
            let str = ""
            str = params.replace("policies/privacy_policy.html", `${["en", "cn"].includes(this.website) ? "" : this.website}/policies/privacy_policy.html`)
            return str
        },
    },
    computed: {
        ...mapState({
            website: (state) => state.webSiteInfo.website,
            showCookieTip: (state) => state.webSiteInfo.showCookieTip,
        }),
    },
    created() {
        console.log(this.website)
    },
}
</script>

<style lang="scss" scoped>
.cookie {
    width: 100%;
    background-color: #707070;
    position: fixed;
    left: 0;
    bottom: 0;
    z-index: 10;
    .main {
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
        height: 100%;

        max-width: 1200px;
        width: 84vw;
        margin: 0 auto;

        @media (max-width: 1200px) {
            width: 94vw;
        }
        @media (max-width: 960px) {
            width: 100%;
            padding: 0;
        }
    }
    .main-notEU {
        p {
            @include font13;
            color: $textColor7;
            padding-right: 20px;
            ::v-deep a {
                text-decoration: underline;
                color: $textColor7;
            }
        }
        .icon {
            color: $textColor7;
            font-size: 12px;
            position: absolute;
            right: 0;
            top: 50%;
            transform: translateY(-50%);
        }
    }
    .main-EU {
        justify-content: space-between;
        :deep(.fs-button) {
            @media (max-width: 768px) {
                width: 100%;
            }
        }
        p {
            max-width: 1200px;
            @include font13;
            color: $textColor3;
            line-height: 20px;
            ::v-deep a {
                text-decoration: underline;
                color: $textColor7;
            }
        }
        @media (max-width: 768px) {
            flex-direction: column;
            .accept {
                margin-left: 0;
                margin-top: 20px;
            }
        }
    }
}
</style>
