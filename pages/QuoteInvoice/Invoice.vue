<template>
    <div class="invoice_detail" v-loading="loading">
        <invoice-ru v-if="isRU" :info="info" @download="download" @print="print" :subtotal="subtotal" :isHeavy="isHeavy"></invoice-ru>
        <InvoiceUK id="pdfCentent" :info="info" :isPickAt="isPickAt" @download="download" @print="print" v-else-if="info.warehouse == 41"></InvoiceUK>
        <!--  收货地址为日本，显示日本pdf样式 -->
        <invoice-jpsite v-else-if="isJpAddress" :info="info"></invoice-jpsite>
        <invoice-default id="pdfCentent" v-else :info="info" :isPickAt="isPickAt" @download="download" @print="print"></invoice-default>
    </div>
</template>

<script>
import FsButton from "@/components/FsButton/FsButton"
import InvoiceJpsite from "./components/InvoiceJpsite1.vue"
import InvoiceUK from "./components/InvoiceUK.vue"
import InvoiceDefault from "./components/InvoicedDefault.vue"
import InvoiceRu from "./components/InvoiceRu.vue"
import { mapState } from "vuex"
export default {
    layout: "print",
    components: {
        FsButton,
        InvoiceJpsite,
        InvoiceUK,
        InvoiceDefault,
        InvoiceRu,
    },
    data() {
        return {
            info: {},
            loading: false,
            isRU: false,
            isHeavy: false,
            subtotal: 0,
            isJP: "",
            isPickAt: false,
            isJpAddress: false,
        }
    },
    computed: {
        ...mapState({
            website: (state) => state.webSiteInfo.website,
        }),
    },
    async created() {
        if (!process.client) return
        this.loading = true
        const params = {
            quotes_id: this.$route.query.id,
        }
        const res = await this.$axios.post("/api/quotes_pdf", params).catch((res) => {
            if (res.code === 422) {
                const { quotes_id } = res.errors
                this.$message.error(quotes_id)
                setTimeout(() => {
                    this.$router.replace(this.localePath("/"))
                }, 3000)
            }
        })
        if (res && res.code === 200) {
            this.info = res.data
            this.isHeavy = this.info.quotes_products.some((v) => {
                return v.isHeavy
            })
            this.info.quotes_products.forEach((v) => {
                v.CN = v.inventory?.CN?.currentQty
                v.RU = v.inventory?.RU?.currentQty
            })
            this.subtotal = +this.info.quotesTotal.subtotal.split("&nbsp;")[0].split(",").join("")
            // }
            this.isRU = this.info.payment_method_code === "alfa" && this.info.quotes_address[0].entry_address_type === "BusinessType"

            // 收货地址为日本，显示日本pdf样式
            this.isJpAddress = this.info.quotes_address[0].entry_country_id === 107
            this.loading = false
        }
    },
    mounted() {
        this.isJP = ~location.pathname.indexOf("/jp/")
    },
    watch: {
        info(n) {
            console.log("info", n)
            if (
                (this.info.shipping_local_method_code == "selfreferencezones_selfreferencezones" && ["selfreferencezones_selfreferencezones", ""].includes(this.info.shipping_delay_method_code)) ||
                (this.info.shipping_delay_method_code == "selfreferencezones_selfreferencezones" && ["selfreferencezones_selfreferencezones", ""].includes(this.info.shipping_local_method_code))
            ) {
                this.isPickAt = true
            }

            this.$nextTick(() => {
                if (this.$route.query?.handle == "print") {
                    this.print()
                }
            })
        },
    },
    methods: {
        print() {
            window.dataLayer &&
                window.dataLayer.push({
                    event: "uaEvent",
                    eventCategory: "Personal Hub_Quote History Page Detail",
                    eventAction: "download_quote_operate",
                    eventLabel: "Print Quote",
                    nonInteraction: false,
                })
            print()
        },
        download() {
            console.log(this.info.quotes_number)
            window.dataLayer &&
                window.dataLayer.push({
                    event: "uaEvent",
                    eventCategory: "Personal Hub_Quote History Page Detail",
                    eventAction: "download_quote_operate",
                    eventLabel: "Download Quote",
                    nonInteraction: false,
                })
            this.exportSavePdf(this.info.quotes_number)
        },
    },
}
</script>

<style lang="scss" scoped>
.invoice_detail {
    @page {
        margin: 0 10mm;
    }
    @media print {
        .noprint {
            visibility: hidden;
        }
    }
}
</style>
