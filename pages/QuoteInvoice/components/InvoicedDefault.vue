<template>
    <div class="main">
        <div class="head">
            <div data-html2canvas-ignore="true" class="noprint">
                <fs-button :text="$c('pages.OrderDetail.bill.print')" type="blackline" @click="print">
                    <i class="iconfont">&#xe736;</i>
                </fs-button>
                <fs-button :text="$c('pages.OrderDetail.bill.download')" type="blackline" @click="download">
                    <i class="iconfont">&#xe653;</i>
                </fs-button>
            </div>
            <div class="title">
                <img src="https://img-en.fs.com/includes/templates/fiberstore/images/fs-logo1218.png" alt="" />
                <span>{{ $c("pages.Quote.officialQuote") }}</span>
            </div>
        </div>
        <div class="order">
            <div>
                <b>{{ $c("pages.Quote.rqNumber") }}</b>
                <span>{{ info.quotes_number }}</span>
                <span class="date">
                    <b>{{ $c("pages.Quote.date") }}</b> <span>{{ info.create_time }}</span>
                </span>
            </div>
            <div v-if="!info.is_local_japan">
                <b>{{ $c("pages.Quote.tradeTerm") }}</b>
                <span>{{ info.trade_term || "C&F" }}</span>
                <span></span>
            </div>
            <div>
                <b>{{ $c("pages.Quote.currency") }}</b>
                <span>{{ info.currency }}</span>
                <span></span>
            </div>
            <div>
                <b>{{ $c("pages.Quote.paymentMethod") }}</b>
                <span>{{ info.payment_method }}</span>
                <span></span>
            </div>
            <div>
                <b>{{ $c("pages.Quote.shipVia") }}</b>
                <span>{{ info.shipping_method }}</span>
                <span></span>
            </div>
            <div>
                <b>{{ $c("pages.Quote.createdBy") }}</b>
                <span>{{ info.admin_name }}</span>
                <span></span>
            </div>
            <div>
                <b>{{ $c("pages.Quote.email") }}</b>
                <span>{{ info.admin_email }}</span>
                <span></span>
            </div>
        </div>
        <div class="address">
            <div v-if="isPickAt">
                <b>{{ $c("pages.confirmOrder.pickUpAt") }}</b>
                <p v-for="(v, i) in info.pickUpInfo" :key="i" v-show="i != 0">{{ v }}</p>
            </div>
            <div v-if="info.quotes_address && !isPickAt">
                <b>{{ $c("pages.Quote.deliverTo") }}</b>
                <p v-for="(item, index) in getAddressDd(info.quotes_address[0])" :key="index">
                    {{ item }}
                </p>
            </div>
            <div v-if="info.quotes_address">
                <b>{{ $c("pages.Quote.billTo") }}</b>
                <p v-for="(item, index) in getAddressDd(info.quotes_address[1])" :key="index">
                    {{ item }}
                </p>
            </div>
            <div>
                <b>{{ $c("pages.Quote.issuedBy") }}</b>
                <p v-for="(v, i) in info.issued_info" :key="i">{{ v }}</p>
            </div>
        </div>
        <div class="product">
            <div class="product_table">
                <div class="thead">
                    <span>{{ $c("pages.Quote.no") }}</span>
                    <span>{{ $c("pages.Quote.productId") }}</span>
                    <span>{{ $c("pages.Quote.itemDescription") }}</span>
                    <span>{{ $c("pages.OrderDetail.bill.quantityPcs") }}</span>
                    <!-- 澳洲消费税特殊展示 -->
                    <span
                        class="price"
                        v-if="info.quotes_address"
                        v-html="website == 'au' && info.quotes_address[0].entry_country_id == 13 ? `${$c('pages.Quote.unitPrice')}<br>(Excl.GST)` : $c('pages.Quote.unitPrice')"></span>
                    <span v-if="info.quotes_address" v-html="$c('pages.Quote.total')"></span>
                </div>
                <ul class="tbody">
                    <li v-for="(v, i) in info.quotes_products" :key="v.orders_id">
                        <span>{{ i + 1 }}</span>
                        <span>{{ v.products_id }}</span>
                        <span class="name-span">
                            {{ v.products_name }}
                            <em class="warehouse" v-if="v.products_warehouse_remark">{{ v.products_warehouse_remark }}</em>
                            <em v-for="(y, j) in v.attributes || []" :key="j + 10">{{ y.products_options }}: {{ y.products_options_values }}</em>
                            <em v-for="(y, j) in v.length || []" :key="j">{{ y.length_title }}: {{ y.length_name }}</em>
                            <server-attributes v-if="v.server_attributes" :list="v.server_attributes"></server-attributes>
                        </span>
                        <span>{{ v.products_qty }}</span>
                        <span v-html="v.products_quotes_price_str"></span>
                        <span class="total_price" v-html="v.products_quotes_price_total_str"></span>
                    </li>
                </ul>
            </div>
            <div class="total" v-if="info.quotesTotal">
                <section>
                    <p>{{ $c("pages.Quote.otherComments") }}</p>
                    <span>{{ info.sale_comment }}</span>
                </section>
                <div>
                    <div>
                        <span>{{ $c("pages.Quote.sub") }}</span>
                        <b v-html="info.quotesTotal.subtotal"></b>
                    </div>
                    <div v-if="info.quotesTotal.shipping">
                        <span>{{ $c("pages.Quote.sh") }}</span>
                        <b v-html="info.quotesTotal.shipping || 'US$ 0.00'"></b>
                    </div>
                    <div v-if="info.quotesTotal.insurance">
                        <span>{{ $c("pages.confirmOrder.ddp.insurance") }}</span>
                        <b v-html="info.quotesTotal.insurance || '0'"></b>
                    </div>
                    <div v-if="info.quotesTotal.tax">
                        <!-- <span>Est. Sales Tax</span> -->
                        <span>{{ info.tax_express }}</span>
                        <b v-html="info.quotesTotal.tax"></b>
                    </div>
                    <div v-if="info.quotesTotal.clearanceFee">
                        <span>{{ $c("pages.confirmOrder.ddp.clearance") }}</span>
                        <b v-html="info.quotesTotal.clearanceFee || '--'"></b>
                    </div>
                    <div>
                        <span>{{ $c("pages.Quote.totalT") }}</span>
                        <b v-html="info.quotesTotal.total"></b>
                    </div>
                </div>
            </div>
        </div>
        <div class="foot">
            <div class="fixed_note">
                <b>{{ $c("pages.Quote.note") }}: </b>
                <p>1.{{ $c("pages.Quote.theQuotation") }}</p>
                <p>2.{{ $c("pages.Quote.leaveMessage") }}</p>
            </div>
            <div class="name_time">
                <div>
                    <span>{{ $c("pages.Quote.authorisedBy") }}</span>
                    <span>{{ info.admin_name }}</span>
                </div>
                <div>
                    <span>{{ $c("pages.Quote.date") }}</span>
                    <span>{{ info.create_time }}</span>
                </div>
            </div>
            <p v-if="info.bool_invoice">{{ $c("pages.Quote.document") }}</p>
        </div>
    </div>
</template>

<script>
import ServerAttributes from "@/components/ServerAttributes/ServerAttributes"
import FsButton from "@/components/FsButton/FsButton"
import { mapState } from "vuex"

export default {
    props: {
        info: {
            type: Object,
            default: () => {},
        },
        isPickAt: {
            type: Boolean,
            default: false,
        },
    },
    components: {
        FsButton,
        ServerAttributes,
    },
    methods: {
        download() {
            this.$emit("download")
        },
        print() {
            this.$emit("print")
        },
        getAddressDd(obj) {
            const { entry_company, entry_street_address, entry_suburb, entry_city, entry_postcode, entry_state, entry_country, entry_firstname, entry_lastname, entry_telephone, entry_tax_number, entry_eori } = obj
            const list = [
                entry_company,
                [entry_street_address, entry_suburb],
                [entry_city, entry_postcode],
                [entry_state, entry_country],
                `${entry_firstname} ${entry_lastname}`,
                `${this.$c("pages.Quote.tel")} ${entry_telephone}`,
                entry_tax_number,
                entry_eori ? `${this.$c("common.basic.addressHandle.EORI")}${entry_eori}` : "",
            ]
            const maxList = list.map((i) => (Array.isArray(i) ? i.filter((j) => j).join(", ") : i))
            return maxList.filter((i) => i)
        },
    },
    computed: {
        ...mapState({
            website: (state) => state.webSiteInfo.website,
        }),
    },
}
</script>

<style lang="scss" scoped>
.main {
    .head {
        .noprint {
            display: flex;
            justify-content: flex-end;
            column-gap: 12px;
            .iconfont {
                margin-right: 8px;
            }
        }

        .title {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 8px;
            padding-bottom: 24px;
            border-bottom: 1px solid $textColor1;

            img {
                height: 36px;
                width: 79px;
            }

            > span {
                @include font20;
                font-weight: 600;
            }
        }
    }
    .order {
        padding: 32px 0;
        @include font14;
        text-align: left;
        > div {
            display: flex;
            > b,
            > span {
                flex: 1;
                padding-right: 20px;
            }

            &:first-child {
                padding: 0;
            }
            .date {
                display: flex;
                b {
                    display: block;
                    min-width: 160px;
                }
            }
        }
    }
    .address {
        display: flex;
        margin-bottom: 40px;
        > div {
            flex: 1;
            @include font12;
            > b {
                display: block;
                @include font14;
                margin-bottom: 8px;
            }
            > p:not(:last-of-type) {
                margin-bottom: 4px;
            }
        }
    }
    .product {
        .product_table {
            .thead {
                background: #4b4b4d;
                -webkit-print-color-adjust: exact;
                @include font14;
                color: #fff;
                span {
                    font-weight: 600;
                    padding: 4px 12px;
                }
            }
            .thead,
            ul li {
                display: flex;
                span {
                    display: flex;
                    justify-content: center;
                    align-items: center;

                    &:nth-child(1) {
                        flex: 8;
                    }
                    &:nth-child(2) {
                        flex: 11;
                    }
                    &:nth-child(3) {
                        flex: 37;
                    }
                    &:nth-child(4) {
                        flex: 13;
                    }
                    &:nth-child(5) {
                        flex: 13;
                        text-align: center;
                    }
                    &:nth-child(6) {
                        flex: 16;
                        text-align: center;
                    }
                    &.name-span {
                        flex-direction: column;
                        align-items: flex-start;
                        em {
                            font-style: normal;
                        }
                    }
                }
            }
            ul li {
                span {
                    @include font14;
                    border-left: 1px solid #19191a;
                    border-bottom: 1px solid #19191a;
                    padding: 16px 12px;
                    word-break: break-word;
                    &:last-child {
                        border-right: 1px solid #19191a;
                    }
                }
            }
        }
        .total {
            display: flex;
            justify-content: space-between;
            padding: 24px 0;
            border-bottom: 1px dashed #19191a;
            @include font14;
            > section {
                flex: 1;
                p {
                    font-weight: 600;
                    line-height: 20px;
                }
                span {
                    display: block;
                }
            }
            > div {
                > div {
                    @include font14;
                    display: grid;
                    grid-template-columns: auto 140px;
                    column-gap: 40px;
                    span {
                        min-width: 200px;
                        text-align: right;
                        font-weight: 600;
                    }
                    b {
                        text-align: right;
                        font-weight: normal;
                    }
                }
            }
        }
    }
    .foot {
        padding-top: 24px;
        b {
            display: block;
            font-weight: 600;
            margin-bottom: 4px;
        }
        .note {
            margin-top: 30px;
        }
        p {
            line-height: 22px;
        }

        .fixed_note {
            @include font14;
            b,
            p {
                margin-bottom: 4px;
            }
            p {
                &:last-child {
                    margin-bottom: 0;
                }
            }
        }
        > .name_time {
            margin-top: 32px;
            > div {
                display: flex;
                justify-content: flex-end;
                span {
                    min-width: 100px;
                    font-weight: 600;
                    &:last-child {
                        min-width: 200px;
                        padding-left: 40px;
                        text-align: right;
                        font-weight: normal;
                    }
                }
            }
        }
    }
}
</style>
