<template>
    <DashBord :breadCrumbList="breadCrumbList">
        <div class="criteria_page" v-loading="loading">
            <h2>{{ $c("pages.OrderReport.criteria.reportCriteria") }}</h2>
            <p class="title_tips" v-if="tips"><span class="iconfont">&#xe718;</span>{{ tips }}</p>
            <!-- 内容区域 -->
            <div>
                <!-- 选择报告时间 -->
                <div class="selct_time">
                    <div class="selct_time_left">
                        <h4>{{ $c("pages.OrderReport.criteria.selectTime") }}:</h4>
                        <fs-select :options="getYear(selcetList)" @change="slectChagne" :value="Number(timeSelect.reporting_time_select)"></fs-select>
                    </div>
                    <div class="time_date" v-show="timeSelect.reporting_time_select == 6">
                        <time-select ref="dateSelect" :show="timeSelect.reporting_time_select == 6" @change="dateChange" :defaultTime="defaultTime" />
                    </div>
                </div>
                <div class="select_data">
                    <p class="tips" v-html="$c('pages.OrderReport.criteria.tips.selectData')"></p>
                    <div class="criteria_box">
                        <shuttle-frame ref="shuttle" @run="handlerRun" @save="save" :info="selectFields" :canRun="tips.length > 0" :disabled="isNoDate" />
                    </div>
                </div>
            </div>
        </div>
        <!-- 弹出层 -->
        <fs-popup :show="showPoup" @close="initPoup" :loading="popupLoading" :title="$c('pages.OrderReport.reportPage.customReport')" :isMDrawer="true">
            <div class="save_report_poup">
                <p class="poup_title">{{ $c("pages.OrderReport.reportPage.name") }}</p>
                <p>
                    <input type="text" v-model.trim="reportInfo.name" maxlength="51" @keydown.enter="continueReport" @input="validateName" />
                    <validate-error :error="reportInfo.errTExt"></validate-error>
                </p>
                <div class="option_btn">
                    <fs-button type="grayline" @click="cancel('Cancel')">{{ $c("pages.OrderReport.poupe.btn.cancel") }}</fs-button>
                    <fs-button @click="continueReport('Continue')" type="red">{{ $c("pages.OrderReport.poupe.btn.countinue") }}</fs-button>
                </div>
            </div>
        </fs-popup>
        <!-- 保存报告 -->
        <fs-popup :show="showSavePoup" @close="initPoup" :title="$c('pages.OrderReport.poupe.saveChanges')">
            <div class="save_report_poup save_new_report_poup">
                <div class="option_btn">
                    <a href="javaScript:;" @click="contnueCarite('Continue to Edit Criteria')">{{ $c("pages.OrderReport.poupe.continue") }}</a>
                    <fs-button type="blackline" @click="saveNew('Save as New')">{{ $c("pages.OrderReport.poupe.saveNew") }}</fs-button>
                    <fs-button :loading="popupLoading" @click="saveChanges('Save Changes', 2)" type="red">{{ $c("pages.OrderReport.poupe.saveChanges") }}</fs-button>
                </div>
            </div>
        </fs-popup>
        <!-- 查看报告弹窗 -->
        <fs-popup :show="showOpenPoup" :loading="popupLoading" @close="initPoup" :title="$c('pages.OrderReport.reportPage.customReport')" :isMDrawer="true">
            <div class="save_report_poup">
                <p class="poup_title">{{ $c("pages.OrderReport.reportPage.name") }}</p>
                <p>
                    <!-- <input type="text" :class="{ input_error: reportInfo.errTExt }" v-model.trim="reportInfo.name" maxlength="51" @keydown.enter="continueReport" @input="validateName" /> -->
                    <input type="text" v-model.trim="reportInfo.name" maxlength="51" @keydown.enter="continueReport('Save as New')" @input="reportInfo.errTExt = ''" />
                    <validate-error :error="reportInfo.errTExt"></validate-error>
                </p>
                <div class="option_btn">
                    <fs-button type="blackline" @click="saveChanges('Save Changes', 1)">{{ $c("pages.OrderReport.poupe.saveChanges") }}</fs-button>
                    <fs-button @click="continueReport('Save as New')" type="red">{{ $c("pages.OrderReport.poupe.saveNew") }}</fs-button>
                </div>
            </div>
        </fs-popup>
    </DashBord>
</template>

<script>
import TimeSelect from "./components/TimeSelect.vue"
import DashBord from "../OrderReport/components/Layout/index.vue"
import FsSelect from "../../components/FsSelect/FsSelect.vue"
import ShuttleFrame from "./components/ShuttleFrame.vue"
import FsPopup from "../../components/FsPopupNew/FsPopupNew.vue"
import ValidateError from "../../components/ValidateError/ValidateError.vue"
import FsButton from "@/components/FsButton/FsButton.vue"
import cariteria from "./reportTable"
import { mapState } from "vuex"
export default {
    name: "reportCriteria",
    components: {
        DashBord,
        FsSelect,
        ShuttleFrame,
        TimeSelect,
        FsPopup,
        ValidateError,
        FsButton,
    },
    head: {
        script: [{ src: "https://front-resource.fs.com/fs-platform/static/js/account/exceljs.min.js" }],
    },
    mixins: [cariteria],
    data() {
        return {
            tips: "",
            breadCrumbList: [
                { name: this.$c("pages.SupportTicket.myAccount"), url: "/my-account" },
                { name: this.$c("pages.OrderReport.reportPage.orderReporting"), url: "/order-reporting.html" },
                { name: this.$c("pages.OrderReport.criteria.reportCriteria"), url: "/reporting-criteria.html" },
            ],
            selcetList: [
                { name: this.$c("pages.OrderReport.criteria.previousDay"), value: 1 },
                { name: this.$c("pages.OrderReport.criteria.latestMonth"), value: 2 },
                { name: this.$c("pages.OrderReport.criteria.lastMonth"), value: 3 },
                { name: this.$c("pages.OrderReport.criteria.latestThreeMonths"), value: 4 },
                { name: this.$c("pages.OrderReport.criteria.lastSix"), value: 5 },
                // { name: this.$c("pages.OrderReport.criteria.year").replace("xxxx", new Date().getFullYear()), value: 6 },
                // { name: this.$c("pages.OrderReport.criteria.year").replace("xxxx", new Date().getFullYear() - 1), value: 7 },
                // { name: this.$c("pages.OrderReport.criteria.year").replace("xxxx", new Date().getFullYear() - 2), value: 8 },
                // { name: this.$c("pages.OrderReport.criteria.custom"), value: 6 },
            ],
            showDateSelect: false,
            dateTime: "",
            //订单数据
            selectFields: [],
            // 报告时间
            timeSelect: {
                reporting_time_select: new Date().getFullYear(),
                start_time: "",
                end_time: "",
            },
            loading: false,
            // 是否是编辑模式
            isEditType: false,
            reportInfo: {
                errTExt: "",
                name: "",
            },
            showPoup: false,
            showSavePoup: false,
            // 查看报告弹窗
            showOpenPoup: false,
            //是否是系统报告
            isSystemReport: false,
            //系统报告为空报告
            isSystemEmpty: false,
            //是否是新建报告
            isNewCreate: false,
            //报告信息基本信息
            reportOneInfo: {},
            //弹出层loading状态
            popupLoading: false,
            //按钮的loading状态
            btnLoading: false,
        }
    },
    methods: {
        dateChange(date) {
            this.dateTime = date
            this.timeSelect.start_time = date.split("-")[0]
            this.timeSelect.end_time = date.split("-")[1]
            const res = this.validateDate(date)
            if (res) {
                this.tips = this.$c("pages.OrderReport.criteria.errorSelect")
                window.dataLayer &&
                    window.dataLayer.push({
                        event: "uaEvent",
                        eventCategory: this.pageGroup,
                        eventAction: "report_errors",
                        eventLabel: `Invalid Date Range`,
                        nonInteraction: false,
                    })
            } else {
                this.tips = ""
            }
        },
        contnueCarite(type) {
            this.initPoup()
            // this.editeGa(type)
            this.GaSaveReport(type, false, "edit_report_pop")
        },
        //编辑报告埋点
        editeGa(type) {
            window.dataLayer &&
                window.dataLayer.push({
                    event: "uaEvent",
                    eventCategory: this.pageGroup,
                    eventAction: "report_criteria",
                    eventLabel: type,
                    nonInteraction: false,
                })
        },
        slectChagne(val) {
            const name = this.getYear(this.selcetList).filter((item) => item.value === val)
            window.dataLayer &&
                window.dataLayer.push({
                    event: "uaEvent",
                    eventCategory: this.pageGroup,
                    eventAction: "criteria_operate",
                    eventLabel: `Time Drop-Down_${name[0]?.name}`,
                    nonInteraction: false,
                })
            this.timeSelect.reporting_time_select = val
            if (val === 6) {
                this.showDateSelect = true
                this.timeSelect.start_time = ""
                this.timeSelect.end_time = ""
            } else {
                this.$refs.dateSelect?.clearDate()
                this.showDateSelect = false
                this.timeSelect.start_time = ""
                this.timeSelect.end_time = ""
                //清除提示，按钮恢复使用
                this.tips = ""
                this.dateTime = ""
            }
        },
        // 校验日期选择是否大于一年
        validateDate(date) {
            const afterDate = date.split("-")[1]
            const beforeDate = date.split("-")[0]
            const res = new Date(afterDate).getTime() - new Date(beforeDate).getTime()
            const day = res / 24 / 60 / 60 / 1000
            return day > 366
        },
        async handlerRun(val) {
            const res = this.validateDate(this.dateTime)
            if (this.timeSelect && this.timeSelect.reporting_time_select == 6) {
                const { start_time, end_time } = this.timeSelect
                if (!start_time || !end_time) {
                    return
                }
            }
            if (res) {
                this.tips = this.$c("pages.OrderReport.criteria.errorSelect")
                window.dataLayer &&
                    window.dataLayer.push({
                        event: "uaEvent",
                        eventCategory: this.pageGroup,
                        eventAction: "report_errors",
                        eventLabel: `Invalid Date Range`,
                        nonInteraction: false,
                    })
                return
            } else {
                this.tips = ""
                // const id = this.$route.query.id || 0
                const { id, type } = this.$route.query
                //将数据进行缓存
                const params = {
                    reporting_fields: val.join(";"),
                    reporting_time_select: this.timeSelect.reporting_time_select,
                    reporting_name: this.reportInfo.name,
                }
                if (this.timeSelect.start_time) {
                    params.start_time = this.timeSelect.start_time
                }
                if (this.timeSelect.end_time) {
                    params.end_time = this.timeSelect.end_time
                }
                window.localStorage.setItem("reportCriteria", JSON.stringify(params))
                const { reportType } = this.$route.query
                //判断是否是新建报告
                if (!id && !type) {
                    // id和类型都为空时,表示为创建的新报告页面
                    this.$router.push(this.localePath({ path: `/reporting-detail.html`, query: { reportType, type: 1 } }))
                } else {
                    let newtype = 1
                    if (type) {
                        newtype = type
                        if (type == 2) {
                            newtype = 4
                        }
                    }
                    const { isCompany } = this.$route.query
                    // this.$router.push(this.localePath(`/reporting-detail.html?id=${id}&type=${newtype == 3 ? 3 : newtype}` + (reportType ? `&reportType=${reportType}` : "")))
                    this.$router.push({ path: this.localePath(`/reporting-detail.html`), query: { id, type: newtype == 3 ? 3 : newtype, reportType, isCompany } })
                }
            }
        },
        // 获取页面上数据
        getCriteria() {
            const { id, type } = this.$route.query
            if (id && id != 0) {
                // 当报告id存在时，需要报告的信息，取customers_id 用于保存用户报告时区分是否是企业报告中保存的非自己报告
                this.getReportDetail(id, type)
            }
            // if (id && id != 0) {
            if (!type) {
                // 编辑模式开启
                this.isEditType = true
            } else {
                // const { reporting_fields, reporting_time_select, end_time, start_time } = JSON.parse(window.localStorage.getItem("reportCriteria"))
                const res = JSON.parse(window.localStorage.getItem("reportCriteria"))
                if (res && res.reporting_fields) {
                    this.selectFields = res.reporting_fields.split(";")
                    this.timeSelect.reporting_time_select = res.reporting_time_select || res.time_select
                    const timeS = res.start_time || res.start_date
                    this.timeSelect.start_time = this.getRangDate(timeS)
                    const timeE = res.end_time || res.end_date
                    this.timeSelect.end_time = this.getRangDate(timeE)
                    this.reportInfo.name = res.reporting_name
                } else {
                    this.selectFields = []
                }
            }
        },
        // 保存报告
        saveReport(params, type) {
            this.popupLoading = true
            const newparams = {
                ...params,
                reporting_time_select: this.timeSelect.reporting_time_select,
                // ...this.timeSelect,
            }
            if (this.timeSelect.start_time) {
                newparams.start_time = this.timeSelect.start_time
            }
            if (this.timeSelect.end_time) {
                newparams.end_time = this.timeSelect.end_time
            }
            const { customers_id: related_id = "" } = this.reportOneInfo
            if (related_id && related_id !== this.customers_id) {
                newparams.related_id = related_id
            }
            //判断保存报告类型
            const { reportType } = this.$route.query
            if (reportType !== undefined) {
                newparams.isPersonal = reportType
            }
            this.$axios
                .post("/api/reporting/save", newparams)
                .then((res) => {
                    this.loading = false
                    if (res.status === "success") {
                        if (type === "Continue") {
                            this.GaSaveReport(type)
                        }
                        if (type === "Save as New") {
                            this.GaSaveReport(type, false, "view_report_pop")
                        }
                        this.reportInfo.errTExt = ""
                        window?.localStorage.removeItem("reportCriteria")
                        this.$router.push(this.localePath("/order-reporting.html"))
                    }
                })
                .catch((err) => {
                    this.loading = false
                    const r = err.data.result
                    if (r == 2) {
                        //报告存在（重名）
                        this.reportInfo.errTExt = this.$c("pages.OrderReport.poupe.text3").replace("xxxx", params.reporting_name)
                        this.GaSaveReport("", 4)
                    }
                    if (r == 1) {
                        //保存报告数量超过50个
                        this.reportInfo.errTExt = this.$c("pages.OrderReport.poupe.text6")
                        this.GaSaveReport("", 5)
                    }
                    if (r == 3) {
                        //报告内容为空  不允许保存
                        this.reportInfo.errTExt = this.$c("pages.OrderReport.poupe.text2")
                        this.GaSaveReport("", 1)
                    }
                })
                .finally(() => {
                    this.popupLoading = false
                })
        },
        getReportDetail(id, type) {
            if (isNaN(id * 1)) {
                this.$router.push(this.localePath("/404.html"))
                return
            }
            this.loading = true
            this.$axios
                .get(`/api/reporting/getOne?id=${id}`)
                .then((res) => {
                    this.loading = false
                    if (res.status === "success") {
                        if (!type) {
                            const timeS = res.data.start_time || res.data.start_date
                            this.timeSelect.start_time = this.getRangDate(timeS)
                            const timeE = res.data.end_time || res.data.end_date
                            this.timeSelect.end_time = this.getRangDate(timeE)
                            this.reportInfo.name = res.data.reporting_name
                            const arr = res.data.reporting_fields.split(";")
                            this.selectFields = arr
                            if (res.data.time_select == 6) {
                                this.showDateSelect = true
                            }
                            // 将选择结果赋值
                            this.selectFields = arr
                            this.timeSelect.reporting_time_select = res.data.time_select
                        }
                        this.reportOneInfo = res.data
                    }
                })
                .catch((err) => {
                    this.loading = false
                })
        },
        // 初始化弹窗
        initPoup() {
            // this.reportInfo.name = ""
            this.reportInfo.errTExt = ""
            this.showPoup = false
            this.showSavePoup = false
            this.showOpenPoup = false
        },
        // 校验报告名
        validateName() {
            if (this.isSystemEmpty) {
                this.reportInfo.errTExt = this.$c("pages.OrderReport.poupe.text2")
                //空报告不允许保存
                this.GaSaveReport("", 1)
                return false
            }
            const target = this.reportInfo.name
            if (target.length < 1) {
                this.reportInfo.errTExt = this.$c("pages.OrderReport.poupe.text1")
                //报告名为空，不允许保存
                this.GaSaveReport("", 2)
                return false
            }
            if (target.length > 50) {
                this.reportInfo.errTExt = this.$c("pages.OrderReport.poupe.text4").replace("xx", "50")
                //报告名长度超过50个字符
                this.GaSaveReport("", 3)
                return false
            }
            this.reportInfo.errTExt = ""
            return true
        },
        continueReport(type) {
            // this.saveChangeGa(type)
            if (this.validateName()) {
                //保存报告
                let params = {
                    reporting_fields: this.selectFields,
                    reporting_name: this.reportInfo.name,
                }
                this.saveReport(params, type)
            }
        },
        cancel(type) {
            this.initPoup()
            this.GaSaveReport(type)
        },
        // 更新报告
        saveChanges(type, btnType) {
            /**
              @btnType==1  查看报告弹窗   @btnType==2  编辑分类弹窗
             * 
             */
            const res = this.validateName()
            if (!res) return
            this.popupLoading = true
            let params = {
                reporting_fields: this.selectFields,
                reporting_name: this.reportInfo.name,
                id: this.$route.query.id,
                ...this.timeSelect,
            }
            const { reportType } = this.$route.query
            if (reportType !== undefined) {
                params.isPersonal = reportType
            }
            // 编辑报告
            this.$axios
                .post("/api/reporting/update", params)
                .then((res) => {
                    //跳转到首页
                    if (btnType === 1) {
                        this.GaSaveReport(type)
                    } else {
                        this.GaSaveReport(type, false, "edit_report_pop")
                    }
                    this.initPoup()
                    this.$router.push(this.localePath("/order-reporting.html"))
                })
                .catch((err) => {
                    const r = err.data.result
                    if (r == 2) {
                        //报告存在（重名）
                        this.reportInfo.errTExt = this.$c("pages.OrderReport.poupe.text3").replace("xxxx", params.reporting_name)
                        this.GaSaveReport("", 4)
                    }
                    if (r == 1) {
                        //保存报告数量超过50个
                        this.reportInfo.errTExt = this.$c("pages.OrderReport.poupe.text6")
                        this.GaSaveReport("", 5)
                    }
                    if (r == 3) {
                        //报告内容为空  不允许保存
                        this.reportInfo.errTExt = this.$c("pages.OrderReport.poupe.text2")
                        this.GaSaveReport("", 1)
                    }
                })
                .finally(() => {
                    this.popupLoading = false
                })
        },
        //savechages埋点
        saveChangeGa(type) {
            window.dataLayer &&
                window.dataLayer.push({
                    event: "uaEvent",
                    eventCategory: this.pageGroup,
                    eventAction: "save_report",
                    eventLabel: type,
                    nonInteraction: false,
                })
        },
        //保存为新报告
        saveNew(type) {
            this.GaSaveReport(type, false, "edit_report_pop")
            // this.editeGa(type)
            this.showSavePoup = false
            this.reportInfo.name = ""
            this.showOpenPoup = false
            this.showPoup = true
        },
        save(val) {
            const res = this.validateDate(this.dateTime)
            if (res) {
                this.tips = this.$c("pages.OrderReport.criteria.errorSelect")
                return
            } else {
                this.tips = ""
            }
            this.selectFields = val
            // 当为系统报告时
            if (this.isSystemReport) {
                this.showPoup = true
                this.reportInfo.name = ""
                return
            }
            if (this.isNewCreate) {
                this.showPoup = true
            } else {
                this.showOpenPoup = true
            }
            // if (this.isEditType) {
            //     this.showSavePoup = true
            // } else {
            //     this.showPoup = true
            // }
        },
        // 获取日期年份
        getYear(oldList) {
            const nowD = new Date().getFullYear()
            const arr = []
            for (let i = 0; i <= nowD - 2020; i++) {
                arr.unshift({
                    name: this.$c("pages.OrderReport.criteria.year").replace("xxxx", nowD - i),
                    value: nowD - i,
                })
            }
            arr.push({ name: this.$c("pages.OrderReport.criteria.custom"), value: 6 })
            return [...oldList, ...arr]
        },
        // 补零函数
        addZero(val) {
            if (val * 1 < 10) {
                return "0" + val
            } else {
                return val
            }
        },
        // 将时间转化为日期选择器格式
        getRangDate(val) {
            let olddate = val
            if (!val) {
                return ""
            }
            //对德语进行判断
            if (this.website == "de") {
                const arr = val.split(".")
                olddate = `${arr[2]}/${arr[1]}/${arr[0]}}`
            }
            const time = new Date(olddate)
            const yy = time.getFullYear()
            const mm = time.getMonth() + 1
            const dd = time.getDate()
            let res = `${yy}/${this.addZero(mm)}/${this.addZero(dd)}`
            if (this.website === "de") {
                res = `${this.addZero(dd)}.${this.addZero(mm)}.${yy}`
            }
            return res
        },
        //保存报告弹窗埋点
        GaSaveReport(type, errorType, event = "save_report_pop") {
            if (errorType) {
                window.dataLayer &&
                    window.dataLayer.push({
                        event: "uaEvent",
                        eventCategory: this.pageGroup,
                        eventAction: "report_errors",
                        eventLabel: `Save Error_${errorType}`,
                        nonInteraction: false,
                    })
            } else {
                window.dataLayer &&
                    window.dataLayer.push({
                        event: "uaEvent",
                        eventCategory: this.pageGroup,
                        eventAction: event,
                        eventLabel: type,
                        nonInteraction: false,
                    })
            }
        },
    },
    computed: {
        ...mapState({
            pageGroup: (state) => state.ga.pageGroup,
            customers_id: (state) => state.userInfo?.userInfo?.customers_id,
        }),
        //选择自定义时间情况下未选择日期
        isNoDate() {
            let flag = false
            if (this.showDateSelect && !this.timeSelect.start_time && !this.timeSelect.end_time) {
                flag = true
            } else {
                flag = false
            }
            return flag
        },
        defaultTime() {
            if (this.timeSelect.start_time && this.timeSelect.end_time) {
                return `${this.timeSelect.start_time} - ${this.timeSelect.end_time}`
            } else {
                return ""
            }
        },
    },
    mounted() {
        //type类型含义:  1-用户临时报告   2-系统生成年度报告，不允许编辑保存   3-系统报告为空报告，不允许保存

        const { id, type } = this.$route.query
        if (!id || id == 0) {
            //当id不存在 --创建的新报告
            this.isNewCreate = true
        }
        // 当类型为2的时候为系统生成报告，不允许保存修改
        if (type == 2) {
            this.isSystemReport = true
        }
        if (type == 3) {
            this.isSystemEmpty = true
        }
        this.getCriteria()
    },
}
</script>

<style lang="scss" scoped>
.criteria_page {
    color: $textColor1;
    > h2 {
        @include font20;
        margin-bottom: 24px;
    }
    .title_tips {
        padding: 10px 16px;
        background: rgba(192, 0, 0, 0.04);
        border-radius: 3px;
        color: #c00000;
        @include font14;
        margin-top: -16px;
        margin-bottom: 36px;
        .iconfont {
            font-size: 14px;
            margin-right: 8px;
        }
    }
    .selct_time {
        // display: flex;
        display: flex;
        margin-bottom: 24px;
        align-items: flex-start;
        .selct_time_left {
            display: flex;
            align-items: center;
            ::v-deep .options-wrap-absolute {
                z-index: 4;
            }
            > h4 {
                @include font16;
                width: max-content;
            }
            .fs-select {
                margin-left: 12px;
                min-width: 200px;
                max-width: max-content;
            }
        }

        .time_date {
            margin-left: 12px;
        }
    }
    .select_data {
        .tips {
            @include font16;
            margin-bottom: 20px;
        }
        .criteria_box {
            max-width: 752px;
        }
    }
}
.save_report_poup {
    min-width: 680px;
    // padding: 20px 32px;
    padding: 16px 24px 0;
    .poup_title {
        @include font12;
        margin-bottom: 4px;
    }
    p {
        margin-bottom: 24px;
        @media (max-width: 768px) {
            margin-bottom: 20px;
        }
    }
    .option_btn {
        display: flex;
        justify-content: flex-end;
        // padding-top: 20px;
        // margin-top: 20px;
        margin-bottom: 24px;
        @media (max-width: 768px) {
            margin-bottom: 16px;
        }
        .fs-button {
            margin-left: 12px;
            @media (max-width: 768px) {
                &:last-child {
                    margin-top: 0;
                }
            }
        }
        a {
            width: max-content;
        }
    }
}
.save_new_report_poup {
    .option_btn {
        align-items: center;
        padding: 0;
        margin-top: 0;
    }
}
::v-deep {
    .fs-popup-ctn {
        .fs-popup-header {
            .iconfont_close {
                // top: 50%;
                // transform: translateY(-50%);
            }
        }
    }
    .fs-button > {
        .box {
            width: max-content;
        }
    }
}
@media (max-width: 1200px) {
    .criteria_page {
        > h2 {
            margin: 0;
            padding: 24px 0;
        }
    }
}
@media (max-width: 760px) {
    .criteria_page {
        .selct_time {
            flex-direction: column;
            margin-bottom: 24px;
            .selct_time_left {
                width: 100%;
                flex-direction: column;
                align-items: flex-start;
                > .fs-select {
                    margin-left: 0;
                    margin-top: 12px;
                    min-width: 100%;
                    max-width: none;
                }
            }
            .time_date {
                width: 100%;
                margin-left: 0;
                margin-top: 12px;
            }
        }
    }
    .save_report_poup {
        min-width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        padding: 16px 16px 0;
        .option_btn {
            flex: 1;
            flex-direction: column-reverse;
            justify-content: flex-start;
            > .fs-button {
                margin: 0;
                margin-top: 12px;
                &::v-deep {
                    .box {
                        width: 100%;
                    }
                }
            }
        }
    }
}
</style>
