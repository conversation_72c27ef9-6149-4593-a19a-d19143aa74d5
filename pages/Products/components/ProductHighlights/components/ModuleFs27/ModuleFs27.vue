<template>
    <div class="module_FS27">
        <div class="title" v-html="data.title"></div>
        <div class="description" v-html="data.description"></div>
        <div v-if="!isMobile" class="content">
            <div class="left">
                <div class="item" :class="{ active: index == hoverIndex }" v-for="(item, index) in leftNav" :key="index" @click="mouseoverIndex(index)">
                    <span v-html="item" :title="stripHtml(item)"></span>
                </div>
            </div>
            <div class="right">
                <div class="nav_body">
                    <div class="nav_item" :class="{ lessBody: data?.content[hoverIndex]?.section.length < 2 }">
                        <div
                            class="options"
                            :class="{ active: checkIndex == index, less: data?.content[hoverIndex]?.section.length < 2 }"
                            v-for="(item, index) in data?.content[hoverIndex]?.section"
                            :key="index"
                            @click=";(checkIndex = index), (show_image_index = index)">
                            <span v-html="item.horizontal_title" :title="stripHtml(item.horizontal_title)"></span>
                        </div>
                    </div>
                    <div class="nav_text">
                        <div class="nav_text_title" v-html="data?.content[hoverIndex]?.section[checkIndex]?.title"></div>
                        <div class="nav_text_descr" v-html="data?.content[hoverIndex]?.section[checkIndex]?.description"></div>
                    </div>
                </div>
                <div class="pic">
                    <template v-if="data?.content[hoverIndex]?.section[checkIndex]?.src_type === 'img'">
                        <img class="enlarge_image" @click="imgClick" :src="data?.content[hoverIndex]?.section[checkIndex]?.src" :alt="data?.content[hoverIndex]?.section[checkIndex]?.src_desc" />
                        <!-- <PhotoGallery galleryID="demo_gallery" :images="[data?.content[hoverIndex]?.section[checkIndex]?.src]" /> -->
                    </template>
                    <template v-else-if="data?.content[hoverIndex]?.section[checkIndex]?.src_type === 'mp4'">
                        <video :src="data?.content[hoverIndex]?.section[checkIndex]?.src" width="100%" controls />
                    </template>
                    <template v-else-if="data?.content[hoverIndex]?.section[checkIndex]?.src_type === 'tag' && Array.isArray(data?.content[hoverIndex]?.section[checkIndex].tag)">
                        <fs-tag-new :tag="data?.content[hoverIndex]?.section[checkIndex]?.tag[0]" :source="'feature'"></fs-tag-new>
                    </template>
                </div>
            </div>
        </div>
        <div v-else class="content isMobileContent">
            <div class="bgNav">
                <div class="nav" @click="m_show_list = !m_show_list">
                    <span v-html="m_show_title"></span>
                    <i class="iconfont" :class="{ iconfont_down_up: m_show_list }">&#xe704;</i>
                </div>
                <div class="nav_content">
                    <div class="option_list" :class="{ less_list: data?.content[m_show_hoverIndex]?.section.length < 2 }">
                        <div class="option_item" :class="{ active: m_show_checkIndex == index }" v-for="(item, index) in data?.content[m_show_hoverIndex]?.section" :key="index" @click="m_show_checkIndex = index">
                            <span v-html="item.horizontal_title"></span>
                        </div>
                    </div>
                    <div class="option_text">
                        <div class="option_title" v-html="data?.content[m_show_hoverIndex]?.section[m_show_checkIndex]?.title"></div>
                        <div class="option_descr" v-html="data?.content[m_show_hoverIndex]?.section[m_show_checkIndex]?.description"></div>
                    </div>
                    <div class="option_pic">
                        <template v-if="data?.content[m_show_hoverIndex]?.section[m_show_checkIndex]?.src_type === 'img'">
                            <!-- <img :src="data?.content[m_show_hoverIndex]?.section[m_show_checkIndex]?.src_m" :alt="data?.content[m_show_hoverIndex]?.section[m_show_checkIndex]?.src_desc" /> -->
                            <PhotoGallery :key="`mobile-${m_show_hoverIndex}-${m_show_checkIndex}`" galleryID="demo_gallery" :images="[data?.content[m_show_hoverIndex]?.section[m_show_checkIndex]?.src_m]" />
                        </template>
                        <template v-else-if="data?.content[m_show_hoverIndex]?.section[m_show_checkIndex]?.src_type === 'mp4'">
                            <video :src="data?.content[m_show_hoverIndex]?.section[m_show_checkIndex]?.src" width="100%" controls />
                        </template>
                        <template v-else-if="data?.content[m_show_hoverIndex]?.section[m_show_checkIndex]?.src_type === 'tag' && Array.isArray(data?.content[m_show_hoverIndex]?.section[m_show_checkIndex].tag_m)">
                            <fs-tag-new :tag="data?.content[m_show_hoverIndex]?.section[m_show_checkIndex]?.tag_m[0]" :source="'feature'"></fs-tag-new>
                        </template>
                    </div>
                </div>
                <slide-down>
                    <div v-if="m_show_list" class="nav_list">
                        <div class="nav_item_m" :class="{ active: m_show_hoverIndex == index }" v-for="(item, index) in leftNav" :key="index" @click="handleMobileNavClick(index)">
                            <span v-html="item"></span>
                        </div>
                    </div>
                </slide-down>
            </div>
        </div>
        <image-view v-model="show_image_view" :list="show_image_list" :index="show_image_index"></image-view>
    </div>
</template>

<script>
import ImageView from "../ImageView/ImageView.vue"
import PhotoGallery from "@/components/PhotoGallery/PhotoGallery.vue"
import SlideDown from "@/components/SlideDown/SlideDown.vue"
import FsTagNew from "@/components/FsTag/FsTagNew"
import { mapState } from "vuex"
export default {
    name: "ModuleFs27",
    components: {
        ImageView,
        FsTagNew,
        SlideDown,
        PhotoGallery,
    },
    props: {
        data: {
            type: Object,
            default() {
                return {}
            },
        },
    },
    data() {
        return {
            hoverIndex: 0,
            checkIndex: 0,
            m_show_title: "",
            m_show_list: false,
            m_show_hoverIndex: 0,
            m_show_checkIndex: 0,
            show_image_view: false,
            show_image_index: 0,
            show_image_list: [],
        }
    },
    watch: {
        hoverIndex(val) {
            this.checkIndex = 0
        },
        m_show_hoverIndex(val) {
            this.m_show_checkIndex = 0
            // 修复：更新移动端标题
            this.m_show_title = this.data?.content[val]?.title || ""
            // 修复：更新图片列表
            if (this.data?.content[val]?.section && this.data?.content[val]?.section.length) {
                let arr = this.data?.content[val]?.section.map((item) => {
                    return { src: item.src, src_type: item.src_type, src_desc: item.src_desc }
                })
                this.show_image_list = arr
            }
        },
    },
    computed: {
        ...mapState({
            isMobile: (state) => state.device.isMobile,
        }),
        leftNav() {
            return this.data.content.map((item) => {
                return item.title
            })
        },
    },
    mounted() {
        this.m_show_title = this.data?.content[0].title
        if (this.data?.content[0]?.section && this.data?.content[0]?.section.length) {
            let arr = this.data?.content[0]?.section.map((item) => {
                return { src: item.src, src_type: item.src_type, src_desc: item.src_desc }
            })
            this.show_image_list = arr
        }
    },
    methods: {
        menuMouseenter(index) {
            this.hoverIndex = index
        },
        stripHtml(html) {
            return html.replace(/<[^>]*>?/gm, "")
        },
        mouseoverIndex(index) {
            this.show_image_index = 0
            this.hoverIndex = index
            let arr = this.data?.content[index]?.section.map((item) => {
                return { src: item.src, src_type: item.src_type, src_desc: item.src_desc }
            })
            this.show_image_list = arr
        },
        imgClick() {
            this.show_image_view = true
        },
        // 新增：处理移动端导航点击
        handleMobileNavClick(index) {
            this.m_show_hoverIndex = index
            this.m_show_list = false
        },
    },
}
</script>
<style lang="scss" scoped>
.module_FS27 {
    width: 100%;
    max-width: 900px;
    margin: 0 auto;
    display: flex;
    align-items: center;
    flex-direction: column;
    .title {
        padding-top: 20px;
        @include font20;
        color: $textColor1;
        font-weight: 600;
        text-align: center;
        @media (max-width: 768px) {
            padding: 0;
            @include font16;
        }
    }
    .description {
        @include font14;
        color: $textColor3;
        max-width: 700px;
        margin: 12px auto 0;
        text-align: center;
        @media (max-width: 768px) {
            max-width: 100%;
        }
    }
    .content {
        display: flex;
        width: 100%;
        max-width: 1200px;
        margin: 0 auto;
        margin-top: 28px;
        @media (max-width: 768px) {
            margin-top: 20px;
        }
        .left {
            border-radius: 4px;
            background-color: $bgColor1;
            padding: 20px 0 20px 16px;
            .item {
                padding: 13px 16px;
                border-radius: 4px 0 0 4px;
                width: 152px;
                cursor: pointer;
                text-overflow: ellipsis;
                overflow: hidden;
                white-space: nowrap;
                span {
                    @include font14;
                    color: $textColor1;
                    position: relative;
                    &::after {
                        content: "";
                        height: 2px;
                        border-radius: 999px;
                        background-color: $bgColor5;
                        display: block;
                        width: 100%;
                        position: absolute;
                        opacity: 0;
                        bottom: -4px;
                        max-width: 120px;
                    }
                }
                &.active {
                    background-color: $bgColor3;
                    span {
                        &::after {
                            opacity: 1;
                        }
                    }
                }
            }
        }
        .right {
            flex: 1;
            display: flex;
            grid-gap: 20px;
            padding-left: 20px;
            flex-direction: column;
            .nav_body {
                flex: 1;
                // max-width: 380px;
                .nav_item {
                    padding: 6px;
                    border-radius: 999px;
                    background: rgba(25, 25, 26, 0.04);
                    display: flex;
                    gap: 4px;
                    max-width: max-content;
                    .options {
                        padding: 4px 16px;
                        border-radius: 999px;
                        cursor: pointer;
                        max-width: 165px;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        white-space: nowrap;
                        &.less {
                            max-width: 330px;
                            border-radius: 0;
                            cursor: initial;
                            > span {
                                &::after {
                                    max-width: 298px;
                                }
                            }
                        }
                        > span {
                            @include font14;
                            color: $textColor3;
                            position: relative;
                            &::after {
                                content: "";
                                height: 2px;
                                border-radius: 999px;
                                background-color: $bgColor5;
                                display: block;
                                width: 100%;
                                position: absolute;
                                opacity: 0;
                                bottom: -4px;
                                max-width: 133px;
                            }
                        }
                        &.active {
                            background-color: $bgColor3;
                            > span {
                                color: $textColor1;
                                &::after {
                                    opacity: 1;
                                }
                            }
                        }
                    }
                    &.lessBody {
                        background-color: #fff;
                        padding: 0;
                        .options {
                            padding: 0;
                            > span {
                                @include font14;
                                font-weight: 600;
                                &::after {
                                    display: none;
                                }
                            }
                        }
                        .less {
                            > span {
                                @include font16;
                            }
                        }
                    }
                }
                .nav_text {
                    margin-top: 20px;
                    .nav_text_title {
                        @include font14;
                        color: $textColor1;
                        font-weight: 600;
                    }
                    .nav_text_descr {
                        @include font12;
                        color: $textColor3;
                        margin-top: 8px;
                    }
                }
            }
            .pic {
                flex: 1;
                max-width: 100%;
                // max-height: 338px;
                > img {
                    width: 100%;
                    // height: 100%;
                    &.enlarge_image {
                        cursor: url("https://img-en.fs.com/includes/templates/fiberstore/images/cursor_serch_ic.png"), auto;
                    }
                }
                > video {
                    height: 100%;
                    border-radius: 12px;
                }
                ::v-deep .fs_tag_wrap {
                    height: 100%;
                    .fs_tag {
                        height: 100%;
                        .tag_img {
                            width: 100%;
                            height: 100%;
                            border-radius: 12px;
                        }
                    }
                }
            }
        }
        .bgNav {
            width: 100%;
            position: relative;
            .nav {
                padding: 12px 0;
                background-color: $bgColor1;
                display: flex;
                align-items: center;
                justify-content: space-between;
                position: relative;
                > span {
                    @include font14;
                    color: $textColor1;
                    font-weight: 600;
                }
                > i {
                    font-size: 12px;
                    line-height: 1;
                    color: $textColor1;
                    &.iconfont_down_up {
                        transform: rotateX(-180deg);
                    }
                }
            }
            .nav_list {
                top: 46px;
                left: -16px;
                width: calc(100% + 32px);
                position: absolute;
                padding: 8px 0 8px 32px;
                background-color: #f7f7f7;
                border-top: 1px solid $borderColor2;
                .nav_item_m {
                    padding: 13px 16px;
                    border-radius: 4px 0 0 4px;
                    cursor: pointer;
                    span {
                        @include font14;
                        color: $textColor1;
                        position: relative;
                        &::after {
                            content: "";
                            height: 2px;
                            border-radius: 999px;
                            background-color: $bgColor5;
                            display: block;
                            width: 100%;
                            position: absolute;
                            opacity: 0;
                            bottom: -4px;
                        }
                    }
                    &.active {
                        background-color: $bgColor3;
                        span {
                            &::after {
                                opacity: 1;
                            }
                        }
                    }
                }
            }
            .nav {
                &::after {
                    display: block;
                    position: absolute;
                    content: "";
                    width: calc(100% + 32px);
                    background-color: #f7f7f7;
                    height: 100%;
                    z-index: -1;
                    left: -16px;
                    top: 0;
                }
            }
            .nav_content {
                margin-top: 28px;
                .option_list {
                    margin: 0 auto;
                    padding: 6px;
                    border-radius: 999px;
                    background: rgba(25, 25, 26, 0.04);
                    display: flex;
                    gap: 4px;
                    max-width: max-content;
                    .option_item {
                        padding: 4px 16px;
                        border-radius: 999px;
                        cursor: pointer;
                        > span {
                            @include font14;
                            color: $textColor3;
                            position: relative;
                            &::after {
                                content: "";
                                height: 2px;
                                border-radius: 999px;
                                background-color: $bgColor5;
                                display: block;
                                width: 100%;
                                position: absolute;
                                opacity: 0;
                                bottom: -4px;
                            }
                        }
                        &.active {
                            background-color: $bgColor3;
                            > span {
                                color: $textColor1;
                                &::after {
                                    opacity: 1;
                                }
                            }
                        }
                    }
                    &.less_list {
                        padding: 0;
                        .option_item {
                            padding: 0;
                            > span {
                                @include font16;
                                font-weight: 600;
                                &::after {
                                    display: none;
                                }
                            }
                        }
                    }
                }
                .option_text {
                    margin-top: 16px;
                    text-align: center;
                    .option_title {
                        @include font14;
                        color: $textColor1;
                    }
                    .option_descr {
                        @include font12;
                        color: $textColor3;
                        margin-top: 12px;
                    }
                }
                .option_pic {
                    border-radius: 12px;
                    margin-top: 16px;
                    overflow: hidden;
                    > img {
                        width: 100%;
                    }
                    > video {
                        max-height: 214px;
                    }
                }
            }
        }
    }
}
</style>
