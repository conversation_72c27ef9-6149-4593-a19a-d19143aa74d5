<template>
    <div class="sample-request sample-request-products" v-loading="loading">
        <div class="sample_main">
            <div class="box-title">{{ $c("pages.Products.installationPopup.SelectProduct") }}</div>
            <div class="product-box">
                <div class="add-pro-box">
                    <div class="image_div">
                        <img :src="imgUrl" alt="" />
                    </div>

                    <div class="add-pro-content">
                        <p class="input-title">{{ $c("form.form.enterProID") }} *</p>
                        <div class="input-box">
                            <span class="prefix">#</span>
                            <input
                                type="text"
                                :placeholder="$c('single.sampleApplication.form.addPl')"
                                :class="{ error_input: errors.products_id }"
                                @keyup.enter="addProduct(product_sample.products_id, product_sample.products_num)"
                                @input.stop="focusInput('products_id', true, 'Sample Product Input')"
                                @blur="blurInput('products_id')"
                                v-model.trim="product_sample.products_id"
                                class="inp is_new" />
                        </div>
                        <validate-error v-if="screenWidth > 768" class="p_error" :error="errors.products_id"></validate-error>
                    </div>
                    <fs-button :text="$c('form.form.add')" type="red" @click="addProduct(product_sample.products_id, product_sample.products_num)" :loading="sbtn_pro_loading" htmlType="submit"></fs-button>
                </div>
                <validate-error v-if="screenWidth <= 768" class="p_error" :error="errors.products_id"></validate-error>
                <div class="product-list">
                    <template v-if="!isMobile">
                        <div class="product-item" v-for="(item, index) in product_list" :key="index">
                            <img :src="item.products_img" class="product-img" />
                            <div>
                                <div class="product-item_box">
                                    <div class="txt-box">
                                        <div class="tit">{{ item.products_name }}</div>
                                        <p class="txt">
                                            <span>#{{ item.product_ids }}</span>
                                        </p>
                                    </div>
                                    <div class="handle-box">
                                        <qty-box :isNewStyle="true" :num="item.product_nums" :attr="index" @change="qtyChange"></qty-box>
                                        <div class="del-box" @click="deletePro(index)">
                                            <i class="iconfont">&#xf30a;</i>
                                        </div>
                                    </div>
                                </div>
                                <template v-if="item.detailList && item.detailList.length > 0">
                                    <div class="detail">
                                        <div class="detail_title">
                                            <div class="detail_title_box" @click="toggle(index)">
                                                <span>This item includes the following products</span>
                                                <i class="iconfont">{{ item.isOpen ? "&#xe700;" : "&#xe704;" }}</i>
                                            </div>
                                        </div>
                                        <template v-if="item.isOpen">
                                            <div v-for="ele in item.detailList" :key="ele.id" class="detail_box">
                                                <img :src="ele.value_image" />
                                                <div class="detail_box_right">
                                                    <div>{{ ele.name || ele.value_name }}</div>
                                                    <div>
                                                        <span>{{ ele.related_qty }}</span>
                                                        <span>x</span>
                                                        <span v-html="ele.related_product_price_total"></span>
                                                    </div>
                                                </div>
                                            </div>
                                        </template>
                                    </div>
                                </template>
                            </div>
                        </div>
                    </template>
                    <template v-else>
                        <div class="product-item" v-for="(item, index) in product_list" :key="index">
                            <img :src="item.products_img" class="product-img" />
                            <div class="txt-box">
                                <div class="tit">{{ item.products_name }}</div>
                                <p class="txt">
                                    <span>#{{ item.product_ids }}</span>
                                </p>
                                <template v-if="item.detailList && item.detailList.length > 0">
                                    <div class="detail">
                                        <div class="detail_title" @click="toggle(index)">
                                            <span>This item includes the following products</span>
                                            <i class="iconfont">{{ item.isOpen ? "&#xe700;" : "&#xe704;" }}</i>
                                        </div>
                                        <template v-if="item.isOpen">
                                            <div v-for="ele in item.detailList" :key="ele.id" class="detail_box">
                                                <img :src="ele.value_image" />
                                                <div class="detail_box_right">
                                                    <div>{{ ele.name || ele.value_name }}</div>
                                                    <div>
                                                        <span>{{ ele.related_qty }}</span>
                                                        <span>x</span>
                                                        <span v-html="ele.related_product_price_total"></span>
                                                    </div>
                                                </div>
                                            </div>
                                        </template>
                                    </div>
                                </template>
                            </div>
                            <div class="handle-box">
                                <qty-box :isNewStyle="true" :num="item.product_nums" :attr="index" @change="qtyChange"></qty-box>
                                <div class="del-box" @click="deletePro(index)">
                                    <i class="iconfont">&#xf30a;</i>
                                </div>
                            </div>
                        </div>
                    </template>
                </div>
                <div class="product-box-line"></div>
            </div>
            <div class="sign" v-if="!isLogin">
                <i class="iconfont iconfFont"> &#xe718;</i>
                <div class="sign_content">
                    <span @click="toSign">Sign in</span>
                    <span> to automatically fill in your account information.</span>
                </div>
            </div>
            <div class="box-title">{{ $c("form.form.letUsContactYou") }}</div>
            <div class="form">
                <div class="sample-item" :class="{ 'sample-item-cn': isDifferentCountriesName }">
                    <div class="sample-item-title">
                        <span>{{ $c("form.form.first_name") }}{{ website === "cn" ? "*" : " *" }}</span>
                    </div>
                    <input
                        v-model.trim="form.first_name"
                        :class="{ error_input: errors.first_name }"
                        @input.stop="focusInput('first_name', false, 'First Name Input')"
                        @blur="blurInput('first_name')"
                        type="text"
                        class="inp is_new" />
                    <validate-error :error="errors.first_name"></validate-error>
                </div>
                <div class="sample-item">
                    <div class="sample-item-title">
                        <span>{{ $c("form.form.last_name") }}{{ website === "cn" ? "*" : " *" }}</span>
                    </div>
                    <input
                        v-model.trim="form.last_name"
                        :class="{ error_input: errors.last_name }"
                        @input.stop="focusInput('last_name', false, 'Last Name Input')"
                        @blur="blurInput('last_name')"
                        type="text"
                        class="inp is_new" />
                    <validate-error :error="errors.last_name"></validate-error>
                </div>
                <div class="sample-item">
                    <div class="sample-item-title">
                        <span>{{ $c("form.form.email") }} {{ website === "jp" ? "（必須）" : website === "cn" ? "" : " *" }}</span>
                    </div>
                    <input
                        type="text"
                        :disabled="disabled"
                        :class="{ error_input: errors.email }"
                        v-model.trim="form.email"
                        @input.stop="focusInput('email', false, 'Email Address Input')"
                        @blur="blurInput('email')"
                        class="inp is_new" />
                    <validate-error :error="errors.email.replace('XXXX', localePath({ name: 'login', query: { redirect: $route.fullPath } }))"></validate-error>
                </div>
                <div class="sample-item">
                    <div class="sample-item-title">
                        <span>{{ $c("form.form.phone_number") }}{{ website === "cn" ? "*" : " *" }}</span>
                    </div>
                    <tel-code
                        :isNewStyle="true"
                        code="1"
                        @changeCode="changeCode"
                        :error="errors.entry_telephone"
                        :phone="form.phone"
                        @change="telChange"
                        @input="telInput"
                        @point="handleBuried('Phone Number Input')"></tel-code>
                    <validate-error :error="errors.phone.replace('XXXX', localePath({ name: 'login', query: { redirect: $route.fullPath } }))"></validate-error>
                </div>
                <div class="sample-item">
                    <div class="sample-item-title">
                        <span>{{ $c(country_label) }}{{ website === "cn" ? "*" : " *" }}</span>
                    </div>
                    <select-Country :isNewStyle="true" position="absolute" @change="changeCountry" v-model.trim="form.countries_id" @click.native="handleBuried('Country/Region Drop-Down')"></select-Country>
                    <validate-error :error="errors.countries_id"></validate-error>
                </div>
                <div class="sample-item" v-if="isShowState">
                    <div class="sample-item-title">
                        <span>{{ $c("single.ProductReturnForm.state.tit") }}{{ website === "cn" ? "*" : "" }}</span>
                    </div>
                    <RegionSelect :isNewStyle="true" ref="regionSelect" />
                </div>
                <div class="sample-item sample-item-line" v-if="type === 3">
                    <div class="sample-item-title">
                        <span>{{ $c("pages.Products.installationPopup.ServiceRequirement") }}{{ website === "cn" ? "*" : " *" }}</span>
                    </div>
                    <div class="input-list">
                        <label class="input-item radio mobileFl" v-for="(r, index) in management_method" :key="index">
                            <input v-model="form.requirement" type="radio" :value="r.value" @click.stop="radioChange('requirement', r.label)" />
                            <span class="input-txt">{{ r.label }}</span>
                        </label>
                    </div>
                    <validate-error :error="errors.requirement"></validate-error>
                </div>
                <div class="sample-item sample-item-line" v-if="form.requirement === 3 && type === 3">
                    <input v-model.trim="form.requirement_info" type="text" />
                    <validate-error :error="errors.requirement_info"></validate-error>
                </div>
                <div class="sample-item line sample-item-comments">
                    <div class="tit-num">
                        <div class="sample-item-title">
                            <span>{{ $c("pages.Products.installationPopup.RequirementDescription") }}</span>
                        </div>
                        <div class="numb">
                            <span :class="{ active: form.description.length === 5000 }"> {{ form.description.length }} </span>
                            <span>/5000</span>
                        </div>
                    </div>
                    <textarea
                        v-model.trim="form.description"
                        :placeholder="$c('pages.Products.installationPopup.DescriptionPlaceholder')"
                        maxlength="5000"
                        class="textarea is_new"
                        @input.stop="focusInput('description', false, 'Comments(Optional) Input')"
                        @change="blurInput('comments')"></textarea>
                    <div class="input-item-number" :class="{ noFlex: errors.description }">
                        <validate-error :error="errors.description"></validate-error>
                    </div>
                </div>
                <div class="upload-main">
                    <div class="upload-main-title">{{ $c("pages.Products.installationPopup.AttachmentOptional") }}</div>
                    <upload-file
                        :isNewStyle="true"
                        :text="$c('pages.CaseDetail.upload_file')"
                        type="file"
                        ref="uploadFile"
                        :defaultFiles="form.network_file"
                        :maxSize="5 * 1024 * 1024"
                        :multiple="true"
                        :limit="5"
                        accept=".pdf,.doc,.docx,.xls,.xlsx,.txt,image/jpeg,image/jpg,image/png"
                        @change="handleChange"></upload-file>
                </div>
                <div class="sample-item line">
                    <div class="policy_box">
                        <input v-model="form.isAgreePolicy" @change="blurInput('isAgreePolicy')" type="checkbox" class="chk" />
                        <div
                            class="agreement_wrap"
                            @click.stop="clickLink($event)"
                            v-html="
                                $c('form.validate.aggree_policy_new')
                                    .replace('AAAA', localePath({ name: 'privacy-notice' }))
                                    .replace('BBBB', localePath({ name: 'terms-of-use' }))
                            "></div>
                    </div>
                    <validate-error :error="errors.isAgreePolicy"></validate-error>
                </div>
            </div>
        </div>
        <div class="sbtn-box sample-item line">
            <fs-button id="sample_submit" :text="$c('form.form.submit')" @click="submitFu" :loading="sbtn_loading" htmlType="submit"></fs-button>
        </div>
    </div>
</template>

<script>
import { mapState, mapGetters } from "vuex"
import { email_valdate, phone_validate, cn_mobile_tel } from "@/constants/validate.js"
import FsButton from "@/components/FsButton/FsButton.vue"
import FsSelect from "@/components/FsSelect/FsSelect.vue"
import ValidateError from "@/components/ValidateError/ValidateError.vue"
import TelCode from "@/components/TelCode/TelCode.vue"
import SelectCountry from "@/components/SelectCountry/SelectCountry.vue"
import QtyBox from "@/components/QtyBox/QtyBox.vue"
import FsPopup from "@/components/FsPopup/FsPopup.vue"
import RegionSelect from "@/components/RegionSelect/RegionSelect"
import UploadFile from "@/components/UploadFile/UploadFile.vue"
export default {
    name: "InstallationRequest",
    components: {
        FsButton,
        FsSelect,
        ValidateError,
        QtyBox,
        TelCode,
        SelectCountry,
        FsPopup,
        RegionSelect,
        UploadFile,
    },
    props: {
        productId: {
            type: [String, Number],
            default: "",
        },
        type: {
            Boolean: Number,
            default: 3,
        },
        serverCusList: {
            type: Array,
            default: () => [],
        },
        customize_serverList: {
            type: Array,
            default: () => [],
        },
        options: {
            type: Array,
            default: () => [],
        },
        support_integration_service: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {
            product_list: [],
            customizeList: [],
            sbtn_loading: false,
            sbtn_pro_loading: false,
            partsList: [],
            product_sample: {
                products_id: "",
                products_num: 1,
            },
            form: {
                first_name: "",
                last_name: "",
                email: "",
                phone: "",
                countries_id: "",
                description: "",
                requirement: "",
                requirement_info: "",
                network_file: [],
                isAgreePolicy: false,
            },
            errors: {
                products_id: "",
                first_name: "",
                last_name: "",
                email: "",
                phone: "",
                countries_id: "",
                requirement: "",
                requirement_info: "",
                description: "",
                isAgreePolicy: "",
            },
            paramsUrls: [],
            upload_file_name: [],
            disabled: false,
            code: "",
            loading: false,
            management_method: [
                {
                    value: 1,
                    label: this.$c("pages.Products.installationPopup.HardwareInstallation"),
                },
                {
                    value: 2,
                    label: this.$c("pages.Products.installationPopup.OSConfiguration"),
                },
                {
                    value: 3,
                    label: this.$c("pages.Products.installationPopup.Others"),
                },
            ],
        }
    },
    computed: {
        ...mapState({
            isMobile: (state) => state.device.isMobile,
            isLogin: (state) => state.userInfo.isLogin,
            userInfo: (state) => state.userInfo.userInfo,
            website: (state) => state.webSiteInfo.website,
            resourcePage: (state) => state.device.resource_page,
            country_id: (state) => state.webSiteInfo.countries_id,
            screenWidth: (state) => state.device.screenWidth,
        }),
        ...mapGetters({
            isCn: "webSiteInfo/isCn",
            isShowState: "selectCountry/isShowState",
            country_label: "selectCountry/country_label",
        }),
        // 不同国家的姓名前后表达顺序不一样
        isDifferentCountriesName() {
            return ["cn", "hk", "tw", "mo"].includes(this.website)
        },
        imgUrl() {
            return this.isCn ? "https://resource.fs.com/mall/generalImg/20230703110143tw3bnh.png" : "https://resource.fs.com/mall/generalImg/20230503154732odi7ou.png"
        },
    },
    created() {
        if (this.userInfo) {
            this.form.email = this.userInfo.customers_email_address
            this.form.first_name = this.userInfo.customers_firstname
            this.form.last_name = this.userInfo.customers_lastname
            this.disabled = true
            if (this.isCn) {
                this.disabled = false
            }
        }

        if (this.productId) {
            this.addProduct(this.productId, 1, true)
        }
    },
    mounted() {
        this.form.countries_id = this.country_id
        if (window.localStorage.getItem("onlineInstallation")) {
            const { product_list, form, paramsUrls, upload_file_name, type } = JSON.parse(window.localStorage.getItem("onlineInstallation"))
            ;[this.product_list, this.form] = [product_list, form]
            this.form.network_file = []
            if (type === 5) {
                for (let key in form) {
                    if (!form[key]) {
                        if (key === "email") {
                            this.form.email = this.userInfo.customers_email_address
                        }
                        if (key === "first_name") {
                            this.form.first_name = this.userInfo.customers_firstname
                        }
                        if (key === "last_name") {
                            this.form.last_name = this.userInfo.customers_lastname
                        }
                    }
                }
            }
            if (paramsUrls && paramsUrls.length > 0) {
                for (let i = 0; i < paramsUrls.length; i++) {
                    const fileName = upload_file_name[i]
                    const url = paramsUrls[i]
                    this.getFileFromLocalStorage(url, fileName)
                }
            }
        }
    },
    methods: {
        getFileFromLocalStorage(url, fileName) {
            if (url) {
                const xhr = new XMLHttpRequest()
                xhr.open("GET", url, true)
                xhr.responseType = "blob"
                xhr.onload = () => {
                    const blob = xhr.response
                    const file = new File([blob], fileName, { type: blob.type })
                    this.form.network_file.push(file)
                    this.$refs.uploadFile.setFiles(this.form.network_file)
                }
                xhr.send()
            } else {
                console.log("File URL not found in LocalStorage.")
            }
        },
        toggle(index) {
            this.$set(this.product_list[index], "isOpen", !this.product_list[index].isOpen)
        },
        changeCode(code) {
            this.code = code
        },
        handleChange(params) {
            this.form.network_file.splice(0, this.form.network_file.length, ...params.files)
            this.upload_file_name = params.files.map((item) => item.name)
        },
        radioChange(attr, label) {
            this.errors.requirement = ""
            this.$emit("gaEvent", "requirements_describe", `${label} Select Detail`)
        },
        handleBuried(label) {
            window.dataLayer &&
                window.dataLayer.push({
                    event: "uaEvent",
                    eventCategory: `productDetailPage_${this.productId}`,
                    eventAction: this.type === 5 ? "submit_integration" : "submit_installation",
                    eventLabel: label,
                    nonInteraction: false,
                })
        },
        addProduct(pid, num, isInit = false) {
            if (!pid) {
                this.errors.products_id = this.$c("form.form.errors.products_id_error")
                return
            }

            if (isInit) {
                this.loading = true
            } else {
                this.sbtn_pro_loading = true
            }
            let api = this.type === 3 ? "/api/addInstallProduct" : "/api/addIntegrationServiceProduct"
            this.$axios
                .post(api, {
                    products_id: pid,
                    products_num: num,
                })
                .then((res) => {
                    if (isInit) {
                        this.loading = false
                    } else {
                        this.sbtn_pro_loading = false
                    }
                    const { products_id: product_ids, products_name, products_img, customized_server } = res.data
                    const findIndex = this.product_list.findIndex((i) => i.product_ids === product_ids)
                    if (findIndex === -1) {
                        const productItem = {
                            product_ids,
                            products_name,
                            products_img,
                            product_nums: num,
                            isOpen: false,
                        }

                        this.product_list.push(productItem)
                    } else {
                        if (this.support_integration_service) {
                            let foundMatch = false
                            for (let i = 0; i < this.product_list.length; i++) {
                                const existingProduct = this.product_list[i]
                                if (existingProduct.detailList.length === customized_server.length && existingProduct.detailList.every((ele, idx) => ele.value_id === customized_server[idx].value_id)) {
                                    existingProduct.product_nums += num
                                    foundMatch = true
                                    break
                                }
                            }
                            if (!foundMatch) {
                                const productItem = {
                                    product_ids,
                                    products_name,
                                    products_img,
                                    product_nums: num,
                                    isOpen: false,
                                    detailList: customized_server,
                                }
                                this.product_list.push(productItem)
                            }
                            return
                        }
                        this.product_list[findIndex].product_nums += num
                    }
                    this.errors.products_id = ""
                    this.product_sample = {
                        products_id: "",
                        products_num: 1,
                    }
                    if (!isInit) {
                        this.handleBuried(`Add Success_${pid}`)
                    } else {
                        if (this.support_integration_service) {
                            if (this.customize_serverList.length > 0) {
                                let optionsList = this.options.map((item) => item.option_values).flat()
                                this.customizeList = optionsList
                                    .filter((item) => this.customize_serverList.some((ele) => ele.value_id === item.value_id))
                                    .map((item) => {
                                        return {
                                            ...item,
                                            name: item.option_value_info.value_name,
                                        }
                                    })
                            }
                            this.partsList = [...this.serverCusList].filter((item) => item.related_product_price_total)
                            this.product_list[0].detailList = this.customize_serverList.length === 0 ? this.partsList : this.customizeList
                            /**
                             * 配件信息名称相同 则进行累加
                             */
                            const result = []
                            for (const item of this.product_list[0].detailList) {
                                const relatedProductId = item.related_product_id
                                const relatedQty = item.related_qty
                                const existingItem = result.find((x) => x.related_product_id === relatedProductId)
                                if (existingItem) {
                                    existingItem.related_qty += relatedQty
                                } else {
                                    result.push(item)
                                }
                            }
                            this.product_list[0].detailList = result
                        }
                    }
                })
                .catch((err) => {
                    if (isInit) {
                        this.loading = false
                    } else {
                        this.sbtn_pro_loading = false
                        this.handleBuried(`Add Fail_${pid}`)
                    }
                    if (err.code == 400) {
                        this.errors.products_id = err.message
                    } else if (err.code == 422) {
                        this.errors.products_id = err.errors.products_id
                    }
                })
        },
        changeCountry(item) {
            this.form.countries_id = item.countries_id
        },
        telInput(inp) {
            this.telChange(inp)
        },
        telChange(inp) {
            this.form.phone = inp
            this.blurInput("phone")
        },
        qtyChange(num, index) {
            this.product_list[index].product_nums = num
        },
        deletePro(i) {
            this.product_list.splice(i, 1)
        },
        focusInput(attr, type, label) {
            // type ? (this[attr + "_error"] = "") : (this.errors[attr + "_error"] = "")
            // label && this.handleBuried(label)
            this.blurInput(attr)
        },
        blurInput(attr) {
            const val = this.form[attr] || ""
            const map = {
                products_id() {
                    if (this.product_list.length === 0) return this.$c("form.form.errors.products_id_error")
                },

                first_name() {
                    if (!val.replace(/^\s+|\s+$/g, "")) {
                        return this.$c("form.form.errors.entry_firstname_error")
                    } else if (val.length > 40) {
                        return this.$c("form.validate.first_name.first_name_max")
                    }
                },
                last_name() {
                    if (!val.replace(/^\s+|\s+$/g, "")) {
                        return this.$c("form.form.errors.entry_lastname_error")
                    } else if (val.length > 40) {
                        return this.$c("form.validate.last_name.last_name_max")
                    }
                },
                email() {
                    if (this.isCn) {
                        return ""
                    }
                    if (!val.replace(/^\s+|\s+$/g, "")) return this.$c("form.form.errors.email_business_error")
                    if (!email_valdate.test(val)) return this.$c("form.form.errors.email_address_error01")
                },
                phone() {
                    if (!val.replace(/^\s+|\s+$/g, "")) return this.$c("form.form.errors.entry_telephone_error")
                    if (this.isCn) {
                        if (!cn_mobile_tel.test(val)) return this.$c("form.form.errors.entry_telephone_error01")
                    } else {
                        if (val.length > 0 && val.length < 6) {
                            return this.$c("pages.NetTermsApplication.validate.phone.phone_min")
                        } else if (val.length > 40) {
                            return this.$c("pages.NetTermsApplication.validate.phone.phone_validate")
                        } else {
                            return ""
                        }
                    }
                },
                description() {
                    return ""
                },
                isAgreePolicy() {
                    return val ? "" : this.$c("form.form.errors.check2_error")
                },
            }
            this.errors[attr] = (map[attr] && map[attr].apply(this)) || ""
            if (this.type === 3) {
                if (!this.form.requirement.toString()) {
                    this.errors.requirement = this.$c("single.transceiverTestForm.errors.selectError")
                }
            }
            if (this.form.requirement === 3 && !this.form.requirement_info) {
                this.errors.requirement_info = this.website === "fr" ? "Ce champ est requis." : this.$c("single.TechnicalDocumentsFeedback.form.remarks")
            }
        },
        verifyWrapper() {
            const errorMap = Object.keys(this.errors)
            errorMap.forEach((key) => {
                this.blurInput(key)
            })
            return errorMap.some((attr) => this.errors[attr])
        },
        submitFu() {
            if (this.type === 5) {
                delete this.form.requirement
                delete this.form.requirement_info
                delete this.errors.requirement
                delete this.errors.requirement_info
            }
            if (this.sbtn_loading || this.verifyWrapper()) {
                return
            }

            if (!this.isLogin) {
                this.toSign()
                this.buriedPointWrapper("Not Logged Submit", this.type === 5 ? "submit_integration" : "submit_installation")
                return
            }
            this.sbtn_loading = true
            this.fetchProductsSubmit()
        },
        toSign() {
            if (this.form.network_file.length > 0) {
                for (let i = 0; i < this.form.network_file.length; i++) {
                    const file = this.form.network_file[i]
                    const blob = new Blob([file], { type: file.type })
                    const url = URL.createObjectURL(blob)
                    this.paramsUrls.push(url)
                }
            }
            let params = {
                form: this.form,
                product_list: this.product_list,
                paramsUrls: this.paramsUrls,
                upload_file_name: this.upload_file_name,
                type: this.type,
            }
            window.localStorage.setItem("onlineInstallation", JSON.stringify(params))
            this.$router.push(this.localePath({ name: "login", query: { redirect: this.$route.fullPath } }))
        },
        async fetchProductsSubmit() {
            try {
                const formData = new FormData()
                for (const key in this.form) {
                    if (key === "network_file") {
                        for (const file of this.form[key]) {
                            formData.append(`${key}[]`, file)
                        }
                    } else if (key === "phone") {
                        formData.append(key, `${this.code.replace("+", "")} ${this.form.phone}`)
                    } else {
                        formData.append(key, this.form[key])
                    }
                }
                const ids = this.product_list.map((i) => i.product_ids)
                const nums = this.product_list.map((i) => i.product_nums)
                let customize_serverList = []
                this.product_list.map((item) => {
                    let res =
                        item.detailList && item.detailList.length > 0
                            ? item.detailList.map((ele) => {
                                  return {
                                      option_id: ele.option_id,
                                      value_id: ele.value_id,
                                  }
                              })
                            : []
                    customize_serverList.push(res)
                })
                for (let i = 0; i < customize_serverList.length; i++) {
                    formData.append(`customize_server[]`, JSON.stringify(customize_serverList[i]))
                }
                for (let i = 0; i < ids.length; i++) {
                    formData.append("product_ids[]", ids[i])
                    formData.append("product_nums[]", nums[i])
                }
                if (this.$refs.regionSelect) {
                    formData.append("state", this.$refs.regionSelect.state || "")
                }
                let api = this.type === 3 ? "/api/productInstallApply" : "/api/integrationServiceSubmit "
                const res = await this.$axios.post(api, formData)

                if (res.code === 200 && res.status === "sensiWords") {
                    for (let key in res.errors) {
                        this.errors[key] = this.$c("form.form.errors.sensiWords")
                    }
                    this.sbtn_loading = false
                    return
                }

                this.buriedPointWrapper(`Submit Success_${res.data.case_number}`, this.type === 5 ? "submit_integration" : "submit_installation")
                this.$emit("submitSuccess", res.data.case_number)
                this.$refs.uploadFile.clear()
                this.sbtn_loading = false
            } catch (error) {
                this.sbtn_loading = false
                this.buriedPointWrapper(`Submit Fail_${res.data.case_number}`, this.type === 5 ? "submit_integration" : "submit_installation")
            }
            window.localStorage.removeItem("onlineInstallation")
        },
        buriedPointWrapper(eventLabel, eventAction) {
            if (window.dataLayer) {
                window.dataLayer.push({
                    event: "uaEvent",
                    eventCategory: `productDetailPage_${this.productId}`,
                    eventAction,
                    eventLabel,
                    nonInteraction: false,
                })
            }
        },
    },
}
</script>

<style lang="scss" scoped>
.sample-request {
    color: $textColor1;

    .sample_main {
        // padding: 20px 32px;
        padding: 16px 24px;
        max-height: calc(100vh - 282px);
        @include scrollY;
        .box-title {
            display: none;
            margin-bottom: 16px;
            @include font14();
            font-weight: 600;
            color: $textColor1;
        }
        .product-box {
            // margin-bottom: 24px;
            .add-pro-box {
                display: flex;
                align-items: flex-start;
                .image_div {
                    margin-top: 24px;
                    width: 68px;
                    height: 42px;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    img {
                        width: 80px;
                        height: auto;
                    }
                }
                .add-pro-content {
                    flex: 1;
                    margin: 0 16px;
                    .input-title {
                        @include font12();
                        margin-bottom: 4px;
                        color: $textColor3;
                    }
                    .input-box {
                        display: flex;
                        align-items: center;
                        background: $bgColor8;
                        border-radius: 4px;
                        .prefix {
                            // border-radius: 2px 0px 0px 2px;
                            top: 1px;
                            left: 1px;
                            background: $bgColor8;
                            width: 40px;
                            height: 42px;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            // border: 1px solid #e5e5e5;
                            // border-right: 0;
                            flex-shrink: 0;
                            @include font12();
                        }
                        // > input {
                        //     border-radius: 0 4px 4px 0;
                        //     border: none;
                        //     // border-left: 1px solid #e5e5e5;
                        //     background: #f6f6f8;
                        //     &:hover {
                        //         border-left: 1px solid #f6f6f8;
                        //         background: linear-gradient(0deg, rgba(25, 25, 26, 0.04), rgba(25, 25, 26, 0.04)), #f6f6f8;
                        //     }
                        //     &:focus {
                        //         border: 1px solid #19191a;
                        //         background: linear-gradient(0deg, rgba(25, 25, 26, 0.04), rgba(25, 25, 26, 0.04)), #f6f6f8;
                        //     }
                        // }
                    }
                }
                ::v-deep .fs-button {
                    margin-top: 24px;
                    height: 42px;
                    font-weight: 600;
                }
            }
            .product-list {
                // background: #fafafb;
                // padding: 0 24px;
                margin-top: 16px;
                .product-item {
                    display: flex;
                    // align-items: center;
                    padding: 20px 0;
                    &_box {
                        display: flex;
                    }
                    &:not(:first-child) {
                        border-top: 1px solid #e5e5e5;
                    }
                    img {
                        width: 68px;
                        height: 68px;
                        mix-blend-mode: multiply;
                    }
                    .txt-box {
                        flex: 1;
                        // margin: 0 16px 0 20px;
                        margin: 0 16px;
                        .tit {
                            @include font14();
                            color: #19191a;
                            @include txt-more-hid(2); /////
                        }
                        .txt {
                            @include font13();
                            color: #707070;
                            margin-top: 4px;
                        }
                    }

                    .handle-box {
                        display: flex;
                        align-items: center;
                        justify-content: space-between;

                        .del-box {
                            margin-left: 16px;
                            cursor: pointer;
                            padding: 4px;
                            border-radius: 3px;
                            display: flex;
                            .iconfont {
                                color: #707070;
                                width: 14px;
                                height: 14px;
                                font-size: 14px;
                            }
                            &:hover {
                                background: rgba(25, 25, 26, 0.04);
                                .iconfont {
                                    color: #19191a;
                                }
                            }
                        }
                    }
                    .detail {
                        margin: 12px 0 0 20px;
                        @media (max-width: 768px) {
                            margin: 0;
                        }
                        &_title {
                            .detail_title_box {
                                color: #0060bf;
                                @include font14;
                                cursor: pointer;
                                display: inline-flex;
                                &:hover {
                                    > span {
                                        text-decoration: underline;
                                    }
                                }
                                i {
                                    font-size: 12px;
                                    margin-left: 4px;
                                }
                            }
                            padding-bottom: 8px;
                            border-bottom: 1px solid #e5e5e5;
                        }
                        &_box {
                            display: flex;
                            margin: 8px 0;
                            img {
                                width: 60px;
                                height: 60px;
                            }
                            &_right {
                                display: flex;
                                flex-direction: column;
                                margin-left: 12px;
                                > div:first-child {
                                    @include font13;
                                    color: #707070;
                                    margin-bottom: 4px;
                                }
                                > div:last-child {
                                    @include font13;
                                    color: #707070;
                                }
                            }
                        }
                    }
                }
            }
            .product-box-line {
                border-bottom: 1px solid #e5e5e5;
                margin: 16px 0;
            }
        }
        .sign {
            display: flex;
            align-items: center;
            padding: 10px 16px;
            background: rgba(0, 96, 191, 0.04);
            @include font14;
            margin-bottom: 16px;
            &_content {
                :first-child {
                    color: #0060bf;
                    cursor: pointer;
                    &:hover {
                        text-decoration: underline;
                    }
                }
            }
        }
        .iconfFont {
            color: #0060bf;
            margin-right: 8px;
        }

        .form {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            grid-gap: 16px 20px;
            .sample-item {
                &.line {
                    grid-column: span 2;
                }
                &.protocol {
                    margin-top: -4px;
                }
                &.sample-item-cn {
                    grid-area: 1/2/1/3;
                }
                .tit-num {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    .numb {
                        display: flex;
                        color: $textColor3;
                        @include font12;
                        justify-content: flex-end;
                        .active {
                            color: $textColor4;
                        }
                    }
                }
                .sample-item-title {
                    color: $textColor3;
                    @include font12();
                    margin-bottom: 4px;
                }
                .mobileFl {
                    @media (max-width: 414px) {
                        align-items: baseline !important;
                    }
                }

                // ::v-deep .select-country {
                //     .select-country-active {
                //         background: #f6f6f8;
                //         border-radius: 4px;
                //         border: none;
                //         &:hover {
                //             background: linear-gradient(0deg, rgba(25, 25, 26, 0.04), rgba(25, 25, 26, 0.04)), #f6f6f8;
                //         }
                //         .iconfont {
                //             // width: 12px;
                //             // height: 12px;
                //             font-size: 12px;
                //         }
                //     }
                // }
                // ::v-deep .dropdown {
                //     .select-label {
                //         background: #f6f6f8;
                //         border-radius: 4px;
                //         border: none;
                //         &:hover {
                //             background: linear-gradient(0deg, rgba(25, 25, 26, 0.04), rgba(25, 25, 26, 0.04)), #f6f6f8;
                //         }
                //         .iconfont {
                //             // width: 12px;
                //             // height: 12px;
                //             font-size: 12px;
                //         }
                //     }
                // }
                .input-item-number {
                    // margin-top: 4px;
                }

                .agreement {
                    width: 100%;
                    display: flex;
                    align-items: flex-start;
                    input {
                        // font-size: 16px;
                        margin: 0 8px 0 0;
                        @media (max-width: 960px) {
                            margin-top: 1px;
                        }
                    }
                    > p {
                        @include font12;
                        color: $textColor3;
                        a {
                            color: #0060bf;
                        }
                    }
                    &:hover {
                        cursor: pointer;
                        input[type="checkbox"] {
                            &:before {
                                color: #707070;
                            }
                        }
                    }
                }
                .policy_box {
                    display: flex;
                    .chk {
                        margin-top: 4px;
                        margin-right: 8px;
                        width: 14px;
                        height: 14px;
                        font-size: 14px;
                    }
                    .agreement_wrap {
                        @include font14;
                        color: $textColor3;
                    }
                }
            }
            .sample-item-comments {
                .sample-item-title {
                    margin-bottom: 4px;
                }
                // .textarea {
                //     background: #f6f6f8;
                //     border-radius: 4px;
                //     border: none;
                //     &:hover {
                //         background: linear-gradient(0deg, rgba(25, 25, 26, 0.04), rgba(25, 25, 26, 0.04)), #f6f6f8;
                //     }
                //     &:focus {
                //         border: 1px solid #19191a;
                //         background: linear-gradient(0deg, rgba(25, 25, 26, 0.04), rgba(25, 25, 26, 0.04)), #f6f6f8;
                //     }
                // }
            }
            .sample-item-line {
                grid-column: 1 / -1;
                .input-item {
                    cursor: pointer;
                }
                .input-txt {
                    @include font13;
                    margin-right: 48px;
                }
                .sample-item-title {
                    margin-bottom: 8px;
                }
            }
            .input-list {
                display: flex;
                .radio {
                    display: flex;
                    align-items: center;
                    margin-right: 5px;
                    input[type="radio"] {
                        font-size: 14px;
                        height: 14px;
                        width: 14px;
                        margin-right: 8px;
                    }
                    .input-txt {
                        @include font13;
                        margin-right: 55px;
                    }
                }
                @media (max-width: 768px) {
                    display: grid;
                    grid-template-columns: repeat(2, 1fr);
                    grid-row-gap: 8px;
                    .input-txt {
                        margin-right: 0;
                    }
                }
            }
            .upload-main {
                // margin-top: -8px;
                .upload-main-title {
                    color: #707070;
                    @include font12;
                    margin-bottom: 12px;
                }
            }
        }
    }
    .sbtn-box {
        display: flex;
        justify-content: flex-end;
        padding: 4px 24px 24px 24px;
        background: #fff;
        // border-top: 1px solid #e5e5e5;
        :deep(.fs-button) {
            height: 36px;
            padding: 10px 16px;
            font-weight: normal;
        }
    }

    /* .error_input {
        @include errorInput;
    } */
    ::v-deep .validate_error {
        .error_info {
            @include font12;
        }
        .error_box .iconfont {
            margin-top: 3px;
        }
    }
    @media (max-width: 960px) {
        .sample_main {
            // max-height: calc(100vh - 138px);
            // height: 100vh;
        }
    }
    @include mediaM {
        .sample_main {
            // max-height: calc(100vh - 132px);
            padding: 20px 16px;
            .product-box {
                .add-pro-box {
                    // margin-top: 20px;
                    align-items: flex-end;
                    > img {
                        display: none;
                    }
                    .add-pro-content {
                        margin: 0 10px 0 0;
                    }
                    ::v-deep .fs-button {
                        margin-top: 0;
                        // width: 100%;
                    }
                }
                .product-list {
                    // padding: 0 20px;
                    .product-item {
                        flex-wrap: wrap;
                        .txt-box {
                            margin-right: 0;
                        }
                        .handle-box {
                            flex: 0 0 100%;
                            padding-left: 84px;
                            margin-top: 16px;
                        }
                    }
                }
            }
            .form {
                .sample-item {
                    grid-column: span 2;
                }
            }
        }
        .sbtn-box {
            padding: 0px 16px 20px 16px;
            ::v-deep .fs-button {
                width: 100%;
            }
        }
    }
    @media (max-width: 768px) {
        .sbtn-box {
            position: sticky;
            bottom: 0;
        }
    }
}
</style>
