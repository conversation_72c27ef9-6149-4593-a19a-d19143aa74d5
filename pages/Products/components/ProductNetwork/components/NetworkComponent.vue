<template>
    <div class="network_component" v-if="componentInstance">
        <!-- 导航 -->
        <div class="network_nav_box" :class="{ optionAll: showIndex && showIndex != 0 }" ref="network">
            <div class="nav_box" :class="{ only: showIndex === 0, defOne: data?.data?.scroll_title.length === 1 }">
                <div
                    class="nav_item"
                    :class="{ active: index === activeIndex && (!showOptions || !isMobile), no_show: index >= showIndex && showIndex != null && showAll }"
                    v-for="(item, index) in data?.data?.scroll_title"
                    :key="index"
                    @click="handleClick(index, 1)">
                    {{ item }}
                </div>
            </div>

            <div v-if="showAll" class="nav_box pc_show" ref="allBox" @mouseover="showOptions = true" @mouseleave="showOptions = false">
                <div class="nav_item_all" :class="{ active: showOptions || all_text_default, active_line: all_text_default }">
                    <span>{{ `All (${data?.data?.scroll_title.length - showIndex})` }}</span>
                    <!-- <span v-if="!all_text_default">{{ `All (${data?.data?.scroll_title.length - showIndex})` }}</span>
                    <span v-else>{{ all_text }}</span> -->
                    <i class="iconfont icon-arrow-down" :class="{ transform: showOptions }">&#xe704;</i>
                </div>
                <slide-down>
                    <div class="show_all_box" v-show="showOptions" ref="options">
                        <div class="show_all_list">
                            <div
                                class="show_all_item"
                                :class="{ active: index === activeIndex, no_show: index < showIndex }"
                                v-for="(item, index) in data?.data?.scroll_title"
                                :key="index"
                                @click="handleClick(index, 2, item)">
                                <i class="iconfont icon-checked" v-if="index === activeIndex">&#xf050;</i>
                                <i class="iconfont icon-check" v-else>&#xf051;</i>
                                <span>{{ item }}</span>
                            </div>
                        </div>
                    </div>
                </slide-down>
            </div>
            <div v-if="showAll" class="nav_box m_show" ref="allBox" @click=";(showOptions = true), (activeIndexMobile = activeIndex)">
                <div class="nav_item_all" :class="{ active: showOptions || all_text_default, active_line: all_text_default }">
                    <span>{{ `All (${data?.data?.scroll_title.length - showIndex})` }}</span>
                    <!-- <span v-if="!all_text_default">{{ `All (${data?.data?.scroll_title.length - showIndex})` }}</span>
                    <span v-else>{{ all_text }}</span> -->
                    <i class="iconfont icon-arrow-down">&#xe704;</i>
                </div>
                <slide-down>
                    <div class="show_all_box" v-show="showOptions" ref="options">
                        <div class="show_all_list">
                            <div
                                class="show_all_item"
                                :class="{ active: index === activeIndex, no_show: index < showIndex }"
                                v-for="(item, index) in data?.data?.scroll_title"
                                :key="index"
                                @click="handleClick(index, 2, item)">
                                <i class="iconfont icon-checked" v-if="index === activeIndex">&#xf050;</i>
                                <i class="iconfont icon-check" v-else>&#xf051;</i>
                                <span>{{ item }}</span>
                            </div>
                        </div>
                    </div>
                </slide-down>
            </div>
        </div>
        <!-- 内容 -->
        <div class="network_tree_box">
            <div class="network-tree" v-for="(item, index) in data?.data?.data" :key="index">
                <template v-if="index === activeIndex">
                    <!-- <fs-tag-new :tag="item" :showTagTitle="false"></fs-tag-new> -->
                    <!--m端添加产品列表 pc和pad展示tag图 -->
                    <div class="tree_pc">
                        <fs-tag-new :tag="item" :showTagTitle="false" :source="'netWorkTree'"></fs-tag-new>
                        <div class="scene_id_popover">
                            <fs-popover isAccessible :position="'top'" trigger="click" :transfer="false" :closeIcon="false" :icon="false" :offset="0">
                                <div class="title" slot="trigger">
                                    <div class="scene_id" @click="handleSceneIdClick(item)">
                                        <!-- <a> {{ item.tagPrefix }}</a> -->
                                        <a v-html="`${$c('pages.List.Level3List.comboId').replace('XXXX', item.scene_id)}`"></a>
                                        <span class="iconfont">&#xe701;</span>
                                    </div>
                                </div>
                                <div class="scene_id_popover_content">
                                    <div class="scene_id_popover_content_head">
                                        <span class="iconfont">&#xf052;</span>
                                        <span v-html="`${$c('pages.List.Level3List.copy')}`"></span>
                                    </div>
                                    <div class="scene_id_popover_content_body">
                                        <span v-html="`${$c('pages.List.Level3List.copyTips').replace('XXXX', localePath({ path: '/solution-services.html', query: { redirect: $route.fullPath } }))}`"></span>
                                    </div>
                                </div>
                            </fs-popover>
                        </div>
                    </div>
                    <products-tree-mobile class="tree_mobile" :productData="item"></products-tree-mobile>
                </template>
            </div>
        </div>

        <fs-popup-new class="mobile_popup" :show="showOptions" :autoContent="true" @close=";(showOptions = false), (activeIndexMobile = 0)" :transition="isMobile ? 'fade' : 'slide-up'" :isMDrawer="true">
            <div class="top">
                <span>{{ title }}</span>
                <i @click="showOptions = false" class="iconfont icon-close">&#xf30a;</i>
            </div>
            <div class="center">
                <div
                    class="center_item"
                    :class="{ active: index === activeIndexMobile, no_show: index < showIndex }"
                    v-for="(item, index) in data?.data?.scroll_title"
                    :key="index"
                    @click="mobileHandleClick(index, item)">
                    <i class="iconfont icon-checked" v-if="index === activeIndexMobile">&#xf050;</i>
                    <i class="iconfont icon-check" v-else>&#xf051;</i>
                    <span>{{ item }}</span>
                </div>
            </div>
            <div class="bottom">
                <fs-button type="grayline" @click="clear">{{ $c("pages.List.Level3List.clean") }}</fs-button>
                <fs-button type="red" @click="submit">{{ $c("pages.List.Level3List.submit") }}</fs-button>
            </div>
        </fs-popup-new>
        <!-- <component :is="componentInstance" :data="data.data"></component> -->
    </div>
</template>

<script>
const Types = {
    products_tree_01: "./ProductsTree01/ProductsTree01.vue",
    products_tree_02: "./ProductsTree02/ProductsTree02.vue",
}
import { mapState } from "vuex"
import ProductsTreeMobile from "./ProductsTreeMobile/ProductsTreeMobile.vue"
import FsTagNew from "@/components/FsTag/FsTagNew.vue"
import SlideDown from "@/components/SlideDown/SlideDown.vue"
import FsPopupNew from "@/components/FsPopupNew/FsPopupNew.vue"
import FsButton from "@/components/FsButton/FsButton"
import FsPopover from "@/components/FsPopover/index"
import { copyText } from "@/util/util"
export default {
    name: "NetworkComponent",
    props: {
        data: {
            type: Object,
            default() {
                return {}
            },
        },
        title: {
            type: String,
            default: "",
        },
    },
    data() {
        return {
            showAll: false,
            showIndex: null,
            activeIndex: 0,
            activeIndexMobile: 0,
            showOptions: false,
            all_text: "",
            all_text_mobile: "",
            all_text_default: false,
        }
    },
    components: {
        FsTagNew,
        SlideDown,
        FsPopupNew,
        FsButton,
        ProductsTreeMobile,
        FsPopover,
    },
    computed: {
        componentInstance() {
            const path = Types[this.data.block]
            if (path) {
                return () => import(`${path}`)
            } else {
                return ""
            }
        },
        ...mapState({
            isMobile: (state) => state.device.isMobile,
        }),
    },
    mounted() {
        if (this.data?.data && this.data?.data?.scroll_title?.length) {
            this.$nextTick(() => {
                if (typeof document !== "undefined" && this.$refs.network) {
                    // 添加安全检查
                    const networkRef = this.$refs.network
                    if (!networkRef) {
                        console.warn("Network ref not found")
                        return
                    }

                    const navItems = document.querySelectorAll(".nav_item")
                    if (!navItems || navItems.length === 0) {
                        console.warn("Nav items not found")
                        return
                    }

                    let sum = 0
                    const containerWidth = networkRef.clientWidth
                    const maxWidth = this.isMobile ? 271 : containerWidth
                    const allButtonWidth = 104

                    for (let i = 0; i < navItems.length; i++) {
                        // 添加元素存在性检查
                        if (!navItems[i]) continue

                        const currentItemWidth = navItems[i].clientWidth || 0

                        // 添加数据安全检查
                        const scrollTitleLength = this.data?.data?.scroll_title?.length || 0
                        if (i >= scrollTitleLength) break

                        if (i === navItems.length - 1) {
                            sum += currentItemWidth
                        } else {
                            sum += currentItemWidth + 4 // 4px间距
                        }

                        // 检查是否超出容器宽度
                        if (sum + allButtonWidth > maxWidth) {
                            // M端特殊处理：确保至少显示一个nav_item
                            if (this.isMobile && i === 0) {
                                // 如果第一个item就超宽，强制显示第一个，隐藏其余
                                this.showIndex = 1
                                this.showAll = navItems.length > 1
                                return
                            }

                            // 正常情况：从第i个开始隐藏
                            const remainingCount = scrollTitleLength - i
                            if (remainingCount > 0) {
                                this.showAll = true
                                this.showIndex = Math.max(this.isMobile ? 1 : 0, i) // M端确保至少显示一个
                            } else {
                                this.showAll = false
                                this.showIndex = i
                            }
                            return
                        } else {
                            this.showIndex = i + 1
                        }
                    }

                    // 如果循环结束都没有超出宽度，说明所有元素都可以显示
                    this.showAll = false
                    this.showIndex = navItems.length
                }
            })
        }
    },
    methods: {
        handleClick(index, type, text) {
            this.activeIndex = index
            if (type === 1) {
                this.all_text = ""
                this.all_text_default = false
            } else if (type === 2) {
                this.all_text = text
                this.all_text_default = true
            }
            this.showOptions = false
            // 添加环境检查
            if (typeof window !== "undefined" && window.dataLayer) {
                window.dataLayer.push({
                    event: "uaEvent",
                    eventCategory: `productDetailPage_${this.$route.params.id || ``}`,
                    eventAction: "product_tree_tag",
                    eventLabel: `Switch_${this.data?.data?.scroll_title[index]}`,
                    nonInteraction: false,
                })
            }
        },
        mobileHandleClick(index, item) {
            this.activeIndexMobile = index
            this.all_text_mobile = item
        },
        submit() {
            this.activeIndex = this.activeIndexMobile
            this.all_text = this.all_text_mobile
            this.showOptions = false
            this.all_text_default = true
        },
        clear() {
            this.showOptions = false
        },
        handleSceneIdClick(item) {
            if (item && item.tagPrefix) {
                copyText(item.tagPrefix)
            }
        },
    },
}
</script>

<style lang="scss" scoped>
.network_component {
    padding-bottom: 40px;
    @media (max-width: 960px) {
        padding: 16px 0 24px;
    }
}
.network_nav_box {
    opacity: 1;
    display: flex;
    justify-content: center;
    &.optionAll {
        opacity: 1;
    }
    .nav_box {
        position: relative;
        border-radius: 36px;
        background: #f6f6f8;
        padding: 6px;
        display: flex;
        width: max-content;
        .nav_item,
        .nav_item_all {
            width: max-content;
            cursor: pointer;
            padding: 4px 16px;
            border-radius: 28px;
            @include font12;
            color: $textColor3;
        }
        .nav_item {
            margin-left: 4px;
            position: relative;
            @include font14;
            &:first-child {
                margin-left: 0;
            }
            &.active {
                color: $textColor1;
                background-color: #fff;
                &::after {
                    @include rowLine;
                    opacity: 1;
                    width: calc(100% - 32px);
                    left: 16px;
                    @media (max-width: 768px) {
                        width: calc(100% - 16px);
                        left: 8px;
                    }
                }
            }
            &.no_show {
                display: none;
            }
            &:hover {
                color: $textColor1;
                background-color: #fff;
            }
            @media (max-width: 768px) {
                @include font12;
            }
        }
        .nav_item_all {
            display: flex;
            align-items: center;
            margin-right: 0;
            max-width: 92px;
            position: relative;
            > span {
                @include font14;
                color: $textColor3;
                text-overflow: ellipsis;
                overflow: hidden;
                white-space: nowrap;
                @media (max-width: 768px) {
                    @include font12;
                }
            }
            > i {
                font-size: 14px;
                line-height: 1;
                color: $textColor3;
                margin-left: 8px;
                transition: all 0.3s;
                &.transform {
                    transform: rotateX(-180deg);
                    @media (max-width: 960px) {
                        transform: initial;
                    }
                }

                @media (max-width: 768px) {
                    font-size: 12px;
                }
            }
            &.active {
                background-color: #fff;
                > span {
                    color: $textColor1;
                }
                > i {
                    color: $textColor1;
                }
            }
            &.active_line {
                &::after {
                    @include rowLine;
                    opacity: 1;
                    width: calc(100% - 32px);
                    left: 16px;
                }
            }
        }
        margin-right: 12px;
        &:last-child {
            margin-right: 0;
        }

        .show_all_box {
            position: absolute;
            top: 38px;
            right: 0;
            padding-top: 8px;
            z-index: 1;
            .show_all_list {
                padding: 7px 0;
                border-radius: 8px;
                border: 1px solid #e5e5e5;
                background-color: #fff;
                .show_all_item {
                    cursor: pointer;
                    padding: 7px 12px;
                    display: flex;
                    align-items: center;
                    > i {
                        font-size: 18px;
                        line-height: 1;
                        height: 18px;
                        margin-right: 8px;
                        color: rgba(25, 25, 26, 0.3);
                    }
                    > span {
                        @include font14;
                        color: $textColor3;
                        width: max-content;
                        @media (max-width: 768px) {
                            @include font12;
                        }
                    }

                    &.active {
                        background: #f7f7f7;
                        > i {
                            color: #707070;
                        }
                        > span {
                            color: #19191a;
                        }
                    }
                    &:hover {
                        background: #f7f7f7;
                        > i {
                            color: #707070;
                        }
                    }
                    &.no_show {
                        display: none;
                    }
                }
            }
            @media (max-width: 768px) {
                display: none;
            }
        }
        &.pc_show {
            margin-right: 0;
            @media (max-width: 960px) {
                display: none;
            }
        }
        &.m_show {
            display: none;
            @media (max-width: 960px) {
                display: block;
            }
        }
        &.only {
            padding: 0;
            background-color: $bgColor3;
            .nav_item {
                padding: 0;
                cursor: default;
                font-weight: 600;
                &::after {
                    display: none;
                }
            }
        }
        &.defOne {
            padding: 0;
            background-color: $bgColor3;
            .nav_item {
                padding: 0;
                cursor: default;
                font-weight: 600;
                @include font16;
                &::after {
                    display: none;
                }
            }
        }
        @media (max-width: 768px) {
            padding: 4px;
            .nav_item {
                padding: 4px 8px;
                > span {
                    display: block;
                    max-width: 271px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                }
            }
            .nav_item_all {
                padding: 4px 8px;
                min-width: 67px;
            }
        }
    }
}
.network_tree_box {
    margin-top: 28px;
    .tree_pc {
        .scene_id {
            margin-top: 12px;
            color: $textColor3;
            text-align: right;
            font-size: 13px;
            width: 175px;
            &:hover {
                a {
                    color: #19191a;
                    text-decoration: underline;
                }

                .iconfont {
                    color: #19191a;
                }
            }
            a {
                color: $textColor3;
                line-height: 20px;
            }
            .iconfont {
                font-size: 16px;
                color: $textColor3;
            }
        }
        .scene_id_popover {
            text-align: right;
            // padding-right: 40px;

            .scene_id_popover_content {
                text-align: left;
                .scene_id_popover_content_head {
                    span {
                        font-size: 14px;
                        line-height: 22px;
                        color: #19191a;
                    }
                    .iconfont {
                        color: #10a300;
                        font-size: 12px;
                        margin-right: 8px;
                    }
                }
                .scene_id_popover_content_body {
                    color: #707070;
                    font-size: 13px;
                }
            }
        }
    }

    .tree_mobile {
        display: none;
    }
    @media (max-width: 768px) {
        .tree_pc {
            display: none;
        }
        .tree_mobile {
            display: block;
        }
    }
}
.mobile_popup {
    display: none;
    @media (max-width: 960px) {
        display: block;
    }
    .top {
        padding: 12px 16px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        border-bottom: 1px solid #e5e5e5;
        > span {
            @include font16;
            color: $textColor1;
        }
        > i {
            font-size: 16px;
            line-height: 1;
            color: $textColor3;
        }
    }
    .center {
        padding: 8px 0;
        .center_item {
            padding: 8px 16px;
            display: flex;
            align-items: center;
            > i {
                font-size: 18px;
                line-height: 1;
                height: 18px;
                margin-right: 8px;
                color: #ccc;
            }
            > span {
                @include font14;
                color: $textColor3;
                width: max-content;
            }
            &.active {
                background-color: #f7f7f7;
                > i {
                    color: #707070;
                }
                > span {
                    color: $textColor1;
                }
            }
            &.no_show {
                display: none;
            }
        }
    }
    .bottom {
        padding: 16px;
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        grid-gap: 16px;
        border-top: 1px solid #e5e5e5;
    }
}
</style>
