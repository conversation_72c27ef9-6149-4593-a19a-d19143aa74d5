<template>
    <div class="sample-request">
        <div class="sample_main">
            <div class="sign" v-if="!isLogin && pageType === 'productDetailPage'">
                <i class="iconfont iconfFont"> &#xe718;</i>
                <div class="sign_content">
                    <span @click="toSign">{{ $c("single.PicosRequest.singin") }}</span>
                    <span class="signTip">{{ $c("single.PicosRequest.singintip") }}</span>
                </div>
            </div>
            <div class="form">
                <div class="sample-item product-type">
                    <div class="sample-item-title">
                        <span>{{ $c("single.ModifyRequest.common.chooseProduct") }}</span>
                    </div>
                    <fs-select :options="product_type_options" :isNewStyle="true" v-model="form.product_type"></fs-select>
                </div>
                <div class="sample-item">
                    <div class="sample-item-title">
                        <span>{{ $c("form.form.first_name") }}</span>
                    </div>
                    <input
                        v-model.trim="form.entry_firstname"
                        :class="{ error_input: errors.entry_firstname }"
                        @focus.stop="focusInput('entry_firstname')"
                        @input="blurInput('entry_firstname')"
                        @blur="blurCheck('entry_firstname')"
                        type="text"
                        class="inp is_new" />
                    <validate-error :error="errors.entry_firstname"></validate-error>
                </div>
                <div class="sample-item">
                    <div class="sample-item-title">
                        <span>{{ $c("form.form.last_name") }}</span>
                    </div>
                    <input
                        v-model.trim="form.entry_lastname"
                        :class="{ error_input: errors.entry_lastname }"
                        @input.stop="focusInput('entry_lastname')"
                        @blur="blurInput('entry_lastname')"
                        type="text"
                        class="inp is_new" />
                    <validate-error :error="errors.entry_lastname"></validate-error>
                </div>
                <div class="sample-item">
                    <div class="sample-item-title">
                        <span>{{ $c("form.form.email_ddress") }}</span>
                    </div>
                    <input type="text" :class="{ error_input: errors.email_address }" v-model.trim="form.email_address" @input.stop="focusInput('email_address')" @blur="blurInput('email_address')" class="inp is_new" />
                    <validate-error :error="errors.email_address.replace('XXXX', localePath({ name: 'login', query: { redirect: $route.fullPath } }))"></validate-error>
                </div>
                <div class="sample-item">
                    <div class="sample-item-title">
                        <span>{{ $c("single.PicosRequest.hardware.phone") }}</span>
                    </div>
                    <tel-code :isNewStyle="true" code="1" @changeCode="changeCode" :error="errors.entry_telephone" :phone="form.entry_telephone" @change="telChange" @input="telInput"></tel-code>
                    <validate-error :error="errors.entry_telephone.replace('XXXX', localePath({ name: 'login', query: { redirect: $route.fullPath } }))"></validate-error>
                </div>
                <div class="sample-item">
                    <div class="sample-item-title">
                        <span>{{ $c("form.form.country") }}</span>
                    </div>
                    <select-country :isNewStyle="true" position="absolute" v-model.trim="form.country_code"></select-country>
                    <validate-error :error="errors.country_code_error"></validate-error>
                </div>
                <div class="sample-item">
                    <div class="sample-item-title">
                        <span>{{ $c("single.PicosRequest.hardware.company") }}</span>
                    </div>
                    <input v-model.trim="form.company" :class="{ error_input: errors.company }" @input="blurInput('company')" @blur="blurCheck('company')" type="text" class="inp is_new" />
                    <validate-error :error="errors.company"></validate-error>
                </div>
            </div>
            <!-- 动态表单 -->
            <div class="dynamics-form">
                <div class="sample-item line" v-if="form.product_type !== 4">
                    <div class="sample-item-title">
                        <span>{{ $c("single.ModifyRequest.common.requestType") }}</span>
                    </div>
                    <div class="switch_wrap">
                        <div class="btn_item" @click="switchRequestType(item)" :class="{ active: item.value === activeRequestType }" v-for="item in request_type_btns" :key="item.value">
                            {{ item.name }}
                        </div>
                    </div>
                </div>
                <!-- 动态组件 -->
                <dynamic-form-factory :productType="form.product_type" :requestType="activeRequestType" @form-change="handleDynamicFormChange" ref="dynamicForm"> </dynamic-form-factory>
            </div>
            <div class="upload">
                <upload-file
                    accept=".pdf,image/jpg,image/png"
                    :isNewStyle="true"
                    :text="$c('single.SolutionDesign.public.upload[3]')"
                    :maxSize="5 * 1024 * 1024"
                    :limit="5"
                    :multiple="true"
                    @change="handleChange"
                    v-model="form.file"
                    name="po_files">
                    <fs-popover class="upload-tip" slot="tip">
                        <p v-html="$c('pages.OrderDetail.pop.allow')"></p>
                    </fs-popover>
                </upload-file>
            </div>
            <PolicyCheck class="agreement-item" v-model="form.check2" @change="blurInput('check2')" :error="errors.check2" />
        </div>
        <div class="sbtn-box sample-item line" :class="{ 'page-style': pageType === 'Customer Service_FS Modify' }">
            <!-- <FsButton v-show="!isMobile" id="modify_cancel" @click="$emit('close')" class="cancel_btn" type="white">{{ $c("pages.ShoppingCart.Cancel") }}</FsButton> -->
            <fs-button id="modify_submit" :text="$c('form.form.submit')" @click="submitFu" :loading="sbtn_loading" htmlType="submit"></fs-button>
        </div>
    </div>
</template>

<script>
import { mapState, mapGetters } from "vuex"
import { email_valdate, phone_validate, cn_all_phone } from "@/constants/validate.js"
import FsButton from "@/components/FsButton/FsButton.vue"
import FsSelect from "@/components/FsSelect/FsSelect.vue"
import ValidateError from "@/components/ValidateError/ValidateError.vue"
import TelCode from "@/components/TelCode/TelCode.vue"
import SelectCountry from "@/components/SelectCountry/SelectCountry.vue"
import UploadFile from "@/components/UploadFile/UploadFile.vue"
import FsPopover from "@/components/FsPopover"
import DynamicFormFactory from "./DynamicFormFactory.vue"
import { MOBILE_WIDTH } from "@/constants/common"
import PolicyCheck from "@/components/PolicyCheck/PolicyCheck.vue"

export default {
    name: "FsModifyRequest",
    components: {
        FsButton,
        FsSelect,
        ValidateError,
        TelCode,
        SelectCountry,
        UploadFile,
        FsPopover,
        DynamicFormFactory,
        PolicyCheck,
    },
    provide() {
        return {
            formConfig: {
                applicationScenario: this.application_scenario,
                productType: this.form.product_type,
                requestType: this.activeRequestType,
                pageType: this.pageType,
                productId: this.productId,
            },
        }
    },
    props: {
        pageType: {
            type: String,
            default: "",
        },
        productId: {
            type: [String, Number],
            default: "",
        },
        product_type: {
            type: [String, Number],
            default: "",
        },
        products_categories_id: {
            type: [String, Number],
            default: "",
        },
        application_scenario: {
            type: [String, Number],
            default: null,
        },
    },
    data() {
        return {
            isMobile: false,
            sbtn_loading: false,
            code: "+1",
            upload_file_name: [],
            select_options: ["PicOS® Data Center Switches", "PicOS® Enterprise Switches", "AmpCon Management Platform", "Optical Transceivers & Cables"],
            select_form: "",
            product_type_options: [
                {
                    value: 1,
                    name: this.$c("single.ModifyRequest.common.product_type_options[0].name"),
                },
                {
                    value: 2,
                    name: this.$c("single.ModifyRequest.common.product_type_options[1].name"),
                },
                {
                    value: 3,
                    name: this.$c("single.ModifyRequest.common.product_type_options[2].name"),
                },
                {
                    value: 4,
                    name: this.$c("single.ModifyRequest.common.product_type_options[3].name"),
                },
            ],
            request_type_btns: [
                {
                    name: this.$c("single.ModifyRequest.common.request_type_btns[0].name"),
                    value: 1,
                },
                {
                    name: this.$c("single.ModifyRequest.common.request_type_btns[1].name"),
                    value: 2,
                },
            ],
            activeRequestType: 1,
            form: {
                product_type: Number(this.product_type),
                entry_firstname: "",
                entry_lastname: "",
                email_address: "",
                company: "",
                entry_telephone: "",
                country_code: "us",
                countries_id: this.select_country_id,
                description: "",
                files: [],
                check2: false,
                request_type: 1,
                dynamic_form_data: {},
            },
            errors: {
                entry_firstname: "",
                entry_lastname: "",
                email_address: "",
                entry_telephone: "",
                country_code: "",
                country_code_error: "",
                description: "",
                check2: "",
            },
        }
    },
    watch: {
        product_type(newVal) {
            this.form.product_type = Number(newVal)
        },
        "form.product_type": {
            handler(newVal, oldVal) {
                console.log("form.product_type", newVal, oldVal)
                this.updateSelectForm()
                console.log(`FS Modify Select_${this.select_form}`)
                this.buriedPointWrapper(`FS Modify Select_${this.select_form}`, this.pageType === "Customer Service_FS Modify" ? "submit_form" : "policy_service")

                if (newVal !== oldVal && oldVal) {
                    // 清空动态表单数据，保留共同字段
                    this.form.dynamic_form_data = {}

                    // 重置请求类型为默认值
                    this.activeRequestType = 1
                    this.form.request_type = 1

                    // 如果动态表单组件存在，重置它
                    if (this.$refs.dynamicForm) {
                        this.$refs.dynamicForm.resetForm && this.$refs.dynamicForm.resetForm()
                    }
                }
                this.updateSelectForm()
            },
            immediate: false,
        },
    },
    computed: {
        ...mapState({
            isLogin: (state) => state.userInfo.isLogin,
            website: (state) => state.webSiteInfo.website,
            userInfo: (state) => state.userInfo.userInfo,
            screenWidth: (state) => state.device.screenWidth,
            country_code: (state) => state.webSiteInfo.iso_code,
            select_country_id: (state) => state.selectCountry.select_country_id,
        }),
        // 不同国家的姓名前后表达顺序不一样
        isDifferentCountriesName() {
            return ["cn", "hk", "tw", "mo"].includes(this.website)
        },
        isDifferentArgreement() {
            return ["de", "de-en", "dn", "uk", "fr", "it", "es"].includes(this.website)
        },
    },
    methods: {
        updateSelectForm() {
            let idx = Number(this.form.product_type)
            // 判断 idx 是否为有效数字且在 select_options 范围内
            if (Number.isNaN(idx) || idx < 1 || idx > this.select_options.length) {
                this.select_form = ""
            } else {
                this.select_form = this.select_options[idx - 1]
            }
        },
        toSign() {
            this.$router.push(this.localePath({ name: "login", query: { redirect: this.$route.fullPath } }))
        },
        changeCode(code) {
            this.code = code
        },
        telInput(inp) {
            this.telChange(inp)
        },
        telChange(inp) {
            this.form.entry_telephone = inp
            this.blurInput("entry_telephone")
        },
        handleChange(params) {
            this.form.files.splice(0, this.form.files.length, ...params.files)
            this.upload_file_name = params.files.map((item) => item.name)
        },
        focusInput(attr) {
            this.blurInput(attr)
        },
        blurInput(attr) {
            const val = this.form[attr] || ""
            const map = {
                entry_firstname() {
                    if (!val.replace(/^\s+|\s+$/g, "")) {
                        return this.$c("form.form.errors.entry_firstname_error")
                    } else if (val.length > 40) {
                        return this.$c("form.validate.first_name.first_name_max")
                    }
                },
                entry_lastname() {
                    if (!val.replace(/^\s+|\s+$/g, "")) {
                        return this.$c("form.form.errors.entry_lastname_error")
                    } else if (val.length > 40) {
                        return this.$c("form.validate.last_name.last_name_max")
                    }
                },
                email_address() {
                    if (["cn", "hk", "tw", "mo"].includes(this.website)) return
                    if (!val.replace(/^\s+|\s+$/g, "")) return this.$c("form.form.errors.email_business_error")
                    if (!email_valdate.test(val)) return this.$c("form.form.errors.email_address_error01")
                },
                entry_telephone() {
                    if (["CN", "HK", "TW", "MO"].includes(this.form.country_code)) {
                        if (!cn_all_phone.test(val)) return this.$c("form.form.errors.entry_telephone_error01")
                    } else {
                        if (val.length > 0 && val.length < 6) {
                            return this.$c("pages.NetTermsApplication.validate.phone.phone_min")
                        } else if (val.length > 40) {
                            return this.$c("pages.NetTermsApplication.validate.phone.phone_validate")
                        } else {
                            return ""
                        }
                    }
                },
                check2() {
                    if (!this.form.check2) return this.$c("form.form.errors.check2_error")
                },
            }
            this.errors[attr] = (map[attr] && map[attr].apply(this)) || ""
        },
        blurCheck(attr) {
            if (!this.form[attr]) {
                this.blurInput(attr)
            }
        },
        verifyWrapper() {
            const errorMap = Object.keys(this.errors)
            errorMap.forEach((key) => {
                this.blurInput(key)
            })
            console.log("errorsAAAA:", this.errors)
            return errorMap.some((attr) => this.errors[attr])
        },
        submitFu() {
            // 基础表单验证
            const basicFormHasError = this.verifyWrapper()

            // 动态表单验证
            let dynamicFormHasError = false
            if (this.$refs.dynamicForm) {
                dynamicFormHasError = !this.$refs.dynamicForm.validate()
            }

            if (this.sbtn_loading || basicFormHasError || dynamicFormHasError) {
                if (dynamicFormHasError && !basicFormHasError) {
                    this.$message.error(this.$c("single.FeedBack.requiredTip") || "请完成所有必填项")
                }
                return
            }

            this.sbtn_loading = true
            this.fetchSubmit()
        },
        async fetchSubmit() {
            try {
                const formData = new FormData()
                for (const key in this.form) {
                    if (key === "files") {
                        this.form.files.forEach((item) => {
                            formData.append("files[]", item)
                        })
                    } else if (key === "entry_telephone") {
                        if (this.form.entry_telephone) {
                            formData.append("entry_telephone", `${this.code} ${this.form.entry_telephone}`)
                        }
                    } else if (key === "countries_id") {
                        formData.append("countries_id", this.select_country_id)
                    } else if (key === "dynamic_form_data") {
                        // 处理动态表单数据
                        const dynamicData = this.form.dynamic_form_data
                        console.log("dynamicData===", dynamicData.formData)
                        if (dynamicData && dynamicData.formData) {
                            for (const dynamicKey in dynamicData.formData) {
                                formData.append(`${dynamicKey}`, dynamicData.formData[dynamicKey])
                            }
                        }
                    } else {
                        formData.append(key, this.form[key])
                    }
                }
                formData.append("choose_type", this.form.product_type)
                formData.append("website_link", location.href)
                formData.append("products_id", this.productId)
                formData.append("products_categories_id", this.products_categories_id)

                console.log("formData entries:")
                for (let [key, value] of formData.entries()) {
                    console.log(key, value)
                }

                const res = await this.$axios.post("/api/modifyForm", formData)
                console.log(res)
                if (res.code === 200) {
                    for (let key in res.errors) {
                        this.$message.error(res.errors[key])
                    }
                    this.sbtn_loading = false
                }

                // 获取固定的英文表单名称
                const formName = this.getFormNameForBurial()
                this.buriedPointWrapper(`FS Modify Submit_${formName}_success`, this.pageType === "Customer Service_FS Modify" ? "submit_form" : "policy_service")
                this.$emit("submitSuccess")
            } catch (error) {
                // 获取固定的英文表单名称
                const formName = this.getFormNameForBurial()
                this.buriedPointWrapper(`FS Modify Submit_${formName}_fail`, this.pageType === "Customer Service_FS Modify" ? "submit_form" : "policy_service")
                console.error("Error in fetchSubmit:", error)
                if (error.code === 403 || error.code === 400) {
                    this.$message.error(error.message)
                }
            } finally {
                this.sbtn_loading = false
            }
        },
        getFormNameForBurial() {
            const productType = Number(this.form.product_type)

            switch (productType) {
                case 1:
                    return "PicOS® Data Center Switches"
                case 2:
                    return "PicOS® Enterprise Switches"
                case 3:
                    // AmpCon Management Platform 需要根据 application_scenario 进行二级分类
                    const applicationScenario = this.form.dynamic_form_data?.formData?.application_scenario
                    switch (Number(applicationScenario)) {
                        case 1:
                            return "AmpCon Management Platform_Data Center"
                        case 2:
                            return "AmpCon Management Platform_Enterprise"
                        case 3:
                            return "AmpCon Management Platform_Optical Networking"
                        default:
                            return "AmpCon Management Platform"
                    }
                case 4:
                    return "Optical Transceivers & Cables"
                default:
                    return "Unknown Product Type"
            }
        },
        buriedPointWrapper(eventLabel, eventAction) {
            console.log("aaaaa", eventLabel, eventAction, `${this.pageType}_${this.productId}`)
            if (window.dataLayer) {
                window.dataLayer.push({
                    event: "uaEvent",
                    eventCategory: `${this.pageType}_${this.productId}`,
                    eventAction,
                    eventLabel,
                    nonInteraction: false,
                })
            }
        },
        resize() {
            this.isMobile = window.screen.width < MOBILE_WIDTH
        },
        switchRequestType(item) {
            this.activeRequestType = item.value
            this.form.request_type = item.value
        },
        handleDynamicFormChange(data) {
            console.log("handleDynamicFormChange", data)
            this.form.dynamic_form_data = data
        },
    },
    mounted() {
        this.resize()
    },
    created() {
        this.updateSelectForm()
        if (this.userInfo) {
            this.form.email_address = this.userInfo.customers_email_address
            this.form.entry_firstname = this.userInfo.customers_firstname
            this.form.entry_lastname = this.userInfo.customers_lastname
            this.form.entry_telephone = this.userInfo.customers_telephone
        }
    },
}
</script>

<style lang="scss" scoped>
.sample-request {
    color: $textColor1;
    ::v-deep .validate_error {
        .iconfont_error {
            margin-top: 5px;
        }
    }
    .sample_main {
        padding: 16px 24px 0 24px;

        ::v-deep .upload-file-wrap.is_new .file-box {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            width: 100%;
            margin-top: 16px;
            grid-column: span 2;
            .file-item {
                margin-bottom: 0;
                margin-right: 0;
            }
        }

        .sign {
            display: flex;
            align-items: flex-start;
            padding: 10px 16px;
            background: rgba(0, 96, 191, 0.04);
            @include font14;
            margin-bottom: 12px;
            border-radius: 4px;
            &_content {
                :first-child {
                    color: #0060bf;
                    cursor: pointer;
                    &:hover {
                        text-decoration: underline;
                    }
                }
            }
        }
        .signTip {
            color: #707070;
            line-height: 22px;
        }

        .iconfFont {
            color: #0060bf;
            line-height: 22px;
            margin-right: 8px;
        }
        .upload {
            margin-top: 12px;
            ::v-deep {
                .upload-file-wrap {
                    .info-txt {
                        @include font12;
                    }
                    &:hover {
                        .info-txt {
                            text-decoration: none;
                        }
                    }
                }
            }
            .upload-title {
                color: #707070;
                @include font12;
                margin-bottom: 12px;
            }
            .fs-tip {
                margin-left: 12px;
                margin-top: 3px;

                .tip-ctn {
                    min-width: 300px;

                    .info {
                        padding: 20px;
                    }
                }
            }
        }
        .agreement-item {
            margin-top: 12px;
            .agreement {
                width: 100%;
                display: flex;
                align-items: center;
                input {
                    margin: 0 8px 0 0;

                    width: 14px;
                    height: 14px;
                    font-size: 14px;
                    @media (max-width: 960px) {
                        margin-top: 1px;
                    }
                }
                > p {
                    @include font12;
                    color: $textColor3;
                    a {
                        color: #0060bf;
                    }
                }
                &:hover {
                    cursor: pointer;
                    input[type="checkbox"] {
                        &:before {
                            color: #707070;
                        }
                    }
                }
            }
            .agreement_wrap {
                @include font14;
                color: $textColor3;
            }
        }
        .form,
        .dynamics-form {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            grid-gap: 12px;
            .tit-num {
                display: flex;
                justify-content: space-between;
                align-items: center;
                .numb {
                    color: $textColor3;
                    @include font12;

                    .active {
                        color: $textColor4;
                    }
                }
            }

            .select-box {
                ::v-deep .fs-select .fs-select-active {
                    background-color: #f6f6f8;
                }
            }
            .product-type {
                grid-column: span 2;
                ::v-deep .fs-select {
                    width: 295px;
                    @include mediaM {
                        width: 100%;
                    }
                }
            }
            .sample-item {
                &.line {
                    grid-column: span 2;
                }
                &.sample-item-cn {
                    grid-area: 1/2/1/3;
                }

                .sample-item-title {
                    color: $textColor3;
                    @include font12();
                    margin-bottom: 4px;
                }
                ::v-deep .select-country .select-country-active {
                    background-color: #f6f6f8;
                }
                ::v-deep .textarea {
                    resize: vertical;
                }
            }
            .sample-item-comments {
                .sample-item-title {
                    margin-bottom: 4px;
                }
            }
        }
        .dynamics-form {
            border-top: 1px solid #eeeeee;
            padding-top: 16px;
            margin-top: 20px;
            .switch_wrap {
                width: fit-content;
                display: flex;
                padding: 2px;
                border-radius: 4px;
                background: #f6f6f8;
                .btn_item {
                    padding: 4px 16px;
                    @include font13;
                    cursor: pointer;
                    border-radius: 2px;
                    text-align: center;
                }
                .active {
                    background-color: #fff;
                }
            }
        }
    }
    .sbtn-box {
        display: flex;
        justify-content: flex-end;
        position: sticky;
        bottom: 0;
        padding: 16px 24px 24px;
        background: #fff;
        gap: 16px;
        :deep(.fs-button) {
            height: 36px;
            padding: 0 16px;
            font-weight: normal;
        }
        .cancel_btn {
            color: #707070;
        }
        &.page-style {
            position: static;
            justify-content: flex-start;
            padding: 16px 24px 0;
            :deep(.fs-button) {
                height: 42px;
                padding: 10px 24px;
                font-weight: normal;
            }
        }
    }
    @include mediaM {
        .sample_main {
            padding: 16px 16px 0 16px;
            .form {
                .sample-item {
                    grid-column: span 2;
                }
            }
        }
        .sbtn-box {
            position: sticky;
            bottom: 0;
            padding: 16px 16px 20px;
            ::v-deep .fs-button {
                width: 100%;
            }
        }
    }
}
</style>
