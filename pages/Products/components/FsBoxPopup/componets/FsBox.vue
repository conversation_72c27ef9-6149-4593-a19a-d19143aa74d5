<template>
    <div class="fs-box-request fs-box-request-products" v-loading="loading">
        <div class="fs_box_main">
            <div class="box-title">{{ $c("pages.Products.fs_box_pop[1]") }}</div>
            <div class="product-box">
                <div class="product-list">
                    <div class="product-item">
                        <img :src="'https://resource.fs.com/mall/mainImg/60x60/20220920094348uewyw6.jpg'" class="product-img" />
                        <div class="txt-box">
                            <div class="tit">{{ info.products_name }}</div>
                            <p class="txt">
                                <span>{{ info.products_model }}</span>
                                <span>#{{ info.products_id_show }}</span>
                            </p>
                        </div>
                        <div class="handle-box">
                            <div class="num-box">{{ $c("pages.Products.Qty") }}: 1</div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="box-title">{{ $c("form.form.letUsContactYou") }}</div>
            <div class="form">
                <div class="fs-box-item" :class="{ 'fs-box-item-cn': isDifferentCountriesName }">
                    <div class="fs-box-item-title">
                        <span>{{ $c("form.form.first_name") }}</span>
                    </div>
                    <input
                        v-model.trim="form.customers_firstname"
                        :class="{ error_input: errors.customers_firstname }"
                        @focus.stop="focusInput('customers_firstname', false, 'First Name Input')"
                        @blur="blurInput('customers_firstname')"
                        type="text"
                        class="inp is_new" />
                    <validate-error :error="errors.customers_firstname"></validate-error>
                </div>
                <div class="fs-box-item">
                    <div class="fs-box-item-title">
                        <span>{{ $c("form.form.last_name") }}</span>
                    </div>
                    <input
                        v-model.trim="form.customers_lastname"
                        :class="{ error_input: errors.customers_lastname }"
                        @focus.stop="focusInput('customers_lastname', false, 'Last Name Input')"
                        @blur="blurInput('customers_lastname')"
                        type="text"
                        class="inp is_new" />
                    <validate-error :error="errors.customers_lastname"></validate-error>
                </div>
                <div class="fs-box-item">
                    <div class="fs-box-item-title">
                        <span>{{ $c("form.form.email_business") }}</span>
                    </div>
                    <input
                        type="text"
                        :disabled="disabled"
                        :class="{ error_input: errors.email_address }"
                        v-model.trim="form.email_address"
                        @focus.stop="focusInput('email_address', false, 'Email Address Input')"
                        @blur="blurInput('email_address')"
                        class="inp is_new" />
                    <validate-error :error="errors.email_address.replace('XXXX', localePath({ name: 'login', query: { redirect: $route.fullPath } }))"></validate-error>
                </div>
                <div class="fs-box-item">
                    <div class="fs-box-item-title">
                        <span>{{ $c("form.form.phone_number") }}</span>
                    </div>
                    <tel-code
                        :isNewStyle="true"
                        code="1"
                        @changeCode="changeCode"
                        :error="errors.customers_telephone"
                        :phone="form.customers_telephone"
                        @change="telChange"
                        @point="handleBuried('Phone Number Input')"></tel-code>
                    <validate-error :error="errors.customers_telephone.replace('XXXX', localePath({ name: 'login', query: { redirect: $route.fullPath } }))"></validate-error>
                </div>
                <div class="fs-box-item" :class="{ line: !isShowState }">
                    <div class="fs-box-item-title">
                        <span>{{ $c(country_label) }}</span>
                    </div>
                    <select-country :isNewStyle="true" position="absolute" @change="changeCountry" v-model.trim="form.countries_id" @click.native="handleBuried('Country/Region Drop-Down')"></select-country>
                    <validate-error :error="errors.countries_id"></validate-error>
                </div>
                <div class="fs-box-item" v-if="isShowState">
                    <div class="fs-box-item-title">
                        <span>{{ $c("single.ProductReturnForm.state.tit") }}</span>
                    </div>
                    <RegionSelect :isNewStyle="true" ref="regionSelect" />
                </div>
                <div class="fs-box-item line fs-box-item-comments">
                    <div class="tit-num">
                        <div class="fs-box-item-title">
                            <span>{{ $c("form.form.comment") }} ({{ $c("form.form.optional") }})</span>
                        </div>
                        <div class="numb">
                            <span :class="{ active: form.content.length === 5000 }"> {{ form.content.length }} </span> <span>/5000</span>
                        </div>
                    </div>

                    <textarea
                        v-model.trim="form.content"
                        :placeholder="$c('form.form.comments_placeholder')"
                        maxlength="5000"
                        class="textarea is_new"
                        @focus.stop="focusInput('comments', false, 'Comments(Optional) Input')"
                        @change="blurInput('comments')"></textarea>
                    <div class="input-item-number" :class="{ noFlex: errors.content }">
                        <validate-error :error="errors.content"></validate-error>
                    </div>
                </div>
                <div class="fs-box-item line">
                    <div class="agreement">
                        <input v-model="check1" @change="blurInput('check1')" type="checkbox" class="chk" />
                        <p v-if="website == 'jp'">
                            <a @click.stop="openAgreement" href="javascript:;">{{ $c("pages.Products.fs_box_pop[8]") }}</a
                            >{{ $c("pages.Products.fs_box_pop[9]") }}
                        </p>
                        <p v-else-if="website == 'de'" v-html="de_or_fr_agree_txt01"></p>
                        <p v-else>
                            {{ $c("pages.Products.fs_box_pop[8]") }} <a @click.stop="openAgreement" href="javascript:;">{{ $c("pages.Products.fs_box_pop[9]") }}</a
                            >.
                        </p>
                    </div>
                    <validate-error :error="errors.check1"></validate-error>
                </div>
                <PolicyCheck class="fs-box-item line protocol" v-model="check2" @change="blurInput('check2')" :error="errors.check2" />
            </div>
        </div>
        <div>
            <g-recaptcha ref="grecaptcha" @getValidateCode="getValidateCode"></g-recaptcha>
        </div>
        <div class="sbtn-box fs-box-item line">
            <fs-button id="sample_submit" :text="$c('form.form.submit')" @click="submitFu" :loading="sbtn_loading" htmlType="submit"></fs-button>
        </div>
        <fs-box-popup @close="closeAgreement" @checkFsBox="setAgree" :show="agreementShow" :show_fs_box_pop="agreementShow"></fs-box-popup>
    </div>
</template>

<script>
import { mapState, mapGetters } from "vuex"
import { email_valdate, phone_validate, cn_mobile_tel } from "@/constants/validate.js"
import FsButton from "@/components/FsButton/FsButton.vue"
import FsSelect from "@/components/FsSelect/FsSelect.vue"
import ValidateError from "@/components/ValidateError/ValidateError.vue"
import TelCode from "@/components/TelCode/TelCode.vue"
import SelectCountry from "@/components/SelectCountry/SelectCountry.vue"
import QtyBox from "@/components/QtyBox/QtyBox.vue"
import FsPopup from "@/components/FsPopup/FsPopup.vue"
import RegionSelect from "@/components/RegionSelect/RegionSelect"
import GRecaptcha from "@/components/GRecaptcha/GRecaptcha"
import FsBoxPopup from "@/popup/FsBoxPopup/FsBoxPopup"
import { isNeedGrecaptcha } from "@/util/grecaptchaHost"
import PolicyCheck from "@/components/PolicyCheck/PolicyCheck.vue"
export default {
    name: "FsBox",
    components: {
        FsButton,
        FsSelect,
        ValidateError,
        QtyBox,
        TelCode,
        SelectCountry,
        FsPopup,
        RegionSelect,
        GRecaptcha,
        FsBoxPopup,
        PolicyCheck,
    },
    props: {
        productId: {
            type: [String, Number],
            default: "",
        },
        info: {
            type: Object,
            default: {},
        },
    },
    data() {
        return {
            product_list: [],
            sbtn_loading: false,
            sbtn_pro_loading: false,
            product_sample: {
                products_id: "",
                products_num: 1,
            },
            form: {
                customers_firstname: "",
                customers_lastname: "",
                email_address: "",
                customers_telephone: "",
                countries_id: 223,
                content: "",
            },
            check1: false,
            check2: false,
            errors: {
                products_id: "",
                customers_firstname: "",
                customers_lastname: "",
                email_address: "",
                customers_telephone: "",
                countries_id: "",
                check1: "",
                check2: "",
                content: "",
            },
            disabled: false,
            agreementShow: false,
            code: "",
            loading: false,
            recaptchaTp: false,
            recaptchaVal: "",
        }
    },
    computed: {
        ...mapState({
            isMobile: (state) => state.device.isMobile,
            userInfo: (state) => state.userInfo.userInfo,
            website: (state) => state.webSiteInfo.website,
            resourcePage: (state) => state.device.resource_page,
            country_code: (state) => state.webSiteInfo.iso_code,
            countries_id: (state) => state.webSiteInfo.countries_id,
        }),
        ...mapGetters({
            isCn: "webSiteInfo/isCn",
            isShowState: "selectCountry/isShowState",
            country_label: "selectCountry/country_label",
        }),
        // 不同国家的姓名前后表达顺序不一样
        isDifferentCountriesName() {
            return ["cn", "hk", "tw", "mo"].includes(this.website)
        },
        jp_agreement_choose() {
            return this.$c("single.sampleApplication.agreement_choose.txt02")
                .replace("/policies/privacy_policy.html", this.localePath({ path: "/policies/privacy_policy.html" }))
                .replace("/policies/terms_of_use.html", this.localePath({ path: "/policies/terms_of_use.html" }))
        },
        de_or_fr_agree_txt01() {
            return `Ich bestätige, dass ich XXXX gelesen und verstanden habe.`.replace("XXXX", `<a onclick="openAgreement()" href="javascript:;">die Bedingungen für die kostenlose FS BOX</a>`)
        },
        de_or_fr_agree_txt02() {
            let msg = this.$c("form.form.agree_txt02")
            msg = msg.replace(/xxxxx/, `<a href="${this.localePath({ name: "privacy-policy" })}" target="_blank">${this.$c("form.form.privacy_policy")}</a>`)
            msg = msg.replace(/vvvvv/, `<a href="${this.localePath({ name: "terms-of-use" })}" target="_blank">${this.$c("form.form.terms_of_use")}</a>`)
            return msg
        },
    },
    created() {
        if (this.userInfo) {
            this.form.email_address = this.userInfo.customers_email_address
            this.form.customers_firstname = this.userInfo.customers_firstname
            this.form.customers_lastname = this.userInfo.customers_lastname
            this.disabled = true
        }
    },
    mounted() {
        let _this = this
        if (typeof window !== "undefined") {
            window.openAgreement = _this.openAgreement
        }
        this.form.countries_id = this.countries_id
    },
    methods: {
        changeCode(code) {
            this.code = code
        },
        handleBuried(label) {
            // window.dataLayer &&
            //     window.dataLayer.push({
            //         event: "uaEvent",
            //         eventCategory: "Product Service_Sample Application Page",
            //         eventAction: "sample_application",
            //         eventLabel: label,
            //         nonInteraction: false,
            //     })
        },
        changeCountry(item) {
            this.form.countries_id = item.countries_id
        },
        telChange(inp) {
            this.form.customers_telephone = inp
            this.blurInput("customers_telephone")
        },
        focusInput(attr, type, label) {
            type ? (this[attr + "_error"] = "") : (this.errors[attr + "_error"] = "")
            label && this.handleBuried(label)
        },
        blurInput(attr) {
            const val = this.form[attr] || ""
            const map = {
                // products_id() {
                //     if (this.product_list.length === 0) return this.$c("form.form.errors.products_id_error")
                // },
                customers_firstname() {
                    if (!val.replace(/^\s+|\s+$/g, "")) {
                        return this.$c("form.form.errors.entry_firstname_error")
                    } else if (val.length > 40) {
                        return this.$c("form.validate.first_name.first_name_max")
                    }
                },
                customers_lastname() {
                    if (!val.replace(/^\s+|\s+$/g, "")) {
                        return this.$c("form.form.errors.entry_lastname_error")
                    } else if (val.length > 40) {
                        return this.$c("form.validate.last_name.last_name_max")
                    }
                },
                email_address() {
                    if (!val.replace(/^\s+|\s+$/g, "")) return this.$c("form.form.errors.email_business_error")
                    if (!email_valdate.test(val)) return this.$c("form.form.errors.email_address_error01")
                },
                customers_telephone() {
                    if (!val.replace(/^\s+|\s+$/g, "")) return this.$c("form.form.errors.entry_telephone_error")
                    if (this.isCn) {
                        if (!cn_mobile_tel.test(val)) return this.$c("form.form.errors.entry_telephone_error01")
                    } else {
                        if (val.length > 0 && val.length < 6) {
                            return this.$c("pages.NetTermsApplication.validate.phone.phone_min")
                        } else if (val.length > 40) {
                            return this.$c("pages.NetTermsApplication.validate.phone.phone_validate")
                        } else {
                            return ""
                        }
                    }
                },
                check1() {
                    if (!this.check1) return this.$c("pages.Products.fs_box_pop[4]")
                },
                check2() {
                    if (!this.check2) return this.$c("form.form.errors.check2_error")
                },
                content() {
                    return ""
                },
            }
            this.errors[attr] = (map[attr] && map[attr].apply(this)) || ""
        },
        verifyWrapper() {
            const errorMap = Object.keys(this.errors)
            errorMap.forEach((key) => {
                this.blurInput(key)
            })
            return errorMap.some((attr) => this.errors[attr])
        },
        submitFu() {
            if (this.sbtn_loading || this.verifyWrapper()) {
                return
            }
            if (typeof window !== "undefined" && isNeedGrecaptcha(window.location.hostname)) {
                if (!this.recaptchaTp && this.$refs.grecaptcha) {
                    this.$refs.grecaptcha.clickRecaptcha()
                    return
                }
            }
            this.sbtn_loading = true
            this.fetchProductsSubmit()
        },
        async fetchProductsSubmit() {
            try {
                let params = {
                    ...this.form,
                    customers_telephone: `${this.code.replace("+", "")} ${this.form.customers_telephone}`,
                    products_id: this.productId,
                }
                if (this.$refs.regionSelect) {
                    params.state = this.$refs.regionSelect.state || ""
                }
                console.log(params)
                const res = await this.$axios.post("/api/box_gift", params, { headers: { "g-recaptcha-response": this.recaptchaVal } })

                if (res.code === 200 && res.status === "sensiWords") {
                    for (let key in res.errors) {
                        this.errors[key] = this.$c("form.form.errors.sensiWords")
                    }
                    this.initGrecaptcha()
                    this.sbtn_loading = false
                    return
                }

                this.buriedPointWrapper("Submit Success", "submit_application")
                this.$emit("submitSuccess", res.data.case_number)
            } catch (error) {
                this.initGrecaptcha()
                this.buriedPointWrapper("Submit Fail", "submit_application")
                if (typeof window !== "undefined" && isNeedGrecaptcha(window.location.hostname)) {
                    if (error.code === 409 && this.$refs.grecaptcha) {
                        this.$refs.grecaptcha.grecaptchaError = this.$c("pages.Login.regist.grecaptcha_error")
                    }
                }
                if (error.code === 403 || error.code === 400) {
                    this.$message.error(error.message)
                }
                if (error.code === 422) {
                    this.errors.countries_id = error.message
                }
            }
            this.sbtn_loading = false
        },
        closeAgreement() {
            this.agreementShow = false
        },
        openAgreement() {
            this.agreementShow = true
        },
        buriedPointWrapper(eventLabel, eventAction) {
            if (typeof window !== "undefined" && window.dataLayer) {
                window.dataLayer.push({
                    event: "uaEvent",
                    eventCategory: `productDetailPage_${this.productId}`,
                    eventAction,
                    eventLabel,
                    nonInteraction: false,
                })
            }
        },
        // 谷歌人机校验
        getValidateCode(val) {
            this.recaptchaTp = val.recaptchaTp
            if (val.recaptchaTp) {
                this.recaptchaVal = val.recaptchaVal
                this.submitFu()
            }
        },
        setAgree() {
            this.check1 = true
            this.blurInput("check1")
        },
        // 初始化谷歌验证
        initGrecaptcha() {
            this.recaptchaTp = false
            this.recaptchaVal = ""
        },
    },
}
</script>

<style lang="scss" scoped>
.fs-box-request {
    color: $textColor1;

    .fs_box_main {
        padding: 20px 32px;
        max-height: calc(100vh - 282px);
        @include scrollY;
        .box-title {
            margin-bottom: 16px;
            @include font16();
            font-weight: 600;
            color: $textColor1;
        }
        .product-box {
            margin-bottom: 24px;
            .product-list {
                background: #fafafb;
                padding: 0 24px;
                margin-top: 20px;
                .product-item {
                    display: flex;
                    align-items: flex-start;
                    padding: 20px 0;
                    &:not(:first-child) {
                        border-top: 1px solid #e5e5e5;
                    }
                    img {
                        width: 80px;
                        height: 80px;
                        mix-blend-mode: multiply;
                    }
                    .txt-box {
                        flex: 1;
                        margin: 0 16px 0 20px;
                        .tit {
                            @include font14();
                            font-weight: 600;
                            color: #19191a;
                            @include txt-more-hid(3);
                        }
                        .txt {
                            @include font14();
                            color: #89898c;
                            margin-top: 12px;
                            > span:first-child {
                                margin-right: 8px;
                            }
                        }
                    }

                    .handle-box {
                        display: flex;
                        align-items: center;
                        justify-content: flex-end;
                        width: 114px;
                        .num-box {
                            @include font16;
                            color: #19191a;
                        }
                    }
                }
            }
        }

        .form {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            grid-gap: 16px 12px;
            .fs-box-item {
                &.line {
                    grid-column: span 2;
                }
                &.protocol {
                    margin-top: -4px;
                }
                &.fs-box-item-cn {
                    grid-area: 1/2/1/3;
                }

                .fs-box-item-title {
                    color: $textColor3;
                    @include font12();
                    margin-bottom: 4px;
                }
                .tit-num {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                }
                .numb {
                    display: flex;
                    color: $textColor3;
                    @include font12;
                    justify-content: flex-end;
                    .active {
                        color: $textColor4;
                    }
                }
                // .input-item-number {
                //     display: flex;
                //     justify-content: flex-end;
                //     align-items: center;
                //     margin-top: 4px;

                // }
                // .noFlex {
                //     display: flex;
                //     justify-content: space-between;
                //     align-items: center;
                // }

                .agreement {
                    width: 100%;
                    display: flex;
                    align-items: flex-start;
                    input {
                        font-size: 14px;
                        margin: 3px 8px 0 0;
                        width: 14px;
                        height: 14px;
                        @media (max-width: 960px) {
                            margin-top: 1px;
                        }
                    }
                    > p {
                        @include font12;
                        color: $textColor2;
                        a {
                            color: #0060bf;
                        }
                    }
                    &:hover {
                        cursor: pointer;
                        input[type="checkbox"] {
                            &:before {
                                color: #646466;
                            }
                        }
                    }
                }
            }
        }
    }
    .sbtn-box {
        display: flex;
        justify-content: flex-end;
        padding: 20px 32px;
        background: #fff;
        border-top: 1px solid #e5e5e5;
    }
    .agreement {
        ::v-deep .fs-popup-ctn {
            .fs-popup-header {
                @include font20;
                padding: 20px 68px 20px 32px;
                .iconfont_close {
                    right: 32px;
                    line-height: 30px;
                }
            }
            @media (max-width: 414px) {
                .fs-popup-body .agreement-content {
                    padding: 16px 16px 40px;
                }
            }
            .title_box {
                .title {
                    // @include font16;
                    font-weight: normal;
                    padding-right: 0;
                }
            }
            .agreement-content {
                padding: 20px 32px 40px;
                li {
                    display: flex;
                    margin-bottom: 10px;
                    color: $textColor2;
                    &:last-child {
                        margin-bottom: 0;
                    }
                    span {
                        width: 4px;
                        height: 4px;
                        background-color: $textColor2;
                        border-radius: 50%;
                        flex-shrink: 1;
                        margin: 10px 14px 0 0;
                    }
                    p {
                        @include font14;
                        flex-shrink: 1;
                        flex: 1;
                    }
                }
            }
        }
    }

    .error_input {
        @include errorInput;
    }
    ::v-deep .validate_error .error_info {
        @include font12;
    }
    @media (max-width: 960px) {
        .fs_box_main {
            max-height: calc(100vh - 138px);
            height: 100vh;
        }
    }
    @include mediaM {
        .fs_box_main {
            max-height: calc(100vh - 132px);
            padding: 20px 16px;
            .product-box {
                .add-pro-box {
                    display: block;
                    > img {
                        display: none;
                    }
                    .add-pro-content {
                        margin: 0;
                    }
                    ::v-deep .fs-button {
                        margin-top: 20px;
                        width: 100%;
                    }
                }
                .product-list {
                    padding: 0 20px;
                    .product-item {
                        flex-wrap: wrap;
                        .txt-box {
                            margin-right: 0;
                        }
                        .handle-box {
                            flex: 0 0 100%;
                            padding-left: 100px;
                            margin-top: 12px;
                        }
                    }
                }
            }
            .form {
                .fs-box-item {
                    grid-column: span 2;
                }
            }
        }
        .sbtn-box {
            padding: 20px 16px;
            ::v-deep .fs-button {
                width: 100%;
            }
        }
    }
}
</style>
