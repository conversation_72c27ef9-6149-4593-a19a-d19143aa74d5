<template>
    <div class="custom-select" ref="customSelect" @mouseover="triggerMouseover" @mouseleave="triggerMouseleave">
        <div class="custom-select-body">
            <div class="custom-select-active" :class="{ active: show, only: options.length < 2, isTipWidth: isCustomServer == 2 }" @click="toogleShow">
                <div class="left" :class="{ one: defaultObj.price }">
                    <img class="img" v-if="defaultObj.img" :src="defaultObj.img" />
                    <span class="name" v-if="defaultObj.name" :title="defaultObj.name">{{ defaultObj.name }}</span>
                    <span class="name" v-else>None</span>
                </div>
                <span v-if="defaultObj.price && isCustomServer != 2" class="price" v-html="defaultObj.price"></span>
                <span v-if="allShow" class="iconfont icofnont-down" :class="{ 'icofnont-down-up': show, hide: !(options.length > 1) }">&#xe704;</span>
            </div>
            <fs-popover position="top" :icon="defaultObj.pop ? true : false">
                <div v-if="defaultObj.pop">{{ defaultObj.pop }}</div>
            </fs-popover>
        </div>
        <slide-down>
            <div class="select-wrap" :class="{ isTran: !show, isCus: isCustomServer == 2 }" v-show="show">
                <div class="select-box">
                    <div
                        class="item"
                        v-for="(item, index) in setOptions(options)"
                        :key="index"
                        @click="seleItem(item)"
                        @mouseenter="mouseNameCheck = item.name"
                        @mouseleave="mouseNameCheck = ''"
                        :class="{ one: item.price, isCheck: mouseName == item.name && !mouseNameCheck }">
                        <div class="left">
                            <fs-popover :transfer="false" :icon="false" position="left" v-if="item.img">
                                <div slot="trigger">
                                    <img class="list-view" :src="item.img" :alt="item.name" />
                                </div>
                                <img class="mImg" :src="item.img" />
                            </fs-popover>
                            <div v-if="item.img" class="m_pic_box" @click.stop>
                                <PhotoGallery galleryID="demo_gallery" :images="[item.img]" />
                            </div>
                            <span class="name" :title="item.name">{{ item.name }}</span>
                        </div>
                        <span class="price" v-if="item.price && isCustomServer != 2" v-html="item.price"></span>
                    </div>
                </div>
            </div>
        </slide-down>
    </div>
</template>
<script>
import SlideDown from "@/components/SlideDown/SlideDown.vue"
import FsPopover from "@/components/FsPopover"
import PhotoGallery from "@/components/PhotoGallery/PhotoGallery.vue"

export default {
    name: "CustomSelect",
    props: {
        options: {
            type: Array,
            required: true,
            default: () => [],
        },
        clickclose: {
            type: Boolean,
            default: true,
        },
        allShow: {
            type: Boolean,
            default: false,
        },
        isCustomServer: {
            type: Number,
            default: 1,
        },
        oArry: {
            type: Array,
            default: () => [],
        },
    },
    components: {
        SlideDown,
        FsPopover,
        PhotoGallery,
    },
    data: () => ({
        click_show: false,
        active_show: false,
        options_show: false,
        defaultObj: {},
        mouseName: "",
        mouseNameCheck: "",
    }),
    computed: {
        show() {
            return this.click_show
        },
        newOArry() {
            if (this.oArry.length) {
                return [...new Set(this.oArry)]
            } else {
                return this.oArry
            }
        },
    },
    watch: {
        options: {
            deep: true,
            handler(val) {
                if (this.isCustomServer == 2) {
                    let arr = val.filter((item) => {
                        return item.is_default == 1
                    })
                    if (arr.length) {
                        this.defaultObj = {
                            name: arr[0].name,
                            id: arr[0].id,
                            value: arr[0].value,
                            img: arr[0].img,
                            price: arr[0].price,
                            pop: arr[0].tip,
                        }
                    }
                }
            },
        },
    },
    created() {
        let arr = this.options.filter((item) => {
            return item.is_default == 1
        })
        if (arr.length) {
            this.defaultObj = {
                name: arr[0].name,
                id: arr[0].id,
                value: arr[0].value,
                img: arr[0].img,
                price: arr[0].price,
                pop: arr[0].tip,
            }
        }
    },
    mounted() {
        if (typeof document !== "undefined") {
            document.addEventListener("click", this.handleOtherClick)
        }
    },
    destroyed() {
        if (typeof document !== "undefined") {
            document.removeEventListener("click", this.handleOtherClick)
        }
    },
    methods: {
        toogleShow() {
            if (this.options.length > 1) {
                this.mouseName = this.defaultObj.name
                this.click_show = !this.click_show
            }
        },
        triggerMouseover() {
            if (this.trigger === "hover") {
                this.active_show = true
                this.options_show = false
            }
        },
        triggerMouseleave() {
            if (this.trigger === "hover") {
                setTimeout(() => {
                    this.active_show = false
                }, 100)
            }
        },
        handleOtherClick(e) {
            e.stopPropagation()
            if (this.clickclose && typeof document !== "undefined") {
                // 检查点击的元素是否在PhotoSwipe容器内
                const pswpElement = document.querySelector(".pswp")
                const pswpBackdrop = document.querySelector(".pswp__bg")

                // 如果点击的是PhotoSwipe相关元素，不关闭选项列表
                if ((pswpElement && pswpElement.contains(e.target)) || (pswpBackdrop && pswpBackdrop.contains(e.target)) || e.target.closest(".pswp")) {
                    return
                }

                if (!this.$refs.customSelect.contains(e.target)) {
                    this.click_show = false
                }
            }
        },
        seleItem(data) {
            if (data.name != this.defaultObj.name) {
                this.defaultObj = {
                    name: data.name,
                    id: data.id,
                    value: data.value,
                    img: data.img,
                    price: data.price,
                    pop: data.tip,
                }
                this.click_show = false
                this.$emit("setCusCup", { option_id: data.id, value_id: data.value, value_type: data.type, related: data.related })
            } else {
                this.click_show = false
            }
        },
        setOptions(data) {
            if (this.newOArry.length) {
                let newa = this.options.map((item) => {
                    return String(item.server)
                })
                let ass = this.newOArry.filter((item) => {
                    return newa.indexOf(item) > -1
                })
                if (ass.length) {
                    return this.options.filter((item) => {
                        return ass.indexOf(String(item.server)) != -1
                    })
                } else {
                    return data
                }
            } else {
                return data
            }
        },
    },
}
</script>
<style lang="scss" scoped>
.custom-select {
    width: 100%;
    position: relative;
    .custom-select-body {
        display: flex;
        align-items: center;
        .isTipWidth {
            max-width: calc(100% - 20px);
        }
    }
    .custom-select-active {
        border: 1px solid #e5e5e5;
        position: relative;
        border-radius: 4px;
        padding: 0 8px;
        @include font13;
        color: $textColor1;
        min-width: 100%;
        height: 30px;
        display: flex;
        align-items: center;
        justify-content: flex-start;
        cursor: pointer;
        user-select: none;
        background: #fff;
        .left {
            display: flex;
            align-items: center;
            flex: 1;
            overflow: hidden;
            .img {
                width: 24px;
                height: 24px;
                margin-right: 8px;
                mix-blend-mode: multiply; // 正片叠底
            }
            > span {
                text-overflow: ellipsis;
                white-space: nowrap;
                overflow: hidden;
                max-width: 264px;
            }
            &.one {
                > span {
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    overflow: hidden;
                }
            }
        }
        .price {
            color: $textColor3;
            margin-left: 8px;
        }
        .icofnont-down {
            transition: all 0.2s;
            color: $textColor3;
            font-size: 12px;
            margin-left: 8px;
            &.icofnont-down-up {
                color: #19191a;
                transform: rotateX(-180deg);
            }
            &.hide {
                visibility: hidden;
            }
        }
        .select-active-box {
            display: flex;
            align-items: center;
        }
        &:hover {
            background-color: #f2f2f2;
            .iconfont {
                color: #19191a;
            }
        }
        &.active {
            border-color: #19191a;
        }
        &.only {
            cursor: default;
        }
    }
    .select-wrap {
        width: 100%;
        background: #fff;
        border-radius: 4px;
        border: solid 1px #e5e5e5;
        box-shadow: 0px 3px 6px -2px rgba(0, 0, 0, 0.1);
        top: 36px;
        position: absolute;
        z-index: 1;
        .select-box {
            margin: 5px auto;
            .item {
                display: flex;
                align-items: center;
                justify-content: space-between;
                padding: 3px 8px;
                @include font13;
                cursor: pointer;
                color: $textColor1;
                .left {
                    flex: 1;
                    display: flex;
                    align-items: center;
                    overflow: hidden;
                    ::v-deep .fs-popover {
                        margin-left: 0;
                        .popper-computer {
                            padding: 12px;
                            cursor: default;
                        }
                        .popper-computer-left {
                            right: 14px !important;
                        }
                        .popper-computer-left::after {
                            width: 34px;
                            right: -26px;
                        }
                        .mImg {
                            width: 200px;
                            height: 200px;
                            display: block;
                        }
                        .trigger {
                            > div {
                                display: flex;
                            }
                        }
                        @media (max-width: 960px) {
                            display: none;
                        }
                    }
                    .list-view {
                        width: 24px;
                        height: 24px;
                        margin-right: 8px;
                        mix-blend-mode: multiply;
                    }
                    > span {
                        overflow: hidden;
                        text-overflow: ellipsis;
                        white-space: nowrap;
                    }
                    .m_pic_box {
                        display: none;
                        @media (max-width: 960px) {
                            width: 24px;
                            height: 24px;
                            margin-right: 8px;
                            display: flex;
                            position: relative;
                            mix-blend-mode: multiply;
                        }
                    }
                }
                .price {
                    margin-left: 8px;
                    color: $textColor3;
                }
                &:hover {
                    background-color: #f7f7f7;
                }
                &.one {
                    .left {
                        overflow: hidden;
                        > span {
                            overflow: hidden;
                            text-overflow: ellipsis;
                            white-space: nowrap;
                        }
                    }
                }
                &.isCheck {
                    background-color: #f7f7f7;
                    cursor: default;
                }
            }
        }
        &.isTran {
            overflow: hidden;
            border: none;
        }
        &.isCus {
            // width: calc(100% - 20px);
        }
    }
}
</style>
