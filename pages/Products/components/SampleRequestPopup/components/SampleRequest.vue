<template>
    <fs-popup-new transition="slide-up" v-loading="loading" :clickHide="false" :show="show" :title="$c('form.form.sampleRequestPopupTitle')" width="650" isNew type="all" @close="$emit('close')" :isMDrawer="true">
        <div class="sample-request sample-request-products">
            <div class="sample_main">
                <div class="box-title">{{ $c("single.sampleApplication.form.title") }}</div>
                <div class="product-box">
                    <div class="add-pro-box">
                        <img src="https://resource.fs.com/mall/generalImg/20230503154732odi7ou.png" alt="" />
                        <div class="add-pro-content">
                            <p class="input-title">{{ $c("form.form.enterProID") }} *</p>
                            <div class="input-box">
                                <span class="prefix">#</span>
                                <input
                                    type="text"
                                    :placeholder="$c('components.smallComponents.search.pcPlaceholder')"
                                    :class="{ error_input: errors.products_id }"
                                    @keyup.enter="addProduct(product_sample.products_id, product_sample.products_num)"
                                    @focus.stop="focusInput('products_id', true, 'Sample Product Input')"
                                    @blur="blurInput('products_id')"
                                    v-model.trim="product_sample.products_id"
                                    class="inp is_new" />
                                <fs-button :text="$c('form.form.add')" type="red" @click="addProduct(product_sample.products_id, product_sample.products_num)" :loading="sbtn_pro_loading" htmlType="submit"></fs-button>
                            </div>
                            <validate-error v-if="screenWidth > 768" class="p_error" :error="errors.products_id"></validate-error>
                        </div>
                    </div>
                    <validate-error v-if="screenWidth <= 768" class="p_error" :error="errors.products_id"></validate-error>
                    <div class="product-list">
                        <div class="product-item" v-for="(item, index) in product_list" :key="index">
                            <img :src="item.images_arr" class="product-img" />
                            <div class="txt-box">
                                <div class="tit">{{ item.products_name }}</div>
                                <p class="txt">
                                    <span>#{{ item.product_ids }}</span>
                                </p>
                            </div>
                            <div class="handle-box">
                                <qty-box :isNewStyle="true" :num="item.product_nums" :attr="index" @change="qtyChange"></qty-box>
                                <div class="del-box" @click="deletePro(index)">
                                    <i class="iconfont">&#xe65f;</i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="box-title">{{ $c("form.form.letUsContactYou") }}</div>
                <div class="form">
                    <div class="sample-item" :class="{ 'sample-item-cn': isDifferentCountriesName }">
                        <div class="sample-item-title">
                            <span>{{ $c("form.form.first_name") }} *</span>
                        </div>
                        <input
                            v-model.trim="form.entry_firstname"
                            :class="{ error_input: errors.entry_firstname }"
                            @focus.stop="focusInput('entry_firstname', false, 'First Name Input')"
                            @input="blurInput('entry_firstname')"
                            @blur="blurCheck('entry_firstname')"
                            type="text"
                            class="inp is_new" />
                        <validate-error :error="errors.entry_firstname"></validate-error>
                    </div>
                    <div class="sample-item">
                        <div class="sample-item-title">
                            <span>{{ $c("form.form.last_name") }} *</span>
                        </div>
                        <input
                            v-model.trim="form.entry_lastname"
                            :class="{ error_input: errors.entry_lastname }"
                            @focus.stop="focusInput('entry_lastname', false, 'Last Name Input')"
                            @input="blurInput('entry_lastname')"
                            @blur="blurCheck('entry_firstname')"
                            type="text"
                            class="inp is_new" />
                        <validate-error :error="errors.entry_lastname"></validate-error>
                    </div>
                    <div class="sample-item">
                        <div class="sample-item-title">
                            <span>{{ $c("form.form.email_business") }}{{ isCn ? "" : " *" }}</span>
                        </div>
                        <input
                            type="text"
                            :disabled="!['cn', 'hk', 'tw', 'mo'].includes(website) ? 'disabled' : false"
                            :class="{ error_input: errors.email_address }"
                            v-model.trim="form.email_address"
                            @focus.stop="focusInput('email_address', false, 'Email Address Input')"
                            @input="blurInput('email_address')"
                            @blur="blurCheck('entry_firstname')"
                            class="inp is_new" />
                        <validate-error :error="errors.email_address.replace('XXXX', localePath({ name: 'login', query: { redirect: $route.fullPath } }))"></validate-error>
                    </div>
                    <div class="sample-item">
                        <div class="sample-item-title">
                            <span>{{ $c("form.form.phone_number") }} *</span>
                        </div>
                        <tel-code
                            :isNewStyle="true"
                            code="1"
                            @changeCode="changeCode"
                            :error="errors.entry_telephone"
                            :phone="form.entry_telephone"
                            @change="telChange"
                            @input="telInput"
                            @point="handleBuried('Phone Number Input')"></tel-code>
                        <validate-error :error="errors.entry_telephone.replace('XXXX', localePath({ name: 'login', query: { redirect: $route.fullPath } }))"></validate-error>
                    </div>
                    <div class="sample-item" :class="{ line: !isShowState }">
                        <div class="sample-item-title">
                            <span>{{ $c(country_label) }} *</span>
                        </div>
                        <select-country
                            :isNewStyle="true"
                            :disabled="isCn"
                            position="absolute"
                            @change="changeCountry"
                            v-model.trim="form.country_code"
                            @click.native="handleBuried('Country/Region Drop-Down')"></select-country>
                        <validate-error :error="errors.country_code"></validate-error>
                    </div>
                    <div class="sample-item" v-if="isShowState">
                        <div class="sample-item-title">
                            <span>{{ $c("single.ProductReturnForm.state.tit") }}</span>
                        </div>
                        <RegionSelect :isNewStyle="true" ref="regionSelect" />
                    </div>
                    <div class="sample-item line" v-show="interest_type_options && interest_type_options.length > 0">
                        <div class="sample-item-title">
                            <span>{{ $c("form.form.interest_type_products") }} *</span>
                        </div>
                        <div class="select-box">
                            <fs-select
                                :isNewStyle="true"
                                :options="interest_type_options"
                                v-model.trim="form.interest_type"
                                @change="changeType"
                                :placeholder="$c('form.form.please_select')"
                                :other="true"
                                @focus.stop="focusInput('interest_type', false, 'Interest Type Drop-Down')" />
                        </div>
                        <input
                            type="text"
                            v-if="otherType"
                            :class="{ error_input: errors.interest_type }"
                            v-model.trim="interest_type_other"
                            @focus.stop="focusInput('interest_type', false, 'Email Address Input')"
                            @blur="blurInput('interest_type')"
                            class="inp is_new" />
                        <validate-error :error="errors.interest_type"></validate-error>
                    </div>
                    <div class="sample-item line sample-item-comments">
                        <div class="tit-num">
                            <div class="sample-item-title">
                                <span>{{ $c("form.form.comment") }}</span>
                            </div>
                            <div class="numb">
                                <span :class="{ active: form.comments.length === 5000 }"> {{ form.comments.length }} </span> <span>/5000</span>
                            </div>
                        </div>
                        <textarea
                            v-model.trim="form.comments"
                            :placeholder="$c('form.form.comments_placeholder')"
                            maxlength="5000"
                            class="textarea is_new"
                            @focus.stop="focusInput('comments', false, 'Comments(Optional) Input')"
                            @change="blurInput('comments')"></textarea>
                        <div class="input-item-number">
                            <validate-error :error="errors.comments"></validate-error>
                        </div>
                    </div>
                    <div class="sample-item line">
                        <label class="agreement">
                            <input type="checkbox" v-model="check1" @change="blurInput('check1')" />
                            <!-- <span class="checkbox-box"></span> -->
                            <p v-if="website == 'jp'">
                                <a @click.stop="openAgreement" href="javascript:;">{{ $c("single.sampleApplication.agreement_choose.link01") }}</a
                                >{{ $c("single.sampleApplication.agreement.tit") }}
                            </p>
                            <p v-else-if="website == 'de'" v-html="de_or_fr_agree_txt01"></p>
                            <p v-else-if="['cn', 'hk', 'tw', 'mo'].includes(website)">
                                {{ $c("form.form.agree_txt01") }} <a @click.stop="openAgreement" href="javascript:;">{{ $c("single.sampleApplication.agreement.tit") }}</a
                                >。
                            </p>
                            <p v-else>
                                {{ $c("form.form.agree_txt01") }} <a @click.stop="openAgreement" href="javascript:;">{{ $c("single.sampleApplication.agreement.tit") }}</a
                                >.
                            </p>
                        </label>
                        <validate-error :error="errors.check1"></validate-error>
                    </div>
                    <div class="sample-item line protocol">
                        <label class="agreement" v-if="isDifferentArgreement">
                            <input type="checkbox" v-model="check2" @change="blurInput('check2')" />
                            <!-- <span class="checkbox-box"></span> -->
                            <p
                                v-html="
                                    $c('form.validate.aggree_policy_new')
                                        .replace('AAAA', localePath({ name: 'privacy-notice' }))
                                        .replace('BBBB', localePath({ name: 'terms-of-use' }))
                                "></p>
                        </label>
                        <div
                            class="agreement_wrap"
                            v-if="!isDifferentArgreement"
                            v-html="
                                $c('single.ContactSales.submitTip')
                                    .replace('XXXX1', localePath({ name: 'privacy-policy' }))
                                    .replace('XXXX2', localePath({ name: 'terms-of-use' }))
                            "></div>
                        <validate-error :error="errors.check2"></validate-error>
                    </div>
                </div>
            </div>
            <div>
                <g-recaptcha ref="grecaptcha" @getValidateCode="getValidateCode"></g-recaptcha>
            </div>
        </div>
        <div class="sbtn-box sample-item line" slot="footer">
            <fs-button id="sample_submit" :text="$c('form.form.submit')" @click="submitFu" :loading="sbtn_loading" htmlType="submit"></fs-button>
        </div>
    </fs-popup-new>
</template>

<script>
import { mapState, mapGetters } from "vuex"
import { email_valdate, phone_validate, cn_all_phone } from "@/constants/validate.js"
import FsButton from "@/components/FsButton/FsButton.vue"
import FsSelect from "@/components/FsSelect/FsSelect.vue"
import ValidateError from "@/components/ValidateError/ValidateError.vue"
import TelCode from "@/components/TelCode/TelCode.vue"
import SelectCountry from "@/components/SelectCountry/SelectCountry.vue"
import QtyBox from "@/components/QtyBox/QtyBox.vue"
import FsPopupNew from "@/components/FsPopupNew/FsPopupNew.vue"
import RegionSelect from "@/components/RegionSelect/RegionSelect"
import GRecaptcha from "@/components/GRecaptcha/GRecaptcha"
import { isNeedGrecaptcha } from "@/util/grecaptchaHost"
export default {
    name: "SampleRequest",
    components: {
        FsButton,
        FsSelect,
        ValidateError,
        QtyBox,
        TelCode,
        SelectCountry,
        FsPopupNew,
        RegionSelect,
        GRecaptcha,
    },
    props: {
        productId: {
            type: [String, Number],
            default: "",
        },
        show: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {
            product_list: [],
            sbtn_loading: false,
            sbtn_pro_loading: false,
            product_sample: {
                products_id: "",
                products_num: 1,
            },
            form: {
                entry_firstname: "",
                entry_lastname: "",
                email_address: "",
                entry_telephone: "",
                country_code: "us",
                interest_type: "",
                comments: "",
            },
            check1: false,
            check2: false,
            errors: {
                products_id: "",
                entry_firstname: "",
                entry_lastname: "",
                email_address: "",
                entry_telephone: "",
                country_code: "",
                interest_type: "",
                check1: "",
                check2: "",
                comments: "",
            },
            disabled: false,

            interest_type_options: this.$c("form.form.interest_type_options"),
            code: "",
            loading: false,
            recaptchaTp: false,
            recaptchaVal: "",
            otherType: false,
            interest_type_other: "",
        }
    },
    computed: {
        ...mapState({
            isMobile: (state) => state.device.isMobile,
            userInfo: (state) => state.userInfo.userInfo,
            website: (state) => state.webSiteInfo.website,
            resourcePage: (state) => state.device.resource_page,
            country_code: (state) => state.webSiteInfo.iso_code,
            select_country_id: (state) => state.selectCountry.select_country_id,
            screenWidth: (state) => state.device.screenWidth,
        }),
        isDifferentArgreement() {
            // return ["de", "de-en", "dn", "uk", "fr", "it", "es"].includes(this.website)
            return true
        },
        ...mapGetters({
            isCn: "webSiteInfo/isCn",
            isShowState: "selectCountry/isShowState",
            country_label: "selectCountry/country_label",
        }),
        // 不同国家的姓名前后表达顺序不一样
        isDifferentCountriesName() {
            return ["cn", "hk", "tw", "mo"].includes(this.website)
        },
        jp_agreement_choose() {
            return this.$c("single.sampleApplication.agreement_choose.txt02")
                .replace("/policies/privacy_policy.html", this.localePath({ path: "/policies/privacy_policy.html" }))
                .replace("/policies/terms_of_use.html", this.localePath({ path: "/policies/terms_of_use.html" }))
        },
        de_or_fr_agree_txt01() {
            return this.$c("form.form.agree_txt01").replace(/xxxxx/, `<a onclick="openAgreement()" href="javascript:;">${this.$c("single.sampleApplication.agreement.tit")}</a>`)
        },
        de_or_fr_agree_txt02() {
            let msg = this.$c("form.form.agree_txt02")
            msg = msg.replace(/xxxxx/, `<a href="${this.localePath({ name: "privacy-policy" })}" target="_blank">${this.$c("form.form.privacy_policy")}</a>`)
            msg = msg.replace(/vvvvv/, `<a href="${this.localePath({ name: "terms-of-use" })}" target="_blank">${this.$c("form.form.terms_of_use")}</a>`)
            return msg
        },
    },
    created() {
        if (this.userInfo) {
            console.log(this.userInfo)
            this.form.email_address = this.userInfo.customers_email_address
            this.form.entry_firstname = this.userInfo.customers_firstname
            this.form.entry_lastname = this.userInfo.customers_lastname
            this.form.entry_telephone = this.userInfo.customers_telephone
            // entry_telephone
            this.disabled = true
        }
        // if (this.productId) {
        //     this.addProduct(this.productId, 1, true)
        // }
    },
    mounted() {
        let _this = this
        // 添加环境检查
        if (typeof window !== "undefined") {
            window.openAgreement = _this.openAgreement
        }
        this.form.country_code = this.country_code
    },
    methods: {
        changeCode(code) {
            this.code = code
        },
        handleBuried(label) {
            // window.dataLayer &&
            //     window.dataLayer.push({
            //         event: "uaEvent",
            //         eventCategory: "Product Service_Sample Application Page",
            //         eventAction: "sample_application",
            //         eventLabel: label,
            //         nonInteraction: false,
            //     })
        },
        addProduct(pid, num, isInit = false) {
            if (!pid) {
                this.errors.products_id = this.$c("form.form.errors.products_id_error")
                return
            }

            if (isInit) {
                this.loading = true
            } else {
                this.sbtn_pro_loading = true
            }
            this.$axios
                .post("/api/addSample", {
                    products_id: pid,
                    products_num: num,
                })
                .then((res) => {
                    if (isInit) {
                        this.loading = false
                    } else {
                        this.sbtn_pro_loading = false
                    }
                    const { products_id: product_ids, products_desc, images_arr } = res.data
                    const findIndex = this.product_list.findIndex((i) => i.product_ids === product_ids)
                    if (findIndex === -1) {
                        const productItem = {
                            product_ids,
                            products_name: products_desc.products_name,
                            images_arr,
                            product_nums: num,
                        }

                        this.product_list.push(productItem)
                    } else {
                        this.product_list[findIndex].product_nums += num
                    }

                    this.errors.products_id = ""
                    this.product_sample = {
                        products_id: "",
                        products_num: 1,
                    }
                })
                .catch((err) => {
                    if (isInit) {
                        this.loading = false
                    } else {
                        this.sbtn_pro_loading = false
                    }
                    if (err.code == 400) {
                        this.errors.products_id = err.message
                    } else if (err.code == 422) {
                        this.errors.products_id = err.errors.products_id
                    }
                })
        },
        changeCountry(item) {
            this.form.country_code = item.iso_code
        },
        telInput(inp) {
            this.telChange(inp)
        },
        telChange(inp) {
            this.form.entry_telephone = inp
            this.blurInput("entry_telephone")
        },
        qtyChange(num, index) {
            this.product_list[index].product_nums = num
        },
        deletePro(i) {
            this.product_list.splice(i, 1)
        },
        focusInput(attr, type, label) {
            type ? (this[attr + "_error"] = "") : (this.errors[attr + "_error"] = "")
            label && this.handleBuried(label)
        },
        blurCheck(attr) {
            if (!this.form[attr]) {
                this.blurInput(attr)
            }
        },
        blurInput(attr) {
            const val = this.form[attr] || ""
            const map = {
                products_id() {
                    if (this.product_list.length === 0) return this.$c("form.form.errors.products_id_error")
                },

                entry_firstname() {
                    if (!val.replace(/^\s+|\s+$/g, "")) {
                        return this.$c("form.form.errors.entry_firstname_error")
                    } else if (val.length > 40) {
                        return this.$c("form.validate.first_name.first_name_max")
                    }
                },
                entry_lastname() {
                    if (!val.replace(/^\s+|\s+$/g, "")) {
                        return this.$c("form.form.errors.entry_lastname_error")
                    } else if (val.length > 40) {
                        return this.$c("form.validate.last_name.last_name_max")
                    }
                },

                email_address() {
                    if (["cn", "hk", "tw", "mo"].includes(this.website)) return
                    if (!val.replace(/^\s+|\s+$/g, "")) return this.$c("form.form.errors.email_business_error")
                    if (!email_valdate.test(val)) return this.$c("form.form.errors.email_address_error01")
                },
                entry_telephone() {
                    if (!val.replace(/^\s+|\s+$/g, "")) return this.$c("form.form.errors.entry_telephone_error")
                    if (["CN", "HK", "TW", "MO"].includes(this.form.country_code)) {
                        if (!cn_all_phone.test(val)) return this.$c("form.form.errors.entry_telephone_error01")
                    } else {
                        if (val.length > 0 && val.length < 6) {
                            return this.$c("pages.NetTermsApplication.validate.phone.phone_min")
                        } else if (val.length > 40) {
                            return this.$c("pages.NetTermsApplication.validate.phone.phone_validate")
                        } else {
                            return ""
                        }
                    }
                },
                interest_type() {
                    if (!val.replace(/^\s+|\s+$/g, "")) return this.$c("form.form.errors.interest_type_error")
                    if (["cn", "hk", "tw", "mo"].includes(this.website) && val == this.interest_type_options.at(-1) && !this.interest_type_other.replace(/^\s+|\s+$/g, ""))
                        return this.$c("form.form.errors.interest_type_error")
                },
                check1() {
                    if (!this.check1) return this.$c("form.form.errors.check1_error")
                },
                check2() {
                    if (!this.check2 && this.isDifferentArgreement) return this.$c("form.form.errors.check2_error")
                },
                comments() {
                    return ""
                },
            }
            this.errors[attr] = (map[attr] && map[attr].apply(this)) || ""
        },
        verifyWrapper() {
            const errorMap = Object.keys(this.errors)
            errorMap.forEach((key) => {
                this.blurInput(key)
            })
            return errorMap.some((attr) => this.errors[attr])
        },
        submitFu() {
            if (this.sbtn_loading || this.verifyWrapper()) {
                return
            }
            // 添加环境检查
            if (typeof window !== "undefined" && isNeedGrecaptcha(window.location.hostname)) {
                if (!this.recaptchaTp && this.$refs.grecaptcha) {
                    this.$refs.grecaptcha.clickRecaptcha()
                    return
                }
            }
            this.sbtn_loading = true
            this.fetchProductsSubmit()
        },
        async fetchProductsSubmit() {
            try {
                let params = {
                    ...this.form,
                    entry_telephone: `${this.code.replace("+", "")} ${this.form.entry_telephone}`,
                    product_ids: this.product_list.map((i) => i.product_ids),
                    product_nums: this.product_list.map((i) => i.product_nums),
                    country_id: this.select_country_id,
                    // resource_page: this.resourcePage,
                }
                if (this.$refs.regionSelect) {
                    params.state = this.$refs.regionSelect.state || ""
                }
                if (params.interest_type == this.interest_type_options.at(-1)) {
                    params.interest_type = this.interest_type_other
                }

                const res = await this.$axios.post("/api/submit_tw_sample_apply", params, { headers: { "g-recaptcha-response": this.recaptchaVal } })

                if (res.code === 200 && res.status === "sensiWords") {
                    for (let key in res.errors) {
                        this.errors[key] = this.$c("form.form.errors.sensiWords")
                    }
                    this.sbtn_loading = false
                    return
                }
                this.initGrecaptcha()
                this.buriedPointWrapper("Submit Success", "submit_application")
                this.$emit("submitSuccess", res.data.caseNumber)
            } catch (error) {
                this.initGrecaptcha()
                this.buriedPointWrapper("Submit Fail", "submit_application")
                // 添加环境检查
                if (typeof window !== "undefined" && isNeedGrecaptcha(window.location.hostname)) {
                    if (error.code === 409 && this.$refs.grecaptcha) {
                        this.$refs.grecaptcha.grecaptchaError = this.$c("pages.Login.regist.grecaptcha_error")
                    }
                }
                if (error.code === 403 || error.code === 400) {
                    this.$message.error(error.message)
                }
            }
            this.sbtn_loading = false
        },

        openAgreement() {
            this.$emit("showAgreement")
        },
        buriedPointWrapper(eventLabel, eventAction) {
            // 添加环境检查
            if (typeof window !== "undefined" && window.dataLayer) {
                window.dataLayer.push({
                    event: "uaEvent",
                    eventCategory: `productDetailPage_${this.productId}`,
                    eventAction,
                    eventLabel,
                    nonInteraction: false,
                })
            }
        },
        // 谷歌人机校验
        getValidateCode(val) {
            this.recaptchaTp = val.recaptchaTp
            if (val.recaptchaTp) {
                this.recaptchaVal = val.recaptchaVal
                this.submitFu()
            }
        },
        changeType(e) {
            // console.log(e, );
            if (["cn", "hk", "tw", "mo"].includes(this.website)) {
                if (this.interest_type_options.at(-1) == e) {
                    this.otherType = true
                } else {
                    this.otherType = false
                    this.interest_type_other = ""
                }
            }
            this.blurInput("interest_type")
        },
        // 初始化谷歌验证
        initGrecaptcha() {
            this.recaptchaTp = false
            this.recaptchaVal = ""
        },
    },
}
</script>

<style lang="scss" scoped>
::v-deep .fs-popup.is_new {
    .fs-popup-body {
        padding: 0;
    }
    .fs-popup-footer {
        padding-top: 0;
    }
}
::v-deep .select-country .country-wrap .country-box {
    max-height: 285px;
    @include mediaM {
        max-height: 360px;
    }
}
::v-deep.tel-code .menu .menu-list {
    max-height: 135px;

    @include mediaM {
        max-height: 328px;
    }
}
.sbtn-box {
    display: flex;
    justify-content: flex-end;
    @include mediaM {
        ::v-deep .fs-button {
            width: 100%;
        }
    }
}
::v-deep .fs-popup.is_new .fs-popup-header {
    padding: 24px 24px 0;
    @media (max-width: 768px) {
        padding: 24px 16px 0;
    }
}
::v-deep .fs-popup.is_new .fs-popup-body {
    @media (max-width: 768px) {
        padding: 20px 16px 20px 16px;
    }
}
::v-deep .fs-popup.is_new .fs-popup-footer {
    padding: 24px;
    @media (max-width: 768px) {
        padding: 16px;
    }
}
.sample-request {
    color: $textColor1;
    ::v-deep .validate_error {
        .iconfont_error {
            margin-top: 3px;
        }
    }
    .sample_main {
        padding: 16px 24px 24px 24px;
        // max-height: calc(100vh - 282px);
        @include scrollY;
        .box-title {
            margin-bottom: 12px;
            @include font16();
            font-weight: 600;
            color: $textColor1;
        }
        .product-box {
            margin-bottom: 24px;
            .add-pro-box {
                display: flex;
                align-items: flex-start;
                > img {
                    width: 104px;
                    height: 42px;
                    margin-top: 22px;
                }
                .add-pro-content {
                    margin: 0 12px;
                    .input-title {
                        @include font14();
                        margin-bottom: 4px;
                        color: #707070;
                    }
                    .input-box {
                        display: flex;
                        align-items: center;
                        .prefix {
                            border-radius: 4px 0px 0px 4px;
                            top: 1px;
                            left: 1px;
                            background: #f7f7f7;
                            width: 48px;
                            height: 42px;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            // border: 1px solid #e5e5e5;
                            border-right: 0;
                            flex-shrink: 0;
                        }
                        > input {
                            width: 131px;
                            border-radius: 0 4px 4px 0;
                        }
                        ::v-deep .fs-button {
                            margin-left: 12px;
                        }
                    }
                }
            }
            .product-list {
                background: #fafafb;
                padding: 0 24px;
                margin-top: 20px;
                .product-item {
                    display: flex;
                    align-items: flex-start;
                    padding: 20px 0;
                    &:not(:first-child) {
                        border-top: 1px solid #e5e5e5;
                    }
                    img {
                        width: 80px;
                        height: 80px;
                        mix-blend-mode: multiply;
                    }
                    .txt-box {
                        flex: 1;
                        margin: 0 16px 0 20px;
                        .tit {
                            @include font14();
                            color: #19191a;
                            @include txt-more-hid(3);
                        }
                        .txt {
                            @include font14();
                            color: #707070;
                            margin-top: 12px;
                        }
                    }

                    .handle-box {
                        display: flex;
                        align-items: center;
                        justify-content: space-between;
                        // ::v-deep .qty-box {
                        //     background-color: #fff;
                        // }

                        .del-box {
                            margin-left: 50px;
                            cursor: pointer;
                            .iconfont {
                                color: #707070;
                            }
                            &:hover {
                                .iconfont {
                                    color: #19191a;
                                }
                            }
                        }
                    }
                }
            }
        }

        .form {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            grid-gap: 12px;
            .tit-num {
                display: flex;
                justify-content: space-between;
                align-items: center;
                .numb {
                    color: $textColor3;
                    @include font12;

                    .active {
                        color: $textColor4;
                    }
                }
            }
            .sample-item {
                &.line {
                    grid-column: span 2;
                }

                &.sample-item-cn {
                    grid-area: 1/2/1/3;
                }

                .sample-item-title {
                    color: $textColor3;
                    @include font14();
                    margin-bottom: 4px;
                }
                // .inp {
                //     background: #f6f6f8;
                //     border: 0;
                // }
                // .inp:focus {
                //     border: 1px solid #19191a;
                // }
                // .inp:disabled {
                //     background: #f6f6f8;
                // }
                // .textarea {
                //     background: #f6f6f8;
                //     border: 0;
                //     border-radius: 4px;
                // }
                // .textarea:focus {
                //     border: 1px solid #19191a;
                // }
                // :deep(.tel-code) {
                //     .code {
                //         background: #f6f6f8;
                //         border: 0;
                //         border-radius: 4px 0 0 4px;
                //     }
                //     .tel {
                //         background: #f6f6f8;
                //         border: 0;
                //         border-radius: 0 4px 4px 0;
                //     }
                //     .tel:focus {
                //         border: 1px solid #19191a;
                //     }
                // }
                // :deep(.select-country) {
                //     .select-country-active {
                //         background: #f6f6f8;
                //         border: 0;
                //         border-radius: 4px;
                //     }
                // }
                // :deep(.dropdown) {
                //     background-color: #f6f6f8;
                //     .select-label {
                //         border: 0;
                //         border-radius: 4px;
                //     }
                // }

                /* .select-box {
                    margin-bottom: 16px;
                } */

                .agreement {
                    width: 100%;
                    display: flex;
                    align-items: flex-start;
                    margin-top: 4px;
                    input[type="checkbox"] {
                        font-size: 14px;
                        width: 14px;
                        height: 14px;
                        margin-top: 4px;
                        margin-right: 8px;
                        // display: none; // ✅ 完全隐藏原始 checkbox
                    }

                    // .checkbox-box {
                    //     width: 14px;
                    //     height: 14px;
                    //     border: 1px solid #d8d8d8;
                    //     background-color: #fff;
                    //     border-radius: 2px;
                    //     position: relative;
                    //     transition: all 0.2s ease-in-out;
                    //     margin-right: 8px;
                    //     flex-shrink: 0; // ✅ 防止被压缩
                    // }
                    input[type="checkbox"]:checked + .checkbox-box {
                        background-color: #707070;
                        border-color: #707070;
                    }

                    input[type="checkbox"]:checked + .checkbox-box::after {
                        content: "";
                        position: absolute;
                        left: 3px;
                        top: 0px;
                        width: 4px;
                        height: 8px;
                        border: solid white;
                        border-width: 0 2px 2px 0;
                        transform: rotate(45deg);
                    }
                    > p {
                        @include font14;
                        color: $textColor3;
                        a {
                            color: #0060bf;
                        }
                    }
                    &:hover {
                        cursor: pointer;
                        input[type="checkbox"] {
                            &:before {
                                color: #707070;
                            }
                        }
                    }
                }
                .agreement_wrap {
                    @include font14;
                    color: $textColor3;
                }
            }
        }
    }

    /* .error_input {
        @include errorInput;
    } */
    ::v-deep .validate_error .error_info {
        @include font12;
    }

    @include mediaM {
        .sample_main {
            .product-box {
                .add-pro-box {
                    // margin-top: 20px;
                    align-items: flex-end;
                    > img {
                        display: none;
                    }
                    .add-pro-content {
                        flex: 1;
                        margin: 0 10px 0 0;
                        .input-box {
                            width: 100%; // 撑满整行
                            display: flex;
                            align-items: center;

                            > input {
                                flex: 1; // 自动撑开剩余空间
                                border-radius: 0 2px 2px 0;
                            }
                            ::v-deep .fs-button {
                                margin-left: 12px;
                                white-space: nowrap;
                                flex-shrink: 0; // 防止按钮被压缩
                                height: 42px;
                            }
                        }
                    }
                }
                .product-list {
                    padding: 0 20px;
                    .product-item {
                        flex-wrap: wrap;
                        .txt-box {
                            margin-right: 0;
                        }
                        .handle-box {
                            flex: 0 0 100%;
                            padding-left: 100px;
                            margin-top: 12px;
                        }
                    }
                }
            }
            .form {
                .sample-item {
                    grid-column: span 2;
                    .agreement {
                        align-items: baseline !important;
                    }
                }
            }
        }
    }
}
</style>
