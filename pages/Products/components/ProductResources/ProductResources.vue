<template>
    <!-- 
file_type
1代表video,
2代表blog, 
3代表case study
4、5 不确定
6代表 download 专用 -->
    <div class="product_resources" v-if="data">
        <div class="resource_ctn" v-show="data.download && data.download.length">
            <client-only>
                <!-- <div class="resource_box">
                    <div class="resource_item" v-for="index in isMobile ? (data.download.length > 4 ? 4 : data.download.length) : data.download.length" :key="index"> -->
                <!-- <a
                            class="resource_link"
                            v-if="data.download[index - 1].file_type === 1"
                            href="javascript:;"
                            target="_blank"
                            @click.stop="
                                showVideoPopup(data.download[index - 1])
                                gaEvent('video', data.download[index - 1], 'download')
                            ">
                            <img class="icon_resource_img" v-if="data.cmsFlag" :src="data.download[index - 1].iconUrl" />
                            <span v-else :class="`icon_resource ${data.download[index - 1].icon_position}`"></span>
                            <div class="resource_text">
                                <span class="text" v-html="data.download[index - 1].file_name"></span>
                                <span class="new_tag" v-if="data.download[index - 1].is_new_tag">New</span>
                            </div>
                        </a> -->
                <!-- 将type = 2 和type = 6 分开 -->
                <!-- <a
                            class="resource_link types"
                            v-if="data.download[index - 1].file_type === 2 || data.download[index - 1].file_type === 6"
                            :href="
                                data.download[index - 1].file_type === 6 && data.download[index - 1].is_datasheet
                                    ? localePath({
                                          path: '/products_support/preview.html',
                                          query: {
                                              categories_id: data.download[index - 1].category_id,
                                              files_id: data.download[index - 1].resourceId,
                                              model_name: data.download[index - 1].module,
                                              is_datasheet: data.download[index - 1].is_datasheet,
                                          },
                                      })
                                    : data.download[index - 1].file_url
                            "
                            target="_blank"
                            @click.stop="gaEvent('pdf', data.download[index - 1], 'download')">
                            <img class="icon_resource_img" v-if="data.cmsFlag" :src="data.download[index - 1].iconUrl" />
                            <span v-else :class="`icon_resource ${data.download[index - 1].icon_position}`"></span>
                            <div class="resource_text">
                                <span class="text" v-html="data.download[index - 1].file_name"></span>
                                <span class="new_tag" v-if="data.download[index - 1].is_new_tag">New</span>
                            </div>
                        </a> -->
                <!-- <a
                            class="resource_link"
                            v-if="data.download[index - 1].file_type === 3"
                            :href="localePath({ name: 'case-detail', params: { id: data.download[index - 1].file_url.replace(/(.*\/)*([^.]+).*/gi, '$2') } })"
                            @click.stop="gaEvent('case-studies', data.download[index - 1], 'download')"
                            target="_blank">
                            <img class="icon_resource_img" v-if="data.cmsFlag" :src="data.download[index - 1].iconUrl" />
                            <span v-else :class="`icon_resource ${data.download[index - 1].icon_position}`"></span>
                            <div class="resource_text">
                                <span class="text" v-html="data.download[index - 1].file_name"></span>
                                <span class="new_tag" v-if="data.download[index - 1].is_new_tag">New</span>
                            </div>
                        </a> -->
                <!-- <a
                            class="resource_link"
                            v-if="data.download[index - 1].file_type === 4 || data.download[index - 1].file_type === 5"
                            :href="$handleLink(data.download[index - 1].file_url).url"
                            target="_blank"
                            @click.stop="gaEvent('link', data.download[index - 1], 'download')">
                            <img class="icon_resource_img" v-if="data.cmsFlag" :src="data.download[index - 1].iconUrl" />
                            <span v-else :class="`icon_resource ${data.download[index - 1].icon_position}`"></span>
                            <div class="resource_text">
                                <span class="text" v-html="data.download[index - 1].file_name"></span>
                                <span class="new_tag" v-if="data.download[index - 1].is_new_tag">New</span>
                            </div>
                        </a> -->
                <!-- </div>
                </div> -->
                <div class="tag_box" v-if="data.downloadTag && data.downloadTag.length && false">
                    <div
                        class="tag"
                        :class="{ active_tag: activeIndex === index }"
                        v-for="(item, index) in data.downloadTag"
                        :key="index"
                        :tabindex="activeIndex === index ? -1 : 0"
                        @keyup.enter.stop="activeIndexChange(index)"
                        @click="activeIndexChange(index)">
                        {{ item.style_name + ` (${item.file_num})` }}
                    </div>
                </div>
                <div class="resource_box" v-if="data.download && data.download.length">
                    <div class="resource_item" v-for="(item, index) in filterListNew" :key="index">
                        <a class="resource_link" :href="returnNewHref(item, item.file_type)" target="_blank" tabindex="0" @click.stop="showVideoPopup(item), gaEvent(item.file_type, item, 'download')">
                            <img class="icon_resource_img" v-if="data.cmsFlag" :src="item.iconUrl" />
                            <span v-else :class="`icon_resource ${item.icon_position}`"></span>
                            <div class="resource_text">
                                <div class="text_name">
                                    <div :class="['text_left', { noTag: !item.is_new_tag }]" :title="item.file_name">{{ item.file_name }}</div>
                                    <div class="text_right" v-if="item.is_new_tag && 1">
                                        <FsTextTag :type="item.is_new_tag && 1" v-if="item.is_new_tag && 1" />
                                    </div>
                                </div>
                            </div>
                        </a>
                    </div>
                </div>
                <div class="more-file-btn-pc" v-if="data.download && data.download.length > 12">
                    <span class="btn" tabindex="0" @keyup.enter.stop="isShowMore = !isShowMore" @click.stop="isShowMore = !isShowMore">
                        <span class="info">{{ isShowMore ? $c("pages.Products.See_less") : $c("pages.Products.See_more") }}</span>
                        <span class="iconfont iconfont_down" :class="{ rotate: isShowMore }">&#xe704;</span>
                    </span>
                </div>
                <div class="more-file-btn-m" v-if="data.download && data.download.length > 4">
                    <span class="btn" tabindex="0" @keyup.enter.stop="isShowMore = !isShowMore" @click.stop="isShowMore = !isShowMore">
                        <span class="info" @click="gaEventMore">{{ isShowMore ? $c("pages.Products.See_less") : $c("pages.Products.See_more") }}</span>
                        <span class="iconfont iconfont_down" :class="{ rotate: isShowMore }">&#xe704;</span>
                    </span>
                </div>
                <div class="more-file-btn" v-if="data.downloadTag && data.downloadTag[this.activeIndex] && data.downloadTag[this.activeIndex].children && data.downloadTag[this.activeIndex].children.length > 4">
                    <span class="btn" tabindex="0" @keyup.enter.stop="isShowMore = !isShowMore" @click.stop="isShowMore = !isShowMore">
                        <span class="info" @click="gaEventMore">{{ isShowMore ? $c("pages.Products.See_less") : $c("pages.Products.See_more") }}</span>
                        <span class="iconfont iconfont_down" :class="{ rotate: isShowMore }">&#xe704;</span>
                    </span>
                </div>
                <div class="download_more" v-if="data.downloadMore">
                    <nuxt-link
                        @click.native="gaEventMore"
                        tabindex="0"
                        :to="
                            localePath({
                                path: '/products_support/search.html',
                                query:
                                    data.downloadMore.id && data.downloadMore.id.match(/(\d+)[^\d]*$/g)
                                        ? Object.assign({ id: data.downloadMore.id.match(/(\d+)[^\d]*$/g)[0] }, isObject(data.downloadMore.query) ? data.downloadMore.query : {})
                                        : Object.assign(isObject(data.downloadMore.query) ? data.downloadMore.query : {}),
                            })
                        "
                        class="more_btn"
                        target="_blank">
                        <span class="info">{{ $c("pages.Products.Learn_more") }}</span>
                        <i class="iconfont">&#xe703;</i>
                    </nuxt-link>
                </div>
            </client-only>
        </div>
        <fs-popup :show="video_data.show" :title="video_data.title" @close="closeVideoPopup">
            <div class="video_box">
                <iframe class="iframe" v-if="video_data.src" :src="video_data.src" frameborder="0" allow="autoplay; encrypted-media" allowfullscreen=""></iframe>
            </div>
        </fs-popup>
    </div>
</template>

<script>
import FsPopup from "@/components/FsPopup/FsPopup.vue"
import FsTextTag from "@/components/FsTag/FsTextTag"
import { mapState } from "vuex"
import { isObject } from "@/util/types"

export default {
    name: "ProductResources",
    components: {
        FsPopup,
        FsTextTag,
    },
    props: {
        data: {
            type: Object,
            default() {
                return {}
            },
        },
        crumb: {
            type: Array,
            default() {
                return []
            },
        },
    },
    data() {
        return {
            video_data: {
                show: false,
                title: "",
                src: "",
            },
            activeIndex: 0,
            isShowMore: false,
        }
    },
    watch: {
        data: {
            handler: function (v, o) {
                if (v.downloadTag && v.downloadTag.length) {
                    v.downloadTag.forEach((item) => {
                        item.children = []
                        if (item.style_id === -1) {
                            item.children = v.download
                        } else {
                            v.download.forEach((val) => {
                                if (val.file_style_id === item.style_id) {
                                    item.children.push(val)
                                }
                            })
                        }
                        item.file_num = item.children.length
                    })
                }
            },
            immediate: true,
        },
    },
    computed: {
        ...mapState({
            isMobile: (state) => state.device.isMobile,
        }),
        filterList() {
            if (this.isShowMore) {
                return this.data.downloadTag[this.activeIndex].children
            } else {
                return this.data.downloadTag[this.activeIndex].children.slice(0, 4)
            }
        },
        filterListNew() {
            if (this.isShowMore) {
                return this.data.download
            } else if (this.isMobile) {
                return this.data.download.slice(0, 4)
            } else {
                return this.data.download.slice(0, 12)
            }
        },
    },
    mounted() {},
    methods: {
        isObject,
        showVideoPopup(item) {
            if (item.file_type !== 1) return
            this.video_data.show = true
            this.video_data.title = item.file_name
            this.video_data.src = item.file_url
        },
        closeVideoPopup() {
            this.video_data.show = false
            this.video_data.title = ""
            this.video_data.src = ""
        },
        returnNewHref(item, type) {
            if (type === 6 || type === 2) {
                if (type === 6 && item?.documentUrl) {
                    if (item.documentUrl.includes("http")) {
                        return item.documentUrl
                    } else {
                        return this.localePath({
                            path: documentUrl,
                        })
                    }
                } else {
                    return item.file_url
                }
            } else if (type === 3) {
                return this.localePath({ name: "case-detail", params: { id: item.file_url.replace(/(.*\/)*([^.]+).*/gi, "$2") } })
            } else if (type === 4 || type === 5) {
                return this.$handleLink(item.file_url).url
            } else if (type === 1) {
                return "javascript:;"
            }
        },
        gaEvent(t, item, s) {
            let val = ""
            if (t == 1) {
                val = "video"
                e.preventDefault()
            } else if (t == 3) {
                val = "case-studies"
            } else if ([2, 6].includes(t)) {
                val = "pdf"
                if (t === 6 && item?.is_soft) {
                    this.$axios
                        .post("/api/pica8Download", {
                            products_id: this.$route.params.id,
                        })
                        .then((res) => {})
                        .catch((error) => {
                            console.log(error)
                        })
                }
            } else {
                val = "link"
            }
            if (window.dataLayer) {
                window.dataLayer.push({
                    event: "uaEvent",
                    eventCategory: `productDetailPage_${this.$route.params.id || ``}`,
                    eventAction: "resource_opreate",
                    eventLabel: `${s}_${val}_${item.file_name}`,
                    nonInteraction: false,
                })
            }
        },
        gaEventMore() {
            console.log(this.crumb)
            if (window.dataLayer) {
                window.dataLayer.push({
                    event: "uaEvent",
                    eventCategory: `productDetailPage_${this.$route.params.id || ``}`,
                    eventAction: "resource_opreate",
                    eventLabel: "See more",
                    nonInteraction: false,
                })
            }
        },
        activeIndexChange(i) {
            this.activeIndex = i
            this.isShowMore = false
        },
    },
}
</script>

<style lang="scss" scoped>
.product_resources {
    padding-bottom: 40px;
    position: relative;

    @media (max-width: 960px) {
        padding-bottom: 24px;
        // border-top: 1px solid #e5e5e5;

        // border-bottom: 1px solid #e5e5e5;
        .resource_ctn {
            // padding: 0 16px;
        }
    }

    .tag_box {
        display: flex;
        flex-wrap: wrap;
        margin-top: 16px;

        .tag {
            @include font14;
            color: #19191a;
            height: 28px;
            line-height: 28px;
            padding: 0 12px;
            background: #f7f7f7;
            margin: 12px 12px 0 0;
            border-radius: 4px;
            position: relative;

            &::before {
                content: " ";
                display: block;
                width: 100%;
                height: 100%;
                top: 0;
                left: 0;
                bottom: 0;
                position: absolute;
                right: 0;
                background: #19191a;
                opacity: 0;
                transition: all 0.3s;
                border-radius: 4px;
            }

            &:hover {
                cursor: pointer;

                &:not(.active_tag)::before {
                    opacity: 0.04;
                }
            }
        }

        .active_tag {
            color: #ffffff;
            background: #707070;
        }
    }

    @media (max-width: 960px) {
        // &::before {
        // 	content: ' ';
        // 	display: block;
        // 	width: 120%;
        // 	border-bottom: 1px solid #e5e5e5;
        // 	position: absolute;
        // 	top: 6px;
        // 	left: -3%;
        // }
    }
}

.related_ctn {
    @media (max-width: 960px) {
        display: none;
    }
}

.resource_title {
    padding-top: 17px;
    @include font16;
    color: $textColor1;
    font-weight: 600;

    @media (max-width: 960px) {
        display: none;
    }
}

.resource_box {
    width: 100%;
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    grid-gap: 20px;

    @media (max-width: 768px) {
        width: 100%;
        padding: 16px 16px 0;
        grid-template-columns: repeat(1, 1fr);
        grid-gap: 12px;
    }
}

.more-file-btn,
.more-file-btn-pc,
.more-file-btn-m {
    display: flex;
    justify-content: center;
    padding-top: 20px;
    margin-top: 28px;
    border-top: 1px solid #e5e5e5;
    color: #0060bf;

    @media (max-width: 768px) {
        margin-top: 0;
        padding-top: 24px;
        border-top: none;
    }

    .btn {
        cursor: pointer;
        display: inline-flex;
        align-items: center;

        &:hover {
            .info {
                text-decoration: underline;
            }
        }
    }

    .info {
        @include font13;
    }

    .iconfont_down {
        font-size: 12px;
        margin-left: 4px;
        transition: all 0.2s;

        &.rotate {
            transform: rotateX(-180deg);
        }
    }
}
.more-file-btn-pc {
    display: flex;
    border: none;
    margin: 0;
    @media (max-width: 960px) {
        display: none;
    }
}
.more-file-btn-m {
    display: none;
    @media (max-width: 960px) {
        display: flex;
    }
}

.download_more {
    display: flex;
    align-items: center;
    justify-content: center;
    padding-top: 20px;
    display: none;

    @media (max-width: 960px) {
        display: none;

        @media (max-width: 414px) {
            padding-top: 24px;
            text-align: center;
        }
    }

    .more_btn {
        cursor: pointer;
        text-decoration: none;

        .info {
            color: #0060bf;
            @include font14;
        }

        .iconfont {
            color: #0060bf;
            width: 12px;
            font-size: 12px;
            margin-left: 4px;
        }

        &:hover {
            .info {
                text-decoration: underline;
            }
        }

        @media (max-width: 960px) {
            display: inline-flex;
            justify-content: center;
            align-items: center;

            .iconfont {
                display: block;
            }
        }
    }
}

.resource_item {
    cursor: pointer;
    border-radius: 4px;
    padding: 16px;
    background-color: #fafbfb !important;
    transition: all 0.3s;
    display: flex;
    align-items: center;
    height: 76px;
    .resource_link {
        display: flex;
        align-items: center;
        width: 100%;
        text-decoration: none;
        color: $textColor1;

        .resource_text {
            flex: 1;
            overflow: hidden;

            .text_name {
                @include font14;
                // display: flex;
                // align-items: center;
                word-break: break-word;
                @include txt-more-hid(2);
            }

            .text_left {
                // max-width: calc(100% - 45px);
                // @include txt-hid;
                display: inline;
                &.noTag {
                    max-width: 100%;
                }

                @media (max-width: 768px) {
                    // max-width: calc(100% - 46px);

                    &.noTag {
                        max-width: 100%;
                    }
                }
            }

            .text_right {
                display: inline;
                &::before {
                    content: "";
                    display: inline-block;
                    height: 100%;
                    vertical-align: middle;
                }
            }

            .new_tag {
                display: inline-block;
                vertical-align: middle;
                float: left;
                width: 44px;
                height: 24px;
                margin-left: 4px;
                background-image: url("https://resource.fs.com/mall/generalImg/20230426155715gx0x94.png");
                background-repeat: no-repeat;
                background-size: 128px 82px;
                background-position: -16px -16px;
            }

            .iconfont {
                @include font14;
                display: inline-block;
                line-height: 24px;
                float: left;
                margin-left: 4px;
            }
        }
    }

    .icon_resource {
        display: inline-block;
        width: 20px;
        height: 20px;
        background-image: url(https://img-en.fs.com/includes/templates/fiberstore/images/new-pc-img/download-icon.png);
        background-repeat: no-repeat;
        flex-shrink: 0;
        margin-right: 8px;
    }

    .icon_resource_img {
        display: inline-block;
        width: 20px;
        height: 20px;
        flex-shrink: 0;
        margin-right: 10px;
    }

    .type_one {
        background-position: -7px -9px;
    }

    .type_two {
        background-position: -37px -9px;
    }

    .type_three {
        background-position: -67px -10px;
    }

    .type_four {
        background-position: -97px -9px;
    }

    .type_five {
        background-position: -128px -10px;
    }

    .type_six {
        background-position: -7px -71px;
    }

    .type_seven {
        background-position: -38px -70px;
    }

    .type_eight {
        background-position: -67px -70px;
    }

    .type_nine {
        background-position: -7px -132px;
    }

    .type_ten {
        background-position: -97px -70px;
    }

    .type_eleven {
        background-position: -37px -70px;
    }

    .type_wwelve {
        background-position: -127px -70px;
    }

    .text {
        @include font14;
        font-weight: 400;
        transition: all 0.3s;
        overflow: hidden;
    }

    &:hover {
        box-shadow: 0 15px 15px -10px rgba(0, 0, 0, 0.15);
        .resource_text {
            .text,
            .text_left {
                text-decoration: underline;
            }
        }
    }
}

.video_box {
    width: 880px;
    height: 495px;

    @media (max-width: 960px) {
        width: 100%;
        height: 210px;
        padding: 30px 16px 0;
    }

    .iframe {
        display: block;
        width: 100%;
        height: 100%;
    }
}

@media (max-width: 414px) {
    .resource_item {
        // margin-top: 12px;
        // .resource_link {
        //     padding: 16px 0;
        // }
        &:first-child {
            // margin-top: 4px;
        }
    }

    .product_resources {
        .tag_box {
            margin-top: 4px;
        }
    }
}
</style>
