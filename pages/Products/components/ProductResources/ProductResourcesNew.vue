<template>
    <!-- 
file_type
1代表video,
2代表blog, 
3代表case study
4、5 不确定
6代表 download 专用 -->
    <div class="product_resources" v-if="data">
        <div class="resource_ctn" v-show="processedDownloads && processedDownloads.length">
            <client-only>
                <!-- 添加分类标题切换，模仿NetworkComponent的样式 -->
                <div class="network_nav_box" :class="{ optionAll: showIndex && showIndex != 0 }" ref="network" v-if="data.isCategoryEnabled === 1 && categoryTitles.length > 1">
                    <div class="nav_box" :class="{ only: showIndex === 0, defOne: categoryTitles.length === 1 }">
                        <div
                            class="nav_item"
                            :class="{ active: index === currentCategoryIndex && (!showOptions || !isMobile), no_show: index >= showIndex && showIndex != null && showAll }"
                            v-for="(item, index) in categoryTitles"
                            :key="index"
                            @click="switchCategory(index, 1)">
                            {{ item }}
                        </div>
                    </div>

                    <div v-if="showAll" class="nav_box pc_show" ref="allBox" @mouseover="showOptions = true" @mouseleave="showOptions = false">
                        <div class="nav_item_all" :class="{ active: showOptions || all_text_default, active_line: all_text_default }">
                            <span>{{ `All (${categoryTitles.length - showIndex})` }}</span>
                            <i class="iconfont icon-arrow-down" :class="{ transform: showOptions }">&#xe704;</i>
                        </div>
                        <slide-down>
                            <div class="show_all_box" v-show="showOptions" ref="options">
                                <div class="show_all_list">
                                    <div
                                        class="show_all_item"
                                        :class="{ active: index === currentCategoryIndex, no_show: index < showIndex }"
                                        v-for="(item, index) in categoryTitles"
                                        :key="index"
                                        @click="switchCategory(index, 2, item)">
                                        <i class="iconfont icon-checked" v-if="index === currentCategoryIndex">&#xf050;</i>
                                        <i class="iconfont icon-check" v-else>&#xf051;</i>
                                        <span>{{ item }}</span>
                                    </div>
                                </div>
                            </div>
                        </slide-down>
                    </div>
                    <div v-if="showAll" class="nav_box m_show" ref="allBox" @click=";(showOptions = true), (activeIndexMobile = currentCategoryIndex)">
                        <div class="nav_item_all" :class="{ active: showOptions || all_text_default, active_line: all_text_default }">
                            <span>{{ `All (${categoryTitles.length - showIndex})` }}</span>
                            <i class="iconfont icon-arrow-down">&#xe704;</i>
                        </div>
                    </div>
                </div>

                <div class="resource_box">
                    <div class="resource_item" v-for="(item, index) in filterListNew" :key="index">
                        <a class="resource_link" :href="returnNewHref(item, item.file_type)" target="_blank" tabindex="0" @click.stop="showVideoPopup(item), gaEvent(item.file_type, item, 'download')">
                            <img class="icon_resource_img" v-if="data.cmsFlag" :src="item.iconUrl" />
                            <span v-else :class="`icon_resource ${item.icon_position}`"></span>
                            <div class="resource_text">
                                <div class="text_name">
                                    <div :class="['text_left', { noTag: !item.is_new_tag }]" :title="item.file_name">{{ item.file_name }}</div>
                                    <div class="text_right" v-if="item.is_new_tag">
                                        <FsTextTag :type="item.is_new_tag" v-if="item.is_new_tag" />
                                    </div>
                                </div>
                            </div>
                        </a>
                    </div>
                </div>
                <!-- PC更多 -->
                <div class="more-file-btn-pc" v-if="currentCategoryData && currentCategoryData.length > 12">
                    <span class="btn" tabindex="0" @keyup.enter.stop="isShowMore = !isShowMore" @click.stop="isShowMore = !isShowMore">
                        <span class="info">{{ isShowMore ? $c("pages.Products.See_less") : $c("pages.Products.See_more") }}</span>
                        <span class="iconfont iconfont_down" :class="{ rotate: isShowMore }">&#xe704;</span>
                    </span>
                </div>
                <!-- M更多 -->
                <div class="more-file-btn-m" v-if="currentCategoryData && currentCategoryData.length > 4">
                    <span class="btn" tabindex="0" @keyup.enter.stop="isShowMore = !isShowMore" @click.stop="isShowMore = !isShowMore">
                        <span class="info" @click="gaEventMore">{{ isShowMore ? $c("pages.Products.See_less") : $c("pages.Products.See_more") }}</span>
                        <span class="iconfont iconfont_down" :class="{ rotate: isShowMore }">&#xe704;</span>
                    </span>
                </div>
            </client-only>
        </div>
        <fs-popup :show="video_data.show" :title="video_data.title" @close="closeVideoPopup">
            <div class="video_box">
                <iframe class="iframe" v-if="video_data.src" :src="video_data.src" frameborder="0" allow="autoplay; encrypted-media" allowfullscreen=""></iframe>
            </div>
        </fs-popup>

        <fs-popup-new class="mobile_popup" :show="showOptions" :autoContent="true" @close=";(showOptions = false), (activeIndexMobile = 0)" :transition="isMobile ? 'fade' : 'slide-up'" :isMDrawer="true">
            <div class="top">
                <span>资源</span>
                <i @click="showOptions = false" class="iconfont icon-close">&#xf30a;</i>
            </div>
            <div class="center">
                <div class="center_item" :class="{ active: index === activeIndexMobile, no_show: index < showIndex }" v-for="(item, index) in categoryTitles" :key="index" @click="mobileHandleClick(index, item)">
                    <i class="iconfont icon-checked" v-if="index === activeIndexMobile">&#xf050;</i>
                    <i class="iconfont icon-check" v-else>&#xf051;</i>
                    <span>{{ item }}</span>
                </div>
            </div>
            <div class="bottom">
                <fs-button type="grayline" @click="clear">{{ $c("pages.List.Level3List.clean") }}</fs-button>
                <fs-button type="red" @click="submit">{{ $c("pages.List.Level3List.submit") }}</fs-button>
            </div>
        </fs-popup-new>
    </div>
</template>

<script>
import FsPopup from "@/components/FsPopup/FsPopup.vue"
import FsPopupNew from "@/components/FsPopupNew/FsPopupNew.vue"
import FsButton from "@/components/FsButton/FsButton"
import FsTextTag from "@/components/FsTag/FsTextTag"
import SlideDown from "@/components/SlideDown/SlideDown.vue"
import { mapState } from "vuex"
import { isObject } from "@/util/types"

export default {
    name: "ProductResources",
    components: {
        FsPopup,
        FsTextTag,
        SlideDown,
        FsPopupNew,
        FsButton,
    },
    props: {
        data: {
            type: Object,
            default() {
                return {}
            },
        },
        crumb: {
            type: Array,
            default() {
                return []
            },
        },
    },
    data() {
        return {
            video_data: {
                show: false,
                title: "",
                src: "",
            },
            activeIndex: 0,
            isShowMore: false,
            currentCategoryIndex: 0, // 当前选中的分类索引
            showAll: false,
            showIndex: null,
            showOptions: false,
            activeIndexMobile: 0,
            all_text: "",
            all_text_mobile: "",
            all_text_default: false,
        }
    },
    watch: {
        data: {
            handler: function (v) {
                if (v.downloadTag && v.downloadTag.length) {
                    v.downloadTag.forEach((item) => {
                        item.children = []
                        if (item.style_id === -1) {
                            item.children = v.download
                        } else {
                            v.download.forEach((val) => {
                                if (val.file_style_id === item.style_id) {
                                    item.children.push(val)
                                }
                            })
                        }
                        item.file_num = item.children.length
                    })
                }

                // 重置当前分类索引
                this.currentCategoryIndex = 0
                this.isShowMore = false
                this.showAll = false
                this.showIndex = null

                // 在下一个DOM更新周期计算导航栏宽度
                this.$nextTick(() => {
                    this.calculateNavWidth()
                })
            },
            immediate: true,
        },
    },
    mounted() {
        this.$nextTick(() => {
            this.calculateNavWidth()
        })
    },
    computed: {
        ...mapState({
            isMobile: (state) => state.device.isMobile,
        }),

        // 提取分类标题
        categoryTitles() {
            if (!this.data || !this.data.download || !this.data.isCategoryEnabled) return []

            const titles = []
            if (this.data.isCategoryEnabled === 1 && Array.isArray(this.data.download)) {
                this.data.download.forEach((category) => {
                    if (category && category.length > 0 && category[0].category_name) {
                        titles.push(category[0].category_name)
                    }
                })
            }

            return titles
        },

        // 获取当前分类的数据
        currentCategoryData() {
            if (!this.data || !this.data.download) return []

            if (this.data.isCategoryEnabled === 1 && Array.isArray(this.data.download)) {
                if (this.data.download.length > this.currentCategoryIndex) {
                    return this.data.download[this.currentCategoryIndex] || []
                }
            }

            return this.processedDownloads
        },

        // 处理下载数据
        processedDownloads() {
            if (!this.data || !this.data.download) return []

            // 当isCategoryEnabled为0时，将二维数组平铺成一维数组
            if (this.data.isCategoryEnabled === 0 && Array.isArray(this.data.download)) {
                // 检查是否为二维数组
                if (this.data.download.length > 0 && Array.isArray(this.data.download[0])) {
                    return this.data.download.flat()
                }
            }

            // 其他情况直接返回原数组
            return this.data.download
        },

        // 根据当前分类和展开状态过滤列表
        filterListNew() {
            const sourceData = this.data.isCategoryEnabled === 1 ? this.currentCategoryData : this.processedDownloads

            if (this.isShowMore) {
                return sourceData
            } else if (this.isMobile) {
                return sourceData.slice(0, 4)
            } else {
                return sourceData.slice(0, 12)
            }
        },
    },
    methods: {
        isObject,
        // 计算导航栏宽度，决定是否显示"All"选项
        calculateNavWidth() {
            if (this.data?.isCategoryEnabled === 1 && this.categoryTitles.length > 1 && this.$refs.network) {
                let ctns = this.$refs.network.querySelectorAll(".nav_item")
                let sum = 0
                for (let i = 0; i < ctns.length; i++) {
                    if (i == ctns.length - 1) {
                        sum += ctns[i].clientWidth
                    } else {
                        sum += ctns[i].clientWidth + 4
                    }
                    if (sum + 104 > this.$refs.network.clientWidth) {
                        this.showAll = true
                        this.showIndex = i
                        return
                    } else {
                        this.showIndex = i
                    }
                }
            }
        },
        // 切换分类
        switchCategory(index, type, text) {
            this.currentCategoryIndex = index
            if (type === 1) {
                this.all_text = ""
                this.all_text_default = false
            } else if (type === 2) {
                this.all_text = text
                this.all_text_default = true
            }
            this.showOptions = false
            this.isShowMore = false

            // 添加GA事件跟踪
            if (window.dataLayer) {
                window.dataLayer.push({
                    event: "uaEvent",
                    eventCategory: `productDetailPage_${this.$route.params.id || ``}`,
                    eventAction: "resource_category",
                    eventLabel: `Switch_${this.categoryTitles[index]}`,
                    nonInteraction: false,
                })
            }
        },
        mobileHandleClick(index, item) {
            this.activeIndexMobile = index
            this.all_text_mobile = item
        },
        submit() {
            this.currentCategoryIndex = this.activeIndexMobile
            this.all_text = this.all_text_mobile
            this.showOptions = false
            this.all_text_default = true
        },
        clear() {
            this.showOptions = false
        },
        showVideoPopup(item) {
            if (item.file_type !== 1) return
            this.video_data.show = true
            this.video_data.title = item.file_name
            this.video_data.src = item.file_url
        },
        closeVideoPopup() {
            this.video_data.show = false
            this.video_data.title = ""
            this.video_data.src = ""
        },
        returnNewHref(item, type) {
            if (type === 6 || type === 2) {
                if (type === 6 && item?.documentUrl) {
                    if (item.documentUrl.includes("http")) {
                        return item.documentUrl
                    } else {
                        return this.$localeLink(item.documentUrl)
                    }
                } else {
                    return item.file_url
                }
            } else if (type === 3) {
                return this.localePath({ name: "case-detail", params: { id: item.file_url.replace(/(.*\/)*([^.]+).*/gi, "$2") } })
            } else if (type === 4 || type === 5) {
                return this.$handleLink(item.file_url).url
            } else if (type === 1) {
                return "javascript:;"
            }
        },
        gaEvent(t, item, s) {
            let val = ""
            if (t == 1) {
                val = "video"
            } else if (t == 3) {
                val = "case-studies"
            } else if ([2, 6].includes(t)) {
                val = "pdf"
                if (t === 6 && item?.is_soft) {
                    this.$axios
                        .post("/api/pica8Download", {
                            products_id: this.$route.params.id,
                        })
                        .then(() => {})
                        .catch((error) => {
                            console.log(error)
                        })
                }
            } else {
                val = "link"
            }
            if (window.dataLayer) {
                window.dataLayer.push({
                    event: "uaEvent",
                    eventCategory: `productDetailPage_${this.$route.params.id || ``}`,
                    eventAction: "resource_opreate",
                    eventLabel: `${s}_${val}_${item.file_name}`,
                    nonInteraction: false,
                })
            }
        },
        gaEventMore() {
            if (window.dataLayer) {
                window.dataLayer.push({
                    event: "uaEvent",
                    eventCategory: `productDetailPage_${this.$route.params.id || ``}`,
                    eventAction: "resource_opreate",
                    eventLabel: "See more",
                    nonInteraction: false,
                })
            }
        },
    },
}
</script>

<style lang="scss" scoped>
.product_resources {
    padding-bottom: 40px;
    position: relative;
    @media (max-width: 960px) {
        padding-bottom: 24px;
    }
}

// 添加NetworkComponent的导航样式
.network_nav_box {
    opacity: 1;
    display: flex;
    justify-content: center;
    margin-bottom: 28px;
    &.optionAll {
        opacity: 1;
    }
    .nav_box {
        position: relative;
        border-radius: 287px;
        background: #f6f6f8;
        padding: 6px;
        display: flex;
        width: max-content;
        .nav_item,
        .nav_item_all {
            width: max-content;
            cursor: pointer;
            padding: 4px 16px;
            border-radius: 87px;
            @include font12;
            color: $textColor3;
        }
        .nav_item {
            margin-left: 4px;
            position: relative;
            @include font14;
            &:first-child {
                margin-left: 0;
            }
            &.active {
                color: $textColor1;
                background-color: #fff;
                &::after {
                    @include rowLine;
                    opacity: 1;
                    width: calc(100% - 32px);
                    left: 16px;
                }
            }
            &.no_show {
                display: none;
            }
            &:hover {
                color: $textColor1;
                background-color: #fff;
            }
            @media (max-width: 768px) {
                @include font12;
            }
        }
        .nav_item_all {
            display: flex;
            align-items: center;
            margin-right: 0;
            min-width: 76px;
            max-width: 92px;
            position: relative;
            > span {
                @include font14;
                color: $textColor3;
                text-overflow: ellipsis;
                overflow: hidden;
                white-space: nowrap;
                @media (max-width: 768px) {
                    @include font12;
                }
            }
            > i {
                font-size: 14px;
                line-height: 1;
                color: $textColor3;
                margin-left: 8px;
                transition: all 0.3s;
                &.transform {
                    transform: rotateX(-180deg);
                    @media (max-width: 960px) {
                        transform: initial;
                    }
                }

                @media (max-width: 768px) {
                    font-size: 12px;
                }
            }
            &.active {
                background-color: #fff;
                > span {
                    color: $textColor1;
                }
                > i {
                    color: $textColor1;
                }
            }
            &.active_line {
                &::after {
                    @include rowLine;
                    opacity: 1;
                    width: calc(100% - 32px);
                    left: 16px;
                }
            }
        }
        margin-right: 12px;
        &:last-child {
            margin-right: 0;
        }

        .show_all_box {
            position: absolute;
            top: 38px;
            right: 0;
            padding-top: 8px;
            z-index: 1;
            .show_all_list {
                padding: 7px 0;
                border-radius: 8px;
                border: 1px solid #e5e5e5;
                background-color: #fff;
                .show_all_item {
                    cursor: pointer;
                    padding: 7px 12px;
                    display: flex;
                    align-items: center;
                    > i {
                        font-size: 18px;
                        line-height: 1;
                        height: 18px;
                        margin-right: 8px;
                        color: #d8d8d8;
                    }
                    > span {
                        @include font14;
                        color: $textColor3;
                        width: max-content;
                        @media (max-width: 768px) {
                            @include font12;
                        }
                    }

                    &.active {
                        background: #f7f7f7;
                        > i {
                            color: #707070;
                        }
                        > span {
                            color: #19191a;
                        }
                    }
                    &:hover {
                        background: #f7f7f7;
                        > i {
                            color: #707070;
                        }
                    }
                    &.no_show {
                        display: none;
                    }
                }
            }
            @media (max-width: 768px) {
                display: none;
            }
        }
        &.pc_show {
            margin-right: 0;
            @media (max-width: 960px) {
                display: none;
            }
        }
        &.m_show {
            display: none;
            @media (max-width: 960px) {
                display: block;
            }
        }
        &.only {
            padding: 0;
            background-color: $bgColor3;
            .nav_item {
                padding: 0;
                cursor: default;
                font-weight: 600;
                &::after {
                    display: none;
                }
            }
        }
        &.defOne {
            padding: 0;
            background-color: $bgColor3;
            .nav_item {
                padding: 0;
                cursor: default;
                font-weight: 600;
                @include font16;
                &::after {
                    display: none;
                }
            }
        }
    }
    @media (max-width: 960px) {
        margin-top: 16px;
    }
}

.resource_box {
    width: 100%;
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    grid-gap: 20px;

    @media (max-width: 768px) {
        width: 100%;
        padding: 0 16px;
        grid-template-columns: repeat(1, 1fr);
        grid-gap: 12px;
    }
}

.more-file-btn-pc,
.more-file-btn-m {
    display: flex;
    justify-content: center;
    padding-top: 20px;
    margin-top: 28px;
    border-top: 1px solid #e5e5e5;
    color: #0060bf;

    @media (max-width: 768px) {
        margin-top: 0;
        padding-top: 24px;
        border-top: none;
    }

    .btn {
        cursor: pointer;
        display: inline-flex;
        align-items: center;

        &:hover {
            .info {
                text-decoration: underline;
            }
        }
    }

    .info {
        @include font13;
    }

    .iconfont_down {
        font-size: 12px;
        margin-left: 2px;
        transition: all 0.2s;

        &.rotate {
            transform: rotateX(-180deg);
        }
    }
}

.more-file-btn-pc {
    display: flex;
    border: none;
    margin: 0;
    @media (max-width: 960px) {
        display: none;
    }
}

.more-file-btn-m {
    display: none;
    @media (max-width: 960px) {
        display: flex;
    }
}

.resource_item {
    cursor: pointer;
    border-radius: 4px;
    padding: 16px;
    background-color: #fafbfb !important;
    transition: all 0.3s;
    display: flex;
    align-items: center;
    height: 76px;

    .resource_link {
        display: flex;
        align-items: center;
        width: 100%;
        text-decoration: none;
        color: $textColor1;

        .resource_text {
            flex: 1;
            overflow: hidden;

            .text_name {
                @include font14;
                word-break: break-word;
                @include txt-more-hid(2);
            }

            .text_left {
                display: inline;
                &.noTag {
                    max-width: 100%;
                }
            }

            .text_right {
                display: inline;
                &::before {
                    content: "";
                    display: inline-block;
                    height: 100%;
                    vertical-align: middle;
                }
            }
        }
    }

    .icon_resource {
        display: inline-block;
        width: 20px;
        height: 20px;
        background-image: url(https://img-en.fs.com/includes/templates/fiberstore/images/new-pc-img/download-icon.png);
        background-repeat: no-repeat;
        flex-shrink: 0;
        margin-right: 8px;
    }

    .icon_resource_img {
        display: inline-block;
        width: 20px;
        height: 20px;
        flex-shrink: 0;
        margin-right: 10px;
    }

    &:hover {
        box-shadow: 0 15px 15px -10px rgba(0, 0, 0, 0.15);
        .resource_text {
            .text,
            .text_left {
                text-decoration: underline;
            }
        }
    }
}

.video_box {
    width: 880px;
    height: 495px;

    @media (max-width: 960px) {
        width: 100%;
        height: 210px;
        padding: 30px 16px 0;
    }

    .iframe {
        display: block;
        width: 100%;
        height: 100%;
    }
}

// 移动端弹窗样式
.mobile_popup {
    display: none;
    @media (max-width: 960px) {
        display: block;
    }
    .top {
        padding: 12px 16px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        border-bottom: 1px solid #e5e5e5;
        > span {
            @include font16;
            color: $textColor1;
        }
        > i {
            font-size: 16px;
            line-height: 1;
            color: $textColor3;
        }
    }
    .center {
        padding: 8px 0;
        .center_item {
            padding: 8px 16px;
            display: flex;
            align-items: center;
            > i {
                font-size: 18px;
                line-height: 1;
                height: 18px;
                margin-right: 8px;
                color: #ccc;
            }
            > span {
                @include font12;
                color: $textColor3;
                width: max-content;
            }
            &.active {
                background-color: #f7f7f7;
                > i {
                    color: #707070;
                }
                > span {
                    color: $textColor1;
                }
            }
            &.no_show {
                display: none;
            }
        }
    }
    .bottom {
        padding: 16px;
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        grid-gap: 16px;
        border-top: 1px solid #e5e5e5;
    }
}
</style>
