<template>
    <div class="products_container">
        <div class="product_wrap" v-if="!isOffline" v-loading.fullscreen="loading.global_loading">
            <div class="product_main">
                <div class="product_describe_box" ref="productDescribe" v-if="products_desc.length">
                    <div class="describe_ctn_m" v-if="products_desc && products_desc.length">
                        <div class="describe_m_item_box" v-for="(item, index) in products_desc" :key="index">
                            <div class="describe_m_item" v-if="item.block === 'specifications' && (!desc_type || desc_type === 'desc')">
                                <!-- <div class="describe_m_item_title">{{item.title}}</div> -->
                                <!-- <span class="iconfont iconfont_right">&#xe703;</span> -->
                                <product-specifications client="app" :data="item.data"></product-specifications>
                            </div>
                            <div class="describe_m_item" v-if="item.block === 'network_connectivity' && (!desc_type || desc_type === 'network')">
                                <!-- <div class="describe_m_item_title">{{item.title}}</div> -->
                                <!-- <span class="iconfont iconfont_right">&#xe703;</span> -->
                                <product-network client="app" :data="item.data" @close="productToogle"></product-network>
                            </div>
                            <div class="describe_m_item" v-if="item.block === 'products_highlights' && (!desc_type || desc_type === 'desc')">
                                <div class="describe_m_item_title">{{ item.title }}</div>
                                <!-- <span class="iconfont iconfont_right">&#xe703;</span> -->
                                <product-highlights client="app" :data="item.data" @close="productToogle"></product-highlights>
                            </div>
                            <div class="describe_m_item" v-if="item.block === 'platform_support' && (!desc_type || desc_type === 'desc')">
                                <div class="describe_m_item_title">{{ item.title }}</div>
                                <!-- <span class="iconfont iconfont_right">&#xe703;</span> -->
                                <product-platform client="app" :data="item.data" @close="productToogle"></product-platform>
                            </div>
                            <!-- <div class="describe_m_item" v-if="item.block === 'community'" >
								<div class="describe_m_item_title">{{item.title}}</div>
								<span class="iconfont iconfont_right">&#xe703;</span>
								<product-community client="app" :data="item.data" @close="productToogle"></product-community>
							</div> -->
                        </div>
                    </div>
                </div>
            </div>
            <!-- <transition name="slide-right">
				<product-specifications v-if="product_side === 'specifications'" :title="product_side_title" :data="product_side_data" @close="productToogle"></product-specifications>
				<product-network v-if="product_side === 'network_connectivity'" :title="product_side_title" :data="product_side_data" @close="productToogle"></product-network>
				<product-highlights v-if="product_side === 'products_highlights'" :title="product_side_title" :data="product_side_data" @close="productToogle"></product-highlights>
				<product-platform v-if="product_side === 'platform_support'" :title="product_side_title" :data="product_side_data" @close="productToogle"></product-platform>
				<product-community v-if="product_side === 'community'" :title="product_side_title" :data="product_side_data" @close="productToogle"></product-community>
			</transition> -->
            <fs-popup class="video_popup" :show="popup.show_video_popup" :hideHeader="true" @close="hideVideoPopup">
                <iframe class="iframe" width="100%" height="100%" :src="popup.video_popup_src" frameborder="0" allow="autoplay; encrypted-media" allowfullscreen=""></iframe>
            </fs-popup>
            <add-cart-popup :show="popup.showCartPopup" @close="hideCartPopup" @showSurePopCheck="showSurePopCheck" ref="addCartPopup"></add-cart-popup>

            <add-cart-success :show="popup.show_add_success" @close="closePopup('show_add_success')"></add-cart-success>
            <add-custom-product
                :show="popup.show_add_custom"
                :loading="loading.custom_add_cart_loading"
                :list="popup.custom_popup_products"
                :shipping="popup.custom_popup_shipping"
                @close="closePopup('show_add_custom')"
                @addCart="cusAddCart"
                @qtyChange="cusQtyChange"></add-custom-product>
            <qa-popup
                :show="popup.show_qa_popup"
                @close="closeQaPopup"
                :products="{ select_type: 0, products_name: product_info.products_name, products_img: images.length && images[0].big ? images[0].big : '', products_id: product_info.products_id_show }"></qa-popup>
            <change-location
                ref="changeLocation"
                :products="popup.change_location_popup.products"
                :shipping_post="shipping_post"
                :show="popup.show_change_location"
                @close="hideChangeLocation"
                @getPackingPrice="getPackingPrice"></change-location>
            <get-quote :show="popup.show_get_quote" @close="hideGetQuote" :productsData="popup.get_quote_products"></get-quote>
            <share-products-email
                :show="popup.show_share_products_popup"
                @close="hideShareProductsEmail"
                :productsData="{
                    products_name: product_info.products_name,
                    products_img: images.length && images[0].big ? images[0].big : '',
                    products_id: product_info.products_id_show,
                    qty: qty,
                }"></share-products-email>
        </div>
        <div class="product_offline_wrap" v-if="isOffline">
            <div class="product_offline_main">
                <div class="product_offline_ctn">
                    <bread-crumb :list="crumbs"></bread-crumb>
                    <p class="offline_total">
                        <span>{{ offlineProductInfo.length }}</span> {{ $c("pages.Products.result_for") }} <span> "{{ products_id }}"</span>
                    </p>
                    <div class="tab_box">
                        <div class="tab">{{ $c("pages.Products.Product") }}</div>
                    </div>
                </div>
            </div>
            <div class="product_offline_main product_offline_main2">
                <div class="product_offline_ctn product_offline_ctn2">
                    <div class="result_warning"><i class="iconfont icon">&#xe718;</i>"{{ products_id }}" <span v-html="$c('pages.Products.no_longer_available').replace('xxxx', `${+products_id}`)"></span></div>
                    <div class="result_similar" v-if="offlineProductInfo.length">
                        <template v-if="replaceType == 2">
                            {{ $c("pages.Products.similar_product_recommended") }}
                        </template>
                        <template v-else-if="replaceType == 3">
                            {{ $c("pages.Search.search_result3.result_only_flag") }}
                        </template>
                    </div>
                    <ul class="offline_list" v-if="offlineProductInfo.length">
                        <li v-for="(v, i) in offlineProductInfo" :key="i">
                            <list-goods-item :info="v"></list-goods-item>
                        </li>
                    </ul>
                    <div class="result_contcat">
                        <div class="result_contcat_tit">{{ $c("pages.Products.Contact_Us") }}</div>
                        <div class="result_contcat_list">
                            <nuxt-link class="result_contcat_item" :to="localePath({ name: 'solution_support' })">
                                <span class="ct"><i class="iconfont icon">&#xf023;</i></span>
                                <p class="ct_info">{{ $c("pages.Products.free_technical_support") }}</p>
                            </nuxt-link>
                            <a class="result_contcat_item" :href="`mailto:${email}`">
                                <span class="ct"><i class="iconfont icon">&#xe733;</i></span>
                                <p class="ct_info">{{ email }}</p>
                            </a>
                            <div class="result_contcat_item">
                                <span class="ct"><i class="iconfont icon">&#xe66c;</i></span>
                                <p class="ct_info">{{ phone }}</p>
                            </div>
                            <div class="result_contcat_item" @click.stop="liveChat">
                                <span class="ct"><i class="iconfont icon">&#xf021;</i></span>
                                <p class="ct_info">{{ $c("pages.Products.Live_Chat") }}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import BreadCrumb from "@/components/BreadCrumb/BreadCrumb"
import FsTip from "@/components/FsTip/FsTip"
import QtyBox from "@/components/QtyBox/QtyBox"
import FsButton from "@/components/FsButton/FsButton"
import ProductsUploadFile from "./components/ProductsUploadFile"
import SlideDown from "@/components/SlideDown/SlideDown"
import ImageView from "@/components/ImageView/ImageView"
import ChangeLocation from "@/popup/ChangeLocation/ChangeLocation"
import GetQuote from "@/popup/GetQuote/GetQuote"
import ShareProductsEmail from "@/popup/ShareProductsEmail/ShareProductsEmail"
import FsStar from "@/components/FsStar/FsStar"
import QaPopup from "@/popup/QaPopup/QaPopup"
import ListGoodsItem from "@/components/ListCardMenu/ListGoodsItem"
import FsPopup from "@/components/FsPopup/FsPopup"
import ValidateError from "@/components/ValidateError/ValidateError"
import RecentlyViewed from "@/components/RecentlyViewed/RecentlyViewed"
import ValidateMessage from "@/components/ValidateMessage/ValidateMessage"
import ProductSpecifications from "./components/ProductSpecifications/ProductSpecifications"
import ProductNetwork from "./components/ProductNetwork/ProductNetwork"
import ProductHighlights from "./components/ProductHighlights/ProductHighlights"
import ProductPlatform from "./components/ProductPlatform/ProductPlatform"
import ProductCommunity from "./components/ProductCommunity/ProductCommunity"
import ProductQa from "./components/ProductQa/ProductQa"
import ProductReviews from "./components/ProductReviews/ProductReviews"
import ProductResources from "./components/ProductResources/ProductResources"
import AddCartPopup from "@/components/AddCartPopup/AddCartPopup"
import AddCartSuccess from "@/popup/AddCartSuccess/AddCartSuccess"
import AddCustomProduct from "./components/AddCustomProduct"
import { Swiper, SwiperSlide } from "vue-awesome-swiper"
import { mapState, mapMutations, mapActions } from "vuex"
import { scrollTo, getElementTop, debounce, liveChat, shareLink, setCookieOptions } from "@/util/util"
import { trust_badge } from "@/util/meta.js"
import fixScroll from "@/util/fixScroll"
import colors from "@/constants/product_color.js"

function handleAttr(arr, type, id) {
    if (arr && arr.length) {
        for (let i = 0; i < arr.length; i++) {
            if (id && id === 318) {
                arr[i].private_type = "customLabel"
            }
            arr[i].current_attr = []
            if (arr[i].options_type === 0) {
                if (arr[i].related_options && arr[i].related_options.options_values_id) {
                    if (arr[i].related_options.options_type === 1) {
                        arr[i].current_text = ""
                    }
                }
                if (arr[i].options_values && arr[i].options_values.length) {
                    for (let j = 0; j < arr[i].options_values.length; j++) {
                        if (arr[i].options_values[j].is_default) {
                            arr[i].current_value = arr[i].options_values[j].options_values_id

                            let o = {
                                products_options_value_id: arr[i].options_values[j].options_values_id,
                            }
                            if (type === 2) {
                                o.column_id = arr[i].options_values[j].column_id
                            }
                            arr[i].current_attr.push(o)
                            break
                        }
                    }
                }
            } else if (arr[i].options_type === 1) {
                arr[i].current_value = ""
            } else if (arr[i].options_type === 3) {
                arr[i].current_value = []
                for (let j = 0; j < arr[i].options_values.length; j++) {
                    if (arr[i].options_values[j].is_default) {
                        arr[i].current_value.push(arr[i].options_values[j].options_values_id)
                        let o = {
                            products_options_value_id: arr[i].options_values[j].options_values_id,
                        }
                        if (type === 2) {
                            o.column_id = arr[i].options_values[j].column_id
                        }
                        arr[i].current_attr.push(o)
                    }
                    if (arr[i].options_values[j].is_none) {
                        arr[i].none_value = arr[i].options_values[j].options_values_id
                    }
                }
            } else if (arr[i].options_type === 4) {
                arr[i].current_value = ""
            }
        }
    }
}

export default {
    name: "Product",
    components: {
        BreadCrumb,
        FsTip,
        ProductsUploadFile,
        Swiper,
        SwiperSlide,
        FsButton,
        ListGoodsItem,
        ChangeLocation,
        QtyBox,
        SlideDown,
        ImageView,
        FsStar,
        FsPopup,
        RecentlyViewed,
        ProductSpecifications,
        ProductNetwork,
        ProductHighlights,
        ProductCommunity,
        ProductPlatform,
        ProductQa,
        ProductReviews,
        ProductResources,
        ValidateError,
        ValidateMessage,
        AddCartSuccess,
        AddCustomProduct,
        QaPopup,
        GetQuote,
        ShareProductsEmail,
        AddCartPopup,
    },

    data() {
        const _this = this
        return {
            colors,
            isOffline: false,
            replaceType: 2,
            offlineProductInfo: [],
            total: 0,
            default_address_info: {},
            delivery_date: {},
            shipping: {},
            tab_active: 0,
            products_name_tags: {
                main_tags: [],
                vice_tags: [],
            },
            packing_data: {
                packing_info: [],
                packing_info_active: "",
                packing_origin_price: "",
                packing_save_price: "",
                is_discount: 0,
            },
            is_location_packing: 0,
            is_show_spool: 0,
            is_inquiry: 0,
            loading: {
                global_loading: false,
                add_cart_loading: false,
                custom_add_cart_loading: false,
                qa_review_loading: false,
            },
            images: [],
            products_id: this.$route.params.id || "",
            match_products_id: "",
            label_products: {
                label_products_id: "",
                products_name: "",
                products_img: "",
            },
            popup: {
                show_add_success: false,
                show_qa_popup: false,
                show_add_custom: false,
                custom_popup_products: [],
                custom_popup_shipping: {},
                show_change_location: false,
                change_location_popup: {
                    products: [],
                },
                show_get_quote: false,
                get_quote_products: {},
                show_share_products_popup: false,
                show_video_popup: false,
                video_popup_src: "",
                showCartPopup: false,
            },
            product_info: {
                products_name: "",
                products_price_str: "",
                price_express: "",
                import_fees: 0,
                sales_num: "",
                reviewsRating: "",
                reviewsTotalNum: 0,
                qa_total_num: 0,
                products_model: "",
                is_show_tax_price: 0,
                tax_price: {},
            },
            add_cart_error: "",
            has_file: false,
            crumbs: [],
            desc_type: this.$route.query.desc_type || "",
            products_desc: [],
            related_products: {},
            related_products_active: 0,
            related_products_more: false,
            attributes: null,
            product_side: "",
            product_side_data: [],
            product_side_title: "",
            related_test_tool_products: [],
            product_fixed: {
                show: false,
                show_detail: false,
                click_flag: false,
            },
            shipping_post: {
                city: "",
                state: "",
                shippingMethod: "",
                postCode: "",
                ipInfo: "",
            },
            tabs_slide_left: 0,
            fixed_tabs_slide_left: 0,
            current_index: 0,
            show_image_view: false,
            qty: 1,
            spotlights: {},
            smallOptions: {
                slidesPerView: 5,
                slidesPerGroup: 5,
                loop: false,
                loopFillGroupWithBlank: true,
                navigation: {
                    nextEl: ".swiper-button-next",
                    prevEl: ".swiper-button-prev",
                },
            },
            bigOptions: {
                pagination: {
                    el: ".swiper-pagination",
                    type: "fraction",
                },
                on: {
                    slideChange: function () {
                        if (!this.isOffline) {
                            _this.current_index = this.realIndex
                            if (_this.images[this.previousIndex].type && _this.images[this.previousIndex].type === 2) {
                                _this.images[this.previousIndex].src = _this.images[this.previousIndex].origin_src + (_this.images[this.previousIndex].src.indexOf("?") === -1 ? "?" : "&") + "time=" + new Date().getTime()
                            }
                        }
                    },
                },
            },
        }
    },
    computed: {
        ...mapState({
            isMobile: (state) => state.device.isMobile,
            screenWidth: (state) => state.device.screenWidth,
            isLogin: (state) => state.userInfo.isLogin,
            userInfo: (state) => state.userInfo.userInfo,
            current_country_code: (state) => state.selectCountry.current_country_code,
            country_list: (state) => state.selectCountry.country_list,
            email: (state) => state.category.email,
            phone: (state) => state.category.phone,
            iso_code: (state) => state.webSiteInfo.iso_code,
            website: (state) => state.webSiteInfo.website,
            currency: (state) => state.webSiteInfo.currency,
            language_id: (state) => state.webSiteInfo.language_id,
            description: (state) => state.meta.description,
            currency: (state) => state.webSiteInfo.currency,
            domain: (state) => state.meta.domain,
            langLink: (state) => state.meta.langLink,
            language: (state) => state.webSiteInfo.language,
            pre_url: (state) => state.webSiteInfo.pre_url,
        }),
    },
    asyncData({ app, $axios, store, params, query, $c }) {
        let id = parseInt(params.id)
        let attribute_id = query.attribute ? parseInt(query.attribute) : undefined
        let relate_attribute_id = query.id ? parseInt(query.id) : undefined
        let iso_code = store.state.webSiteInfo.iso_code || "" //webSiteInfo.iso_code || '';
        let address_info = {}
        if (iso_code) {
            if (app.$cookies.get(`products_address_info_${iso_code}`)) {
                address_info = app.$cookies.get(`products_address_info_${iso_code}`)
            }
        }
        let { shippingMethod = "", postCode = "", city = "", state = "", ipInfo = "" } = address_info
        return $axios
            .post("/api/products/product_detail", { products_id: id, postCode, city, state, attribute: attribute_id, id: relate_attribute_id })
            .then((res) => {
                let data = res.data
                if (data.isOffline) {
                    let bread_crumbs = []
                    bread_crumbs.push({ name: $c("pages.Products.Home"), url: app.localePath({ name: "home" }) })
                    bread_crumbs.push({ name: id, url: "" })
                    return {
                        isOffline: data.isOffline,
                        replaceType: data.replaceType,
                        crumbs: bread_crumbs,
                        total: data.total,
                        offlineProductInfo: data.productInfo || [],
                    }
                } else {
                    let recentlyProducts = app.$cookies.get("recentlyProducts") || []
                    if (id) {
                        if (recentlyProducts && recentlyProducts.length) {
                            if (!recentlyProducts.includes(id)) {
                                if (recentlyProducts.length >= 20) {
                                    recentlyProducts.splice(0, 1, id)
                                } else {
                                    recentlyProducts.push(id)
                                }
                            }
                        } else {
                            recentlyProducts = []
                            recentlyProducts.push(id)
                        }
                    }
                    app.$cookies.set("recentlyProducts", recentlyProducts)

                    let { length, no_hie, hie, relate } = data.attributes
                    let images = data.images_arr,
                        current_index = ""
                    let custom_type //0 无定制属性   1 非层级属性   2 层级属性
                    let custom_list = no_hie.concat(hie)
                    let bread_crumbs = data.bread_crumbs
                    let _length = {
                        max: 0,
                        min: 0,
                        cid: "",
                        has_length: false,
                        custom_length: "",
                        length_id: "",
                        length_id_name: "",
                        length_error: "",
                        length_units: ["m", "ft"],
                        length_unit: "m",
                        length_select_show: false,
                    }
                    if (no_hie.length || hie.length) {
                        if (no_hie.length) {
                            custom_type = 1
                        } else {
                            custom_type = 2
                        }
                    } else {
                        custom_type = 0
                    }
                    bread_crumbs.map((item, index) => {
                        item.url = app.localePath({ path: item.url })
                    })
                    bread_crumbs.unshift({ name: $c("pages.Products.Home"), url: app.localePath({ name: "home" }) })
                    bread_crumbs.push({ name: id, url: "" })
                    _length.cid = bread_crumbs[bread_crumbs.length - 2].id
                    if (length && length.title_name) {
                        _length.min = length.min_length
                        _length.max = length.max_length
                        _length.min_ft = parseFloat((length.min_length / 0.3048).toFixed(2))
                        _length.max_ft = parseFloat((length.max_length / 0.3048).toFixed(2))
                        _length.has_length = true
                    }
                    if (relate && relate.length) {
                        relate.map((item) => {
                            item.show_more = false
                            if (item.is_custom_length) {
                                _length.has_length = true
                                _length.min = parseFloat(item.min_length)
                                _length.max = parseFloat(item.max_length)
                                _length.min_ft = parseFloat((_length.min / 0.3048).toFixed(2))
                                _length.max_ft = parseFloat((_length.max / 0.3048).toFixed(2))
                            }
                        })
                    }
                    handleAttr(custom_list, custom_type)
                    for (let i = 0; i < images.length; i++) {
                        if (images[i].type && images[i].type === 1) {
                            current_index = i
                            break
                        }
                    }

                    let related_test_tool_obj = {}
                    if (data.related_test_tool && data.related_test_tool.products_info && data.related_test_tool.products_info.length) {
                        data.related_test_tool.products_info.map((item) => {
                            related_test_tool_obj[item.products_id] = {
                                products_id: item.products_id,
                                qty: item.qty,
                                isChecked: 1,
                                product_info: {
                                    loading: false,
                                    products_name: item.popup_desc,
                                    products_img: item.images.big,
                                },
                            }
                        })
                    }
                    data.item_spotlights.show_more = false
                    let shipping_post = {
                        shippingMethod: data.shipping_free.code,
                        postCode: data.default_address_info.postCode || "",
                        city: data.default_address_info.city || "",
                        state: data.default_address_info.state || "",
                        ipInfo: data.default_address_info.ipInfo || "",
                    }
                    // address_info = shipping_post;
                    // $cookies.set("address_info", address_info,setCookieOptions())
                    if (data.isComposite) {
                        data.tax_price.price_str = data.compositePriceInfo.compositeTotalPriceTaxFormat
                    }

                    let tab_list = []
                    if (data.products_desc && data.products_desc.length) {
                        data.products_desc.map((item) => {
                            if (item.block !== "products_highlights") {
                                tab_list.push(item.label)
                            }
                        })
                    }
                    // relate.splice(1,0,{attr_en_title:'isPackage'});
                    // console.log(relate)
                    return {
                        products_id: id,
                        isOffline: data.isOffline,
                        replaceType: data.replaceType,
                        is_inquiry: data.is_inquiry,
                        default_address_info: data.default_address_info,
                        shipping: data.shipping_free,
                        delivery_date: data.delivery_date,
                        products_name_tags: {
                            main_tags: data.products_name_tags.main_tags,
                            vice_tags: data.products_name_tags.vice_tags,
                        },
                        attributes: {
                            custom_type,
                            relate,
                            custom_list,
                            length,
                        },
                        packing_data: {
                            packing_info: data.packages || [],
                            packing_info_active: data.packages && data.packages.length ? data.packages[0].package_qty : "",
                            packing_origin_price: "",
                            packing_save_price: "",
                            is_discount: 0,
                        },
                        is_location_packing: data.is_location_packing,
                        shipping_post,
                        length: _length,
                        crumbs: bread_crumbs,
                        match_products_id: data.match_products_id || "",
                        product_info: {
                            products_id_show: data.products_id_show,
                            price_express: data.price_express,
                            products_name: data.products_name,
                            products_price_str: data.isComposite ? data.compositePriceInfo.compositeTotalPriceFormat : data.products_price_str,
                            import_fees: data.import_fees,
                            sales_num: data.sales_num,
                            reviewsRating: data.reviews_total.reviewsRating,
                            reviewsTotalNum: data.reviews_total.reviewsTotalNum,
                            highestRating: data.reviews_total.highestRating,
                            qa_total_num: data.qa_total_num,
                            products_model: data.products_model,
                            is_show_tax_price: data.is_show_tax_price,
                            tax_price: data.tax_price,
                        },
                        spotlights: data.item_spotlights,
                        products_desc: data.products_desc,
                        tab_list,
                        related_products: data.related_products,
                        related_products_active: 0,
                        related_products_more: false,
                        related_test_tool: data.related_test_tool,
                        related_test_tool_obj,
                        products_match_tips: data.products_match_tips,
                        inventory: data.inventory,
                        images,
                        current_index,
                    }
                }
            })
            .catch((err) => {})
    },

    mounted() {
        this.getDetail()
        // if (this.product_info.products_name && !this.isOffline) {
        // 	this.$nextTick(() => {
        // 		if (this.$route.query.module) {
        // 			this.clickSlide("reviews")
        // 		} else {
        // 			this.tabClick(0);
        // 		}
        // 		this.onScroll();
        // 		window.addEventListener("scroll", this.onScroll);
        // 	})
        // };
        if (!["de", "de-en", "es", "mx"].includes(this.website)) {
            trust_badge(this.website)
        }
    },
    beforeDestroy() {
        if (this.product_info.products_name && !this.isOffline) {
            // window.removeEventListener("scroll", this.onScroll);
        }
    },
    methods: {
        ...mapMutations({
            setCartData: "cart/setCartData",
        }),
        ...mapActions({
            getUserInfo: "userInfo/getUserInfo",
        }),
        relatedProductsClick(i) {
            this.related_products_active = i
        },
        toogleRelatedProducts() {
            this.related_products_more = !this.related_products_more
        },
        showAddCart(id) {
            if (this.isMobile) {
                this.$router.push(this.localePath({ name: "products", params: { id } }))
            } else {
                this.popup.showCartPopup = true
                this.$refs.addCartPopup.init(id)
            }
        },
        hideCartPopup() {
            this.popup.showCartPopup = false
        },
        showSurePopCheck() {
            this.popup.show_add_success = true
        },
        hideSurePop() {
            this.popup.show_add_success = false
        },
        handleShare(type) {
            if (type === "email") {
                this.popup.show_share_products_popup = true
            } else {
                let params = {
                    type: type,
                    url: location.href,
                    title: document.title,
                    img: this.images && this.images.length && this.images[0].big ? this.images[0].big : "",
                }
                shareLink(params)
            }
        },
        sameProductsClick(p) {
            if (!p.is_default) {
                location.href = this.localePath({ name: "products", params: { id: p.product_id }, query: { attribute: p.related_attribute_id, id: p.id } })
            }
        },
        showVideoPopup(src) {
            this.popup.show_video_popup = true
            this.popup.video_popup_src = src
        },
        hideVideoPopup() {
            this.popup.show_video_popup = false
            this.popup.video_popup_src = ""
        },
        closeQaPopup() {
            this.popup.show_qa_popup = false
        },
        attrSelectChange(cus) {
            if (cus.options_values && cus.options_values.length) {
                let options_values = cus.options_values
                for (let i = 0; i < options_values.length; i++) {
                    if (options_values[i].options_values_id === cus.current_value) {
                        let o = {
                            products_options_value_id: options_values[i].options_values_id,
                        }
                        if (this.attributes.custom_type === 2) {
                            o.column_id = options_values[i].column_id
                        }
                        if (cus.current_value === 4262) {
                            o.products_options_value_text = cus.current_text
                        }
                        cus.current_attr.splice(0, cus.current_attr.length, o)
                    }
                }
            }
            this.checkRequired()
            if (cus.private_type !== "customLabel") {
                this.updateAttributes(cus)
            }
        },

        clickSlide(s) {
            if (this.products_desc && this.products_desc.length) {
                let num = -1,
                    flag = false
                for (let i = 0; i < this.products_desc.length; i++) {
                    if (this.products_desc[i].block === s) {
                        num += 1
                        flag = true
                        break
                    } else {
                        if (this.products_desc[i].block !== "products_highlights") {
                            num += 1
                        }
                    }
                }
                if (flag) {
                    this.tabClick(num, true)
                }
            }
        },
        attrInputChange(cus) {
            if (cus.options_values && cus.options_values.length) {
                let options_values = cus.options_values
                let o = {
                    products_options_value_text: cus.current_value,
                    products_options_value_id: 0,
                }
                if (this.attributes.custom_type === 2) {
                    o.column_id = options_values[0].column_id
                }
                if (cus.current_value) {
                    cus.current_attr.splice(0, cus.current_attr.length, o)
                }
            }
            //this.updateAttributes(cus)
        },
        attrSelectInputChange(cus) {
            if (cus.current_value === 4262) {
                cus.current_attr[0].products_options_value_text = cus.current_text
            }
        },
        attrInputBlur(cus) {
            this.checkRequired()
            //this.updateAttributes(cus)
        },
        attrCheckChange(c, l, e) {
            if (e.target.checked) {
                if (l.is_none) {
                    c.current_value = [l.options_values_id]
                } else {
                    if (c.none_value && c.current_value.includes(c.none_value)) {
                        c.current_value.splice(c.current_value.indexOf(c.none_value), 1)
                    }
                }
            }
            let c_attr = []
            if (c.current_value && c.current_value.length) {
                for (let i = 0; i < c.current_value.length; i++) {
                    if (c.options_values && c.options_values.length) {
                        for (let j = 0; j < c.options_values.length; j++) {
                            let o = {}
                            if (c.current_value[i] === c.options_values[j].options_values_id) {
                                o.products_options_value_id = c.current_value[i]
                                if (this.attributes.custom_type === 2) {
                                    o.column_id = c.options_values[j].column_id
                                }
                                c_attr.push(o)
                            }
                        }
                    }
                }
            }
            c.current_attr.splice(0, c.current_attr.length, ...c_attr)
            this.checkRequired(true)
            if (this.add_cart_error) {
                return
            }
            this.updateAttributes(c)
        },
        attrFileChange(params, cus) {
            let { file, clientOriginalName, error } = params
            if (file) {
                let o = {
                    upload_file: {
                        file: file,
                        clientOriginalName: clientOriginalName,
                    },
                }
                if (this.attributes.custom_type === 2) {
                    o.column_id = cus.options_values[0].column_id
                }
                cus.current_attr.splice(0, cus.current_attr.length, o)
                cus.current_value = clientOriginalName
            } else {
                cus.current_attr.splice(0, cus.current_attr.length)
                cus.current_value = ""
            }
            console.log(cus.current_attr)
            this.checkRequired()
        },
        setLengthId(l) {
            this.length.custom_length = ""
            this.length.length_id = l.products_length_id
            this.length.length_id_name = l.length_str
            this.length.length_error = ""
            this.checkRequired()
            this.updateAttributes()
        },
        inputLengchClick() {
            this.length.length_id = ""
            this.match_products_id = ""
            this.length.length_id_name = ""
        },
        toogleLength() {
            this.length.length_select_show = !this.length.length_select_show
        },
        lengthInput() {
            this.length.custom_length = this.length.custom_length.replace(/[^\d^\.]+/g, "")
        },
        lengthBlur() {
            if (this.length.custom_length) {
                let length_error = this.$c("pages.Products.length_error")
                let length_error_max = this.$c("pages.Products.length_error_max")
                let cable_length_error = this.$c("pages.Products.cable_length_error")

                let err = ""
                let length_m
                //1ft = 0.3048m
                if (this.length.length_unit === "m") {
                    length_m = parseFloat(this.length.custom_length)
                } else if (this.length.length_unit === "ft") {
                    length_m = parseFloat(this.length.custom_length) * 0.3048
                }
                if (this.length.min || this.length.max) {
                    if (this.length.min && length_m < this.length.min) {
                        err = length_error.replace("XX", this.length.min)
                        if (this.website !== "jp") {
                            err = `${err}(${this.length.min_ft}ft)`
                        }
                        if (this.website === "fr" || this.website === "de") {
                            err = err.replace(".", ",")
                        }
                        this.length.length_error = err
                        this.length.custom_length = ""
                        return
                    }
                    if (this.length.max && length_m > this.length.max) {
                        let err_max = `${this.length.max}'m (${this.length.max_ft}ft)`
                        if (this.website === "fr" || this.website === "de") {
                            err_max = err_max.replace(".", ",")
                        }
                        err = length_error_max.replace("XX", err_max)
                        this.length.length_error = err
                        this.length.custom_length = ""
                        return
                    }
                }
                if (this.length.cid === 2875) {
                    if ([30775, 30793, 30809, 30746].includes(this.products_id)) {
                        if (this.products_id === 30809) {
                            if (length_m < 1 || length_m > 100) {
                                if (["de", "fr", "es"].includes(this.website)) {
                                    err = cable_length_error.replace("0.5", "1")
                                    err = cable_length_error.replace("1.64", "3,28")
                                } else {
                                    err = cable_length_error.replace("0.5", "1")
                                    err = cable_length_error.replace("1.64", "3.28")
                                }
                                this.length.length_error = err
                                this.length.custom_length = ""
                                return
                            }
                        } else {
                            if (length_m < 0.5 || length_m > 100) {
                                err = cable_length_error
                                this.length.length_error = err
                                this.length.custom_length = ""
                                return
                            }
                        }
                    } else {
                        if (length_m > 100) {
                            if (this.length.length_unit === "m") {
                                this.length.custom_length = 100
                            } else {
                                this.length.custom_length = 328.084
                            }
                        }
                    }
                }
                this.length.length_error = ""
                this.checkRequired()
                this.updateAttributes()
            }
        },
        changeLengthUnit(u) {
            if (u !== this.length.length_unit) {
                this.length.length_unit = u
                this.toogleLength()
                this.lengthBlur()
            }
        },
        toogleLoading() {
            this.loading.global_loading = !this.loading.global_loading
        },
        testToolClick(t) {
            if (this.related_test_tool_products.includes(t.products_id)) {
                this.related_test_tool_products.splice(this.related_test_tool_products.indexOf(t.products_id), 1)
            } else {
                this.related_test_tool_products.push(t.products_id)
            }
        },
        getDetail() {
            let address_info = this.$cookies.get("address_info") || {}
            let { shippingMethod = "", postCode = "", city = "", state = "", ipInfo = "" } = address_info
            let attribute_id = this.$route.query.attribute ? parseInt(this.$route.query.attribute) : undefined
            let relate_attribute_id = this.$route.query.id ? parseInt(this.$route.query.id) : undefined
            this.$axios.post("/api/products/product_detail", { products_id: this.products_id, postCode, city, state, attribute: attribute_id, id: relate_attribute_id })
        },
        liveChat,
        getAttributes(cus) {
            let custom_product = {
                products_id: this.products_id,
                qty: this.packing_data.packing_info && this.packing_data.packing_info.length ? this.qty * this.packing_data.packing_info_active : this.qty,
                isChecked: 1,
                current_options_id: cus ? cus.options_type : 0,
                attr: [],
                attributes: {},
            }
            // let custom_product_popup = JSON.parse(JSON.stringify(custom_product));
            // custom_product_popup.attr = []

            custom_product.product_info = {
                loading: false,
                products_name: this.product_info.products_name,
                products_img: this.images[0].big,
            }

            let label_index = 0

            let label_product = {
                qty: 1,
                isChecked: 1,
                attr: [],
                attributes: {},
            }
            // let label_product_popup = JSON.parse(JSON.stringify(label_product));
            // label_product_popup.attr = [];
            if (this.label_products.label_products_id) {
                label_product.products_id = this.label_products.label_products_id
                label_product.product_info = {
                    loading: false,
                    products_name: this.label_products.products_name,
                    products_img: this.label_products.products_img,
                }
            }
            let custom_type = this.attributes.custom_type
            let custom_index
            if (this.length.has_length) {
                let len = {
                    custom_length: "",
                    product_length_id: "",
                }
                if (this.length.custom_length || this.length.length_id) {
                    if (this.length.custom_length) {
                        len.custom_length = `${this.length.custom_length}${this.length.length_unit}`
                        len.product_length_id = ""
                        custom_product.attr.push(`${this.$c("pages.Products.Length")} - ${this.length.custom_length}${this.length.length_unit}`)
                    } else {
                        len.custom_length = ""
                        len.product_length_id = this.length.length_id
                        custom_product.attr.push(`${this.$c("pages.Products.Length")} - ${this.length.length_id_name}`)
                    }
                }
                custom_product["attributes"]["length"] = len
            }

            if (this.attributes && this.attributes.custom_list && this.attributes.custom_list.length) {
                let custom_list = this.attributes.custom_list
                for (let i = 0; i < custom_list.length; i++) {
                    if (custom_list[i].options_type === 0) {
                        if (custom_list[i].current_attr && custom_list[i].current_attr.length) {
                            if (this.label_products.label_products_id && label_index > 0 && i + 1 > label_index) {
                                label_product.attributes[custom_list[i].options_id] = custom_list[i].current_attr
                                for (let j = 0; j < custom_list[i].options_values.length; j++) {
                                    if (custom_list[i].options_values[j].options_values_id === custom_list[i].current_value) {
                                        custom_list[i].current_value = custom_list[i].options_values[j].options_values_id
                                        label_product.attr.push(`${custom_list[i].options_name} - ${custom_list[i].options_values[j].options_values_name}`)
                                    }
                                }
                                if (custom_list[i].current_value === 4262 && custom_list[i].current_text) {
                                    label_product.attr.push(`${custom_list[i].options_name} - ${custom_list[i].current_text}`)
                                }
                            } else {
                                if ((custom_list[i].options_id === 341 && this.is_show_spool) || custom_list[i].options_id !== 341) {
                                    custom_product.attributes[custom_list[i].options_id] = custom_list[i].current_attr
                                    for (let j = 0; j < custom_list[i].options_values.length; j++) {
                                        if (custom_list[i].options_values[j].options_values_id === custom_list[i].current_value) {
                                            custom_list[i].current_value = custom_list[i].options_values[j].options_values_id
                                            custom_product.attr.push(`${custom_list[i].options_name} - ${custom_list[i].options_values[j].options_values_name}`)
                                        }
                                    }
                                }
                                if (custom_list[i].current_value === 4262 && custom_list[i].current_text) {
                                    label_product.attr.push(`${custom_list[i].options_name} - ${custom_list[i].current_text}`)
                                }
                            }
                        }
                    }

                    if (custom_list[i].options_type === 1) {
                        if (custom_list[i].current_attr && custom_list[i].current_attr.length) {
                            if (this.label_products.label_products_id && label_index > 0 && i + 1 > label_index) {
                                label_product.attributes[custom_list[i].options_id] = custom_list[i].current_attr
                                label_product.attr.push(`${custom_list[i].options_name} - ${custom_list[i].current_value}`)
                            } else {
                                custom_product.attributes[custom_list[i].options_id] = custom_list[i].current_attr
                                custom_product.attr.push(`${custom_list[i].options_name} - ${custom_list[i].current_value}`)
                            }
                        }
                    }

                    if (custom_list[i].options_type === 4) {
                        if (custom_list[i].current_attr && custom_list[i].current_attr.length) {
                            if (this.label_products.label_products_id && label_index > 0 && i + 1 > label_index) {
                                label_product.attributes[custom_list[i].options_id] = custom_list[i].current_attr

                                label_product.attr.push(`${custom_list[i].options_name} - ${custom_list[i].current_value}`)
                            } else {
                                custom_product.attributes[custom_list[i].options_id] = custom_list[i].current_attr
                                custom_product.attr.push(`${custom_list[i].options_name} - ${custom_list[i].current_value}`)
                            }
                        }
                    }
                    if (custom_list[i].options_type === 3) {
                        if (custom_list[i].current_attr && custom_list[i].current_attr.length) {
                            if (this.label_products.label_products_id && label_index > 0 && i + 1 > label_index) {
                                label_product.attributes[custom_list[i].options_id] = custom_list[i].current_attr
                                for (let j = 0; j < custom_list[i].current_value.length; j++) {
                                    for (let k = 0; k < custom_list[i].options_values.length; k++) {
                                        if (custom_list[i].current_value[j] === custom_list[i].options_values[k].options_values_id) {
                                            label_product.attr.push(`${custom_list[i].options_name} - ${custom_list[i].options_values[k].options_values_name}`)
                                        }
                                    }
                                }
                            } else {
                                for (let j = 0; j < custom_list[i].current_value.length; j++) {
                                    custom_product.attributes[custom_list[i].options_id] = custom_list[i].current_attr
                                    for (let k = 0; k < custom_list[i].options_values.length; k++) {
                                        if (custom_list[i].current_value[j] === custom_list[i].options_values[k].options_values_id) {
                                            custom_product.attr.push(`${custom_list[i].options_name} - ${custom_list[i].options_values[k].options_values_name}`)
                                        }
                                    }
                                }
                            }
                        }
                        if (custom_list[i].options_id === 318) {
                            if (cus) {
                                custom_index = i
                                break
                            }
                            if (this.label_products.label_products_id) {
                                label_index = i + 1
                            }
                        }
                    }
                    if (cus && custom_type === 2 && cus.options_id === custom_list[i].options_id) {
                        custom_index = i
                        break
                    }
                }
            }
            if (cus && custom_type) {
                custom_product.current_options_id = cus.options_id
            }
            // if (!cus && this.match_products_id) {
            // 	custom_product.products_id = this.match_products_id;
            // 	custom_product.current_options_id = 0;
            // 	custom_product.attributes = {};
            // }
            let addProducts = []
            addProducts.push(custom_product)

            if (!cus && this.label_products.label_products_id) {
                addProducts.push(label_product)
            }

            if (!cus && this.related_test_tool_products && this.related_test_tool_products.length) {
                for (let i = 0; i < this.related_test_tool_products.length; i++) {
                    let o = this.related_test_tool_obj[this.related_test_tool_products[i]]
                    addProducts.push(o)
                }
            }
            return { addProducts, custom_index }
        },
        updateAttributes(cus) {
            let { addProducts, custom_index } = this.getAttributes(cus)
            let new_products = JSON.parse(JSON.stringify(addProducts))
            if (new_products && new_products.length) {
                for (let n = 0; n < new_products.length; n++) {
                    delete new_products[n].attr
                    delete new_products[n].product_info
                }
            }
            this.loading.global_loading = true
            this.$axios
                .post("/api/products/change_attributes", { products: new_products })
                .then((res) => {
                    this.loading.global_loading = false
                    this.product_info.products_price_str = res.data.price_str
                    this.product_info.tax_price.price_str = res.data.price_str_tax
                    this.is_show_spool = res.data.is_show_spool
                    this.match_products_id = res.data.match_products_id
                    this.delivery_date = res.data.delivery_date
                    this.inventory = res.data.inventory
                    if (res.data.label_products_info && res.data.label_products_info.products_id) {
                        this.label_products.label_products_id = res.data.label_products_info.products_id
                        this.label_products.products_name = res.data.label_products_info.products_desc
                        this.label_products.products_img = ""
                    } else {
                        this.label_products.label_products_id = ""
                        this.label_products.products_name = ""
                        this.label_products.products_img = ""
                    }
                    if (res.data.attributes_extension && res.data.attributes_extension.length) {
                        handleAttr(res.data.attributes_extension, this.attributes.custom_type, cus.options_id)
                        if (this.attributes.custom_type === 2) {
                            this.attributes.custom_list.splice(custom_index + 1, this.attributes.custom_list.length - custom_index - 1, ...res.data.attributes_extension)
                        }

                        if (cus.options_id === 318) {
                            res.data.attributes_extension.map((item) => {
                                item.label_products_id = res.data.label_products_info.products_id
                            })
                            this.attributes.custom_list.splice(custom_index + 1, 0, ...res.data.attributes_extension)
                        }
                    } else {
                        if (cus.options_id === 318) {
                            this.attributes.custom_list.splice(custom_index + 1, this.attributes.custom_list.length - custom_index - 1)
                        }
                        if (this.attributes.custom_type === 2) {
                            this.attributes.custom_list.splice(custom_index + 1, this.attributes.custom_list.length - custom_index - 1)
                        }
                    }
                })
                .catch((err) => {
                    this.loading.global_loading = false
                })
        },
        checkRequired(show) {
            let flag = false
            let err_example = this.$c("pages.Products.please_select_attribute")
            if (this.length && this.length.has_length) {
                if (!this.length.custom_length && !this.length.length_id) {
                    flag = true
                    if (show) {
                        this.add_cart_error = err_example
                    }
                }
            }
            if (this.attributes.custom_list && this.attributes.custom_list.length) {
                let custom_list = this.attributes.custom_list
                for (let i = 0; i < custom_list.length; i++) {
                    if (custom_list[i].options_type === 4) {
                        this.has_file = true
                    }
                    if (custom_list[i].is_required) {
                        if (custom_list[i].options_type === 0 || custom_list[i].options_type === 1 || custom_list[i].options_type === 4) {
                            if (!custom_list[i].current_value) {
                                flag = true
                                break
                            }
                            if (custom_list[i].related_options && custom_list[i].related_options.options_values_id) {
                                if (custom_list[i].related_options.is_required && !custom_list[i].current_text) {
                                    flag = true
                                    break
                                }
                            }
                        } else if (custom_list[i].options_type === 3) {
                            if (!custom_list[i].current_value.length) {
                                flag = true
                                break
                            }
                        }
                    }
                }
            }
            if (flag) {
                if (show) {
                    this.add_cart_error = err_example
                } else {
                    this.add_cart_error = ""
                }
            } else {
                this.add_cart_error = ""
            }
        },
        showChangeLocation() {
            this.checkRequired(true)
            if (this.add_cart_error) {
                return
            }
            let { addProducts } = this.getAttributes()
            let obj = {}
            let ps = JSON.parse(JSON.stringify(addProducts))
            if (ps && ps.length) {
                for (let i = 0; i < ps.length; i++) {
                    delete ps[i].attr
                    delete ps[i].product_info
                    delete ps[i].current_options_id
                }
            }
            if (this.packing_data.packing_info && this.packing_data.packing_info.length && ps && ps.length) {
                ps[0].qty = this.qty * this.packing_data.packing_info_active
            }
            this.popup.change_location_popup.products.splice(0, this.popup.change_location_popup.products.length, ...ps)
            // obj.products = ps;
            // obj.postCode = this.shipping_post.postCode;
            //this.$axios.post("/api/products/get_products_detail_shipping_list", obj)
            if (this.shipping_post.postCode) {
                this.$refs.changeLocation.countrySelect({
                    countries_name: `${this.$c("pages.Products.Ship_outside_the")} ${this.current_country_code}`,
                    iso_code: this.current_country_code,
                })
            }
            this.$refs.changeLocation.getData()
            this.popup.show_change_location = true
        },
        hideChangeLocation() {
            this.popup.show_change_location = false
        },
        showGetQuote() {
            this.checkRequired(true)
            if (this.add_cart_error) {
                return
            }
            let { addProducts } = this.getAttributes()
            this.popup.get_quote_products = {
                products_name: this.product_info.products_name,
                products_img: this.images.length ? this.images[0].big : "",
                products_id: this.product_info.products_id_show,
                qty: this.qty,
                attributes: addProducts[0].attributes || {},
            }
            this.popup.show_get_quote = true
        },
        hideGetQuote() {
            this.popup.show_get_quote = false
        },
        showShareProductsEmail() {
            this.popup.show_share_products_popup = true
        },
        hideShareProductsEmail() {
            this.popup.show_share_products_popup = false
        },
        addCart(type) {
            this.checkRequired(true)
            if (this.add_cart_error) {
                if (type === 2) {
                    scrollTo(document.body)
                }
                return
            }
            let { addProducts } = this.getAttributes()
            console.log(addProducts)
            let api = ""
            let obj = {}
            if ((this.attributes.custom_type || this.length.has_length) && !this.match_products_id) {
                api = "/api/products/add_customized_popup"
                obj.default_shipping_method = this.shipping_post.shippingMethod
                obj.post_code = this.shipping_post.postCode
                obj.state = this.shipping_post.state
                obj.city = this.shipping_post.city
            } else {
                api = "/api/cart/add"
            }
            let ps = JSON.parse(JSON.stringify(addProducts))
            if (ps && ps.length) {
                for (let i = 0; i < ps.length; i++) {
                    delete ps[i].attr
                    delete ps[i].product_info
                }
            }
            obj.products = ps
            this.loading.add_cart_loading = true
            this.$axios
                .post(api, obj)
                .then((res) => {
                    if ((this.attributes.custom_type || this.length.has_length) && !this.match_products_id) {
                        if (res.data && res.data.products && res.data.products.length) {
                            for (let i = 0; i < addProducts.length; i++) {
                                for (let j = 0; j < res.data.products.length; j++) {
                                    if (addProducts[i].products_id == res.data.products[j].products_id) {
                                        addProducts[i].product_info.price_str = res.data.products[j].price_str
                                        addProducts[i].product_info.tax_price_str = res.data.products[j].price_str_tax
                                    }
                                }
                            }
                        }
                        if (res.data && res.data.productsCustomizeId) {
                            for (let i = 0; i < addProducts.length; i++) {
                                addProducts[i].products_customized_id = res.data.productsCustomizeId
                            }
                        }
                        this.popup.custom_popup_products.splice(0, this.popup.custom_popup_products.length, ...addProducts)
                        this.popup.custom_popup_shipping = res.data.delivery_date
                        this.popup.show_add_custom = true
                        this.loading.add_cart_loading = false
                        if (res.data && res.data.cartInfo) {
                            this.setCartData(res.data.cartInfo)
                        }
                    } else {
                        this.loading.add_cart_loading = false
                        this.setCartData(res.data)
                        //this.setSubTotal(res.data.subTotal.subTotalFormat)
                        this.popup.show_add_success = true
                        if (window.dataLayer) {
                            let ca = []
                            this.crumbs.map((item, index) => {
                                if (index > 0 && index < 4) {
                                    ca.push(item.name)
                                }
                            })
                            window.dataLayer.push({ ecommerce: null })
                            window.dataLayer.push({
                                event: "addToCart",
                                ecommerce: {
                                    currencyCode: "USD",
                                    add: {
                                        products: [
                                            {
                                                name: this.product_info.products_name,
                                                id: this.products_id,
                                                price: this.product_info.tax_price.price_us,
                                                brand: "FS",
                                                category: ca.join(" / "),
                                                quantity: this.qty,
                                            },
                                        ],
                                    },
                                },
                            })
                        }
                    }
                })
                .catch((err) => {
                    this.loading.add_cart_loading = false
                    if (err?.code === 400) {
                        this.shopCartLimitShow(err.message, err.data.addAllowFlag)
                    }
                })
        },

        shopCartLimitShow(message, showCheckOut) {
            this.$shopcartLimit.show({
                title: this.$c("pages.ShoppingCart.Confirmation"),
                message: message,
                viewCartName: this.$c("pages.Products.View_Cart"),
                checkoutName: this.$c("pages.ShoppingCart.Secure_Checkout2"),
                isShowCheckOutBtn: showCheckOut,
                checkOutMethod: () => {
                    // this.$router.push(this.localePath({ name: "confirm-order" }))
                    location.href = this.$localeLink(`/confirm-order`)
                },
                shopCartMethod: () => {
                    this.$router.push(this.localePath({ name: "shopping-cart" }))
                },
            })
        },
        closePopup(attr) {
            this.popup[attr] = false
        },
        cusQtyChange(qty, i) {
            this.popup.custom_popup_products[i].qty = qty
            if (i === 0) {
                this.qty = qty
            }
            this.popup.custom_popup_products[i].product_info.loading = true
            this.$axios
                .post("/api/products/add_customized_popup", {
                    products: this.popup.custom_popup_products,
                    default_shipping_method: this.shipping_post.shippingMethod,
                    post_code: this.shipping_post.postCode,
                    city: this.shipping_post.city,
                    state: this.shipping_post.state,
                })
                .then((res) => {
                    if (res.data && res.data.products && res.data.products.length) {
                        for (let i = 0; i < this.popup.custom_popup_products.length; i++) {
                            for (let j = 0; j < res.data.products.length; j++) {
                                if (this.popup.custom_popup_products[i].products_id == res.data.products[j].products_id) {
                                    this.popup.custom_popup_products[i].product_info.price_str = res.data.products[j].price_str
                                }
                            }
                        }
                    }
                    this.popup.custom_popup_shipping = res.data.delivery_date
                    this.popup.custom_popup_products[i].product_info.loading = false
                })
                .catch((err) => {
                    this.popup.custom_popup_products[i].product_info.loading = false
                })
        },
        cusAddCart() {
            if (this.loading.custom_add_cart_loading) {
                return
            }
            let ps = JSON.parse(JSON.stringify(this.popup.custom_popup_products))
            if (ps && ps.length) {
                for (let i = 0; i < ps.length; i++) {
                    delete ps[i].attr
                    delete ps[i].product_info
                }
            }
            this.loading.custom_add_cart_loading = true
            this.$axios
                .post("/api/cart/add", { products: ps })
                .then((res) => {
                    this.loading.custom_add_cart_loading = false
                    this.setCartData(res.data)
                    this.popup.show_add_custom = false
                    if (window.dataLayer) {
                        let ca = []
                        this.crumbs.map((item, index) => {
                            if (index > 0 && index < 4) {
                                ca.push(item.name)
                            }
                        })
                        window.dataLayer.push({ ecommerce: null })
                        window.dataLayer.push({
                            event: "addToCart",
                            ecommerce: {
                                currencyCode: "USD",
                                add: {
                                    products: [
                                        {
                                            name: this.product_info.products_name,
                                            id: this.products_id,
                                            price: this.product_info.tax_price.price_us,
                                            brand: "FS",
                                            category: ca.join(" / "),
                                            quantity: this.qty,
                                        },
                                    ],
                                },
                            },
                        })
                    }
                    this.$router.push(this.localePath({ name: "shopping-cart" }))
                })
                .catch((err) => {
                    this.loading.custom_add_cart_loading = false
                    if (err?.code === 400) {
                        this.shopCartLimitShow(err.message, err.data.addAllowFlag)
                    }
                })
        },
        relateClick(p, r) {
            if (p.product_id) {
                this.$router.push(this.localePath({ name: "products", params: { id: p.product_id } }))
            } else {
                r.show_more = !r.show_more
            }
        },
        productToogle(s, d) {
            this.product_side = s || ""
            this.product_side_data = d && d.data ? d.data : []
            this.product_side_title = d && d.title ? d.title : ""
            if (this.product_side) {
                fixScroll.fixed()
            } else {
                fixScroll.unfixed()
            }
        },
        smallMouseEnter(i) {
            console.log(i)
            this.current_index = i
        },
        qtyChange(n, attr) {
            this.checkRequired(true)
            if (attr.type === 1) {
                if (this.add_cart_error) {
                    this.$refs.qtyBox1.setQty(this.qty)
                    return
                }
            } else if (attr.type === 2) {
                if (this.add_cart_error) {
                    scrollTo(document.body)
                    this.$refs.qtyBox2.setQty(this.qty)
                    return
                }
            }

            this.qty = n
            //if (this.packing_data.packing_info && this.packing_data.packing_info.length) {
            this.getPackingPrice({ hideLoading: 1 })
            //}
        },
        packingClick(p) {
            this.packing_data.packing_info_active = p
            this.qty = 1
            this.getPackingPrice()
        },
        getPackingPrice(params) {
            let postCode = "",
                city = "",
                state = "",
                shippingMethod = ""
            if (params && !params.hideLoading) {
                postCode = params.postCode
                city = params.city
                state = params.state
                shippingMethod = params.shippingMethod
            } else {
                postCode = this.shipping_post.postCode
                city = this.shipping_post.city
                state = this.shipping_post.state
                shippingMethod = this.shipping_post.shippingMethod
            }
            if (!params) {
                this.loading.global_loading = true
            }
            let { addProducts } = this.getAttributes()
            let ps = JSON.parse(JSON.stringify(addProducts))
            if (ps && ps.length) {
                for (let i = 0; i < ps.length; i++) {
                    delete ps[i].attr
                    delete ps[i].product_info
                    delete ps[i].current_options_id
                }
            }
            if (this.packing_data.packing_info && this.packing_data.packing_info.length && ps && ps.length) {
                ps[0].qty = this.qty * this.packing_data.packing_info_active
            }
            let obj = {
                products: ps,
                postCode,
                city,
                state,
                shippingMethod,
            }
            if (params.addressId) {
                obj.addressId = params.addressId
            }
            this.$axios
                .post("/api/products/change_products_info", obj)
                .then((res) => {
                    this.packing_data.packing_origin_price = res.data.price_data.origin_price
                    this.packing_data.packing_save_price = res.data.price_data.diff_price
                    this.packing_data.is_discount = res.data.price_data.is_discount
                    this.product_info.products_price_str = res.data.price_data.current_price
                    this.product_info.tax_price.price_str = res.data.price_data.tax_current_price
                    this.delivery_date = res.data.delivery_date
                    this.inventory.new_source = res.data.delivery_date
                    this.shipping = res.data.shipping_free
                    this.loading.global_loading = false
                    if (params) {
                        this.shipping_post = Object.assign(res.data.default_address_info, { shippingMethod: res.data.shipping_free.code })
                        this.default_address_info = res.data.default_address_info
                        this.hideChangeLocation()
                        this.$cookies.set(`products_address_info_${this.current_country_code}`, this.shipping_post)
                        this.$refs.changeLocation.hideBtnLoading()
                        if (params.delete_address_id) {
                            this.$cookies.remove("address_book_id")
                        }
                    }
                })
                .catch((err) => {
                    this.loading.global_loading = false
                })
        },
        spotlightsToogle() {
            this.spotlights.show_more = !this.spotlights.show_more
        },
        showImageView() {
            this.show_image_view = true
        },
        tabClick(i, scroll) {
            console.log(i)
            this.tab_active = i
            let tabs = this.$refs.mt1_2.querySelectorAll(".mt1_tab")
            if (tabs && tabs.length) {
                let ct = tabs[i]
                this.tabs_slide_left = ct.offsetLeft + ct.offsetWidth / 2
                this.fixed_tabs_slide_left = ct.offsetLeft + ct.offsetWidth / 2 - this.$refs.mt1_tab_left.offsetWidth
            }
            if (scroll) {
                this.product_fixed.click_flag = true
                if (this.is_inquiry) {
                    let ctns = this.$refs.productDescribe.querySelectorAll(".describe_ctn_tab")
                    scrollTo(
                        ctns[i],
                        7,
                        () => {
                            this.product_fixed.click_flag = false
                        },
                        0
                    )
                } else {
                    let ctns = this.$refs.productDescribe.querySelectorAll(".describe_ctn_tab")
                    scrollTo(
                        ctns[i],
                        7,
                        () => {
                            this.product_fixed.click_flag = false
                        },
                        -111
                    )
                }
            }
        },
        onScroll() {
            if (!this.isMobile && !document.body.classList.contains("fixScroll")) {
                let ctns = this.$refs.productDescribe.querySelectorAll(".describe_ctn_tab")
                let scrollTop = document.documentElement.scrollTop || document.body.scrollTop
                let fixedTop = getElementTop(ctns[0])
                if (scrollTop + 50 >= fixedTop) {
                    this.product_fixed.show = true
                    if (scrollTop + 20 >= fixedTop) {
                        this.product_fixed.show_detail = true
                    } else {
                        this.product_fixed.show_detail = false
                    }
                } else {
                    this.product_fixed.show = false
                    this.product_fixed.show_detail = false
                }
                this.$nextTick(() => {
                    let fixedHeight = this.$refs.productFixed ? this.$refs.productFixed.offsetHeight : 0
                    for (let i = 0; i < ctns.length; i++) {
                        if (
                            (i === ctns.length - 1
                                ? scrollTop + fixedHeight + 20 >= getElementTop(ctns[i])
                                : scrollTop + fixedHeight + 20 >= getElementTop(ctns[i]) && scrollTop + fixedHeight + 20 < getElementTop(ctns[i + 1])) &&
                            !this.product_fixed.click_flag
                        ) {
                            this.tabClick(i)
                        }
                    }
                })
            }
        },
        qaReviewClick(type) {
            if (!this.isLogin) {
                this.$router.push(
                    this.localePath({
                        name: "login",
                        query: {
                            redirect: this.$route.fullPath,
                        },
                    })
                )
            } else {
                if (this.loading.qa_review_loading) {
                    return
                }
                this.loading.qa_review_loading = true
                this.getUserInfo(() => {
                    this.loading.qa_review_loading = false
                    if (this.isLogin) {
                        if (type === "qa") {
                            this.popup.show_qa_popup = true
                        } else if (type === "reviews") {
                            this.$router.push(this.localePath({ name: "write-review", query: { pId: this.products_id } }))
                        }
                    }
                })
            }
        },
    },
    watch: {
        isMobile(n) {
            if (!n) {
                this.popup.show_video_popup = false
                fixScroll.unfixed()
            }
        },
    },
}
</script>

<style lang="scss" scoped>
.product_wrap {
    // background: $bgColor1;
    width: 100%;
    padding-bottom: 40px;
    .product_fixed {
        position: fixed;
        left: 0;
        right: 0;
        top: 0;
        width: 100%;
        background: #fff;
        box-shadow: 0 1px 5px #ccc;
        transition: all 0.3s;
        border-bottom: 1px solid #dedede;
        z-index: 50;
        display: block;
        @media (max-width: 960px) {
            display: none;
        }
    }
    .products_match_box {
        display: flex;
        flex-direction: column;
        margin-top: 15px;
        .products_match_item {
            @include font13;
            &::v-deep {
                a {
                    &:hover {
                        color: $textColor1;
                    }
                }
            }
        }
    }
    .mt1 {
        display: flex;
        align-items: center;
        position: relative;
        margin-top: 16px;
        padding-bottom: 10px;

        &.mt1_2 {
            border-bottom: 1px solid #dedede;
            padding-bottom: 0;
            margin-top: 0;
            .mt1_tab {
                height: 57px;
                line-height: 57px;
            }
        }
        .mt1_tab_left {
            @include font16;
            color: $textColor3;
            font-weight: 600;
            flex-shrink: 0;
            padding-right: 43px;
            position: relative;
            &:after {
                display: block;
                content: " ";
                width: 3px;
                height: 3px;
                background-color: #e5e5e5;
                border-radius: 50%;
                position: absolute;
                top: 11px;
                right: 20px;
            }
            @media (max-width: 1200px) {
                @include font14;
                padding-right: 33px;
                &:after {
                    top: 10px;
                    right: 15px;
                }
            }
        }
        .mt1_tab {
            @include font16;
            color: $textColor3;
            font-weight: 600;
            margin-right: 22px;
            cursor: pointer;
            white-space: nowrap;
            // max-width: 178px;
            overflow: hidden;
            text-overflow: ellipsis;

            @media (max-width: 1200px) {
                @include font14;
                margin-right: 16px;
            }
            &:hover {
                color: $textColor3;
            }
            @media (max-width: 1020px) {
                margin-right: 10px;
            }
            &.mt1_tab_active {
                color: $textColor1;
                &:hover {
                    color: $textColor1;
                }
            }
            &:last-of-type {
                margin-right: 0;
            }
        }
        .mt1_slide {
            position: absolute;
            left: 0;
            height: 3px;
            background: $bgColor5;
            position: absolute;
            width: 40px;
            transition: all 0.2s;
            border-radius: 10px;
            bottom: -1px;
            z-index: 1;
            bottom: 0;
            transform: translateX(-50%);
        }
    }
    .mt1_box {
        width: 100%;
        background: #fff;
        padding: 4px 32px 0 32px;
        overflow: hidden;
        @media (max-width: 1420px) {
            padding: 4px 3% 0 3%;
        }
        @media (max-width: 960px) {
            display: none;
        }
    }
    .describe_ctn {
        background: #fff;
        padding: 1px 32px 1px 32px;
        margin-bottom: 20px;

        @media (max-width: 1420px) {
            padding: 1px 3% 1px 3%;
        }
        @media (max-width: 960px) {
            margin: 12px auto;
            padding: 0 3% 0 3%;
        }
        &.describe_ctn_community {
            .describe_ctn_title {
                border-bottom: none;
            }
        }
        &.describe_ctn_resources {
            overflow: hidden;
        }
    }
    .describe_ctn_pc {
        @media (max-width: 960px) {
            display: none;
        }
    }
    .describe_ctn_m {
        // margin: 12px auto;
        display: none;
        @media (max-width: 960px) {
            display: block;
        }
        .describe_m_item {
            // display: flex;
            // justify-content: space-between;
            // align-items: center;
            // height: 52px;
            // background: #fff;
            // margin-bottom: 1px;
            // padding: 0 3%;
            // cursor: pointer;
        }
        .describe_m_item_title {
            color: $textColor1;
            @include font16;
            font-weight: 600;
            transition: all 0.2s;
            height: 52px;
            line-height: 52px;
            background: #fff;
            margin-bottom: 1px;
            padding: 0 3%;
        }
        .iconfont_right {
            color: $textColor3;
            @include font14;
        }
    }
    .describe_ctn_title {
        @include font20;
        color: $textColor1;
        font-weight: 600;
        padding: 20px 0;
        border-bottom: 1px solid $btnBgColor5;
        display: flex;
        align-items: center;
        justify-content: space-between;
        .qa_tip {
            &::v-deep {
            }
        }
        .qa_tip_trigger {
            @include font14;
            font-weight: 400;
            @media (max-width: 960px) {
                @include font13;
            }
        }
        .qa_tip_ctn {
            min-width: 190px;
            text-align: center;
            white-space: nowrap;
            font-weight: 400;
            @include pc_tip();
            @media (max-width: 960px) {
                @include m_tip();
            }
        }
        @media (max-width: 960px) {
            @include font16;
            border-bottom: none;
            padding: 19px 0 4px 0;
        }
    }
    .add_cart_error {
        margin-top: 22px;
    }
    .product_fixed_ctn {
        display: flex;
        width: 84vw;
        max-width: 1200px;
        margin: 0 auto;
        flex-direction: column;
        position: relative;
        padding: 0 32px;
        @media (max-width: 1200px) {
            width: 94%;
            padding: 0 3%;
        }
        @media (max-width: 960px) {
            width: 94%;
            padding: 0 3%;
        }
        .mt0 {
            display: flex;
            align-items: center;
            transition: all 0.5s;
            max-height: 0;
            overflow: hidden;
            &.mt0_active {
                max-height: 300px;
            }
        }
        .mt0_img {
            display: block;
            width: 60px;
            margin-right: 20px;
        }
        .mt0_detail {
            width: 50%;
        }
        .mt0_title {
            color: $textColor1;
            @include font14;
            overflow: hidden;
            font-weight: 600;
            white-space: nowrap;
            text-overflow: ellipsis;
        }
        .mt0_reviews {
            display: flex;
            align-items: center;
            .reviews_box {
                display: flex;
                cursor: pointer;
            }
            &::v-deep {
                .star {
                    i {
                        display: inline-block;
                        margin-right: 3px;
                        width: 12px;
                        height: 12px;
                        transform: scale(0.9);
                    }
                }
            }
            .reviews {
                margin: 0 20px 0 5px;
                @include font13;
                color: $textColor1;
            }
            .help {
                @include font13;
                color: $textColor1;
                cursor: pointer;
                &:hover {
                    text-decoration: underline;
                }
            }
        }

        .mt2 {
            display: flex;
            align-items: center;
            position: absolute;
            right: 0;
            top: 26px;
            .mt2_price {
                text-align: right;
                white-space: nowrap;
                .price {
                    font-size: 18px;
                    line-height: 24px;
                    color: $textColor1;
                    @media (max-width: 1200px) {
                        @include font16;
                    }
                }
                .tax_price {
                    @include font16;
                    color: $textColor1;
                    @media (max-width: 1200px) {
                        @include font14;
                    }
                }
                .stock {
                    @include font13;
                    color: $textColor3;
                }
            }
            .qty-box {
                width: 76px;
                margin: 0 10px;
                &::v-deep {
                    .qty {
                        font-weight: 600;
                        font-size: 14px;
                    }
                }
            }
            .add_cart_btn {
                width: auto;
                padding: 0 12px;
                .iconfont_cart {
                    margin-right: 8px;
                }
            }
        }
    }
    .product_main {
        max-width: 1200px;
        width: 84vw;
        margin: 0 auto;
        @media (max-width: 1200px) {
            width: 94vw;
        }
        @media (max-width: 960px) {
            width: 100%;
            padding: 0;
        }
    }
    .product_detail_box {
        background: #fff;
        padding: 40px 0;
        display: flex;
        align-items: flex-start;
        justify-content: space-between;

        @media (max-width: 960px) {
            margin-top: 0;
            margin-bottom: 0;
        }
    }
    .related_box {
        padding: 0 32px 20px 32px;
        background-color: #fff;
        @media (max-width: 960px) {
            padding: 0 3% 20px 3%;
        }
        .related_title_wrap {
            position: relative;
            width: 100%;
            &:after {
                content: "";
                width: 88px;
                height: 100%;
                background: linear-gradient(270deg, #ffffff 0%, rgba(255, 255, 255, 0.6) 100%);
                position: absolute;
                right: 0;
                top: 0;
                @media (max-width: 960px) {
                    width: 60px;
                }
            }
        }
        .related_title_box {
            border-bottom: 1px solid #e5e5e5;
            display: flex;
            align-items: center;
            overflow-x: auto;
            position: relative;
            @media (max-width: 960px) {
                flex-direction: column;
                align-items: flex-start;
                overflow: hidden;
            }
            .related_title_left {
                @include font16;
                font-weight: 600;
                color: #707070;
                position: relative;
                padding-right: 43px;
                padding-bottom: 20px;
                flex-shrink: 0;
                white-space: nowrap;
                @media (max-width: 1200px) {
                    @include font14;
                    padding-right: 33px;
                    &:after {
                        top: 10px;
                        right: 15px;
                    }
                }
                @media (max-width: 960px) {
                    padding-right: 0;
                    padding-bottom: 12px;
                }
                &:after {
                    display: block;
                    content: " ";
                    width: 3px;
                    height: 3px;
                    background-color: #e5e5e5;
                    border-radius: 50%;
                    position: absolute;
                    top: 11px;
                    right: 20px;
                    @media (max-width: 960px) {
                        display: none;
                    }
                }
            }
            .related_title_right {
                display: flex;
                align-items: center;
                flex-shrink: 0;
                white-space: nowrap;
                @media (max-width: 960px) {
                    width: 100%;
                    overflow-x: auto;
                }
                .related_title_item {
                    margin-right: 20px;
                    position: relative;
                    cursor: pointer;
                    @include font16;
                    font-weight: 600;
                    color: #707070;
                    padding-bottom: 20px;
                    @media (max-width: 1200px) {
                        @include font14;
                        margin-right: 10px;
                    }

                    &.related_title_item_active {
                        color: #19191a;
                        &:after {
                            content: "";
                            position: absolute;
                            width: 40px;
                            height: 2px;
                            background-color: #c00000;
                            left: 50%;
                            transform: translateX(-50%);
                            bottom: 0;
                        }
                    }
                }
            }
        }
        .related_products_box {
            padding-top: 24px;
            display: flex;
            flex-wrap: wrap;
            @media (max-width: 960px) {
                flex-direction: column;
                max-height: 202px;
                overflow: hidden;
                transition: all 0.3s;
                flex-wrap: inherit;
                &.related_products_box_max {
                    max-height: 1500px;
                    transition: all 0.3s;
                }
            }
            .related_products_item {
                display: flex;
                align-items: stretch;
                margin: 0 12px 12px 0;
                width: calc((100% - 36px) / 4);
                cursor: pointer;
                &:nth-of-type(4n) {
                    margin-right: 0;
                }
                @media (max-width: 960px) {
                    width: 100%;
                    margin: 0 0 12px 0;
                }
                .related_products {
                    display: flex;
                    align-items: center;
                    padding: 8px 14px 8px 12px;
                    border: 1px solid #ccc;
                    border-right: none;
                    transition: all 0.3s;
                    text-decoration: none;
                    flex: 1 1 auto;
                    &:hover {
                        border-color: #707070;
                        .related_products_title {
                            text-decoration: underline;
                        }
                    }
                    .related_products_img {
                        display: block;
                        max-width: 60px;
                        max-height: 60px;
                        width: 60px;
                        height: 60px;
                        margin-right: 14px;
                        @media (max-width: 1200px) {
                            margin-right: 8px;
                        }
                    }
                    .related_products_title {
                        @include font14;
                        font-weight: 600;
                        color: $textColor1;
                        max-width: 100%;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        display: -webkit-box;
                        -webkit-line-clamp: 2;
                        -webkit-box-orient: vertical;
                    }
                    .related_products_subtitle {
                        @include font13;
                        color: #707070;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        display: -webkit-box;
                        -webkit-line-clamp: 2;
                        -webkit-box-orient: vertical;
                    }
                    .related_products_price {
                        @include font14;
                        color: $textColor1;
                    }
                }
                .related_addcart {
                    display: flex;
                    align-items: center;
                    width: 30px;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    background-color: #ccc;
                    transition: all 0.3s;
                    font-size: 18px;
                    color: #fff;
                    flex-shrink: 0;
                    position: relative;
                    cursor: pointer;
                    border-top-right-radius: 3px;
                    border-bottom-right-radius: 3px;
                    &:hover {
                        background-color: #c00000;
                    }
                }
            }
        }
        .see_more_btn {
            display: flex;
            justify-content: center;
            padding-top: 10px;
            font-size: 14px;
            color: #0060bf;
            display: none;
            @media (max-width: 960px) {
                display: flex;
            }
            .iconfont {
                display: inline-block;
                font-size: 12px;
                &.iconfont_down_up {
                    transform: rotate(-180deg);
                }
            }
        }
    }
    .product_describe_box {
        margin-top: 20px;
        @media (max-width: 960px) {
            margin-top: 0;
        }
    }

    .product_gallery_box {
        // width: 620px;
        flex: 1;
        position: sticky;
        top: 0;
        @media (max-width: 1420px) {
            width: 50%;
        }
        @media (max-width: 960px) {
            display: none;
        }
    }
    .share_tip {
        position: absolute;
        top: 16px;
        left: 32px;
        margin-left: 0;
        @media (max-width: 960px) {
            display: none;
        }
        &::v-deep {
            .tip-trigger {
                padding: 0;
            }
            .tip-ctn {
                &.tip-ctn-bottom {
                    left: -32px;
                    transform: translateX(0);
                }
            }
        }
        .iconfont_share {
            font-size: 18px;
            color: #979797;
            display: flex;
            justify-content: center;
            align-items: center;
            width: 42px;
            height: 42px;
            border-radius: 42px;
            border: 1px solid #d8d8d8;
        }
        .share_ctn {
            display: flex;
            min-width: 150px;
            justify-content: space-between;
            align-items: center;
            padding: 22px 21px;
            background: #fff;
            border-radius: 2px;
            -webkit-box-shadow: 0px 1px 8px 0 rgb(120 102 102 / 40%);
            -moz-box-shadow: 0px 1px 8px 0 rgba(120, 102, 102, 0.4);
            box-shadow: 0px 1px 8px 0 rgb(120 102 102 / 40%);
            .iconfont {
                color: #8d8d8f;
                font-size: 14px;
                text-decoration: none;
                cursor: pointer;
                transition: all 0.3s;

                &.iconfont_facebook {
                    &:hover {
                        color: #3c5a98;
                    }
                }
                &.iconfont_twitter {
                    &:hover {
                        color: #29c5f6;
                    }
                }
                &.iconfont_pinterest {
                    &:hover {
                        color: #e72f30;
                    }
                }
                &.iconfont_linkin {
                    &:hover {
                        color: #0481d9;
                    }
                }
                &.iconfont_email {
                    font-size: 13px;
                    margin-top: 3px;
                    &:hover {
                        color: #4c4948;
                    }
                }
            }
        }
    }
    .big_img_box {
        text-align: center;
        position: relative;
        overflow: hidden;
        width: 420px;
        height: 420px;
        display: block;
        margin: auto;
        @media (max-width: 1420px) {
            width: 67%;
            height: 0;
            padding-top: 67%;
        }
        .big_img {
            display: block;
            max-width: 100%;
            max-height: 100%;
            margin: auto;
            cursor: url(https://img-en.fs.com/includes/templates/fiberstore/images/cursor_serch_ic.png), auto;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate3d(-50%, -50%, 0);
        }
        .video_iframe {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 100%;
        }
    }
    .click_view {
        @include font12;
        color: #9e9e9e;
        text-align: center;
        margin-top: 10px;
    }
    .video_popup {
        display: none;
        @media (max-width: 960px) {
            display: block;
        }
        &::v-deep {
            .iconfont_close {
                color: #fff;
            }
            .fs-popup-ctn {
                background: #000;
                .slot-wraper {
                    display: flex;
                    align-items: center;
                }
                .iframe {
                    height: 56.5vw;
                }
            }
        }
    }
    .product_attr_box {
        width: 608px;
        padding: 0 32px 0 11px;
        @media (max-width: 960px) {
            padding: 0 3%;
            width: 100%;
        }
    }
    .product_title {
        padding-bottom: 7px;
        h1 {
            @include font24;
            display: inline;
            font-weight: normal;
            color: $textColor1;
            font-weight: 600;
        }
        span {
            font-size: 13px;
            padding-left: 10px;
            color: $textColor3;
        }
        @media (max-width: 960px) {
            padding-bottom: 0;
            h1 {
                @include font20;
            }
        }
    }
    .product_sub {
        margin-bottom: 14px;
        .main_tags {
            padding-top: 3px;
            @include font12;
            color: #8d8d8f;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 1;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }
        .vice_tags {
            padding-top: 3px;
            color: $textColor3;
            @include font13;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 1;
            -webkit-box-orient: vertical;
            overflow: hidden;
            cursor: default;
        }
        @media (max-width: 960px) {
            margin-bottom: 3px;
        }
    }
    .product_swiper_box {
        max-width: 550px;
        width: 86%;
        margin: 0 auto;
        position: relative;
        margin-top: 10px;
        display: none;
        .swiper-container {
            padding-bottom: 20px;
        }
        .swiper-slide {
            margin: 0 auto;
            .big_img_m {
                position: relative;
                > img {
                    display: block;
                    max-width: 550px;
                    max-height: 550px;
                    width: 100%;
                }
                .video_btn {
                    position: absolute;
                    width: 60px;
                    height: 60px;
                    background-color: rgba(0, 0, 0, 0.2);
                    border-radius: 50%;
                    left: 50%;
                    top: 50%;
                    margin-left: -30px;
                    margin-top: -30px;
                    transition: all 0.3s;
                    box-sizing: border-box;
                    cursor: pointer;
                    > span {
                        position: absolute;
                        width: 19px;
                        height: 22px;
                        top: 50%;
                        margin-top: -11px;
                        left: 50%;
                        margin-left: -7.5px;
                        background: url(https://img-en.fs.com/includes/templates/fiberstore/images/new_index/video_icon.png) no-repeat;
                    }
                }
            }
        }
        .swiper-pagination {
            display: inline-block;
            border-radius: 10px;
            background: $bgColor1;
            width: auto;
            padding: 0 10px;
            color: #664466;
            left: 50%;
            bottom: 0;
            @include font13;
            transform: translate3d(-50%, 0, 0);
        }
        @media (max-width: 960px) {
            display: block;
        }
    }

    .small_img_box {
        width: 420px;
        margin: 20px auto 0 auto;
        position: relative;
        padding: 0 35px;
        .swiper-slide {
            cursor: pointer;
        }
        .small_img {
            display: block;
            width: 62px;
            height: 62px;
            border-radius: 2px;
            border: 1px solid #ccc;
            margin: 0 auto;
            position: relative;
            > img {
                display: block;
                max-width: 100%;
                max-height: 100%;
                width: 100%;
                height: 100%;
            }
        }
        .small_img_active {
            border: 1px solid #616265;
        }
        .small_img_video {
            &::before {
                content: " ";
                display: block;
                top: 0;
                right: 0;
                bottom: 0;
                left: 0;
                position: absolute;
                // background: rgba(0, 0, 0, 0.15);
            }
            &::after {
                content: " ";
                background: url(https://img-en.fs.com/includes/templates/fiberstore/images/paly-icon.png) center;
                display: inline-block;
                width: 18px;
                height: 18px;
                position: absolute;
                left: 50%;
                margin-left: -9px;
                top: 50%;
                margin-top: -9px;
                z-index: 10;
            }
            > img {
                display: block;
                max-width: 100%;
                max-height: 100%;
                width: 100%;
                height: auto;
                object-fit: cover;
                margin-top: 50%;
                transform: translateY(-50%);
            }
        }
        .swiper-button-prev,
        .swiper-button-next {
            .iconfont {
                font-size: 20px;
                color: #8d8d8f;
            }
            &:after {
                content: "";
            }
        }
    }
    .attributes_wrap {
        margin-bottom: 20px;
    }

    .attributes_length_box {
        margin-bottom: 10px;
    }
    .package_wrap {
        .package_title {
            @include font14;
            color: $textColor3;
            margin: 31px 0 8px 0;
            display: -webkit-box;
            display: -ms-flexbox;
            display: flex;
            -webkit-box-align: center;
            -ms-flex-align: center;
            align-items: center;
        }
        .package_box {
            display: flex;
            align-items: center;
            .package_item {
                display: flex;
                align-items: center;
                height: 36px;
                border-radius: 4px;
                border: 1px solid $borderColor6;
                margin: 2.5px 5px 2.5px 0;
                background: #fff;
                transition: all 0.3s;
                color: $textColor1;
                padding: 1px 15px;
                cursor: pointer;
                @include font13;
                &:hover {
                    background: $bgColor1;
                    text-decoration: none;
                }
                &.package_item_active {
                    font-weight: 600;
                    padding: 0 14px;
                    border: 2px solid #707070;
                    background-color: #f7f7f7;
                }
            }
        }
    }
    .product_sales_box {
        padding: 15px 25px 20px 25px;
        margin: 10px 0 0 0;
        display: flex;
        justify-content: space-between;
        background: $bgColor1;
        flex-wrap: wrap;

        .current_price {
            font-size: 22px;
            line-height: 36px;
            font-weight: 600;
            color: $textColor1;
            white-space: nowrap;
            &.current_price_italic {
                font-style: italic;
            }
        }
        .packing_origin_price {
            @include font14;
            color: #999999;
            margin-left: 5px;
            > span {
                text-decoration: line-through;
            }
        }
        .pn {
            color: $textColor3;
            @include font14;
            margin-top: 2px;
        }
        .product_sales {
            display: flex;
            align-items: center;
            flex-shrink: 0;
        }
        .packing_save_price {
            @include font14;
            color: #c00000;
            margin-top: 2px;
        }
        .tax_price_box {
            margin: 6px 0 6px 0;
            .tax_price {
                @include font16;
                color: $textColor1;
                font-weight: 600;
            }
            .tax_price_tip {
                min-width: 260px;
                @include pc_tip();
                @media (max-width: 960px) {
                    @include m_tip();
                }
            }
        }
        .fee {
            margin: 6px 0 5px 0px;
            .fee_tip {
                margin-left: 0;
                .fee_tip_trigger {
                    display: flex;
                    align-items: center;
                    @include font13;
                    margin-right: 5px;
                    color: $textColor1;
                    .fee_tip_trigger_text {
                        color: $textColor6;
                        margin-right: 5px;
                    }
                }
                &::v-deep {
                    .arrow-box-top {
                        left: auto;
                        transform: translateX(0);
                        right: 0;
                        margin-right: -3px;
                    }
                    .tip-ctn-top {
                        left: 72%;
                        transform: translateX(0);
                        @media (max-width: 960px) {
                            left: 50%;
                            transform: translate3d(-50%, -50%, 0);
                        }
                    }
                    .info {
                        padding: 20px;
                        @media (max-width: 960px) {
                            padding: 44px 20px 40px 20px;
                        }
                    }
                }
                .fee_tip_ctn {
                    min-width: 260px;
                    @include pc_tip();
                    @media (max-width: 960px) {
                        @include m_tip();
                    }
                }
            }
        }
        .sales_item {
            border-right: 1px solid $borderColor2;
            padding: 0 11px;
            font-size: 13px;
            line-height: 1;
            color: $textColor3;
            flex-shrink: 0;
            &:first-child {
                padding-left: 0;
                > span {
                    color: #a65300;
                }
            }
            &:nth-child(2n) {
                cursor: pointer;
                > span {
                    color: #0070bc;
                }
            }
            &:last-child {
                cursor: pointer;
                border-right: none;
                > span {
                    color: $textColor1;
                }
            }
        }
        @media (max-width: 1220px) {
            flex-direction: column;
            .product_sales {
                margin-top: 10px;
                .sales_item {
                    &:first-child {
                        padding-left: 0;
                    }
                }
            }
        }
        @media (max-width: 960px) {
            background: #fff;
            flex-direction: column;
            padding: 15px 0 0 0;
            .product_sales {
                margin-top: 32px;
                .sales_item {
                    &:first-child {
                        padding-left: 0;
                    }
                }
            }
        }
    }

    .attr_title {
        @include font14;
        color: $textColor3;
        margin: 31px 0 8px 0;
        display: flex;
        align-items: center;
        @media (max-width: 960px) {
            margin-top: 14px;
        }
        .relate_tip {
            min-width: 300px;
            @include pc_tip();
            @media (max-width: 960px) {
                @include m_tip();
            }
        }
    }
    .cus_tip {
        min-width: 300px;
        @include pc_tip();
        @media (max-width: 960px) {
            @include m_tip();
        }
    }
    .select_inp_tip {
        min-width: 284px;
        @include pc_tip();
        @media (max-width: 960px) {
            @include m_tip();
        }
    }

    .length_input_box {
        display: flex;
        font-weight: 600;
        align-items: center;
        position: relative;
        .length {
            width: 70px;
            height: 34px;
            border: none;
            text-align: center;
            font-weight: 600;
            padding-left: 2px;
            padding-right: 2px;
        }
        .length_unit_box {
            position: relative;
            .length_current_unit {
                cursor: pointer;
                padding: 0 12px;
                height: 34px;
                display: flex;
                align-items: center;
                position: relative;
            }
            .unit_border {
                border-left: 1px solid $borderColor2;
                height: 16px;
                top: 50%;
                transform: translateY(-50%);
                left: 0;
                position: absolute;
            }
            .length_unit {
                font-weight: 600;
                line-height: 1;
                font-size: 13px;
                width: 12px;
                text-align: center;
            }
            .iconfont_down {
                font-weight: 400;
                font-size: 12px;
                margin-left: 4px;
                line-height: 1;
                transition: all 0.3s;
                &.iconfont_down_up {
                    transform: rotate(180deg);
                }
            }
            .length_unit_select {
                width: 100%;
                position: absolute;
                top: 100%;
                background: #fff;
                box-shadow: 1px 0.5px 1px 1px rgb(0 0 0 / 5%);
                margin-top: 6px;
                border: 1px solid $borderColor2;
                overflow: hidden;
                z-index: 2;
                left: 12px;
                .length_item {
                    cursor: pointer;
                    color: $textColor1;
                    transition: all 0.3s;
                    padding: 9px 10px;
                    text-align: center;
                    background: #fff;
                    @include font13;
                    &:hover {
                        background: $bgColor1;
                    }
                }
            }
        }
    }

    .relate_ctn {
        display: flex;
        align-items: center;
        flex-wrap: wrap;

        .color_item {
            display: flex;
            border: 1px solid $borderColor6;
            width: 30px;
            height: 30px;
            margin-right: 5px;
            box-sizing: border-box;
            border-radius: 2px;
            cursor: pointer;
            justify-content: center;
            align-items: center;
            .color {
                display: inline-block;
                width: 20px;
                height: 20px;
                &.color_white {
                    border: 1px solid #dedede;
                }
            }
            &.active {
                border: 2px solid #707070;
            }
        }
    }
    .relate_item {
        display: flex;
        align-items: center;
        height: 36px;
        border-radius: 4px;
        border: 1px solid $borderColor6;
        margin: 2.5px 5px 2.5px 0;
        background: #fff;
        transition: all 0.3s;
        color: $textColor1;
        padding: 1px 15px;
        cursor: pointer;
        @include font13;
        &:hover {
            background: $bgColor1;
            text-decoration: none;
        }
        .img {
            display: inline-block;
            height: 33px;
            margin-right: 13px;
        }

        &.relate_item_active {
            font-weight: 600;
            padding: 0 14px;
            border: 2px solid #707070;
            background-color: $bgColor1;
        }
        &.relate_item_blue {
            color: #0070bc;
        }
        &.relate_item_img {
            height: 48px;
        }
        &.relate_item_length {
            padding: 0;
        }
    }
    .customized_wrap {
        margin-top: 16px;
        @media (max-width: 960px) {
            margin-top: 0;
        }
    }
    .customized_box {
        display: flex;
        align-items: flex-start;
        margin-bottom: 10px;
        .customized_main {
            width: 100%;
            display: flex;
            align-items: flex-start;
            @media (max-width: 960px) {
                flex-wrap: wrap;
            }
        }
        .customized_title {
            color: $textColor3;
            @include font13;
            width: 165px;
            margin-right: 5px;
            height: 40px;
            display: flex;
            align-items: center;
            flex-shrink: 0;
        }
        .customized_ctn {
            .customized_item {
                width: 280px;
                max-width: 460px;
                @media (max-width: 1000px) {
                    width: 260px;
                }
                @media (max-width: 960px) {
                    width: 100%;
                    max-width: 100%;
                }
            }
            .customized_select {
                height: 40px;
            }
            .customized_inp {
                height: 40px;
            }
            .select_inp_box {
                margin-top: 10px;
                position: relative;
                .select_tip_box {
                    position: absolute;
                    top: 50%;
                    right: -22px;
                    transform: translateY(-50%);
                }
            }
            .customized_label {
                display: flex;
                align-items: center;
            }
            .check_box {
                display: flex;
                align-items: center;
                flex-wrap: wrap;
                .label_box {
                    flex-wrap: nowrap;
                    height: 40px;
                    cursor: pointer;
                    @include font13;
                    color: $textColor1;
                    display: flex;
                    align-items: flex-start;
                    margin-right: 12px;
                    .chk {
                        line-height: 20px;
                    }
                }
            }
        }
        @media (max-width: 960px) {
            flex-direction: column;
            .customized_ctn {
                width: 100%;
                max-width: 100%;
            }
        }
    }
    .test_tool_box {
        margin-bottom: 10px;
        .test_tool_tip {
            min-width: 260px;
            padding: 20px;
        }
    }

    .test_tool_ctn {
        display: flex;
        flex-wrap: wrap;
        align-items: flex-start;
        .test_tool_ctn_item {
            margin: 2.5px 5px 2.5px 0;
        }
        .test_tool_products {
            margin-left: 0;
        }
        .test_tool_item {
            margin: 0;
            &.active {
                background: #fff;
                background: url(https://img-en.fs.com/includes/templates/fiberstore/images/new-pc-img/subscript.svg) no-repeat;
                background-size: 14px;
                background-position: right bottom -1px;
                border: 2px solid #616265;
                padding: 0 14px;
            }
        }
        .test_tool_popup {
            display: flex;
            min-width: 480px;
            padding: 10px 20px;
            align-items: center;
            .tool_popup_img {
                display: inline-block;
                flex-shrink: 0;
                > img {
                    display: inline-block;
                    width: 180px;
                    height: 180px;
                }
            }
            .tool_popup_detail {
                padding-left: 20px;
                padding-bottom: 12px;
                .tool_popup_title {
                    display: block;
                    color: $textColor1;
                    @include font14;
                    margin: 10px 0 16px 0;
                }
                .tool_popup_price {
                    font-size: 22px;
                    line-height: 26px;
                    color: $textColor1;
                    font-weight: 400;
                }
                .tool_popup_stock {
                    > p {
                        margin-top: 6px;
                        @include font13;
                        color: $textColor3;
                    }
                }
            }
        }
    }
    .delivery_wrap {
        margin-top: 14px;
    }

    .delivery_box {
        display: flex;
        align-items: flex-start;

        .iconfont_stock {
            @include font14;
            color: #4b4b4d;
            margin: 0 4px 0 0;
        }
        .iconfont_express {
            @include font14;
            color: #4b4b4d;
            margin: 0 4px 0 0;
        }
        .delivery_item {
            @include font13;

            .stock_tip {
                margin-left: 0;
                .stock_tip_trigger {
                    color: $textColor6;
                }
                &::v-deep {
                    .info {
                        padding: 0;
                    }
                }
                .stock_tip_ctn {
                    min-width: 280px;
                    padding: 20px;
                    .stock_num {
                        @include font13;
                        padding: 2px 0;
                        font-weight: 400;
                        color: $textColor3;
                        position: relative;
                        padding-left: 14px;
                        > span {
                            color: $textColor1;
                        }
                        &:before {
                            display: block;
                            content: " ";
                            width: 4px;
                            height: 4px;
                            border-radius: 4px;
                            background: $textColor3;
                            position: absolute;
                            top: 50%;
                            left: 0;
                            margin-top: -2px;
                        }
                    }
                }
            }
            .delivery_btn {
                display: inline-block;
                margin-left: 4px;
            }
        }
        .shipping_info {
            display: inline-block;
            color: $textColor3;
            @media (max-width: 960px) {
                display: block;
                margin-left: -20px;
            }
        }
        .warehouse_tip {
            min-width: 290px;
            @include pc_tip();
            @media (max-width: 960px) {
                @include m_tip();
            }
        }
        .shipping_tip {
            min-width: 300px;
            @include pc_tip();
            @include font13;
            @media (max-width: 960px) {
                @include m_tip();
            }
        }
    }
    .add_box {
        margin-top: 28px;
        padding-bottom: 24px;
        border-bottom: 1px solid $btnBgColor5;
        display: flex;
        align-items: center;
        .qty-box {
            height: 42px;
            width: 85px;
            margin-right: 12px;
            &::v-deep {
                .qty {
                    font-weight: 600;
                    font-size: 14px;
                }
            }
        }
        .add_cart_btn {
            width: auto;
            padding: 0 30px;
            height: 42px;
            .iconfont_cart {
                font-size: 16px;
                margin-right: 8px;
            }
            @media (max-width: 960px) {
                flex: 1;
            }
        }
    }
    .spotlights_box {
        .spotlights_title {
            @include font12;
            color: $textColor1;
            font-weight: 600;
            padding: 16px 0 7px;
        }
        .spotlights_more {
            overflow: hidden;
        }
        .spotlights_ctn_2 {
            display: flex;
            flex-wrap: wrap;
            align-items: center;
            width: 100%;

            @media (max-width: 960px) {
                flex-wrap: nowrap;
                flex-direction: column;
                align-items: flex-start;
            }
            .spotlights_item {
                display: inline-block;
                margin-right: 30px;
                @media (max-width: 960px) {
                    display: flex;
                }
                > a {
                    color: #616265;
                    text-decoration: none;
                    &:hover {
                        color: $textColor1;
                    }
                }
            }
        }
        .spotlights_item {
            position: relative;
            @include font12;
            color: $textColor1;
            padding: 0 0 0 14px;
            margin: 0 0 7px 0;
            &:before {
                display: block;
                content: " ";
                width: 4px;
                height: 4px;
                border-radius: 4px;
                background: $textColor3;
                position: absolute;
                top: 50%;
                left: 0;
                margin-top: -2px;
            }
        }
        .spotlights_btn {
            display: flex;
            min-width: 140px;
            padding: 0 0 0 14px;
            color: $textColor6;
            cursor: pointer;
            align-items: center;
            @include font12;
            .iconfont_arrow {
                font-size: 12px;
                margin: 0 0 0 6px;
                transition: all 0.3s;
                &.iconfont_arrow_down {
                    transform: rotate(180deg);
                }
            }
        }
    }
}

.product_offline_wrap {
    .product_offline_main {
        background: #fff;
        &.product_offline_main2 {
            background: $bgColor1;
        }
        .product_offline_ctn {
            width: 1420px;
            margin: 0 auto;
            &.product_offline_ctn2 {
                padding: 20px 0 48px 0;
            }
            @media (max-width: 1420px) {
                width: 100%;
                padding: 0 3%;
                &.product_offline_ctn2 {
                    padding: 20px 3% 48px 3%;
                }
            }
            .offline_total {
                @include font26;
                color: #707473;
                font-weight: normal;
                margin-bottom: 25px;
                > span {
                    color: $textColor1;
                }
            }
            .tab_box {
                .tab {
                    display: inline-block;
                    @include font16;
                    font-weight: 600;
                    padding-bottom: 10px;
                    cursor: pointer;
                    color: $textColor1;
                    position: relative;
                    &:after {
                        display: block;
                        content: " ";
                        position: absolute;
                        bottom: 0;
                        left: 50%;
                        transform: translateX(-50%);
                        width: 40px;
                        height: 2px;
                        background: #c00000;
                    }
                }
            }
            .result_warning {
                background: #fff;
                border-radius: 2px;
                border: 1px solid #b2d0ec;
                font-size: 14px;
                color: #707070;
                position: relative;
                line-height: 22px;
                padding: 7px 40px 7px 44px;
                box-sizing: border-box;
                margin-bottom: 14px;
                i {
                    font-size: 16px;
                    display: inline-block;
                    top: 10px;
                    width: 16px;
                    height: 16px;
                    line-height: 16px;
                    position: absolute;
                    left: 16px;
                    color: #0060bf;
                }
                a {
                    color: #0070bc;
                    text-decoration: none;
                }
            }
            .result_similar {
                @include font14;
                color: $textColor1;
                margin-bottom: 8px;
            }

            .offline_list {
                li {
                    position: relative;
                    margin-bottom: 10px;
                    animation: all 0.3s;
                    transition: all 0.3s ease-out;
                    &:hover {
                        box-shadow: 0 12px 36px 0 rgb(0 0 0 / 10%);
                    }
                }
            }
            .result_contcat {
                background: #fff;
                padding: 34px 0 38px 0;
                .result_contcat_tit {
                    @include font20;
                    color: $textColor1;
                    font-weight: 600;
                    text-align: center;
                    margin-bottom: 22px;
                }
                .result_contcat_list {
                    display: flex;
                    justify-content: center;
                    align-items: flex-start;
                    @media (max-width: 768px) {
                        flex-wrap: wrap;
                    }
                    .result_contcat_item {
                        display: flex;
                        align-items: center;
                        flex-direction: column;
                        margin-right: 36px;
                        text-decoration: none;
                        cursor: pointer;
                        &:last-child {
                            margin-right: 0;
                        }
                        @media (max-width: 768px) {
                            width: 50%;
                            flex-shrink: 0;
                            margin-right: 0;
                            margin-bottom: 20px;
                        }
                        .ct {
                            display: inline-block;
                            height: 54px;
                            width: 54px;
                            border-radius: 50%;
                            background: #f7f7f7;
                            line-height: 54px;
                            font-size: 20px;
                            color: #616265;
                            text-align: center;
                            margin-bottom: 10px;
                        }
                        .ct_info {
                            font-size: 14px;
                            color: #616265;
                            text-align: center;
                        }
                    }
                }
            }
        }
    }
}
</style>
