<template>
    <div class="order_review">
        <div class="area">
            <div class="crumb">
                <BreadCrumb :list="crumb" />
            </div>
            <!-- <div class="head">
                <i class="icon iconfont" @click="back">&#xe702;</i>
                <h1>{{ $c("pages.Review.writeReview") }}</h1>
            </div> -->
            <!-- <UploadFile type='img' limit='5' :multiple="true"/> -->
            <div class="content" v-show="!popVisiable">
                <section>
                    <!-- <img src="https://9180.oss-cn-chengdu.aliyuncs.com/1.jpg" alt=""> -->
                    <img :src="info.image" alt="" />
                    <div>
                        <nuxt-link tabindex="0" :to="localePath(`/products/${info.products_id}.html`)">{{ info.products_name }}</nuxt-link>
                        <!-- <nuxt-link :to="localePath('/'+info.products_href)">{{info.products_name}}</nuxt-link> -->
                        <!-- <h6>{{info.products_name}}</h6> -->
                        <div>
                            <span>P/N:{{ info.products_model }}</span
                            ><span>SKU:{{ info.products_id }}</span>
                        </div>
                    </div>
                </section>
                <div class="star">
                    <div>
                        <FsStar v-model="form.rating" :editable="true" />
                        <span>{{ ["", $c("pages.Review.poor"), $c("pages.Review.fair"), $c("pages.Review.average"), $c("pages.Review.good"), $c("pages.Review.excellent")][form.rating] }}</span>
                    </div>
                    <div :class="{ h0: form.rating > 2 }">
                        <div>
                            <div>
                                <span>{{ $c("pages.Review.productQuality") }}</span>
                                <FsStar v-model="form.product_quality" :editable="true" />
                            </div>
                            <div>
                                <span>{{ $c("pages.Review.price") }}</span>
                                <FsStar v-model="form.price" :editable="true" />
                            </div>
                        </div>
                        <div>
                            <div>
                                <span>{{ $c("pages.Review.logisticsService") }}</span>
                                <FsStar v-model="form.logistics_service" :editable="true" />
                            </div>
                            <div>
                                <span>{{ $c("pages.Review.presaleService") }}</span>
                                <FsStar v-model="form.pre_sale_service" :editable="true" />
                            </div>
                        </div>
                        <div>
                            <div>
                                <span>{{ $c("pages.Review.others") }}</span>
                                <FsStar v-model="form.others" :editable="true" />
                            </div>
                        </div>
                    </div>
                </div>
                <div class="review" ref="content">
                    <div>
                        <p>{{ $c("pages.Review.yourReview") }} *</p>
                        <span>{{ form.review_content.length }}/5000</span>
                    </div>
                    <textarea
                        class="is_new"
                        @input="checkValue('review_content')"
                        maxlength="5000"
                        v-model.trim="form.review_content"
                        :placeholder="form.rating < 3 ? $c('pages.Review.shareYour') : $c('pages.Review.tellOthers')"></textarea>
                    <validate-error :error="errors.review_content" />
                    <!-- <p class="warning">Please fill in your review.</p> -->
                    <!-- <p class="warning">Please write a review with more than 10 characters.</p> -->
                </div>
                <div class="upload">
                    <div class="img" v-if="0">
                        <img src="https://9180.oss-cn-chengdu.aliyuncs.com/1.jpg" alt="" />
                        <i class="iconfont">&#xe65f;</i>
                        <!-- <div class="add_tag">
                            <i class="iconfont">&#xf054;</i>
                            <span>Add Tags</span>
                        </div> -->
                    </div>
                    <div class="img" v-for="(v, i) in imgTags" :key="i" :class="{ active: editIndex === i }">
                        <img :src="v.img" alt="" @click="chose(v, i)" />
                        <i class="iconfont" tabindex="0" @keyup.enter.stop="delImg(i)" @click="delImg(i)">&#xe65f;</i>
                    </div>
                    <div class="add" tabindex="0" @keyup.enter.stop="keyupEnter" v-show="imgTags.length < 5">
                        <i class="iconfont">&#xf068;</i>
                        <input type="file" accept="image/png, image/jpeg" :title="''" @change="change" ref="file" />
                    </div>
                    <!-- <upload-file type='img' accept="image/jpg,image/png" text="" :limit="5" :multiple="true" :maxSize="5*1024*1024" @change="change"/> -->
                    <fs-popover isAccessible>
                        <p v-html="$c('pages.Review.allow')"></p>
                    </fs-popover>
                </div>
                <div class="tags" v-if="currentImg.img" @mousemove="mousemove" @mouseup="mouseup">
                    <p>{{ $c("pages.Review.clickPicture") }} {{ 5 - currentImg.tag.length }}/5 {{ $c("pages.Review.tags") }}.</p>
                    <div class="picture">
                        <div>
                            <!-- <img src="https://9180.oss-cn-chengdu.aliyuncs.com/1.jpg" alt=""> -->
                            <img :src="currentImg.img" alt="" @click="addTag" draggable="false" />
                            <div
                                class="tag"
                                v-for="(v, i) in currentImg.tag"
                                :key="i"
                                :class="{ left: v.direction === 'left', move: v.name || v.txt }"
                                :style="{ left: v.x + 'px', top: v.y + 'px' }"
                                @mousedown="mousedown($event, i)"
                                @mouseleave="mouseleave">
                                <s></s>
                                <em></em>
                                <div class="content">
                                    <i class="iconfont" v-if="v.name || v.txt" @click="delTag(i)">&#xf30a;</i>
                                    <div>
                                        <p v-if="v.name">{{ v.name }}</p>
                                        <b v-if="v.price" v-html="v.price"></b>
                                        <span v-if="!v.name && !v.txt" @click="addTitle(i)">{{ $c("pages.Review.toEnter") }}</span>
                                        <i v-if="v.txt">{{ v.txt }}</i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <FsButton tabindex="0" :text="$c('pages.Review.save')" type="blackline" @click="save" />
                </div>
                <label class="check_box">
                    <input type="checkbox" tabindex="0" @change="checkValue('check')" v-model="check" />
                    <span
                        v-html="
                            $c('pages.Review.agree').replace('/policies/privacy_policy.html', localePath('/policies/privacy_policy.html')).replace('/policies/terms_of_use.html', localePath('/policies/terms_of_use.html'))
                        "></span>
                </label>
                <validate-error :error="errors.check" />
                <p class="warning"></p>
                <div>
                    <g-recaptcha ref="grecaptcha" @getValidateCode="getValidateCode"></g-recaptcha>
                </div>
                <FsButton tabindex="0" :text="$c('pages.Review.submit')" @click="submit" :loading="btnLoading" />
            </div>
            <div class="content" v-show="popVisiable">
                <div class="sure_type">
                    <div class="waiting_status">
                        <div class="iconfont iconfont_success">&#xe710;</div>
                        <p class="title">{{ $c("pages.Review.thanks") }}</p>
                        <p class="msg" v-html="$c('pages.Products.review_descr')"></p>
                        <div class="return">
                            <fs-button tabindex="0" @click="toProduct" type="lightgray">{{ $c("pages.Products.Continue_Shopping") }}</fs-button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <EditTags :show="editTagsVisiable" @close="hideEditTags" @confirm="confirm" ref="editTags" />
        <!-- <fs-success-popup :show="popVisiable" class="result_tip" @close="hidePop">
            <div slot="header">
                <span v-if="form.rating > 2">{{ $c("pages.Review.thanks") }}</span>
                <span v-else>{{ $c("pages.Review.unpleasant") }}</span>
            </div>
            <p v-if="form.rating > 2">{{ $c("pages.Review.becomeVisible") }}</p>
            <p v-else>{{ $c("pages.Review.resolveIssues") }}</p>
            <div class="btn" v-if="type === 1">
                <FsButton :text="$c('pages.Review.continueShopping')" type="blackline" @click="hidePop" />
            </div>
        </fs-success-popup> -->
    </div>
</template>

<script>
import BreadCrumb from "@/components/BreadCrumb/BreadCrumb"
import FsButton from "@/components/FsButton/FsButton"
import FsStar from "@/components/FsStar/FsStar"
import FsPopover from "@/components/FsPopover"
import FsTip from "@/components/FsTip/FsTip"
import UploadFile from "@/components/UploadFile/UploadFile"
import EditTags from "@/popup/Review/EditTags"
import ValidateError from "@/components/ValidateError/ValidateError"
import FsSuccessPopup from "@/components/FsSuccessPopup/FsSuccessPopup"
import GRecaptcha from "@/components/GRecaptcha/GRecaptcha"
import { mapState, mapGetters, mapMutations } from "vuex"
import { isNeedGrecaptcha } from "@/util/grecaptchaHost"

export default {
    head() {
        return {
            meta: [...this.robotsMeta],
        }
    },
    components: {
        BreadCrumb,
        FsButton,
        FsStar,
        FsTip,
        EditTags,
        UploadFile,
        ValidateError,
        FsSuccessPopup,
        GRecaptcha,
        FsPopover,
    },
    computed: {
        ...mapState({
            website: (state) => state.webSiteInfo.website,
            robotsMeta: (state) => state.meta.robotsMeta,
        }),
    },
    data() {
        return {
            crumb: [{ name: this.$c("pages.Review.myAccount"), url: "/my-account" }, { name: this.$c("pages.Review.orderReview"), url: "/order-review" }, { name: this.$c("pages.Review.writeReview") }],
            editTagsVisiable: false,
            form: {
                rating: 5,
                product_quality: 5,
                price: 5,
                logistics_service: 5,
                pre_sale_service: 5,
                others: 5,
                reviews_img: [],
                review_content: "",
            },
            info: {},
            currentImg: {},
            imgTags: [],
            editing: false, //单个tag
            x: 0,
            y: 0,
            currentTag: {},
            origin: {
                x: 0,
                y: 0,
            }, //tag原点
            editIndex: -1, //选中图片
            errors: {
                review_content: "",
                check: "",
            },
            check: false,
            btnLoading: false,
            type: 1, // 1-详情页，2-订单
            popVisiable: false,
            timer: "",

            recaptchaTp: false,
            recaptchaVal: "",
        }
    },
    asyncData({ app, $axios, store, params, query, $c }) {
        let { oId, pId, offline } = query
        oId = parseInt(oId)
        pId = parseInt(pId) || 1
        if (query.oId) {
            let type = 2
            return $axios
                .get(`/api/orders_review/${oId}/${pId}`, { params: { orders_products_id: pId, orders_id: oId, orders_type: offline } })
                .then((res) => {
                    return {
                        type,
                        info: res.data[0],
                    }
                })
                .catch((err) => {
                    console.log(err)
                })
        } else {
            return $axios
                .get(`/api/showReviews/${pId}`, { params: { products_id: pId } })
                .then((res) => {
                    let bread_crumbs = []
                    if (query.from && query.from === "product" && res.data.bread_crumbs && res.data.bread_crumbs.length) {
                        bread_crumbs = [
                            {
                                name: $c("pages.Products.Home"),
                                url: app.localePath({ name: "home" }),
                            },
                            ...res.data.bread_crumbs,
                            {
                                name: pId,
                                url: app.localePath({ name: "products", params: { id: pId } }),
                            },
                            {
                                name: $c("pages.Review.writeReview"),
                                url: app.localePath({ name: "products", params: { id: pId } }),
                            },
                        ]
                    } else {
                        bread_crumbs = [
                            {
                                name: $c("pages.Review.home"),
                                url: "/",
                            },
                            {
                                name: $c("pages.Review.myAccount"),
                                url: "/my-account",
                            },
                            {
                                name: $c("pages.Review.orderReview"),
                                url: "/order-review",
                            },
                            {
                                name: $c("pages.Review.writeReview"),
                                url: "",
                            },
                        ]
                    }
                    return {
                        crumb: bread_crumbs,
                        info: res.data,
                    }
                })
                .catch((err) => {
                    console.log(err)
                })
        }
    },
    async mounted() {
        // if (!process.client) return
        // let res
        // let { oId, pId } = this.$route.query
        // oId = parseInt(oId)
        // pId = parseInt(pId)
        // if (this.$route.query.oId) {
        //     this.type = 2
        //     res = await this.$axios.get(`/api/orders_review/${oId}/${pId}`, { params: { orders_products_id: pId, orders_id: oId } })
        //     this.info = res.data[0]
        // } else {
        //     res = await this.$axios.get(`/api/showReviews/${pId}`, { params: { products_id: pId } })
        //     this.info = res.data
        // }
    },
    watch: {
        "form.rating": {
            handler(v) {
                if (v < 3) {
                    this.form.product_quality = 0
                    this.form.price = 0
                    this.form.logistics_service = 0
                    this.form.pre_sale_service = 0
                    this.form.others = 0
                } else {
                    this.form.product_quality = 5
                    this.form.price = 5
                    this.form.logistics_service = 5
                    this.form.pre_sale_service = 5
                    this.form.others = 5
                }
                console.log(this.form)
            },
        },
    },
    methods: {
        change(e) {
            console.log(e.target.files)
            console.log(this.$refs.file.value)
            if (!e.target.files.length) return
            for (let i = 0; i < e.target.files.length; i++) {
                if (e.target.files[i].size > 5 * 1024 * 1024) {
                    return
                }
            }
            // let reader = new FileReader()
            // reader.readAsDataURL(e.target.files[0])
            // reader.onload = (q) => {
            //     console.log(q,reader.result)
            //     this.form.reviews_img.push(reader.result)
            // }
            const img = window.URL.createObjectURL(e.target.files[0])
            this.form.reviews_img.push(e.target.files[0])
            this.imgTags.push({ img, tag: [] })
            this.$refs.file.value = ""
            // {direction: '', x: 0, y: 0, txt: '', name: '', price: ''}
        },
        chose(v, i) {
            if (this.editIndex === i) return
            this.currentImg = this.imgTags[i]
            this.editIndex = i
            const currentTag = this.currentImg.tag.slice(-1)
            if (!currentTag.length || (currentTag.length && currentTag[0].txt)) {
                this.editing = false
            }
            if (currentTag.length && !currentTag[0].txt) {
                this.editing = true
            }
        },
        addTag(e) {
            if (this.currentImg.tag.length >= 5) return
            const width = document.querySelector(".picture>div img").offsetWidth
            const height = document.querySelector(".picture>div img").offsetHeight
            let direction
            if (this.editing) {
                this.currentTag = this.currentImg.tag.slice(-1)[0]
                direction = e.offsetX > width / 2 ? "right" : "left"
                this.currentTag.direction = direction
                this.currentTag.x = e.offsetX
                this.currentTag.y = e.offsetY
                return
            }
            this.editing = true
            direction = e.offsetX > width / 2 ? "right" : "left"
            this.currentImg.tag.push({ direction, x: e.offsetX, y: e.offsetY, txt: "", name: "", price: "", left: (e.offsetX * 100) / width, top: (e.offsetY * 100) / height })
        },
        delTag(i) {
            this.currentImg.tag.splice(i, 1)
        },
        addTitle(i) {
            this.editTagsVisiable = true
            this.$refs.editTags.init()
        },
        delImg(i) {
            this.form.reviews_img.splice(i, 1)
            this.imgTags.splice(i, 1)

            if (i === this.editIndex) {
                this.currentImg = {}
            } else if (this.editIndex > i) {
                this.editIndex--
            }
        },
        confirm(v) {
            this.editing = false
            this.editTagsVisiable = false
            if (typeof v === "object") {
                console.log(v.products_name)
                this.currentTag.name = v.products_name
                this.currentTag.price = v.products_price
                this.currentTag.pId = v.products_id
            } else {
                this.currentTag.txt = v
            }
        },
        hideEditTags() {
            this.editTagsVisiable = false
        },
        save() {
            this.editIndex = -1
            this.currentImg = {}
        },
        submit() {
            this.checkValue("review_content")
            this.checkValue("check")
            if (this.errors.review_content) {
                this.$refs.content?.scrollIntoView()
                return
            }
            if (this.errors.check) return
            this.btnLoading = true
            const data = new FormData()
            this.form.reviews_img.forEach((v) => {
                data.append("reviews_img[]", v)
            })
            // delete this.form.reviews_img
            for (const k in this.form) {
                if (k !== "reviews_img") {
                    data.append(k, this.form[k])
                }
            }
            data.append("products_id", this.info.products_id)
            data.append("privacy_policy", 1)
            if (this.type === 2) {
                data.append("orders_products_id", this.info.orders_products_id)
                data.append("orders_id", this.info.orders_id)
                data.append("orders_type", this.$route.query.offline)
            }
            const tagInfo = this.imgTags.map((v, i) => {
                const tag = v.tag.map((y) => {
                    return {
                        left: y.left,
                        top: y.top,
                        txt: y.txt,
                        pid: y.pId,
                        toward: y.direction === "left" ? 1 : 2,
                    }
                })
                return {
                    img_content: tag,
                    img_id: i,
                }
            })
            data.append("tag_info", JSON.stringify(tagInfo))

            if (isNeedGrecaptcha(window.location.hostname)) {
                if (!this.recaptchaTp && this.$refs.grecaptcha) {
                    this.$refs.grecaptcha.clickRecaptcha()
                    return
                }
            }

            this.$axios
                .post(this.type === 1 ? "/api/submitReviews" : "/api/view_reviews_submit", data, this.type === 1 ? { headers: { "g-recaptcha-response": this.recaptchaVal } } : {})
                .then((res) => {
                    this.btnLoading = false
                    console.log(123, res)
                    if (res.code === 200 && res.status === "sensiWords" && this.website === "cn") {
                        for (let key in res.errors) {
                            console.log(`${res.errors[key]}`)
                            this.errors[key] = this.$c("form.form.errors.sensiWords")
                        }
                        return
                    }
                    if (res.code === 200) {
                        this.popVisiable = true
                        // this.$message.success(this.$c('pages.Review.thanks'))
                        // this.timer = setTimeout(() => {
                        //     this.hidePop()
                        // }, 2500)

                        this.initGrecaptcha()
                        if (this.$refs.grecaptcha) {
                            this.$refs.grecaptcha.grecaptchaError = ""
                        }
                    }
                })
                .catch((err) => {
                    this.btnLoading = false
                    this.initGrecaptcha()
                    if (isNeedGrecaptcha(window.location.hostname)) {
                        if (err.code === 409 && this.$refs.grecaptcha) {
                            this.$refs.grecaptcha.grecaptchaError = this.$c("pages.Login.regist.grecaptcha_error")
                        }
                    }
                    if (err.code === 403) {
                        this.$message.error(err.message)
                    }
                })
        },
        hidePop() {
            clearTimeout(this.timer)
            this.$router.go(-1)
            this.popVisiable = false
        },
        mousedown(e, i) {
            this.currentTag = this.currentImg.tag[i]
            if (!this.currentTag.txt && !this.currentTag.name) return
            this.move = true
            this.x = e.x
            this.y = e.y
            this.origin.x = this.currentTag.x
            this.origin.y = this.currentTag.y
        },
        mousemove(e) {
            if (!this.move) return
            this.currentTag.x = this.origin.x + e.x - this.x
            this.currentTag.y = this.origin.y + e.y - this.y
            const dom = document.querySelector(".picture>div img")
            this.currentTag.x <= 0 && (this.currentTag.x = 0)
            this.currentTag.y <= 0 && (this.currentTag.y = 0)
            this.currentTag.x >= dom.offsetWidth - 16 && (this.currentTag.x = dom.offsetWidth - 16)
            this.currentTag.y >= dom.offsetHeight - 16 && (this.currentTag.y = dom.offsetHeight - 16)
            this.currentTag.top = (this.currentTag.x * 100) / dom.offsetHeight
            this.currentTag.left = (this.currentTag.x * 100) / dom.offsetWidth
        },
        mouseup(e) {
            this.move = false
            const width = document.querySelector(".picture>div img").offsetWidth
            if (this.currentTag.x > width / 2) {
                this.currentTag.direction = "right"
            } else {
                this.currentTag.direction = "left"
            }
        },
        mouseleave(e) {
            // this.move = false
        },
        checkValue(v, y) {
            console.log(v, y)
            const val = this.form[v]
            switch (v) {
                case "review_content":
                    if (!val) {
                        this.errors[v] = this.$c("pages.Review.form.content")
                    } else if (val.length < 10) {
                        this.errors[v] = this.$c("pages.Review.form.contentMin")
                    } else {
                        this.errors[v] = ""
                    }
                    break
                case "check":
                    if (!this.check) {
                        this.errors[v] = this.$c("pages.Review.form.agree")
                    } else {
                        this.errors[v] = ""
                    }
                    break
                default:
                    break
            }
        },
        // 谷歌人机校验
        getValidateCode(val) {
            this.recaptchaTp = val.recaptchaTp
            if (val.recaptchaTp) {
                this.recaptchaVal = val.recaptchaVal
                this.submit()
            }
        },
        // 初始化谷歌验证
        initGrecaptcha() {
            this.recaptchaTp = false
            this.recaptchaVal = ""
        },
        toProduct() {
            let id = this.info.products_id
            this.$router.push(this.localePath({ name: "products", params: { id } }))
        },
        // back
        back() {
            this.$router.go(-1)
        },
        keyupEnter() {
            if (this.$refs.file) {
                this.$refs.file.click()
            }
        },
    },
}
</script>

<style lang="scss" scoped>
.order_review {
    // background: $bgColor1;
    background: #fff;
    color: #19191a;
    font-weight: 400;
    font-size: 14px;
    @include accountBox;
    @mixin click() {
        cursor: pointer;
        &:hover {
            text-decoration: underline;
        }
    }
    .warning {
        font-size: 13px;
        color: #c00000;
        line-height: 20px;
    }
    input[type="checkbox"] {
        font-size: 14px;
        width: 14px;
        height: 14px;
    }
    ::v-deep .fs-button {
        width: auto;
        padding: 0 30px;
        min-width: 100px;
        @media (max-width: 768px) {
            width: 100%;
        }
        &.fs-button-gray {
            border-color: #707070;
        }
        .box {
            white-space: nowrap;
        }
    }
    ::v-deep .result_tip {
        .fs-popup-header {
            padding-top: 6px;
            .header_slot {
                font-size: 16px;
            }
            .iconfont_close {
                right: 20px;
            }
        }
        .fs-popup-body {
            .slot-wraper {
                padding-left: 40px;
            }
            .btn {
                padding-top: 30px;
                display: flex;
                justify-content: flex-end;
            }
        }
    }
    .area {
        @include accountWidthBox;
        max-width: 1200px;
        margin: 0 auto;
        width: 84vw;
        .head {
            display: flex;
            align-items: center;
            padding: 0 0 16px;
            > i {
                width: 28px;
                height: 16px;
                font-size: 16px;
                line-height: 1;
                padding: 0 6px;
                color: #19191a;
            }
            > h1 {
                font-size: 20px;
                font-weight: 600;
            }
        }
        > .content {
            background: #fff;
            // padding: 24px 24px 40px;
            > div,
            > section {
                max-width: 725px;
            }
            > section {
                max-width: 100%;
                display: flex;
                padding-bottom: 20px;
                border-bottom: 1px solid #e5e5e5;
                img {
                    width: 100px;
                    height: 100px;
                    margin-right: 20px;
                }
                > div {
                    a {
                        @include font14;
                        color: #19191a;
                        max-width: 550px;
                        font-weight: 600;
                        margin-bottom: 16px;
                        display: -webkit-box;
                        -webkit-box-orient: vertical;
                        -webkit-line-clamp: 2;
                        overflow: hidden;
                    }
                    > div {
                        span {
                            @include font13;
                            color: #707070;
                            &:first-child {
                                margin-right: 20px;
                            }
                        }
                    }
                }
            }
            .star {
                > div:first-child {
                    padding: 24px 0 30px;
                    display: flex;
                    align-items: center;
                    ::v-deep .fs_star {
                        i {
                            width: 28px;
                            height: 20px;
                            font-size: 20px;
                            padding-right: 8px;
                            &.active {
                                color: #fae14b;
                            }
                        }
                    }
                    > span {
                        margin-left: 4px;
                    }
                }
                > div:last-child {
                    max-height: 250px;
                    transition: all 0.2s;
                    &.h0 {
                        max-height: 0;
                        overflow: hidden;
                    }
                    > div {
                        display: flex;
                        justify-content: space-between;
                        > div {
                            display: flex;
                            margin-bottom: 22px;
                            span {
                                width: 160px;
                                color: #707070;
                            }
                            ::v-deep .fs_star {
                                i {
                                    width: 28px;
                                    height: 20px;
                                    font-size: 20px;
                                    padding-right: 8px;
                                    &.active {
                                        color: #fae14b;
                                    }
                                }
                            }
                        }
                    }
                }
            }
            .review {
                padding: 12px 0 24px;
                > div {
                    display: flex;
                    justify-content: space-between;
                    margin-bottom: 10px;
                    > span {
                        font-size: 13px;
                        color: $textColor3;
                    }
                }
            }
            .upload {
                display: flex;
                flex-wrap: wrap;
                align-items: center;
                padding-bottom: 20px;
                // padding-bottom: 4px;
                .img,
                .add {
                    width: 120px;
                    height: 120px;
                    border-radius: 3px;
                    margin: 0 20px 0 0;
                }
                .img {
                    border: 1px solid #e5e5e5;
                    padding: 6px;
                    position: relative;
                    cursor: pointer;
                    &.active {
                        border-color: #707070;
                    }
                    img {
                        width: 100%;
                        height: 100%;
                        object-fit: contain;
                    }
                    > i {
                        position: absolute;
                        top: 6px;
                        right: 6px;
                        cursor: pointer;
                        color: #fff;
                        padding: 5px;
                        background: rgba($color: #000000, $alpha: 0.3);
                    }
                    .add_tag {
                        position: absolute;
                        top: 48px;
                        left: 19px;
                        width: 83px;
                        height: 24px;
                        background: #19191a;
                        border-radius: 12px;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        color: #fff;
                        cursor: pointer;
                        i {
                            font-size: 12px;
                            color: #fff;
                            margin-right: 2px;
                        }
                        span {
                            font-size: 12px;
                        }
                    }
                }
                .add {
                    border: 1px solid #e5e5e5;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    margin-right: 10px;
                    position: relative;
                    i {
                        color: #8d8d8f;
                    }
                    input {
                        position: absolute;
                        top: 0;
                        left: 0;
                        width: 100%;
                        height: 100%;
                        opacity: 0;
                        cursor: pointer;
                    }
                }
                ::v-deep .fs-tip {
                    .info {
                        padding: 10px 16px;
                        min-width: 300px;
                        text-align: left;
                        @media (max-width: 960px) {
                            padding: 40px 20px;
                        }
                    }
                }
            }
            .tags {
                background: #f0f0f0;
                margin: 0 -24px;
                padding: 0 24px 40px;
                max-width: 100vw;
                > p {
                    color: #707070;
                    padding: 18px 0;
                }
                .picture {
                    width: 600px;
                    max-width: calc(100vw - 32px);
                    background: #fff;
                    position: relative;
                    text-align: center;
                    cursor: url("https://9180.oss-cn-chengdu.aliyuncs.com/mouse.png"), auto;
                    user-select: none;
                    border: 1px solid #ccc;
                    > div {
                        position: relative;
                        display: inline-block;
                        img {
                            // min-width: 100%;
                            // min-height: 200px;
                            max-width: 100%;
                            max-height: 600px;
                            object-fit: contain;
                            vertical-align: top;
                        }
                        .tag {
                            position: absolute;
                            top: 200px;
                            left: 400px;
                            &.move {
                                cursor: all-scroll;
                            }
                            &.left {
                                > s::before {
                                    right: -26px;
                                }
                                .content {
                                    right: auto;
                                    left: calc(100% + 47px);
                                    &::after {
                                        right: auto;
                                        left: -12px;
                                        top: 11px;
                                        border-color: transparent transparent #fff #fff;
                                    }
                                    > i {
                                        order: 2;
                                        margin-right: 0;
                                        margin-left: 10px;
                                    }
                                    > div {
                                        i {
                                            text-align: left;
                                        }
                                    }
                                }
                            }
                            > s {
                                position: absolute;
                                top: 0;
                                left: 0;
                                width: 16px;
                                height: 16px;
                                border-radius: 8px;
                                background: #fff;
                                border: 4px solid rgba(0, 0, 0, 0.6);
                                &::before {
                                    content: "";
                                    width: 22px;
                                    height: 1px;
                                    background: #fff;
                                    position: absolute;
                                    right: 12px;
                                    top: 4px;
                                }
                            }
                            .content {
                                width: 160px;
                                height: 50px;
                                background: #fff;
                                border-radius: 4px;
                                display: flex;
                                align-items: center;
                                padding: 10px;
                                position: absolute;
                                top: 50%;
                                right: calc(100% + 33px);
                                transform: translateY(-16px);
                                &::after {
                                    content: "";
                                    position: absolute;
                                    right: -12px;
                                    top: 11px;
                                    border-width: 14px;
                                    border-color: #fff #fff transparent transparent;
                                    border-style: solid;
                                    transform: rotate(45deg) skew(15deg, 15deg);
                                    border-top-right-radius: 4px;
                                }
                                > i {
                                    cursor: pointer;
                                    font-size: 12px;
                                    margin-right: 10px;
                                    color: #707070;
                                    cursor: pointer;
                                }
                                > div {
                                    font-size: 12px;
                                    text-align: left;
                                    width: 118px;
                                    p,
                                    i {
                                        display: block;
                                        overflow: hidden;
                                        text-overflow: ellipsis;
                                        white-space: nowrap;
                                    }
                                    b {
                                        font-weight: 600;
                                    }
                                    span {
                                        color: #8d8d8f;
                                        cursor: pointer;
                                    }
                                    i {
                                        font-style: normal;
                                        text-align: right;
                                    }
                                }
                            }
                        }
                    }
                }
                > button {
                    width: 92px;
                    margin-top: 20px;
                }
            }
            > label {
                display: flex;
                align-items: center;
                margin: 16px 0 0;
                cursor: pointer;
                input {
                    margin-right: 8px;
                }
                span {
                    color: #707070;
                }
                ::v-deep a:focus-visible {
                    @include focusVisible;
                }
            }
            > button {
                margin-top: 30px;
                width: 100px;
            }
        }
    }

    @include mediaM {
        .area {
            .content {
                padding-left: 16px;
                padding-right: 16px;
                .star {
                    > div:last-child {
                        > div {
                            display: block;
                        }
                    }
                }
                .tags {
                    margin: 0 -16px;
                    padding: 0 16px 40px;
                    .picture {
                        // height: 400px;
                    }
                    > button {
                        width: 100%;
                    }
                }
                > button {
                    width: 100%;
                }
            }
        }
    }
    .area > .content > .sure_type {
        max-width: 100%;
        margin-top: 100px;
        margin-bottom: 72px;
        .waiting_status {
            margin: 0 48px;
            .iconfont_success {
                display: block;
                text-align: center;
                font-size: 50px;
                color: #10a300;
                line-height: 50px;
            }
            .title {
                @include font16;
                margin: 16px auto 8px auto;
                font-weight: 600;
                color: $textColor1;
                text-align: center;
            }
            .msg {
                @include font14;
                margin-bottom: 32px;
                color: $textColor3;
                text-align: center;
            }
            .return {
                text-align: center;
                margin-bottom: 80px;
            }
        }
    }
    @media (max-width: 768px) {
        padding: 0;
        .area {
            width: 100%;
            .content {
                padding: 24px 16px 40px;
            }
        }
    }
}
</style>
