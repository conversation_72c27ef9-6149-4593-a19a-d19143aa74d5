import FsTip from "@/components/FsTip/FsTip"
import FsPopover from "@/components/FsPopover"
import FsPopupNew from "@/components/FsPopupNew/FsPopupNew.vue"
import FsButton from "@/components/FsButton/FsButton.vue"
import FsSelect from "@/components/FsSelect/FsSelect.vue"
import SelectCountry from "@/components/SelectCountry/SelectCountry.vue"
import ValidateError from "@/components/ValidateError/ValidateError"
import FsSuccessPopup from "@/components/FsSuccessPopup/FsSuccessPopup"
import FsMessage from "@/components/FsMessage/FsMessage"
import CnArea from "@/components/CnArea/CnArea"
import GoogleAddressAutoComplementInput from "@/components/GoogleAddressAutoComplementInput/GoogleAddressAutoComplementInput.vue"
import { mapState, mapMutations, mapGetters, mapActions } from "vuex"
import { cn_mobile_tel, cn_new_tel, email_valdate } from "@/constants/validate"
// import TelCode from "@/components/TelCode/TelCode.vue"
import TelCodeNew from "@/components/TelCode/TelCodeNew.vue"
import FsWarn from "@/components/FsWarn/FsWarn"
import { debounce } from "@/util/util.js"
import { buriedPointMixin } from "@/pages/Quote/buriedPointMixin"

export const mixin = {
    mixins: [buriedPointMixin],
    components: {
        FsTip,
        FsPopover,
        FsButton,
        FsPopupNew,
        FsSelect,
        SelectCountry,
        ValidateError,
        FsSuccessPopup,
        CnArea,
        // TelCode,
        FsPopover,
        FsMessage,
        TelCodeNew,
        FsWarn,
        GoogleAddressAutoComplementInput,
    },
    props: {
        list: {
            type: Array,
            default: () => [],
        },
        params: {
            type: Object,
            default: () => {
                return {}
            },
        },
        shippingAddress: {
            // type: Object,
            default: () => ({}),
        },
        exclude: {
            type: Array,
            default: () => [],
        },
        defaultBillingAddress: {
            // type: Object,
            default: () => ({}),
        },
        billingSync: {
            type: Boolean,
            default: false,
        },
        currentStep: "",
        fromPage: "",
    },
    data() {
        return {
            addressTypeOptions: [
                { name: this.$c("pages.confirmOrder.address.businessType"), value: "BusinessType" },
                { name: this.$c("pages.confirmOrder.address.individualType"), value: "IndividualType" },
            ],
            stateOptions: [],
            states: [],
            citysOPtions: [],
            form: {
                companyType: "",
                entryFirstname: "",
                entryLastname: "",
                entryCompany: "",
                entryTaxNumber: "",
                entryStreetAddress: "",
                entrySuburb: "",
                entryCity: "",
                entryCountryId: "",
                entryState: "",
                entryPostcode: "",
                entryTelephone: "",
                addressBookId: "",
                telPrefix: "",
                entryEori: "",
                identityType: "iec_number",
                identityValue: "",
                entryEmail: "",
            },
            errors: {
                companyType: "",
                entryFirstname: "", //
                entryLastname: "", //
                entryCompany: "", //
                entryTaxNumber: "", //
                entryCountryId: "",
                entryStreetAddress: "", //
                entrySuburb: "", //
                entryCity: "", //
                entryState: "",
                entryPostcode: "", //
                entryTelephone: "", //
                entryEori: "",
                identityValue: "",
                entryEmail: "",
            },
            sensitiveEntrySuburb: "",
            sensitiveEntryStreetAddress: "",
            country: { name: "", code: "" },
            btnLoading: false,
            isEu: [21, 222, 73, 81, 105, 150, 124, 57, 103, 195, 84, 171, 14, 203, 72, 132, 55, 170, 97, 56, 189, 190, 67, 117, 123, 175, 33, 53, 141],
            notEu: [244, 98, 27, 236, 242, 126, 2, 140, 160, 204, 5, 122, 182, 245, 70, 85, 87, 75, 134, 137, 12, 250, 243, 228, 253, 254],
            US: [38, 223, 138, 172],
            SG: [32, 100, 36, 116, 146, 129, 168, 188, 209, 61, 230],
            AU: [13, 153],
            DE: [
                21, 222, 73, 81, 105, 150, 124, 57, 103, 195, 84, 171, 14, 203, 72, 132, 55, 170, 97, 56, 189, 190, 67, 117, 123, 175, 33, 53, 141, 244, 98, 27, 236, 242, 126, 2, 140, 160, 204, 5, 122, 182, 245, 70, 85,
                87, 75, 134, 137, 12, 250, 243, 228, 253, 254,
            ],
            RU: [176],
            EU_code: {
                73: "FR",
                81: "DE",
                203: "SE",
                56: "CZ",
                150: "NL",
                124: "LU",
                171: "PT",
                84: "EL",
                103: "IE",
                170: "PL",
                123: "LT",
                117: "LV",
                67: "EE",
                72: "FI",
                14: "AT",
                53: "HR",
                97: "HU",
                189: "SK",
                175: "RO",
                33: "BG",
                21: "BE",
                105: "IT",
                195: "ES",
                55: "CY",
                190: "SI",
                57: "DK",
                132: "MT",
                141: "MC",
            },
            Reunion: [87, 75, 174, 137, 254, 134],
            taxTip: this.$c("pages.confirmOrder.address.taxTip"),
            taxHolder: "",
            isRequired: false,
            taxVisible: false,
            codeVisible: true,
            trans: {
                tax: this.$c("pages.confirmOrder.address.taxNumber"),
                ad: this.$c("pages.confirmOrder.address.address"),
                ad2: this.$c("pages.confirmOrder.address.address2"),
                city: this.$c("pages.confirmOrder.address.city"),
                state: this.$c("pages.confirmOrder.address.stateProvinceRegion"),
                code: this.$c("pages.confirmOrder.address.zipCode"),
            },
            companyKey: 1,
            timer: "",
            isQuote: false,
            isEdit: false,
            isReunion: false,
            address: [],
            cnRedirect: { currency: "CNY", iso_code: "CN", language: "简体中文" },
            showSGTip: false,
            identityNumList: [
                {
                    name: this.$c("pages.confirmOrder.identityNumList")[0],
                    value: "iecNumber",
                    errMsg: this.$c("pages.confirmOrder.identityNumErrorList")[0],
                },
                {
                    name: this.$c("pages.confirmOrder.identityNumList")[1],
                    value: "identityCard",
                    errMsg: this.$c("pages.confirmOrder.identityNumErrorList")[1],
                },
                {
                    name: this.$c("pages.confirmOrder.identityNumList")[2],
                    value: "passportNumber",
                    errMsg: this.$c("pages.confirmOrder.identityNumErrorList")[2],
                },
                {
                    name: this.$c("pages.confirmOrder.identityNumList")[3],
                    value: "driverLicenseNumber",
                    errMsg: this.$c("pages.confirmOrder.identityNumErrorList")[3],
                },
            ],
            cnTrReg: new RegExp("([\u4E00-\u9FFF]|[\u3002\uff1b\uff0c\uff1a\u201c\u201d\uff08\uff09\u3001\uff1f\u300a\u300b\uff01\u3010\u3011\uffe5])+", "g"),
            isCompany: false,
            jpSearchLoading: false,
            jpProviceList: [
                { name: "北海道", value: "北海道" },
                { name: "青森県", value: "青森県" },
                { name: "岩手県", value: "岩手県" },
                { name: "宮城県", value: "宮城県" },
                { name: "秋田県", value: "秋田県" },
                { name: "山形県", value: "山形県" },
                { name: "福島県", value: "福島県" },
                { name: "茨城県", value: "茨城県" },
                { name: "栃木県", value: "栃木県" },
                { name: "群馬県", value: "群馬県" },
                { name: "埼玉県", value: "埼玉県" },
                { name: "千葉県", value: "千葉県" },
                { name: "東京都", value: "東京都" },
                { name: "神奈川県", value: "神奈川県" },
                { name: "新潟県", value: "新潟県" },
                { name: "富山県", value: "富山県" },
                { name: "石川県", value: "石川県" },
                { name: "福井県", value: "福井県" },
                { name: "山梨県", value: "山梨県" },
                { name: "長野県", value: "長野県" },
                { name: "岐阜県", value: "岐阜県" },
                { name: "静岡県", value: "静岡県" },
                { name: "愛知県", value: "愛知県" },
                { name: "三重県", value: "三重県" },
                { name: "滋賀県", value: "滋賀県" },
                { name: "京都府", value: "京都府" },
                { name: "大阪府", value: "大阪府" },
                { name: "兵庫県", value: "兵庫県" },
                { name: "奈良県", value: "奈良県" },
                { name: "和歌山県", value: "和歌山県" },
                { name: "鳥取県", value: "鳥取県" },
                { name: "島根県", value: "島根県" },
                { name: "岡山県", value: "岡山県" },
                { name: "広島県", value: "広島県" },
                { name: "山口県", value: "山口県" },
                { name: "徳島県", value: "徳島県" },
                { name: "香川県", value: "香川県" },
                { name: "愛媛県", value: "愛媛県" },
                { name: "高知県", value: "高知県" },
                { name: "福岡県", value: "福岡県" },
                { name: "佐賀県", value: "佐賀県" },
                { name: "長崎県", value: "長崎県" },
                { name: "熊本県", value: "熊本県" },
                { name: "大分県", value: "大分県" },
                { name: "宮崎県", value: "宮崎県" },
                { name: "鹿児島県", value: "鹿児島県" },
                { name: "沖縄県", value: "沖縄県" },
            ],
            jpAddressInfo: {
                post_code: "",
                address: "",
                city: "",
                area: "",
            },
            showErrorTip: false,
            errorMsg: "",
            redirectInfo: {},
            windowHeight: 0,
            applyDefault: 0,
            addressInfo: {},
        }
    },
    inject: {
        quotesId: {
            default: "", // 默认值
        },
    },
    computed: {
        ...mapState({
            selectCountry: (state) => state.selectCountry,
            website: (state) => state.webSiteInfo.website,
            current_iso_code: (state) => state.webSiteInfo.iso_code,
            userInfo: (state) => state.userInfo.userInfo,
            isMobile: (state) => state.device.isMobile,
            listCountryState: (state) => state.listCountryState.countryList,
        }),
        ...mapGetters({
            isCn: "webSiteInfo/isCn",
            isCnTr: "webSiteInfo/isCnTr",
        }),
        countryList() {
            let filterCountry = this.exclude
            if (this.isCurrentBilling) {
                filterCountry = []
            }
            return this.listCountryState?.map((v) => v.code).filter((v) => !filterCountry.includes(v))
        },
        // 204 瑞士  160 挪威
        isEORI() {
            return [204, 160].includes(this.form.entryCountryId) && this.isCurrentShipping
        },
        isJp() {
            return this.form.entryCountryId === 107
        },
        isSG() {
            return this.website === "sg" && this.form.entryCountryId === 188
        },
        // US/UK/北爱尔兰
        isEffectiveness() {
            return [223, 222].includes(this.form.entryCountryId)
        },
        //223 美国  38 加拿大
        isUSOrCanada() {
            return [223, 38].includes(this.form.entryCountryId)
        },
        //99 印度
        isIN() {
            return this.form.entryCountryId === 99 && this.isCurrentShipping
        },
        telCodePlaceholder() {
            const { showSGTip, isJp, isUSOrCanada } = this
            let msg = showSGTip ? this.$c("pages.confirmOrder.address.num8") : ""
            if (isJp) {
                msg = this.$c("form.form.placeHolder.telephone")
            }
            if (isUSOrCanada) {
                msg = this.$c("pages.confirmOrder.address.num8").replace(8, 10)
            }
            return msg
        },
        INNumPlaceholder() {
            return this.identityNumList.find((i) => i.value === this.form.identityType)?.name || ""
        },
        INNumErrorMsg() {
            return this.identityNumList.find((i) => i.value === this.form.identityType)?.errMsg || ""
        },
        isCurrentShipping() {
            return this.currentStep == "Shipping"
        },
        isCurrentBilling() {
            return this.currentStep == "Billing"
        },
        addressType() {
            return this.isCurrentShipping ? 0 : 2
        },
    },
    mounted() {
        this.isQuote = location.pathname.includes("/quote")
        this.getCountry && this.getCountry()
        this.getCountryState && this.getCountryState()
        if (this.website == "cn") {
            this.form.entryCountryId = 44
            this.form.telPrefix = "+86"
        }
        this.isCompany = this.userInfo?.isCompanyOrganizationUser
        this.updateWindowHeight()
        window.addEventListener("resize", this.updateWindowHeight)
    },
    beforeDestroy() {
        window.removeEventListener("resize", this.updateWindowHeight)
    },
    methods: {
        ...mapActions({
            updateSiteInfo: "selectCountry/updateSiteInfo",
            getCountry: "selectCountry/getCountry",
            getCountryState: "listCountryState/getCountryState",
        }),
        updateWindowHeight() {
            this.windowHeight = window.innerHeight
        },
        initForm(v, type) {
            console.log(v)
            this.addressInfo = v || {}
            this.heightChange && this.heightChange()
            this.form = {
                companyType: "BusinessType",
                entryFirstname: "",
                entryLastname: "",
                entryCompany: "",
                entryTaxNumber: "",
                entryStreetAddress: "",
                entrySuburb: "",
                entryCity: "",
                entryCountryId: "",
                entryState: "",
                entryPostcode: "",
                entryTelephone: "",
                addressBookId: "",
                telPrefix: "",
                entryEori: "",
                identityType: "iec_number",
                identityValue: "",
                entryEmail: "",
            }
            this.errors = {
                companyType: "",
                entryFirstname: "",
                entryLastname: "",
                entryCompany: "",
                entryTaxNumber: "",
                entryCountryId: "",
                entryStreetAddress: "",
                entrySuburb: "",
                entryCity: "",
                entryState: "",
                entryPostcode: "",
                entryTelephone: "",
                entryEori: "",
                identityValue: "",
                entryEmail: "",
            }
            this.address = []
            this.applyDefault = 0
            this.sensitiveEntrySuburb = ""
            this.sensitiveEntryStreetAddress = ""
            if (this.website == "cn") {
                this.form.entryCountryId = 44
                this.form.telPrefix = "+86"
                this.form.companyType = "IndividualType"
            }

            if (v?.addressBookId) {
                this.isEdit = true
                this.address = [v.entryState, v.entryCity, v.entrySuburb]

                this.title = this.$c(`pages.confirmOrder.address.${type === "Shipping" ? "editShippingAddress" : "editBillingAddress"}`)
                for (const k in this.form) {
                    this.form[k] = v[k]
                }
                this.form.companyType = this.form.companyType || "xx"

                if (this.isIN) {
                    this.identityNumList.forEach((i) => {
                        const { value: key } = i
                        if (v[key]) {
                            this.form.identityType = key
                            this.form.identityValue = v[key]
                        }
                    })
                }
                // 历史的individual地址改为企业地址
                if (this.isCompany && this.isCurrentBilling && this.form.companyType != "BusinessType") {
                    this.form.companyType = "BusinessType"
                    this.typeChange()
                }

                this.country = { name: v.countryName, code: v.countryCode }
                console.log("v.countryName", v)

                const currentCountry = this.listCountryState?.find((item) => {
                    return item.id === v.entryCountryId
                })

                this.jpProviceList =
                    currentCountry?.stateList?.length &&
                    currentCountry.stateList.map((item) => {
                        return {
                            name: item.label,
                            value: item.code,
                        }
                    })

                this.stateOptions =
                    currentCountry?.stateList?.length &&
                    currentCountry.stateList.map((item) => {
                        return {
                            name: item.label,
                            value: item.code,
                        }
                    })

                this.citysOPtions =
                    currentCountry?.cityList?.length &&
                    currentCountry.cityList.map((item) => {
                        return {
                            name: item.label,
                            value: item.code,
                        }
                    })
                this.citysOPtions &&
                    this.citysOPtions.forEach((c) => {
                        if (v.entryCity == c.name) {
                            this.form.entryCity = c.value
                        }
                    })
                // console.log("赋值成功了吗", this.form.entryCountryId)
                this.checkAll(this.form.entryCountryId)
                // this.initCheck()
            } else {
                this.isEdit = false
                this.title = this.$c(`pages.confirmOrder.address.${type === "Shipping" ? "addShippingAddress" : "addBillingAddress"}`)
                if (this.website !== "cn") {
                    this.getCountry && this.getCountry()
                    this.getCountryState && this.getCountryState()
                    // const currentCountry = this.selectCountry.country_list.find((item) => {
                    //     return this.current_iso_code === item.iso_code
                    // })
                    const currentCountry = this.listCountryState?.find((item) => {
                        return this.current_iso_code === item.code
                    })
                    const code = this.EU_code[currentCountry.id]
                    this.form.entryTaxNumber = code === "AT" ? "ATU" : code ? code : ""

                    this.form.entryCountryId = currentCountry.id
                    this.form.telPrefix = this.selectCountry?.current_tel_prefix
                    console.log("this.selectCountry", this.selectCountry)
                    this.stateOptions =
                        currentCountry?.stateList?.length &&
                        currentCountry.stateList.map((item) => {
                            return {
                                name: item.label,
                                value: item.code,
                            }
                        })
                    this.jpProviceList =
                        currentCountry?.stateList?.length &&
                        currentCountry.stateList.map((item) => {
                            return {
                                name: item.label,
                                value: item.code,
                            }
                        })
                    this.citysOPtions =
                        currentCountry?.cityList?.length &&
                        currentCountry.cityList.map((item) => {
                            return {
                                name: item.label,
                                value: item.code,
                            }
                        })
                    this.country = { name: currentCountry.name, code: currentCountry.code }
                }
                this.checkAll(this.form.entryCountryId)
            }
            if (this.isCurrentShipping && this.form.entryCountryId === 188) {
                this.showSGTip = true
            }
        },
        changeAd() {
            //  entryState 省 ，entryCity市，entrySuburb县/区
            console.log(this.address)
            this.form.entrySuburb = this.address[2]
            this.form.entryCity = this.address[1]
            this.form.entryState = this.address[0]
            this.checkValue("entryState")
            this.checkValue("entryCity")
            this.checkValue("entrySuburb")
        },
        countryChange(item) {
            // console.log(this.Reunion, item, this.shippingAddress)
            this.errors.entryTaxNumber = ""
            // this.form.entryTaxNumber = ''
            const currentCountry = this.listCountryState?.find((s) => {
                return s.id === item.countries_id
            })

            const code = this.EU_code[currentCountry.id]
            this.form.entryTaxNumber = code === "AT" ? "ATU" : code ? code : ""
            if (this.Reunion.includes(currentCountry.id) && this.isCurrentBilling && this.shippingAddress.entryCountryId == 73) {
                this.form.entryTaxNumber = "FR"
            }

            // console.log(item)
            this.country = { name: currentCountry.name, code: currentCountry.code }
            this.checkAll(currentCountry.id)
            this.form.telPrefix = currentCountry.telPrefix
            this.form.entryCountryId = currentCountry.id
            this.form.entryState = ""
            this.stateOptions =
                currentCountry?.stateList?.length &&
                currentCountry.stateList.map((item) => {
                    return {
                        name: item.label,
                        value: item.code,
                    }
                })
            this.jpProviceList =
                currentCountry?.stateList?.length &&
                currentCountry.stateList.map((item) => {
                    return {
                        name: item.label,
                        value: item.code,
                    }
                })
            this.citysOPtions =
                currentCountry?.cityList?.length &&
                currentCountry.cityList.map((item) => {
                    return {
                        name: item.label,
                        value: item.code,
                    }
                })
            if (this.website != "cn" && this.form.entryCountryId == 44) {
                this.errors.entryCountryId = this.$c("form.form.CNlimitTip")
            } else {
                this.errors.entryCountryId = ""
            }
            if (this.isCurrentShipping && this.form.entryCountryId === 188) {
                this.showSGTip = true
            } else {
                this.showSGTip = false
            }
            if (item.countries_id === 188) {
                this.errors.entryCity = ""
            }
        },
        // 全角转半角
        checkIssbccase(str) {
            var result = ""
            for (var i = 0; i < str.length; i++) {
                var code = str.charCodeAt(i)
                if (code >= 65281 && code <= 65374) {
                    result += String.fromCharCode(str.charCodeAt(i) - 65248)
                } else if (code == 12288) {
                    result += String.fromCharCode(str.charCodeAt(i) - 12288 + 32)
                } else {
                    result += str.charAt(i)
                }
            }
            return result
        },
        checkValue(v) {
            if (v === "entryTaxNumber") {
                this.form.entryTaxNumber = this.form.entryTaxNumber.replace(/\s+/g, "")
            }
            // 有人反馈说客户公司名称是小写，默认转换成大写税号不通过
            // if (v == "entryCompany") {
            //     this.form.entryCompany = this.form.entryCompany.replace(this.form.entryCompany[0], this.form.entryCompany[0]?.toUpperCase() || "")
            // }

            if (["entryTelephone", "entryPostcode"].includes(v) && this.isCurrentShipping && this.form.entryCountryId == 107) {
                this.form[v] = this.checkIssbccase(this.form[v])
            }
            if (v === "entryPostcode" && [188, 81].includes(this.form.entryCountryId)) {
                const reg = /[^\d]/g
                this.form[v] = this.form[v].replace(reg, "") || ""
            }
            const jpAddressReg = /^[0-9a-zA-Z\u3000\u3040-\u309F\u30A0-\u30FF\u4E00-\u9FAF\s-ー,、]+$/g
            const val = this.form[v]
            const length = val?.length || 0
            if (this.website !== "cn") {
                switch (v) {
                    case "companyType":
                        if (val === "xx") {
                            this.errors[v] = this.$c("pages.confirmOrder.form.address_type_required")
                        } else {
                            this.errors[v] = ""
                        }
                        break
                    case "entryFirstname":
                    case "entryLastname":
                        if (this.isCnTr) {
                            if (!val) {
                                this.errors[v] = this.$c(v === "entryFirstname" ? "pages.confirmOrder.form.first_name_required2" : "pages.confirmOrder.form.last_name_required2")
                            } else if (this.cnTrReg.test(val)) {
                                this.errors[v] = this.$c(v === "entryFirstname" ? "pages.confirmOrder.form.only_en_firstname" : "pages.confirmOrder.form.only_en_lastname")
                            } else if (length < 1) {
                                this.errors[v] = this.$c(v === "entryFirstname" ? "pages.confirmOrder.form.first_name_min" : "pages.confirmOrder.form.last_name_min")
                            } else if (length > 20) {
                                this.errors[v] = this.$c(v === "entryFirstname" ? "pages.confirmOrder.form.first_name_max" : "pages.confirmOrder.form.last_name_max")
                            } else {
                                this.errors[v] = ""
                            }
                        } else {
                            if (!val) {
                                this.errors[v] = this.$c(v === "entryFirstname" ? "pages.confirmOrder.form.first_name_required2" : "pages.confirmOrder.form.last_name_required2")
                            } else if (length < 2) {
                                this.errors[v] = this.$c(v === "entryFirstname" ? "pages.confirmOrder.form.first_name_min" : "pages.confirmOrder.form.last_name_min")
                            } else if (length > 35) {
                                this.errors[v] = this.$c(v === "entryFirstname" ? "pages.confirmOrder.form.first_entry_name_max" : "pages.confirmOrder.form.last_entry_name_max")
                            } else {
                                this.errors[v] = ""
                            }
                            if (~location.pathname.indexOf("/jp/") && val) {
                                this.errors[v] = ""
                            }
                        }
                        break
                    case "entryCountryId":
                        if (this.website != "cn" && this.form.entryCountryId == 44) {
                            this.errors.entryCountryId = this.$c("form.form.CNlimitTip")
                        } else {
                            this.errors.entryCountryId = ""
                        }
                        break

                    case "entryStreetAddress":
                        if (this.isCnTr) {
                            if (!val) {
                                this.errors[v] = this.$c("pages.confirmOrder.form.address_required")
                            } else if (length < 4 || length > 35) {
                                this.errors[v] = this.$c("pages.confirmOrder.form.address_validate")
                            } else if (this.isCnTr && this.cnTrReg.test(val)) {
                                this.errors[v] = this.$c("pages.confirmOrder.form.only_en_street")
                            } else {
                                this.errors[v] = ""
                            }
                        } else {
                            if (!val) {
                                this.errors[v] = this.isJp ? this.$c("form.form.placeHolder.addressNone") : this.$c("pages.confirmOrder.form.address_required")
                            } else if (this.isJp && !jpAddressReg.test(val)) {
                                this.errors[v] = this.$c("pages.confirmOrder.form.jpAddressLimit")
                            } else if (length < 4 || length > (this.form.entryCountryId == 188 ? 300 : 35)) {
                                this.errors[v] = this.isJp ? this.$c("form.form.placeHolder.addressLength") : this.$c("pages.confirmOrder.form.address_validate")
                            } else {
                                this.errors[v] = ""
                            }
                        }

                        this.sensitiveCheck(v, val)
                        break

                    case "entrySuburb":
                        if (this.isCnTr) {
                            // if ((this.type == "Shipping" || this.isCurrentShipping) && [81, 73, 188].includes(this.form.entryCountryId)) {
                            if ((this.type == "Shipping" || this.isCurrentShipping) && [188].includes(this.form.entryCountryId)) {
                                if (!val) {
                                    this.errors[v] = this.$c("pages.confirmOrder.form.address2_required")
                                } else if (length < 1 || length > 40) {
                                    this.errors[v] = this.$c("form.form.addressLimit")
                                } else {
                                    this.errors[v] = ""
                                }
                            } else if (this.isCnTr && this.cnTrReg.test(val)) {
                                this.errors[v] = this.$c("pages.confirmOrder.form.only_en_street")
                            } else {
                                this.errors[v] = ""
                            }
                        } else if (this.isCnTr && this.cnTrReg.test(val)) {
                            this.errors[v] = this.$c("pages.confirmOrder.form.only_en_street")
                        } else {
                            // if ((this.type == "Shipping" || this.isCurrentShipping) && [81, 73, 188].includes(this.form.entryCountryId)) {
                            if ((this.type == "Shipping" || this.isCurrentShipping) && [188].includes(this.form.entryCountryId)) {
                                if (!val) {
                                    this.errors[v] = this.$c("pages.confirmOrder.form.address2_required")
                                } else if (length < 1 || length > 40) {
                                    this.errors[v] = this.$c("form.form.addressLimit")
                                } else {
                                    this.errors[v] = ""
                                }
                            } else {
                                if (length > 40) {
                                    this.errors[v] = this.$c("form.form.addressLimit")
                                } else if (length > 0 && this.isJp && !jpAddressReg.test(val)) {
                                    this.errors[v] = this.$c("pages.confirmOrder.form.jpAddressLimit")
                                } else {
                                    this.errors[v] = ""
                                }
                            }
                        }

                        this.sensitiveCheck(v, val)
                        break
                    case "entryCity":
                        // console.log(this.form.entryCountryId)
                        if (!val) {
                            this.errors[v] = this.isJp ? this.$c("form.form.placeHolder.cityNone") : this.$c("pages.confirmOrder.form.city_required")
                        } else if (this.isJp && !jpAddressReg.test(val)) {
                            this.errors[v] = this.$c("pages.confirmOrder.form.jpAddressLimit")
                        } else if (length > 40) {
                            this.errors[v] = this.$c("pages.confirmOrder.form.cityMaxLength")
                        } else {
                            this.errors[v] = ""
                        }
                        break
                    case "entryTelephone":
                        // console.log("this.$refs.telCode", this.$refs.telCode)
                        this.errors[v] = this.$refs.telCode ? this.$refs.telCode.validFn() : ""

                        break
                    case "entryCompany":
                        // if (this.form.companyType === "BusinessType" || [81, 73].includes(this.form.entryCountryId)) {
                        if (this.form.companyType === "BusinessType") {
                            if (!val) {
                                this.errors[v] = this.$c("pages.confirmOrder.form.company_name_required")
                            } else if (length < 3 || length > 120) {
                                this.errors[v] = this.$c("pages.confirmOrder.form.company_name_validate")
                            } else {
                                this.errors[v] = ""
                            }
                        } else {
                            if (!val) {
                                this.errors[v] = ""
                            } else {
                                if (length < 3 || length > 120) {
                                    this.errors[v] = this.$c("pages.confirmOrder.form.company_name_validate")
                                } else {
                                    this.errors[v] = ""
                                }
                            }
                        }
                        break
                    case "entryPostcode":
                        if (!val) {
                            this.errors[v] = this.$c("pages.confirmOrder.form.zip_code_required")
                            if (this.isJp) {
                                this.errors[v] = this.$c("form.form.placeHolder.nodata")
                            }
                        } else if (length < 3) {
                            this.errors[v] = this.$c("pages.confirmOrder.form.zip_code_validate")
                        } else if (length > 10) {
                            this.errors[v] = this.$c("form.form.zipCodeMax")
                        } else {
                            this.errors[v] = ""
                        }
                        if (!this.codeVisible) {
                            this.errors[v] = ""
                        }
                        if (this.form.entryCountryId === 188) {
                            if (length != 6) {
                                this.errors[v] = this.$c("form.validate.valid_post_number")
                            } else {
                                this.errors[v] = ""
                            }
                        }
                        if (this.form.entryCountryId === 81) {
                            this.errors[v] = length !== 5 ? this.$c("form.validate.valid_post_number") : ""
                        }
                        if (this.isJp) {
                            if (length < 7) {
                                this.errors[v] = this.$c("form.form.placeHolder.short")
                            } else {
                                this.errors[v] = ""
                            }
                        }
                        if (this.isEffectiveness) {
                            this.errors[v] = ""
                        }

                        // this.timer && clearTimeout(this.timer)
                        // if ((this.form.entryCountryId === 223 && val.length === 5) || this.form.entryCountryId === 13) {
                        //     this.timer = setTimeout(() => {
                        //         this.codeToState(val, this.form.entryCountryId)
                        //     }, 300)
                        // }
                        break

                    case "entryTaxNumber":
                        if (this.taxVisible && this.isRequired) {
                            if (!val) {
                                this.errors[v] = this.$c("pages.confirmOrder.form.tax_required")
                            } else {
                                const id = this.form.entryCountryId
                                this.errors[v] = ""

                                if (id === 30) {
                                    if (this.form.companyType === "IndividualType") {
                                        this.errors[v] = !/^\d{3}\.\d{3}\.\d{3}\/\d{2}$/.test(val) ? this.$c("pages.confirmOrder.form.tax_validate") + " 000.000.000/00." : ""
                                    } else {
                                        this.errors[v] = !/^\d{2}\.\d{3}\.\d{3}\/\d{4}-\d{2}$/.test(val) ? this.$c("pages.confirmOrder.form.tax_validate") + " 00.000.000/0000-00." : ""
                                    }
                                }
                                if (id === 138) {
                                    if (this.form.companyType === "IndividualType") {
                                        this.errors[v] = !/^[a-zA-Z0-9]{13}$/.test(val) ? this.$c("pages.confirmOrder.form.tax_validate") + " 0000000000000." : ""
                                    } else {
                                        this.errors[v] = !/^[a-zA-Z0-9]{12}$/.test(val) ? this.$c("pages.confirmOrder.form.tax_validate") + " 000000000000." : ""
                                    }
                                }
                                //印度尼西亚
                                if (id === 100) {
                                    if (this.form.companyType === "BusinessType") {
                                        this.errors[v] = !/^\d{15}$/.test(val) ? this.$c("pages.confirmOrder.form.tax_validate") + " 000000000000000." : ""
                                    } else {
                                        this.errors[v] = !/^[0-9a-zA-Z]*$/g.test(val) ? this.$c("pages.confirmOrder.form.tax_validate") + " 000000000000000." : ""
                                    }
                                }
                                if (id === 10 && !/^\d{2}-\d{8}-\d$/.test(val)) {
                                    this.errors[v] = this.$c("pages.confirmOrder.form.tax_validate") + " 00-00000000-0."
                                }
                                if (id === 43 && !/^\d{2}.\d{3}.\d{3}-[A-Za-z0-9]{1}$/.test(val)) {
                                    this.errors[v] = this.$c("pages.confirmOrder.form.tax_validate") + " 00.000.000-0."
                                }
                            }
                        } else if (this.taxVisible && !this.isRequired) {
                            if (length > 40) {
                                this.errors[v] = this.$c("form.form.TaxMaxAdress")
                            } else {
                                this.errors[v] = ""
                            }
                        } else {
                            this.errors[v] = ""
                        }
                        break
                    case "entryState":
                        if (this.stateOptions?.length && !val) {
                            this.errors[v] = this.$c("pages.confirmOrder.form.state_required")
                        } else if (this.isJp && !val) {
                            this.errors[v] = this.$c("form.form.placeHolder.provinceNone")
                        } else {
                            this.errors[v] = ""
                        }
                        break
                    case "entryEori":
                        if (!/^[0-9a-zA-Z]*$/g.test(val)) {
                            this.errors[v] = this.$c("common.basic.addressHandle.EORIErrors.format")
                        } else if (length < 10) {
                            this.errors[v] = this.$c("common.basic.addressHandle.EORIErrors.minLen")
                        } else if (length > 17) {
                            this.errors[v] = this.$c("common.basic.addressHandle.EORIErrors.maxLen")
                        } else {
                            this.errors[v] = ""
                        }
                        if (!this.isEORI || !val) {
                            this.errors[v] = ""
                            this.form[v] = ""
                        }
                        break
                    case "identityValue":
                        if (!val) {
                            this.errors[v] = this.INNumErrorMsg
                        } else {
                            this.errors[v] = ""
                        }
                        if (!this.isIN) {
                            this.errors[v] = ""
                        }
                        break
                    default:
                        break
                }
            } else {
                switch (v) {
                    case "entryFirstname":
                        if (!val) {
                            this.errors[v] = "请填入您的姓名"
                        } else if (length > 1 && length < 21) {
                            this.errors[v] = ""
                        } else {
                            this.errors[v] = "姓名长度最小为2个字，最长为20个字"
                        }
                        break
                    case "entryTelephone":
                        if (!val) {
                            this.errors[v] = "请输入您的手机号"
                        } else if (!cn_new_tel.test(val)) {
                            this.errors[v] = "请填入正确格式的手机号"
                        } else {
                            this.errors[v] = ""
                        }
                        break
                    case "entryState":
                    case "entryCity":
                    case "entrySuburb":
                        if (!val) {
                            this.errors[v] = "请选择您的收货地址"
                        } else {
                            this.errors[v] = ""
                        }
                        break
                    case "entryStreetAddress":
                        if (!val) {
                            this.errors[v] = "请输入详细地址"
                        } else if (length < 5 || length > 32) {
                            this.errors[v] = "详细地址长度不对，最小为5个字，最长为32个字"
                        } else {
                            this.errors[v] = ""
                        }
                        break
                    case "entryPostcode":
                        if (length && length != 6) {
                            // this.errors[v] = "请输入正确的邮政编码"
                            this.errors[v] = ""
                        } else {
                            this.errors[v] = ""
                        }
                        break
                    case "entryEmail":
                        if (!val) {
                            this.errors[v] = ""
                        } else {
                            if (length && !email_valdate.test(val)) {
                                this.errors[v] = this.$c("form.validate.email.email_valid")
                            } else {
                                this.errors[v] = ""
                            }
                        }
                        break
                    default:
                        break
                }
            }

            this.heightChange && this.heightChange()
        },

        jpChangeState() {
            this.checkValue("entryState")
            this.selectClick("State/Province/Region")
        },
        stateChange(v) {
            this.checkValue("entryState")
        },
        typeChange() {
            if (this.form.companyType !== "xx") {
                this.errors.companyType = ""
            }
            this.form.entryTaxNumber = ""
            this.errors.entryTaxNumber = ""
            this.checkAll(this.form.entryCountryId)
            if (this.form.companyType === "IndividualType") {
                this.errors.entryCompany = ""
            } else {
                const code = this.EU_code[this.form.entryCountryId]
                this.form.entryTaxNumber = code === "AT" ? "ATU" : code ? code : ""
            }
        },
        cityChange() {
            this.checkValue("entryCity")
        },
        async sensitiveCheck(v, val) {
            var err = ""
            //台湾敏感词检测
            const res = await this.$axios.post("/api/sensitive_monitor", { content: val, country_id: this.form.entryCountryId })
            // console.log(res.data.message)
            err = res.data.message ? res.data.message : ""
            v == "entrySuburb" ? (this.sensitiveEntrySuburb = err) : (this.sensitiveEntryStreetAddress = err)
        },
        checkAll(id) {
            if (this.Reunion.includes(id)) {
                this.isReunion = true
            } else {
                this.isReunion = false
            }
            const type = this.form.companyType
            this.trans = {
                tax: this.$c("pages.confirmOrder.address.taxNumber"),
                ad: this.$c("pages.confirmOrder.address.address"),
                ad2: this.$c("pages.confirmOrder.address.address2"),
                city: this.$c("pages.confirmOrder.address.city"),
                state: this.$c("pages.confirmOrder.address.stateProvinceRegion"),
                code: this.$c("pages.confirmOrder.address.zipCode"),
            }
            this.isRequired = false
            this.taxVisible = false
            this.taxHolder = ""
            this.taxTip = this.$c("pages.confirmOrder.address.taxTip")
            this.codeVisible = true
            this.addressTypeOptions = [
                { name: this.$c("pages.confirmOrder.address.businessType"), value: "BusinessType" },
                { name: this.$c("pages.confirmOrder.address.individualType"), value: "IndividualType" },
            ]
            this.companyKey = Math.random()
            switch (id) {
                case 13: //澳大利亚
                    this.taxVisible = type === "BusinessType"
                    this.trans.tax = "ABN"
                    this.trans.city = this.$c("pages.confirmOrder.address.suburb")
                    this.trans.state = this.$c("pages.confirmOrder.address.state")
                    this.trans.code = this.$c("pages.confirmOrder.address.postcode")
                    this.taxTip = ""
                    break
                case 138: //墨西哥
                    // this.taxVisible = type === "BusinessType"
                    if (this.website === "mx") {
                        this.trans.tax = "RFC"
                    }
                    this.taxVisible = true
                    this.isRequired = true
                    break
                case 188: //新加坡
                    this.taxVisible = false
                    // this.trans.tax = this.$c("pages.confirmOrder.address.GST")
                    // this.taxTip = ""
                    break
                case 44: //中国
                case 125: //澳门
                case 206: //台湾
                    this.taxVisible = true
                    break
                case 96: //香港
                    this.taxVisible = true
                    this.codeVisible = false
                    break
                case 30: //巴西
                case 10: //阿根廷
                case 100: //印度尼西亚
                    this.taxVisible = true
                    this.isRequired = true
                    this.trans.tax = id === 30 ? "CNPJ" : "CUIT"
                    this.trans.tax = id === 100 ? "NPWP" : this.trans.tax
                    this.taxHolder = id === 100 ? "eg:000000000000000" : ""
                    break
                case 43: //智利
                    this.taxVisible = type === "BusinessType"
                    this.isRequired = true
                    this.trans.tax = "RUT"
                    break
                case 99: //印度
                    this.taxVisible = true
                    this.trans.tax = "GSTIN"
                    break
                case 62: //厄瓜多尔
                    this.taxVisible = type === "BusinessType"
                    this.trans.tax = "RUC"
                    break

                case 223: //美国 加拿大 新西兰 俄罗斯
                case 38:
                case 153:

                case 176:
                    this.taxVisible = false
                    break
                default:
                    this.taxVisible = true
                    break
            }
            if (this.isEu.includes(id)) {
                this.taxVisible = type === "BusinessType"
                this.trans.tax = this.$c("pages.confirmOrder.address.VATTAXNumber")
                this.trans.ad = this.$c("pages.confirmOrder.address.streetandHouse")
                this.trans.ad2 = this.$c("pages.confirmOrder.address.additionalAddress")
                this.trans.code = this.$c("pages.confirmOrder.address.postcode")
                this.isRequired = true
                if (id === 222) {
                    this.isRequired = false
                    this.trans.ad = this.$c("pages.confirmOrder.address.streetandHouseUk")
                    if (type === "BusinessType") {
                        this.trans.tax = this.$c("pages.confirmOrder.address.ukVATTAXNumber")
                    }
                }
            }
            if (this.notEu.includes(id)) {
                this.taxVisible = type === "BusinessType"
                this.trans.ad = this.$c("pages.confirmOrder.address.streetandHouse")
                this.trans.ad2 = this.$c("pages.confirmOrder.address.additionalAddress")
                this.trans.code = this.$c("pages.confirmOrder.address.postcode")
            }
            // if (id === 222) {
            //     this.isRequired = false
            // }
            if (this.US.includes(id) || this.SG.includes(id) || id === 44) {
                this.addressTypeOptions = [
                    { name: this.$c("pages.confirmOrder.address.business"), value: "BusinessType" },
                    { name: this.$c("pages.confirmOrder.address.residential"), value: "IndividualType" },
                ]
                this.companyKey = Math.random()
            }
            if (this.AU.includes(id)) {
                this.addressTypeOptions = [
                    { name: this.$c("pages.confirmOrder.address.business"), value: "BusinessType" },
                    { name: this.$c("pages.confirmOrder.address.private"), value: "IndividualType" },
                ]
                this.companyKey = Math.random()
            }
            if (this.RU.includes(id)) {
                this.addressTypeOptions = [
                    { name: this.$c("pages.confirmOrder.address.legalPerson"), value: "BusinessType" },
                    { name: this.$c("pages.confirmOrder.address.naturalPerson"), value: "IndividualType" },
                ]
                this.companyKey = Math.random()
            }
            if (this.isReunion) {
                this.isRequired = true
            }
            if (this.isRequired) {
                this.taxTip = this.$c("pages.confirmOrder.address.taxTip2")
                if (id === 222 && type === "BusinessType") {
                    this.taxTip = this.$c("pages.confirmOrder.address.taxTip")
                }
                if (id === 138) {
                    this.taxTip = this.$c("pages.confirmOrder.address.taxTip3")
                }
            }
            if (id === 174 && type != "BusinessType") {
                //留尼汪 税号选填
                this.isRequired = false
            }
            // 德国
            if (id === 81) {
                // this.taxTip = ""
                this.taxTip = this.$c("pages.confirmOrder.address.taxTip")
            }
            // 印度尼西亚
            if (id === 100) {
                this.taxTip = this.$c("pages.confirmOrder.tax.npwpTip")
            }
            if (this.form.companyType === "xx") {
                this.addressTypeOptions.unshift({ name: this.$c("form.form.please_select"), value: "xx" })
            }
        },
        async codeToState(v, id) {
            // console.log(v, id)
            const res = await this.$axios("/api/code_match_state", { params: { entry_country_id: id, entry_postcode: v } })
            if (res.data.city) {
                this.form.entryCity = res.data.city
                this.form.entryState = res.data.state_code
            } else {
                this.form.entryState = res.data.state_code
            }
        },
        // 编辑地址初步验证
        initCheck() {
            if (this.form.entryCountryId === 188) {
                this.form.entryCity = this.country.name
            }
            for (const k in this.errors) {
                this.checkValue(k)
            }
            this.timer && clearTimeout(this.timer)
            for (const k in this.errors) {
                // console.log(k, this.errors[k])
                if (this.errors[k]) return
            }
            if (this.isCurrentBilling) {
                this.$emit("update:billingSync", false)
            }
            let data = {}
            let form = { ...this.form }
            // console.log("form1", this.form)
            // console.log("form2", form, this.taxVisible)
            if (!this.taxVisible) {
                form.entryTaxNumber = ""
            }

            if (!this.isIN) {
                delete form.identityType
                delete form.identityValue
            }
            // console.log("form3", form)
            //address改成addressOperate
            data = { addressOperate: { ...form, addressType: this.addressType, applyDefault: this.applyDefault }, ...this.params }
            if (this.isCurrentBilling) {
                data.addressOperate.billingSync = this.isCurrentBilling ? 0 : this.billingSync
            }
            // console.log("form4", form)
            this.$axios
                .post(`/order-api/init/address/${this.isCurrentShipping ? "shipping" : "billing"}`, data)
                .then((res) => {
                    if (res.code != 200) {
                        this.errorManage(res)
                        return
                    }
                })
                .catch((err) => {
                    this.errorManage(err)
                    return
                })
        },

        // 新增/编辑地址
        async save() {
            this.btnClick(1)
            if (this.form.entryCountryId === 188) {
                this.form.entryCity = this.country.name
            }
            for (const k in this.errors) {
                this.checkValue(k)
            }

            this.timer && clearTimeout(this.timer)
            for (const k in this.errors) {
                // console.log(this.errors[k], k)
                if (this.errors[k]) {
                    if (Array.isArray(this.$refs[k])) {
                        this.$refs[k][0]?.scrollIntoView()
                    } else {
                        this.$refs[k]?.scrollIntoView()
                    }
                    return false
                }
            }
            if (this.isCurrentBilling) {
                this.$emit("update:billingSync", false)
            }

            let data = {}
            let form = { ...this.form }

            if (!this.isIN) {
                delete form.identityType
                delete form.identityValue
            }
            if (!this.taxVisible) {
                form.entryTaxNumber = ""
            }

            //address改成addressOperate
            data = { addressOperate: { applyDefault: this.applyDefault, ...form, addressType: this.addressType }, ...this.params }
            // data = { ...form }
            this.loading = true
            const res = await this.$axios.post(`/order-api/v1/address/save`, data.addressOperate)
            // const res = await this.$axios.post(`/order-api/init/address/${this.isCurrentShipping ? "shipping" : "billing"}`, data)
            this.loading = false
            if (res.code == 200) {
                if (!this.isEdit && window.dataLayer) {
                    if (this.isQuote) {
                        // this.quoteSaveGio(res.code)
                    } else {
                        // this.confirmADSaveSuccessGio(res)
                    }
                }
                this.$emit("closeError")
                if (this.form.addressBookId) {
                    this.$emit("editSuccess", { ...res.data })
                } else {
                    this.$emit("addSuccess", { ...res.data, addressType: this.addressType, applyDefault: this.applyDefault })
                }
            } else if (res.code == 42400) {
                // 客户在网站下单填写收货地址的公司名需要与税号对应公司名完全一致才能够成功保存地址下单，大多法国客户的税号对应的公司名里都包含了公司类型的信息比如SAS SARL，
                if (data.addressOperate.entryCountryId == 73 && data.addressOperate.companyType == "BusinessType" && this.errors["entryTaxNumber"]) {
                    this.errors["entryCompany"] = this.$c("form.form.frCompanyTip")
                }
                this.errorManage(res)
                return
            } else {
                this.errorManage(res)
                if (!this.isQuote) {
                    this.confirmADSaveFailGio()
                }
                return
            }
        },

        errorManage(err) {
            switch (err.code) {
                case 42500:
                // 路由跳转：首页，购物车
                // this.$router.replace(this.localePath("/shopping_cart.html"))
                // break
                case 42200:
                case 500:
                case 42300:
                    // 弹窗错误提示
                    this.showErrorTip = true
                    if (err?.extend?.message) {
                        this.errorMsg = err.extend.message
                        if (err?.extend?.redirect) {
                            const redirect = err.extend.redirect
                            this.redirectInfo = {
                                iso_code: redirect.isoCode,
                                currency: redirect.currency,
                                language: redirect.language,
                            }
                        }
                    } else {
                        this.errorMsg = err.message
                    }
                    break
                case 42400:
                    // 表单错误提示
                    for (const k in err.extend) {
                        this.errors[k] = err.extend[k][0]
                        this.heightChange && this.heightChange()
                    }
                    break

                default:
                    this.showErrorTip = true
                    this.errorMsg = err?.extend?.message ? err.extend.message : err.message
                    break
            }
        },
        defaultHandle() {
            this.applyDefault = !this.applyDefault
            console.log(this.applyDefault)
            if (this.isConfirmOrderMixin) {
                this.buriedPointMixin({
                    eventAction: `${this.type} Address_operate`,
                    eventLabel: `Save As Default`,
                })
            }
        },

        confirmADSaveSuccessGio(res) {
            let id = [],
                products = []
            for (const k in res.data.orderStandardList) {
                res.data.orderStandardList[k].products.forEach((y, i) => {
                    const attr = []
                    if (y.productsAttributes) {
                        for (const k in y.productsAttributes) {
                            const v = y.productsAttributes[k]
                            attr.push(`${v.optionName}: ${v.optionsValuesName}`)
                        }
                    }
                    if (y.productsLength?.productLengthName) {
                        attr.push(`${this.$c("pages.SavedCarts.Length")}:${y.productsLength.productLengthName}`)
                    }
                    id.push(y.productsId)
                    products.push({
                        name: y.productsInfo.productName.slice(0, 100),
                        id: y.productsId,
                        price: +y.productsInfo.finalProductsPrice.price, //应当改为当前货币价格，不带货币符号
                        variant: attr.join("|"),
                        category: y.productsInfo.productsCategoriesList.map((item) => item.name).join("_"),
                        quantity: y.qty,
                        position: i + 1,
                    })
                })
            }
            window.dataLayer.push({
                event: "eeEvent",
                eventCategory: "Confirm Order Page",
                eventAction: `${this.isCurrentShipping ? "Shipping Address" : "Billing Address"}_success`,
                eventLabel: "Confirm Success",
                eventValue: +res.data.total.priceUs,
                nonInteraction: false,
                ecommerce: {
                    checkout: {
                        actionField: {
                            step: this.currentStep,
                            // "option": 'paypal'
                        },
                        products: products,
                    },
                },
            })
        },
        confirmADSaveFailGio() {
            window.dataLayer &&
                window.dataLayer.push({
                    event: "uaEvent",
                    eventCategory: "Confirm Order Page",
                    eventAction: `${this.isCurrentShipping ? "Shipping Address" : "Billing Address"}_operate`,
                    eventLabel: "add_fail",
                    nonInteraction: false,
                })
        },
        quoteSaveGio(res) {
            window.dataLayer &&
                window.dataLayer.push({
                    event: "uaEvent",
                    eventCategory: "Quote Page",
                    eventAction: `${this.isCurrentShipping ? "Shipping Address" : "Billing Address"}_operate`,
                    eventLabel: res === 200 ? "Confirm Success" : "Confirm Fail",
                    nonInteraction: false,
                })
        },
        cancel() {
            document.body.click()
            window.dataLayer &&
                window.dataLayer.push({
                    event: "uaEvent",
                    eventCategory: this.isQuote ? "Quote Page" : "Confirm Order Page",
                    eventAction: `${this.isCurrentShipping ? "Shipping Address" : "Billing Address"}_operate`,
                    eventLabel: "Cancel_Delete",
                    nonInteraction: false,
                })
        },
        changeSite(v) {
            this.updateSiteInfo({
                ...v,
                callback: () => {},
            })
        },
        stop() {},
        toCart() {
            this.$router.replace(this.localePath("/shopping_cart.html"))
        },
        inputClick(v) {
            window.dataLayer &&
                window.dataLayer.push({
                    event: "uaEvent",
                    eventCategory: this.isQuote ? "Quote Page" : "Confirm Order Page",
                    eventAction: `${this.form.addressBookId ? "edit" : "new"}_${this.isCurrentShipping ? "Shipping Address" : "Billing Address"}`,
                    eventLabel: `${v} Input`,
                    nonInteraction: false,
                })
        },
        selectClick(v) {
            window.dataLayer &&
                window.dataLayer.push({
                    event: "uaEvent",
                    eventCategory: this.isQuote ? "Quote Page" : "Confirm Order Page",
                    eventAction: `${this.form.addressBookId ? "edit" : "new"}_${this.isCurrentShipping ? "Shipping Address" : "Billing Address"}`,
                    eventLabel: `${v} Drop-Down`,
                    nonInteraction: false,
                })
        },
        btnClick(v) {
            if (!window.dataLayer) return
            if (this.isQuote) {
                // 213,214
                window.dataLayer.push({
                    event: "uaEvent",
                    eventCategory: "Quote Page",
                    eventAction: `${this.isEdit ? "edit" : "new"}_${this.isCurrentShipping ? "Shipping Address" : "Billing Address"}`,
                    eventLabel: v ? "Save" : "Cancel",
                    nonInteraction: false,
                })
                // 220
                !this.isEdit &&
                    !v &&
                    window.dataLayer.push({
                        event: "uaEvent",
                        eventCategory: "Quote Page",
                        eventAction: `${this.isCurrentShipping ? "Shipping Address" : "Billing Address"}_operate`,
                        eventLabel: "Cancel",
                        nonInteraction: false,
                    })
            } else {
                window.dataLayer.push({
                    event: "uaEvent",
                    eventCategory: "Confirm Order Page",
                    eventAction: `${this.isEdit ? "edit" : "new"}_${this.isCurrentShipping ? "Shipping Address" : "Billing Address"}`,
                    eventLabel: v ? "Save" : "Back",
                    nonInteraction: false,
                })
                !this.isEdit &&
                    !v &&
                    window.dataLayer.push({
                        event: "uaEvent",
                        eventCategory: "Confirm Order Page",
                        eventAction: `${this.isCurrentShipping ? "Shipping Address" : "Billing Address"}_operate`,
                        eventLabel: "Cancel_add",
                        nonInteraction: false,
                    })
            }
        },
        editDelClick(v) {
            window.dataLayer &&
                window.dataLayer.push({
                    event: "uaEvent",
                    eventCategory: this.isQuote ? "Quote Page" : "Confirm Order Page",
                    eventAction: `${this.isCurrentShipping ? "Shipping Address" : "Billing Address"}_operate`,
                    eventLabel: v ? "Edit" : "Remove",
                    nonInteraction: false,
                })
        },
        changeCode(code) {
            this.form.telPrefix = code
            if (!this.form.entryTelephone) {
                this.errors.entryTelephone = ""
            } else {
                this.checkValue("entryTelephone")
            }
        },
        phoneError(errorMsg) {
            this.errors.entryTelephone = errorMsg
        },

        burryPoint(el_sta, el_rea = "") {
            if (el_rea) {
                window.dataLayer &&
                    window.dataLayer.push({
                        event: "uaEvent",
                        nonInteraction: false,
                        eventCategory: this.isQuote ? "Quote Page" : "Confirm Order Page",
                        eventAction: `${this.isEdit ? "edit" : "new"}_${this.isCurrentShipping ? "Shipping Address" : "Billing Address"}`,
                        eventLabel: `Zip Code Search_${el_sta}_${el_rea}`,
                    })
            } else {
                window.dataLayer &&
                    window.dataLayer.push({
                        event: "uaEvent",
                        nonInteraction: false,
                        eventCategory: this.isQuote ? "Quote Page" : "Confirm Order Page",
                        eventAction: `${this.isEdit ? "edit" : "new"}_${this.isCurrentShipping ? "Shipping Address" : "Billing Address"}`,
                        eventLabel: `Zip Code Search_${el_sta}`,
                    })
            }
        },
        postCodeChange: debounce(async function () {
            // 新加坡站点，选中的国家为新加坡，输入邮编带出地址，防抖
            try {
                if (this.isJp || (this.website === "sg" && this.form.entryCountryId === 188)) {
                    let post_code = this.form.entryPostcode
                    if (this.errors.entryPostcode) {
                        return
                    }
                    if (this.isJp) {
                        if (this.form.entryPostcode.length < 7) {
                            if (!this.form.entryPostcode) {
                                this.burryPoint("Fail", 1)
                            } else {
                                this.burryPoint("Fail", 2)
                            }
                            this.errors.entryPostcode = this.$c("form.form.placeHolder.short")
                            return
                        }
                        post_code = post_code.replace(/-/g, "")
                    }
                    let body = {
                        isoCode: this.country.code,
                        postCode: post_code,
                    }
                    this.jpSearchLoading = true

                    const res = await this.$axios.get("/order-api/v1/countryInfo/getAddressByPostCode", { params: body })
                    if (this.website === "sg" && this.country.code === "SG") {
                        // let { data } = res
                        let data = res.data
                        let msg = data?.message ? data.message : ""
                        this.$nextTick(() => {
                            this.form.entryStreetAddress = msg
                        })
                    }
                    if (this.isJp) {
                        if (res.code === 200 && res.data?.address) {
                            this.burryPoint("Success")
                            this.$nextTick(() => {
                                let data = res.data
                                this.form.entryStreetAddress = data.area
                                this.form.entryState = data.address
                                this.form.entryCity = data.city
                                this.errors.entryPostcode = ""
                                this.jpAddressInfo = {
                                    post_code: body.postCode,
                                    ...data,
                                }
                            })
                        } else {
                            this.burryPoint("Fail", 3)
                            this.$nextTick(() => {
                                this.form.entryStreetAddress = ""
                                this.form.entryState = ""
                                this.form.entryCity = ""
                                this.errors.entryPostcode = this.$c("form.form.jpPostcodeMatchErr")
                            })
                        }
                    }
                }
            } catch (err) {
                if (this.isJp) {
                    this.burryPoint("Fail", 3)
                    this.$nextTick(() => {
                        this.form.entryStreetAddress = ""
                        this.form.entryState = ""
                        this.form.entryCity = ""
                        this.errors.entryPostcode = this.$c("form.form.jpPostcodeMatchErr")
                    })
                }
                // console.log(err)
            } finally {
                this.jpSearchLoading = false
            }
        }, 500),

        onAddressSelected(obj) {
            console.log("onAddressSelected:", obj)
            try {
                const isUS = this.form.entryCountryId == 223
                if (this.citysOPtions?.length > 0 && this.form.entryCountryId == 206) {
                    const cityItem = this.citysOPtions.find((i) => i.name === obj.state)
                    if (cityItem) {
                        this.form.entryCity = cityItem.value
                    }
                } else {
                    this.form.entryCity = obj.locality || ""
                }
                let stateList = []
                if (this.isJp) {
                    stateList = this.jpProviceList
                    this.form.entryStreetAddress = obj.sublocality
                } else {
                    stateList = this.stateOptions
                    this.form.entryStreetAddress = obj.address1
                }
                const stateIndex = stateList?.findIndex((i) => i.value === obj.state)
                if (stateIndex > -1) {
                    this.form.entryState = obj.state
                    this.checkValue("entryState")
                }
                this.form.entryPostcode = isUS ? obj.postcode?.slice(0, 5) : obj.postcode
                this.checkValue("entryStreetAddress")
                this.checkValue("entryCity")
                this.checkValue("entryPostcode")
            } catch (error) {
                console.log("google api error:", error)
            }
        },
    },
}
