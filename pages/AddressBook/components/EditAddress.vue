<template>
    <fs-popup-new :clickHide="false" v-bind="$attrs" v-on="$listeners" width="750" type="all" isNew transition="slide-up" :title="title" :loading="loading" :isMDrawer="true">
        <div class="contain" :style="{ height: isMobile ? `${windowHeight - 64 - 49 - 83}px` : 'auto' }">
            <div class="add_shipping_address_pop" ref="add_address_pop">
                <FsWarn class="account_tips" v-if="isCurrentBilling && addressInfo.netTermsTipsEdit">{{ addressInfo.netTermsTipsEdit }}</FsWarn>
                <section v-if="website !== 'cn'">
                    <h6>{{ $c("pages.confirmOrder.address.basicInfo") }}</h6>
                    <div class="information">
                        <div ref="entryFirstname">
                            <p>{{ $c("pages.confirmOrder.address.firstName") }}{{ isCnTr ? $c("pages.confirmOrder.form.only_en") : " *" }}</p>
                            <input
                                type="text"
                                class="is_new"
                                :class="{ error_input: errors.entryFirstname }"
                                v-model="form.entryFirstname"
                                @input="checkValue('entryFirstname')"
                                @focus="inputClick('First Name')"
                                :placeholder="isJp ? $c('form.form.placeHolder.firstname') : ''" />
                            <validate-error :error="errors.entryFirstname" />
                        </div>
                        <div ref="entryLastname">
                            <p>{{ $c("pages.confirmOrder.address.lastName") }}{{ isCnTr ? $c("pages.confirmOrder.form.only_en") : " *" }}</p>
                            <input
                                type="text"
                                class="is_new"
                                :class="{ error_input: errors.entryLastname }"
                                v-model="form.entryLastname"
                                @input="checkValue('entryLastname')"
                                @focus="inputClick('Last Name')"
                                :placeholder="isJp ? $c('form.form.placeHolder.lastname') : ''" />
                            <validate-error :error="errors.entryLastname" />
                        </div>
                        <div class="relative" ref="entryCountryId">
                            <p>{{ $c("pages.confirmOrder.address.countryRegion") }} *</p>
                            <select-country
                                :isNewStyle="true"
                                @change="countryChange"
                                position="absolute"
                                :disabled="addressInfo.cannotModifyFiled && addressInfo.cannotModifyFiled.length > 0"
                                :filter_country="country"
                                :filter="countryList"
                                @click.native="selectClick('Country/Region')"></select-country>
                            <validate-error :error="errors.entryCountryId" @click.native="changeSite(cnRedirect)" />
                        </div>
                        <div ref="companyType">
                            <p>
                                {{ $c("pages.confirmOrder.address.addressType") }} *
                                <fs-popover position="bottom" v-if="isCompany && isCurrentBilling">
                                    <slot>
                                        <p v-html="$c('pages.MyAccount.Company.addressTypeTips1')"></p>
                                        <p v-html="$c('pages.MyAccount.Company.addressTypeTips2')"></p>
                                    </slot>
                                </fs-popover>
                            </p>
                            <fs-select :isNewStyle="true" :key="companyKey" :options="addressTypeOptions" v-model="form.companyType" @change="typeChange" @click.native="selectClick('Address Type')" />
                            <validate-error :error="errors.companyType" />
                        </div>
                        <div ref="entryCompany">
                            <p>
                                {{ $c("pages.confirmOrder.address.companyName") }}
                                <!-- {{ form.companyType === "IndividualType" && ![81, 73].includes(form.entryCountryId) ? `(${$c("pages.confirmOrder.address.optional")})` : " *" }} -->
                                {{ form.companyType === "IndividualType" ? `(${$c("pages.confirmOrder.address.optional")})` : " *" }}
                            </p>
                            <input
                                type="text"
                                class="is_new"
                                :class="{ error_input: errors.entryCompany }"
                                :disabled="addressInfo.cannotModifyFiled && addressInfo.cannotModifyFiled.length > 0"
                                v-model="form.entryCompany"
                                maxlength="120"
                                @input="checkValue('entryCompany')"
                                @focus="inputClick('Company Name')"
                                :placeholder="isJp ? $c('form.form.placeHolder.company') : ''" />
                            <validate-error :error="errors.entryCompany" />
                        </div>
                        <div v-if="taxVisible && !isJp" ref="entryTaxNumber">
                            <p>
                                <span>{{ trans.tax }} {{ form.entryCountryId == 222 ? "" : isRequired ? "*" : `(${$c("pages.confirmOrder.address.optional")})` }}</span>
                                <fs-popover position="top" v-if="taxTip" :transfer="false">
                                    <slot>
                                        <p v-html="taxTip"></p>
                                    </slot>
                                </fs-popover>
                            </p>
                            <input
                                type="text"
                                class="is_new"
                                :class="{ error_input: errors.entryTaxNumber }"
                                :placeholder="isJp ? $c('form.form.placeHolder.tax_number') : taxHolder"
                                v-model="form.entryTaxNumber"
                                @input="checkValue('entryTaxNumber')"
                                @focus="inputClick('TAX Number')" />
                            <validate-error :error="errors.entryTaxNumber" />
                        </div>
                        <div v-if="isEORI" ref="entryEori">
                            <p>{{ $c("common.basic.addressHandle.EORINumber") }}</p>
                            <input type="text" class="is_new" v-model="form.entryEori" @input="checkValue('entryEori')" />
                            <validate-error :error="errors.entryEori" />
                        </div>
                        <!-- 印度附加表单 -->
                        <template v-if="isIN">
                            <div>
                                <p>{{ $c("pages.confirmOrder.additionalInformation") }} *</p>
                                <fs-select :isNewStyle="true" :options="identityNumList" v-model="form.identityType"></fs-select>
                            </div>
                            <div ref="identityValue">
                                <p>&nbsp;</p>
                                <input type="text" class="is_new" :class="{ error_input: errors.identityValue }" v-model="form.identityValue" @focus="inputClick('identityValue')" :placeholder="INNumPlaceholder" />
                                <validate-error :error="errors.identityValue" />
                            </div>
                        </template>
                    </div>
                    <h6>{{ $c("pages.confirmOrder.address.addressInfo") }}</h6>
                    <div class="information">
                        <div v-if="website === 'sg' && !isJp" ref="entryPostcode">
                            <p>{{ website === "sg" ? "Postal code" : trans.code }} *</p>
                            <input
                                type="text"
                                class="is_new"
                                :class="{ error_input: errors.entryPostcode }"
                                v-model="form.entryPostcode"
                                @input="checkValue('entryPostcode'), postCodeChange()"
                                @focus="inputClick('Zip Code')"
                                maxlength="10" />
                            <validate-error :error="errors.entryPostcode" />
                        </div>
                        <div class="phone" v-if="website === 'sg' && !isJp" ref="entryTelephone">
                            <p>
                                {{ $c("pages.confirmOrder.address.phoneNumber") }} *
                                <fs-popover v-if="showSGTip" position="right">{{ $c("pages.confirmOrder.address.sgPhoneTip") }}</fs-popover>
                            </p>

                            <tel-code-new :isNewStyle="true" isTopPosition ref="telCode" :code.sync="form.telPrefix" v-model="form.entryTelephone" @error="phoneError" @point="inputClick('Phone Number')"></tel-code-new>
                            <validate-error :error="errors.entryTelephone" />
                        </div>
                        <div :class="{ jp_class_box: isJp }" v-if="isJp" ref="entryPostcode">
                            <p>{{ trans.code }} *</p>
                            <!-- <div :class="{ jp_class: isJp }">
                                <input
                                    type="text"
                                    :class="{ error_input: errors.entryPostcode }"
                                    v-model="form.entryPostcode"
                                    @input="checkValue('entryPostcode')"
                                    @focus="inputClick('Zip Code')"
                                    maxlength="10"
                                    :placeholder="isJp ? $c('form.form.placeHolder.postcode') : ''" />
                                <fs-button type="blackline" :text="$c('form.form.placeHolder.searchCode')" @click="postCodeChange" :loading="jpSearchLoading"></fs-button>
                            </div> -->
                            <GoogleAddressAutoComplementInput
                                v-model="form.entryPostcode"
                                :className="{ error_input: errors.entryPostcode, is_new: true }"
                                :code="country.code"
                                type="text"
                                :placeholder="isJp ? $c('form.form.placeHolder.postcode') : ''"
                                @input="checkValue('entryPostcode')"
                                @focus="inputClick('Zip Code')"
                                maxlength="10"
                                inputType="postcode"
                                @selected="onAddressSelected" />
                            <validate-error :error="errors.entryPostcode" />
                        </div>
                        <div v-if="isJp" ref="entryState">
                            <p>{{ $c("form.form.placeHolder.provice") }} *</p>
                            <fs-select
                                :isNewStyle="true"
                                :options="jpProviceList"
                                v-model="form.entryState"
                                :showDefaultValue="false"
                                isTopPosition
                                :placeholder="$c('form.form.placeHolder.province')"
                                :search="true"
                                :inpPlaceholder="$c('components.smallComponents.searchRegion')"
                                @change="jpChangeState('State/Province/Region')"></fs-select>
                            <validate-error :error="errors.entryState" />
                        </div>
                        <div v-if="isJp" ref="entryCity">
                            <p>{{ trans.city }} *</p>
                            <input
                                type="text"
                                class="is_new"
                                :class="{ error_input: errors.entryCity }"
                                v-model="form.entryCity"
                                maxlength="40"
                                @input="checkValue('entryCity')"
                                @focus="inputClick('City')"
                                :placeholder="isJp ? $c('form.form.placeHolder.city') : ''" />
                            <validate-error :error="errors.entryCity" />
                        </div>
                        <div :class="{ w100: !(isJp || website === 'sg') }" ref="entryStreetAddress">
                            <p>{{ trans.ad }}{{ isCnTr ? $c("pages.confirmOrder.form.only_en") : " *" }}</p>
                            <input
                                v-if="isJp || website === 'sg'"
                                type="text"
                                class="is_new"
                                maxlength="35"
                                :class="{ error_input: errors.entryStreetAddress }"
                                :placeholder="isJp ? $c('form.form.placeHolder.address') : form.entryCountryId === 188 ? $c('pages.confirmOrder.address.sgAd1ph') : $c('pages.confirmOrder.address.holderAd')"
                                v-model="form.entryStreetAddress"
                                @input="checkValue('entryStreetAddress')"
                                @focus="inputClick('Address')" />
                            <GoogleAddressAutoComplementInput
                                v-else
                                v-model="form.entryStreetAddress"
                                :className="{ error_input: errors.entryStreetAddress, is_new: true }"
                                :code="country.code"
                                type="text"
                                :placeholder="isJp ? $c('form.form.placeHolder.address') : form.entryCountryId === 188 ? $c('pages.confirmOrder.address.sgAd1ph') : $c('pages.confirmOrder.address.holderAd')"
                                @input="checkValue('entryStreetAddress')"
                                @focus="inputClick('Address')"
                                maxlength="35"
                                @selected="onAddressSelected" />
                            <validate-error :error="errors.entryStreetAddress" />
                            <validate-error :error="sensitiveEntryStreetAddress"></validate-error>
                        </div>
                        <div :class="{ w100: !(isJp || website === 'sg') }" ref="entrySuburb">
                            <p>
                                {{ trans.ad2 }} {{ [188].includes(form.entryCountryId) ? "" : `(${$c("pages.confirmOrder.address.optional")})` }}
                                <fs-popover className="add_shipping_address_pop" :transfer="false" v-if="isCurrentShipping && form.entryCountryId === 188">{{ $c("pages.confirmOrder.address.sgAd2Tip") }}</fs-popover>
                            </p>
                            <input
                                type="text"
                                class="is_new"
                                maxlength="40"
                                :placeholder="showSGTip ? $c('pages.confirmOrder.address.sgAd2ph') : isJp ? $c('form.form.placeHolder.address2') : $c('pages.confirmOrder.address.holderAd2')"
                                v-model.trim="form.entrySuburb"
                                @input="checkValue('entrySuburb')"
                                @focus="inputClick('Address2')" />
                            <validate-error :error="errors.entrySuburb" />
                            <validate-error :error="sensitiveEntrySuburb"></validate-error>
                        </div>
                        <!-- 台湾展示城市选项 -->
                        <div v-if="citysOPtions && citysOPtions.length && form.entryCountryId == 206" ref="entryCity">
                            <p>{{ trans.city }} *</p>
                            <fs-select
                                :isNewStyle="true"
                                :options="citysOPtions"
                                v-model="form.entryCity"
                                @change="cityChange"
                                :class="{ error_input: errors.entryState }"
                                :search="true"
                                :inpPlaceholder="$c('components.smallComponents.searchRegion')" />
                            <validate-error :error="errors.entryCity" />
                        </div>
                        <div v-else-if="(!citysOPtions || !citysOPtions.length || form.entryCountryId !== 206) && form.entryCountryId !== 188 && !isJp" ref="entryCity">
                            <p>{{ trans.city }} *</p>
                            <input type="text" class="is_new" :class="{ error_input: errors.entryCity }" v-model="form.entryCity" maxlength="40" @input="checkValue('entryCity')" @focus="inputClick('City')" />
                            <validate-error :error="errors.entryCity" />
                        </div>
                        <div v-if="stateOptions && stateOptions.length && !isJp" ref="entryState">
                            <p>{{ trans.state }} *</p>
                            <fs-select
                                :isNewStyle="true"
                                :options="stateOptions"
                                @change="stateChange"
                                :placeholder="$c('pages.confirmOrder.address.holderState')"
                                v-model="form.entryState"
                                :specialHeight="false"
                                :search="true"
                                isTopPosition
                                :inpPlaceholder="$c('components.smallComponents.searchRegion')"
                                @click.native="selectClick('State/Province/Region')" />
                            <validate-error :error="errors.entryState" />
                        </div>
                        <div class="postcode" v-if="website !== 'sg' && !isJp" ref="entryPostcode">
                            <p>{{ website === "sg" ? "Postal code" : trans.code }} *</p>
                            <input
                                type="text"
                                class="is_new"
                                :class="{ error_input: errors.entryPostcode }"
                                v-model="form.entryPostcode"
                                @input="checkValue('entryPostcode')"
                                @focus="inputClick('Zip Code')"
                                maxlength="10" />
                            <validate-error :error="errors.entryPostcode" />
                        </div>
                        <div class="phone" v-if="website !== 'sg' || (website === 'sg' && isJp)" ref="entryTelephone">
                            <p>
                                {{ $c("pages.confirmOrder.address.phoneNumber") }} *
                                <fs-popover className="add_shipping_address_pop" :transfer="false" v-if="showSGTip && website != 'sg'">{{ $c("pages.confirmOrder.address.sgPhoneTip") }}</fs-popover>
                            </p>

                            <tel-code-new
                                :isNewStyle="true"
                                isTopPosition
                                :error="errors.entryTelephone"
                                :code.sync="form.telPrefix"
                                :canZeroBegin="form.entryCountryId == 105"
                                @error="phoneError"
                                ref="telCode"
                                @point="inputClick('Phone Number')"
                                v-model="form.entryTelephone"></tel-code-new>
                            <validate-error :error="errors.entryTelephone" />
                        </div>
                    </div>
                    <!-- <div class="sg_tip" v-if="showSGTip && website != 'sg'">{{ $c("pages.confirmOrder.address.sgPhoneTip") }}</div> -->
                    <div class="tip">*{{ $c("pages.confirmOrder.address.shippingTip") }}</div>
                </section>
                <section class="cn" v-else>
                    <div class="information">
                        <div class="row">
                            <div ref="entryFirstname">
                                <p>姓名 *</p>
                                <input type="text" class="is_new" :class="{ error_input: errors.entryFirstname }" v-model="form.entryFirstname" @input="checkValue('entryFirstname')" @focus="inputClick('First Name')" />
                                <validate-error :error="errors.entryFirstname" />
                            </div>
                            <div class="phone" ref="entryTelephone">
                                <p>电话 *</p>
                                <tel-code-new
                                    :isNewStyle="true"
                                    isTopPosition
                                    :error="errors.entryTelephone"
                                    :code.sync="form.telPrefix"
                                    @error="phoneError"
                                    ref="telCode"
                                    @point="inputClick('Phone Number')"
                                    v-model="form.entryTelephone"></tel-code-new>
                                <validate-error :error="errors.entryTelephone" />
                            </div>
                        </div>
                        <div ref="entryState">
                            <p>地区 *</p>
                            <!-- entryState 省 ，entryCity市，entrySuburb县/区 -->
                            <cn-area :isNewStyle="true" v-model="address" @change="changeAd" :value="address" placeholder="请选择省 / 市 / 区"></cn-area>
                            <validate-error :error="errors.entryState" />
                        </div>
                        <div ref="entryStreetAddress">
                            <p>详细地址 *</p>
                            <input
                                type="text"
                                class="is_new"
                                :class="{ error_input: errors.entryStreetAddress }"
                                v-model="form.entryStreetAddress"
                                @input="checkValue('entryStreetAddress')"
                                @focus="inputClick('Address')" />
                            <validate-error :error="errors.entryStreetAddress" />
                        </div>
                        <div ref="entryPostcode">
                            <p>邮政编码（选填）</p>
                            <input type="text" class="is_new" :class="{ error_input: errors.entryPostcode }" v-model="form.entryPostcode" @input="checkValue('entryPostcode')" @focus="inputClick('Zip Code')" />
                            <validate-error :error="errors.entryPostcode" />
                        </div>
                        <div ref="entryEmail">
                            <p>{{ $c("pages.Login.Email_Address") }} ({{ $c("form.form.Optional") }})</p>
                            <input type="text" class="is_new" :class="{ error_input: errors.entryEmail }" v-model="form.entryEmail" @input="checkValue('entryEmail')" @focus="inputClick('Email')" />
                            <validate-error :error="errors.entryEmail" />
                        </div>
                    </div>
                </section>
                <label class="set_default">
                    <input type="checkbox" v-model="applyDefault" />
                    <span>{{ $c("pages.confirmOrder.address.defaultAddress") }}</span>
                    <!-- <span @click="defaultHandle"><i :class="{ active: applyDefault }" @keyup.enter.stop="defaultHandle"></i>{{ $c("pages.confirmOrder.address.defaultAddress") }}</span> -->
                </label>
            </div>
        </div>
        <template slot="footer">
            <div class="btn">
                <FsButton type="white" class="cancel_btn" @click="close">{{ $c("pages.confirmOrder.address.back") }}</FsButton>
                <FsButton @click="save">
                    {{ $c("pages.confirmOrder.address.save") }}
                </FsButton>
            </div>
        </template>
        <fs-message :show="showErrorTip" @change="showErrorTip = false">
            <div v-html="errorMsg" id="errMsg"></div>
        </fs-message>
    </fs-popup-new>
</template>

<script>
import { mixin } from "./formMixin"

export default {
    mixins: [mixin],

    data() {
        return {
            title: this.$c("pages.confirmOrder.address.addShippingAddress"),
            loading: false,
        }
    },
    mounted() {},
    methods: {
        close() {
            this.btnClick("")
            this.$emit("close")
        },
    },
}
</script>

<style lang="scss" scoped>
::v-deep .fs-popup.is_new .fs-popup-body {
    padding: 16px 24px 24px;
    @media (max-width: 768px) {
        padding: 16px 16px 20px;
    }
}
::v-deep .fs-popup.is_new .fs-popup-footer {
    padding: 0 24px 24px;
    @media (max-width: 768px) {
        padding: 0 16px 16px;
    }
}
.contain {
    // overflow: auto;
    transition: 0.2s height;
}
.account_tips {
    margin-top: -20px;
    margin-bottom: 16px;
}
.add_shipping_address_pop {
    // padding: 20px 32px;
    color: #19191a;
    font-weight: 400;
    @include font14;
    max-width: 100vw;
    @media (max-width: 960px) {
        // padding: 20px 16px;
        width: 100%;
    }
    .sg_tip {
        color: #707070;
        @include font14;
        margin-top: 4px;
        width: 100%;
    }
    h6 {
        font-size: 16px;
        font-weight: 600;
        margin-bottom: -4px;
        &:last-of-type {
            margin-top: 20px;
        }
    }
    ::v-deep {
        .p_cn_area_wrap,
        .m_cn_area_wrap {
            width: 100%;
        }
    }
    // ::v-deep .fs-select {
    //     .options-box {
    //         max-height: 105px;
    //         overflow: auto;
    //     }
    // }

    ::v-deep .country-wrap {
        .country-wrap-box {
            .clear {
                line-height: 14px;
            }
        }
    }
    input[type="number"] {
        &::-webkit-outer-spin-button,
        &::-webkit-inner-spin-button {
            -webkit-appearance: none;
        }
        -moz-appearance: textfield;
    }
    .cn {
        > .information {
            > div {
                width: 100%;
                &.row {
                    display: flex;
                    > div {
                        width: 49%;
                        &:first-child {
                            margin-right: 20px;
                        }
                    }
                }
            }
        }
    }
    .information {
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
        > div {
            width: calc((100% - 20px) / 2);
            margin-top: 16px;
            &.w100 {
                width: 100%;
            }
            @media (max-width: 768px) {
                width: 100%;
            }
            p {
                margin-bottom: 4px;
                color: $textColor3;
                @include font12;
                ::v-deep .fs-tip {
                    .info {
                        min-width: 300px;
                    }
                }
            }
            ::v-deep .select-country-active {
                border-radius: 4px;
            }
            ::v-deep .fs-select-active {
                border-radius: 4px;
            }
        }
        .phone {
            .input {
                display: flex;
                span {
                    width: 52px;
                    border-top-left-radius: 2px;
                    border-bottom-left-radius: 2px;
                    background: #f7f7f7;
                    border: 1px solid #e5e5e5;
                    border-right: none;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                }
                input {
                    width: auto;
                    flex: 1;
                    border-top-left-radius: 0;
                    border-bottom-left-radius: 0;
                }
            }
            ::v-deep .code {
                border-radius: 4px 0 0 4px;
            }
        }
        /* .error_input {
            @include errorInput;
        } */
        .jp_class_box {
            .jp_class {
                display: flex;
            }
            input {
                // width: auto;
                // flex: 1;
                margin-right: 8px;
            }
        }
    }
    .tip {
        font-size: 14px;
        color: #707070;
        margin-top: 16px;
    }
    .set_default {
        font-size: 14px;
        color: #19191a;
        line-height: 22px;
        vertical-align: middle;
        padding-top: 20px;
        display: flex;
        align-items: center;
        @media (max-width: 768px) {
            padding-bottom: 20px;
        }
        input[type="checkbox"] {
            margin-right: 8px;
            font-size: 14px !important;
            width: 14px !important;
            height: 14px !important;
        }
        > span {
            display: flex;
            cursor: pointer;
            font-size: 13px;
            color: #707070;
        }
        // .active {
        //     border-color: #707070;
        //     &::after {
        //         height: 10px;
        //         width: 10px;
        //     }
        // }
        // i {
        //     display: inline-block;
        //     width: 18px;
        //     height: 18px;
        //     border-radius: 50%;
        //     position: relative;
        //     top: 2px;
        //     border: 1px solid #707070;
        //     margin-right: 8px;
        //     transition: all 0.2s;
        //     flex-shrink: 0;
        //     &::after {
        //         content: "";
        //         width: 0px;
        //         height: 0px;
        //         position: absolute;
        //         top: 50%;
        //         left: 50%;
        //         transform: translate(-50%, -50%);
        //         border-radius: 50%;
        //         background: #fff;
        //         transition: all 0.2s;
        //         background: #707070;
        //     }
        // }
    }
    @media (max-width: 960px) {
        .cn > .information > div.row {
            display: block;
            > div {
                width: 100%;
                &:first-child {
                    margin-right: 0px;
                    margin-bottom: 16px;
                }
            }
        }
    }
}
.btn {
    display: flex;
    justify-content: flex-end;
    // padding: 20px 32px;
    // border: 1px solid #e5e5e5;
    @media (max-width: 768px) {
        display: flex;
        flex-direction: column-reverse;
        // padding: 20px 16px;
    }
    button {
        // min-width: 110px;
        margin-left: 16px;
        @media (max-width: 768px) {
            width: 100%;
            margin-left: 0;
            &:last-child {
                margin-bottom: 12px;
            }
        }
        @media (max-width: 767px) {
            .cancel_btn {
                display: none;
            }
            button {
                margin: 0;
                &:last-child {
                    margin-bottom: 0;
                }
            }
        }
    }
}
@media (max-width: 768px) {
    .btn {
        border: none;
        // border-top: 1px solid #e5e5e5;
    }
}
</style>
