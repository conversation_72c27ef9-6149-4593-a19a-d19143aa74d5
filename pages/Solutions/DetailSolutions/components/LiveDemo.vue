<template>
    <div class="container">
        <div class="top_line"></div>
        <div class="introduction">
            <div class="content">
                <div class="left_div">
                    <p class="tit">{{ liveDemo.title }}</p>
                    <p class="desc">{{ liveDemo.desc }}</p>
                    <div class="btn_div">
                        <fs-button type="black" @click="buttonClick(liveDemo.link1, 1)">{{ liveDemo.btn1 }}</fs-button>
                        <fs-button type="bluetext-link" @click="buttonClick(liveDemo.link2, 2)">{{ liveDemo.btn2 }}</fs-button>
                    </div>
                </div>
                <div class="right_div">
                    <img v-lazy="liveDemo.image" alt="" />
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import FsButton from "@/components/FsButton/FsButton"
export default {
    components: {
        FsButton,
    },
    props: {
        tag_id: {
            type: String,
            default: "",
        },
    },
    data() {
        return {
            liveDemo: {
                title: this.$c("solutions.CampusBranch.liveDemo.title"),
                desc: this.$c("solutions.CampusBranch.liveDemo.desc"),
                btn1: this.$c("solutions.CampusBranch.liveDemo.btn1"),
                btn2: this.$c("solutions.CampusBranch.liveDemo.btn2"),
                link1: "/solution-demo-test.html",
                link2: "/specials/ampcon-management-platform-165.html",
                image: "https://resource.fs.com/mall/generalImg/20241022175519r5pn6s.jpg",
            },
        }
    },
    mounted() {
        this.updateLiveDemo()
    },
    methods: {
        updateLiveDemo() {
            if (this.tag_id === "S10084") {
                this.liveDemo.title = this.$c("solutions.CampusBranch.liveDemo.title")
                this.liveDemo.link1 = "/solution-demo-test.html"
                this.liveDemo.link2 = "/specials/ampcon-management-platform-165.html"
            } else if (this.tag_id === "S10065") {
                this.liveDemo.title = this.$c("solutions.GamingDataCenter.liveDemo.title")
                this.liveDemo.link1 = "/data-center-solution-demo-test.html"
                this.liveDemo.link2 = "/specials/ampcon-management-platform-165.html"
            } else {
            }
        },
        buttonClick(url, index) {
            if (url.includes("https")) {
                window.location.href = url
            } else if (url.includes("www")) {
                window.location.href = `https://${url}`
            } else {
                window.location.href = this.$localeLink(url)
            }
            console.log("urlllll3333==", url)

            if (index === 1) {
                if (this.tag_id === "S10084") {
                    this.gaEvent("Demo Information", "Demo_Get started")
                } else if (this.tag_id === "S10065") {
                    this.gaEvent("Data Center Demo Information", "Data Center Demo_Get started")
                } else {
                }
            } else {
                if (this.tag_id === "S10084") {
                    this.gaEvent("Demo_Information", "Ampcon_Learn more")
                } else if (this.tag_id === "S10065") {
                    this.gaEvent("Data Center Demo_Information", "Picos_Learn more")
                } else {
                }
            }
        },
        gaEvent(eventAction, eventLabel) {
            window.dataLayer &&
                window.dataLayer.push({
                    event: "uaEvent",
                    eventCategory: "Solution Page",
                    eventAction,
                    eventLabel,
                    nonInteraction: false,
                })
        },
    },
}
</script>

<style lang="scss" scoped>
.container {
    padding: 40px 48px;
    position: relative;
    .top_line {
        display: none;
        position: absolute;
        background: $bgColor2;
        width: calc(100% - 96px);
        max-width: 1200px;
        height: 1px;
        top: 0;
        left: 50%;
        transform: translateX(-50%);
    }
    .introduction {
        max-width: 1200px;
        margin: 0 auto;

        .content {
            font-size: 0;
            display: flex;

            .left_div {
                width: 50%;
                padding: 20px 36px 20px 0;
                display: flex;
                flex-direction: column;
                justify-content: center;
                .tit {
                    @include font20;
                    font-weight: 600;
                    color: $textColor1;
                    margin-bottom: 8px;
                }
                .desc {
                    @include font14;
                    color: $textColor3;
                    margin-bottom: 16px;
                }
                .btn_div {
                    ::v-deep {
                        .bluetext-link {
                            width: fit-content;
                            margin-left: 16px;
                            color: $textColor1;
                            .iconfont_right {
                                color: $textColor1;
                                margin-left: 0;
                            }
                        }
                    }
                }
            }
            .right_div {
                width: 50%;
                img {
                    width: 100%;
                    height: auto;
                    border-radius: 8px;
                }
            }
        }
    }
}

@media (max-width: 1024px) {
    .container {
        padding: 40px 24px;
        .top_line {
            width: calc(100% - 48px);
        }
    }
}

@media (max-width: 768px) {
    .container {
        padding: 36px 16px;
        .top_line {
            width: calc(100% - 32px);
        }
        .introduction {
            .content {
                flex-direction: column-reverse;
                .left_div {
                    width: 100%;
                    padding: 20px 0 0 0;
                    .tit {
                        @include font14;
                    }
                }
                .right_div {
                    width: 100%;
                    // img {
                    //     width: 100%;
                    //     height: auto;
                    //     border-radius: 8px;
                    // }
                }
            }
        }
    }
}
</style>
