<template>
    <div class="container">
        <skeleton-solutions v-if="bannerBackgroundImage" :item="{ width: '100%', bg: '#F2F2F2', height: '100%' }"> </skeleton-solutions>
        <video-autoplay class="banner-video" :src="bannerBackgroundImage" :poster="bannerBackgroundImage" :icon="isVideoResource"></video-autoplay>
        <div class="content_div">
            <div class="content_wrap">
                <div class="breadcrumb" :class="{ black_breadcrumb: banner.fontColor == '#19191A' }">
                    <bread-crumb :list="crumbList" size="small"></bread-crumb>
                </div>
                <div class="text_content" :style="{ color: `${banner.fontColor}`, width: describWidth }">
                    <h1 class="title_h1" :style="{ 'text-align': banner.alignment == 0 ? 'center' : 'left' }">{{ banner.title }}</h1>
                    <p class="desc_wrap" :style="{ 'text-align': banner.alignment == 0 ? 'center' : 'left' }">{{ banner.description }}</p>
                    <fs-button v-if="banner.buttonStatus == 1" :type="buttonType" :class="{ whiteColor: banner.fontColor === '#FFFFFF', hide_btn: banner.buttonStyle == 1 }" @click="bannerClick()">{{
                        banner.buttonDesc
                    }}</fs-button>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import BreadCrumb from "@/components/BreadCrumb/BreadCrumb"
import VideoAutoplay from "@/components/FsVideo/VideoAutoplay.vue"
import SkeletonSolutions from "@/components/Skeleton/SkeletonSolutions"
import FsButton from "@/components/FsButton/FsButton"

import { mapState } from "vuex"
export default {
    components: {
        BreadCrumb,
        VideoAutoplay,
        SkeletonSolutions,
        FsButton,
    },
    props: {
        banner: {
            type: Object,
            default: () => {},
        },
        crumbList: {
            type: Array,
            default: () => [],
        },
        type: {
            type: Number,
            default: 1,
        },
        tag_id: {
            type: String,
            default: "",
        },
    },
    mounted() {
        console.log("666666666666:", this.banner)
    },
    computed: {
        ...mapState({
            screenWidth: (state) => state.device.screenWidth,
            deviceType: (state) => state.device.deviceType,
        }),
        bannerBackgroundImage() {
            if (this.screenWidth > 1024) {
                return "https://resource.fs.com/" + this.banner.imageOrVideo
            } else if (this.screenWidth > 768) {
                return "https://resource.fs.com/" + this.banner.padImageOrVideo
            } else {
                return "https://resource.fs.com/" + this.banner.mobileImageOrVideo
            }
        },
        buttonType() {
            if (this.banner.buttonStyle == 1) {
                if (this.banner.fontColor == "#19191A") {
                    return "blackline"
                } else {
                    return "whiteline"
                }
            } else {
                return "bluetext-link"
            }
        },
        isPc() {
            if (this.screenWidth > 1024) {
                return true
            } else {
                return false
            }
        },
        isVideoResource() {
            if (this.bannerBackgroundImage.endsWith(".mp4")) {
                return true
            }
            return false
        },
        describWidth() {
            if (this.deviceType == "m") {
                return `calc(100% - 48px)`
            }
            if (this.banner.alignment == 0) {
                //center
                return `calc(100% - 96px)`
            } else {
                if (this.deviceType == "pad") {
                    return `640px`
                }
                return `750px`
            }
        },
    },
    methods: {
        bannerClick() {
            if (this.banner.pdfLink.includes("https")) {
                window.location.href = this.banner.pdfLink
            } else if (this.banner.pdfLink.includes("www")) {
                window.location.href = `https://${this.banner.pdfLink}`
            } else {
                window.location.href = this.$localeLink(this.banner.pdfLink)
            }
            if (this.tag_id === "S10084") {
                this.gaEvent("Demo_Information", "Demo_Learn more")
            } else if (this.tag_id === "S10065") {
                this.gaEvent("Data Center Demo_Information", "Data Center Demo_Learn more")
            } else {
            }
        },
        gaEvent(eventAction, eventLabel) {
            window.dataLayer &&
                window.dataLayer.push({
                    event: "uaEvent",
                    eventCategory: "Solution Page",
                    eventAction,
                    eventLabel,
                    nonInteraction: false,
                })
        },
    },
}
</script>

<style lang="scss" scoped>
.container {
    position: relative;
    height: 360px;
    ::v-deep {
        .skeleton_wrap {
            z-index: 0;
        }
    }
    .banner_div {
        position: absolute;
        width: 100%;
        height: 100%;
        background: center/cover no-repeat;
        z-index: -1;
        padding: 0 48px;
    }
    .banner-video {
        width: 100%;
        height: 100%;
        position: absolute;
        top: 0;
        left: 0;
        &::v-deep {
            video {
                width: 100%;
                height: 100%;
                object-fit: cover;
            }
            /* .iconfont_play {
                right: 20px;
                background: rgba(255, 255, 255, 0.1);
                z-index: 1;
                &:hover {
                    background: rgba(255, 255, 255, 0.3);
                }
            } */
        }
    }
    .content_div {
        width: 100%;
        height: 100%;
        padding: 0 48px;
        .content_wrap {
            max-width: 1200px;
            margin: 0 auto;
            .breadcrumb {
                padding-top: 20px;
                &::v-deep .bread-item {
                    &:after {
                        color: $textColor7;
                        opacity: 0.7;
                    }
                    a {
                        color: $textColor7;
                        opacity: 0.7;
                    }
                    &:last-child {
                        > a {
                            color: $textColor7 !important;
                            opacity: 1;
                        }
                    }
                }
                &.black_breadcrumb {
                    &::v-deep .bread-item {
                        &:after {
                            color: $textColor1;
                            opacity: 0.7;
                        }
                        a {
                            color: $textColor1;
                            opacity: 0.7;
                        }
                        &:last-child {
                            > a {
                                color: $textColor1 !important;
                                opacity: 1;
                            }
                        }
                    }
                }
            }
            .text_content {
                width: calc(100% - 96px);
                max-width: 1200px;
                // color: #fff;
                position: absolute;
                top: 50%;
                transform: translateY(-50%);
                .title_h1 {
                    max-width: 620px;
                    @include font32;
                    font-weight: 600;
                    white-space: pre-line;
                }
                .desc_wrap {
                    max-width: 560px;
                    @include font16;
                    font-weight: 400;
                    margin-top: 16px;
                    white-space: pre-line;
                }
                ::v-deep {
                    .fs-button {
                        margin-top: 20px;
                        background: rgba(255, 255, 255, 0.1);
                        &.fs-button-blackline,
                        &.fs-button-whiteline {
                            font-weight: 600;
                        }
                        &:hover {
                            background: rgba(255, 255, 255, 0.2);
                        }
                    }
                    .bluetext-link {
                        margin-top: 20px;
                        color: $textColor1;
                        .iconfont_right {
                            color: $textColor1;
                            margin-left: 0;
                        }
                        &.whiteColor {
                            color: $textColor7;
                            .iconfont_right {
                                color: $textColor7;
                            }
                        }
                    }
                }
            }
        }
    }
}

@media (max-width: 1024px) {
    .container {
        .content_div {
            .content_wrap {
                width: 100%;
                // .breadcrumb {
                //     ::v-deep {
                //         .bread-crumb {
                //             display: block;
                //         }
                //     }
                // }
                .text_content {
                    width: calc(100% - 96px);
                    .title_h1 {
                        max-width: 460px;
                    }
                    .desc_wrap {
                        max-width: 400px;
                    }
                    ::v-deep {
                        .fs-button {
                            &.hide_btn {
                                display: none;
                            }
                        }
                    }
                }
            }
        }
    }
}

@media (max-width: 768px) {
    .container {
        height: 440px;
        .content_div {
            padding: 0 24px;
            .content_wrap {
                .breadcrumb {
                    padding: 0;
                    ::v-deep {
                        .bread-crumb {
                            display: none;
                        }
                    }
                }
                .text_content {
                    padding-top: 56px;
                    top: 0;
                    transform: translateY(0);
                    width: calc(100% - 48px);
                    .title_h1 {
                        @include font24;
                    }
                    .desc_wrap {
                        @include font14;
                        margin-top: 12px;
                    }
                }
            }
        }
    }
}
</style>
