<template>
    <div class="container" :style="`background: ${products.backgroundColor}`">
        <div v-if="products.displayStyle == 0">
            <ProductTab :productData="products"></ProductTab>
        </div>
        <div class="content_div" v-else-if="products.displayStyle == 1 || !products.displayStyle">
            <h2 v-if="products.titleStatus == 1" class="title" :style="{ 'text-align': products.alignment == 0 ? 'center' : 'left' }">{{ products.title }}</h2>
            <div class="product_div" v-for="(listItem, listIndex) in products.productCategoriesItems" :key="listIndex">
                <p v-if="listItem.titleStatus == 1" class="list_title" :class="{ first_list_title: listIndex === 0 }">{{ listItem.title }}</p>
                <div :class="{ product_grid_div_fiv: listItem.productsItems.length == 5, product_grid_div: listItem.productsItems.length != 5 }">
                    <template v-for="(item, index) in listItem.productsItems">
                        <!-- :class="{ decoration_none: item.quickLinks.length === 0 }" -->
                        <div v-if="item.productsStatus" class="product_item" :key="index" :class="{ dis_product: disabledProduct(item.quickLinks) || item.quickLinks.length === 0 }">
                            <div class="img_box" v-if="item.imgStatus == 1">
                                <img :src="`https://resource.fs.com/${item.imgUrl}`" alt="" />
                                <skeleton-solutions v-if="item.imgUrl" :item="{ width: '100%', bg: '#F2F2F2', height: '100%' }"> </skeleton-solutions>
                            </div>
                            <div v-if="item.quickLinksStatus == 1" class="text_div" :class="{ no_border: products.backgroundColor === '#FAFAFB' || products.backgroundColor === '#F7F7F7' }">
                                <h5>{{ item.title }}</h5>
                                <div class="links">
                                    <a
                                        v-for="(textItem, descIndex) in item.quickLinks"
                                        :key="descIndex"
                                        :href="linkHref(textItem.url)"
                                        :target="linkHref(textItem.url) === 'javascript:;' ? '_self' : '_blank'"
                                        :class="{ dis_a: disabledItem(textItem.url) }"
                                        >{{ textItem.title }}</a
                                    >
                                </div>
                            </div>
                        </div>
                    </template>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import { mapState } from "vuex"
import ProductTab from "./ProductTab.vue"
import SkeletonSolutions from "@/components/Skeleton/SkeletonSolutions"

export default {
    components: {
        ProductTab,
        SkeletonSolutions,
    },
    props: {
        products: {
            type: Object,
            default: () => {},
        },
        tag_id: {
            type: String,
            default: "",
        },
    },
    computed: {
        ...mapState({
            website: (state) => state.webSiteInfo.website,
        }),
    },
    data() {
        return {}
    },
    mounted() {},
    methods: {
        disabledProduct(item) {
            if (this.tag_id === "S10058") {
                let disAll = true
                item.forEach((element) => {
                    if (element.url && !this.filter10058(element.url)) {
                        disAll = false
                    }
                })
                return ["cn", "hk", "mo", "tw"].includes(this.website) && disAll
            } else if (this.tag_id === "S10063") {
                let disAll = true
                item.forEach((element) => {
                    if (element.url && !this.filter10063(element.url)) {
                        disAll = false
                    }
                })
                return ["cn", "hk", "mo", "tw"].includes(this.website) && disAll
            } else if (this.tag_id === "S10064") {
                let disAll = true
                item.forEach((element) => {
                    if (element.url && !this.filter10064(element.url)) {
                        disAll = false
                    }
                })
                return ["cn", "hk", "mo", "tw"].includes(this.website) && disAll
            } else if (this.tag_id === "S10065") {
                let disAll = true
                item.forEach((element) => {
                    if (element.url && !this.filter10065(element.url)) {
                        disAll = false
                    }
                })
                return ["cn", "hk", "mo", "tw"].includes(this.website) && disAll
            } else if (this.tag_id === "S10066") {
                let disAll = true
                item.forEach((element) => {
                    if (element.url && !this.filter10066(element.url)) {
                        disAll = false
                    }
                })
                return ["cn", "hk", "mo", "tw"].includes(this.website) && disAll
            } else if (this.tag_id === "S10068") {
                let disAll = true
                item.forEach((element) => {
                    if (element.url && !this.filter10068(element.url)) {
                        disAll = false
                    }
                })
                return ["cn", "hk", "mo", "tw"].includes(this.website) && disAll
            } else if (this.tag_id === "S10072") {
                let disAll = true
                item.forEach((element) => {
                    if (element.url && !this.filter10072(element.url)) {
                        disAll = false
                    }
                })
                return ["cn", "hk", "mo", "tw"].includes(this.website) && disAll
            } else if (this.tag_id === "S10078") {
                let disAll = true
                item.forEach((element) => {
                    if (element.url && !this.filter10078(element.url)) {
                        disAll = false
                    }
                })
                return ["cn", "hk", "mo", "tw"].includes(this.website) && disAll
            } else if (this.tag_id === "S10085") {
                let disAll = true
                item.forEach((element) => {
                    if (element.url && !this.filter10085(element.url)) {
                        disAll = false
                    }
                })
                return ["cn", "hk", "mo", "tw"].includes(this.website) && disAll
            } else if (this.tag_id === "S10099") {
                let disAll = true
                item.forEach((element) => {
                    if (element.url && !this.filter10099(element.url)) {
                        disAll = false
                    }
                })
                return ["cn", "hk", "mo", "tw"].includes(this.website) && disAll
            } else {
                return false
            }
        },
        disabledItem(url) {
            if (url) {
                if (this.tag_id === "S10058") {
                    return ["cn", "hk", "mo", "tw"].includes(this.website) && this.filter10058(url)
                } else if (this.tag_id === "S10063") {
                    return ["cn", "hk", "mo", "tw"].includes(this.website) && this.filter10063(url)
                } else if (this.tag_id === "S10064") {
                    return ["cn", "hk", "mo", "tw"].includes(this.website) && this.filter10064(url)
                } else if (this.tag_id === "S10065") {
                    return ["cn", "hk", "mo", "tw"].includes(this.website) && this.filter10065(url)
                } else if (this.tag_id === "S10066") {
                    return ["cn", "hk", "mo", "tw"].includes(this.website) && this.filter10066(url)
                } else if (this.tag_id === "S10068") {
                    return ["cn", "hk", "mo", "tw"].includes(this.website) && this.filter10068(url)
                } else if (this.tag_id === "S10072") {
                    return ["cn", "hk", "mo", "tw"].includes(this.website) && this.filter10072(url)
                } else if (this.tag_id === "S10078") {
                    return ["cn", "hk", "mo", "tw"].includes(this.website) && this.filter10078(url)
                } else if (this.tag_id === "S10085") {
                    return ["cn", "hk", "mo", "tw"].includes(this.website) && this.filter10085(url)
                } else if (this.tag_id === "S10099") {
                    return ["cn", "hk", "mo", "tw"].includes(this.website) && this.filter10099(url)
                } else {
                    return false
                }
            } else {
                return true
            }
        },
        linkHref(url) {
            if (url) {
                if (this.tag_id === "S10058") {
                    return ["cn", "hk", "mo", "tw"].includes(this.website) && this.filter10058(url) ? "javascript:;" : this.$handleLink(url).url
                } else if (this.tag_id === "S10063") {
                    return ["cn", "hk", "mo", "tw"].includes(this.website) && this.filter10063(url) ? "javascript:;" : this.$handleLink(url).url
                } else if (this.tag_id === "S10064") {
                    return ["cn", "hk", "mo", "tw"].includes(this.website) && this.filter10064(url) ? "javascript:;" : this.$handleLink(url).url
                } else if (this.tag_id === "S10065") {
                    return ["cn", "hk", "mo", "tw"].includes(this.website) && this.filter10065(url) ? "javascript:;" : this.$handleLink(url).url
                } else if (this.tag_id === "S10066") {
                    return ["cn", "hk", "mo", "tw"].includes(this.website) && this.filter10066(url) ? "javascript:;" : this.$handleLink(url).url
                } else if (this.tag_id === "S10068") {
                    return ["cn", "hk", "mo", "tw"].includes(this.website) && this.filter10068(url) ? "javascript:;" : this.$handleLink(url).url
                } else if (this.tag_id === "S10072") {
                    return ["cn", "hk", "mo", "tw"].includes(this.website) && this.filter10072(url) ? "javascript:;" : this.$handleLink(url).url
                } else if (this.tag_id === "S10078") {
                    return ["cn", "hk", "mo", "tw"].includes(this.website) && this.filter10078(url) ? "javascript:;" : this.$handleLink(url).url
                } else if (this.tag_id === "S10085") {
                    return ["cn", "hk", "mo", "tw"].includes(this.website) && this.filter10085(url) ? "javascript:;" : this.$handleLink(url).url
                } else if (this.tag_id === "S10099") {
                    return ["cn", "hk", "mo", "tw"].includes(this.website) && this.filter10099(url) ? "javascript:;" : this.$handleLink(url).url
                } else {
                    return this.$handleLink(url).url
                }
            } else {
                return "javascript:;"
            }
        },
        filter10058(url) {
            return ["/products/115385.html", "/products/207189.html", "/products/223011.html", "/products/222999.html", "/products/149659.html", "/products/162394.html"].some((string) => url.includes(string))
        },
        filter10063(url) {
            return ["/products/186776.html", "/products/149659.html", "/products/179641.html", "/products/162394.html", "/products/195436.html", "/products/195428.html", "/products/174018.html"].some((string) =>
                url.includes(string)
            )
        },
        filter10064(url) {
            return [
                "/products/166627.html",
                "/products/161907.html?attribute=76529&id=2094775",
                "/products/161850.html?attribute=49278&id=2094506",
                "/products/161849.html?attribute=49281&id=2094672",
                "/products/161851.html?attribute=49284&id=2094711",
                "/products/207189.html",
                "/products/138717.html",
                "/products/108705.html",
            ].some((string) => url.includes(string))
        },
        filter10065(url) {
            return [
                "/products/196175.html",
                "/products/205269.html",
                "/products/206229.html",
                "/products/206401.html",
                "/products/205267.html",
                "/products/205265.html",
                "/products/207061.html",
                "/products/207065.html",
                "/products/206801.html",
                "/products/207189.html",
                "/products/206803.html",
                "/products/177447.html",
                "/products/157694.html",
                "/products/150004.html",
            ].some((string) => url.includes(string))
        },
        filter10066(url) {
            return ["/products/140093.html", "/products/138716.html", "/products/195436.html", "/products/174116.html", "/products/174016.html"].some((string) => url.includes(string))
        },
        filter10068(url) {
            return [
                "/products/196175.html",
                "/products/206579.html",
                "/products/206401.html",
                "/products/205269.html",
                "/products/205267.html",
                "/products/207065.html",
                "/products/139688.html",
                "/products/177272.html",
                "/products/119646.html",
            ].some((string) => url.includes(string))
        },
        filter10072(url) {
            return ["/products/196175.html", "/products/207189.html", "/products/223011.html", "/products/222999.html", "/products/188837.html", "/products/149656.html", "/products/170047.html"].some((string) =>
                url.includes(string)
            )
        },
        filter10078(url) {
            return ["/products/196175.html", "/products/205269.html", "/products/205267.html", "/products/139689.html"].some((string) => url.includes(string))
        },
        filter10085(url) {
            return ["/products/108710.html", "/products/143751.html", "/products/203895.html", "/products/108705.html", "/products/195440.html"].some((string) => url.includes(string))
        },
        filter10099(url) {
            return [
                "/products/189431.html",
                "/products/190465.html",
                "/products/173970.html",
                "/products/174025.html",
                "/products/174027.html",
                "/products/204101.html",
                "/search_result?keyword=SWB950",
                "/products/195435.html",
                "/products/195436.html",
                "/products/174115.html",
                "/products/140093.html",
                "/products/190464.html",
                "/products/170047.html",
                "/products/138716.html",
            ].some((string) => url.includes(string))
        },
        productClick(url) {
            if (url) {
                window.open(this.localePath({ path: url }))
            }
        },
    },
}
</script>

<style scoped lang="scss">
.container {
    padding: 40px 48px;
    .content_div {
        @include width1200;
        .title {
            @include font20;
            // text-align: center;
            color: $textColor1;
            font-weight: 600;
            padding-bottom: 24px;
        }
        .product_div {
            .list_title {
                @include font16;
                text-align: center;
                font-weight: 600;
                color: $textColor1;
                padding: 36px 0 20px;
                &.first_list_title {
                    padding: 0px 0 20px;
                }
            }
            .product_grid_div {
                display: flex;
                flex-wrap: wrap; /* 允许换行 */
                justify-content: center;
                gap: 20px 20px;
                .product_item {
                    width: calc(25% - 15px);
                    display: flex;
                    flex-direction: column;
                    transition: all 0.3s;
                    border-radius: 8px;

                    &:hover {
                        box-shadow: 0 15px 15px -10px rgb(0 0 0 / 15%);
                    }
                    // &.decoration_none {
                    //     &:hover {
                    //         box-shadow: none;
                    //         cursor: auto;
                    //         .text_div {
                    //             a:hover {
                    //                 cursor: text;
                    //                 text-decoration: none;
                    //             }
                    //         }
                    //     }
                    // }
                    &.dis_product {
                        box-shadow: none;
                    }
                    .img_box {
                        border-top-left-radius: 8px;
                        border-top-right-radius: 8px;
                        overflow: hidden;
                        position: relative;
                        min-height: calc((min(84vw, 1200px) - 60px) / 8);
                        ::v-deep {
                            .skeleton_wrap {
                                z-index: 0;
                            }
                        }
                        @media (max-width: 768px) {
                            min-height: calc((100vw - 32px) / 2);
                        }
                    }
                    img {
                        width: 100%;
                        height: auto;
                        display: block;
                        position: relative;
                        z-index: 1;
                    }
                    .text_div {
                        padding: 20px;
                        color: $textColor1;
                        background-color: $bgColor3;
                        border: 1px solid #e5e5e5;
                        border-top: none;
                        flex: 1;
                        border-bottom-left-radius: 8px;
                        border-bottom-right-radius: 8px;
                        &.no_border {
                            border: none;
                        }
                        h5 {
                            @include font14;
                            font-weight: 600;
                            margin-bottom: 12px;
                        }
                        .links {
                            display: flex;
                            flex-direction: column;
                        }
                        a {
                            @include font14;
                            font-weight: 400;
                            margin-top: 8px;
                            color: $textColor3;
                            &:first-child {
                                margin-top: 0;
                            }
                            &.dis_a {
                                &:hover {
                                    cursor: text;
                                    text-decoration: none;
                                }
                            }
                        }
                    }
                }
            }
            ///==
            .product_grid_div_fiv {
                display: flex;
                flex-wrap: wrap; /* 允许换行 */
                justify-content: center;
                gap: 20px 20px;
                padding: 0 calc((25% - 20px) / 2);
                .product_item {
                    width: calc(33% - 20px);
                    display: flex;
                    flex-direction: column;
                    transition: all 0.3s;
                    border-radius: 8px;
                    .img_box {
                        border-top-left-radius: 8px;
                        border-top-right-radius: 8px;
                        overflow: hidden;
                        position: relative;
                        min-height: calc((min(84vw, 1200px) - 60px) / 8);
                        ::v-deep {
                            .skeleton_wrap {
                                z-index: 0;
                            }
                        }
                        @media (max-width: 768px) {
                            min-height: calc((100vw - 32px) / 2);
                        }
                    }

                    &:hover {
                        box-shadow: 0 15px 15px -10px rgb(0 0 0 / 15%);
                    }
                    // &.decoration_none {
                    //     &:hover {
                    //         box-shadow: none;
                    //         cursor: auto;
                    //         .text_div {
                    //             a:hover {
                    //                 cursor: text;
                    //                 text-decoration: none;
                    //             }
                    //         }
                    //     }
                    // }
                    &.dis_product {
                        box-shadow: none;
                    }
                    img {
                        width: 100%;
                        height: auto;
                        border-top-left-radius: 8px;
                        border-top-right-radius: 8px;
                        display: block;
                        position: relative;
                        z-index: 1;
                    }
                    .text_div {
                        padding: 20px;
                        color: $textColor1;
                        background-color: $bgColor3;
                        border: 1px solid #e5e5e5;
                        border-top: none;
                        flex: 1;
                        border-bottom-left-radius: 8px;
                        border-bottom-right-radius: 8px;
                        &.no_border {
                            border: none;
                        }
                        h5 {
                            @include font14;
                            font-weight: 600;
                            margin-bottom: 12px;
                        }
                        .links {
                            display: flex;
                            flex-direction: column;
                        }
                        a {
                            @include font14;
                            font-weight: 400;
                            margin-top: 8px;
                            color: $textColor3;
                            &:first-child {
                                margin-top: 0;
                            }
                            &.dis_a {
                                &:hover {
                                    cursor: text;
                                    text-decoration: none;
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}

@media (max-width: 1024px) {
    .container {
        padding: 40px 24px;
        .content_div {
            width: 100%;
        }
    }
}
@media (max-width: 768px) {
    .container {
        padding: 36px 16px;
        .content_div {
            .title {
                @include font20;
                font-weight: 600;
            }
            // .product_div {
            //     grid-template-columns: 1fr;
            // }
            .product_div {
                .product_grid_div {
                    flex-direction: column;
                    .product_item {
                        width: 100%;
                    }
                }

                .product_grid_div_fiv {
                    flex-direction: column;
                    padding: 0;
                    .product_item {
                        width: 100%;
                    }
                }
            }
        }
    }
}
</style>
