<template>
    <div class="erp_wrap">
        <div v-for="banner in contentData.banners" :key="banner.id">
            <div class="banner" v-if="screenType == banner.showtype">
                <skeleton-solutions :item="{ width: '100%', bg: '#F2F2F2', height: '100%' }"> </skeleton-solutions>
                <div class="banner_bg" :style="{ backgroundImage: `url(${banner.img})` }"></div>
                <div class="w1200">
                    <div class="crumb_list" :class="{ whiteBread: ['S75'].includes(tag_id) && !['cn', 'de', 'it', 'mx', 'es', 'fr', 'jp'].includes(website) }">
                        <bread-crumb :list="CrumbList" size="small"></bread-crumb>
                    </div>
                    <div class="banner_txt">
                        <h1 v-html="banner.title" :style="{ color: banner.title_color == 1 ? '#fff' : '#19191a' }"></h1>
                        <div class="link_btn">
                            <fs-button type="bluetext-link" :class="{ color_white: banner.title_color == 1 }" @click="GaBannerPoint(banner.button_link)">{{ banner.button_txt }}</fs-button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="right_content">
            <div class="overview" v-if="~contentData.introduce_front_show">
                <div class="w1200">
                    <h2 class="tit">{{ contentData.introduces.title }}</h2>
                    <div class="overview_cont">
                        <p class="txt" v-for="cont in contentData.introduces.content" :key="cont" v-html="cont"></p>
                    </div>
                </div>
            </div>

            <div class="architectures w1200" v-if="~contentData.topology_front_show">
                <div class="">
                    <h2 class="tit" v-if="contentData.tags.title && !hasCabinet">{{ contentData.tags.title }}</h2>
                    <solution-panorama></solution-panorama>
                </div>
                <template v-if="hasCabinet && contentData.data.feature_products2">
                    <cabinet-tag v-if="contentData.data.feature_products2 && contentData.data.feature_products2.length < 2"></cabinet-tag>
                    <muti-cabinet v-if="contentData.data.feature_products2 && contentData.data.feature_products2.length >= 2"></muti-cabinet>
                </template>
            </div>

            <div class="feature_products" v-if="contentData.data.feature_products && ~contentData.featured_front_show && !hasCabinet" :class="contentData.data.feature_products.nav.length ? '' : 'noNav_style'">
                <div class="w1200">
                    <h2 class="tit">{{ contentData.data.feature_products.title }}</h2>
                    <feature-products v-if="!hasCabinet"></feature-products>
                </div>
            </div>

            <!-- <div class="business" v-if="contentData.banner_mid">
                <div class="business_bg" :style="{ backgroundImage: `url(${mid_banner})` }"></div>
                <div class="business_cont">
                    <div class="business_txt">
                        <div class="txt_cont">
                            <h2 :style="{ color: contentData.banner_mid.title_color == 1 ? '#fff' : '#19191a' }">{{ contentData.banner_mid.title }}</h2>
                        </div>
                        <div class="href">
                            <fs-button type="blackline-link" :href="localePath({ path: contentData.banner_mid.button_link })" @click="GaLearnMorePoint(contentData.banner_mid.button_link)">{{
                                contentData.banner_mid.button_txt
                            }}</fs-button>
                        </div>
                    </div>
                </div>
            </div> -->

            <div class="advantages" v-if="~contentData.advantage_front_show">
                <div class="w1200">
                    <h2 class="tit">{{ contentData.data.advantages.title }}</h2>
                    <advantage-list></advantage-list>
                </div>
            </div>

            <div class="case_study" v-if="~contentData.featured_front_show">
                <div class="w1200">
                    <h2 class="tit">{{ contentData.data.featureCase.title }}</h2>
                    <case-study></case-study>
                </div>
            </div>

            <contact-us :formData="contentData.form" :type_id="typeId"></contact-us>
        </div>
    </div>
</template>

<script>
import SolutionPanorama from "./components/SolutionPanorama.vue"
import CabinetTag from "./components/CabinetTag.vue"
import FeatureProducts from "./components/FeatureProducts.vue"
import AdvantageList from "./components/AdvantageList.vue"
import CaseStudy from "./components/CaseStudy.vue"
import MutiCabinet from "./components/MutiCabinet.vue"
import ContactUs from "./components/ContactUs.vue"
// import Nav from "./components/Nav.vue"
import SpecialNav from "@/components/SpecialNav/SpecialNav.vue"
import SkeletonSolutions from "@/components/Skeleton/SkeletonSolutions"

import { mixin } from "../SolutionsMixin.js"

export default {
    components: {
        SolutionPanorama,
        FeatureProducts,
        AdvantageList,
        CaseStudy,
        CabinetTag,
        MutiCabinet,
        ContactUs,
        // Nav,
        SpecialNav,
        SkeletonSolutions,
    },
    mixins: [mixin],
    // props: {
    //     navData: {
    //         type: Object,
    //         default: {}
    //     },
    // },
    provide() {
        return {
            Panorama: this.contentData.tags.content,
            products: this.contentData.data.feature_products,
            advantages: this.contentData.data.advantages.content,
            CaseStudy: this.contentData.data.featureCase.content,
            cabinet: this.hasCabinet,
            cabinetData: this.contentData.data.feature_products2[0],
            MutiCabinetData: this.contentData.data.feature_products2,
            lang: this.contentData.language,
            className: "right_content",
        }
    },
    data() {
        return {
            mid_banner: this.contentData.banner_mid ? this.contentData.banner_mid.img : "",
            footer_banner: this.contentData.banner_detail ? this.contentData.banner_detail.img : "",
            swiper_bg: "https://img-en.fs.com/solution/3d-back.png",
            CrumbList: [],
            screenType: 1,
            hasCabinet: this.contentData.products_type == 1 ? false : true,
            cabineVar: {},
            isShowNavMask: true,
            typeId: "",
        }
    },
    created() {
        this.contentData.data.bread &&
            this.contentData.data.bread.forEach((item) => {
                if (item.name !== "" && item.url !== "") {
                    this.CrumbList.push(item)
                }
            })
        // 获取当前方案应用场景分类类型ID
        if (this.CrumbList && this.CrumbList.length > 0) {
            let index = this.CrumbList.length > 2 ? 1 : this.CrumbList.length - 1
            let array = this.CrumbList[index].url.split("-").pop().split(".")
            this.typeId = isNaN(Number(array[0])) ? null : array[0]
        } else {
            this.typeId = null
        }
    },
    mounted() {
        this.changBanner()
        window.addEventListener("resize", () => {
            this.changBanner()
        })
        // this.listenNavSc()
        console.log(this.pageGroup, 555)
    },
    methods: {
        listenNavSc() {
            if (this.screenWidth <= 960) {
                let navTabEl = this.$refs["NavTab"]
                let navWidth = navTabEl.scrollWidth - navTabEl.clientWidth
                navTabEl.addEventListener(
                    "scroll",
                    () => {
                        let needWidth = navWidth - navTabEl.scrollLeft
                        needWidth = Math.abs(needWidth) //允许误差在5px以内
                        if (navTabEl.scrollLeft >= navWidth || needWidth < 5) {
                            console.log("滚动到了右边")
                            this.isShowNavMask = false
                        } else {
                            this.isShowNavMask = true
                        }
                        if (navTabEl.scrollLeft == 0) {
                            console.log("滚动到了左边")
                        }
                    },
                    true
                )
            }
        },
        changBanner() {
            if (window.document.body.clientWidth > 680 && (this.screenWidth <= 1024 || window.document.body.clientWidth <= 1024)) {
                this.screenType = 3
                this.swiper_bg = "https://img-en.fs.com/solution/M-back.png"
                this.mid_banner = this.contentData.banner_mid ? this.contentData.banner_mid.pad_imgUrl : ""
                this.footer_banner = this.contentData.banner_detail ? this.contentData.banner_detail.pad_imgUrl : ""
            } else if (this.screenWidth < 680 || window.document.body.clientWidth < 680) {
                this.screenType = 2
                this.swiper_bg = "https://img-en.fs.com/solution/Pad-back.png"
                this.mid_banner = this.contentData.banner_mid ? this.contentData.banner_mid.m_imgUrl : ""
                this.footer_banner = this.contentData.banner_detail ? this.contentData.banner_detail.m_imgUrl : ""
            } else {
                this.screenType = 1
                this.swiper_bg = "https://img-en.fs.com/solution/3d-back.png"
                this.mid_banner = this.contentData.banner_mid ? this.contentData.banner_mid.img : ""
                this.footer_banner = this.contentData.banner_detail ? this.contentData.banner_detail.img : ""
            }
        },
        GaBannerPoint(url) {
            window.dataLayer &&
                window.dataLayer.push({
                    event: "uaEvent",
                    eventCategory: this.pageGroup,
                    eventAction: "banner",
                    eventLabel: `https://www.fs.com${url}`,
                    nonInteraction: false,
                })
            this.$router.push(this.localePath({ path: `${url}` }))
        },
        GaLearnMorePoint(url) {
            window.dataLayer &&
                window.dataLayer.push({
                    event: "uaEvent",
                    eventCategory: this.pageGroup,
                    eventAction: "Featured Products",
                    eventLabel: `learn more_https://www.fs.com${url}`,
                    nonInteraction: false,
                })
        },
        GaSupportsPoint(url) {
            const newUrl = url.indexOf("http") === -1 ? `https://www.fs.com${url}` : url
            window.dataLayer &&
                window.dataLayer.push({
                    event: "uaEvent",
                    eventCategory: this.pageGroup,
                    eventAction: "Supports",
                    eventLabel: newUrl,
                    nonInteraction: false,
                })
        },
    },
}
</script>

<style lang="scss" scoped>
.w1200 {
    width: 84vw;
    max-width: 1200px;
    margin: 0 auto;
    overflow: hidden;

    @media (max-width: 1200px) {
        width: 100%;
        padding: 0 24px;
    }
    @media (max-width: 768px) {
        width: 100%;
        padding: 0 16px;
    }
}

.tit {
    @include font20;
    font-weight: 600;
    max-width: 976px;
    margin: 0 auto;
    text-align: center;
    color: $textColor1;
    @include sourceSansPro;
}

.txt {
    @include font16;
    color: $textColor1;
}

.sub_txt {
    @include font14;
    color: $textColor3;
}

img {
    display: block;
    max-width: 100%;
}

.erp_wrap {
    .erp_contain {
        display: flex;
        position: relative;

        // > div {
        //     flex: 1;
        // }
        @media (max-width: 1024px) {
            width: calc(100% - 48px);
        }

        @media (max-width: 768px) {
            flex-direction: column;
            padding-top: 48px;
            width: 100%;

            .left_content {
                position: absolute;
                top: 0;
                width: 100%;
                z-index: 1;
            }
        }

        .right_content {
            overflow: hidden;
            // flex: 1;
        }
    }

    .banner {
        height: 360px;
        position: relative;
        ::v-deep {
            .skeleton_wrap {
                width: 99vw;
                left: 0;
            }
        }
        .banner_bg {
            position: absolute;
            width: 100%;
            height: 360px;
            background-position: center;
            background-repeat: no-repeat;
            background-size: cover;
            left: 0;
            z-index: -1;
        }
        .w1200 {
            height: 100%;
            position: relative;
            padding: 0 48px;
            .banner_txt {
                position: absolute;
                top: 50%;
                transform: translateY(-50%);
                width: 750px;
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: flex-start;

                h1 {
                    width: 620px;
                    @include font32;
                    font-weight: 600;
                    color: $textColor1;
                    @include sourceSansPro;
                }

                ::v-deep .link_btn {
                    display: flex;
                    margin-top: 16px;
                    .box {
                        display: flex;
                        align-items: center;
                        color: $textColor1;
                        .iconfont_right {
                            color: $textColor1;
                        }
                    }
                    a {
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        @include font14;
                        min-width: 195px;
                        height: 38px;
                        padding: 0 30px;
                        border-radius: 3px;
                        text-decoration: none;
                        transition: all 0.3s;
                        cursor: pointer;

                        i {
                            margin-right: 4px;
                        }
                    }

                    .video_btn {
                        color: $textColor5;
                        border: 1px solid $borderColor3;
                        margin-right: 12px;
                    }
                }

                .color_white {
                    ::v-deep .box {
                        color: $textColor7;
                        .iconfont_right {
                            color: $textColor7;
                        }
                    }
                }

                @media (max-width: 1024px) {
                    width: 640px;
                    h1 {
                        width: 460px;
                    }
                }
            }
        }
    }

    ::v-deep .crumb_list {
        padding-top: 14px;
        &.whiteBread {
            .bread-item {
                &:after {
                    color: $textColor7;
                    opacity: 0.7;
                }
                a {
                    color: $textColor7;
                    opacity: 0.7;
                }
                &:last-child {
                    > a {
                        color: $textColor7 !important;
                        opacity: 1;
                    }
                }
            }
        }
        @media (max-width: 1024px) {
            .bread-crumb {
                display: flex;
            }
        }
    }

    .nav {
        position: sticky;
        border-bottom: 1px solid $borderColor2;
        background-color: #fff;
        top: 0;
        z-index: 11;

        .nav_cont {
            position: relative;
            height: 56px;

            ul {
                position: relative;
                display: flex;
                height: 100%;
                overflow-x: scroll;
                margin-left: -24px;

                li {
                    padding: 0 24px;
                    transition: all 0.5s;

                    a {
                        position: relative;
                        display: block;
                        @include font16;
                        color: $textColor3;
                        // min-width: 70px;
                        height: 100%;
                        text-decoration: none;
                        padding: 17px 0;
                        white-space: nowrap;

                        .border-line {
                            @include rowLine;
                        }
                    }

                    .active {
                        color: $textColor1;

                        // font-weight: 600;
                        .border-line {
                            opacity: 1;
                        }
                    }
                }

                &::-webkit-scrollbar {
                    display: none;
                }
            }

            i {
                position: absolute;
                display: block;
                content: "";
                width: 40px;
                height: 56px;
                background: linear-gradient(90deg, rgba(255, 255, 255, 0.14) 0%, rgba(255, 255, 255, 0.66) 43%, #ffffff 100%);
                right: 0;
                bottom: 0;
            }
        }
    }

    .overview {
        padding-top: 40px;
        text-align: center;
        .w1200 {
            border-bottom: 1px solid $borderColor2;
            > div {
                padding-bottom: 40px;
            }
        }
        @media (max-width: 1024px) {
            padding: 40px 24px 0;
            .w1200 {
                padding: 0;
            }
        }
        h2 {
            margin-bottom: 12px;
            text-align: center;
        }

        .overview_cont {
            max-width: 976px;
            margin: 0 auto;

            p {
                @include font14;
                color: $textColor3;
                margin-top: 12px;
                max-width: 976px;
                margin: 0 auto;

                &:not(:first-child) {
                    margin-top: 12px;
                }
            }
        }
    }

    .cab_style {
        padding-bottom: 56px;
    }

    .architectures {
        padding-top: 40px;
        padding-bottom: 40px;
        background: center/cover no-repeat;
        /* border-bottom: 1px solid $borderColor2; */

        h2 {
            /* max-width: 960px; */
            margin: 0 auto 24px;
            text-align: center;
        }
    }

    .feature_products {
        padding-top: 40px;
        padding-bottom: 0;
        /* border-bottom: 1px solid $borderColor2; */
        background: #fafafb;

        @media (max-width: 768px) {
            border-bottom: none;
        }
    }

    .noNav_style {
        padding-bottom: 40px;

        @media (max-width: 1024px) {
            padding-bottom: 40px;
        }
    }

    .business {
        position: relative;
        height: 180px;
        margin-top: 40px;
        padding-bottom: 40px;
        box-sizing: content-box;
        border-bottom: 1px solid $borderColor2;

        @media (max-width: 768px) {
            height: 232px;
            padding-bottom: 0;
            border-bottom: none;
        }

        .business_bg {
            position: absolute;
            width: 100%;
            height: 180px;
            background: center/cover no-repeat;
            z-index: -1;

            @media (max-width: 768px) {
                height: 232px;
            }
        }

        .business_cont {
            .business_txt {
                height: 180px;
                padding-left: 32px;
                display: flex;
                flex-direction: column;
                justify-content: center;

                .txt_cont {
                    max-width: 700px;

                    h2 {
                        @include font18;
                        font-weight: 600;
                        color: $textColor1;
                        margin-bottom: 16px;
                        @include sourceSansPro;
                    }

                    p {
                        @include font14;
                        color: $textColor1;
                        margin: 8px 0 24px;
                    }
                }

                .href {
                    display: flex;
                    justify-content: flex-start;

                    a {
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        @include font14;
                        // min-width: 195px;
                        height: 38px;
                        border-radius: 3px;
                        padding: 0 30px;
                        color: $textColor1;
                        border: 1px solid #19191a;
                        text-decoration: none;
                        cursor: pointer;
                        transition: all 0.3s;

                        &:hover {
                            color: $textColor3;
                            border-color: #707070;
                        }
                    }
                }
            }

            @media (max-width: 1024px) {
                width: 100%;
                padding-left: 0;

                .business_txt {
                    max-width: 600px;
                }
            }
        }
    }

    .advantages {
        padding: 40px 0;
        overflow: hidden;
        /* border-bottom: 1px solid $borderColor2; */
        @media (max-width: 1024px) {
            padding: 40px 0;
        }
    }

    .case_study {
        padding: 40px 0;
        overflow: hidden;
        background: #fafafb;
        .w1200 {
            overflow: visible;
        }
    }

    .help {
        position: relative;
        height: 240px;

        .help_bg {
            display: flex;
            align-items: center;
            justify-content: center;
            position: absolute;
            width: 100%;
            height: 240px;
            background-position: center;
            background-repeat: no-repeat;
            background-size: cover;
            z-index: -1;

            > div {
                height: 100%;
            }

            .left_bg {
                width: 400px;
                @include bgcover("https://img-en.fs.com/solution/common/bg-left.png");
            }

            .center_bg {
                // flex: 1;
                background-color: #f4f7fc;
            }

            .right_bg {
                width: 400px;
                @include bgcover("https://img-en.fs.com/solution/common/bg-right.png");
            }
        }

        .w1200 {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100%;

            > div {
                width: calc(100% / 3);
            }

            .questions {
                position: relative;
                display: flex;
                flex-direction: column;
                align-items: flex-start;

                h2 {
                    @include font24;
                    font-weight: 600;
                    color: $textColor1;
                    max-width: 220px;
                    margin-bottom: 20px;
                    @include sourceSansPro;
                }

                .link_btn {
                    display: flex;
                    align-items: center;
                    justify-content: center;

                    a {
                        @include font14;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        min-width: 195px;
                        height: 38px;
                        border-radius: 3px;
                        padding: 0 30px;
                        text-decoration: none;
                        color: $textColor1;
                        border: 1px solid #19191a;
                        transition: all 0.3s;
                    }

                    &:hover {
                        a {
                            color: $textColor3;
                            border-color: #707070;
                        }
                    }
                }

                .line {
                    position: absolute;
                    display: block;
                    width: 1px;
                    height: 88px;
                    background-color: #e5eaf1;
                    right: 0;
                    top: 50%;
                    margin-top: -44px;
                }
            }

            .entrance {
                display: flex;
                justify-content: center;
                align-items: center;

                .entranc_cont {
                    display: flex;
                    flex-direction: column;
                    align-items: flex-start;
                    justify-content: center;

                    .img_box {
                        width: 36px;
                        height: 36px;
                        background: center/cover no-repeat;
                    }

                    .link_btn {
                        margin: 14px 0 4px;
                    }

                    p {
                        @include font13;
                        color: $textColor3;
                    }
                }
            }

            @media (max-width: 1200px) {
                .questions {
                    padding-left: 48px;
                }
            }
        }
    }
}

/* @media (max-width: 960px) {
    .erp_wrap {
        .banner {
            .banner_txt {
                padding-left: 40px;
            }
        }

        .nav {
            .nav_cont {
                i {
                    display: block;
                }
            }
        }

        .help {
            .w1200 {
                .questions {
                    padding-left: 0;
                }
            }
        }
    }
} */

@media (max-width: 768px) {
    .erp_wrap {
        .help {
            height: auto;

            .help_bg {
                height: 100%;
            }

            .w1200 {
                flex-wrap: wrap;
                padding: 30px 16px;

                > div {
                    width: 100%;
                }

                .questions {
                    justify-content: center;
                    align-items: center;
                    padding: 0 0 29px;
                    margin-bottom: 16px;

                    h2 {
                        max-width: 100%;
                        text-align: center;

                        @media (max-width: 768px) {
                            @include font16;
                        }
                    }

                    .line {
                        width: 100%;
                        height: 1px;
                        top: 100%;
                        margin-top: 0;
                    }
                }

                .entrance {
                    .entranc_cont {
                        width: 100%;
                        align-items: center;

                        .img_box {
                            width: 30px;
                            height: 30px;
                        }
                    }

                    padding: 16px 0;
                    align-items: center;
                    margin-right: 0 !important;
                }
            }
        }
    }
}

@media (max-width: 768px) {
    .w1200 {
        width: 100%;
    }

    .tit {
        @include font20;
        padding: 0 16px;
    }

    .txt {
        @include font14;
    }

    .erp_wrap {
        display: flex;
        flex-direction: column;

        .banner {
            height: 440px;

            .banner_bg {
                height: 440px;
            }
            .w1200 {
                padding: 0 24px;
                .banner_txt {
                    padding-top: 56px;
                    top: 0;
                    transform: translateY(0);
                    justify-content: center;
                    width: calc(100% - 48px);
                    h1 {
                        @include font24;
                        max-width: 100%;
                    }

                    .link_btn {
                        a {
                            @include font14;
                            min-width: 149px;
                            padding: 0 12px;
                        }

                        .video_btn {
                            margin-right: 8px;
                        }
                    }
                }
            }
        }

        .crumb_list {
            display: none;
        }

        .nav {
            order: -1;
            top: 48px;
            z-index: 20;
            margin-top: 5px;

            .nav_cont {
                padding-left: 20px;

                ul {
                    li {
                        padding: 0 16px;
                    }
                }
            }
        }

        .overview {
            padding: 36px 16px 24px !important;
            .w1200 {
                padding-bottom: 0;
                > div {
                    padding-bottom: 24px;
                }
            }
        }

        .architectures {
            padding: 0 16px 36px;
            border-bottom: none;
        }

        .overview {
            padding: 36px 16px 0;
            border-bottom: none;

            h2 {
                margin-bottom: 12px;
                padding: 0;
            }

            .overview_cont {
                p {
                    &:last-child {
                        margin-top: 12px;
                    }
                }
            }
        }

        .architectures {
            h2 {
                margin-bottom: 24px;
                padding: 0;
            }
        }

        .advantages {
            padding: 36px 0 36px;
            border-bottom: none;
        }

        .feature_products {
            padding-top: 36px;
        }

        .business {
            margin-top: 0;

            .business_cont {
                padding-left: 16px;

                .business_txt {
                    max-width: 264px;
                    padding-left: 0;
                    height: 232px;

                    .txt_cont {
                        h2 {
                            @include font18;
                            margin-bottom: 16px;
                            @include sourceSansPro;
                        }

                        p {
                            @include font14;
                            margin: 8px 0 16px 0;
                        }
                    }

                    .href {
                        // height: 32px;
                        a {
                            min-width: 149px;
                            // height: 32px;
                            padding: 0 12px;
                            @include font13;
                        }
                    }
                }
            }
        }

        .case_study {
            padding: 36px 0 36px;
        }

        .help {
            .w1200 {
                .questions {
                    .link_btn {
                        a {
                            @include font13;
                            min-width: 149px;
                            height: 32px;
                            padding: 0 12px;
                        }
                    }
                }
            }
        }
    }
}
</style>
