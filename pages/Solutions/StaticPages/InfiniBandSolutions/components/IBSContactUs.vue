<template>
    <div class="bg_div" :style="{ backgroundImage: `url(${backgroundImage})` }">
        <div class="main_wrap">
            <h2>{{ $c("solutions.InfiniBandSolutions.contactUs.title") }}</h2>
            <p class="desc">{{ $c("solutions.InfiniBandSolutions.contactUs.desc") }}</p>
            <div class="solution_form">
                <!-- <div class="bg">
                    <img :src="iconImage" alt="" />
                </div> -->
                <div class="right">
                    <template v-if="success">
                        <div class="success-box">
                            <!-- <span class="iconfont close-btn" @click="clear">&#xf30a;</span> -->
                            <div class="cont">
                                <img src="https://resource.fs.com/mall/generalImg/202305241737199fpizf.svg" alt="" />
                                <div class="success-tit">{{ $c("single.ContactSales.SubmittedSuccessfully") }}</div>
                                <div class="success-des" v-html="subStr($c('form.form.success.txt1'))"></div>
                            </div>
                        </div>
                    </template>
                    <template v-else>
                        <div>
                            <form @submit.prevent="submitForm">
                                <div class="inpbox inpbox01" :class="{ inpReverse: isCn }">
                                    <div class="lt">
                                        <p class="txt">
                                            {{ $c("form.form.first_name") + " *" }}
                                        </p>
                                        <input v-model.trim="form.entry_firstname" aria-label="First name" type="text" @blur="inputCheck('entry_firstname')" />
                                        <validate-error :error="errors.entry_firstname"></validate-error>
                                    </div>
                                    <div class="rt">
                                        <p class="txt">
                                            {{ $c("form.form.last_name") + " *" }}
                                        </p>
                                        <input v-model.trim="form.entry_lastname" type="text" aria-label="last name" @blur="inputCheck('entry_lastname')" />
                                        <validate-error :error="errors.entry_lastname"></validate-error>
                                    </div>
                                </div>
                                <div class="inpbox01">
                                    <p class="txt">{{ isCn ? "邮箱" : $c("form.form.email_business") }}{{ isCn ? "" : " *" }}</p>
                                    <input v-model.trim="form.email_address" type="text" aria-label="email ddress" @blur="inputCheck('email_address')" />
                                    <validate-error :error="errors.email_address"></validate-error>
                                </div>
                                <div class="inpbox inpbox01">
                                    <div class="lt">
                                        <p class="txt">{{ $c("form.form.country") }} *</p>
                                        <select-country v-model="form.countries_id" @change="selectCountry"></select-country>
                                        <validate-error :error="errors.countries_id_error" @click.native="changeSite(cnRedirect)" />
                                    </div>
                                    <div class="rt">
                                        <p v-if="isCn" class="txt">{{ $c("form.form.phone_business") }} *</p>
                                        <p v-else class="txt">{{ $c("form.form.phone") }}</p>
                                        <tel-code-new ref="telCode" :isEditCode="true" :code.sync="telPrefix" v-model="form.entry_telephone" @error="phoneError"></tel-code-new>
                                        <validate-error :error="errors.entry_telephone" />
                                    </div>
                                </div>
                                <div class="inpbox inpbox01">
                                    <div class="lt">
                                        <p class="txt">{{ $c("form.form.businessCategory") }} *</p>
                                        <fs-select :options="formData.business_category" v-model="form.business_category" @change="handleSelectClick" :other="false" :isTopPosition="false"></fs-select>
                                    </div>
                                    <div class="rt">
                                        <p class="txt">{{ $c("form.form.applicationScenario") }} *</p>
                                        <fs-select :options="formData.application_scenario" v-model="form.application_scenario" @change="handleSelectClick" :other="false" :isTopPosition="false"></fs-select>
                                    </div>
                                </div>
                                <div class="inpbox01 last_inpbox">
                                    <p class="txt">{{ $c("form.form.whatIsYourProject") }} *</p>
                                    <textarea v-model.trim="form.comments" maxlength="5000" :placeholder="$c('form.form.brieflyOutlineYour')" aria-label="Details" @blur="inputCheck('comments')"></textarea>
                                    <div :class="errors.comments ? 'input_item_flex' : 'input_item_number'">
                                        <validate-error :error="errors.comments"></validate-error>
                                        <span class="textarea_num">
                                            <em :class="{ active: form.comments.length === 5000 }">{{ form.comments.length }}</em
                                            >/5000
                                        </span>
                                    </div>
                                </div>
                                <!-- <div
                                    @click="handleGa"
                                    class="form_agreement"
                                    v-html="
                                        $c('single.ContactSales.submitTip')
                                            .replace('XXXX1', localePath({ name: 'privacy-policy' }))
                                            .replace('XXXX2', localePath({ name: 'terms-of-use' }))
                                    "></div> -->
                                <div class="policy_box">
                                    <input v-model="form.isAgreePolicy" @change="blurInput('isAgreePolicy')" type="checkbox" class="chk" />
                                    <div
                                        @click="handleGa"
                                        class="form_agreement"
                                        v-html="
                                            $c('form.validate.aggree_policy_new')
                                                .replace('AAAA', localePath({ name: 'privacy-notice' }))
                                                .replace('BBBB', localePath({ name: 'terms-of-use' }))
                                        "></div>
                                </div>
                                <validate-error :error="errors.isAgreePolicy"></validate-error>
                                <div class="btn">
                                    <fs-button html-type="submit" id="service_chat" :text="$c('form.form.submit')" :loading="loading"></fs-button>
                                </div>
                            </form>
                        </div>
                    </template>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import { email_valdate, cn_all_phone } from "@/constants/validate.js"
import { isValidPhoneNumber } from "@/util/libphonenumber.js"
import SelectCountry from "@/components/SelectCountry/SelectCountry"
import TelCodeNew from "@/components/TelCode/TelCodeNew.vue"
import FsSelect from "@/components/FsSelect/FsSelect"
import ValidateError from "@/components/ValidateError/ValidateError.vue"
import FsButton from "@/components/FsButton/FsButton.vue"
import { mapState, mapGetters } from "vuex"
import { getRecaptchaToken } from "@/util/grecaptchaHost"
export default {
    components: {
        ValidateError,
        FsButton,
        SelectCountry,
        TelCodeNew,
        FsSelect,
    },
    props: {
        type_id: {
            type: String | Number,
            default: "",
        },
    },
    data() {
        return {
            // iconImage: "https://resource.fs.com/mall/generalImg/20240417185522ambqdh.png",
            backgroundImage: "https://resource.fs.com/mall/generalImg/20240417185522yrv0ir.jpg",
            formData: this.$c("form.form.option"),
            telPrefix: "",
            form: {
                entry_firstname: this.$store.state.userInfo.userInfo && this.$store.state.userInfo.userInfo.customers_firstname ? this.$store.state.userInfo.userInfo.customers_firstname : "",
                entry_lastname: this.$store.state.userInfo.userInfo && this.$store.state.userInfo.userInfo.customers_lastname ? this.$store.state.userInfo.userInfo.customers_lastname : "",
                email_address: this.$store.state.userInfo.userInfo && this.$store.state.userInfo.userInfo.customers_email_address ? this.$store.state.userInfo.userInfo.customers_email_address : "",
                countries_id: "",
                entry_telephone: "",
                business_category: 99, // 默认选中最后的Others
                application_scenario: this.type_id || 99, // 默认选择当前方案应用场景分类类型
                comments: "",
                resource_page: "40",
                isAgreePolicy: false,
            },
            errors: {
                entry_firstname: "",
                entry_lastname: "",
                email_address: "",
                countries_id_error: "",
                entry_telephone: "",
                business_category_error: "",
                application_scenario_error: "",
                comments: "",
                isAgreePolicy: "",
            },
            loading: false,
            success: false,
        }
    },
    computed: {
        ...mapGetters({
            isCn: "webSiteInfo/isCn",
        }),

        ...mapState({
            countries_id: (state) => state.webSiteInfo.countries_id,
        }),
    },
    created() {
        this.form.countries_id = `${this.countries_id}`
    },
    methods: {
        subStr(str) {
            return str.replace("%XXXX%", `<a class="case_btn" href="${this.localePath({ path: `/support_ticket` })}">`).replace("%ZZZZ%", "</a>")
        },
        handleGa({ target: { href } }) {
            if (href) {
                const res = href.indexOf("privacy_policy") != -1
                const res2 = href.indexOf("/terms_of_use.html") != -1
                if (res) {
                    this.gaEvent("privacy_policy")
                } else if (res2) {
                    this.gaEvent("terms_of_use")
                }
            }
        },
        gaEvent(eventLabel) {
            window.dataLayer &&
                window.dataLayer.push({
                    event: "uaEvent",
                    eventCategory: "Solution Page",
                    eventAction: "Contact_Module",
                    eventLabel,
                    nonInteraction: false,
                })
        },
        blurInput() {
            this.errors.isAgreePolicy = this.form.isAgreePolicy ? "" : this.$c("form.form.errors.check2_error")
        },

        inputCheck(attr) {
            let msg = ""
            const val = this.form[attr] && this.form[attr]?.replace(/\s+/g, "")

            if (attr === "entry_firstname") {
                if (!val) {
                    msg = this.$c("form.form.errors.entry_firstname_error")
                } else if (val.length > 40) {
                    msg = this.$c("form.validate.first_name.first_name_max")
                }
            }
            if (attr === "entry_lastname") {
                if (!val) {
                    msg = this.$c("form.form.errors.entry_lastname_error")
                } else if (val.length > 40) {
                    msg = this.$c("form.validate.last_name.last_name_max")
                }
            }
            if (attr === "email_address") {
                if (!this.isCn) {
                    if (!val) {
                        msg = this.$c("form.form.errors.email_business_error")
                    } else if (!email_valdate.test(val)) {
                        msg = this.$c("form.form.errors.email_business_error01")
                    }
                }
            }
            if (attr === "countries_id") {
                if (!this.isCn && this.form.countries_id == 44) {
                    msg = this.$c("form.form.CNlimitTip")
                }
            }
            if (attr === "entry_telephone") {
                if (this.isCn) {
                    if (!val) {
                        msg = this.$c("form.form.errors.entry_telephone_error")
                    } else if (this.form.entry_telephone.replace(/^\s+|\s+$/g, "") && !cn_all_phone.test(this.form.entry_telephone)) {
                        msg = this.$c("form.form.errors.entry_telephone_error01")
                    }
                } else {
                    if (this.form.entry_telephone.replace(/^\s+|\s+$/g, "") && !isValidPhoneNumber(this.telPrefix + this.form.entry_telephone)) {
                        msg = this.$c("pages.NetTermsApplication.validate.phone.phone_validate")
                    }
                }
            }
            if (attr === "comments") {
                if (!val) {
                    msg = this.$c("single.ContactSales.fieldRequired")
                }
            }
            this.errors[attr] = msg

            return !!msg
        },
        selectCountry(country) {
            this.form.countries_id = `${country.countries_id}`
            if (!this.isCn && this.form.countries_id == 44) {
                this.errors.countries_id_error = this.$c("form.form.CNlimitTip")
            } else {
                this.errors.countries_id_error = ""
            }
        },
        changeSite(v) {
            this.updateSiteInfo({
                ...v,
                callback: () => {},
            })
        },
        phoneError(errorMsg) {
            if (this.form.entry_telephone.replace(/^\s+|\s+$/g, "")) {
                this.errors.entry_telephone = errorMsg
            }
        },
        handleSelectClick() {
            // this.buriedPointWrapper(`Industry Drop-Down`)
        },
        submitVerify() {
            console.log("this.website", this.isCn)
            let arr = []
            let attr = ["entry_firstname", "entry_lastname", "email_address", "countries_id", "entry_telephone", "comments"]
            attr.map((item) => {
                let f = this.inputCheck(item)
                // 打印每个字段的校验结果和错误信息
                console.log(`[校验] ${item}:`, f, this.errors[item] || this.errors[`${item}_error`])
                arr.push(f)
            })
            this.blurInput()
            if (this.errors.isAgreePolicy) {
                arr.push(true)
            }
            return arr.includes(true)
        },
        clear() {
            this.form.comments = ""
            this.success = false
        },
        burryPoint(eventLabel) {
            window.dataLayer &&
                window.dataLayer.push({
                    event: "uaEvent",
                    eventCategory: "Solution Page",
                    eventAction: "Contact_Module",
                    eventLabel: eventLabel,
                    nonInteraction: false,
                })
        },
        async submitForm() {
            try {
                const flag = this.submitVerify()
                console.log("flag===", flag)
                if (flag) {
                    return
                }
                this.loading = true
                const data = JSON.parse(JSON.stringify(this.form))
                data.entry_telephone = `${this.telPrefix.replace("+", "")} ${this.form.entry_telephone}`
                data.website_link = location.href
                const { recaptchaTp, headers = {}, errorMsg = "grecaptcha_error" } = await getRecaptchaToken()
                if (!recaptchaTp) {
                    this.loading = false
                    this.$message.error(this.$c(`pages.Login.regist.${errorMsg}`))
                    return
                }
                const res = await this.$axios.post("/api/contact_sales", data, { headers })
                console.log(res)
                if (res.code === 200) {
                    this.burryPoint(`Contact_Submit_${window.location.href}`)
                    this.success = true
                    this.$bdRequest({
                        conversionTypes: [
                            {
                                logidUrl: location.href,
                                newType: 18,
                            },
                        ],
                    })
                }
                this.loading = false
            } catch (err) {
                console.log(err)
                this.burryPoint("Contact_Submit_False")
                this.loading = false
            }
        },
    },
}
</script>

<style lang="scss" scoped>
::v-deep .select-country {
    background-color: $bgColor3;
}
::v-deep .tel-code-new {
    background-color: $bgColor3;
}
.bg_div {
    padding: 40px 48px;
    background-position: center;
    background-repeat: no-repeat;
    background-size: cover;
    .main_wrap {
        @include width1200;
        h2 {
            @include font20;
            font-weight: 600;
            text-align: center;
            margin-bottom: 12px;
            color: $textColor1;
        }
        .desc {
            @include font14;
            font-weight: 400;
            margin-bottom: 12px;
            text-align: center;
            color: $textColor3;
        }
        .solution_form {
            display: flex;
            align-items: center;
            padding: 0 48px;
            // .bg {
            //     width: 42%;
            //     flex-shrink: 0;
            //     margin-right: 56px;
            //     img {
            //         width: 100%;
            //         height: auto;
            //     }
            // }
            .right {
                flex: 1;
                .success-box {
                    // width: 100%;
                    // height: 100%;
                    max-width: 900px;
                    margin: 0 auto;
                    position: relative;
                    display: flex;
                    align-items: center;
                    text-align: center;
                    // .close-btn {
                    //     display: flex;
                    //     justify-content: center;
                    //     align-items: center;
                    //     width: 32px;
                    //     height: 32px;
                    //     position: absolute;
                    //     right: 0;
                    //     top: 0;
                    //     font-size: 20px;
                    //     color: $textColor3;
                    //     border-radius: 3px;
                    //     cursor: pointer;
                    //     &:hover {
                    //         color: $textColor1;
                    //         background: rgba($color: #19191a, $alpha: 0.04);
                    //     }
                    // }
                    .cont {
                        max-width: 600px;
                        margin: 0 auto;
                        padding: 130px 0;
                        @media (max-width: 768px) {
                            max-width: 100%;
                            padding: 100px 0;
                        }
                        img {
                            display: block;
                            width: 50px;
                            height: auto;
                            margin: 0 auto;
                        }
                        .success-tit {
                            @include font20;
                            font-weight: 600;
                            color: $textColor1;
                            margin: 20px 0 12px 0;
                        }
                        .success-des {
                            @include font14;
                            font-weight: 400;
                            color: $textColor3;
                        }
                    }
                }
                form {
                    max-width: 900px;
                    margin: 0 auto;
                    .inpbox {
                        display: flex;
                        justify-content: space-between;
                        margin: 0 0 16px 0;
                        > div {
                            width: calc(50% - 10px);
                        }
                    }
                    .inpbox01 {
                        margin-bottom: 16px;
                        &.last_inpbox {
                            margin: 0;
                        }
                        .txt {
                            @include font12;
                            font-weight: 400;
                            color: $textColor1;
                            margin-bottom: 4px;
                        }
                        input {
                            background-color: $bgColor3;
                        }
                        textarea {
                            background-color: $bgColor3;
                            height: 120px;
                        }
                        .input_item_flex {
                            display: flex;
                            justify-content: space-between;
                            align-items: flex-start;
                        }
                        .input_item_number {
                            display: flex;
                            justify-content: flex-end;
                            width: 100%;
                        }
                        .textarea_num {
                            color: $textColor3;
                            @include font12;
                            font-weight: 400;
                            padding-top: 4px;
                            em {
                                font-style: normal;
                                &.active {
                                    color: $textColor4;
                                }
                            }
                        }
                        &.inpReverse {
                            flex-direction: row-reverse;
                        }
                    }
                    .form_agreement {
                        @include font12;
                        // margin: 16px 0 20px;
                        color: $textColor3;
                    }
                    .btn {
                        .fs-button {
                            border-radius: 4px;
                        }
                        margin-top: 28px;
                    }
                }
            }
        }
    }
}

.policy_box {
    margin-top: 16px;
    display: flex;
    .chk {
        margin-top: 4px;
        margin-right: 8px;
        width: 14px;
        height: 14px;
        font-size: 14px;
    }
}

@media (max-width: 1024px) {
    .bg_div {
        padding: 40px 24px;
        .main_wrap {
            width: 100%;
            .solution_form {
                padding: 0;
                // .bg {
                //     margin-right: 24px;
                // }
            }
        }
    }
}

@media (max-width: 768px) {
    .bg_div {
        padding: 36px 16px;
        .main_wrap {
            h2 {
                @include font20;
                font-weight: 600;
            }
            .solution_form {
                // .bg {
                //     display: none;
                // }
                .right {
                    width: 100%;
                    .success-box {
                        .cont {
                            .success-tit {
                                @include font16;
                                margin: 20px 0 8px 0;
                            }
                        }
                    }
                    form {
                        width: 100%;
                        .inpbox {
                            flex-direction: column;
                            .lt {
                                margin: 0 0 16px 0;
                            }
                            > div {
                                width: 100%;
                            }
                            &.inpReverse {
                                flex-direction: column-reverse;
                                .lt {
                                    margin: 0;
                                }
                                .rt {
                                    margin: 0 0 16px 0;
                                }
                            }
                        }
                        .fs-button {
                            width: 100%;
                        }
                    }
                }
            }
        }
    }
}
</style>
