<template>
    <div class="container">
        <div class="content_div">
            <h2 class="title">{{ products.title }}</h2>
            <!-- <div class="nav_div">
                <account-tabs :tabList="products.tabTitList" v-model="navActiveIndex" @changeTab="changeTab"></account-tabs>
            </div> -->
            <!-- <div class="product_div" :class="{ hide_product_div: navActiveIndex === 1 && !isMobile }">
                <div class="product_item" v-for="(item, index) in products.list[navActiveIndex]" :key="index">
                    <img :src="productImage[navActiveIndex][index]" alt="" />
                    <div class="text_div">
                        <h5>{{ item.title }}</h5>
                        <p v-for="(text, descIndex) in item.descTitle" :key="descIndex" @click="productClick(productUrl[navActiveIndex][index][descIndex])">{{ text }}</p>
                    </div>
                </div>
            </div> -->
            <div class="product_div" v-for="(listItem, listIndex) in products.list" :key="listIndex">
                <p class="list_title" :class="{ first_list_title: listIndex === 0 }">{{ products.tabTitList[listIndex] }}</p>
                <div class="product_grid_div">
                    <!-- :class="{ decoration_none: productUrl[listIndex][index].length === 0 }" -->
                    <div class="product_item" v-for="(item, index) in listItem" :key="index" :class="{ dis_product: disabledProduct(productUrl[listIndex][index]) || productUrl[listIndex][index].length === 0 }">
                        <div class="img_box">
                            <skeleton-solutions :item="{ width: '100%', bg: '#F2F2F2', height: '100%' }"> </skeleton-solutions>

                            <img :src="productImage[listIndex][index]" alt="" />
                        </div>
                        <div class="text_div">
                            <h5>{{ item.title }}</h5>
                            <p
                                v-for="(text, descIndex) in item.descTitle"
                                :key="descIndex"
                                @click="productClick(productUrl[listIndex][index][descIndex])"
                                :class="{ dis_p: disabledItem(productUrl[listIndex][index][descIndex]) }">
                                {{ text }}
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- <div class="swiper_content" :class="{ show_swiper_div: navActiveIndex === 1 && !isMobile }">
            <div class="swiper_div">
                <swiper :options="swiperOption">
                    <swiper-slide v-for="(item, index) in products.list[navActiveIndex]" :key="index">
                        <div class="product_item swiper_item">
                            <img :src="productImage[navActiveIndex][index]" alt="" />
                            <div class="text_div">
                                <h5>{{ item.title }}</h5>
                                <p v-for="(text, descIndex) in item.descTitle" :key="descIndex" @click="productClick(productUrl[navActiveIndex][index][descIndex])">{{ text }}</p>
                            </div>
                        </div>
                    </swiper-slide>
                    <div class="swiper-pagination" slot="pagination"></div>
                </swiper>
                <div class="swiper-button-prev swiper-btn iconfont icon" slot="button-prev">&#xe702;</div>
                <div class="swiper-button-next swiper-btn iconfont icon" slot="button-next">&#xe703;</div>
            </div>
        </div> -->
    </div>
</template>

<script>
import SkeletonSolutions from "@/components/Skeleton/SkeletonSolutions"

// import { Swiper, SwiperSlide } from "vue-awesome-swiper"
// import AccountTabs from "@/components/AccountTabs/AccountTabs.vue"
import { mapState } from "vuex"

export default {
    components: {
        // AccountTabs,
        // Swiper,
        // SwiperSlide,
        SkeletonSolutions,
    },
    props: {
        products: {
            type: Object,
            default: () => {},
        },
    },
    computed: {
        ...mapState({
            website: (state) => state.webSiteInfo.website,
        }),
        // isMobile() {
        //     if (this.$store.state.device.screenWidth <= 768) {
        //         return true
        //     } else {
        //         return false
        //     }
        // },
    },
    data() {
        return {
            // navActiveIndex: 0,
            productImage: [
                [
                    "https://resource.fs.com/mall/generalImg/20240409151802nq11aq.jpg",
                    "https://resource.fs.com/mall/generalImg/20240409151802cfacha.jpg",
                    "https://resource.fs.com/mall/generalImg/20240423182527md4spp.png",
                    "https://resource.fs.com/mall/generalImg/20240422204127y6y1y4.jpeg",
                ],
                [
                    "https://resource.fs.com/mall/generalImg/20240409151802jeib35.jpg",
                    "https://resource.fs.com/mall/generalImg/202404091518025c5sna.jpg",
                    "https://resource.fs.com/mall/generalImg/20240409151802pgqunu.jpg",
                    "https://resource.fs.com/mall/generalImg/2024041713041893bidc.jpg",
                    "https://resource.fs.com/mall/generalImg/20240409151802iw2m0s.jpg",
                ],
                [
                    "https://resource.fs.com/mall/generalImg/20240409151802tsbhw5.jpg",
                    "https://resource.fs.com/mall/generalImg/20240507110247dmduom.jpg",
                    "https://resource.fs.com/mall/generalImg/20240409151802alna63.jpg",
                    "https://resource.fs.com/mall/generalImg/20240409151802tqo3xq.jpg",
                ],
            ],
            productUrl: [
                [
                    ["/products/194710.html", "/products/194711.html"],
                    ["/products/177272.html", "/products/212165.html", "/products/212161.html"],
                    ["/products/205113.html", "/products/200963.html"],
                    ["/products/208163.html"],
                ],
                [
                    ["/products/196175.html"],
                    ["/products/206401.html", "/products/205267.html", "/products/205265.html", "/products/207065.html"],
                    ["/products/35182.html", "/products/178070.html", "/products/67973.html", "/products/11589.html"],
                    ["/products/68017.html", "/products/40180.html", "/products/70665.html"],
                ],
                [["/products/206401.html"], ["/products/119646.html"], ["/products/35182.html"], ["/products/68017.html"]],
            ],
            // swiperOption: {
            //     pagination: {
            //         el: ".swiper-pagination",
            //         clickable: true,
            //     },
            //     spaceBetween: 20,
            //     breakpoints: {
            //         0: {
            //             //当屏幕宽度大于等于320
            //             slidesPerView: 1.2,
            //             slidesPerGroup: 1,
            //         },
            //         769: {
            //             //当屏幕宽度大于等于768
            //             slidesPerView: 4,
            //             slidesPerGroup: 4,
            //         },
            //     },
            //     navigation: {
            //         nextEl: ".swiper-button-next",
            //         prevEl: ".swiper-button-prev",
            //     },
            // },
        }
    },
    // mounted() {
    //     this.replaceUrl()
    // },
    methods: {
        // changeTab(index) {
        //     this.navActiveIndex = index
        // },
        // replaceUrl() {
        //     if (this.website === "cn") {
        //         // this.productUrl[0][0] = []
        //         // this.productUrl[0][1] = []
        //         // this.productUrl[1][0] = []
        //         // this.productUrl[1][1] = []
        //         // this.productUrl[2][0] = []
        //         // this.productUrl[2][1] = []

        //         this.$set(this.productUrl[0], 0, [])
        //         this.$set(this.productUrl[0], 1, [])
        //         this.$set(this.productUrl[1], 0, [])
        //         this.$set(this.productUrl[1], 1, [])
        //         this.$set(this.productUrl[2], 0, [])
        //         this.$set(this.productUrl[2], 1, [])
        //     }
        // },
        disabledProduct(item) {
            if (this.$route.path.includes("/solutions/picos-for-h100-infiniband-solution-S3000.html")) {
                let disAll = true
                item.forEach((element) => {
                    if (element && !this.filterH100(element)) {
                        disAll = false
                    }
                })
                return ["cn", "hk", "mo", "tw"].includes(this.website) && disAll
            } else {
                return false
            }
        },
        disabledItem(url) {
            if (url) {
                if (this.$route.path.includes("/solutions/picos-for-h100-infiniband-solution-S3000.html")) {
                    return ["cn", "hk", "mo", "tw"].includes(this.website) && this.filterH100(url)
                } else {
                    return false
                }
            } else {
                return true
            }
        },
        linkHref(url) {
            if (url) {
                if (this.$route.path.includes("/solutions/picos-for-h100-infiniband-solution-S3000.html")) {
                    return ["cn", "hk", "mo", "tw"].includes(this.website) && this.filterH100(url) ? false : true
                } else {
                    return true
                }
            } else {
                return false
            }
        },
        filterH100(url) {
            return [
                "/products/194710.html",
                "/products/194711.html",
                "/products/177272.html",
                "/products/212165.html",
                "/products/212161.html",
                "/products/196175.html",
                "/products/206401.html",
                "/products/205267.html",
                "/products/205265.html",
                "/products/207065.html",
                "/products/119646.html",
            ].some((string) => url.includes(string))
        },
        productClick(url) {
            if (url && this.linkHref(url)) {
                window.open(this.localePath({ path: url }))
            }
        },
    },
}
</script>

<style scoped lang="scss">
.container {
    padding: 40px 48px;
    .content_div {
        @include width1200;
        .title {
            @include font20;
            text-align: center;
            color: $textColor1;
            font-weight: 600;
            // padding-bottom: 24px;
        }
        .product_div {
            .list_title {
                @include font16;
                text-align: center;
                font-weight: 600;
                color: $textColor1;
                padding: 36px 0 20px;
                &.first_list_title {
                    padding: 24px 0 20px;
                }
            }
            .product_grid_div {
                display: flex;
                justify-content: center;
                gap: 20px;
            }
        }
        // .nav_div {
        //     ::v-deep .account-tabs {
        //         width: fit-content;
        //         margin: 0 auto;
        //         .list {
        //             .tab-item {
        //                 @include font14;
        //                 padding: 10px 0;
        //                 color: $textColor1;
        //                 max-width: fit-content;
        //                 &.active {
        //                     font-weight: 600;
        //                 }
        //             }
        //         }
        //         .scroll_mask {
        //             display: none;
        //         }
        //     }
        // }
        // .product_div {
        //     display: flex;
        //     // grid-template-columns: 1fr 1fr 1fr 1fr;
        //     // gap: 20px;
        //     // padding-top: 34px;
        //     // justify-content: center;
        //     // width: fit-content;
        //     justify-content: center;
        //     gap: 20px;
        //     padding-top: 24px;
        //     &.hide_product_div {
        //         // display: grid;
        //         display: none;
        //     }
        // }
    }
    // .swiper_content {
    //     // width: 100%;
    //     max-width: 100%;
    //     margin: 0 auto;
    //     transition: all 0.3s;
    //     display: none;
    //     &.show_swiper_div {
    //         display: block;
    //     }

    //     .swiper_div {
    //         .swiper-button-prev {
    //             opacity: 0;
    //         }
    //         .swiper-button-next {
    //             opacity: 0;
    //         }
    //     }

    //     &:hover {
    //         .swiper_div {
    //             .swiper-button-prev {
    //                 opacity: 1;
    //             }
    //             .swiper-button-next {
    //                 opacity: 1;
    //             }
    //         }
    //     }
    //     .swiper_div {
    //         max-width: 1200px;
    //         margin: 0 auto;
    //         padding-top: 24px;
    //         position: relative;
    //         .swiper-button-prev {
    //             opacity: 0;
    //         }
    //         .swiper-button-next {
    //             opacity: 0;
    //         }
    //         &:hover {
    //             .swiper-button-prev {
    //                 opacity: 1;
    //             }
    //             .swiper-button-next {
    //                 opacity: 1;
    //             }
    //         }
    //     }
    //     .swiper-container {
    //         padding-bottom: 28px;
    //         z-index: 0;
    //         .swiper-slide {
    //             display: flex;
    //             height: auto;
    //         }
    //         &::v-deep .swiper-pagination {
    //             display: flex;
    //             justify-content: center;
    //             bottom: 0;
    //             .swiper-pagination-bullet {
    //                 width: 8px;
    //                 height: 8px;
    //                 background: #707070;
    //                 opacity: 0.4;
    //                 transition: all 0.3s;
    //             }
    //             .swiper-pagination-bullet-active {
    //                 width: 20px;
    //                 opacity: 1;
    //                 border-radius: 4px;
    //             }
    //         }
    //     }
    //     .swiper-btn {
    //         font-size: 24px;
    //         color: $textColor7;
    //         width: 48px;
    //         height: 48px;
    //         border-radius: 50%;
    //         transition: all 0.3s;
    //         box-sizing: border-box;
    //         text-align: center;
    //         line-height: 48px;
    //         background-image: none;
    //         background-color: rgba(0, 0, 0, 0.2);
    //         &::after {
    //             display: none;
    //         }
    //         &:hover {
    //             background-color: rgba(0, 0, 0, 0.4);
    //         }
    //         &.swiper-button-prev {
    //             left: -6.5%;
    //         }
    //         &.swiper-button-next {
    //             right: -6.5%;
    //         }
    //         @media (max-width: 1200px) {
    //             display: none;
    //         }
    //     }
    // }

    .product_item {
        display: flex;
        flex-direction: column;
        transition: all 0.3s;
        width: calc((100% - 60px) / 4);
        border-radius: 8px;
        // &.swiper_item {
        //     width: 100%;
        // }
        &:hover {
            box-shadow: 0 15px 15px -10px rgb(0 0 0 / 15%);
        }
        // &.decoration_none {
        //     &:hover {
        //         box-shadow: none;
        //         cursor: auto;
        //         .text_div {
        //             p:hover {
        //                 cursor: text;
        //                 text-decoration: none;
        //             }
        //         }
        //     }
        // }
        &.dis_product {
            &:hover {
                box-shadow: none;
            }
        }
        .img_box {
            position: relative;
            min-height: calc((min(84vw, 1200px) - 60px) / 8);
            ::v-deep {
                .skeleton_wrap {
                    border-radius: 8px 8px 0 0;
                    overflow: hidden;
                    z-index: 0;
                }
            }
            @media (max-width: 768px) {
                min-height: calc((100vw - 32px) / 2);
            }
        }
        img {
            width: 100%;
            height: auto;
            position: relative;
            display: block;
            z-index: 1;
            border-top-left-radius: 8px;
            border-top-right-radius: 8px;
        }
        .text_div {
            padding: 20px;
            color: $textColor1;
            background-color: $bgColor3;
            // border: 1px solid #e5e5e5;
            // border-top: none;
            flex: 1;
            border-bottom-left-radius: 8px;
            border-bottom-right-radius: 8px;
            h5 {
                @include font14;
                font-weight: 600;
                margin-bottom: 12px;
            }
            p {
                @include font14;
                font-weight: 400;
                margin-top: 8px;
                color: $textColor3;
                &:first-child {
                    margin-top: 0;
                }
                &:hover {
                    cursor: pointer;
                    text-decoration: underline;
                }
                &.dis_p {
                    &:hover {
                        cursor: text;
                        text-decoration: none;
                    }
                }
            }
        }
    }
}

@media (max-width: 1024px) {
    .container {
        padding: 40px 24px;
        .content_div {
            width: 100%;
        }
    }
}
@media (max-width: 768px) {
    .container {
        padding: 36px 16px;
        .content_div {
            .title {
                @include font20;
                font-weight: 600;
            }
            // .product_div {
            //     grid-template-columns: 1fr;
            // }
            .product_div {
                .product_grid_div {
                    flex-direction: column;
                }
            }
        }
        .product_item {
            width: 100%;
        }
        // .swiper_content {
        //     display: none;
        // }
    }
}
</style>
