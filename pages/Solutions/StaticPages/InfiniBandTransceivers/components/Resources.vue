<template>
    <div class="container">
        <div class="content_box">
            <h2 class="title">{{ resources.title }}</h2>
            <div class="card_list">
                <div class="card" v-for="(item, index) in resources.list" :key="index">
                    <img :src="iconUrl[index]" alt="" />
                    <div class="bottom">
                        <h3 class="card_title">{{ item.title }}</h3>
                        <ul>
                            <li v-for="(text, i) in item.linkText" :key="i" @click="jumpToPage(resourcesUrl[index][i], index, i)">
                                <span class="dot"></span>
                                <span>{{ text }}</span>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import { mapState, mapGetters } from "vuex"
export default {
    props: {
        resources: {
            type: Object,
            default: () => {},
        },
    },
    data() {
        return {
            iconUrl: [
                "https://resource.fs.com/mall/generalImg/20240507101611sy0niq.svg",
                "https://resource.fs.com/mall/generalImg/20240507101611y3cvj3.svg",
                "https://resource.fs.com/mall/generalImg/20240507101611vkslxm.svg",
                "https://resource.fs.com/mall/generalImg/20240509120046qdhbvl.svg",
            ],
            resourcesUrl: [
                ["/solutions/picos-for-h100-infiniband-solution-S3000.html", "/specials/1.6t-800g-400g-200g-transceivers-and-cables-156.html", "/solutions/picos-for-automated-driving-solution-10060.html"],
                ["/case-study/fs-helps-autonomous-vehicle-startup-build-data-center-network-1.html"],
                [
                    "/blog/fs-infiniband-transceiver-and-cables-complete-guide.html",
                    "/article/tips-on-choosing-infiniband-products-for-hpc-computing.html",
                    "/article/need-for-speed-–-infiniband-network-bandwidth-evolution.html",
                ],
                ["https://resource.fs.com/video/20240612151539gqhf46.mp4", "https://resource.fs.com/video/202403271132331quf1v.mp4", "https://resource.fs.com/video/20240329115348m9n44t.mp4"],
            ],
        }
    },
    computed: {
        ...mapState({
            website: (state) => state.webSiteInfo.website,
        }),
        ...mapGetters({
            community_website: "webSiteInfo/community_website",
        }),
    },
    mounted() {
        this.replaceUrl()
    },
    methods: {
        replaceUrl() {
            if (this.website === "cn") {
                this.resourcesUrl[2] = [
                    "/article/inquiries-and-answers-about-infiniband-technology.html",
                    "/blog/building-hpc-data-center-networking-architecture-with-fs-infiniband-solution.html",
                    "/article/endtoend-infiniband-solutions-for-llm-trainings-bottleneck.html",
                ]
            }
            if (this.$route.path.includes("/solutions/infiniband-transceivers-and-cables-connectivity-solution-overview-S3001.html") && ["cn", "hk", "mo", "tw"].includes(this.website)) {
                this.resourcesUrl[1] = ["/case-study/manufacturing-company-utilizes-ib-optical-modules-to-overcome-the-bandwidth-bottleneck-in-large-scale-network-deployment-121.html"]
            }
        },
        jumpToPage(url, index, i) {
            if (url.includes("https")) {
                window.open(url)
            } else {
                if (index === 0 || index === 1) {
                    window.open(this.$localeLink(url))
                } else if (index === 2) {
                    if ((this.website === "de" && i === 0) || (this.website === "de" && i === 2)) {
                        window.open(`https://community.fs.com${url}`)
                    } else {
                        window.open(`https://community.fs.com${this.community_website + url}`)
                    }
                } else {
                    window.open(`https://community.fs.com${this.community_website + url}`)
                }
            }
        },
    },
}
</script>

<style lang="scss" scoped>
.container {
    @include width1200;
    .content_box {
        padding: 40px 0;
        .title {
            @include font20;
            font-weight: 600;
            text-align: center;
            color: $textColor1;
            margin-bottom: 24px;
        }
        .card_list {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr 1fr;
            gap: 20px;
            .card {
                display: flex;
                flex-direction: column;
                padding: 32px;
                border: 1px solid #dee0e3;
                border-radius: 8px;
                transition: all 0.3s;
                &:hover {
                    box-shadow: 0px 4px 8px 0px rgba(0, 0, 0, 0.1);
                }
                img {
                    width: 36px;
                    height: 36px;
                }
                .bottom {
                    flex: 1;
                    .card_title {
                        @include font14;
                        font-weight: 600;
                        margin: 12px 0;
                        color: $textColor1;
                    }
                    ul {
                        li {
                            display: flex;
                            @include font12;
                            font-weight: 400;
                            color: $textColor3;
                            margin-top: 4px;
                            align-items: flex-start;
                            &:first-child {
                                margin-top: 0;
                            }
                            &:hover {
                                cursor: pointer;
                                text-decoration: underline;
                            }
                            .dot {
                                width: 4px;
                                height: 4px;
                                border-radius: 50%;
                                background-color: $textColor3;
                                margin-right: 8px;
                                margin-top: 8px;
                                flex-shrink: 0;
                            }
                        }
                    }
                }
            }
        }
    }
}

@media (max-width: 1024px) {
    .container {
        width: 100%;
        padding: 0 24px;
    }
}

@media (max-width: 768px) {
    .container {
        padding: 0 16px;
        .content_box {
            padding: 36px 0;
            .title {
                @include font20;
                font-weight: 600;
            }
            .card_list {
                grid-template-columns: 1fr;
            }
        }
    }
}
</style>
