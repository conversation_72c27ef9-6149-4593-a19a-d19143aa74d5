<template>
    <div class="container">
        <div class="content_box">
            <h2 class="title">{{ resources.title }}</h2>
            <div class="card_list">
                <div class="card" v-for="(item, index) in resources.list" :key="index">
                    <img :src="iconUrl[index]" alt="" />
                    <div class="bottom">
                        <h3 class="card_title">{{ item.title }}</h3>
                        <ul>
                            <li v-for="(text, i) in item.linkText" :key="i" @click="jumpToPage(resourcesUrl[index][i], index, i)">
                                <span class="dot"></span>
                                <span>{{ text }}</span>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import { mapState, mapGetters } from "vuex"
export default {
    props: {
        resources: {
            type: Object,
            default: () => {},
        },
    },
    data() {
        return {
            iconUrl: [
                "https://resource.fs.com/mall/generalImg/20240725163345e2nd2j.svg",
                "https://resource.fs.com/mall/generalImg/20240725163345ecqmer.svg",
                "https://resource.fs.com/mall/generalImg/20240725163345xk00z8.svg",
            ],
            resourcesUrl: [
                ["/solutions/picos-for-h100-infiniband-solution-S3000.html", "/solutions/picos-for-multi-branch-network-solution.html"],
                ["/article/build-your-network-with-pica8-network-platform.html", "/article/the-myth-behind-pica8-zero-touch-provisioning-in-remote-branches-application.html"],
                ["/blog/fs-switches-now-support-picos%C2%AE-for-unified-networking-experience.html"],
            ],
        }
    },
    computed: {
        ...mapState({
            website: (state) => state.webSiteInfo.website,
        }),
        ...mapGetters({
            community_website: "webSiteInfo/community_website",
        }),
    },
    mounted() {
        this.replaceUrl()
    },
    methods: {
        replaceUrl() {
            if (this.website === "cn") {
                this.resourcesUrl[1] = ["/article/effective-solutions-for-100g-cloud-data-center-connectivity.html", "/article/exploring-the-benefits-of-data-center-fabric.html"]
                this.resourcesUrl[2] = ["/blog/FS-Pica8-Announced-a-Strategic-Partnership-to-Jointly-Promote-the-Development-of-Open-Networks.html"]
            }
        },
        jumpToPage(url, index, i) {
            if (index === 0) {
                window.open(this.localePath({ path: url }))
            } else {
                if (index === 1 && (this.website === "jp" || this.website === "fr")) {
                    window.open(`https://community.fs.com${url}`)
                } else {
                    window.open(`https://community.fs.com${this.community_website + url}`)
                }
            }
        },
    },
}
</script>

<style lang="scss" scoped>
.container {
    padding: 40px 48px;
    .content_box {
        @include width1200;
        .title {
            @include font20;
            font-weight: 600;
            text-align: center;
            color: $textColor1;
            margin-bottom: 24px;
        }
        .card_list {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 20px;
            .card {
                display: flex;
                flex-direction: column;
                padding: 32px;
                border: 1px solid #dee0e3;
                border-radius: 8px;
                transition: all 0.3s;
                &:hover {
                    box-shadow: 0px 4px 8px 0px rgba(0, 0, 0, 0.1);
                }
                img {
                    width: 36px;
                    height: 36px;
                }
                .bottom {
                    flex: 1;
                    .card_title {
                        @include font14;
                        font-weight: 600;
                        margin: 12px 0;
                        color: $textColor1;
                    }
                    ul {
                        li {
                            display: flex;
                            @include font12;
                            font-weight: 400;
                            color: $textColor3;
                            margin-top: 4px;
                            align-items: flex-start;
                            &:first-child {
                                margin-top: 0;
                            }
                            &:hover {
                                cursor: pointer;
                                text-decoration: underline;
                            }
                            .dot {
                                width: 4px;
                                height: 4px;
                                border-radius: 50%;
                                background-color: $textColor3;
                                margin-right: 8px;
                                margin-top: 8px;
                                flex-shrink: 0;
                            }
                        }
                    }
                }
            }
        }
    }
}

@media (max-width: 1024px) {
    .container {
        padding: 40px 24px;
        .content_box {
            width: 100%;
        }
    }
}

@media (max-width: 768px) {
    .container {
        padding: 36px 16px;
        .content_box {
            .title {
                @include font20;
                font-weight: 600;
            }
            .card_list {
                grid-template-columns: 1fr;
            }
        }
    }
}
</style>
