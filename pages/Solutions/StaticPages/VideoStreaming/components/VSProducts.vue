<template>
    <div class="container">
        <div class="content_div">
            <h2 class="title">{{ products.title }}</h2>
            <div class="product_div">
                <div class="product_item" v-for="(item, index) in products.list" :key="index" :class="{ dis_product: disabledProduct(productUrl[index]) }">
                    <div class="img_box">
                        <skeleton-solutions :item="{ width: '100%', bg: '#F2F2F2', height: '100%' }"> </skeleton-solutions>
                        <img :src="productImage[index]" alt="" />
                    </div>
                    <div class="text_div">
                        <h5>{{ item.title }}</h5>
                        <p v-for="(text, descIndex) in item.descTitle" :key="descIndex" @click="productClick(productUrl[index][descIndex])" :class="{ dis_p: disabledItem(productUrl[index][descIndex]) }">{{ text }}</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import SkeletonSolutions from "@/components/Skeleton/SkeletonSolutions"
import { mapState } from "vuex"

export default {
    props: {
        products: {
            type: Object,
            default: () => {},
        },
    },
    components: {
        SkeletonSolutions,
    },
    data() {
        return {
            productImage: [
                "https://resource.fs.com/mall/generalImg/20240409151802jeib35.jpg",
                "https://resource.fs.com/mall/generalImg/20240515100718tqnikf.jpg",
                "https://resource.fs.com/mall/generalImg/20240515100718q8duaq.jpg",
                "https://resource.fs.com/mall/generalImg/2024051510071822blh2.jpg",
                "https://resource.fs.com/mall/generalImg/2024051510071869xcgp.jpg",
                "https://resource.fs.com/mall/generalImg/20240515100718b8bnrm.jpg",
                "https://resource.fs.com/mall/generalImg/20240515100718ojjpnh.jpg",
                "https://resource.fs.com/mall/generalImg/20240515100718l9d9l9.jpg",
            ],
            productUrl: [
                ["/products/196175.html"],
                ["/products/206401.html", "/products/205269.html", "/products/205267.html", "/products/205265.html", "/products/207993.html"],
                ["/products/207061.html", "/products/207065.html"],
                ["/products/139687.html", "/products/173986.html"],
                ["/products/119646.html", "/products/119649.html", "/products/141788.html", "/products/75603.html"],
                ["/products/35182.html", "/products/72704.html", "/products/67973.html", "/products/11589.html", "/products/66612.html"],
                ["/products/68017.html", "/products/40180.html"],
                ["/products/70665.html"],
            ],
        }
    },
    computed: {
        ...mapState({
            website: (state) => state.webSiteInfo.website,
        }),
    },
    methods: {
        disabledProduct(item) {
            if (this.$route.path.includes("/solutions/seamless-video-streaming-experience-in-data-center-S3006.html")) {
                let disAll = true
                item.forEach((element) => {
                    if (element && !this.filterVS(element)) {
                        disAll = false
                    }
                })
                return ["cn", "hk", "mo", "tw"].includes(this.website) && disAll
            } else {
                return false
            }
        },
        disabledItem(url) {
            if (url) {
                if (this.$route.path.includes("/solutions/seamless-video-streaming-experience-in-data-center-S3006.html")) {
                    return ["cn", "hk", "mo", "tw"].includes(this.website) && this.filterVS(url)
                } else {
                    return false
                }
            } else {
                return true
            }
        },
        linkHref(url) {
            if (url) {
                if (this.$route.path.includes("/solutions/seamless-video-streaming-experience-in-data-center-S3006.html")) {
                    return ["cn", "hk", "mo", "tw"].includes(this.website) && this.filterVS(url) ? false : true
                } else {
                    return true
                }
            } else {
                return false
            }
        },
        filterVS(url) {
            return [
                "/products/196175.html",
                "/products/206401.html",
                "/products/205269.html",
                "/products/205267.html",
                "/products/205265.html",
                "/products/207993.html",
                "/products/207061.html",
                "/products/207065.html",
                "/products/139687.html",
                "/products/173986.html",
                "/products/119646.html",
                "/products/119649.html",
                "/products/141788.html",
            ].some((string) => url.includes(string))
        },
        productClick(url) {
            if (url && this.linkHref(url)) {
                window.open(this.localePath({ path: url }))
            }
        },
    },
}
</script>

<style scoped lang="scss">
.container {
    padding: 40px 48px;
    .content_div {
        @include width1200;
        .title {
            @include font20;
            text-align: center;
            color: $textColor1;
            font-weight: 600;
        }
        .product_div {
            display: flex;
            justify-content: center;
            flex-wrap: wrap;
            gap: 20px;
            padding-top: 24px;
        }
    }
    .product_item {
        display: flex;
        flex-direction: column;
        transition: all 0.3s;
        width: calc((100% - 60px) / 4);
        border-radius: 8px;
        &:hover {
            box-shadow: 0 15px 15px -10px rgb(0 0 0 / 15%);
        }
        &.dis_product {
            &:hover {
                box-shadow: none;
            }
        }
        .img_box {
            position: relative;
            min-height: calc((min(84vw, 1200px) - 60px) / 8);
            ::v-deep {
                .skeleton_wrap {
                    border-radius: 8px 8px 0 0;
                    overflow: hidden;
                    z-index: 0;
                }
            }
            @media (max-width: 768px) {
                min-height: calc((100vw - 32px) / 2);
            }
        }
        img {
            width: 100%;
            height: auto;
            position: relative;
            display: block;
            z-index: 1;
            border-radius: 8px 8px 0 0;
        }
        .text_div {
            padding: 20px;
            color: $textColor1;
            background-color: $bgColor3;
            // border: 1px solid #e5e5e5;
            // border-top: none;
            flex: 1;
            border-radius: 0 0 8px 8px;
            h5 {
                @include font14;
                font-weight: 600;
                margin-bottom: 12px;
            }
            p {
                @include font14;
                font-weight: 400;
                color: $textColor3;
                margin-top: 8px;
                &:first-child {
                    margin-top: 0;
                }
                &:hover {
                    cursor: pointer;
                    text-decoration: underline;
                }
                &.dis_p {
                    &:hover {
                        cursor: text;
                        text-decoration: none;
                    }
                }
            }
        }
    }
}

@media (max-width: 1024px) {
    .container {
        padding: 40px 24px;
        .content_div {
            width: 100%;
        }
    }
}
@media (max-width: 768px) {
    .container {
        padding: 36px 16px;
        .content_div {
            .title {
                @include font20;
                font-weight: 600;
            }
            .product_div {
                flex-direction: column;
            }
        }
        .product_item {
            width: 100%;
        }
    }
}
</style>
