<template>
    <div class="container">
        <div class="container_title">{{ $c("single.NetworkSolution.byNow.popTit") }}</div>
        <div class="content_box" v-for="(item, index) in products" :key="index">
            <h2 class="title">{{ item.title }}</h2>
            <div class="card_list" :class="{ card_list_4: item.products.length === 4, card_list_2: item.products.length === 2 }">
                <div class="card" v-for="(val, ind) in item.products" :key="ind" :class="{ dis_product: disabledProduct(managementUrl[index][ind]) }">
                    <div class="img_box">
                        <skeleton-solutions :item="{ width: '100%', bg: '#F2F2F2', height: '100%' }"> </skeleton-solutions>
                        <img :src="managementImg[index][ind]" />
                    </div>
                    <div class="bottom">
                        <h3 class="card_title">{{ val.title }}</h3>
                        <div class="card_desc">
                            <!-- :href="localePath({ path: managementUrl[index][ind][i] })" -->
                            <a v-for="(v, i) in val.desc" :key="i" :href="linkHref(managementUrl[index][ind][i])" :class="{ dis_p: disabledItem(managementUrl[index][ind][i]) }">{{ v }}</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import SkeletonSolutions from "@/components/Skeleton/SkeletonSolutions"
import { mapState } from "vuex"

export default {
    components: {
        SkeletonSolutions,
    },
    props: {
        products: {
            type: Array,
            default: () => [],
        },
        managementUrl: {
            type: Array,
            default: () => [],
        },
        managementImg: {
            type: Array,
            default: () => [],
        },
    },
    computed: {
        ...mapState({
            website: (state) => state.webSiteInfo.website,
        }),
    },
    methods: {
        disabledProduct(item) {
            if (this.$route.path.includes("/solutions/enterprise-office-buildings-surveillance-solution-S3004.html")) {
                let disAll = true
                item.forEach((element) => {
                    if (element && !this.filterOB(element)) {
                        disAll = false
                    }
                })
                return ["cn", "hk", "mo", "tw"].includes(this.website) && disAll
            } else {
                return false
            }
        },
        disabledItem(url) {
            if (url) {
                if (this.$route.path.includes("/solutions/enterprise-office-buildings-surveillance-solution-S3004.html")) {
                    return ["cn", "hk", "mo", "tw"].includes(this.website) && this.filterOB(url)
                } else {
                    return false
                }
            } else {
                return true
            }
        },
        linkHref(url) {
            if (url) {
                if (this.$route.path.includes("/solutions/enterprise-office-buildings-surveillance-solution-S3004.html")) {
                    return ["cn", "hk", "mo", "tw"].includes(this.website) && this.filterOB(url) ? "javascript:;" : this.$handleLink(url).url
                } else {
                    return this.$handleLink(url).url
                }
            } else {
                return "javascript:;"
            }
        },
        filterOB(url) {
            return [
                "/products/185135.html",
                "/products/185138.html",
                "/products/174017.html",
                "/products/115388.html",
                "/products/204101.html",
                "/products/174010.html",
                "/products/195438.html",
                "/products/174115.html",
                "/products/195427.html",
                "/products/174112.html",
                "/products/140093.html",
                "/products/174111.html",
                "/products/154788.html",
            ].some((string) => url.includes(string))
        },
    },
}
</script>

<style lang="scss" scoped>
.container {
    padding: 40px 0;
    .container_title {
        @include font20;
        font-weight: 600;
        text-align: center;
        margin-bottom: 24px;
        color: $textColor1;
    }
    .content_box {
        @include width1200;
        &:not(:last-child) {
            margin-bottom: 36px;
        }
        .title {
            @include font16;
            color: $textColor1;
            font-weight: 600;
            text-align: center;
            margin-bottom: 20px;
        }
        .card_list {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            &.card_list_4 {
                grid-template-columns: repeat(4, 1fr);
            }
            &.card_list_2 {
                display: flex;
                justify-content: center;
            }
            gap: 20px;
            max-width: max-content;
            margin: 0 auto;
            .card {
                max-width: 285px;
                background-color: #fff;
                display: flex;
                flex-direction: column;
                border-radius: 8px;
                transition: all 0.3s;
                .img_box {
                    position: relative;
                    min-height: calc((min(84vw, 1200px) - 60px) / 8);
                    ::v-deep {
                        .skeleton_wrap {
                            border-radius: 8px 8px 0 0;
                            overflow: hidden;
                            z-index: 0;
                        }
                    }
                    @media (max-width: 768px) {
                        min-height: calc((100vw - 32px) / 2);
                    }
                }
                &:hover {
                    box-shadow: 0px 4px 8px 0px rgba(0, 0, 0, 0.1);
                    border-radius: 8px;
                }
                &.dis_product {
                    &:hover {
                        box-shadow: none;
                    }
                }
                img {
                    width: 100%;
                    height: auto;
                    position: relative;
                    display: block;
                    z-index: 1;
                    border-radius: 8px 8px 0 0;
                }
                .bottom {
                    flex: 1;
                    padding: 20px;
                    // border-width: 0px 1px 1px 1px;
                    // border-style: solid;
                    // border-color: #e5e5e5;
                    border-radius: 0 0 8px 8px;
                    .card_title {
                        @include font14;
                        font-weight: 600;
                        color: $textColor1;
                    }

                    .card_desc {
                        @include font14;
                        font-weight: 400;
                        margin-top: 12px;
                        display: flex;
                        flex-direction: column;
                        a {
                            color: $textColor3;
                            &:not(:first-child) {
                                margin-top: 8px;
                            }
                            &.dis_p {
                                &:hover {
                                    cursor: text;
                                    text-decoration: none;
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}

@media (max-width: 1024px) {
    .container {
        padding: 40px 24px;
        .content_box {
            width: 100%;
        }
    }
}

@media (max-width: 768px) {
    .container {
        padding: 36px 16px;
        .content_box {
            &:not(:last-child) {
                margin-bottom: 24px;
            }
            .title {
                font-weight: 600;
            }
            .card_list {
                grid-template-columns: 1fr;
                .card {
                    max-width: 100%;
                    &:hover {
                        box-shadow: none;
                    }
                }
                &.card_list_4 {
                    grid-template-columns: 1fr;
                }
            }
        }
    }
}
</style>
