<template>
    <div class="container_div">
        <banner :banner="contentData.banner" textColor="black"></banner>
        <div class="nav_div" ref="slideDownRef">
            <a-d-navigation-bar :nav="contentData.nav" :activeIndex="activeIndex" :slideDownOpen="slideDownOpen" @pageNavClick="pageNavClick" @clickSlideDown="clickSlideDown"></a-d-navigation-bar>
        </div>
        <div ref="ref-0">
            <Introduction :introduction="contentData.introduction" :tagData="tagData" @showImageView="showImageView"></Introduction>
        </div>
        <div class="gray_div" ref="ref-1">
            <Management :products="contentData.products" :managementUrl="managementUrl" :managementImg="managementImg"></Management>
        </div>
        <div ref="ref-2">
            <value-points :textData="contentData.valuePoints" :imageData="valuePointsImg"></value-points>
        </div>
        <div class="gray_div" ref="ref-3">
            <!-- <a-d-benefits :benefits="contentData.benefits" :borderRadius="true"></a-d-benefits> -->
            <value-of-the-plan :benefits="contentData.benefits"></value-of-the-plan>
        </div>
        <div ref="ref-4">
            <surveillance-resource :resources="contentData.resources" :resourcesUrl="resourcesUrl" :borderRadius="true"></surveillance-resource>
        </div>
        <!-- <contact-us></contact-us> -->
        <div ref="ref-5">
            <i-b-s-contact-us :type_id="typeId"></i-b-s-contact-us>
        </div>
        <image-preview :src="imageViewUrl" :open="imageViewOpen" @close="closeImage" />
    </div>
</template>

<script>
import Banner from "./components/Banner.vue"
import ADNavigationBar from "../AutomatedDrivingSolution/components/ADNavigationBar.vue"
import Introduction from "./components/Introduction.vue"
import ADBenefits from "../AutomatedDrivingSolution/components/ADBenefits.vue"
import Management from "./components/Management.vue"
import SurveillanceResource from "./components/SurveillanceResource.vue"
import IBSContactUs from "../InfiniBandSolutions/components/IBSContactUs.vue"
import ValuePoints from "./components/ValuePoints.vue"
import ValueOfThePlan from "./components/ValueOfThePlan.vue"
import ContactUs from "./components/ContactUs.vue"
import ImagePreview from "../components/ImagePreview.vue"
import { mapState } from "vuex"
export default {
    components: {
        Banner,
        ADNavigationBar,
        Introduction,
        ADBenefits,
        Management,
        SurveillanceResource,
        IBSContactUs,
        ValuePoints,
        ValueOfThePlan,
        ContactUs,
        ImagePreview,
    },
    data() {
        const website = this.$store.state.webSiteInfo.website
        return {
            tagData: [],
            activeIndex: 0,
            slideDownOpen: false,
            imageViewOpen: false,
            imageViewUrl: "",
            typeId: "",
            formData: this.$c("form.form.option"),
            contentData: this.$c("solutions.OfficeBuildings"),
            benefitsImg: [
                "https://resource.fs.com/mall/generalImg/20240418170920bgf15c.jpeg",
                "https://resource.fs.com/mall/generalImg/202404181709206iemub.jpg",
                "https://resource.fs.com/mall/generalImg/2024041817092042is28.jpg",
                "https://resource.fs.com/mall/generalImg/20240418170920ualk2z.jpeg",
                "https://resource.fs.com/mall/generalImg/20240418170920g002fm.jpeg",
            ],
            tagImage: ["https://resource.fs.com/mall/generalImg/20240417113850laa8mk.jpg", "https://resource.fs.com/mall/generalImg/20240417113850bq26tp.jpg"],
            managementImg: [
                [
                    "https://resource.fs.com/mall/generalImg/20240530201403q1lrce.jpg",
                    "https://resource.fs.com/mall/generalImg/20240909161407g5wtt2.jpg",
                    "https://resource.fs.com/mall/generalImg/202405302014030n9xl5.jpg",
                ],
                ["https://resource.fs.com/mall/generalImg/20240530201628coof47.jpg", "https://resource.fs.com/mall/generalImg/20240530201628b6crpd.jpg"],
                [
                    "https://resource.fs.com/mall/generalImg/20240530201739rbq0mt.jpg",
                    "https://resource.fs.com/mall/generalImg/20240530201739whntcl.jpg",
                    "https://resource.fs.com/mall/generalImg/20240530201739iz3sue.jpg",
                    "https://resource.fs.com/mall/generalImg/20240530201739lc3qvb.jpg",
                ],
            ],
            managementUrl: [
                [["/products/185135.html", "/products/185138.html"], ["/products/174017.html"], ["/products/115388.html"]],
                [["/products/204101.html"], ["/products/174010.html"], ["/products/174028.html"]],
                [["/products/195438.html", "/products/174115.html"], ["/products/195427.html"], ["/products/174112.html"], ["/products/140093.html", "/products/174111.html", "/products/154788.html"]],
            ],
            resourcesUrl: [
                ["/solutions/Enterprises-Security-Video-Surveillance-Solution-19.html", "/solutions/Enterprise-Video-Surveillance-1016.html"],
                this.$c("solutions.OfficeBuildings").resources.list[1].links,
                this.$c("solutions.OfficeBuildings").resources.list[2].links,
                this.$c("solutions.OfficeBuildings").resources.list[3].links,
            ],
            resourceImg: [
                "https://resource.fs.com/mall/generalImg/20240725161904kil9c2.svg",
                "https://resource.fs.com/mall/generalImg/2024072516190494ze56.svg",
                "https://resource.fs.com/mall/generalImg/202407251619046u4wcj.svg",
                "https://resource.fs.com/mall/generalImg/20240725161904urrj1l.svg",
            ],
            valuePointsImg: {
                vodeoUrl: "",
                imageUrl: "https://resource.fs.com/mall/generalImg/20240601104708ukoibi.jpg",
                iconUrl: [
                    "https://resource.fs.com/mall/generalImg/20250110172152uc0avp.svg",
                    "https://resource.fs.com/mall/generalImg/20240725160711wmu1b1.svg",
                    "https://resource.fs.com/mall/generalImg/20250110172152sogvok.svg",
                    "https://resource.fs.com/mall/generalImg/20250110172152dpxyxh.svg",
                ],
            },
        }
    },
    head() {
        return {
            title: this.$c("solutions.OfficeBuildings.meta.title"),
            meta: [
                {
                    hid: "title",
                    name: "title",
                    content: this.$c("solutions.OfficeBuildings.meta.title"),
                },
                {
                    hid: "description",
                    name: "description",
                    content: this.$c("solutions.OfficeBuildings.meta.description"),
                },
                {
                    hid: "og:title",
                    property: "og:title",
                    content: this.$c("solutions.OfficeBuildings.meta.title"),
                },
                {
                    hid: "og:description",
                    property: "og:description",
                    content: this.$c("solutions.OfficeBuildings.meta.description"),
                },
                {
                    hid: "twitter:title",
                    name: "twitter:title",
                    content: this.$c("solutions.OfficeBuildings.meta.title"),
                },
                {
                    hid: "twitter:description",
                    name: "twitter:description",
                    content: this.$c("solutions.OfficeBuildings.meta.description"),
                },
            ],
        }
    },
    computed: {
        ...mapState({
            isMobile: (state) => state.device.isMobile,
            screenWidth: (state) => state.device.screenWidth,
            website: (state) => state.webSiteInfo.website,
        }),
    },
    created() {
        this.initData()
        console.log(123123, this.managementImg[0][0])
    },
    mounted() {
        document.addEventListener("click", this.handleClickOutSide)
        window.addEventListener("scroll", this.scrollHandle)
    },
    beforeDestroy() {
        window.removeEventListener("scroll", this.scrollHandle)
    },
    methods: {
        showImageView(url) {
            if (!this.isMobile) return
            this.imageViewUrl = url
            this.imageViewOpen = true
        },
        closeImage() {
            this.imageViewOpen = false
        },
        handleClickOutSide(e) {
            const slideDownRef = this.$refs.slideDownRef
            if (slideDownRef && !slideDownRef.contains(e.target)) {
                this.slideDownOpen = false
            }
        },
        clickSlideDown() {
            this.slideDownOpen = !this.slideDownOpen
        },
        pageNavClick(index) {
            this.slideDownOpen = false
            let maxNum = 50
            let ElTop = this.$refs[`ref-${index}`].offsetTop
            document.querySelector("body,html").setAttribute("style", "scroll-behavior: smooth;")
            window.scrollTo(0, ElTop - maxNum)
        },
        scrollHandle() {
            const HeaderTop = this.screenWidth <= 1024 ? 84 : 0
            const scrollTop = document.documentElement.scrollTop - HeaderTop
            let fixHeight = this.screenWidth <= 1024 ? 140 : 60
            if (this.contentData.nav.list.length == 0) return
            this.contentData.nav.list.forEach((item, i) => {
                const offsetTop = this.$refs[`ref-${i}`]?.offsetTop
                if (scrollTop > offsetTop - fixHeight) {
                    this.activeIndex = i
                }
            })
        },
        initData() {
            this.contentData.banner.image.pc = "https://resource.fs.com/mall/generalImg/20240601104708q0e26h.jpg"
            this.contentData.banner.image.pad = "https://resource.fs.com/mall/generalImg/202406011047549se10e.jpeg"
            this.contentData.banner.image.m = "https://resource.fs.com/mall/generalImg/20240601104754x7utrc.jpeg"
            this.contentData.banner.crumbList = [
                {
                    name: this.$c("solutions.MediumAndLargeCampusWirelessNetwork.contentData.data.bread[0].name"),
                    url: "/",
                },
                {
                    name: this.contentData.banner.bread,
                    url: "",
                },
                {
                    name: this.contentData.banner.solutionID,
                    url: this.contentData.headTop.href,
                },
            ]
            this.benefitsImg.forEach((item, index) => {
                this.contentData.benefits.list.forEach((v, i) => {
                    if (index === i) {
                        v.image = item
                    }
                })
            })
            this.resourceImg.forEach((item, index) => {
                this.contentData.resources.list.forEach((v, i) => {
                    if (index === i) {
                        v.icon = item
                    }
                })
            })
            // 获取当前方案应用场景分类类型ID
            if (this.initTypeIdForBreadcrumbs()) {
                this.typeId = this.initTypeIdForBreadcrumbs()
            } else if (this.initTypeIdForBannerTitle()) {
                this.typeId = this.initTypeIdForBannerTitle()
            } else {
                this.typeId = null
            }
        },
        initTypeIdForBreadcrumbs() {
            if (this.contentData.banner.crumbList && this.contentData.banner.crumbList.length > 0) {
                let index = this.contentData.banner.crumbList.length > 2 ? 1 : this.contentData.banner.crumbList.length - 1
                let breadnName = this.contentData.banner.crumbList[index].name
                let targetElement = this.formData.application_scenario.find(function (item) {
                    return item.name == breadnName
                })
                return targetElement ? targetElement.value : null
            } else {
                return null
            }
        },
        initTypeIdForBannerTitle() {
            let bannerTitle = this.contentData.banner.title
            let targetElement = this.formData.application_scenario.find(function (item) {
                return item.name == bannerTitle
            })
            return targetElement ? targetElement.value : null
        },
    },
    asyncData({ app, route }) {
        try {
            app.$axios.post(`/cms/api/fsSolution/browsing/history`, {
                url: route.path,
                shareUrlId: route.query.shareUrlId || "",
            })
        } catch (err) {
            console.log(err)
        }
        return app.$axios
            .post("/api/tag", {
                tag_id: 12545,
            })
            .then((res) => {
                console.log(123, res)
                return {
                    tagData: res.data,
                }
            })
            .catch((err) => {
                alert(err.message)
            })
    },
}
</script>

<style lang="scss">
body .fs-global-float-wrap {
    z-index: 60 !important;
}
</style>
<style lang="scss" scoped>
.container_div {
    .nav_div {
        background-color: #eeeeee;
        position: sticky;
        top: 0;
        left: 0;
        padding: 0 48px;
        z-index: 50;
        @media (max-width: 1024px) {
            padding: 0;
        }
    }
    .gray_div {
        background-color: $bgColor7;
        ::v-deep .container {
            .content_box {
                .card_list {
                    &.card_list_5 {
                        display: flex;
                        justify-content: center;
                        @media (max-width: 768px) {
                            flex-direction: column;
                        }
                        .card {
                            width: calc((100% - 40px) / 3);
                            @media (max-width: 768px) {
                                width: 100%;
                            }
                        }
                    }
                }
            }
        }
    }
    ::v-deep .fs-popover {
        .popper-computer {
            z-index: 1;
        }
    }
}
</style>
