<template>
    <div class="solution">
        <feste-data-solutions :contentData="contentData" :navData="navData" :tag_id="tag_id" v-if="contentData.is_feste_data"></feste-data-solutions>
        <erp-solutions :contentData="contentData" :navData="navData" :tag_id="tag_id" v-else-if="contentData.is_erp"></erp-solutions>
        <group-solutions :contentData="contentData.data" :tag_id="contentData.tag_id" v-else-if="contentData.is_group"></group-solutions>
        <detail-solutions :contentData="details" :resData="data" :tag_id="tag_id" v-else-if="data.version === 'Solutions02'"></detail-solutions>
        <detail-solutions-three :contentData="details" :resData="data" :tag_id="tag_id" v-else-if="data.version === 'Solutions03'"></detail-solutions-three>
        <!-- Solutions01 -->
        <cms-solutions v-else :contentDatas="contentData" :navData="navData" :breadData="bread" :tag_id="tag_id"></cms-solutions>
    </div>
</template>

<script>
import ErpSolutions from "./ErpSolutions/ErpSolutions.vue"
import FesteDataSolutions from "./FesteDataSolutions/FesteDataSolutions.vue"
import CmsSolutions from "./CmsSolutions/CmsSolutions.vue"
import GroupSolutions from "./GroupSolutions/GroupSolutions.vue"
import DetailSolutions from "./DetailSolutions/DetailSolutions.vue"
import DetailSolutionsThree from "./DetailSolutionsThree/DetailSolutionsThree.vue"
import { mapState } from "vuex"
import { deepClone } from "@/util/util"
import { Number } from "core-js"
export default {
    components: {
        ErpSolutions,
        FesteDataSolutions,
        CmsSolutions,
        GroupSolutions,
        DetailSolutions,
        DetailSolutionsThree,
    },
    async asyncData({ app, route, store, redirect, $c }) {
        const $axios = app.$axios
        let tag_id = route.path.substr(route.path.lastIndexOf("-") + 1)
        console.log("hfhffhfhfhhfhf===")
        tag_id = tag_id.replace(".html", "")
        let website = store.state.webSiteInfo.website
        let warehouse = store.state.webSiteInfo.warehouse
        console.log("1234566===", tag_id, route.path, store)
        if (!tag_id.includes("S")) {
            let redirectUrl = route.path.replace(tag_id, `S${tag_id}`)
            redirect(app.localePath({ path: redirectUrl }))
        }
        switch (tag_id) {
            case "S1013":
                redirect(app.localePath({ path: "/solutions/large-and-midsize-campus-network-solution-S10058.html" }))
                break
            case "S44":
                redirect(app.localePath({ path: "/solutions/small-business-security-solutions-S10071.html" }))
                break
            case "S47":
                redirect(app.localePath({ path: "/solutions/smb-office-network-solution-S10072.html" }))
                break
            case "S10046":
                redirect(app.localePath({ path: "/solutions/empower-hpc-network-with-g-roce-lossless-solution-S10068.html" }))
                break
            case "S10014":
                redirect(app.localePath({ path: "/solutions/cdn-solutions-for-ott-iptv-S10078.html" }))
                break
            case "S1012":
                redirect(app.localePath({ path: "/solutions/telecom-rooms-cabling-upgrading-solution-S10079.html" }))
                break
            case "S1010":
                redirect(app.localePath({ path: "/solutions/physical-layer-fiber-network-redundancy-solution-S10083.html" }))
                break
            case "S70":
                redirect(app.localePath({ path: "/solutions/all-optical-network-solution-for-enterprise-high-rise-office-space-S10085.html" }))
                break
            case "S95":
                redirect(app.localePath({ path: "/solutions/enterprise-network-traffic-monitoring-solution-S10086.html" }))
                break
            case "S11":
                redirect(app.localePath({ path: "/solutions/multi-tenant-data-center-mtdc-network-solution-S10090.html" }))
                break
            case "S79":
                redirect(app.localePath({ path: "/solutions/intelligent-1-1-optical-protection-solution-S10091.html" }))
                break
            case "S999":
                redirect(app.localePath({ path: "/solutions/campus-security-solution-S10099.html" }))
                break
            case "S10006":
                redirect(app.localePath({ path: "/solutions/high-performance-computing-hpc-network-solution-S10095.html" }))
                break
            case "S10031":
                redirect(app.localePath({ path: "/solutions/data-center-disaster-recovery-solution-for-optical-networks-S10097.html" }))
                break
            case "S10032":
                redirect(app.localePath({ path: "/solutions/hospitality-network-solution-S10094.html" }))
                break
            case "S10034":
                redirect(app.localePath({ path: "/solutions/fiber-optic-cabling-solutions-for-enterprise-data-centers-S10100.html" }))
                break
            case "S68":
                redirect(app.localePath({ path: "/solutions/passive-optical-lan-pol-solution-for-hotels-S10092.html" }))
                break
            case "S1002":
                redirect(app.localePath({ path: "/solutions/data-center-multi-rack-cabling-solution-S10103.html" }))
                break
            case "S10001":
                redirect(app.localePath({ path: "/solutions/industrial-automation-ring-network-solution-S10108.html" }))
                break
            case "S10022":
                redirect(app.localePath({ path: "/solutions/multi-service-network-isolation-solution-for-enterprise-S10106.html" }))
                break
            case "S1000":
                redirect(app.localePath({ path: "/solutions/scalable-and-flexible-network-virtualization-with-evpn-vxlan-in-cloud-data-centers-S10101.html" }))
                break
            case "S10068":
                redirect(app.localePath({ path: "/solutions/400g-ai-roce-data-center-networking-S3005.html" }))
            case "S10108":
                redirect(app.localePath({ path: "/solutions/industrial-automation-ring-network-solution-S10134.html" }))
            case "S19":
                redirect(app.localePath({ path: "/solutions/smb-video-surveillance-solution-S10137.html" }))
            case "S1017":
                redirect(app.localePath({ path: "/solutions/manufacturing-security-solution-S10141.html" }))
            case "S3000":
                redirect(app.localePath({ path: "/solutions/picos-for-h100-infiniband-solution-S3000.html" }))
            case "S3001":
                redirect(app.localePath({ path: "/solutions/infiniband-transceivers-and-cables-connectivity-solution-overview-S3001.html" }))
            case "S3002":
                redirect(app.localePath({ path: "/solutions/efficient-and-easy-to-use-100G-dwdm-pam4-solutions-S3002.html" }))
            case "S3003":
                redirect(app.localePath({ path: "/solutions/data-center-power-solution-S3003.html" }))
            case "S3004":
                redirect(app.localePath({ path: "/solutions/enterprise-office-buildings-surveillance-solution-S3004.html" }))
            case "S3005":
                redirect(app.localePath({ path: "/solutions/400g-ai-roce-data-center-networking-S3005.html" }))
            case "S3006":
                redirect(app.localePath({ path: "/solutions/seamless-video-streaming-experience-in-data-center-S3006.html" }))
                break
        }
        try {
            app.$axios.post(`/cms/api/fsSolution/browsing/history`, {
                url: route.path,
                shareUrlId: route.query.shareUrlId || "",
                // 1-solutions 2-case study
                type: 1,
            })
        } catch (err) {
            console.log(err)
        }

        if (["S2000", "S2001", "S2002", "S2003", "S2004", "S2005", "S2006", "S2007"].includes(tag_id)) {
            return app.$axios
                .get(`/api/solutionAgg/${tag_id}`)
                .then((res) => {
                    if (res && res.code == 200) {
                        return {
                            contentData: {
                                data: res.data,
                                is_group: true,
                                tag_id: tag_id,
                            },
                        }
                    } else {
                        redirect(app.localePath({ name: "404" }))
                    }
                })
                .catch((err) => {
                    console.log(err)
                })
        }
        // tag_id > 10000  cms - solution 走不同的接口
        if (Number(tag_id.replace("S", "")) > 10000) {
            let res = null,
                res1 = null
            res = await app.$axios.get(`/api/solutionNew/solutionDetail?solution_id=${tag_id}`)
            if (route.path !== app.$localeLink(res.data.page_url)) {
                redirect(app.localePath({ path: res.data.page_url }))
            }
            if (["Solutions02", "Solutions03"].includes(res.data.version)) {
                try {
                    // 请求/cms/api/fsSolution/detail这个接口时候，参数id不要加S
                    let { data } = await app.$axios.post(`/cms/api/fsSolution/detail`, {
                        id: String(tag_id).replace(/^S/, ""),
                        pageurl: "",
                    })
                    if (["S10084", "S10065"].includes(tag_id)) {
                        // 解决方案测试Demo申请入口，写死的，后期可能会做成组件
                        data.componentList.splice(3, 0, { typeId: 100, name: "解决方案测试Demo申请" })
                    }
                    if (["S10067"].includes(tag_id) && data.componentList.length > 4) {
                        // 解决方案细分场景，写死的，后期可能会做成组件
                        data.componentList.splice(4, 1, {
                            name: "解决方案细分场景",
                            id: data.componentList[4].id,
                            typeId: 101,
                            preview_list: $c("solutions.EnterpriseNetworkCabling"),
                        })
                    }
                    // console.log(12312311, "data", JSON.stringify(data))
                    return {
                        details: data,
                        data: res.data,
                        tag_id: tag_id,
                    }
                } catch (err) {
                    console.log(err)
                }
            }
            if (["S10006", "S10011", "S10005", "S10004", "S10013", "S10014", "S10012", "S10018", "S10035", "S10039", "S10036", "S10034", "S10046"].includes(tag_id)) {
                // 根据erp-solution 搜索左侧导航，再push写死solution，再push cms-solution
                res1 = await app.$axios.get(`/api/left_nav?plate=solutions&id=S94`)
                // 94 10 93 75 11
                res1.data.children.unshift($c("solutions.CloudDataCenterVxLAN.navData.children[0]"))
                if (warehouse === "US") {
                    // 1002 美东地区上新
                    res1.data.children.unshift($c("solutions.DCI.navData.children[0]"))
                }
                // 1003 上新
                res1.data.children.unshift($c("solutions.TwoTierNetworkArchitecture.navData.children[0]"))
                // cms 10006
                res1.data.children.unshift($c("solutions.CmsSolutions.headerPushEntry.HighComputingNetworkSolution.navData.children[0]"))
                // cms 10011
                res1.data.children.unshift($c("solutions.CmsSolutions.headerPushEntry.iPSANStorageNetworkingSolution.navData.children[0]"))
                // cms 10013
                res1.data.children.unshift($c("solutions.CmsSolutions.headerPushEntry.securityResourcePoolSolution.navData.children[0]"))
                // cms 10014
                res1.data.children.unshift($c("solutions.CmsSolutions.headerPushEntry.dataCenterCDNSolution.navData.children[0]"))
                // cms 10018
                res1.data.children.splice(-1, 0, $c("solutions.CmsSolutions.headerPushEntry.fourOneNetworkUpgrading.navData.children[0]"))
                // cms 10004
                res1.data.children.push($c("solutions.CmsSolutions.headerPushEntry.eightDataCenter.navData.children[0]"))
                // 1011 上新 修改为 10012
                res1.data.children.push($c("solutions.CmsSolutions.headerPushEntry.fourDaterCenter.navData.children[0]"))
                // 1006 上新
                res1.data.children.push($c("solutions.Transmission400G.navData.children[0]"))
                // cms 10005
                res1.data.children.push($c("solutions.CmsSolutions.headerPushEntry.oneDataCenter.navData.children[0]"))
                // 1005 上新
                res1.data.children.push($c("solutions.Transmission100G.navData.children[0]"))
                // 10035 10036 上新
                res1.data.children.unshift(...$c("solutions.CmsSolutions.headerPushEntry.dataCenterType.head"))
                res1.data.children.push(...$c("solutions.CmsSolutions.headerPushEntry.dataCenterType.back"))
            } else if (["S10002"].includes(tag_id)) {
                res1 = await app.$axios.get(`/api/left_nav?plate=solutions&id=S78`)
                // if (res?.data?.bread) {
                //     $c("solutions.CmsSolutions.headerPushEntry.cloudDataCenterUpgrade.bread")[2] = res.data.bread[2]
                //     res.data.bread = $c("solutions.CmsSolutions.headerPushEntry.cloudDataCenterUpgrade.bread")
                // }
                // 1010 上新
                res1.data.children.push($c("solutions.PhysicalLayerFiber.navData.children[3]"))
                // cms 10002
                res1.data.children.push($c("solutions.CmsSolutions.headerPushEntry.cloudDataCenterUpgrade.navData.children[0]"))
            } else if (["S10003", "S10041"].includes(tag_id)) {
                res1 = await app.$axios.get(`/api/left_nav?plate=solutions&id=S12`)
                // 10003
                res1.data.children.push($c("solutions.CmsSolutions.headerPushEntry.digitalCampusConstructionSolution.navData.children[0]"))
                // 999
                res1.data.children.push($c("solutions.VideoSurveillanceEducation.navData.children[4]"))
                // 10041
                res1.data.children.unshift(...$c("solutions.CmsSolutions.headerPushEntry.highEducation.head"))
            } else if (["S10010", "S10033"].includes(tag_id)) {
                res1 = await app.$axios.get(`/api/left_nav?plate=solutions&id=S6`)
                // 10010
                res1.data.children.push($c("solutions.CmsSolutions.headerPushEntry.outdoorWirelessNetwork.navData.children[0]"))
                // 10033
                res1.data.children.push(...$c("solutions.CmsSolutions.headerPushEntry.retailType.back"))
            } else if (["S10016", "S10019", "S10020", "S10027", "S10022", "S10030", "S10031", "S10038"].includes(tag_id)) {
                res1 = await app.$axios.get(`/api/left_nav?plate=solutions&id=S40`)
                // cms 10020 10019 10016
                res1.data.children.unshift(...$c("solutions.CmsSolutions.headerPushEntry.otnType.head"))
                // cms 10022 10030
                res1.data.children.push(...$c("solutions.CmsSolutions.headerPushEntry.otnType.back"))
            } else if (tag_id === "S10032") {
                res1 = await app.$axios.get(`/api/left_nav?plate=solutions&id=S68`)
                // cms 10032
                res1.data.children.push(...$c("solutions.CmsSolutions.headerPushEntry.hospitalityType.back"))
            } else if (["S10021", "S10023", "S10024", "S10028", "S10040", "S10042", "S10044"].includes(tag_id)) {
                res1 = {}
                res1.data = deepClone($c("solutions.SOHOOffice.navData"))
                // 1009
                res1.data.children.push($c("solutions.VideoSurveillanceSmallMidShoppingMall.navData.children[0]"))
                // 10021 10023
                res1.data.children.push(...$c("solutions.CmsSolutions.headerPushEntry.enterpeiseType"))
            } else if (["S10017", "S10009", "S10008", "S10047"].includes(tag_id)) {
                res1 = {}
                res1.data = deepClone($c("solutions.EnterpriseVideoSurveillance.navData"))
            }
            let data = res.data
            data.components.meta_keyword = data.meta_keyword || ""
            data.components.meta_tag = data.meta_tag || ""
            return {
                contentData: data.components,
                navData: res1?.data || data.leftNav,
                bread: data.bread,
                data,
                tag_id: tag_id,
            }
        }

        // let website = store.state.webSiteInfo.website
        // let warehouse = store.state.webSiteInfo.warehouse
        /* 写死新版solution */
        if (tag_id === "S999") {
            let navData = JSON.parse(JSON.stringify($c("solutions.VideoSurveillanceEducation.navData")))
            let flag = navData.children.some((item) => {
                return item.href === "solutions/digital-campus-construction-solution-10003.html"
            })
            if (!flag) {
                navData.children.splice(4, 0, $c("solutions.CmsSolutions.headerPushEntry.digitalCampusConstructionSolution.navData.children[0]"))
            }
            // 10041
            navData.children.unshift(...$c("solutions.CmsSolutions.headerPushEntry.highEducation.head"))
            return {
                tag_id: tag_id,
                contentData: $c("solutions.VideoSurveillanceEducation.contentData"),
                navData,
            }
        }

        // Enterprise下
        if (tag_id === "S1001") {
            let navData = deepClone($c("solutions.MediumAndLargeCampusWirelessNetwork.navData"))
            // 1008 上新 10028
            navData.children.push($c("solutions.SOHOOffice.navData.children[6]"))
            // 10042
            navData.children.push($c("solutions.SOHOOffice.navData.children[7]"))
            // 10044
            navData.children.push($c("solutions.SOHOOffice.navData.children[8]"))
            // 1009
            navData.children.push($c("solutions.VideoSurveillanceSmallMidShoppingMall.navData.children[0]"))
            // 10021 10023
            navData.children.push(...$c("solutions.CmsSolutions.headerPushEntry.enterpeiseType"))
            return {
                tag_id: tag_id,
                contentData: $c("solutions.MediumAndLargeCampusWirelessNetwork.contentData"),
                navData: navData,
            }
        }
        // if (tag_id === "1008" || tag_id === "1009") {
        if (tag_id === "S1009") {
            let navData = {}
            navData = deepClone($c("solutions.SOHOOffice.navData"))
            // 1009
            navData.children.push($c("solutions.VideoSurveillanceSmallMidShoppingMall.navData.children[0]"))
            // 10021 10023
            navData.children.push(...$c("solutions.CmsSolutions.headerPushEntry.enterpeiseType"))
            // if (tag_id === "1008") {
            //     return {
            //         tag_id: tag_id,
            //         contentData: $c("solutions.SOHOOffice.contentData"),
            //         navData: navData,
            //     }
            // }
            if (tag_id === "S1009") {
                return {
                    tag_id: tag_id,
                    contentData: $c("solutions.VideoSurveillanceSmallMidShoppingMall.contentData"),
                    navData: navData,
                }
            }
        }

        if (tag_id === "S1004" && website === "uk") {
            return {
                tag_id: tag_id,
                contentData: $c("solutions.OpticalCommunicationNetworkForSmartRailways.contentData"),
                navData: $c("solutions.OpticalCommunicationNetworkForSmartRailways.navData"),
            }
        }

        // Data Center 下
        if (tag_id === "S1000") {
            let navData = deepClone($c("solutions.CloudDataCenterVxLAN.navData"))
            if (warehouse === "US") {
                // 1002 美东地区上新
                navData.children.unshift($c("solutions.DCI.navData.children[0]"))
            }
            // 1003 上新
            navData.children.unshift($c("solutions.TwoTierNetworkArchitecture.navData.children[0]"))
            // cms 10006
            navData.children.unshift($c("solutions.CmsSolutions.headerPushEntry.HighComputingNetworkSolution.navData.children[0]"))
            // cms 10011
            navData.children.unshift($c("solutions.CmsSolutions.headerPushEntry.iPSANStorageNetworkingSolution.navData.children[0]"))
            // cms 10013
            navData.children.unshift($c("solutions.CmsSolutions.headerPushEntry.securityResourcePoolSolution.navData.children[0]"))
            // cms 10014
            navData.children.unshift($c("solutions.CmsSolutions.headerPushEntry.dataCenterCDNSolution.navData.children[0]"))
            // cms 10018
            navData.children.splice(-1, 0, $c("solutions.CmsSolutions.headerPushEntry.fourOneNetworkUpgrading.navData.children[0]"))
            // cms 10004
            navData.children.push($c("solutions.CmsSolutions.headerPushEntry.eightDataCenter.navData.children[0]"))
            // 1011 上新 修改为 10012
            navData.children.push($c("solutions.CmsSolutions.headerPushEntry.fourDaterCenter.navData.children[0]"))
            // 1006 上新
            navData.children.push($c("solutions.Transmission400G.navData.children[0]"))
            // cms 10005
            navData.children.push($c("solutions.CmsSolutions.headerPushEntry.oneDataCenter.navData.children[0]"))
            // 1005 上新
            navData.children.push($c("solutions.Transmission100G.navData.children[0]"))
            // 10035 10036 上新
            navData.children.unshift(...$c("solutions.CmsSolutions.headerPushEntry.dataCenterType.head"))
            navData.children.push(...$c("solutions.CmsSolutions.headerPushEntry.dataCenterType.back"))
            return {
                tag_id: tag_id,
                contentData: $c("solutions.CloudDataCenterVxLAN.contentData"),
                navData: navData,
            }
        }
        if (tag_id === "S1002" && warehouse === "US") {
            let navData = deepClone($c("solutions.DCI.navData"))
            // 1003 上新
            navData.children.unshift($c("solutions.TwoTierNetworkArchitecture.navData.children[0]"))
            // cms 10006
            navData.children.unshift($c("solutions.CmsSolutions.headerPushEntry.HighComputingNetworkSolution.navData.children[0]"))
            // cms 10011
            navData.children.unshift($c("solutions.CmsSolutions.headerPushEntry.iPSANStorageNetworkingSolution.navData.children[0]"))
            // cms 10013
            navData.children.unshift($c("solutions.CmsSolutions.headerPushEntry.securityResourcePoolSolution.navData.children[0]"))
            // cms 10014
            navData.children.unshift($c("solutions.CmsSolutions.headerPushEntry.dataCenterCDNSolution.navData.children[0]"))
            // cms 10018
            navData.children.splice(-1, 0, $c("solutions.CmsSolutions.headerPushEntry.fourOneNetworkUpgrading.navData.children[0]"))
            // cms 10004
            navData.children.push($c("solutions.CmsSolutions.headerPushEntry.eightDataCenter.navData.children[0]"))
            // 1011 上新 修改为 10012
            navData.children.push($c("solutions.CmsSolutions.headerPushEntry.fourDaterCenter.navData.children[0]"))
            // 1006 上新
            navData.children.push($c("solutions.Transmission400G.navData.children[0]"))
            // cms 10005
            navData.children.push($c("solutions.CmsSolutions.headerPushEntry.oneDataCenter.navData.children[0]"))
            // 1005 上新
            navData.children.push($c("solutions.Transmission100G.navData.children[0]"))
            // 10035 10036 上新
            navData.children.unshift(...$c("solutions.CmsSolutions.headerPushEntry.dataCenterType.head"))
            navData.children.push(...$c("solutions.CmsSolutions.headerPushEntry.dataCenterType.back"))
            return {
                tag_id: tag_id,
                contentData: $c("solutions.DCI.contentData"),
                navData: navData,
            }
        }
        if (tag_id === "S1003" || tag_id === "S1005" || tag_id === "S1006") {
            let navData = {}
            if (warehouse === "US") {
                navData = deepClone($c("solutions.DCI.navData"))
            } else {
                navData = deepClone($c("solutions.CloudDataCenterVxLAN.navData"))
            }
            navData.children.unshift($c("solutions.TwoTierNetworkArchitecture.navData.children[0]"))
            // cms 10018
            navData.children.splice(-1, 0, $c("solutions.CmsSolutions.headerPushEntry.fourOneNetworkUpgrading.navData.children[0]"))
            // cms 10004
            navData.children.push($c("solutions.CmsSolutions.headerPushEntry.eightDataCenter.navData.children[0]"))
            // 1011 上新 修改为 10012
            navData.children.push($c("solutions.CmsSolutions.headerPushEntry.fourDaterCenter.navData.children[0]"))
            // 1006
            navData.children.push($c("solutions.Transmission400G.navData.children[0]"))
            // cms 10005
            navData.children.push($c("solutions.CmsSolutions.headerPushEntry.oneDataCenter.navData.children[0]"))
            // 1005
            navData.children.push($c("solutions.Transmission100G.navData.children[0]"))
            // cms 10006
            navData.children.unshift($c("solutions.CmsSolutions.headerPushEntry.HighComputingNetworkSolution.navData.children[0]"))
            // cms 10011
            navData.children.unshift($c("solutions.CmsSolutions.headerPushEntry.iPSANStorageNetworkingSolution.navData.children[0]"))
            // cms 10013
            navData.children.unshift($c("solutions.CmsSolutions.headerPushEntry.securityResourcePoolSolution.navData.children[0]"))
            // cms 10014
            navData.children.unshift($c("solutions.CmsSolutions.headerPushEntry.dataCenterCDNSolution.navData.children[0]"))
            // 10035 10036 上新
            navData.children.unshift(...$c("solutions.CmsSolutions.headerPushEntry.dataCenterType.head"))
            navData.children.push(...$c("solutions.CmsSolutions.headerPushEntry.dataCenterType.back"))
            switch (tag_id) {
                case "S1003":
                    return {
                        tag_id: tag_id,
                        contentData: $c("solutions.TwoTierNetworkArchitecture.contentData"),
                        navData: navData,
                    }
                case "S1005":
                    return {
                        tag_id: tag_id,
                        contentData: $c("solutions.Transmission100G.contentData"),
                        navData: navData,
                    }
                case "S1006":
                    return {
                        tag_id: tag_id,
                        contentData: $c("solutions.Transmission400G.contentData"),
                        navData: navData,
                    }
                // case "1011":
                //     return {
                //         tag_id: tag_id,
                //         contentData: $c("solutions.MigrationHighDensity400G.contentData"),
                //         navData: navData,
                //     }
                default:
                    break
            }
        }

        if (tag_id === "S1007") {
            return {
                tag_id: tag_id,
                contentData: $c("solutions.Wireless5G.contentData"),
                navData: $c("solutions.Wireless5G.navData"),
            }
        }

        if (tag_id === "S1010") {
            let nav = $c("solutions.PhysicalLayerFiber.navData")
            // cms 10002
            let flag = nav.children.some((item) => {
                return item.href === "/solutions/cloud-data-center-network-upgrade-solution-10002.html"
            })
            if (!flag) {
                nav.children.push($c("solutions.CmsSolutions.headerPushEntry.cloudDataCenterUpgrade.navData.children[0]"))
            }
            return {
                tag_id: tag_id,
                contentData: $c("solutions.PhysicalLayerFiber.contentData"),
                navData: nav,
            }
        }
        if (tag_id === "S1012") {
            let navData = deepClone($c("solutions.TelecomRoomsCablingUpgradingSolution.navData"))
            // cms 10006
            navData.children.unshift($c("solutions.CmsSolutions.headerPushEntry.HighComputingNetworkSolution.navData.children[0]"))
            // cms 10011
            navData.children.unshift($c("solutions.CmsSolutions.headerPushEntry.iPSANStorageNetworkingSolution.navData.children[0]"))
            // cms 10013
            navData.children.unshift($c("solutions.CmsSolutions.headerPushEntry.securityResourcePoolSolution.navData.children[0]"))
            // cms 10014
            navData.children.unshift($c("solutions.CmsSolutions.headerPushEntry.dataCenterCDNSolution.navData.children[0]"))
            // cms 10018
            navData.children.splice(-1, 0, $c("solutions.CmsSolutions.headerPushEntry.fourOneNetworkUpgrading.navData.children[0]"))
            // 10004  1011修改为10012 1006 10005 1005 顺序
            // cms 10004
            navData.children.splice(11, 0, $c("solutions.CmsSolutions.headerPushEntry.eightDataCenter.navData.children[0]"))
            // cms 10005
            navData.children.splice(13, 0, $c("solutions.CmsSolutions.headerPushEntry.oneDataCenter.navData.children[0]"))
            // 10035 10036 上新
            navData.children.unshift(...$c("solutions.CmsSolutions.headerPushEntry.dataCenterType.head"))
            navData.children.push(...$c("solutions.CmsSolutions.headerPushEntry.dataCenterType.back"))
            let content = $c("solutions.TelecomRoomsCablingUpgradingSolution.contentData")
            if (website == "es") {
                content.tags.content[0].tagData.points_data = [
                    {
                        scene_id: 6559,
                        points_left: "19.5",
                        points_top: "36",
                        direction: "middle-left",
                        main_products_id: "105333",
                        other_products_id: "",
                        main_products_id_array: "105333",
                        show_type: 0,
                        products_tag_info: [
                            {
                                products_id: 105333,
                                products_images: "https://resource.fs.com/mall/products/800x800/105333.main.jpg",
                                products_price: "249,00 €",
                                products_name: "Cassette FHD 3 x MTP®-12, 36 fibras OS2 monomodo, tipo A, 3 x 12F MTP® a 18 x LC dúplex (azul) con tapa shutter, máx. 0.35dB",
                                products_type: "FHD-3MTP18LCDSMFA",
                                products_offline_status: 1,
                            },
                        ],
                        show_popup: false,
                    },
                    {
                        scene_id: 6559,
                        points_left: "32",
                        points_top: "34",
                        direction: "middle-left",
                        main_products_id: "70361",
                        other_products_id: "",
                        main_products_id_array: "70361",
                        show_type: 0,
                        products_tag_info: [
                            {
                                products_id: 70361,
                                products_images: "https://resource.fs.com/mall/products/800x800/70361.main.jpg",
                                products_price: "159,00 €",
                                products_name: "Distribuidor FHD de alta densidad 1U montaje en rack descargado, tipo slide-out, permite hasta 4 x cassettes o paneles FHD, 144 fibras (LC)",
                                products_type: "FHD-1UFCE",
                                products_offline_status: 1,
                            },
                        ],
                        show_popup: false,
                    },
                    {
                        scene_id: 6559,
                        points_left: "21",
                        points_top: "53",
                        direction: "middle-left",
                        main_products_id: "40191",
                        other_products_id: "",
                        main_products_id_array: "40191",
                        show_type: 0,
                        products_tag_info: [
                            {
                                products_id: 40191,
                                products_images: "https://resource.fs.com/mall/products/800x800/40191.main.jpg",
                                products_price: "4,30 €",
                                products_name: "Cable/latiguillo/jumper de fibra óptica LC UPC a LC UPC 1m OS2 9/125 dúplex monomodo PVC (OFNR) 2.0mm",
                                products_type: "SMLCDX",
                                products_offline_status: 1,
                            },
                        ],
                        show_popup: false,
                    },
                    {
                        scene_id: 6559,
                        points_left: "22",
                        points_top: "66",
                        direction: "middle-left",
                        main_products_id: "11555",
                        other_products_id: "",
                        main_products_id_array: "11555",
                        show_type: 0,
                        products_tag_info: [
                            {
                                products_id: 11555,
                                products_images: "https://resource.fs.com/mall/products/550x550/11555.main.jpg",
                                products_price: "27,00 €",
                                products_name: "Módulo transceptor/Transceiver óptico compatible con Cisco SFP-10G-LR, 10GBASE-LR SFP+ 1310nm 10km DOM LC dúplex SMF ",
                                products_type: "SFP-10GLR-31",
                                products_offline_status: 1,
                            },
                        ],
                        show_popup: false,
                    },
                    {
                        scene_id: 6559,
                        points_left: "36",
                        points_top: "44",
                        direction: "middle-left",
                        main_products_id: "40214",
                        other_products_id: "",
                        main_products_id_array: "40214",
                        show_type: 0,
                        products_tag_info: [
                            {
                                products_id: 40214,
                                products_images: "https://resource.fs.com/mall/products/800x800/40214.main.jpg",
                                products_price: "4,80 €",
                                products_name: "Cable/latiguillo/jumper de fibra óptica LC UPC a SC UPC 1m OS2 9/125 dúplex monomodo PVC (OFNR) 2.0mm",
                                products_type: "SMLCSCDX",
                                products_offline_status: 1,
                            },
                        ],
                        show_popup: false,
                    },
                    {
                        scene_id: 6559,
                        points_left: "54",
                        points_top: "17",
                        direction: "middle-left",
                        main_products_id: "30976",
                        other_products_id: "",
                        main_products_id_array: "30976",
                        show_type: 0,
                        products_tag_info: [
                            {
                                products_id: 30976,
                                products_images: "https://resource.fs.com/mall/mainImg/20220805182047e7non6.jpg",
                                products_price: "76,00 €",
                                products_name: "Cable troncal de fibra óptica OS2 monomodo 12 fibras MTP® 8-144 fibras personalizado - 3.0mm, amarillo",
                                products_type: "12FMTPSMF",
                                products_offline_status: 1,
                            },
                        ],
                        show_popup: false,
                    },
                ]
                content.tags.content[1].tagData.points_data = [
                    {
                        scene_id: 6559,
                        points_left: "54.17",
                        points_top: "19.87",
                        direction: "middle-left",
                        main_products_id: "40829",
                        other_products_id: "",
                        main_products_id_array: "40829",
                        show_type: 0,
                        products_tag_info: [
                            {
                                products_id: 40829,
                                products_images: "https://resource.fs.com/mall/products/800x800/40829.main.jpg",
                                products_price: "2,70 €",
                                products_name: "Cable/latiguillo/jumper de fibra óptica LC UPC a FC UPC 1m OS2 9/125 símplex monomodo PVC (OFNR) 2.0mm",
                                products_type: "SMLCFCSX",
                                products_offline_status: 1,
                            },
                        ],
                        show_popup: false,
                    },
                    {
                        scene_id: 6559,
                        points_left: "17",
                        points_top: "41",
                        direction: "middle-left",
                        main_products_id: "40446",
                        other_products_id: "",
                        main_products_id_array: "40446",
                        show_type: 0,
                        products_tag_info: [
                            {
                                products_id: 40446,
                                products_images: "https://resource.fs.com/mall/products/800x800/40446.main.jpg",
                                products_price: "2,70 €",
                                products_name: "Cable/latiguillo/jumper de fibra óptica LC UPC a LC UPC 1m OS2 9/125 símplex monomodo PVC (OFNR) 2.0mm",
                                products_type: "SMLCSX",
                                products_offline_status: 1,
                            },
                        ],
                        show_popup: false,
                    },
                    {
                        scene_id: 6559,
                        points_left: "19",
                        points_top: "54.5",
                        direction: "middle-left",
                        main_products_id: "48812",
                        other_products_id: "",
                        main_products_id_array: "48812",
                        show_type: 0,
                        products_tag_info: [
                            {
                                products_id: 48812,
                                products_images: "https://resource.fs.com/mall/products/550x550/48812.main.jpg",
                                products_price: "24,00 €",
                                products_name: "Módulo transceptor/Transceiver óptico compatible con Cisco SFP-10G-LRM2, 10GBASE-LRM SFP+ 1310nm 2km DOM LC dúplex SMF ",
                                products_type: "SFP-10GLRM-31",
                                products_offline_status: 1,
                            },
                        ],
                        show_popup: false,
                    },
                ]
            }
            if (website == "mx") {
                content.tags.content[0].tagData.points_data = [
                    {
                        scene_id: 6559,
                        points_left: "19.5",
                        points_top: "36",
                        direction: "middle-left",
                        main_products_id: "105333",
                        other_products_id: "",
                        main_products_id_array: "105333",
                        show_type: 0,
                        products_tag_info: [
                            {
                                products_id: 105333,
                                products_images: "https://resource.fs.com/mall/products/800x800/105333.main.jpg",
                                products_price: "MXN$5,168",
                                products_name: "Cassette FHD 3 x MTP®-12, 36 fibras OS2 monomodo, tipo A, 3 x 12F MTP® a 18 x LC dúplex (azul) con tapa shutter, máx. 0.35dB",
                                products_type: "FHD-3MTP18LCDSMFA",
                                products_offline_status: 1,
                            },
                        ],
                        show_popup: false,
                    },
                    {
                        scene_id: 6559,
                        points_left: "32",
                        points_top: "34",
                        direction: "middle-left",
                        main_products_id: "70361",
                        other_products_id: "",
                        main_products_id_array: "70361",
                        show_type: 0,
                        products_tag_info: [
                            {
                                products_id: 70361,
                                products_images: "https://resource.fs.com/mall/products/800x800/70361.main.jpg",
                                products_price: "MXN$3,300",
                                products_name: "Distribuidor FHD de alta densidad 1U montaje en rack descargado, tipo slide-out, permite hasta 4 x cassettes o paneles FHD, 144 fibras (LC)",
                                products_type: "FHD-1UFCE",
                                products_offline_status: 1,
                            },
                        ],
                        show_popup: false,
                    },
                    {
                        scene_id: 6559,
                        points_left: "21",
                        points_top: "53",
                        direction: "middle-left",
                        main_products_id: "40191",
                        other_products_id: "",
                        main_products_id_array: "40191",
                        show_type: 0,
                        products_tag_info: [
                            {
                                products_id: 40191,
                                products_images: "https://resource.fs.com/mall/products/800x800/40191.main.jpg",
                                products_price: "MXN$89",
                                products_name: "Cable/latiguillo/jumper de fibra óptica LC UPC a LC UPC 1m OS2 9/125 dúplex monomodo PVC (OFNR) 2.0mm",
                                products_type: "SMLCDX",
                                products_offline_status: 1,
                            },
                        ],
                        show_popup: false,
                    },
                    {
                        scene_id: 6559,
                        points_left: "22",
                        points_top: "66",
                        direction: "middle-left",
                        main_products_id: "11555",
                        other_products_id: "",
                        main_products_id_array: "11555",
                        show_type: 0,
                        products_tag_info: [
                            {
                                products_id: 11555,
                                products_images: "https://resource.fs.com/mall/products/550x550/11555.main.jpg",
                                products_price: "MXN$560",
                                products_name: "Módulo transceptor/Transceiver óptico compatible con Cisco SFP-10G-LR, 10GBASE-LR SFP+ 1310nm 10km DOM LC dúplex SMF ",
                                products_type: "SFP-10GLR-31",
                                products_offline_status: 1,
                            },
                        ],
                        show_popup: false,
                    },
                    {
                        scene_id: 6559,
                        points_left: "36",
                        points_top: "44",
                        direction: "middle-left",
                        main_products_id: "40214",
                        other_products_id: "",
                        main_products_id_array: "40214",
                        show_type: 0,
                        products_tag_info: [
                            {
                                products_id: 40214,
                                products_images: "https://resource.fs.com/mall/products/800x800/40214.main.jpg",
                                products_price: "MXN$100",
                                products_name: "Cable/latiguillo/jumper de fibra óptica LC UPC a SC UPC 1m OS2 9/125 dúplex monomodo PVC (OFNR) 2.0mm",
                                products_type: "SMLCSCDX",
                                products_offline_status: 1,
                            },
                        ],
                        show_popup: false,
                    },
                    {
                        scene_id: 6559,
                        points_left: "54",
                        points_top: "17",
                        direction: "middle-left",
                        main_products_id: "30976",
                        other_products_id: "",
                        main_products_id_array: "30976",
                        show_type: 0,
                        products_tag_info: [
                            {
                                products_id: 30976,
                                products_images: "https://resource.fs.com/mall/mainImg/20220805182047e7non6.jpg",
                                products_price: "MXN$1,580",
                                products_name: "Cable troncal de fibra óptica OS2 monomodo 12 fibras MTP® 8-144 fibras personalizado - 3.0mm, amarillo",
                                products_type: "12FMTPSMF",
                                products_offline_status: 1,
                            },
                        ],
                        show_popup: false,
                    },
                    {
                        //下线
                        scene_id: 6559,
                        points_left: "18",
                        points_top: "60",
                        direction: "middle-left",
                        main_products_id: "119617",
                        other_products_id: "",
                        main_products_id_array: "119617",
                        show_type: 0,
                        products_tag_info: [
                            {
                                products_id: 119617,
                                products_images: "https://resource.fs.com/mall/products/800x800/119617.main.jpg",
                                products_price: "MXN$1,308",
                                products_name: "PDU básica monofásica 15A/125V, 8 tomacorrientes de NEMA 5-15R, enchufe de NEMA 5-15P, cable de 6.6ft, montaje en rack de 1U",
                                products_type: "PDU-5158N-HBS",
                                products_offline_status: 1,
                            },
                        ],
                        show_popup: false,
                    },
                    {
                        //下线
                        scene_id: 6559,
                        points_left: "38",
                        points_top: "69",
                        direction: "middle-left",
                        main_products_id: "73958",
                        other_products_id: "",
                        main_products_id_array: "73958",
                        show_type: 0,
                        products_tag_info: [
                            {
                                products_id: 73958,
                                products_images: "https://resource.fs.com/mall/products/550x550/73958.main.jpg",
                                products_price: "MXN$22,189",
                                products_name: "Gabinete para servidor 42U serie GR600 negro 600 x 1100mm con 2 soportes PDU y estantes fijos ajustables",
                                products_type: "FS-BSC-42UW60",
                                products_offline_status: 1,
                            },
                        ],
                        show_popup: false,
                    },
                ]
                content.tags.content[1].tagData.points_data = [
                    {
                        scene_id: 6559,
                        points_left: "54.17",
                        points_top: "19.87",
                        direction: "middle-left",
                        main_products_id: "40829",
                        other_products_id: "",
                        main_products_id_array: "40829",
                        show_type: 0,
                        products_tag_info: [
                            {
                                products_id: 40829,
                                products_images: "https://resource.fs.com/mall/products/800x800/40829.main.jpg",
                                products_price: "MXN$56",
                                products_name: "Cable/latiguillo/jumper de fibra óptica LC UPC a FC UPC 1m OS2 9/125 símplex monomodo PVC (OFNR) 2.0mm",
                                products_type: "SMLCFCSX",
                                products_offline_status: 1,
                            },
                        ],
                        show_popup: false,
                    },
                    {
                        scene_id: 6559,
                        points_left: "17",
                        points_top: "41",
                        direction: "middle-left",
                        main_products_id: "40446",
                        other_products_id: "",
                        main_products_id_array: "40446",
                        show_type: 0,
                        products_tag_info: [
                            {
                                products_id: 40446,
                                products_images: "https://resource.fs.com/mall/products/800x800/40446.main.jpg",
                                products_price: "MXN$56",
                                products_name: "Cable/latiguillo/jumper de fibra óptica LC UPC a LC UPC 1m OS2 9/125 símplex monomodo PVC (OFNR) 2.0mm",
                                products_type: "SMLCSX",
                                products_offline_status: 1,
                            },
                        ],
                        show_popup: false,
                    },
                    {
                        //下线
                        scene_id: 6559,
                        points_left: "33",
                        points_top: "36",
                        direction: "middle-left",
                        main_products_id: "119617",
                        other_products_id: "",
                        main_products_id_array: "119617",
                        show_type: 0,
                        products_tag_info: [
                            {
                                products_id: 119617,
                                products_images: "https://resource.fs.com/mall/products/800x800/119617.main.jpg",
                                products_price: "MXN$1,308",
                                products_name: "PDU básica monofásica 15A/125V, 8 tomacorrientes de NEMA 5-15R, enchufe de NEMA 5-15P, cable de 6.6ft, montaje en rack de 1U",
                                products_type: "PDU-5158N-HBS",
                                products_offline_status: 1,
                            },
                        ],
                        show_popup: false,
                    },
                    {
                        scene_id: 6559,
                        points_left: "19",
                        points_top: "54.5",
                        direction: "middle-left",
                        main_products_id: "48812",
                        other_products_id: "",
                        main_products_id_array: "48812",
                        show_type: 0,
                        products_tag_info: [
                            {
                                products_id: 48812,
                                products_images: "https://resource.fs.com/mall/products/550x550/48812.main.jpg",
                                products_price: "MXN$498",
                                products_name: "Módulo transceptor/Transceiver óptico compatible con Cisco SFP-10G-LRM2, 10GBASE-LRM SFP+ 1310nm 2km DOM LC dúplex SMF ",
                                products_type: "SFP-10GLRM-31",
                                products_offline_status: 1,
                            },
                        ],
                        show_popup: false,
                    },
                ]
                content.data.featureCase.content[1].button.href = "https://www.fs.com/mx/solutions/Data-Center-Structured-Cabling-Solution-75.html"
            }
            return {
                tag_id: tag_id,
                contentData: content,
                navData: navData,
            }
        }
        if (tag_id === "S1013") {
            let navData = deepClone($c("solutions.MediumLarge10GigabitCampusNetworkSolution.navData"))
            navData.children.push($c("solutions.VideoSurveillanceSmallMidShoppingMall.navData.children[0]"))
            return {
                tag_id: tag_id,
                contentData: $c("solutions.MediumLarge10GigabitCampusNetworkSolution.contentData"),
                navData: navData,
            }
        }
        if (tag_id === "S1014") {
            let navData = deepClone($c("solutions.WirelessFronthaulOpticalTransceiverModuleSolution4G.navData"))
            // navData.children.push($c("solutions.VideoSurveillanceSmallMidShoppingMall.navData.children[0]"))
            return {
                tag_id: tag_id,
                contentData: $c("solutions.WirelessFronthaulOpticalTransceiverModuleSolution4G.contentData"),
                navData: navData,
            }
        }
        if (tag_id === "S1015") {
            let navData = deepClone($c("solutions.ConvergedWirelessFronthaulOpticalTransceiverModuleSolution4G5G.navData"))
            return {
                tag_id: tag_id,
                contentData: $c("solutions.ConvergedWirelessFronthaulOpticalTransceiverModuleSolution4G5G.contentData"),
                navData: navData,
            }
        }
        if (tag_id === "S1016") {
            let navData = deepClone($c("solutions.EnterpriseVideoSurveillance.navData"))
            return {
                tag_id: tag_id,
                contentData: $c("solutions.EnterpriseVideoSurveillance.contentData"),
                navData: navData,
            }
        }
        if (tag_id === "S1017") {
            let navData = deepClone($c("solutions.EnterpriseVideoSurveillance.navData"))
            return {
                tag_id: tag_id,
                contentData: $c("solutions.MediumAndLargeIndustrialParks.contentData"),
                navData: navData,
            }
        }
        /* 写死新版solution */

        let [res, res1] = await Promise.all([$axios.get(`/api/solution/${tag_id}`), $axios.get(`/api/left_nav?plate=solutions&id=${tag_id}`)])

        /* 写死新版solution */
        if (tag_id === "S12" || tag_id === "S3" || tag_id === "S16" || tag_id === "S38") {
            // 10003
            res1.data.children.push($c("solutions.CmsSolutions.headerPushEntry.digitalCampusConstructionSolution.navData.children[0]"))
            // 999
            res1.data.children.push($c("solutions.VideoSurveillanceEducation.navData.children[4]"))
            // 10041
            res1.data.children.unshift(...$c("solutions.CmsSolutions.headerPushEntry.highEducation.head"))
        }

        // if (tag_id === "94" || tag_id === "10" || tag_id === "93" || tag_id === "75" || tag_id === "11") {
        //     // 1000
        //     res1.data.children.unshift($c("solutions.CloudDataCenterVxLAN.navData.children[0]"))
        // }

        if (tag_id === "S8" || tag_id === "S47" || tag_id === "S70" || tag_id === "S97" || tag_id === "S95" || tag_id === "S44") {
            // 1001
            res1.data.children.push($c("solutions.MediumAndLargeCampusWirelessNetwork.navData.children[5]"))

            // 1008 10028
            res1.data.children.push($c("solutions.SOHOOffice.navData.children[6]"))
            // 10042
            res1.data.children.push($c("solutions.SOHOOffice.navData.children[7]"))
            // 10044
            res1.data.children.push($c("solutions.SOHOOffice.navData.children[8]"))

            // 1009
            res1.data.children.push($c("solutions.VideoSurveillanceSmallMidShoppingMall.navData.children[0]"))

            // 10021 10023
            res1.data.children.push(...$c("solutions.CmsSolutions.headerPushEntry.enterpeiseType"))
        }

        if (tag_id === "S94" || tag_id === "S10" || tag_id === "S93" || tag_id === "S75" || tag_id === "S11") {
            // 1000
            res1.data.children.unshift($c("solutions.CloudDataCenterVxLAN.navData.children[0]"))
            if (warehouse === "US") {
                // 1002 美东地区上新
                res1.data.children.unshift($c("solutions.DCI.navData.children[0]"))
            }
            // 1003 上新
            res1.data.children.unshift($c("solutions.TwoTierNetworkArchitecture.navData.children[0]"))
            // cms 10006
            res1.data.children.unshift($c("solutions.CmsSolutions.headerPushEntry.HighComputingNetworkSolution.navData.children[0]"))
            // cms 10011
            res1.data.children.unshift($c("solutions.CmsSolutions.headerPushEntry.iPSANStorageNetworkingSolution.navData.children[0]"))
            // cms 10013
            res1.data.children.unshift($c("solutions.CmsSolutions.headerPushEntry.securityResourcePoolSolution.navData.children[0]"))
            // cms 10014
            res1.data.children.unshift($c("solutions.CmsSolutions.headerPushEntry.dataCenterCDNSolution.navData.children[0]"))
            // cms 10018
            res1.data.children.splice(-1, 0, $c("solutions.CmsSolutions.headerPushEntry.fourOneNetworkUpgrading.navData.children[0]"))
            // cms 10004
            res1.data.children.push($c("solutions.CmsSolutions.headerPushEntry.eightDataCenter.navData.children[0]"))
            // 1011 上新 修改为 10012
            res1.data.children.push($c("solutions.CmsSolutions.headerPushEntry.fourDaterCenter.navData.children[0]"))
            // 1006 上新
            res1.data.children.push($c("solutions.Transmission400G.navData.children[0]"))
            // cms 10005
            res1.data.children.push($c("solutions.CmsSolutions.headerPushEntry.oneDataCenter.navData.children[0]"))
            // 1005 上新
            res1.data.children.push($c("solutions.Transmission100G.navData.children[0]"))
            // 10035 10036 上新
            res1.data.children.unshift(...$c("solutions.CmsSolutions.headerPushEntry.dataCenterType.head"))
            res1.data.children.push(...$c("solutions.CmsSolutions.headerPushEntry.dataCenterType.back"))
        }

        if (tag_id === "S40" && warehouse === "US") {
            // 此专题紧美东地区上线
            res1.data.children[0] = $c("solutions.HighCapacityOTN.navData.children[0]")
            // cms 10020 10019 10016
            res1.data.children.unshift(...$c("solutions.CmsSolutions.headerPushEntry.otnType.head"))
            // cms 10022 10030
            res1.data.children.push(...$c("solutions.CmsSolutions.headerPushEntry.otnType.back"))
            return {
                tag_id: tag_id,
                contentData: $c("solutions.HighCapacityOTN.contentData"),
                navData: res1.data,
            }
        }

        if (tag_id === "S40" || tag_id === "S46" || tag_id === "S69" || tag_id === "S23") {
            if (website === "uk") {
                // 1004 上新 仅英国站
                res1.data.children.push($c("solutions.OpticalCommunicationNetworkForSmartRailways.navData.children[5]"))
            }
            // cms 10020 10019 10016
            res1.data.children.unshift(...$c("solutions.CmsSolutions.headerPushEntry.otnType.head"))
            // cms 10022 10030
            res1.data.children.push(...$c("solutions.CmsSolutions.headerPushEntry.otnType.back"))
        }

        if (tag_id === "S1" || tag_id === "S101" || tag_id === "S20") {
            // 1007 上新
            res1.data.children.unshift($c("solutions.Wireless5G.navData.children[0]"), $c("solutions.Wireless5G.navData.children[1]"), $c("solutions.Wireless5G.navData.children[2]"))
        }

        if (tag_id === "S14" || tag_id === "S78" || tag_id === "S56") {
            // 1010 上新
            res1.data.children.push($c("solutions.PhysicalLayerFiber.navData.children[3]"))
            // cms 10002
            res1.data.children.push($c("solutions.CmsSolutions.headerPushEntry.cloudDataCenterUpgrade.navData.children[0]"))
        }
        if (tag_id === "S19" || tag_id === "S4" || tag_id === "S45" || tag_id === "S17" || tag_id === "S74") {
            // 1007 上新
            res1.data.children.push(...$c("solutions.EnterpriseVideoSurveillance.navData.children").slice(5))
        }
        /* 写死新版solution */

        // 10010 cms加在 Entertainment 下，需要在下面的几个solution加上10010
        if (tag_id === "S5" || tag_id === "S13" || tag_id === "S100" || tag_id === "S6" || tag_id === "S73") {
            // 1007 上新
            res1.data.children.push($c("solutions.CmsSolutions.headerPushEntry.outdoorWirelessNetwork.navData.children[0]"))
            // 10033
            res1.data.children.push(...$c("solutions.CmsSolutions.headerPushEntry.retailType.back"))
        }

        /* 新加坡图片本地替换, 图片顺序 pc m pad */
        let sgBanner = {
            S12: [
                "https://resource.fs.com/mall/generalImg/20230322111105s82yuk.jpg",
                "https://resource.fs.com/mall/generalImg/202303241122398zqxsz.jpg",
                "https://resource.fs.com/mall/generalImg/20230322111105r9lmt8.jpg",
            ],
            S38: [
                "https://resource.fs.com/mall/generalImg/20230322111105e9kcne.jpg",
                "https://resource.fs.com/mall/generalImg/20230324112239zdkktj.jpg",
                "https://resource.fs.com/mall/generalImg/20230322111105b6erou.jpg",
            ],
            S16: [
                "https://resource.fs.com/mall/generalImg/20230322111105dgyt2j.jpg",
                "https://resource.fs.com/mall/generalImg/20230324112239jc9kvn.jpg",
                "https://resource.fs.com/mall/generalImg/20230322111105shkw5f.jpg",
            ],
            S101: [
                "https://resource.fs.com/mall/generalImg/20230322111105w1m9hq.jpg",
                "https://resource.fs.com/mall/generalImg/202303241122396sca5r.jpg",
                "https://resource.fs.com/mall/generalImg/202303221111052ydtha.jpg",
            ],
            S3: [
                "https://resource.fs.com/mall/generalImg/20230322111105ftee55.jpg",
                "https://resource.fs.com/mall/generalImg/202303241122399doss4.jpg",
                "https://resource.fs.com/mall/generalImg/20230322111105686u0o.jpg",
            ],
        }
        if (website === "sg" && (tag_id === "S12" || tag_id === "S38" || tag_id === "S16" || tag_id === "S101" || tag_id === "S3")) {
            for (let key of Object.keys(sgBanner)) {
                if (tag_id === key) {
                    sgBanner[key].forEach((v, i) => {
                        res.data.banners[i].img = v
                    })
                }
            }
        }
        /* 新加坡图片本地替换 */

        let cnBanner = {
            //   PC m pad
            S1: [
                "https://resource.fs.com/mall/generalImg/20230829164927p3qden.png", //   PC
                "https://resource.fs.com/mall/generalImg/20230908170606p5m3mm.png", //   M
                "https://resource.fs.com/mall/generalImg/20230908170606vo696g.png", //   pad
            ],
            S5: [
                "https://resource.fs.com/mall/generalImg/20230830155723all7u5.png", //   PC
                "https://resource.fs.com/mall/generalImg/20230830155723yrp1s3.png", //   M
                "https://resource.fs.com/mall/generalImg/202308301557239gfhvr.png", //   pad
            ],
            S6: [
                "https://resource.fs.com/mall/generalImg/20230829180638nw3ylg.png", //   PC
                "https://resource.fs.com/mall/generalImg/20230829180638aycvpr.png", //   M
                "https://resource.fs.com/mall/generalImg/20230829180638apdr6i.png", //   pad
            ],
            S9: [
                "https://resource.fs.com/mall/generalImg/20230829175440uq8o24.png", //   PC
                "https://resource.fs.com/mall/generalImg/20230829175440zxs7a9.png", //   M
                "https://resource.fs.com/mall/generalImg/202308291754404l7kab.png", //   pad
            ],
            S11: [
                "https://resource.fs.com/mall/generalImg/20230830160611gjsxle.png", //   PC
                "https://resource.fs.com/mall/generalImg/20230830160611reh729.png", //   M
                "https://resource.fs.com/mall/generalImg/20230830160611gtouz1.png", //   pad
            ],
            S12: [
                "https://resource.fs.com/mall/generalImg/202308291529447zovx1.png", //   PC
                "https://resource.fs.com/mall/generalImg/20230829152944ehi7i6.png", //   M
                "https://resource.fs.com/mall/generalImg/2023082915294440hp58.png", //   pad
            ],
            S14: [
                "https://resource.fs.com/mall/generalImg/20230830155947421sr6.png", //   PC
                "https://resource.fs.com/mall/generalImg/20230830155947swgrqo.png", //   M
                "https://resource.fs.com/mall/generalImg/20230830155947jhbct1.png", //   pad
            ],
            S16: [
                "https://resource.fs.com/mall/generalImg/20230829155359ne30y7.png", //   PC
                "https://resource.fs.com/mall/generalImg/20230908153129onlu84.png", //   M
                "https://resource.fs.com/mall/generalImg/2023082915535943uzv6.png", //   pad
            ],
            S17: [
                "https://resource.fs.com/mall/generalImg/20230830163057xzptsq.png", //   PC
                "https://resource.fs.com/mall/generalImg/20230830163057ehs3l7.png", //   M
                "https://resource.fs.com/mall/generalImg/202308301630570whrda.png", //   pad
            ],
            S23: [
                "https://resource.fs.com/mall/generalImg/202308301608071twise.png", //   PC
                "https://resource.fs.com/mall/generalImg/20230830160807pugq67.png", //   M
                "https://resource.fs.com/mall/generalImg/202308301608078a21vi.png", //   pad
            ],
            S38: [
                "https://resource.fs.com/mall/generalImg/20230908163034meh6ps.png", //   PC
                "https://resource.fs.com/mall/generalImg/20230908163034bcu6ff.png", //   M
                "https://resource.fs.com/mall/generalImg/20230908163034cpi43r.png", //   pad
            ],
            S40: [
                "https://resource.fs.com/mall/generalImg/20230829165101oc7tr8.png", //   PC
                "https://resource.fs.com/mall/generalImg/20230829165101fwt07k.png", //   M
                "https://resource.fs.com/mall/generalImg/20230829165101qj728k.png", //   pad
            ],
            S41: [
                "https://resource.fs.com/mall/generalImg/20230829164412n9bc5o.png", //   PC
                "https://resource.fs.com/mall/generalImg/20230829164412x2aljg.png", //   M
                "https://resource.fs.com/mall/generalImg/20230829164412pv3yb4.png", //   pad
            ],
            S46: [
                "https://resource.fs.com/mall/generalImg/20230829182226ig9ib4.png", //   PC
                "https://resource.fs.com/mall/generalImg/202308291822264ifsz4.png", //   M
                "https://resource.fs.com/mall/generalImg/20230829182226tbvb2a.png", //   pad
            ],
            S47: [
                "https://resource.fs.com/mall/generalImg/20230908165924c9ipg9.png", //   PC
                "https://resource.fs.com/mall/generalImg/202309081659242qt1l7.png", //   M
                "https://resource.fs.com/mall/generalImg/20230908165924xt3qcd.png", //   pad
            ],
            S56: [
                "https://resource.fs.com/mall/generalImg/20230829181841rvhlnc.png", //   PC
                "https://resource.fs.com/mall/generalImg/202308291818412uuzhp.png", //   M
                "https://resource.fs.com/mall/generalImg/20230829181841g3rps2.png", //   pad
            ],
            S69: [
                "https://resource.fs.com/mall/generalImg/20230829181057ev2kzb.png", //   PC
                "https://resource.fs.com/mall/generalImg/20230829181057d2zwwu.png", //   M
                "https://resource.fs.com/mall/generalImg/20230829181057cfmuzf.png", //   pad
            ],
            S96: [
                "https://resource.fs.com/mall/generalImg/20230829175058j96q0j.png", //   PC
                "https://resource.fs.com/mall/generalImg/20230829175058jdn6y8.png", //   M
                "https://resource.fs.com/mall/generalImg/20230829175058izlwac.png", //   pad
            ],
            S97: [
                "https://resource.fs.com/mall/generalImg/20230829154442s5mg74.png", //   PC
                "https://resource.fs.com/mall/generalImg/20230829154442ljfo6s.png", //   M
                "https://resource.fs.com/mall/generalImg/20230829154442q82x9o.png", //   pad
            ],
            S100: [
                "https://resource.fs.com/mall/generalImg/20230829181550rtyf3c.png", //   PC
                "https://resource.fs.com/mall/generalImg/20230829181550bv072x.png", //   M
                "https://resource.fs.com/mall/generalImg/20230829181550mpucon.png", //   pad
            ],
            S101: [
                "https://resource.fs.com/mall/generalImg/202308291813246ku0nf.png", //   PC
                "https://resource.fs.com/mall/generalImg/202308291813240iosdn.png", //   M
                "https://resource.fs.com/mall/generalImg/2023082918132488winz.png", //   pad
            ],
            // 100000: [
            //     "https://resource.fs.com/mall/generalImg/                    .png",//   PC
            //     "https://resource.fs.com/mall/generalImg/                    .png",//   M
            //     "https://resource.fs.com/mall/generalImg/                    .png",//   pad
            // ],
        }
        if (
            ["cn", "hk", "tw", "mo"].includes(website) &&
            ["S1", "S5", "S6", "S9", "S11", "S12", "S14", "S16", "S17", "S23", "S38", "S40", "S41", "S46", "S47", "S56", "S69", "S96", "S97", "S100", "S101"].includes(tag_id)
        ) {
            for (let key of Object.keys(cnBanner)) {
                if (tag_id === key) {
                    cnBanner[key].forEach((v, i) => {
                        res.data.banners[i].img = v
                    })
                }
            }
        }
        /* CN图片本地替换 */

        // Hospitality下 96 102 9 18 68 要push加上新的cms10032
        if (tag_id === "S96" || tag_id === "S102" || tag_id === "S9" || tag_id === "S18" || tag_id === "S68") {
            // cms 10032
            res1.data.children.push(...$c("solutions.CmsSolutions.headerPushEntry.hospitalityType.back"))
        }
        return {
            tag_id: tag_id,
            contentData: res.data,
            navData: res1.data,
        }
    },
    created() {
        console.log(55555, this.contentData, 323, this.navData, this.tag_id)
    },
    data() {
        return {
            contentData: {},
            navData: {},
            breadData: [],
            data: {},
            details: {},
        }
    },
    computed: {
        ...mapState({
            language: (state) => state.webSiteInfo.language,
        }),
    },
}
</script>

<style lang="scss">
body .fs-global-float-wrap {
    z-index: 60 !important;
}
</style>
<style lang="scss" scoped>
.solution {
    ::v-deep .left_content {
        margin-bottom: 36px;
    }
}
</style>
