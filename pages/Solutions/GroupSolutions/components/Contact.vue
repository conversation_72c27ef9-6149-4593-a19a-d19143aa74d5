<template>
    <div class="contact_us">
        <div class="common">
            <div class="tit">{{ $c("solutions.InfiniBandSolutions.contactUs.title") }}</div>
            <div class="txt">{{ $c("solutions.InfiniBandSolutions.contactUs.desc") }}</div>
            <div class="form_box" v-if="!hasSubmit">
                <div class="rt">
                    <div class="input_row input_row2" :class="{ input_row_cn: isCn }">
                        <div class="input_item">
                            <p class="input_txt">{{ $c("form.form.first_name") }} *</p>
                            <input
                                aria-label="firstname"
                                v-model.trim="form.entry_firstname"
                                :class="{ error_input: errors.entry_firstname_error }"
                                @focus="focusInput('entry_firstname', 'First Name')"
                                @blur="blurInput('entry_firstname')"
                                @input="blurInput('entry_firstname')"
                                class="inp"
                                type="text" />
                            <validate-error :error="errors.entry_firstname_error"></validate-error>
                        </div>
                        <div class="input_item">
                            <p class="input_txt">{{ $c("form.form.last_name") }} *</p>
                            <input
                                aria-label="lastname"
                                v-model.trim="form.entry_lastname"
                                :class="{ error_input: errors.entry_lastname_error }"
                                @focus="focusInput('entry_lastname', 'Last Name')"
                                class="inp"
                                type="text"
                                @blur="blurInput('entry_lastname')"
                                @input="blurInput('entry_lastname')" />
                            <validate-error :error="errors.entry_lastname_error"></validate-error>
                        </div>
                    </div>
                    <div class="input_row">
                        <div class="input_item">
                            <p class="input_txt">{{ isSg ? "Email address" : $c("single.ContactSales.BusinessEmail") }} *</p>
                            <input
                                aria-label="email_address"
                                v-model.trim="form.email_address"
                                :class="{ error_input: errors.email_address_error }"
                                @focus="focusInput('email_address', 'Business Email')"
                                class="inp"
                                type="text"
                                @blur="blurInput('email_address')"
                                @input="blurInput('email_address')" />
                            <validate-error :error="errors.email_address_error.replace('XXXX', localePath({ name: 'login', query: { redirect: $route.fullPath } }))"></validate-error>
                        </div>
                    </div>
                    <div class="input_row input_row2">
                        <div class="input_item">
                            <p class="input_txt">{{ $c("form.form.country") }} *</p>
                            <select-country v-model="form.countries_id" @change="selectCountry"></select-country>
                            <validate-error :error="errors.countries_id_error" @click.native="changeSite(cnRedirect)" />
                        </div>
                        <div class="input_item">
                            <!-- <span>{{ website === "jp" ? "（必須）" : " *" }}</span> -->
                            <p class="input_txt">{{ $c("form.form.phone") }}</p>
                            <tel-code-new ref="telCode" :isEditCode="true" :code.sync="form.telPrefix" v-model="form.entry_telephone" @error="phoneError"></tel-code-new>
                            <validate-error :error="errors.entry_telephone_error" />
                        </div>
                    </div>
                    <div class="input_row input_row2">
                        <!-- ifidkkkkfbkdfh -->
                        <div class="input_item">
                            <p class="input_txt">{{ $c("form.form.businessCategory") }} *</p>
                            <fs-select :options="formData.business_category" v-model="form.business_category" @change="handleSelectClick" :other="false" :isTopPosition="false"></fs-select>
                        </div>
                        <div class="input_item">
                            <p class="input_txt">{{ $c("form.form.applicationScenario") }} *</p>
                            <fs-select :options="formData.application_scenario" v-model="form.application_scenario" @change="handleSelectClick" :other="false" :isTopPosition="false"></fs-select>
                        </div>
                    </div>
                    <div class="input_row" v-if="false">
                        <div class="input_item">
                            <p class="input_txt">{{ $c("form.form.phone_business") }} *</p>
                            <input
                                v-model.trim="form.entry_telephone"
                                :class="{ error_input: errors.entry_telephone_error }"
                                @focus="focusInput('entry_telephone', 'Entry Telephone')"
                                class="inp"
                                type="text"
                                @blur="telChange(form.entry_telephone)" />
                            <validate-error v-if="!isSg" :error="errors.entry_telephone_error.replace('XXXX', localePath({ name: 'login', query: { redirect: $route.fullPath } }))"></validate-error>
                        </div>
                    </div>
                    <div class="input_row">
                        <div class="input_item input_item_last">
                            <p class="input_txt">{{ $c("form.form.whatIsYourProject") }} *</p>
                            <textarea
                                aria-label="comments"
                                v-model.trim="form.comments"
                                :class="{ error_input: errors.comments_error }"
                                @focus="focusInput('comments', 'comments')"
                                maxlength="5000"
                                name=""
                                class="textarea"
                                :placeholder="$c('form.form.brieflyOutlineYour')"
                                @blur="blurInput('comments')"
                                @input="blurInput('comments')"></textarea>
                            <div :class="errors.comments_error ? 'input-item-flex' : 'input-item-number'">
                                <validate-error :error="errors.comments_error"></validate-error>
                                <span class="textarea-num">
                                    <em :class="{ active: form.comments.length === 5000 }">{{ form.comments.length }}</em
                                    >/5000
                                </span>
                            </div>
                        </div>
                    </div>
                    <!-- <div
                        @click="handleGa"
                        class="form_agreement"
                        v-html="
                            $c('single.ContactSales.submitTip')
                                .replace('XXXX1', localePath({ name: 'privacy-policy' }))
                                .replace('XXXX2', localePath({ name: 'terms-of-use' }))
                        "></div> -->
                    <div class="form_agreement">
                        <div class="policy_box">
                            <input v-model="form.isAgreePolicy" @change="blurInput('isAgreePolicy')" type="checkbox" class="chk" />
                            <div
                                class="agreement_wrap"
                                @click.stop="handleGa"
                                v-html="
                                    $c('form.validate.aggree_policy_new')
                                        .replace('AAAA', localePath({ name: 'privacy-notice' }))
                                        .replace('BBBB', localePath({ name: 'terms-of-use' }))
                                "></div>
                        </div>
                        <validate-error :error="errors.isAgreePolicy"></validate-error>
                    </div>
                    <div class="sbtn-box">
                        <fs-button :text="$c('single.ContactSales.Submit')" id="service_chat" :loading="sbtn_loading" @click="submitFn"></fs-button>
                    </div>
                </div>
            </div>
            <div class="success_box" v-else>
                <div class="success_icon">
                    <i class="iconfont">&#xe710;</i>
                </div>
                <div class="success_tit">{{ $c("form.form.success.tit") }}</div>
                <p class="success_txt" v-html="subStr($c('form.form.success.txt1'))"></p>
            </div>
        </div>
    </div>
</template>

<script>
import FsButton from "@/components/FsButton/FsButton"
import SelectCountry from "@/components/SelectCountry/SelectCountry"
import TelCodeNew from "@/components/TelCode/TelCodeNew.vue"
import FsSelect from "@/components/FsSelect/FsSelect"
import ValidateError from "@/components/ValidateError/ValidateError.vue"
import { isValidPhoneNumber } from "@/util/libphonenumber.js"
import { email_valdate, cn_all_phone } from "@/constants/validate.js"
import { getRecaptchaToken } from "@/util/grecaptchaHost"
import { mapState, mapGetters } from "vuex"
export default {
    name: "solutions_group_contact",
    components: {
        FsButton,
        SelectCountry,
        TelCodeNew,
        FsSelect,
        ValidateError,
    },
    props: {
        contactData: {
            type: Object,
            default: () => {},
        },
        formData: {
            type: Object,
            default: () => {},
        },
        type_id: {
            type: String | Number,
            default: "",
        },
    },
    data() {
        return {
            sbtn_loading: false,
            form: {
                entry_firstname: this.$store.state.userInfo.userInfo && this.$store.state.userInfo.userInfo.customers_firstname ? this.$store.state.userInfo.userInfo.customers_firstname : "",
                entry_lastname: this.$store.state.userInfo.userInfo && this.$store.state.userInfo.userInfo.customers_lastname ? this.$store.state.userInfo.userInfo.customers_lastname : "",
                email_address: this.$store.state.userInfo.userInfo && this.$store.state.userInfo.userInfo.customers_email_address ? this.$store.state.userInfo.userInfo.customers_email_address : "",
                countries_id: "",
                comments: "",
                industry: "",
                job_title: "",
                company_size: "",
                company_name: "",
                telPrefix: "",
                entry_telephone: "",
                business_category: 99, // 默认选中最后的Others
                application_scenario: this.type_id || 99, // 默认选择当前方案应用场景分类类型
                reviews_newImg: [],
                isAgreePolicy: false,
            },
            errors: {
                entry_firstname_error: "",
                entry_lastname_error: "",
                email_address_error: "",
                countries_id_error: "",
                industry_error: "",
                comments_error: "",
                job_title_error: "",
                company_size_error: "",
                company_name_error: "",
                entry_telephone_error: "",
                business_category_error: "",
                application_scenario_error: "",
                isAgreePolicy: "",
            },
            hasSubmit: false,
        }
    },
    computed: {
        ...mapState({
            isLogin: (state) => state.userInfo.isLogin,
            select_country_id: (state) => state.selectCountry.select_country_id,
            website: (state) => state.webSiteInfo.website,
            countries_id: (state) => state.webSiteInfo.countries_id,
            language: (state) => state.webSiteInfo.language,
            pageGroup: (state) => state.ga.pageGroup,
            resourcePage: (state) => state.device.resource_page,
        }),
        ...mapGetters({
            isShowState: "selectCountry/isShowState",
            isCn: "webSiteInfo/isCn",
            country_label: "selectCountry/country_label",
        }),
        isSg() {
            return this.website === "sg"
        },
    },
    created() {
        this.form.countries_id = this.countries_id
    },
    mounted() {
        console.log("contactData", this.contactData)
    },
    methods: {
        subStr(str) {
            return str.replace("%XXXX%", `<a class="case_btn" href="${this.localePath({ path: `/support_ticket` })}">`).replace("%ZZZZ%", "</a>")
        },
        handleGa({ target: { href } }) {
            if (href) {
                const res = href.indexOf("privacy_policy") != -1
                const res2 = href.indexOf("/terms_of_use.html") != -1
                if (res) {
                    this.gaEvent("privacy_policy")
                } else if (res2) {
                    this.gaEvent("terms_of_use")
                }
            }
        },
        gaEvent(eventLabel) {
            window.dataLayer &&
                window.dataLayer.push({
                    event: "uaEvent",
                    eventCategory: "Send A Message Page",
                    eventAction: "contact_sales",
                    eventLabel,
                    nonInteraction: false,
                })
        },
        focusInput(attr, label) {
            window.dataLayer &&
                window.dataLayer.push({
                    event: "uaEvent",
                    eventCategory: "Send A Message Page",
                    eventAction: "contact_sales",
                    eventLabel: `${label}_Input`,
                    nonInteraction: false,
                })
        },
        blurInput(attr) {
            if (attr === "entry_firstname") {
                if (!this.form.entry_firstname.replace(/^\s+|\s+$/g, "")) {
                    this.errors.entry_firstname_error = this.$c("form.form.errors.entry_firstname_error")
                    return
                } else if (this.form.entry_firstname.length > 40) {
                    this.errors.entry_firstname_error = this.$c("form.validate.first_name.first_name_max")
                }
                this.errors.entry_firstname_error = ""
            }
            if (attr === "entry_lastname") {
                if (!this.form.entry_lastname.replace(/^\s+|\s+$/g, "")) {
                    this.errors.entry_lastname_error = this.$c("form.form.errors.entry_lastname_error")
                    return
                } else if (this.form.entry_lastname.length > 40) {
                    this.errors.entry_lastname_error = this.$c("form.validate.last_name.last_name_max")
                }
                this.errors.entry_lastname_error = ""
            }
            if (attr === "email_address") {
                if (!this.isCn) {
                    if (!this.form.email_address.replace(/^\s+|\s+$/g, "")) {
                        this.errors.email_address_error = this.isSg ? this.$c("form.form.errors.email_address_error") : this.$c("form.form.errors.email_business_error")
                    } else if (!email_valdate.test(this.form.email_address)) {
                        this.errors.email_address_error = this.isSg ? this.$c("form.form.errors.email_address_error01") : this.$c("form.form.errors.email_business_error01")
                    } else {
                        this.errors.email_address_error = ""
                    }
                } else {
                    if (!this.form.email_address.replace(/^\s+|\s+$/g, "")) {
                    } else if (!email_valdate.test(this.form.email_address)) {
                        this.errors.email_address_error = this.isSg ? this.$c("form.form.errors.email_address_error01") : this.$c("form.form.errors.email_business_error01")
                    } else {
                        this.errors.email_address_error = ""
                        if (this.isLogin == 1) return
                        this.$axios
                            .post("/api/user/isHasRegister", { customers_name: this.form.email_address })
                            .then((res) => {
                                if (res.code != 200) return
                                const data = res.data
                                if (data && data.is_has) {
                                    this.errors.email_address_error = "账户已存在，单击此处<a href=XXXX title='登录'>登录</a>。"
                                } else {
                                    this.errors.email_address_error = ""
                                }
                            })
                            .catch((e) => {
                                this.errors.email_address_error = ""
                            })
                    }
                }
            }
            if (attr === "countries_id") {
                if (this.website != "cn" && this.form.countries_id == 44) {
                    this.errors.countries_id_error = this.$c("form.form.CNlimitTip")
                } else {
                    this.errors.countries_id_error = ""
                }
            }
            if (attr === "comments") {
                if (!this.form.comments.replace(/^\s+|\s+$/g, "")) {
                    this.errors.comments_error = this.$c("single.ContactSales.fieldRequired")
                } else {
                    this.errors.comments_error = ""
                }
            }
            if (attr === "isAgreePolicy") {
                this.errors.isAgreePolicy = this.form.isAgreePolicy ? "" : this.$c("form.form.errors.check2_error")
            }
        },
        selectCountry(country) {
            this.form.countries_id = country.countries_id
            if (this.website != "cn" && this.form.countries_id == 44) {
                this.errors.countries_id_error = this.$c("form.form.CNlimitTip")
            } else {
                this.errors.countries_id_error = ""
            }
        },
        changeSite(v) {
            this.updateSiteInfo({
                ...v,
                callback: () => {},
            })
        },
        telChange(inp) {
            this.form.entry_telephone = inp
            // 判断是否为6位数字
            if (!inp.replace(/^\s+|\s+$/g, "")) {
                this.errors.entry_telephone_error = this.$c("form.form.errors.entry_telephone_error")
            } else {
                if (!this.isCn) {
                    // if (!this.regExp.test(inp)) {
                    //     this.errors.entry_telephone_error = this.$c("form.form.errors.entry_telephone_error01")
                    // } else {
                    //     this.errors.entry_telephone_error = ""
                    // }
                } else {
                    if (!cn_all_phone.test(inp)) {
                        this.errors.entry_telephone_error = this.$c("form.form.errors.entry_telephone_error01")
                    } else {
                        this.errors.entry_telephone_error = ""
                        if (this.isLogin == 1) return
                        this.$axios
                            .post("/api/user/isHasRegister", { customers_name: inp })
                            .then((res) => {
                                if (res.code != 200) return
                                const data = res.data
                                if (data && data.is_has) {
                                    this.errors.entry_telephone_error = "账户已存在，单击此处<a href=XXXX title='登录'>登录</a>。"
                                } else {
                                    this.errors.entry_telephone_error = ""
                                }
                            })
                            .catch((e) => {
                                this.errors.entry_telephone_error = ""
                            })
                    }
                }
            }
        },
        phoneError(errorMsg) {
            if (this.form.entry_telephone.replace(/^\s+|\s+$/g, "")) {
                this.errors.entry_telephone_error = errorMsg
            }
        },
        handleSelectClick() {
            // this.buriedPointWrapper(`Industry Drop-Down`)
        },
        async submitFn() {
            if (this.sbtn_loading) {
                return
            }
            let flag = false
            if (!this.form.entry_firstname.replace(/^\s+|\s+$/g, "")) {
                this.errors.entry_firstname_error = this.$c("form.form.errors.entry_firstname_error")
                flag = true
            }
            if (!this.form.entry_lastname.replace(/^\s+|\s+$/g, "")) {
                this.errors.entry_lastname_error = this.$c("form.form.errors.entry_lastname_error")
                flag = true
            }
            if (!this.isCn) {
                if (!this.form.email_address.replace(/^\s+|\s+$/g, "")) {
                    this.errors.email_address_error = this.isSg ? this.$c("form.form.errors.email_address_error") : this.$c("form.form.errors.email_business_error")
                    flag = true
                } else if (!email_valdate.test(this.form.email_address)) {
                    this.errors.email_address_error = this.isSg ? this.$c("form.form.errors.email_address_error01") : this.$c("form.form.errors.email_business_error01")
                    flag = true
                }
            } else {
                if (this.errors.email_address_error) {
                    flag = true
                }
                if (this.errors.entry_telephone_error) {
                    flag = true
                }
            }
            if (!this.form.countries_id && this.website != "cn" && this.form.countries_id == 44) {
                this.errors.countries_id_error = this.$c("form.form.CNlimitTip")
                flag = true
            }
            if (this.isCn) {
                if (this.form.entry_telephone.replace(/^\s+|\s+$/g, "") && !cn_all_phone.test(this.form.entry_telephone)) {
                    this.errors.entry_telephone_error = this.$c("form.form.errors.entry_telephone_error01")
                    flag = true
                }
            } else {
                if (this.form.entry_telephone.replace(/^\s+|\s+$/g, "") && !isValidPhoneNumber(this.form.telPrefix + this.form.entry_telephone)) {
                    this.errors.entry_telephone_error = this.$c("pages.NetTermsApplication.validate.phone.phone_validate")
                    flag = true
                }
            }
            if (!this.form.comments.replace(/^\s+|\s+$/g, "")) {
                this.errors.comments_error = this.$c("single.ContactSales.fieldRequired")
                flag = true
            }
            if (!this.form.isAgreePolicy) {
                this.errors.isAgreePolicy = this.$c("form.form.errors.check2_error")
                flag = true
            }
            if (flag) return
            let data = {
                entry_firstname: this.form.entry_firstname,
                entry_lastname: this.form.entry_lastname,
                email_address: this.form.email_address,
                countries_id: this.form.countries_id,
                entry_telephone: `${this.form.telPrefix.replace("+", "")} ${this.form.entry_telephone}`,
                business_category: this.form.business_category,
                application_scenario: this.form.application_scenario,
                comments: this.form.comments,
            }
            let pathStr = this.$route.path.split("-")
            // let resource_page = pathStr.pop().replace(".html", "")
            let resource_page = "41"
            data.resource_page = resource_page
            data.website_link = location.href
            this.sbtn_loading = true
            const { recaptchaTp, headers = {}, errorMsg = "grecaptcha_error" } = await getRecaptchaToken()
            if (!recaptchaTp) {
                this.loading = false
                this.$message.error(this.$c(`pages.Login.regist.${errorMsg}`))
                return
            }
            this.$axios
                .post("/api/contact_sales", data, { headers })
                .then((res) => {
                    this.sbtn_loading = false
                    if (res.code == 200 && res.data.result == true) {
                        window.dataLayer &&
                            window.dataLayer.push({
                                event: "uaEvent",
                                eventCategory: "Send A Message Page",
                                eventAction: "contact_sales",
                                eventLabel: `Submit Success_${res.data.data.caseNumber}_${window.location.href}`,
                                nonInteraction: false,
                            })
                        this.hasSubmit = true
                        this.form = {
                            entry_firstname: "",
                            entry_lastname: "",
                            email_address: "",
                            countries_id: this.countries_id,
                            // industry: "",
                            comments: "",
                            // job_title: "",
                            // company_size: "",
                            // company_name: "",
                            entry_telephone: "",
                            business_category: 99,
                            application_scenario: this.type_id || 99,
                        }
                        this.$bdRequest({
                            conversionTypes: [
                                {
                                    logidUrl: location.href,
                                    newType: 18,
                                },
                            ],
                        })
                    } else if (res.code === 200 && res.status === "sensiWords" && this.website === "cn") {
                        for (let key in res.errors) {
                            this.errors[`${key}_error`] = this.$c("form.form.errors.sensiWords")
                        }
                    } else {
                        this.$message.error("There was an error processing your request. Please try again or contact support.")
                    }
                })
                .catch((err) => {
                    this.sbtn_loading = false
                    window.dataLayer &&
                        window.dataLayer.push({
                            event: "uaEvent",
                            eventCategory: "Send A Message Page",
                            eventAction: "contact_sales",
                            eventLabel: "Submit Fail",
                            nonInteraction: false,
                        })
                    if (err.code === 422) {
                        this.errors.entry_firstname_error = err.errors.entry_firstname ? err.errors.entry_firstname : ""
                        this.errors.entry_lastname_error = err.errors.entry_lastname ? err.errors.entry_lastname : ""
                        this.errors.email_address_error = err.errors.email_address ? err.errors.email_address : ""
                        this.errors.countries_id_error = err.errors.countries_id ? err.errors.countries_id : ""
                        // this.errors.industry_error = err.errors.industry ? err.errors.industry : ""
                        // this.errors.job_title_error = err.errors.job_title ? err.errors.job_title : ""
                        // this.errors.company_size_error = err.errors.company_size ? err.errors.company_size : ""
                        // this.errors.company_name_error = err.errors.company_name ? err.errors.company_name : ""
                        this.errors.comments_error = err.errors.comments ? err.errors.comments : ""
                        this.errors.entry_telephone_error = err.errors.entry_telephone ? err.errors.entry_telephone : ""
                        this.errors.business_category_error = err.errors.business_category ? err.errors.business_category : ""
                        this.errors.application_scenario_error = err.errors.application_scenario ? err.errors.application_scenario : ""
                    }
                })
        },
    },
}
</script>

<style lang="scss" scoped>
::v-deep .select-country {
    background-color: $bgColor3;
}
::v-deep .tel-code-new {
    background-color: $bgColor3;
}

img {
    max-width: 100%;
    display: block;
}
.contact_us {
    @include bgcover("https://resource.fs.com/mall/generalImg/202404181804127jt7nb.webp");
    padding: 40px 0;
    color: $textColor1;
    .common {
        width: 84vw;
        max-width: 1200px;
        margin: 0 auto;
        position: relative;
        display: flex;
        flex-direction: column;
        align-items: center;
        .tit {
            @include font20;
            font-weight: 600;
            text-align: center;
            margin-bottom: 12px;
        }
        .txt {
            @include font14;
            color: $textColor3;
            text-align: center;
            margin-bottom: 24px;
        }
        .form_box {
            padding: 0 48px;
            width: 100%;
            display: flex;
            justify-content: center;
            .rt {
                width: 100%;
                max-width: 900px;
                .input_row {
                    display: flex;
                    justify-content: space-between;
                    margin-top: 16px;
                    &:first-child {
                        margin-top: 0;
                    }
                    input,
                    textarea {
                        background-color: #fff;
                    }
                    .input_item {
                        width: 100%;
                        .input_txt {
                            @include font12;
                            margin-bottom: 8px;
                        }
                        .input-item-flex {
                            display: flex;
                            justify-content: space-between;
                            align-items: center;
                        }
                        .input-item-number {
                            display: flex;
                            justify-content: flex-end;
                            width: 100%;
                        }
                        .textarea-num {
                            color: $textColor3;
                            @include font12;
                            margin-top: 4px;
                            em {
                                font-style: normal;
                                &.active {
                                    color: $textColor4;
                                }
                            }
                        }
                    }
                    &.input_row2 {
                        .input_item {
                            width: calc(50% - 8px);
                        }
                    }
                    &.input_row_cn {
                        flex-direction: row-reverse;
                    }
                }
                .form_agreement {
                    @include font12;
                    margin: 16px 0 32px;
                    color: $textColor3;
                }
                .policy_box {
                    display: flex;
                    .chk {
                        margin-top: 4px;
                        margin-right: 8px;
                        width: 14px;
                        height: 14px;
                        font-size: 14px;
                    }
                    .agreement_wrap {
                        @include font14;
                        color: $textColor3;
                    }
                }
                .sbtn-box {
                    margin-top: 8px;
                    .fs-button {
                        border-radius: 4px;
                        &::before {
                            border-radius: 4px;
                        }
                    }
                }
            }
        }
        .success_box {
            margin: 0 auto;
            height: 412px;
            max-width: 900px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            .success_icon {
                margin-bottom: 20px;
                display: flex;
                justify-content: center;
                .iconfont {
                    font-size: 50px;
                    color: #10a300;
                }
            }
            .success_tit {
                @include font20;
                font-weight: 600;
                text-align: center;
                margin-bottom: 12px;
            }
            .success_txt {
                @include font14;
                text-align: center;
                color: $textColor3;
            }
        }
    }
}
@media (max-width: 1024px) {
    .contact_us {
        background-image: url("https://resource.fs.com/mall/generalImg/202404131202098j98dm.webp");

        .common {
            width: calc(100% - 48px);
            .form_box {
                padding: 0 62px;
            }
            .tit {
                padding: 0;
            }
        }
    }
}
@media (max-width: 767px) {
    .contact_us {
        background-image: url("https://resource.fs.com/mall/generalImg/20240413120209udk1k9.webp");

        padding: 36px 0;
        .common {
            width: calc(100% - 32px);
            .form_box {
                padding: 0;
                .rt {
                    .input_row {
                        &.input_row2 {
                            flex-flow: wrap;
                            .input_item {
                                width: 100%;
                                &:first-child {
                                    margin-bottom: 16px;
                                }
                            }
                        }
                        &.input_row_cn {
                            flex-direction: column-reverse;
                            .input_item {
                                &:first-child {
                                    margin-bottom: 0;
                                }
                                &:last-child {
                                    margin-bottom: 16px;
                                }
                            }
                        }
                    }
                    .sbtn-box {
                        .fs-button {
                            width: 100%;
                        }
                    }
                }
            }
            .success_box {
                height: 376px;
            }
            .tit {
                padding: 0;
                margin-bottom: 12px;
                @include font20;
                font-weight: 600;
            }
        }
    }
}
</style>
