<template>
    <div class="products">
        <div class="tit">{{ productData.tit }}</div>
        <!-- <div class="product_tabs" :class="has_scroll">
            <ul>
                <li v-for="(c, t) in productData.products" :class="{ active: t === isActive }" :key="t" @click="changeProducts(t)">
                    <span>{{ c.title }}</span>
                </li>
            </ul>
            <div class="shadow"></div>
        </div> -->
        <div class="nav_div">
            <account-tabs :tabList="productData.products" v-model="isActive" @changeTab="changeTab"></account-tabs>
        </div>
        <div class="list">
            <div class="product_list">
                <div class="product_list_cont" :class="{ no_swiper: productData.products[isActive].list.length <= 4 }">
                    <swiper :options="swiperProduct" ref="productRef">
                        <swiper-slide v-for="(item, i) in productData.products[isActive].list" :key="i">
                            <div class="product" v-if="item && Object.keys(productData)" :class="{ dis_product: disabledProduct(item) }">
                                <div class="pic">
                                    <skeleton-solutions :item="{ width: '100%', bg: '#F2F2F2', height: '100%' }"> </skeleton-solutions>

                                    <img :src="item.img" :alt="item.title" />
                                </div>
                                <div class="desc_box">
                                    <div class="des_tit" :title="item.title">
                                        <span>{{ item.title }}</span>
                                    </div>
                                    <div class="category_items" v-if="item.urls && item.urls.length">
                                        <a v-for="(items, indexs) in item.urls" :key="indexs" :href="linkHref(items.url)" :title="items.tit" :class="{ dis_a: disabledItem(items.url) }">{{ items.tit }}</a>
                                    </div>
                                </div>
                            </div>
                        </swiper-slide>
                        <div class="swiper-pagination" slot="pagination"></div>
                    </swiper>
                    <div class="swiper-btn-box">
                        <div class="swiper-button-prev swiper-btn" slot="button-prev"></div>
                        <div class="swiper-button-next swiper-btn" slot="button-next"></div>
                    </div>
                </div>
                <div class="product_list_cont_m">
                    <div v-for="(item, i) in productData.products[isActive].list" :key="i">
                        <div class="product" v-if="item && Object.keys(item)" :class="{ dis_product: disabledProduct(item) }">
                            <div class="pic">
                                <img :src="item.img" :alt="item.name" />
                                <skeleton-solutions :item="{ width: '100%', bg: '#F2F2F2', height: '100%' }"> </skeleton-solutions>
                            </div>
                            <div class="desc_box">
                                <div class="des_tit" :title="item.title">
                                    <span>{{ item.title }}</span>
                                </div>
                                <div class="category_items" v-if="item.urls && item.urls.length">
                                    <a v-for="(items, indexs) in item.urls" :key="indexs" :href="linkHref(items.url)" :title="items.tit" :class="{ dis_a: disabledItem(items.url) }">{{ items.tit }}</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import AccountTabs from "@/components/AccountTabs/AccountTabs.vue"
import { Swiper, SwiperSlide } from "vue-awesome-swiper"
import SkeletonSolutions from "@/components/Skeleton/SkeletonSolutions"
import { mapState } from "vuex"

import "swiper/css/swiper.css"

export default {
    name: "solutions_group_product",
    components: {
        AccountTabs,
        Swiper,
        SwiperSlide,
        SkeletonSolutions,
    },
    props: {
        productData: {
            type: Object,
            default: {},
        },
        tag_id: {
            type: String,
            default: "",
        },
    },
    data() {
        const _this = this
        return {
            screenWidth: null,
            swiperProduct: {
                pagination: {
                    el: ".swiper-pagination",
                    clickable: true,
                },
                navigation: {
                    nextEl: ".swiper-button-next",
                    prevEl: ".swiper-button-prev",
                },
                onlyExternal: true,
                preventClicks: true,
                allowTouchMove: true,
                resizeObserver: true,
                observeParents: true,
                observer: true,
                slidesPerView: 4,
                slidesPerGroup: 4,
                spaceBetween: 20,
                on: {
                    slideChangeTransitionStart: function () {
                        _this.screenWidth = document.body.clientWidth
                    },
                },
            },
            isActive: 0,
        }
    },
    computed: {
        swiper() {
            return this.$refs.productRef.$swiper
        },
        has_scroll() {
            return this.productData.products.length > 2 ? "has_scroll" : ""
        },
        ...mapState({
            website: (state) => state.webSiteInfo.website,
        }),
    },
    methods: {
        disabledProduct(item) {
            if (this.tag_id === "S2000") {
                let disAll = true
                item.urls.forEach((element) => {
                    if (element.url && !this.filter2000(element.url)) {
                        disAll = false
                    }
                })
                return ["cn", "hk", "mo", "tw"].includes(this.website) && disAll
            } else if (this.tag_id === "S2001") {
                let disAll = true
                item.urls.forEach((element) => {
                    if (element.url && !this.filter2001(element.url)) {
                        disAll = false
                    }
                })
                return ["cn", "hk", "mo", "tw"].includes(this.website) && disAll
            } else if (this.tag_id === "S2002") {
                let disAll = true
                item.urls.forEach((element) => {
                    if (element.url && !this.filter2002(element.url)) {
                        disAll = false
                    }
                })
                return ["cn", "hk", "mo", "tw"].includes(this.website) && disAll
            } else if (this.tag_id === "S2003") {
                let disAll = true
                item.urls.forEach((element) => {
                    if (element.url && !this.filter2003(element.url)) {
                        disAll = false
                    }
                })
                return ["cn", "hk", "mo", "tw"].includes(this.website) && disAll
            } else if (this.tag_id === "S2004") {
                let disAll = true
                item.urls.forEach((element) => {
                    if (element.url && !this.filter2004(element.url)) {
                        disAll = false
                    }
                })
                return ["cn", "hk", "mo", "tw"].includes(this.website) && disAll
            } else if (this.tag_id === "S2005") {
                let disAll = true
                item.urls.forEach((element) => {
                    if (element.url && !this.filter2005(element.url)) {
                        disAll = false
                    }
                })
                return ["cn", "hk", "mo", "tw"].includes(this.website) && disAll
            } else {
                return false
            }
        },
        disabledItem(url) {
            if (url) {
                if (this.tag_id === "S2000") {
                    return ["cn", "hk", "mo", "tw"].includes(this.website) && this.filter2000(url)
                } else if (this.tag_id === "S2001") {
                    return ["cn", "hk", "mo", "tw"].includes(this.website) && this.filter2001(url)
                } else if (this.tag_id === "S2002") {
                    return ["cn", "hk", "mo", "tw"].includes(this.website) && this.filter2002(url)
                } else if (this.tag_id === "S2003") {
                    return ["cn", "hk", "mo", "tw"].includes(this.website) && this.filter2003(url)
                } else if (this.tag_id === "S2004") {
                    return ["cn", "hk", "mo", "tw"].includes(this.website) && this.filter2004(url)
                } else if (this.tag_id === "S2005") {
                    return ["cn", "hk", "mo", "tw"].includes(this.website) && this.filter2005(url)
                } else {
                    return false
                }
            } else {
                return true
            }
        },
        linkHref(url) {
            if (url) {
                if (this.tag_id === "S2000") {
                    return ["cn", "hk", "mo", "tw"].includes(this.website) && this.filter2000(url) ? "javascript:;" : this.$handleLink(url).url
                } else if (this.tag_id === "S2001") {
                    return ["cn", "hk", "mo", "tw"].includes(this.website) && this.filter2001(url) ? "javascript:;" : this.$handleLink(url).url
                } else if (this.tag_id === "S2002") {
                    return ["cn", "hk", "mo", "tw"].includes(this.website) && this.filter2002(url) ? "javascript:;" : this.$handleLink(url).url
                } else if (this.tag_id === "S2003") {
                    return ["cn", "hk", "mo", "tw"].includes(this.website) && this.filter2003(url) ? "javascript:;" : this.$handleLink(url).url
                } else if (this.tag_id === "S2004") {
                    return ["cn", "hk", "mo", "tw"].includes(this.website) && this.filter2004(url) ? "javascript:;" : this.$handleLink(url).url
                } else if (this.tag_id === "S2005") {
                    return ["cn", "hk", "mo", "tw"].includes(this.website) && this.filter2005(url) ? "javascript:;" : this.$handleLink(url).url
                } else {
                    return this.$handleLink(url).url
                }
            } else {
                return "javascript:;"
            }
        },
        filter2000(url) {
            return [
                "/c/nvidia-infiniband-switches-4887",
                "/c/nvidiar-infiniband-adapters-4173",
                "/c/40-100g-picos-switches-4369?551=108892",
                "/c/ampcon-management-platform-4227",
                "/c/1-2.5g-picos-switches-1368?551=108899",
                "/c/firewalls-3646",
                "/c/400-800g-picos-switches-4228?192=30495",
                "/c/nvidia%C2%AE-ethernet-adapters-4014",
            ].some((string) => url.includes(string))
        },
        filter2001(url) {
            return [
                "/c/data-center-network-4365?327=30322,30498",
                "/c/data-center-network-4365?327=30326,30323,30329",
                "/c/enterprise-network-4367?327=30326,30323",
                "/c/enterprise-network-4367?327=30327,30328",
                "/c/enterprise-network-4367?327=30325",
                "/products/196175.html",
                "/c/rack-servers-4082",
                "/c/tower-servers-4135",
                "/c/high-density-servers-4134",
                "/c/data-storage-4193",
                "/c/mellanox-ethernet-adapters-4014",
                "/c/qlogicr-fc-hbas-2887",
                "/c/firewalls-3646",
            ].some((string) => url.includes(string))
        },
        filter2002(url) {
            return ["/c/ampcon-management-platform-4227", "/c/gateways-1150", "/c/firewalls-3646", "/c/modular-enterprise-switches-4243", "/c/10g-industrial-switches-4238"].some((string) => url.includes(string))
        },
        filter2003(url) {
            return ["/products/108708.html", "/c/routers-gateways-1150", "/c/indoor-access-points-3086", "/c/outdoor-access-points-4133", "/products/178410.html", "/c/accessories-3872"].some((string) =>
                url.includes(string)
            )
        },
        filter2004(url) {
            return [
                "/c/video-recorders-4053",
                "/c/video-storage-3645",
                "/c/milestone%20software-4266",
                "/c/video-management-system-4113",
                "/c/security-cameras-4052",
                "/c/face-recognition-terminals-4091",
                "/c/display-control-4074",
                "/c/poe-kits-4305",
            ].some((string) => url.includes(string))
        },
        filter2005(url) {
            return ["/products/107371.html", "/products/166627.html", "/products/183175.html", "/products/188132.html", "/products/161851.html"].some((string) => url.includes(string))
        },
        changeProducts(n) {
            this.isActive = n
            this.swiper.slideTo(0)
        },
        changeTab(index) {
            this.isActive = index
            this.swiper.slideTo(0)
        },
    },
    mounted() {
        console.log("productData", this.productData)
    },
}
</script>

<style lang="scss" scoped>
.products {
    padding: 40px 0 0;
    color: $textColor1;
    .tit {
        @include font20;
        font-weight: 600;
        text-align: center;
        margin-bottom: 12px;
    }
    .product_tabs {
        .shadow {
            display: none;
        }
        > ul {
            width: 84vw;
            max-width: 1200px;
            margin: 0 auto;
            position: relative;
            display: flex;
            justify-content: center;
            margin-bottom: 24px;
            border-bottom: 1px solid $borderColor2;
            width: fit-content;
            > li {
                @include font14;
                // width: 288px;
                margin-right: 48px;
                color: $textColor1;
                text-align: center;
                padding: 10px 0 14px;
                cursor: pointer;
                span {
                    position: relative;
                    display: inline-block;
                }
                &.active {
                    font-weight: 600;
                    color: $textColor1;
                    span {
                        &::after {
                            content: "";
                            display: block;
                            position: absolute;
                            bottom: -14px;
                            width: 100%;
                            height: 2px;
                            border-radius: 2px;
                            background-color: $bgColor5;
                        }
                    }
                }
                &:last-child {
                    margin-right: 0;
                }
            }
        }
    }
    .nav_div {
        width: 84vw;
        max-width: 1200px;
        margin: 0 auto 24px;
        ::v-deep .account-tabs {
            width: fit-content;
            margin: 0 auto;
            .list {
                .tab-item {
                    @include font14;
                    padding: 9px 0;
                    max-width: fit-content;
                    font-weight: 400;
                }
            }
        }
    }
    .list {
        max-width: 1360px;
        margin: 0 auto;
        .product_list {
            width: 84vw;
            max-width: 1200px;
            margin: 0 auto;
            position: relative;
            .product_list_cont.no_swiper {
                .swiper-container {
                    padding-bottom: 40px;
                }
                ::v-deep .swiper-wrapper {
                    justify-content: center;
                    .swiper-slide {
                        &:last-child {
                            margin-right: 0 !important;
                        }
                    }
                }
                .swiper-pagination {
                    display: none;
                }
                .swiper-btn-box {
                    display: none;
                }
            }
            .product_list_cont_m {
                display: none;
            }
            .swiper-btn-box {
                transition: all 0.3s;
                opacity: 0;
                .swiper-btn {
                    position: absolute;
                    font-size: 28px;
                    color: #fff;
                    width: 48px;
                    height: 48px;
                    border-radius: 50%;
                    transition: all 0.3s;
                    box-sizing: border-box;
                    background-color: rgba(0, 0, 0, 0.2);
                    text-align: center;
                    line-height: 48px;
                    background-image: none;
                    user-select: none;
                    opacity: 1;
                    top: 50%;
                    transform: translateY(-50%);
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    cursor: pointer;
                    &.swiper-button-prev {
                        &:after {
                            content: "\f048";
                            font-family: "iconfont" !important;
                            font-size: 24px;
                            font-style: normal;
                            -webkit-font-smoothing: antialiased;
                            -moz-osx-font-smoothing: grayscale;
                        }
                    }
                    &.swiper-button-next {
                        &:after {
                            content: "\f047";
                            font-family: "iconfont" !important;
                            font-size: 24px;
                            font-style: normal;
                            -webkit-font-smoothing: antialiased;
                            -moz-osx-font-smoothing: grayscale;
                        }
                    }
                    &.swiper-button-disabled {
                        cursor: default;
                    }
                    &:hover {
                        background-color: rgba(0, 0, 0, 0.4);
                    }
                    &::after {
                        content: none;
                    }
                    @media (max-width: 1024px) {
                        display: none;
                    }
                }
                .swiper-button-prev {
                    left: -82px;
                }
                .swiper-button-next {
                    right: -82px;
                }
                .swiper-button-disabled {
                    background-color: rgba(0, 0, 0, 0.05) !important;
                }
            }
            .pic {
                background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.05) 100%), #f7f7f7;
                border-radius: 8px 8px 0px 0px;
                display: flex;
                justify-content: center;
                position: relative;
                min-height: calc((min(84vw, 1200px) - 60px) / 8);
                @media (max-width: 768px) {
                    min-height: calc((min(84vw, 1200px) - 32px) / 2);
                }
                ::v-deep {
                    .skeleton_wrap {
                        z-index: 0;
                    }
                    .skeleton_item {
                        border-radius: 8px 8px 0 0;
                    }
                }

                img {
                    display: block;
                    max-width: 100%;
                    border-radius: 8px 8px 0 0;
                    position: relative;
                    z-index: 1;
                    // height: 160px;
                }
            }
            .desc_box {
                border: 1px solid #e5e5e5;
                border-top: none;
                border-radius: 0px 0px 8px 8px;
                flex: 1;
                .des_tit {
                    display: flex;
                    align-items: center;
                    justify-content: flex-start;
                    padding: 0 20px;
                    margin-top: 20px;
                    span {
                        flex: 1;
                        @include font14;
                        color: $textColor1;
                        font-weight: 600;
                        text-align: left;
                        min-height: 24px;
                        white-space: nowrap;
                        overflow: hidden;
                        text-overflow: ellipsis;
                    }
                }
                .category_items {
                    display: flex;
                    flex-direction: column;
                    align-items: flex-start;
                    justify-content: flex-start;
                    padding: 12px 20px 20px;
                    flex: 1;
                    a {
                        display: block;
                        width: 100%;
                        min-height: 20px;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        white-space: nowrap;
                        @include font14;
                        font-weight: 400;
                        color: $textColor3;
                        text-decoration: none;
                        &:not(:last-child) {
                            margin-bottom: 8px;
                        }
                        &:hover {
                            text-decoration: underline;
                        }
                        &.dis_a {
                            &:hover {
                                cursor: text;
                                text-decoration: none;
                            }
                        }
                    }
                }
            }
            .swiper-container {
                padding-bottom: 68px;
            }
            ::v-deep {
                .swiper-slide {
                    height: auto;
                    width: calc((100% - 60px) / 4);
                    margin-right: 20px;
                    .product {
                        height: 100%;
                        display: flex;
                        flex-direction: column;
                        transition: all 0.3s;
                        &:hover {
                            border-radius: 8px;
                            box-shadow: 0px 4px 8px 0px rgba(0, 0, 0, 0.1);
                        }
                        &.dis_product {
                            box-shadow: none;
                        }
                    }
                }
                .swiper-pagination {
                    display: flex;
                    justify-content: center;
                    bottom: 40px;
                    .swiper-pagination-bullet {
                        background: #707070;
                        opacity: 0.4;
                        transition: all 0.3s;
                        border-radius: 100%;
                        &.swiper-pagination-bullet-active {
                            width: 20px;
                            opacity: 1;
                            background: #707070;
                            border-radius: 4px;
                        }
                    }
                }
            }
        }
        &:hover {
            .swiper-btn-box {
                opacity: 1;
            }
        }
    }
}
@media (max-width: 1024px) {
    .products {
        .nav_div {
            width: calc(100% - 48px);
        }
        > ul {
            width: calc(100% - 48px);
        }
        .list {
            .product_list {
                width: calc(100% - 48px) !important;
            }
        }
    }
}
@media (max-width: 767px) {
    .products {
        padding: 36px 0;
        .tit {
            @include font20;
            font-weight: 600;
        }
        .product_tabs {
            > ul {
                // width: calc(100% - 32px);
                justify-content: center;
                gap: 24px;
                > li {
                    // width: calc(50% - 8px);
                    margin-right: 0;
                    span {
                        display: block;
                    }
                }
            }
            &.has_scroll {
                position: relative;
                > ul {
                    width: calc(100% - 32px);
                    overflow: auto;
                    > li {
                        width: calc((100vw - 48px) / 2);
                        flex-shrink: 0;
                        margin-right: 16px;
                        &:last-child {
                            margin-right: 0;
                        }
                    }
                }
                .shadow {
                    display: block;
                    position: absolute;
                    width: 60px;
                    height: 100%;
                    right: 0;
                    top: 0;
                    background: linear-gradient(270deg, #ffffff 36%, rgba(255, 255, 255, 0.6) 68%, rgba(255, 255, 255, 0) 100%);
                }
            }
        }
        .nav_div {
            width: calc(100% - 32px);
        }
        .list {
            .product_list {
                width: calc(100% - 32px);
                .product_list_cont {
                    display: none;
                }
                .product_list_cont_m {
                    display: flex;
                    flex-flow: wrap;
                    justify-content: space-between;
                    > div {
                        width: 100%;
                        &:not(:first-child) {
                            margin-top: 20px;
                        }
                        .product {
                            height: 100%;
                            display: flex;
                            flex-direction: column;
                            &:hover {
                                box-shadow: none;
                            }
                        }
                    }
                }
            }
        }
    }
}
</style>
