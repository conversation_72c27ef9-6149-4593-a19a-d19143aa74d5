<template>
    <div class="solutions_group">
        <Banner :bannerData="contentData.header"></Banner>
        <div class="nav_div" ref="slideDownRef">
            <navigation-bar :nav="nav" :activeIndex="activeIndex" :slideDownOpen="slideDownOpen" @pageNavClick="pageNavClick" @clickSlideDown="clickSlideDown" @btnClickAction="btnClickAction"></navigation-bar>
        </div>
        <div ref="ref-0">
            <Paragraph :paragraph="contentData.pageNavigation"></Paragraph>
        </div>
        <div ref="ref-1">
            <Products :productData="contentData.product" :tag_id="tag_id"></Products>
        </div>
        <div ref="ref-2">
            <!-- <Preview :previewData="contentData.preview"></Preview> -->
            <PreviewNew :previewData="contentData.preview_list"></PreviewNew>
        </div>
        <div ref="ref-3">
            <Case :caseData="contentData.case"></Case>
            <DcTool v-if="isShowDcTool" />
        </div>

        <div ref="ref-4">
            <Contact :contactData="contentData.footer" :formData="contentData.form" :type_id="typeId"></Contact>
        </div>
    </div>
</template>
<script>
import Banner from "./components/Banner"
import Products from "./components/Products"
// import Preview from "./components/Preview"
import PreviewNew from "./components/PreviewNew"
import Case from "./components/Case"
import DcTool from "./components/DcTool"
import Contact from "./components/Contact"
import Paragraph from "./components/Paragraph"
import NavigationBar from "@/pages/single/QualityControl/components/NavigationBar.vue"
import { mapState } from "vuex"

export default {
    head() {
        if (this.contentData.header?.meta) {
            return {
                title: this.contentData.header?.meta || "",
                meta: [
                    {
                        hid: "title",
                        name: "title",
                        content: this.contentData.header?.meta || "",
                    },
                    {
                        hid: "description",
                        name: "description",
                        content: this.contentData.header?.meta_desc || "",
                    },
                    {
                        hid: "og:title",
                        property: "og:title",
                        content: this.contentData.header?.meta || "",
                    },
                    {
                        hid: "og:description",
                        property: "og:description",
                        content: this.contentData.header?.meta_desc || "",
                    },
                    {
                        hid: "twitter:title",
                        name: "twitter:title",
                        content: this.contentData.header?.meta || "",
                    },
                    {
                        hid: "twitter:description",
                        name: "twitter:description",
                        content: this.contentData.header?.meta_desc || "",
                    },
                ],
            }
        } else {
            return {}
        }
    },
    components: {
        Banner,
        Products,
        // Preview,
        PreviewNew,
        Case,
        DcTool,
        Contact,
        Paragraph,
        NavigationBar,
    },
    props: {
        contentData: {
            type: Object,
            default: {},
        },
        tag_id: {
            type: String,
            default: "",
        },
    },
    data() {
        return {
            languageData: {},
            typeId: "",
            activeIndex: 0,
            slideDownOpen: false,
            nav: {
                list: this.$c("solutions.CmsSolutions.nav"),
                btn: this.$c("solutions.CmsSolutions.btnTitle"),
            },
            isShowDcTool: false,
        }
    },
    computed: {
        ...mapState({
            screenWidth: (state) => state.device.screenWidth,
        }),
        bannerUrl() {
            let url = this.bannerData.banner.pc
            if (this.screenWidth >= 0) {
                if (this.screenWidth <= 768) {
                    url = this.bannerData.banner.m
                } else if (this.screenWidth > 768 && this.screenWidth <= 1024) {
                    url = this.bannerData.banner.pad || this.bannerData.banner.pc
                } else if (this.screenWidth > 1024) {
                    url = this.bannerData.banner.big ? this.bannerData.banner.big : this.bannerData.banner.pc
                }
            }
            return url
        },
    },
    created() {
        // 获取当前方案应用场景分类类型ID
        let bannerTitle = this.contentData.header.tit
        let targetElement = this.contentData.form.application_scenario.find(function (item) {
            return item.name == bannerTitle
        })
        this.typeId = targetElement ? targetElement.value : null
    },
    mounted() {
        let tag_id = window.location.href.substr(window.location.href.lastIndexOf("-") + 1)
        console.log("hfhffhfhfhhfhf===")
        tag_id = tag_id.replace(".html", "")
        if (tag_id == "S2001") {
            this.isShowDcTool = true
        } else {
            this.isShowDcTool = false
        }
        document.addEventListener("click", this.handleClickOutSide)
        window.addEventListener("scroll", this.scrollHandle)
    },
    beforeDestroy() {
        window.removeEventListener("scroll", this.scrollHandle)
    },
    methods: {
        handleClickOutSide(e) {
            const slideDownRef = this.$refs.slideDownRef
            if (slideDownRef && !slideDownRef.contains(e.target)) {
                this.slideDownOpen = false
            }
        },
        clickSlideDown() {
            this.slideDownOpen = !this.slideDownOpen
        },
        btnClickAction() {
            this.slideDownOpen = false
            let maxNum = this.screenWidth <= 1024 ? 106 : 60
            let ElTop = this.$refs["ref-4"].offsetTop
            document.querySelector("body,html").setAttribute("style", "scroll-behavior: smooth;")
            window.scrollTo(0, ElTop - maxNum)
        },
        pageNavClick(index) {
            this.slideDownOpen = false
            let maxNum = this.screenWidth <= 1024 ? 106 : 60
            let ElTop = this.$refs[`ref-${index}`].offsetTop
            console.log("indexxxx=", index)
            console.log("this.$refs111=", this.$refs[`ref-${index}`].offsetTop)
            document.querySelector("body,html").setAttribute("style", "scroll-behavior: smooth;")
            window.scrollTo(0, ElTop - maxNum)
        },
        scrollHandle() {
            const HeaderTop = this.screenWidth <= 1024 ? 36 : 0
            const scrollTop = document.documentElement.scrollTop - HeaderTop
            let fixHeight = this.screenWidth <= 1024 ? 142 : 60
            if (this.nav.list.length == 0) return
            this.nav.list.forEach((item, i) => {
                const offsetTop = this.$refs[`ref-${i}`]?.offsetTop
                if (scrollTop >= offsetTop - fixHeight) {
                    this.activeIndex = i
                }
            })
        },
    },
    watch: {
        screenWidth: {
            handler(val) {
                if (val > 768) {
                    this.slideDownOpen = false
                }
            },
            deep: true,
            immediate: true,
        },
    },
}
</script>

<style lang="scss" scoped>
::v-deep {
    img {
        display: block;
    }
}
.nav_div {
    background-color: #eeeeee;
    position: sticky;
    top: 0;
    left: 0;
    padding: 0 48px;
    z-index: 20;
    @media (max-width: 1024px) {
        padding: 0;
        top: 48px;
    }
}
</style>
