<template>
    <div class="search_page">
        <div class="search_page_container" v-loading.fullscreen="loading">
            <div class="search_page_container_head">
                <div :class="{ b_none: pageState == 4 }">
                    <bread-crumb isMShow :list="crumbs"></bread-crumb>
                    <section class="pc_nav">
                        <h4 v-if="pageState == 1">
                            <div v-if="near_synonym">
                                <div v-if="near_synonym.is_has_closet_word && near_synonym.keyword_has_data">
                                    <p v-html="subs($c('pages.Search.head.near_synonym1'), keyword, 1)"></p>
                                    <!-- <p class="correct" @click="toCorrect($event, near_synonym.closet_word)" v-html="subs($c('pages.Search.head.near_synonym2'), near_synonym.closet_word, 2)"></p> -->
                                </div>
                                <div v-else-if="near_synonym.is_has_closet_word && !near_synonym.keyword_has_data">
                                    <p v-html="subs($c('pages.Search.head.near_synonym3_0'), near_synonym.closet_word, 1)"></p>
                                    <!-- <p class="correct" v-html="subs($c('pages.Search.head.near_synonym3_1'), keyword, 3)"></p> -->
                                </div>
                            </div>
                            <div v-else v-html="subs($c('pages.Search.head.near_synonym1'), keyword, 1)"></div>
                        </h4>
                        <h4 v-else-if="pageState == 2">
                            <p v-html="subs($c('pages.Search.head.near_synonym4'), keyword, 4)"></p>
                        </h4>
                        <h4 v-else-if="pageState == 3">
                            <p v-html="subs($c('pages.Search.head.near_synonym5'), keyword, 5)"></p>
                        </h4>
                        <h4 v-else-if="pageState == 4">
                            <p v-html="subs($c('pages.Search.head.near_synonym6'), keyword, 1)"></p>
                        </h4>
                        <!-- <div v-if="tabIndex === 0 && pageState === 1">
                            <div class="display_type">
                                <i :title="$c('pages.Search.head.grid')" class="iconfont icon" :class="{active: displayType === 1}" @click="changeListType(1)">&#xe743;</i>
                                <i :title="$c('pages.Search.head.list')" class="iconfont icon" :class="{active: displayType === 2}" @click="changeListType(2)">&#xe744;</i>
                            </div>
                        </div> -->
                    </section>
                    <section class="m_nav">
                        <h4 v-if="pageState == 1">
                            <div v-if="near_synonym">
                                <div v-if="near_synonym.is_has_closet_word && near_synonym.keyword_has_data">
                                    <p v-html="subs($c('pages.Search.head.near_synonym1'), keyword, 1)"></p>
                                    <!-- <p class="correct" @click="toCorrect($event, near_synonym.closet_word)" v-html="subs($c('pages.Search.head.near_synonym2'), near_synonym.closet_word, 2)"></p> -->
                                </div>
                                <div v-else-if="near_synonym.is_has_closet_word && !near_synonym.keyword_has_data">
                                    <p v-html="subs($c('pages.Search.head.near_synonym3'), near_synonym.closet_word, 3)"></p>
                                </div>
                            </div>
                            <div v-else v-html="subs($c('pages.Search.head.near_synonym1'), keyword, 1)"></div>
                        </h4>
                        <h4 v-else-if="pageState == 2">
                            <p v-html="subs($c('pages.Search.head.m_near_synonym2'), keyword, 4)"></p>
                        </h4>
                        <h4 v-else-if="pageState == 3">
                            <p v-html="subs($c('pages.Search.head.m_near_synonym3'), keyword, 5)"></p>
                        </h4>
                        <h4 v-else-if="pageState == 4">
                            <p v-html="subs($c('pages.Search.head.m_near_synonym4'), keyword, 1)"></p>
                        </h4>
                        <div v-if="tabIndex === 0 && pageState === 1">
                            <div class="display_type">
                                <i :title="$c('pages.Search.head.grid')" class="iconfont icon" :class="{ active: displayType === 1 }" @click="changeListType(1)">&#xe743;</i>
                                <i :title="$c('pages.Search.head.list')" class="iconfont icon" :class="{ active: displayType === 2 }" @click="changeListType(2)">&#xe744;</i>
                            </div>
                        </div>
                    </section>
                    <m-search :pageState="pageState"></m-search>
                    <div class="tab" v-if="tabFlag == 3">
                        <span v-for="(v, i) in tabs" :key="i" tabindex="0" @keyup.enter="checkTabIndex(i)" @click="checkTabIndex(i)" :class="{ active: tabIndex === i }">
                            {{ `${v} (${i == 0 ? pageConfig.total : downloadNumber})` }}
                        </span>
                    </div>
                    <div class="tab" v-else-if="tabFlag == 2">
                        <span v-for="(v, i) in tabs.slice(1, 2)" tabindex="0" @keyup.enter="checkTabIndex(i)" :key="i" @click="checkTabIndex(i)" :class="{ active: tabIndex === i }">{{ `${v} (${downloadNumber})` }}</span>
                    </div>
                    <div class="tab" v-else-if="tabFlag == 1">
                        <span v-for="(v, i) in tabs.slice(0, 1)" tabindex="0" @keyup.enter="checkTabIndex(i)" :key="i" @click="checkTabIndex(i)" :class="{ active: tabIndex === i }">{{
                            `${v} (${pageConfig.total})`
                        }}</span>
                    </div>
                    <!-- <div class="tab" v-else></div> -->
                </div>
            </div>
            <div class="search_result" v-if="pageState == 1">
                <div class="result_warning" v-if="showTip && searchTip">
                    <i class="iconfont icon">&#xe718;</i>
                    <span v-html="searchTip"></span>
                </div>
                <div class="filter" v-if="tabIndex != 1">
                    <div>
                        <div>
                            <!-- <FsFilterMenu :narrow="narrow" :category="category" :filterList="filterListShow" @removeOpt="removeOpt" @setSortList="setSortList" @changeOption="changeOption" /> -->
                            <FsFilterMenu
                                :pageState="pageState"
                                :category="category"
                                :narrow="narrow"
                                :filterList="filterList"
                                :displayType="displayType"
                                :total="subs(pageConfig.total > 1 ? $c('pages.Search.search_result1.results1') : $c('pages.Search.search_result1.result_single'), pageConfig.total, 9)"
                                @changeListType="changeListType" />
                        </div>
                        <!-- <span v-if="tabIndex === 0" v-html="subs(pageConfig.total>1?$c('pages.Search.search_result1.results1'):$c('pages.Search.search_result1.result_single'),pageConfig.total,9)"></span> -->
                    </div>
                    <div class="m_icon">
                        <m-filter-menu :category="category" :narrow="narrow" :pageState="pageState"></m-filter-menu>
                        <!-- <div @click="filterMobile">
                            <i class="iconfont icon">&#xe712;</i>
                            <span>{{ $c("pages.Search.search_result1.m_icon") }}</span>
                        </div>
                        <div class="filter_display_type">
                            <i v-if="displayType == 1" :title="$c('pages.Search.search_result1.grid')" class="iconfont icon" @click="changeListType(2)">&#xe743;</i>
                            <i v-else :title="$c('pages.Search.search_result1.grid')" class="iconfont icon" @click="changeListType(1)">&#xe744;</i>
                        </div> -->
                    </div>
                </div>
                <transition name="slide_left">
                    <section class="lists" v-show="tabIndex === 0">
                        <div class="cate_narr_list" v-if="narrseleList.length">
                            <div class="cate_narr_item" v-for="(item, index) in narrseleList" :key="index" @click="delNarSelect(item)">
                                <i class="icon iconfont">&#xf30a;</i>
                                <span>{{ item.name }}</span>
                            </div>
                            <div class="clear" @click="delAllNarSelect">{{ $c("pages.Search.clean") }}</div>
                        </div>
                        <ul class="grid" v-show="displayType === 1">
                            <li v-for="(v, i) in goodsList" :key="v.products_id" :class="{ grid_li_not_en: website !== 'en', custom_li: v.is_custom_server }">
                                <section>
                                    <customized-card
                                        v-if="v.is_custom_server"
                                        :info="v"
                                        :index="i"
                                        @changeGoodsInfo="changeGoodsInfo"
                                        :displayType="displayType"
                                        :source="`search`"
                                        @routeLoading="routeLoading"
                                        @add="add"></customized-card>
                                    <GridGoodsItem v-else @add="add" @changeGoodsInfo="changeGoodsInfo" :displayType="displayType" :source="`search`" :index="i" :info="v" @routeLoading="routeLoading" />
                                </section>
                            </li>
                        </ul>
                        <ul class="list" v-show="displayType === 2">
                            <li v-for="(v, i) in goodsList" :key="v.products_id">
                                <section>
                                    <customized-card
                                        v-if="v.is_custom_server"
                                        :info="v"
                                        :index="i"
                                        @changeGoodsInfo="changeGoodsInfo"
                                        :displayType="displayType"
                                        :source="`search`"
                                        @routeLoading="routeLoading"
                                        @add="add"></customized-card>
                                    <ListGoodsItem v-else @add="add" @changeGoodsInfo="changeGoodsInfo" :displayType="displayType" :source="`search`" :index="i" :info="v" @routeLoading="routeLoading" />
                                </section>
                            </li>
                        </ul>
                        <!-- <div class="pagination" :class="{isPagShow:tabIndex==1}" v-if="pageState!=4&&pageState!=2&&pageState!=3">
                            <FsPagination :pageConfig="pageConfigCh" @changeCurrentPage="changeCurrentPage" />
                        </div> -->
                    </section>
                </transition>
                <transition name="slide_right">
                    <div class="download" v-show="tabIndex === 1">
                        <!-- <div class="tag">
                            <div>{{ $c("pages.Search.search_result1.download") }}</div>
                            <div v-html="subs(downloadNumber > 1 ? $c('pages.Search.search_result1.results2') : $c('pages.Search.search_result1.result_single'), downloadNumber, 9)"></div>
                        </div> -->
                        <div>
                            <!-- <h6>
                                <span>{{ $c("pages.Search.search_result1.download") }}</span>
                                <span v-html="subs(downloadNumber > 1 ? $c('pages.Search.search_result1.results2') : $c('pages.Search.search_result1.result_single'), downloadNumber, 9)"></span>
                            </h6> -->
                            <ul v-loading="downloadLoading">
                                <li v-for="(v, i) in downloadList" :key="i" tabindex="0" @keyup.enter.stop="clickDownloadView(v)" @click="clickDownloadView(v)">
                                    <img :src="v.file_icon" />
                                    <div>
                                        <div>
                                            <h3 v-html="subs($c('pages.Search.search_result1.h3'), v, 7)"></h3>
                                            <div>
                                                <div>
                                                    <!-- <i class="iconfont icon">&#xe632;</i> -->
                                                    <span>{{ v.file_add_time }}</span>
                                                </div>
                                                <div v-if="v.file_size">
                                                    <!-- <i class="iconfont icon">&#xf111;</i> -->
                                                    <span>{{ v.file_size }}</span>
                                                </div>
                                            </div>
                                        </div>
                                        <a href="javascript:;" tabindex="-1">{{ v.file_type === "Software" ? $c("pages.Search.search_result1.download") : $c("pages.Search.search_result1.view") }}</a>
                                        <!-- <a class="down" :href="v.file_url" target="_blank" download="download">{{$c('pages.Search.search_result1.download')}}</a> -->
                                    </div>
                                </li>
                            </ul>
                        </div>
                    </div>
                </transition>
                <!-- <div class="sendFeedback" v-if="tabIndex != 1"> -->
                <div class="sendFeedback" v-if="false">
                    <!-- <b>{{ $c('pages.Search.sendfeedbak.feedbak[0]') }}</b> -->
                    <span tabindex="0" @keyup.enter="openSendFeedback" @click="openSendFeedback">{{ $c("pages.Search.sendfeedbak.feedbak[1]") }}</span>
                    <fs-popover isAccessible>
                        <span v-if="website != 'jp'"> {{ $c("pages.Search.sendfeedbak.feedbak[15]") }}{{ $c("pages.Search.sendfeedbak.feedbak[16]") }} </span>
                        <span v-else> お客様のサイト利用満足度を向上させるために、どうぞフィードバックを投稿してください。 </span>
                    </fs-popover>
                </div>
            </div>
            <div class="search_result" v-else-if="pageState == 2">
                <div class="result_undercarriage">
                    <div class="result_warning">
                        <i class="iconfont icon">&#xe718;</i>
                        <span v-if="showTip && searchTip" v-html="searchTip"></span>
                        <span v-else v-html="subs($c('pages.Search.search_result2.result_warning'), keyword, 8)"></span>
                    </div>
                    <div class="result_404_help">
                        <div class="result_404_help_tit">
                            <span>{{ $c("pages.Search.search_result4_help.tit0") }}</span>
                        </div>
                        <div class="result_404_help_list">
                            <div class="result_404_help_item">
                                <span>
                                    <img src="https://resource.fs.com/mall/generalImg/20241211175533ceewz4.svg" />
                                    <span>{{ $c("pages.Search.search_result4_help.tit1") }}</span>
                                </span>
                                <a class="ck" tabindex="0" @keyup.enter.stop="toFree" @click.stop="toFree">{{ $c("pages.Search.search_result4_help.txt1") }}</a>
                            </div>
                            <template v-if="!isRussia">
                                <div class="result_404_help_item">
                                    <span>
                                        <img src="https://resource.fs.com/mall/generalImg/2024121117553397durs.svg" />
                                        <span>{{ $c("pages.Search.search_result4_help.tit2") }}</span>
                                    </span>
                                    <a class="ck" tabindex="0" @keyup.enter.stop="toEmail" @click.stop="toEmail">{{ email }}</a>
                                </div>
                                <div class="result_404_help_item">
                                    <span>
                                        <img src="https://resource.fs.com/mall/generalImg/20241211175533dehurh.svg" />
                                        <span>{{ $c("pages.Search.search_result4_help.tit3") }}</span>
                                    </span>
                                    <a class="cka" @click.stop="telClick" :href="'tel:' + '' + phone + ''">{{ phone }}</a>
                                </div>
                            </template>

                            <div class="result_404_help_item">
                                <span>
                                    <img src="https://resource.fs.com/mall/generalImg/20241211175533xa7lbi.svg" />
                                    <span>{{ $c("pages.Search.search_result4_help.tit4") }}</span>
                                </span>
                                <a class="ck" tabindex="0" id="bdtj_lxdh" @keyup.enter.stop="toLiveChat" @click.stop="toLiveChat">{{ $c("pages.Search.search_result4_help.ckm") }}</a>
                            </div>
                        </div>
                    </div>
                    <div class="category_404">
                        <div class="category_404_title">{{ $c("single.siteMap.all_title.category") }}</div>
                        <div class="category_404_box" :class="{ maxHeight: category_show_more }">
                            <div class="category_404_box_item" v-for="(item, index) in showCategoryList" :key="index" @click="toCategory(item)">
                                <img :src="category_icon[index]" />
                                <div>
                                    <span>{{ item.name }}</span>
                                </div>
                            </div>
                            <div v-if="isMobile" class="see_more_btn" @click.stop="moreClick(index)">
                                <span class="btn_info">{{ category_show_more ? $c("pages.Products.See_less") : $c("pages.Products.See_more") }}</span>
                                <span class="iconfont iconfont_down" :class="{ iconfont_down_up: category_show_more }">&#xe704;</span>
                            </div>
                        </div>
                    </div>
                    <div class="recommend" v-if="hotProducts && hotProducts.length">
                        <recently-viewed :isLoHot_re="isLoHot_re" :list="hotProducts" type="products" :pageState="pageState" @quickAdd="recToCart"></recently-viewed>
                    </div>
                </div>
            </div>
            <div class="search_result" v-else-if="pageState == 3">
                <div class="result_only">
                    <transition name="slide_left">
                        <div class="lists" v-show="tabIndex === 0">
                            <div class="result_warning">
                                <i class="iconfont icon">&#xe718;</i>
                                <span v-html="subs($c('pages.Search.search_result3.result_warning'), keyword, 8)"></span>
                            </div>
                            <div class="result_only_flag" v-if="replaceType == 2 || replaceType == 3">
                                <template v-if="replaceType == 2">
                                    {{ $c("pages.Products.similar_product_recommended") }}
                                </template>
                                <template v-else>
                                    {{ $c("pages.Search.search_result3.result_only_flag") }}
                                </template>
                            </div>
                            <ul class="list">
                                <li v-for="(v, i) in goodsList" :key="v.products_id">
                                    <customized-card
                                        v-if="v.is_custom_server"
                                        :info="v"
                                        :index="i"
                                        :displayType="2"
                                        @changeGoodsInfo="changeGoodsInfo"
                                        @routeLoading="routeLoading"
                                        @add="add"
                                        :source="`search`"></customized-card>
                                    <ListGoodsItem v-else @add="add" @changeGoodsInfo="changeGoodsInfo" :index="i" :info="v" @routeLoading="routeLoading" :source="`search`" />
                                </li>
                            </ul>
                            <div class="result_404_help">
                                <div class="result_404_help_tit">
                                    <span>{{ $c("pages.Search.search_result4_help.tit0") }}</span>
                                </div>
                                <div class="result_404_help_list">
                                    <div class="result_404_help_item">
                                        <span>
                                            <img src="https://resource.fs.com/mall/generalImg/20241211175533ceewz4.svg" />
                                            <span>{{ $c("pages.Search.search_result4_help.tit1") }}</span>
                                        </span>
                                        <a class="ck" tabindex="0" @keyup.enter.stop="toFree" @click.stop="toFree">{{ $c("pages.Search.search_result4_help.txt1") }}</a>
                                    </div>
                                    <template v-if="!isRussia">
                                        <div class="result_404_help_item">
                                            <span>
                                                <img src="https://resource.fs.com/mall/generalImg/2024121117553397durs.svg" />
                                                <span>{{ $c("pages.Search.search_result4_help.tit2") }}</span>
                                            </span>
                                            <a class="ck" tabindex="0" @keyup.enter.stop="toEmail" @click.stop="toEmail">{{ email }}</a>
                                        </div>
                                        <div class="result_404_help_item">
                                            <span>
                                                <img src="https://resource.fs.com/mall/generalImg/20241211175533dehurh.svg" />
                                                <span>{{ $c("pages.Search.search_result4_help.tit3") }}</span>
                                            </span>
                                            <a class="cka" @click.stop="telClick" :href="'tel:' + '' + phone + ''">{{ phone }}</a>
                                        </div>
                                    </template>

                                    <div class="result_404_help_item">
                                        <span>
                                            <img src="https://resource.fs.com/mall/generalImg/20241211175533xa7lbi.svg" />
                                            <span>{{ $c("pages.Search.search_result4_help.tit4") }}</span>
                                        </span>
                                        <a class="ck" tabindex="0" id="bdtj_lxdh" @keyup.enter.stop="toLiveChat" @click.stop="toLiveChat">{{ $c("pages.Search.search_result4_help.ckm") }}</a>
                                    </div>
                                </div>
                            </div>
                            <div class="category_404">
                                <div class="category_404_title">{{ $c("single.siteMap.all_title.category") }}</div>
                                <div class="category_404_box" :class="{ maxHeight: category_show_more }">
                                    <div class="category_404_box_item" v-for="(item, index) in showCategoryList" :key="index" @click="toCategory(item)">
                                        <img :src="category_icon[index]" />
                                        <div>
                                            <span>{{ item.name }}</span>
                                        </div>
                                    </div>
                                    <div v-if="isMobile" class="see_more_btn" @click.stop="moreClick(index)">
                                        <span class="btn_info">{{ category_show_more ? $c("pages.Products.See_less") : $c("pages.Products.See_more") }}</span>
                                        <span class="iconfont iconfont_down" :class="{ iconfont_down_up: category_show_more }">&#xe704;</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </transition>
                    <transition name="slide_right">
                        <div class="download" v-show="tabIndex === 1">
                            <div class="tag">
                                <div>{{ $c("pages.Search.search_result3.download") }}</div>
                                <div v-html="subs(downloadNumber > 1 ? $c('pages.Search.search_result3.results2') : $c('pages.Search.search_result3.result_single'), downloadNumber, 9)"></div>
                            </div>
                            <div>
                                <h6>
                                    <span>{{ $c("pages.Search.search_result3.download") }}</span>
                                    <span v-html="subs(downloadNumber > 1 ? $c('pages.Search.search_result3.results2') : $c('pages.Search.search_result3.result_single'), downloadNumber, 9)"></span>
                                </h6>
                                <ul v-loading="downloadLoading">
                                    <li v-for="(v, i) in downloadList" :key="i" @click="clickDownloadView(v)">
                                        <img :src="v.file_icon" />
                                        <div>
                                            <div>
                                                <h3 v-html="subs($c('pages.Search.search_result3.h3'), v, 7)"></h3>
                                                <div>
                                                    <div>
                                                        <i class="iconfont icon">&#xe632;</i>
                                                        <span>{{ v.file_add_time }}</span>
                                                    </div>
                                                    <div>
                                                        <i class="iconfont icon">&#xf111;</i>
                                                        <span>{{ v.file_size }}</span>
                                                    </div>
                                                </div>
                                            </div>
                                            <a href="javascript:;" tabindex="0">{{ v.file_type === "Software" ? $c("pages.Search.search_result1.download") : $c("pages.Search.search_result1.view") }}</a>
                                            <!-- <a class="down" :href="v.file_url" target="_blank" download="download">{{$c('pages.Search.search_result3.download')}}</a> -->
                                        </div>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </transition>
                </div>
            </div>
            <div class="search_result" v-else-if="pageState == 4">
                <div class="result_404">
                    <div class="result_404_container">
                        <div class="result_404_left">
                            <p class="result_404_tit">{{ $c("pages.Search.search_result4.result_404_tit1") }}</p>
                            <p class="result_404_txt">
                                <i></i>
                                <span>{{ $c("pages.Search.search_result4.result_404_txt1") }}</span>
                            </p>
                            <p class="result_404_txt">
                                <i></i>
                                <span>{{ $c("pages.Search.search_result4.result_404_txt2") }}</span>
                            </p>
                            <p class="result_404_txt">
                                <i></i>
                                <span>{{ $c("pages.Search.search_result4.result_404_txt3") }}</span>
                            </p>
                            <p class="result_404_txt" v-if="['firmware', 'download', 'visio'].indexOf(keyword.toLowerCase()) != -1">
                                <i></i>
                                <span v-html="subs($c('pages.Search.head.tech_docum'), keyword, 10)"></span>
                            </p>
                            <p class="result_404_txt">
                                <i></i>
                                <span v-if="website != 'jp'">
                                    <b tabindex="0" @keyup.enter="openSendFeedback" @click="openSendFeedback">{{ $c("pages.Search.sendfeedbak.feedbak[15]") }}</b
                                    >{{ $c("pages.Search.sendfeedbak.feedbak[16]") }}
                                </span>
                                <span v-else> お客様のサイト利用満足度を向上させるために、どうぞ<b @click="openSendFeedback">フィードバック</b>を投稿してください。 </span>
                            </p>
                        </div>
                    </div>
                    <div class="result_404_help">
                        <div class="result_404_help_tit">
                            <span>{{ $c("pages.Search.search_result4_help.tit0") }}</span>
                        </div>
                        <div class="result_404_help_list">
                            <div class="result_404_help_item">
                                <span>
                                    <img src="https://resource.fs.com/mall/generalImg/20241211175533ceewz4.svg" />
                                    <span>{{ $c("pages.Search.search_result4_help.tit1") }}</span>
                                </span>
                                <a class="ck" tabindex="0" @keyup.enter.stop="toFree" @click.stop="toFree">{{ $c("pages.Search.search_result4_help.txt1") }}</a>
                            </div>
                            <template v-if="!isRussia">
                                <div class="result_404_help_item">
                                    <span>
                                        <img src="https://resource.fs.com/mall/generalImg/2024121117553397durs.svg" />
                                        <span>{{ $c("pages.Search.search_result4_help.tit2") }}</span>
                                    </span>
                                    <a class="ck" tabindex="0" @keyup.enter.stop="toEmail" @click.stop="toEmail">{{ email }}</a>
                                </div>
                                <div class="result_404_help_item">
                                    <span>
                                        <img src="https://resource.fs.com/mall/generalImg/20241211175533dehurh.svg" />
                                        <span>{{ $c("pages.Search.search_result4_help.tit3") }}</span>
                                    </span>
                                    <a class="cka" @click.stop="telClick" :href="'tel:' + '' + phone + ''">{{ phone }}</a>
                                </div>
                            </template>

                            <div class="result_404_help_item">
                                <span>
                                    <img src="https://resource.fs.com/mall/generalImg/20241211175533xa7lbi.svg" />
                                    <span>{{ $c("pages.Search.search_result4_help.tit4") }}</span>
                                </span>
                                <a class="ck" tabindex="0" id="bdtj_lxdh" @keyup.enter.stop="toLiveChat" @click.stop="toLiveChat">{{ $c("pages.Search.search_result4_help.ckm") }}</a>
                            </div>
                        </div>
                    </div>
                    <div class="category_404">
                        <div class="category_404_title">{{ $c("single.siteMap.all_title.category") }}</div>
                        <div class="category_404_box" :class="{ maxHeight: category_show_more }">
                            <div class="category_404_box_item" v-for="(item, index) in showCategoryList" :key="index" @click="toCategory(item)">
                                <img :src="category_icon[index]" />
                                <div>
                                    <span>{{ item.name }}</span>
                                </div>
                            </div>
                            <div v-if="isMobile" class="see_more_btn" @click.stop="moreClick(index)">
                                <span class="btn_info">{{ category_show_more ? $c("pages.Products.See_less") : $c("pages.Products.See_more") }}</span>
                                <span class="iconfont iconfont_down" :class="{ iconfont_down_up: category_show_more }">&#xe704;</span>
                            </div>
                        </div>
                    </div>
                    <div class="recommend" v-if="hotProducts && hotProducts.length">
                        <recently-viewed :isLoHot_re="isLoHot_re" :list="hotProducts" type="products" :pageState="pageState" @quickAdd="recToCart"></recently-viewed>
                    </div>
                </div>
            </div>
        </div>
        <!-- <shop-car-box></shop-car-box> -->
        <shop-ball ref="shopball"></shop-ball>
        <!-- <AddCartSuccessPopup class="listAddPop" :size="true" :show="showPop" @close="hidePop" @shopPop="shopPop" ref="addCartPopup" :correspondingTerm="goodsList && goodsList.length ? goodsList[index] : {}" /> -->

        <AddCartSuccess :show="showPop" @close="hidePop" @shopPop="shopPop" ref="addCartPopup"></AddCartSuccess>

        <!-- <addCartSuccess :show="popup.show_add_success" :price="product_info.products_price_str" :model="old_product_info.products_model" @close="closePopup('show_add_success')" ref="addCartPopup"></addCartSuccess> -->

        <!-- <AddCartPopup :show="showPop" @close="hidePop" @shopPop="shopPop" @showSurePopCheck="showSurePopCheck" ref="addCartPopup" /> -->
        <!-- <AddCartSuccess :show="showSurePop" @close="hideSurePop" /> -->
        <div class="pagination" :class="{ isPagShow: tabIndex == 1, b_40: pageConfigCh.total <= 12 }" v-if="pageState != 4 && pageState != 2 && pageState != 3">
            <FsPagination v-bind="{ ...pageConfigCh }" @change="changeCurrentPage" @clickBuried="clickBuried" />
            <div class="sendFeedback_m" v-if="false">
                {{ $c("pages.Search.sendfeedbak.feedbak[0]") }}
                <span @click="openSendFeedback">{{ $c("pages.Search.sendfeedbak.feedbak[1]") }}</span>
            </div>
        </div>
        <!-- <send-feedback :show="showSendFeedback" :state="pageState" @close="closeSendFeedback"></send-feedback> -->
        <!-- <scoring @close="closeScoring" :showScoring="showScoring"></scoring> -->
        <related-searche v-if="pageState == 1 && relateList && relateList.length" @routeLoading="routeLoading" :relateList="relateList"></related-searche>
    </div>
</template>
<script>
import ShopCarBox from "@/components/ShopCarBox/ShopCarBox"
import ShopBall from "@/components/ShopBall/ShopBall"
import BreadCrumb from "@/components/BreadCrumb/BreadCrumb"
import FsFilterMenu from "@/components/ListCardMenu/CateFilterMenu"
import GridGoodsItem from "@/components/ListCardMenu/GridGoodsItem"
import ListGoodsItem from "@/components/ListCardMenu/ListGoodsItem"
import FsPagination from "@/components/FsPagination/index"
import CustomizedCard from "@/components/ListCardMenu/CustomizedCard"
import FsSelect from "@/components/ListCardMenu/CateSelect"
import AddCartPopup from "@/components/AddCartPopup/AddCartPopup"
import FsPopup from "@/components/FsPopup/FsPopup.vue"
import FsButton from "@/components/FsButton/FsButton.vue"
import AddCartSuccessPopup from "@/popup/AddCartSuccessPopup/AddCartSuccessPopup.vue"
import RecentlyViewed from "./components/RecentlyViewed.vue"
import MFilterMenu from "@/components/ListCardMenu/MFilterMenu"
import SendFeedback from "./components/SendFeedback.vue"
import Scoring from "./components/Scoring.vue"
import FsPopover from "@/components/FsPopover"
import { mapState, mapGetters } from "vuex"
import { Swiper, SwiperSlide } from "vue-awesome-swiper"
import "swiper/css/swiper.css"
import { liveChat, filterParameters, searchResultBuried, goodsItemBuried, grouping, searchResultPV, parseSearchParams } from "@/util/util.js"
import AES from "@/util/AES.js"
import RelatedSearche from "./components/RelatedSearche.vue"
import MSearch from "./components/MSearch.vue"
import AddCartSuccess from "@/popup/AddCartSuccess/AddCartSuccess"
export default {
    components: {
        ShopCarBox,
        ShopBall,
        BreadCrumb,
        FsFilterMenu,
        GridGoodsItem,
        ListGoodsItem,
        FsPagination,
        FsSelect,
        AddCartPopup,
        FsPopup,
        FsButton,
        Swiper,
        SwiperSlide,
        RecentlyViewed,
        // AddCartSuccessPopup,
        AddCartSuccess,
        CustomizedCard,
        MFilterMenu,
        SendFeedback,
        Scoring,
        FsPopover,
        RelatedSearche,
        MSearch,
    },
    data() {
        return {
            isMobile: false,
            loading: false,
            crumbs: [
                { name: this.$c("pages.Search.crumbs.home"), url: "/" },
                { name: this.$c("pages.Search.crumbs.results"), url: "" },
            ],
            displayType: 1,
            sizeList: [
                { name: "12", value: 12 },
                { name: "24", value: 24 },
                { name: "48", value: 48 },
            ],
            showPop: false,
            // showSurePop: false,
            tabs: [this.$c("pages.Search.tabs.pd"), this.$c("pages.Search.tabs.dr")],
            tabIndex: 0,
            // add
            goodsList: [],
            downloadList: [],
            downloadNumber: 0,
            pageState: null,
            replaceType: 2,
            swiperProduct: {
                slidesPerView: 5,
                spaceBetween: 16,
                slidesPerGroup: 5,
                navigation: {
                    nextEl: ".swiper-button-next",
                    prevEl: ".swiper-button-prev",
                },
                pagination: {
                    el: ".swiper-pagination",
                    clickable: true,
                },
                loop: false,
            },
            keyword: null,
            pageConfig: {},
            sortOrder: null,
            hotProducts: [],
            newHotProducts: [],
            hotFlag: false,
            near_synonym: {},
            pageConfigCh: {},
            mSwiperNum: 5,
            tabFlag: 0,
            startPos: {},
            stopPos: {},
            visualHeight: null,
            scrollNumber: 0,
            snapIndex: 0,
            narrseleList: [],
            isLoHot_re: false,
            isPad: 0,
            cateIdNum: null,
            oldKeyword: "",
            index: null,
            filterList: [],
            downloadLoading: false,
            showSendFeedback: false,
            showScoring: false,
            submitSatisfaction: false,
            relateList: [],
            showTip: false,
            searchTip: "",
            category_icon: [
                "https://resource.fs.com/mall/generalImg/202412111402177nwe77.svg",
                "https://resource.fs.com/mall/generalImg/202412111402174ka965.svg",
                "https://resource.fs.com/mall/generalImg/20241211140249n0gfcb.svg",
                "https://resource.fs.com/mall/generalImg/202412111402491f40u8.svg",
                "https://resource.fs.com/mall/generalImg/202412111402498ihp6h.svg",
                "https://resource.fs.com/mall/generalImg/20241211140249oa5gyo.svg",
                "https://resource.fs.com/mall/generalImg/20241211140249zw0d4p.svg",
                "https://resource.fs.com/mall/generalImg/20241211140249otcfam.svg",
            ],
            category_show_more: false,
        }
    },
    inject: ["fsLiveChat"],
    watch: {
        $route(val, old) {
            this.loading = true
            const oldQuery = parseSearchParams(old.fullPath)
            const newQuery = parseSearchParams(val.fullPath)
            this.oldKeyword = oldQuery.keyword
            this.keyword = newQuery.keyword
            if (this.keyword != this.oldKeyword || !newQuery.page) {
                this.pageConfig.pageNo = 1
            }
            newQuery.page
            this.cateIdNum = newQuery.categories_id
            let filterMap = {}
            if (newQuery) {
                for (let i in newQuery) {
                    if (filterParameters(i)) {
                        filterMap[i] = newQuery[i].split(",")
                    }
                }
            }
            if (this.keyword == "switches" || this.keyword == "ethernet switch" || this.keyword == "network switch" || this.keyword == "fiber switch" || this.keyword == "10gb switch" || this.keyword == "fibre switch") {
                let routeData = this.$router.resolve(this.localePath({ name: "list", params: { id: "enterprise-switches-3079" } }))
                window.open(routeData.href, "_blank")
            } else {
                this.$axios
                    .post("/api/search/indexPost", {
                        keyword: this.keyword,
                        sort_order: newQuery.sort_order ? newQuery.sort_order : "popularity",
                        filter_map: filterMap,
                        page: this.pageConfig.pageNo,
                        count: this.pageConfig.pageSize,
                        url: val.fullPath,
                        categories_id: this.cateIdNum,
                    })
                    .then((res) => {
                        this.loading = false
                        if (res.code !== 200) return
                        if (res.data.productInfo && res.data.productInfo.length > 0) {
                            res.data.productInfo.forEach((item, index) => {
                                item.posIndex = index + 1
                                item.pageState = res.data.pageState
                            })
                        }
                        this.goodsList = res.data.productInfo
                        if (res.data.productInfo && res.data.productInfo.length) {
                            if (this.screenWidth > 1240) {
                                this.getNewArr(this.goodsList, 4)
                            } else if (this.screenWidth >= 768) {
                                this.getNewArr(this.goodsList, 3)
                            } else {
                                this.getNewArr(this.goodsList, 2)
                            }
                        }
                        this.relateList = res.data.relateList
                        this.tabIndex = 0
                        if (res.data.total && res.data.total > 0 && res.data.downloadNumber) {
                            this.tabFlag = 3
                        } else if (res.data.total && res.data.total > 0) {
                            this.tabFlag = 1
                        } else if (res.data.download && res.data.downloadNumber) {
                            this.tabFlag = 2
                        } else {
                            this.tabFlag = 0
                        }
                        let filterList = []
                        for (let i in filterMap) {
                            filterMap[i].forEach((item) => {
                                filterList.push(item)
                            })
                        }
                        this.filterList = filterList
                        this.narrow = res.data.narrowInfo ? res.data.narrowInfo : []
                        this.pageState = res.data.pageState
                        this.replaceType = res.data.replaceType
                        this.pageConfig.total = res.data.total
                        this.downloadList = res.data.download
                        this.downloadNumber = res.data.downloadNumber
                        this.near_synonym = res.data.near_synonym
                        this.searchTip = res.data.tip
                        this.showTip = res.data.showTip
                        this.pageViewBuried()
                        if (res.data.pageState == 4 || res.data.pageState == 2) {
                            if (res.data.hot_products.length > 0) {
                                res.data.hot_products.map((item, index) => {
                                    item.posIndex = index + 1
                                })
                                if (!this.isLogin) {
                                    this.hotProducts = res.data.hot_products
                                } else {
                                    let recentlyProducts = JSON.parse(localStorage.getItem("recentlyProducts"))
                                    if (recentlyProducts && recentlyProducts.length >= 6) {
                                        recentlyProducts = recentlyProducts.slice(0, 12)
                                        this.isLoHot_re = true
                                        let arr = []
                                        this.$axios.post("/api/special_index", { products_ids: recentlyProducts, products_status: 1, page: undefined }).then((res) => {
                                            if (res.data && res.data.length) {
                                                res.data.map((item) => {
                                                    arr.push({
                                                        images_arr: item.image,
                                                        inventory_des: item.current_qty,
                                                        products_id: item.products_id,
                                                        products_name: item.products_name,
                                                        products_price: item.products_price_str,
                                                        products_price_number: item.products_price,
                                                    })
                                                })
                                                this.hotProducts = arr
                                            }
                                        })
                                    }
                                }
                                this.hotFlag = true
                                this.snapIndex = 0
                                this.hotOrRecom(this.snapIndex)
                            }
                        }
                        this.$nextTick(() => {
                            window.scrollTo({ top: 0, behavior: "smooth" })
                        })

                        if (this.oldKeyword && this.keyword != this.oldKeyword) {
                            if (res.data.categoryInfo) {
                                this.category = [res.data.categoryInfo]
                                this.oldKeyword = ""
                            }
                        }
                    })
                    .catch((error) => {
                        this.loading = false
                        if (error.code === 422) {
                            this.$router.replace(this.localePath("/500.html"))
                        }
                    })
            }
        },
        pageConfig: {
            deep: true,
            handler(val) {
                this.pageConfigCh = JSON.parse(JSON.stringify(val))
            },
        },
        goodsList(val) {
            this.getScreenListBuried()
        },
        scrollNumber(val, old) {
            if (old < val) {
                this.scrollScreenListBuried()
            }
        },
        visualHeight(val, old) {
            if (val > old && old) {
                let res = grouping(this.goodsList, this.displayType == 1 ? 4 : 1)
                if (val <= res.length) {
                    let arr = []
                    res[val - 1].map((item) => {
                        arr.push({
                            list: document.title.substring(0, 100),
                            id: item.products_id,
                            name: item.products_name.substring(0, 100),
                            price: item.price_us,
                            position: item.posIndex,
                        })
                    })
                    goodsItemBuried("exposure", { list: arr, currency: this.currency, searchContent: this.$route.query.keyword }, "search", this.pageState)
                }
            }
        },
        snapIndex(val, old) {
            if (val > old) {
                this.hotOrRecom(val)
            }
        },
    },
    computed: {
        pageSizeShow() {
            //条数选择是否显示
            return Math.ceil(parseInt(this.pageConfig.total) / parseInt(this.pageConfig.pageSize)) > 1 ? true : false
        },
        showCategoryList() {
            if (this.category_show_more === false && this.isMobile === false) {
                return this.category_data.data
            } else if (this.isMobile) {
                if (this.category_show_more) {
                    return this.category_data.data
                } else {
                    return this.category_data.data.slice(0, 4)
                }
            }
        },
        ...mapState({
            title: (state) => state.meta.title,
            website: (state) => state.webSiteInfo.website,
            email: (state) => state.category.email,
            phone: (state) => state.category.phone,
            isLogin: (state) => state.userInfo.isLogin,
            isFirstLogin: (state) => state.userInfo.isFirstLogin,
            userInfo: (state) => state.userInfo.userInfo,
            domain: (state) => state.meta.domain,
            qty: (state) => state.cart.qty,
            currency: (state) => state.webSiteInfo.currency,
            iso_code: (state) => state.webSiteInfo.iso_code,
            language: (state) => state.webSiteInfo.language,
            pageTitle: (state) => state.meta.title,
            pageLocation: (state) => state.ga.pageLocation,
            pre_url: (state) => state.webSiteInfo.pre_url,
            screenWidth: (state) => state.device.screenWidth,
            category_data: (state) => state.category.category_data,
        }),
        ...mapGetters({
            gaLoginString: "userInfo/gaLoginString",
            isRussia: "webSiteInfo/isRussia",
        }),
    },
    asyncData({ app, route, store, redirect }) {
        let iso_code = store.state.webSiteInfo.iso_code
        const queryParams = parseSearchParams(route.fullPath) || {}
        if (queryParams.keyword && queryParams.keyword.match(/<|>/g)) {
            redirect(app.localePath({ name: "search_result", query: { keyword: queryParams.keyword.replace(/<|>/g, "") } }))
        }
        let cateId = ""
        let filterMap = {}
        if (queryParams) {
            for (let i in queryParams) {
                if (filterParameters(i)) {
                    filterMap[i] = queryParams[i].split(",")
                } else if (i == "categories_id") {
                    cateId = queryParams[i]
                }
            }
        }
        if (!queryParams.keyword) {
            redirect(app.localePath("/500.html"))
        } else if (
            queryParams.keyword == "switches" ||
            queryParams.keyword == "ethernet switch" ||
            queryParams.keyword == "network switch" ||
            queryParams.keyword == "fiber switch" ||
            queryParams.keyword == "10gb switch" ||
            queryParams.keyword == "fibre switch"
        ) {
            redirect(app.localePath({ name: "list", params: { id: "enterprise-switches-3079" } }))
        } else if ((iso_code === "US" || iso_code === "GB" || iso_code === "IN") && queryParams.keyword.includes("FHD®")) {
            redirect(app.localePath({ path: "/search_result", query: { keyword: queryParams.keyword.split("®").join("") } }))
        } else {
            return app.$axios
                .post("/api/search/indexPost", {
                    keyword: queryParams.keyword,
                    sort_order: queryParams.sort_order ? queryParams.sort_order : "popularity",
                    filter_map: filterMap,
                    page: queryParams.page,
                    count: queryParams.count,
                    url: route.fullPath,
                    categories_id: cateId,
                })
                .then(async (res) => {
                    let tabFlag = null
                    let hot = []
                    if (res.data.total && res.data.total > 0 && res.data.downloadNumber) {
                        tabFlag = 3
                    } else if (res.data.total && res.data.total > 0) {
                        tabFlag = 1
                    } else if (res.data.download && res.data.downloadNumber) {
                        tabFlag = 2
                    } else {
                        tabFlag = 0
                    }
                    let isLoHot_re = false
                    if (res.data.pageState == 4 || res.data.pageState == 2) {
                        if (res.data.hot_products.length > 0) {
                            res.data.hot_products.map((item, index) => {
                                item.posIndex = index + 1
                            })
                        }
                        if (!app.store.state.userInfo.isLogin) {
                            hot = res.data.hot_products
                        } else {
                            let recentlyProducts = app.$cookies.get("recentlyProducts") || []
                            if (recentlyProducts && recentlyProducts.length >= 6) {
                                recentlyProducts = recentlyProducts.slice(0, 12)
                                isLoHot_re = true
                                await app.$axios.post("/api/special_index", { products_ids: recentlyProducts, products_status: 1, page: undefined }).then((res) => {
                                    if (res.data && res.data.length) {
                                        res.data.map((item) => {
                                            hot.push({
                                                images_arr: item.image,
                                                inventory_des: item.current_qty,
                                                products_id: item.products_id,
                                                products_name: item.products_name,
                                                products_price: item.products_price_str,
                                                products_price_number: item.products_price,
                                            })
                                        })
                                    }
                                })
                            } else {
                                hot = res.data.hot_products
                            }
                        }
                    }
                    if (res.data.productInfo && res.data.productInfo.length > 0) {
                        res.data.productInfo.forEach((item, index) => {
                            item.posIndex = index + 1
                            item.pageState = res.data.pageState
                        })
                    }
                    let filterList = []
                    for (let i in filterMap) {
                        filterMap[i].forEach((item) => {
                            filterList.push(item)
                        })
                    }
                    let cateList = []
                    if (res.data.categoryInfo) {
                        cateList.push(res.data.categoryInfo)
                    }
                    return {
                        keyword: queryParams.keyword,
                        goodsList: res.data.productInfo,
                        narrow: res.data.narrowInfo ? res.data.narrowInfo : [],
                        filterList: filterList,
                        category: cateList,
                        pageState: res.data.pageState,
                        replaceType: res.data.replaceType,
                        downloadList: res.data.download,
                        downloadNumber: res.data.downloadNumber,
                        sortOrder: queryParams.sort_order ? queryParams.sort_order : "popularity",
                        tabFlag: tabFlag,
                        pageConfig: {
                            pageSize: queryParams.count ? Number(queryParams.count) : 12,
                            pageNo: queryParams.page ? Number(queryParams.page) : 1,
                            total: res.data.total,
                        },
                        hotProducts: hot.length > 0 ? hot : [],
                        near_synonym: res.data.near_synonym,
                        isLoHot_re: isLoHot_re,
                        submitSatisfaction: res.data.submitSatisfaction,
                        relateList: res.data.relateList,
                        searchTip: res.data.tip,
                        showTip: res.data.showTip,
                    }
                })
                .catch((err) => {
                    if (err.code === 422) {
                        redirect(app.localePath("/500.html"))
                    }
                })
        }
    },
    methods: {
        // 切换分页
        changeCurrentPage(n) {
            if (n != this.pageConfig.pageNo) {
                this.pageConfig.pageNo = n
                this.$router.push({ query: { ...this.$route.query, page: n } })
            }
        },
        add(n) {
            this.showPop = true
            this.index = n.index
            this.$refs.addCartPopup.addToCart(n.products_id)
        },
        //关闭添加弹窗
        hidePop() {
            this.showPop = false
        },
        changeGoodsInfo(n) {
            this.goodsList[n.index] = Object.assign(this.goodsList[n.index], n.data)
        },
        // 跳转到商品详情
        toProductsId(v, txt) {
            let routeData = this.$router.resolve(this.localePath({ name: "products", params: { id: v.products_id } }))
            window.open(routeData.href, "_blank")
            goodsItemBuried(
                "hOrR",
                {
                    currency: this.currency,
                    searchContent: this.$route.query.keyword,
                    hOrR: txt,
                    products_id: v.products_id,
                    index: v.posIndex,
                    price: v.products_price_number,
                    products_name: v.products_name.substring(0, 100),
                },
                "search",
                this.pageState
            )
        },
        // 跳转到纠正词
        toCorrect(e, txt) {
            console.log(e.target.localName)
            if (e.target.localName == "em") {
                if (txt == "switches" || txt == "ethernet switch" || txt == "network switch" || txt == "fiber switch" || txt == "10gb switch" || txt == "fibre switch") {
                    let routeData = this.$router.resolve(this.localePath({ name: "list", params: { id: "enterprise-switches-3079" } }))
                    window.open(routeData.href, "_blank")
                } else {
                    this.$router.push(this.localePath({ name: "search_result", query: { keyword: txt } }))
                }
            }
        },
        // 关键字触犯置顶
        keyWordTop(data) {
            if (this.keyword.indexOf("converter") != -1 || this.keyword.indexOf("transponder") != -1) {
                data.map((item, index) => {
                    if (item.products_id == 107365) {
                        data.unshift(data.splice(index, 1)[0])
                    }
                })
                return data
            } else if (this.keyword.indexOf("switch") != -1) {
                data.map((item, index) => {
                    if (item.products_id == 108716 || item.products_id == 108710) {
                        data.unshift(data.splice(index, 1)[0])
                    }
                })
                return data
            } else {
                return data
            }
        },
        toLiveChat() {
            this.fsLiveChat()
            searchResultBuried("help", "LiveChat", this.pageState)
            this.$bdRequest({
                conversionTypes: [
                    {
                        logidUrl: location.href,
                        newType: 1,
                    },
                ],
            })
        },
        toFree() {
            this.$bdRequest({
                conversionTypes: [
                    {
                        logidUrl: location.href,
                        newType: 20,
                    },
                ],
            })
            searchResultBuried("help", "TechnicalSupport", this.pageState)
            this.$router.push(this.localePath({ name: "tech_support" }))
        },
        toEmail() {
            searchResultBuried("help", "Email", this.pageState)
            window.location.href = "mailto:" + this.email
            this.$bdRequest({
                conversionTypes: [
                    {
                        logidUrl: location.href,
                        newType: 27,
                    },
                ],
            })
        },
        telClick() {
            this.$bdRequest({
                conversionTypes: [
                    {
                        logidUrl: location.href,
                        newType: 2,
                    },
                ],
            })
        },
        // 替换语言包中的变量
        subs(str, n, t) {
            if (t == 1) {
                if (this.website == "jp") {
                    return str.replace("xxxx", "<em>「" + n + "」</em>")
                } else if (this.website == "de") {
                    return str.replace("xxxx", "<em>" + n + "</em>")
                } else if (this.website == "ru") {
                    return str.replace("xxxx", "<em>«" + n + "»</em>")
                } else {
                    return str.replace("xxxx", '<em>"' + n + '"</em>')
                }
            } else if (t == 2) {
                if (this.website == "jp") {
                    return str.replace("xxxx", "<span>「<em>" + n + "</em>」</span>")
                } else if (this.website == "de") {
                    return str.replace("xxxx", "<em>" + n + "</em>")
                } else if (this.website == "ru") {
                    return str.replace("xxxx", "<span>«<em>" + n + "</em>»</span>")
                } else {
                    return str.replace("xxxx", '<span>"<em>' + n + '</em>"</span>')
                }
            } else if (t == 3) {
                if (this.website == "jp") {
                    return str.replace("xxxx", '<span>「<em class="und">' + n + "</em>」</span>")
                } else if (this.website == "de") {
                    return str.replace("xxxx", '<em class="und">' + n + "</em>")
                } else if (this.website == "ru") {
                    return str.replace("xxxx", '<span>«<em class="und">' + n + "</em>»</span>")
                } else {
                    return str.replace("xxxx", '<span>"<em class="und">' + n + '</em>"</span>')
                }
            } else if (t == 4) {
                if (this.website == "jp") {
                    return str.replace("xxxx", "<em>「" + n + "」</em>").replace("yyyy", "<em>0</em>")
                } else if (this.website == "de") {
                    return str.replace("xxxx", "<em> " + n + " </em>").replace("yyyy", "<em>0</em>")
                } else if (this.website == "ru") {
                    return str.replace("xxxx", "<em>«" + n + "»</em>").replace("yyyy", "<em>0</em>")
                } else {
                    return str.replace("xxxx", '<em>"' + n + '"</em>').replace("yyyy", "<em>0</em>")
                }
            } else if (t == 5) {
                if (this.website == "jp") {
                    return str.replace("xxxx", "<em>「" + n + "」</em>").replace("yyyy", "<em>1</em>")
                } else if (this.website == "de") {
                    return str.replace("xxxx", "<em> " + n + " </em>").replace("yyyy", "<em>1</em>")
                } else if (this.website == "ru") {
                    return str.replace("xxxx", "<em>«" + n + "»</em>").replace("yyyy", "<em>1</em>")
                } else {
                    return str.replace("xxxx", '<em>"' + n + '"</em>').replace("yyyy", "<em>1</em>")
                }
            } else if (t == 6) {
                return str.replace("xxxx", '<em>"' + n + '"</em>').replace("yyyy", '<em>"' + this.pageConfig.total + '"</em>')
            } else if (t == 7) {
                return str.replace("xxxx", "javascript:;").replace("_blank", "_self").replace("yyyy", n.file_name)
            } else if (t == 8) {
                if (this.website == "de") {
                    return str.replace("xxxx", n)
                } else {
                    // if (["cn", "hk", "tw", "mo"].includes(this.website)) {
                    //     str = str.replace("yyy", this.localePath({ path: "/offine-products-eos?pId=911&cId=3079" }))
                    // }
                    return str.replace("xxxx", '"' + n + '"')
                }
            } else if (t == 9) {
                return str.replace("xxxx", n)
            } else if (t == 10) {
                return str.replace("xxxx", this.localePath({ path: "/products_support/search.html", query: { page: 1, keyword: n } }))
            }
        },
        shopPop() {
            this.$refs.shopball.drop(this.startPos, this.stopPos)
        },
        checkTabIndex(i) {
            this.tabIndex = i
            let e = i === 1 ? "Document & Resources" : "Product"
            searchResultBuried("type", e, this.pageState)
            if (i == 0) {
                return
            } else if (this.downloadList && this.downloadList.length) {
                return
            }
            this.downloadLoading = true
            this.$axios
                .get("/api/search/getDocument", {
                    params: {
                        keyword: this.keyword,
                    },
                })
                .then((res) => {
                    this.downloadLoading = false
                    this.downloadList = res.data
                })
                .catch((err) => {
                    this.downloadLoading = false
                })
        },
        changeListType(i) {
            this.displayType = i
            let e = i === 1 ? "View in a grid" : "View in a list"
            searchResultBuried("icon", e, this.pageState)
            this.getScreenListBuried()
        },
        clickDownloadTitle(n) {
            if (n.file_type === "Software") {
                searchResultBuried("fileTxt", `text_${n.file_name}`, this.pageState)
                searchResultBuried("download", `${n.file_url}_${n.file_name}`, this.pageState)
            } else {
                searchResultBuried("fileTxt", `text_${n.file_name}`, this.pageState)
            }
        },
        clickDownloadView(n) {
            if (n.file_type === "Software") {
                searchResultBuried("download", `${n.file_url}_${n.file_name}`, this.pageState)
            } else {
                searchResultBuried("fileView", `view_${n.file_name}`, this.pageState)
            }
            window.open(n.file_url, "_blank")
        },
        // 获取浏览器可视高度曝光列表埋点
        getScreenListBuried() {
            if (!this.goodsList) return
            let w = document.documentElement.clientWidth || document.body.clientWidth
            let h = document.documentElement.clientHeight || document.body.clientHeight
            let total = 0
            let num = 0
            if (this.displayType == 1) {
                if (w > 1200) {
                    total =
                        document.getElementsByClassName("fs-header")[0].offsetHeight +
                        document.getElementsByClassName("search_page_container_head")[0].offsetHeight +
                        (document.getElementsByClassName("filter")[0] ? document.getElementsByClassName("filter")[0].offsetHeight : 0)
                    num = Math.ceil((h - total) / 456) * 4
                } else if (w > 960 && w < 1200) {
                    total =
                        document.getElementsByClassName("fs-header")[0].offsetHeight +
                        document.getElementsByClassName("search_page_container_head")[0].offsetHeight +
                        (document.getElementsByClassName("filter")[0] ? document.getElementsByClassName("filter")[0].offsetHeight : 0)
                    num = Math.ceil((h - total) / 456) * 3
                } else if (w > 768 && w < 960) {
                    total =
                        document.getElementsByClassName("m-fs-header")[0].offsetHeight +
                        document.getElementsByClassName("search_page_container_head")[0].offsetHeight +
                        (document.getElementsByClassName("filter")[0] ? document.getElementsByClassName("filter")[0].offsetHeight : 0)
                    num = Math.ceil((h - total) / 456) * 3
                } else if (w <= 768) {
                    total =
                        document.getElementsByClassName("m-fs-header")[0].offsetHeight +
                        document.getElementsByClassName("search_page_container_head")[0].offsetHeight +
                        (document.getElementsByClassName("filter")[0] ? document.getElementsByClassName("filter")[0].offsetHeight : 0)
                    num = Math.ceil((h - total) / 389) * 2
                }
            } else if (this.displayType == 2) {
                if (w > 1200) {
                    total =
                        document.getElementsByClassName("fs-header")[0].offsetHeight +
                        document.getElementsByClassName("search_page_container_head")[0].offsetHeight +
                        (document.getElementsByClassName("filter")[0] ? document.getElementsByClassName("filter")[0].offsetHeight : 0)
                    num = Math.ceil((h - total) / 255)
                } else if (w > 960 && w < 1200) {
                    total =
                        document.getElementsByClassName("fs-header")[0].offsetHeight +
                        document.getElementsByClassName("search_page_container_head")[0].offsetHeight +
                        (document.getElementsByClassName("filter")[0] ? document.getElementsByClassName("filter")[0].offsetHeight : 0)
                    num = Math.ceil((h - total) / 255)
                } else if (w > 768 && w < 960) {
                    total =
                        document.getElementsByClassName("m-fs-header")[0].offsetHeight +
                        document.getElementsByClassName("search_page_container_head")[0].offsetHeight +
                        (document.getElementsByClassName("filter")[0] ? document.getElementsByClassName("filter")[0].offsetHeight : 0)
                    num = Math.ceil((h - total) / 255)
                } else if (w <= 768) {
                    total =
                        document.getElementsByClassName("m-fs-header")[0].offsetHeight +
                        document.getElementsByClassName("search_page_container_head")[0].offsetHeight +
                        (document.getElementsByClassName("filter")[0] ? document.getElementsByClassName("filter")[0].offsetHeight : 0)
                    num = Math.ceil((h - total) / 242)
                }
            }
            let res = this.goodsList.filter((item, index) => {
                return index < num
            })
            res = grouping(res, this.displayType == 1 ? 4 : 1)
            this.visualHeight = this.displayType == 1 ? num / 4 : num
            res.map((item) => {
                let arr = []
                item.map((it) => {
                    arr.push({
                        list: document.title.substring(0, 100),
                        id: it.products_id,
                        name: it.products_name.substring(0, 100),
                        price: it.price_us,
                        position: it.posIndex,
                    })
                })
                goodsItemBuried("exposure", { list: arr, currency: this.currency, searchContent: this.$route.query.keyword }, "search", this.pageState)
            })
        },
        // 获取浏览器可视高度曝光列表埋点:滚动
        scrollScreenListBuried() {
            if (!this.goodsList) return
            let w = document.documentElement.clientWidth || document.body.clientWidth
            let h = document.documentElement.clientHeight || document.body.clientHeight
            let total = 0
            if (this.displayType == 1) {
                if (w > 1200) {
                    total =
                        document.getElementsByClassName("fs-header")[0].offsetHeight +
                        document.getElementsByClassName("search_page_container_head")[0].offsetHeight +
                        (document.getElementsByClassName("filter")[0] ? document.getElementsByClassName("filter")[0].offsetHeight : 0)
                    if (Math.ceil((h - total + this.scrollNumber) / 456) > this.visualHeight) {
                        this.visualHeight = Math.ceil((h - total + this.scrollNumber) / 456)
                    }
                } else if (w > 960 && w < 1200) {
                    total =
                        document.getElementsByClassName("fs-header")[0].offsetHeight +
                        document.getElementsByClassName("search_page_container_head")[0].offsetHeight +
                        (document.getElementsByClassName("filter")[0] ? document.getElementsByClassName("filter")[0].offsetHeight : 0)
                    if (Math.ceil((h - total + this.scrollNumber) / 456) > this.visualHeight) {
                        this.visualHeight = Math.ceil((h - total + this.scrollNumber) / 456)
                    }
                } else if (w > 768 && w < 960) {
                    total =
                        document.getElementsByClassName("m-fs-header")[0].offsetHeight +
                        document.getElementsByClassName("search_page_container_head")[0].offsetHeight +
                        (document.getElementsByClassName("filter")[0] ? document.getElementsByClassName("filter")[0].offsetHeight : 0)
                    if (Math.ceil((h - total + this.scrollNumber) / 456) > this.visualHeight) {
                        this.visualHeight = Math.ceil((h - total + this.scrollNumber) / 456)
                    }
                } else if (w <= 768) {
                    total =
                        document.getElementsByClassName("m-fs-header")[0].offsetHeight +
                        document.getElementsByClassName("search_page_container_head")[0].offsetHeight +
                        (document.getElementsByClassName("filter")[0] ? document.getElementsByClassName("filter")[0].offsetHeight : 0)
                    if (Math.ceil((h - total + this.scrollNumber) / 389) > this.visualHeight) {
                        this.visualHeight = Math.ceil((h - total + this.scrollNumber) / 389)
                    }
                }
            } else if (this.displayType == 2) {
                if (w > 1200) {
                    total =
                        document.getElementsByClassName("fs-header")[0].offsetHeight +
                        document.getElementsByClassName("search_page_container_head")[0].offsetHeight +
                        (document.getElementsByClassName("filter")[0] ? document.getElementsByClassName("filter")[0].offsetHeight : 0)
                    if (Math.ceil((h - total + this.scrollNumber) / 255) > this.visualHeight) {
                        this.visualHeight = Math.ceil((h - total + this.scrollNumber) / 255)
                    }
                } else if (w > 960 && w < 1200) {
                    total =
                        document.getElementsByClassName("fs-header")[0].offsetHeight +
                        document.getElementsByClassName("search_page_container_head")[0].offsetHeight +
                        (document.getElementsByClassName("filter")[0] ? document.getElementsByClassName("filter")[0].offsetHeight : 0)
                    if (Math.ceil((h - total + this.scrollNumber) / 255) > this.visualHeight) {
                        this.visualHeight = Math.ceil((h - total + this.scrollNumber) / 255)
                    }
                } else if (w > 768 && w < 960) {
                    total =
                        document.getElementsByClassName("m-fs-header")[0].offsetHeight +
                        document.getElementsByClassName("search_page_container_head")[0].offsetHeight +
                        (document.getElementsByClassName("filter")[0] ? document.getElementsByClassName("filter")[0].offsetHeight : 0)
                    if (Math.ceil((h - total + this.scrollNumber) / 255) > this.visualHeight) {
                        this.visualHeight = Math.ceil((h - total + this.scrollNumber) / 255)
                    }
                } else if (w <= 768) {
                    total =
                        document.getElementsByClassName("m-fs-header")[0].offsetHeight +
                        document.getElementsByClassName("search_page_container_head")[0].offsetHeight +
                        (document.getElementsByClassName("filter")[0] ? document.getElementsByClassName("filter")[0].offsetHeight : 0)
                    if (Math.ceil((h - total + this.scrollNumber) / 242) > this.visualHeight) {
                        this.visualHeight = Math.ceil((h - total + this.scrollNumber) / 242)
                    }
                }
            }
        },
        handleScroll() {
            let scrollTop = window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop
            this.scrollNumber = scrollTop
        },
        // 曝光热门和推荐列表
        hotOrRecom(num) {
            if (this.hotProducts.length > 0) {
                let res = grouping(this.hotProducts, 6)
                // res.map((item) => {
                let arr = []
                res[num].map((it) => {
                    arr.push({
                        list: document.title.substring(0, 100),
                        id: it.products_id,
                        name: it.products_name.substring(0, 100),
                        price: it.products_price_number,
                        position: it.posIndex,
                    })
                })
                goodsItemBuried("exposure", { list: arr, currency: this.currency, searchContent: this.$route.query.keyword }, "search", this.pageState)
                // })
            }
        },
        // 点击切换搜索无结果的热门和推荐
        hotOrRecomClick(type, dir) {
            if (type === "recom") {
                searchResultBuried("hOrR", `Related Products_${dir}`, this.pageState)
                if (dir == "right") {
                    if (this.snapIndex < this.$refs.relaProduct.$swiper.snapIndex + 1) {
                        this.snapIndex = this.$refs.relaProduct.$swiper.snapIndex + 1
                    }
                }
            } else {
                searchResultBuried("hOrR", `Hot Products_${dir}`, this.pageState)
                if (dir == "right") {
                    if (this.snapIndex < this.$refs.hotProduct.$swiper.snapIndex + 1) {
                        this.snapIndex = this.$refs.hotProduct.$swiper.snapIndex + 1
                    }
                }
            }
        },
        //pageview事件
        pageViewBuried() {
            let loginState = this.isLogin ? `Login_${this.gaLoginString}` : `Logout_${this.gaLoginString}`
            let userId = this.isLogin && this.userInfo ? AES.encrypt(`${this.userInfo.customers_level}${this.userInfo.customers_id}`, `_-yu_xuan_3507-_`, `fs_com_phone2016`) : ""
            let website = `${this.iso_code}_${this.language}_${this.currency}`
            let fsPvid = this.$cookies.get("_fs_pvid")
            searchResultPV(loginState, userId, website, fsPvid, this.$route, this.pageLocation, this.pageTitle, this.pageState, this.pre_url, this)
        },
        reSizeT() {
            if (document.body.clientWidth <= 768) {
                this.isMobile = true
            } else {
                this.isMobile = false
            }
            this.reS()
        },
        routeLoading() {
            this.loading = true
        },
        recToCart(data) {
            this.showPop = true
            this.$refs.addCartPopup.init(data.products_id)
        },
        // 监听屏幕宽度
        reS() {
            if (document.body.clientWidth > 768 && document.body.clientWidth <= 1024) {
                this.isPad = 1
            } else {
                this.isPad = 2
            }
        },
        // 分页器埋点
        clickBuried(val, b) {
            let str = ""
            if (val === "page_num") {
                str = `Go to_${b}`
            } else {
                str = val
            }
            searchResultBuried("hOrR", str, this.pageState)
        },
        // 判断是否缩小间距
        getNewArr(list, num) {
            for (let i = 0; i < list.length; i += num) {
                if (
                    list.slice(i, i + num).filter((item) => {
                        return Object.keys(item.attributes).length
                    }).length
                ) {
                    list.slice(i, i + num).forEach((t) => {
                        t.hasAttr = true
                    })
                } else {
                    list.slice(i, i + num).forEach((t) => {
                        t.hasAttr = false
                    })
                }
            }
            this.goodsList = JSON.parse(JSON.stringify(list))
        },
        // closeSendFeedback
        openSendFeedback() {
            this.showSendFeedback = true
        },
        closeSendFeedback() {
            this.showSendFeedback = false
        },
        closeScoring() {
            this.showScoring = false
        },
        toCategory(item) {
            window.open(item.url, "_blank")
            if (window.dataLayer) {
                window.dataLayer.push({
                    event: "uaEvent",
                    eventCategory: "WebNavigationSearch_NoResult Page",
                    eventAction: "click_Product Categories",
                    eventLabel: item.name,
                    nonInteraction: false,
                })
            }
        },
        moreClick(i) {
            this.category_show_more = !this.category_show_more
        },
    },
    created() {
        this.cateIdNum = this.$route.query.categories_id
        this.pageConfigCh = JSON.parse(JSON.stringify(this.pageConfig))
        if (this.$route.query.settab) {
            if (this.$route.query.settab == "two") {
                this.displayType = 1
            } else if (this.$route.query.settab == "one") {
                this.displayType = 2
            }
        }
    },
    mounted() {
        window.addEventListener("scroll", this.handleScroll)
        this.reSizeT()
        window.addEventListener("resize", this.reSizeT)
        this.reS()
        this.pageViewBuried()
        if (this.hotProducts.length > 0) {
            this.hotFlag = true
            this.hotOrRecom(this.snapIndex)
        } else {
            this.hotFlag = false
        }
        if (this.goodsList && this.goodsList.length) {
            if (this.screenWidth > 1240) {
                this.getNewArr(this.goodsList, 4)
            } else if (this.screenWidth >= 768) {
                this.getNewArr(this.goodsList, 3)
            } else {
                this.getNewArr(this.goodsList, 2)
            }
        }
        // 功能暂时下架
        // if (!this.submitSatisfaction) {
        //     if (JSON.parse(localStorage.getItem("history"))) {
        //         let local = JSON.parse(localStorage.getItem("history"))
        //         if (local.length > 2) {
        //             setTimeout(() => {
        //                 this.showScoring = true
        //                 setTimeout(() => {
        //                     this.showScoring = false
        //                 }, 30000)
        //             }, 10000)
        //         }
        //     }
        // }
    },
}
</script>
<style lang="scss" scoped>
.search_page {
    display: flex;
    align-items: center;
    flex-direction: column;
    justify-content: center;

    // background: $bgColor1;
    .search_page_container {
        width: 100%;

        .search_page_container_head {
            display: flex;
            justify-content: center;
            background: #fff;
            position: relative;

            > div {
                width: 84vw;
                // width: 1200px;
                max-width: 1200px;
                // &::v-deep .bread-crumb {
                //     display: flex;
                //     align-items: center;
                //     padding-top: 16px;
                //     padding-bottom: 20px;
                //     @media (max-width: 960px) {
                //         display: none;
                //     }
                // }
                // &::v-deep .bread-item {
                //     display: flex;
                //     align-items: center;
                //     color: $textColor3;
                //     font-size: 13px;
                //     line-height: 20px;
                //     cursor: pointer;
                //     text-decoration: none;
                //     transition: all 0.3s;

                //     &:after {
                //         font-weight: 400;
                //         text-align: center;
                //         content: "/";
                //         padding: 0 7px;
                //         color: #d3d3d3;
                //         font-size: 13px;
                //         // line-height: 20px;
                //     }

                //     &:last-child {
                //         color: $textColor1;
                //         cursor: text;
                //         > a {
                //             color: $textColor1;
                //             cursor: text;
                //         }
                //         &:after {
                //             display: none;
                //         }
                //     }
                //     > a {
                //         color: $textColor3;
                //         text-decoration: none;
                //         &:hover {
                //             color: $textColor1;
                //             text-decoration: none;
                //             cursor: pointer;
                //         }
                //     }
                // }
                > section {
                    display: flex;
                    justify-content: space-between;

                    h4 {
                        color: #19191a;
                        @include font24;
                        font-weight: 600;
                        ::v-deep em {
                            color: $textColor1;
                            font-style: normal;
                            word-break: break-word;
                        }

                        .correct {
                            font-size: 16px;
                            line-height: 24px;
                            color: #707070;
                            margin-top: 12px;
                            font-weight: 600;

                            ::v-deep {
                                em {
                                    cursor: pointer;
                                    font-style: normal;
                                    color: #0060bf;

                                    &:hover {
                                        text-decoration: underline;
                                    }
                                }

                                .und {
                                    cursor: auto;
                                    color: #19191a;

                                    &:hover {
                                        text-decoration: none;
                                    }
                                }

                                span {
                                    color: #19191a;
                                }
                            }
                        }
                    }

                    > div {
                        display: flex;

                        .sort {
                            min-width: 160px;

                            ::v-deep .fs-select .list-wrap-absolute {
                                z-index: 4;
                            }
                        }
                    }
                }

                .pc_nav {
                    display: flex;
                    margin-bottom: 20px;

                    //    @media (max-width: 960px) {
                    //         margin-top: 20px;
                    //     }
                    .display_type {
                        display: flex;
                        align-items: center;

                        i {
                            color: $textColor3;
                            font-size: 20px;
                            margin-left: 14px;
                            cursor: pointer;

                            &:hover {
                                color: $textColor1;
                            }

                            &.active {
                                color: $textColor1;
                            }
                        }
                    }
                }

                .m_nav {
                    display: none;
                }

                .tab {
                    display: flex;
                    padding: 4px;
                    gap: 4px;
                    border-radius: 999px;
                    background-color: #f6f6f6;
                    max-width: max-content;
                    span {
                        color: $textColor3;
                        @include font12;
                        padding: 4px 8px;
                        border-radius: 999px;
                        cursor: pointer;
                        &:hover {
                            color: $textColor1;
                        }
                    }
                    .active {
                        color: $textColor1;
                        background-color: #ffffff;
                    }

                    .f_d_t {
                        @media (max-width: 960px) {
                            .head_select,
                            .viewSize {
                                display: none;
                            }
                        }

                        display: flex;
                        position: absolute;
                        right: 0;
                        bottom: 12px;

                        ::v-deep .head_select {
                            .fs-select {
                                margin: 0;

                                .options-box-bg {
                                    background: transparent;
                                }
                            }
                        }

                        .viewSize {
                            margin-left: 16px;

                            &:hover {
                                ::v-deep .fs-select-active {
                                    border-color: #6f6f6f;
                                }
                            }

                            ::v-deep .options-box-bg {
                                padding-top: 0;

                                .options-wrap {
                                    border-top: none;

                                    &.options-wrap-absolute {
                                        top: 0;
                                    }

                                    .options-box {
                                        .item {
                                            justify-content: center;
                                        }
                                    }
                                }
                            }

                            ::v-deep .fs-select {
                                background: transparent;
                            }
                        }

                        .display_type {
                            display: flex;
                            align-items: center;

                            i {
                                color: $textColor3;
                                font-size: 20px;
                                margin-left: 16px;
                                cursor: pointer;
                                font-weight: 400;

                                &:hover {
                                    color: $textColor1;
                                }

                                &.active {
                                    color: $textColor1;
                                }
                            }

                            @media (max-width: 768px) {
                                display: none;
                            }
                        }
                    }
                }
            }
        }

        .search_result {
            width: 84vw;
            // width: 1200px;
            max-width: 1200px;
            margin: 0px auto auto;
            // display: flex;
            position: relative;
            .result_warning {
                border-radius: 2px;
                font-size: 14px;
                color: #707070;
                position: relative;
                line-height: 22px;
                padding: 10px 16px 10px 40px;
                box-sizing: border-box;
                background: rgba(0, 96, 191, 0.04);

                i {
                    font-size: 16px;
                    display: inline-block;
                    top: 13px;
                    width: 16px;
                    height: 16px;
                    line-height: 16px;
                    position: absolute;
                    left: 16px;
                    color: #0060bf;
                }

                a {
                    color: #0070bc;
                    text-decoration: none;
                    cursor: pointer;

                    &:focus-visible {
                        @include focusVisible;
                    }

                    &:hover {
                        text-decoration: underline;
                    }
                }
            }
            .filter {
                width: 100%;
                display: flex;
                justify-content: center;

                > div {
                    width: 100%;
                    padding: 20px 0;
                    display: flex;
                    justify-content: space-between;

                    > span {
                        white-space: nowrap;
                        padding-top: 12px;
                        font-size: 13px;
                    }

                    > div {
                        width: 100%;
                    }
                }

                .m_icon {
                    display: none;
                    padding: 0;
                    background: #fff;
                    margin-top: 16px;
                    ::v-deep {
                        .mobile_filter {
                            .filter_list {
                                .filter_content {
                                    .top {
                                        .list {
                                            max-height: 53.8vh;
                                        }
                                    }

                                    .category,
                                    .narrow {
                                        max-height: 53.8vh;
                                    }
                                }
                            }
                        }
                    }
                }
            }

            @media (max-width: 1024px) {
            }

            @media (max-width: 768px) {
                margin-top: 0;
                .result_warning {
                    margin: 24px 16px 20px;
                }
                .filter {
                    > div {
                        display: none;

                        > span {
                            display: none;
                        }
                    }

                    .m_icon {
                        display: block;
                        // background: #fff;
                        // padding: 20px 16px 14px;
                        // margin-top: 0;
                        // display: flex;
                        // line-height: 22px;
                        // align-items: center;
                        // justify-content: space-between;
                        // > div {
                        //     i {
                        //         color: $textColor1;
                        //         font-size: 20px;
                        //         cursor: pointer;
                        //     }
                        //     span {
                        //         color: $textColor3;
                        //         font-size: 14px;
                        //         cursor: pointer;
                        //         margin-left: 6px;
                        //     }
                        // }
                        // .filter_display_type {
                        //     display: none;
                        // }
                    }
                }
            }

            > .lists {
                flex: 1;
                display: flex;
                flex-direction: column;

                .cate_narr_list {
                    @media (max-width: 960px) {
                        display: none;
                    }

                    display: flex;
                    margin-bottom: 20px;

                    .cate_narr_item {
                        cursor: pointer;
                        padding: 10px 16px;
                        margin-right: 12px;
                        display: flex;
                        align-items: center;
                        background: #fff;
                        border: 1px dotted #e5e5e5;

                        i {
                            color: #19191a;
                            font-size: 12px;
                            margin-right: 16px;
                        }

                        span {
                            font-size: 14px;
                            line-height: 22px;
                            color: #19191a;
                        }
                    }

                    .clear {
                        font-size: 13px;
                        line-height: 44px;
                        color: #0060bf;
                        cursor: pointer;

                        &:hover {
                            text-decoration: underline;
                        }
                    }
                }

                .grid {
                    display: flex;
                    flex-wrap: wrap;
                    position: relative;

                    &:before {
                        content: " ";
                        background-color: #eee;
                        position: absolute;
                        height: 1px;
                        width: 100%;
                        left: 0;
                        top: 0;
                        z-index: 1;

                        @media (max-width: 768px) {
                            display: none;
                        }
                    }

                    &:after {
                        content: " ";
                        background-color: #fff;
                        position: absolute;
                        height: 1px;
                        width: 100%;
                        left: 0;
                        bottom: 0;
                        z-index: 1;
                    }

                    li {
                        border-bottom: 1px solid #eee;
                        // min-height: 470px;
                        position: relative;
                        // background: #fff;
                        position: relative;
                        // width: calc((100% - 36px) / 4);
                        width: 25%;
                        padding: 20px 10px 24px;
                        // margin-bottom: 12px;
                        box-sizing: border-box;
                        animation: all 0.3s;
                        transition: all 0.3s ease-out;
                        position: relative;

                        // &:nth-child(n) {
                        //     margin-right: 12px;
                        // }
                        // &:nth-child(4n) {
                        //     margin-right: 0;
                        // }
                        &:hover {
                            border-color: transparent;
                            box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.1);

                            @media (max-width: 1024px) {
                                box-shadow: none;
                            }

                            ::v-deep .grid_item {
                                .img {
                                    .add_to_cart {
                                        display: flex;
                                        justify-content: center;
                                        align-items: center;

                                        @media (max-width: 1024px) {
                                            display: none;
                                            @media (max-width: 960px) {
                                                display: none;
                                            }
                                        }
                                    }
                                }
                            }
                        }

                        @media (max-width: 1024px) {
                            padding: 20px 20px 20px;
                        }

                        @media (max-width: 768px) {
                            padding: 12px 12px 16px;
                            border-top: none;

                            &:nth-child(n + 3) {
                                border-bottom: 1px solid #eee;
                            }
                        }
                    }

                    .custom_li {
                        padding: 20px 20px 20px 20px;
                    }

                    .grid_li_not_en {
                        &:hover {
                            ::v-deep {
                                .grid_item {
                                    .sold_reviews {
                                        s {
                                            // display: none;
                                            @media (max-width: 1024px) {
                                                display: inline-block;
                                            }
                                        }

                                        div:last-child {
                                            // display: none;
                                            @media (max-width: 1024px) {
                                                display: inline-block;
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }

                .list {
                    li {
                        position: relative;
                        // margin-bottom: 10px;
                        animation: all 0.3s;
                        transition: all 0.3s ease-out;

                        &:hover {
                            box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.1);
                        }
                    }
                }
            }

            .download {
                flex: 1;
                &::v-deep {
                    .loading-wrap {
                        z-index: inherit;
                    }
                }
                .tag {
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    padding: 20px 0;
                    margin-bottom: 0px;
                    background: transparent;

                    > div:nth-child(1) {
                        background: #fff;
                        cursor: pointer;
                        margin-right: 0px;
                        box-sizing: border-box;
                        font-size: 13px;
                        color: #19191a;
                        border-radius: 3px;
                        height: 38px;
                        padding: 0 20px;
                        border: 2px solid #707070;
                        line-height: 34px;
                        transition: all 0.3s;

                        &:hover {
                            border-color: #707070;
                        }
                    }

                    > div:nth-child(2) {
                        font-size: 13px;
                        color: #19191a;
                    }

                    div {
                        display: none;
                    }
                }

                > div {
                    color: #232323;
                    padding: 20px 0 36px;
                    background: #fff;

                    h6 {
                        display: flex;
                        align-items: center;
                        justify-content: space-between;

                        span:nth-child(1) {
                            font-size: 16px;
                            font-weight: 600;
                        }

                        span:nth-child(2) {
                            font-size: 13px;
                            color: #19191a;
                            font-weight: 500;
                        }

                        padding-bottom: 26px;
                        border-bottom: 1px solid #e5e5e5;
                    }

                    ul {
                        min-height: 200px;

                        li {
                            position: relative;
                            padding: 24px;
                            display: flex;
                            align-items: center;
                            border-radius: 4px;
                            background-color: #fafafb;
                            cursor: pointer;
                            margin-top: 20px;
                            transition: all 0.3s;
                            &:first-child {
                                margin-top: 0;
                            }
                            > img {
                                width: 32px;
                                height: 32px;
                                margin-right: 16px;
                                // position: absolute;
                                // left: 0;
                                // margin-top: -19px;
                                // top: 50%;
                            }

                            &:hover {
                                background-color: #fff;
                                box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.1);
                            }

                            @media (max-width: 768px) {
                                padding: 16px;
                                align-items: normal;

                                > img {
                                    margin-top: 4px;
                                }
                            }

                            > div {
                                flex: 1;
                                box-sizing: border-box;
                                // padding: 0 170px 0 54px;
                                position: relative;

                                > div {
                                    > h3 {
                                        font-size: 14px;
                                        color: #707070;
                                        line-height: 22px;
                                        margin-bottom: 6px;

                                        ::v-deep > a {
                                            color: #19191a;
                                            font-weight: 600;

                                            // margin-left: 4px;
                                            &:hover {
                                                text-decoration: underline;
                                            }
                                        }
                                    }

                                    > div {
                                        color: #707070;
                                        display: flex;
                                        align-items: center;

                                        > div {
                                            span {
                                                font-size: 13px;
                                                line-height: 18px;
                                            }

                                            i {
                                                font-size: 14px;
                                                color: #707070;
                                                margin-right: 6px;
                                            }
                                        }

                                        > div:nth-child(1) {
                                            margin-right: 28px;

                                            @media (max-width: 768px) {
                                                margin-right: 16px;
                                            }
                                        }
                                    }
                                }

                                > a {
                                    position: absolute;
                                    right: 0;
                                    top: 50%;
                                    transform: translateY(-50%);
                                    font-size: 14px;
                                    color: #0060bf;
                                    line-height: 22px;

                                    &:hover {
                                        text-decoration: underline;
                                    }
                                }

                                .down {
                                    display: none;
                                }
                            }

                            // &:nth-child(2n + 1) {
                            //     background-color: #fafafb;
                            // }
                        }
                    }
                }
            }

            .result_404 {
                width: 100%;

                .result_404_container {
                    background: #fafafb;
                    padding: 24px;
                    display: flex;
                    flex-wrap: wrap;
                    justify-content: space-between;
                    border-radius: 8px;
                    .result_404_tit {
                        @include font16;
                        color: #19191a;
                        font-weight: 600;
                        margin-bottom: 12px;
                    }

                    .result_404_txt {
                        display: flex;
                        margin-bottom: 8px;
                        @include font14;

                        > span {
                            flex: 1;
                            @include font14;
                            color: $textColor2;

                            > b {
                                cursor: pointer;
                                color: $textColor6;
                                font-weight: initial;

                                &:focus-visible {
                                    @include focusVisible;
                                }

                                &:hover {
                                    text-decoration: underline;
                                }
                            }
                        }
                    }

                    .result_404_txt:nth-last-child(1) {
                        margin-bottom: 0;
                    }

                    .result_404_left {
                        .result_404_txt {
                            i {
                                background: $textColor2;
                                width: 4px;
                                height: 4px;
                                display: inline-block;
                                border-radius: 50%;
                                margin-right: 8px;
                                margin-top: 9px;
                                vertical-align: middle;
                            }
                        }
                    }
                }

                .recommend {
                    margin-top: 16px;
                    // border-top: 1px solid #e5e5e5;
                    // border-bottom: 1px solid #e5e5e5;
                    ::v-deep {
                        .recently_viewed {
                            .recently_viewed_box {
                                .title {
                                    border: none;
                                }
                            }
                        }
                    }
                    @media (max-width: 768px) {
                        margin-top: 18px;
                        border: none;
                    }
                }

                .result_404_help {
                    background: #fff;
                    padding-top: 36px;
                    margin-top: 36px;
                    margin-bottom: 36px;
                    border-top: 1px solid #e5e5e5;
                    .result_404_help_tit {
                        box-sizing: border-box;
                        height: initial;
                        overflow: hidden;
                        span {
                            display: block;
                            @include font16;
                            color: $textColor1;
                            font-weight: 600;
                            margin-bottom: 20px;
                        }
                    }

                    .result_404_help_list {
                        padding: 0 0 36px;
                        display: flex;
                        justify-content: space-between;
                        border-bottom: 1px solid $borderColor2;
                        .result_404_help_item {
                            display: flex;
                            flex-direction: column;
                            padding: 20px;
                            width: calc((100% - 60px) / 4);
                            border-radius: 8px;
                            border: 1px solid $borderColor2;
                            transition: all 0.3s;
                            > span {
                                display: flex;
                                align-items: center;
                                margin-bottom: 4px;

                                img {
                                    margin-right: 12px;
                                }

                                span {
                                    color: #19191a;
                                    font-size: 14px;
                                    line-height: 22px;
                                }
                            }

                            > a {
                                text-indent: 36px;
                                font-size: 14px;
                                // color: #707070;
                                text-decoration: none;
                                line-height: 22px;
                                cursor: initial;
                                &.cka {
                                    width: max-content;
                                    &:hover {
                                        text-decoration: underline;
                                    }
                                    cursor: pointer;
                                }
                            }

                            .ck {
                                cursor: pointer;
                                color: #0060bf;
                                width: max-content;
                                &:hover {
                                    text-decoration: underline;
                                }
                            }
                            &:hover {
                                box-shadow: 0px 6px 8px 0px rgba(0, 0, 0, 0.1);
                            }
                            &:last-child {
                                > a {
                                    text-indent: 32px;
                                }
                            }
                        }
                    }

                    @media (max-width: 768px) {
                        margin: 36px 16px;
                        .result_404_help_tit {
                            margin: 0;
                            padding: 0;
                            // border-bottom: 1px solid #eee;

                            b {
                                font-size: 16px;
                                line-height: 24px;
                                padding-bottom: 0;
                                border: 0;
                            }
                        }

                        .result_404_help_list {
                            display: flex;
                            flex-direction: column;
                            gap: 20px;
                            padding: 0 0 36px;

                            .result_404_help_item {
                                width: 100%;
                                padding: 20px;
                                border-bottom: 1px solid $borderColor2;
                            }
                        }
                    }
                }

                .category_404 {
                    .category_404_title {
                        @include font16;
                        color: $textColor1;
                        font-weight: 600;
                        margin-bottom: 20px;
                    }
                    .category_404_box {
                        padding-bottom: 36px;
                        border-bottom: 1px solid $borderColor2;
                        display: grid;
                        grid-template-columns: repeat(4, 1fr);
                        gap: 20px;
                        transition: all 0.3s;
                        max-height: 562px;
                        &.maxHeight {
                            max-height: 2000px;
                        }
                        .category_404_box_item {
                            padding: 20px;
                            border-radius: 8px;
                            border: 1px solid $borderColor2;
                            transition: all 0.3s;
                            display: flex;
                            align-items: center;
                            cursor: pointer;
                            &:hover {
                                box-shadow: 0px 6px 8px 0px rgba(0, 0, 0, 0.1);
                            }
                            img {
                                // width: 64px;
                                // height: 64px;
                                margin-right: 8px;
                            }
                            div {
                                span {
                                    @include font14;
                                    color: $textColor1;
                                }
                            }
                        }
                    }
                    @media (max-width: 768px) {
                        margin: 0 16px;
                        .category_404_box {
                            grid-template-columns: repeat(1, 1fr);
                            .see_more_btn {
                                cursor: pointer;
                                color: $textColor6;
                                @include font13;
                                display: inline-flex;
                                align-items: center;
                                width: max-content;
                                margin: 0 auto;

                                .iconfont_down {
                                    font-size: 12px;
                                    margin-left: 4px;
                                    transition: all 0.3s;
                                    display: inline-block;

                                    &.iconfont_down_up {
                                        transform: rotateX(-180deg);
                                    }
                                }
                            }
                        }
                    }
                }
            }

            .result_undercarriage {
                width: 100%;
                > div {
                    &:last-child {
                        border: none;
                        > div {
                            &:last-child {
                                border: none;
                            }
                        }
                    }
                }
                .result_warning {
                    border-radius: 2px;
                    font-size: 14px;
                    color: #707070;
                    position: relative;
                    line-height: 22px;
                    padding: 10px 16px 10px 40px;
                    box-sizing: border-box;
                    background: rgba(0, 96, 191, 0.04);

                    i {
                        font-size: 16px;
                        display: inline-block;
                        top: 13px;
                        width: 16px;
                        height: 16px;
                        line-height: 16px;
                        position: absolute;
                        left: 16px;
                        color: #0060bf;
                    }

                    a {
                        color: #0070bc;
                        text-decoration: none;
                        cursor: pointer;

                        &:focus-visible {
                            @include focusVisible;
                        }

                        &:hover {
                            text-decoration: underline;
                        }
                    }
                }
                .result_404_help {
                    background: #fff;
                    padding-top: 36px;
                    margin-top: 36px;
                    margin-bottom: 36px;
                    border-top: 1px solid #e5e5e5;
                    .result_404_help_tit {
                        box-sizing: border-box;
                        height: initial;
                        overflow: hidden;
                        span {
                            display: block;
                            @include font16;
                            color: $textColor1;
                            font-weight: 600;
                            margin-bottom: 20px;
                        }
                    }

                    .result_404_help_list {
                        padding: 0 0 36px;
                        display: flex;
                        justify-content: space-between;
                        border-bottom: 1px solid $borderColor2;
                        .result_404_help_item {
                            display: flex;
                            flex-direction: column;
                            padding: 20px;
                            width: calc((100% - 60px) / 4);
                            border-radius: 8px;
                            border: 1px solid $borderColor2;
                            transition: all 0.3s;
                            > span {
                                display: flex;
                                align-items: center;
                                margin-bottom: 4px;

                                img {
                                    margin-right: 12px;
                                }

                                span {
                                    color: #19191a;
                                    font-size: 14px;
                                    line-height: 22px;
                                }
                            }

                            > a {
                                text-indent: 36px;
                                font-size: 14px;
                                // color: #707070;
                                text-decoration: none;
                                line-height: 22px;
                                cursor: initial;
                                &.cka {
                                    width: max-content;
                                    &:hover {
                                        text-decoration: underline;
                                    }
                                    cursor: pointer;
                                }
                            }

                            .ck {
                                cursor: pointer;
                                color: #0060bf;
                                width: max-content;
                                &:hover {
                                    text-decoration: underline;
                                }
                            }
                            &:hover {
                                box-shadow: 0px 6px 8px 0px rgba(0, 0, 0, 0.1);
                            }
                            &:last-child {
                                > a {
                                    text-indent: 32px;
                                }
                            }
                        }
                    }

                    @media (max-width: 768px) {
                        margin: 36px 16px;
                        .result_404_help_tit {
                            margin: 0;
                            padding: 0;
                            // border-bottom: 1px solid #eee;

                            b {
                                font-size: 16px;
                                line-height: 24px;
                                padding-bottom: 0;
                                border: 0;
                            }
                        }

                        .result_404_help_list {
                            display: flex;
                            flex-direction: column;
                            gap: 20px;
                            padding: 0 0 36px;

                            .result_404_help_item {
                                width: 100%;
                                padding: 20px;
                                border-bottom: 1px solid $borderColor2;
                            }
                        }
                    }
                }

                .category_404 {
                    .category_404_title {
                        @include font16;
                        color: $textColor1;
                        font-weight: 600;
                        margin-bottom: 20px;
                    }
                    .category_404_box {
                        padding-bottom: 36px;
                        border-bottom: 1px solid $borderColor2;
                        display: grid;
                        grid-template-columns: repeat(4, 1fr);
                        gap: 20px;
                        transition: all 0.3s;
                        max-height: 562px;
                        &.maxHeight {
                            max-height: 2000px;
                        }
                        .category_404_box_item {
                            padding: 20px;
                            border-radius: 8px;
                            border: 1px solid $borderColor2;
                            transition: all 0.3s;
                            display: flex;
                            align-items: center;
                            cursor: pointer;
                            &:hover {
                                box-shadow: 0px 6px 8px 0px rgba(0, 0, 0, 0.1);
                            }
                            img {
                                // width: 64px;
                                // height: 64px;
                                margin-right: 8px;
                            }
                            div {
                                span {
                                    @include font14;
                                    color: $textColor1;
                                }
                            }
                        }
                    }
                    @media (max-width: 768px) {
                        margin: 0 16px;
                        .category_404_box {
                            grid-template-columns: repeat(1, 1fr);
                            .see_more_btn {
                                cursor: pointer;
                                color: $textColor6;
                                @include font13;
                                display: inline-block;
                                width: max-content;
                                margin: 0 auto;

                                .iconfont_down {
                                    font-size: 12px;
                                    margin-left: 5px;
                                    margin-top: 1px;
                                    transition: all 0.3s;
                                    display: inline-block;

                                    &.iconfont_down_up {
                                        transform: rotateX(-180deg);
                                    }
                                }
                            }
                        }
                    }
                }

                .recommend {
                    margin-top: 16px;
                    ::v-deep {
                        .recently_viewed {
                            .recently_viewed_box {
                                .title {
                                    border: none;
                                }
                            }
                        }
                    }
                    @media (max-width: 768px) {
                        margin-top: 18px;
                        border: none;
                    }
                }
            }

            .result_only {
                width: 100%;

                .result_warning {
                    margin-top: 20px;
                    border-radius: 2px;
                    font-size: 14px;
                    color: #707070;
                    position: relative;
                    line-height: 22px;
                    padding: 10px 16px 10px 40px;
                    box-sizing: border-box;
                    margin-bottom: 20px;
                    background: rgba(0, 96, 191, 0.04);

                    i {
                        font-size: 16px;
                        display: inline-block;
                        top: 13px;
                        width: 16px;
                        height: 16px;
                        line-height: 16px;
                        position: absolute;
                        left: 16px;
                        color: #0060bf;
                    }

                    a {
                        color: #0070bc;
                        text-decoration: none;
                    }
                }

                .result_only_flag {
                    margin: 0;
                    font-size: 14px;
                    color: #19191a;
                    line-height: 22px;
                    padding-bottom: 8px;
                }

                .lists {
                    .list {
                        > li {
                            .list_item {
                                margin-bottom: 0;
                            }
                        }
                    }

                    .result_404_help {
                        background: #fff;
                        padding-top: 36px;
                        margin-top: 36px;
                        margin-bottom: 36px;
                        border-top: 1px solid #e5e5e5;
                        .result_404_help_tit {
                            box-sizing: border-box;
                            height: initial;
                            overflow: hidden;
                            span {
                                display: block;
                                @include font16;
                                color: $textColor1;
                                font-weight: 600;
                                margin-bottom: 20px;
                            }
                        }

                        .result_404_help_list {
                            padding: 0 0 36px;
                            display: flex;
                            justify-content: space-between;
                            border-bottom: 1px solid $borderColor2;
                            .result_404_help_item {
                                display: flex;
                                flex-direction: column;
                                padding: 20px;
                                width: calc((100% - 60px) / 4);
                                border-radius: 8px;
                                border: 1px solid $borderColor2;
                                transition: all 0.3s;
                                > span {
                                    display: flex;
                                    align-items: center;
                                    margin-bottom: 4px;

                                    img {
                                        margin-right: 12px;
                                    }

                                    span {
                                        color: #19191a;
                                        font-size: 14px;
                                        line-height: 22px;
                                    }
                                }

                                > a {
                                    text-indent: 36px;
                                    font-size: 14px;
                                    // color: #707070;
                                    text-decoration: none;
                                    line-height: 22px;
                                    cursor: initial;
                                    &.cka {
                                        width: max-content;
                                        &:hover {
                                            text-decoration: underline;
                                        }
                                        cursor: pointer;
                                    }
                                }

                                .ck {
                                    cursor: pointer;
                                    color: #0060bf;
                                    width: max-content;
                                    &:hover {
                                        text-decoration: underline;
                                    }
                                }
                                &:hover {
                                    box-shadow: 0px 6px 8px 0px rgba(0, 0, 0, 0.1);
                                }
                                &:last-child {
                                    > a {
                                        text-indent: 32px;
                                    }
                                }
                            }
                        }

                        @media (max-width: 768px) {
                            margin: 20px 16px 36px;
                            .result_404_help_tit {
                                margin: 0;
                                padding: 0;
                                // border-bottom: 1px solid #eee;

                                b {
                                    font-size: 16px;
                                    line-height: 24px;
                                    padding-bottom: 0;
                                    border: 0;
                                }
                            }

                            .result_404_help_list {
                                display: flex;
                                flex-direction: column;
                                gap: 20px;
                                padding: 0 0 36px;

                                .result_404_help_item {
                                    width: 100%;
                                    padding: 20px;
                                    border-bottom: 1px solid $borderColor2;
                                }
                            }
                        }
                    }

                    .category_404 {
                        .category_404_title {
                            @include font16;
                            color: $textColor1;
                            font-weight: 600;
                            margin-bottom: 20px;
                        }
                        .category_404_box {
                            padding-bottom: 40px;
                            display: grid;
                            grid-template-columns: repeat(4, 1fr);
                            gap: 20px;
                            transition: all 0.3s;
                            max-height: 562px;
                            &.maxHeight {
                                max-height: 2000px;
                            }
                            .category_404_box_item {
                                padding: 20px;
                                border-radius: 8px;
                                border: 1px solid $borderColor2;
                                transition: all 0.3s;
                                display: flex;
                                align-items: center;
                                cursor: pointer;
                                &:hover {
                                    box-shadow: 0px 6px 8px 0px rgba(0, 0, 0, 0.1);
                                }
                                img {
                                    // width: 64px;
                                    // height: 64px;
                                    margin-right: 8px;
                                }
                                div {
                                    span {
                                        @include font14;
                                        color: $textColor1;
                                    }
                                }
                            }
                        }
                        @media (max-width: 768px) {
                            margin: 0 16px;
                            .category_404_box {
                                grid-template-columns: repeat(1, 1fr);
                                .see_more_btn {
                                    cursor: pointer;
                                    color: $textColor6;
                                    @include font13;
                                    display: inline-block;
                                    width: max-content;
                                    margin: 0 auto;

                                    .iconfont_down {
                                        font-size: 12px;
                                        margin-left: 5px;
                                        margin-top: 1px;
                                        transition: all 0.3s;
                                        display: inline-block;

                                        &.iconfont_down_up {
                                            transform: rotateX(-180deg);
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    @media (max-width: 1024px) {
        .search_page_container {
            .search_page_container_head {
                > div {
                    width: calc(100% - 48px);
                }
            }

            .search_result {
                width: calc(100% - 48px);
            }
        }

        .pagination {
            width: calc(100% - 48px) !important;
        }
    }

    @media (max-width: 1240px) {
        .search_page_container {
            .search_result {
                > .lists {
                    .grid {
                        li {
                            // width: calc((100% - 36px) / 4);
                            // &:nth-child(n) {
                            //     margin-right: 12px;
                            // }
                            // &:nth-child(4n) {
                            //     margin-right: 0px;
                            // }
                        }
                    }
                }
            }
        }
    }

    @media (max-width: 1240px) {
        .search_page_container {
            .search_result {
                > .lists {
                    .grid {
                        li {
                            width: calc(100% / 3);
                            // &:nth-child(n) {
                            //     margin-right: 12px;
                            // }
                            // &:nth-child(3n) {
                            //     margin-right: 0px;
                            // }
                        }
                    }
                }
            }
        }
    }

    @media (max-width: 768px) {
        .search_page_container {
            .search_page_container_head {
                // border-bottom: 1px solid #e5e5e5;
                > div {
                    &::v-deep .bread-crumb {
                        //     display: flex;
                        //     align-items: center;
                        //     padding-top: 16px;
                        //     padding-bottom: 20px;
                        //     @media (max-width: 960px) {
                        display: none;
                        //     }
                    }
                    // &::v-deep .bread-item {
                    //     display: flex;
                    //     align-items: center;
                    //     color: $textColor3;
                    //     font-size: 13px;
                    //     line-height: 20px;
                    //     cursor: pointer;
                    //     text-decoration: none;
                    //     transition: all 0.3s;

                    //     &:after {
                    //         font-weight: 400;
                    //         text-align: center;
                    //         content: "/";
                    //         padding: 0 7px;
                    //         color: #d3d3d3;
                    //         font-size: 13px;
                    //         line-height: 20px;
                    //     }

                    //     &:last-child {
                    //         color: $textColor1;
                    //         cursor: text;
                    //         > a {
                    //             color: $textColor1;
                    //             cursor: text;
                    //         }
                    //         &:after {
                    //             display: none;
                    //         }
                    //     }
                    //     > a {
                    //         color: $textColor3;
                    //         text-decoration: none;
                    //         &:hover {
                    //             color: $textColor1;
                    //             text-decoration: none;
                    //             cursor: pointer;
                    //         }
                    //     }
                    // }
                    section {
                        display: block;

                        h4 {
                            @include font20;
                            font-weight: 600;
                            color: #19191a;
                            // margin: 30px 0 15px 0;
                            margin: 0 0 16px 0;

                            @media (max-width: 768px) {
                                margin-bottom: 20px;
                            }
                        }

                        > div {
                            height: 28px;
                            margin-bottom: 16px;
                            line-height: 28px;
                            display: none;

                            .item_filter {
                                display: block;

                                span {
                                    font-size: 14px;
                                }

                                i {
                                    font-size: 16px;
                                    margin-right: 5px;
                                }
                            }

                            .display_type {
                                margin: 0;
                                padding: 0;
                                display: none;
                            }

                            .display_type_mobile {
                                display: block;
                            }

                            justify-content: space-between;
                        }
                    }

                    .pc_nav {
                        display: none;
                    }

                    .m_nav {
                        display: flex;
                        padding-top: 24px;

                        h4 {
                            @include font20;
                            font-weight: 600;
                        }
                    }

                    .tab {
                        .active {
                            color: $textColor1;
                        }
                    }
                }
            }

            .search_page_container_head {
                > div {
                    width: 100% !important;
                    margin: 0;
                    padding: 0 16px;

                    &.b_none {
                        border-bottom: none;
                    }
                }
            }

            .search_result {
                width: auto;
                padding: 0;

                // background: #FFF;
                > .filter {
                    .m_icon {
                        display: block;
                        // background: #fff;
                        // padding: 20px 16px 14px;
                        // margin-top: 0;
                        // display: flex;
                        // line-height: 22px;
                        // align-items: center;
                        // justify-content: flex-end;
                        // > div {
                        //     width: auto;
                        //     display: flex;
                        //     i {
                        //         color: $textColor1;
                        //         font-size: 16px;
                        //         cursor: pointer;
                        //         margin-right: 8px;
                        //     }
                        //     span {
                        //         color: $textColor1;
                        //         @include font14;
                        //         cursor: pointer;
                        //         margin: 0;
                        //     }
                        // }
                    }
                }

                > .lists {
                    padding: 0;

                    .grid {
                        width: auto;
                        margin: 0 16px;
                        margin-top: 16px;
                        display: flex;
                        flex-wrap: wrap;
                        justify-content: space-between;

                        // border-top: 1px solid #f7f7f7;
                        li {
                            width: calc(100% / 2);

                            // &:nth-child(n) {
                            //     margin-right: 0;
                            //     margin-bottom: 8px;
                            // }
                            &:hover {
                                box-shadow: none;
                            }
                        }
                    }

                    .list {
                        width: 100%;
                        border-top: 1px solid $borderColor2;

                        li {
                            background: $bgColor3;
                            margin-bottom: 0;
                            padding: 16px 0 12px 0;
                            border-bottom: 1px solid #f7f7f7;
                        }
                    }
                }

                .download {
                    background: #fff;

                    > div {
                        padding: 16px 16px 32px 16px;
                        margin-bottom: 0;

                        h6 {
                            display: none;
                        }

                        > ul {
                            > li {
                                img {
                                    top: 40px;
                                }

                                > div {
                                    padding: 0 0 0 2px;

                                    > div {
                                        > div {
                                            padding: 0 0 12px;

                                            > div {
                                                // margin-right: 28px;
                                            }
                                        }
                                    }

                                    > a {
                                        position: static;
                                        // margin-right: 30px;
                                    }

                                    .down {
                                        position: static;
                                        display: contents;
                                    }
                                }
                            }

                            > li:nth-last-child(1) {
                                border-bottom: none;
                            }
                        }
                    }

                    .tag {
                        justify-content: flex-end;
                        // padding: 24px 16px;
                        padding: 10px 16px;
                        // border-bottom: 1px solid #e5e5e5;
                        border-bottom: 0;

                        > div:nth-child(1) {
                            padding: 0 15px;
                            font-weight: 600;
                            display: none;
                        }

                        div {
                            display: block;
                        }
                    }

                    > div:nth-child(2) {
                        margin: 0 16px;
                        padding: 0;
                    }
                }

                .result_404 {
                    width: auto;
                    margin-top: 0;

                    .result_404_container {
                        padding: 0 16px;
                        background-color: #fff;
                        display: block;

                        > div {
                            width: auto;
                            padding: 0;
                        }

                        .result_404_tit {
                            margin-bottom: 8px;

                            @media (max-width: 768px) {
                                margin-top: 0px;
                            }
                        }

                        .result_404_txt {
                            margin-bottom: 4px;
                        }

                        .result_404_left {
                            padding: 24px;
                            background-color: #fafafb;
                            border-radius: 4px;
                        }

                        .result_404_right {
                            .result_404_txt {
                                line-height: 30px;
                                padding-bottom: 0;

                                i {
                                    font-size: 14px;
                                    color: #4b4b4d;
                                    margin-right: 8px;
                                    display: inline-block;
                                    vertical-align: middle;
                                    margin-top: 0;
                                }

                                a {
                                    color: $textColor1;
                                    text-decoration: none;
                                    font-size: 14px;
                                    &.cka {
                                        &:hover {
                                            text-decoration: underline;
                                        }
                                        cursor: pointer;
                                    }
                                }

                                .ck:hover {
                                    cursor: pointer;
                                    text-decoration: underline;
                                }
                            }
                        }
                    }
                }

                .result_undercarriage {
                    padding: 0;
                    margin-bottom: 0px;
                    background: #fff;
                    overflow: hidden;

                    .result_warning {
                        padding: 10px 16px 10px 40px;
                        margin: 24px 16px 20px;
                        background: rgba(0, 96, 191, 0.04);
                        display: flex;

                        > span {
                            color: #707070;
                            font-size: 13px;
                            line-height: 20px;
                            font-weight: 400;
                        }

                        > i {
                            top: 13px;
                        }
                        @media (max-width: 768px) {
                            margin: 0 16px;
                        }
                    }
                }

                .result_only {
                    padding: 0;
                    margin-bottom: 0;

                    .lists {
                        margin-bottom: 0;
                        padding: 0;
                        background: #fff;
                        overflow: hidden;

                        .result_warning {
                            padding: 10px 16px 10px 40px;
                            margin: 20px 16px;
                            // border: none;
                            // margin-bottom: 0;
                            background: rgba(0, 96, 191, 0.04);

                            > span {
                                color: #707070;
                                font-size: 13px;
                                line-height: 20px;
                                font-weight: 400;
                            }

                            > i {
                                top: 13px;
                                // display: none;
                            }
                        }

                        .result_only_flag {
                            padding: 0 16px 16px;
                        }

                        .list {
                            > li {
                                padding: 12px 16px;
                                background: #fff;
                            }
                        }
                    }
                }
            }
        }

        .pagination {
            width: 100%;

            .viewSize {
                display: none;
            }
        }
    }

    @media (max-width: 768px) and (min-width: 414px) {
        .search_page_container {
            .search_result {
                .lists {
                    overflow: auto;

                    .table {
                        width: 100%;
                    }
                }
            }
        }
    }

    @media (max-width: 414px) {
        .search_page_container {
            .search_result {
                .lists {
                    overflow: auto;

                    .table {
                        width: 100%;
                    }

                    .grid {
                        .custom_li {
                            padding: 12px 12px 16px;
                        }
                    }
                }
            }
        }
    }

    .pagination {
        display: flex;
        width: 84vw;
        // width: 1200px;
        max-width: 1200px;
        justify-content: center;
        margin: 20px auto 16px;
        padding-bottom: 20px;
        position: relative;

        &.b_40 {
            padding-bottom: 40px;
        }

        .viewSize {
            width: auto !important;
            position: absolute;
            top: 0;
            right: 0;

            &:hover {
                ::v-deep .fs-select-active {
                    border-color: #6f6f6f;
                }
            }

            ::v-deep .options-box-bg {
                padding-top: 0;

                .options-wrap {
                    border-top: none;

                    &.options-wrap-absolute {
                        top: 0;
                    }

                    .options-box {
                        .item {
                            justify-content: center;
                        }
                    }
                }
            }

            ::v-deep .fs-select {
                background: transparent;
            }
        }

        &::v-deep .pre_page {
            color: #000;
        }

        @media (max-width: 768px) {
            flex-wrap: wrap;
            margin: 10px auto;
            width: 100% !important;

            ::v-deep .fs-pagination {
                .page_num {
                    text-decoration: none;
                }
            }
        }

        .sendFeedback_m {
            display: none;
            width: 100%;
            margin: 16px 16px 0;
            @include font14;
            color: $textColor1;

            > span {
                color: $textColor6;

                &:hover {
                    cursor: pointer;
                    text-decoration: underline;
                }
            }

            @media (max-width: 768px) {
                display: block;
            }
        }
    }

    .sendFeedback {
        position: absolute;
        left: 0;
        bottom: -45px;
        z-index: 2;
        @include font14;
        color: $textColor1;
        display: flex;
        align-items: center;

        > span {
            color: $textColor6;

            &:hover {
                cursor: pointer;
                text-decoration: underline;
            }
        }

        @media (max-width: 768px) {
            display: none;
        }
    }

    .isPagShow {
        display: none;
    }

    ::v-deep .fs-popup {
        .fs-popup-ctn {
            // width: auto;
            // height: auto;
            @media (max-width: 768px) {
                width: 100%;
                height: 100%;
            }

            .fs-popup-header {
                .iconfont_close {
                    top: 24px;
                    font-size: 16px;
                }
            }

            .fs-popup-body {
                .slot-wraper {
                    .product_wrap {
                        .products_box {
                            .products_main {
                                .products_detail {
                                    .product_price_box {
                                        .qty-box {
                                            width: 80px;
                                        }
                                    }
                                }
                            }

                            .iconfont_delete {
                                font-size: 15px;
                            }

                            .iconfont_delete:hover {
                                color: $textColor1;
                            }
                        }
                    }
                }
            }
        }
    }

    ::v-deep .listAddPop {
        @media (max-width: 768px) {
            .fs-popup-ctn {
                // width: calc(100% - 40px) !important;

                @media (max-width: 480px) {
                    width: 100% !important;
                    height: 100% !important;
                }

                .add_cart_pop {
                    @media (max-width: 768px) {
                        width: 100%;

                        .main {
                            max-height: max-content;
                            padding: 0 16px;
                        }

                        @media (max-width: 480px) {
                            height: 100%;
                            display: flex;
                            flex-direction: column;

                            .main {
                                flex: 1;
                            }
                        }
                    }
                }
            }
        }
    }
}
</style>
