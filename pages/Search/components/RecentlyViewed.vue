<template>
    <div class="recently_viewed" :class="`recently_viewed_${type}`">
        <div class="recently_viewed_box">
            <div class="title">
                <span>{{ $c("components.smallComponents.recentlyViewed") }}</span>
                <span class="iconfont iconfont_down" :class="{ iconfont_down_up: show_list }" @click="show_list = !show_list">&#xe704;</span>
            </div>
            <!-- PC -->
            <div class="content">
                <swiper ref="recently" :options="swiperOption" v-if="list && list.length">
                    <swiper-slide v-for="(item, index) in list" :key="item.product_id">
                        <div class="item_box">
                            <nuxt-link target="_blank" class="img_box" :to="localePath({ name: 'products', params: { id: item.products_id } })">
                                <img :src="item.image" />
                            </nuxt-link>
                            <div class="bottom_box">
                                <nuxt-link target="_blank" class="name" :to="localePath({ name: 'products', params: { id: item.products_id } })">{{ item.products_name }}</nuxt-link>
                                <div class="other">
                                    <!-- <div class="star">
                                        <div class="star_item" v-for="i in 5" :key="i">
                                            <span class="iconfont iconfont_yellow" v-if="format(item?.reviews_total?.reviewsRating) >= i">&#xf066;</span>
                                            <span class="iconfont iconfont_empty" v-if="format(item?.reviews_total?.reviewsRating) < i">&#xe724;</span>
                                        </div>
                                        <div class="rating">{{ `${item?.reviews_total?.reviewsTotalNum ? item?.reviews_total?.reviewsTotalNum : 0} ${$c("pages.List.Level3List.grid.info_complex")}` }}</div>
                                    </div> -->
                                    <div class="price" v-html="item.products_price_str"></div>
                                </div>
                            </div>
                            <!-- <div class="item_add" @click="addToCart(item)">
                                <i class="icon iconfont" :class="{ hide_icon: !['en', 'cn'].includes(website) }">&#xe64c;</i>
                                <span :class="{ hide_icon: !['en', 'cn'].includes(website) }">{{ $c("form.form.add") }}</span>
                            </div> -->
                            <div class="item_add_new">
                                <fs-button @click="addToCart(item)" :loading="is_add_loading">
                                    <i class="icon iconfont iconfont_cart" :class="{ hide_icon: !['en', 'cn'].includes(website) }">&#xe64c;</i>
                                    <span class="add_txt" :class="{ hide_icon: !['en', 'cn'].includes(website) }">{{ $c("form.form.add") }}</span>
                                </fs-button>
                            </div>
                        </div>
                    </swiper-slide>
                    <div class="swiper-pagination" slot="pagination" v-show="list.length > 5"></div>
                </swiper>
                <div class="swiper-btn-box">
                    <div class="swiper-button-prev RecentlyPrev" v-show="list.length > 5" slot="button-prev"></div>
                    <div class="swiper-button-next RecentlyNext" v-show="list.length > 5" slot="button-next"></div>
                </div>
            </div>
            <!-- M -->
            <div class="content_m" v-if="!show_list">
                <div class="list_box">
                    <div class="item_box" v-for="(item, index) in list_show_more ? list : list.slice(0, 6)" :key="item.product_id">
                        <nuxt-link target="_blank" class="img_box" :to="localePath({ name: 'products', params: { id: item.products_id } })">
                            <img :src="item.image" />
                        </nuxt-link>
                        <div class="bottom_box">
                            <nuxt-link target="_blank" class="name" :to="localePath({ name: 'products', params: { id: item.products_id } })">{{ item.products_name }}</nuxt-link>
                            <div class="other">
                                <!-- <div class="star">
                                    <div class="star_item" v-for="i in 5" :key="i">
                                        <span class="iconfont iconfont_yellow" v-if="format(item?.reviews_total?.reviewsRating) >= i">&#xf066;</span>
                                        <span class="iconfont iconfont_empty" v-if="format(item?.reviews_total?.reviewsRating) < i">&#xe724;</span>
                                    </div>
                                    <div class="rating">{{ `${item?.reviews_total?.reviewsTotalNum ? item?.reviews_total?.reviewsTotalNum : 0} ${$c("pages.List.Level3List.grid.info_complex")}` }}</div>
                                </div> -->
                                <div class="price" v-html="item.products_price_str"></div>
                            </div>
                        </div>
                        <div class="item_add" @click="addToCart(item)">
                            <i class="icon iconfont">&#xe64c;</i>
                        </div>
                    </div>
                </div>
                <div class="spotlights_box" v-if="list.length > 6">
                    <span class="spotlights_btn" @click.stop="toogleList">
                        <span class="spotlights_btn_info">{{ list_show_more ? $c("pages.Products.See_less") : $c("pages.Products.See_more") }}</span>
                        <span class="iconfont iconfont_arrow" :class="{ iconfont_arrow_down: list_show_more }">&#xe704;</span>
                    </span>
                </div>
            </div>
        </div>
        <AddCartSuccess :show="show_add_cart_popup" @displayCartPopup="displayCartPopup" @close="hideAddCartPopup" ref="addCartPopup"></AddCartSuccess>
    </div>
</template>
<script>
import AddCartSuccess from "@/popup/AddCartSuccess/AddCartSuccess"
import SlideDown from "@/components/SlideDown/SlideDown.vue"
import FsButton from "@/components/FsButton/FsButton"
import { Swiper, SwiperSlide } from "vue-awesome-swiper"
import { setCookieOptions } from "@/util/util"
import { mapState } from "vuex"
export default {
    name: "RecentlyViewed",
    components: {
        Swiper,
        SwiperSlide,
        SlideDown,
        AddCartSuccess,
        FsButton,
    },
    props: {
        type: {
            type: String,
            default: "",
        },
    },
    data() {
        return {
            show_add_cart_popup: false,
            list: [],
            swiperOption: {
                slidesPerView: 5,
                spaceBetween: 12,
                slidesPerGroup: 5,
                loop: false,
                loopFillGroupWithBlank: true,
                pagination: {
                    el: ".swiper-pagination",
                    clickable: true,
                },
                navigation: {
                    nextEl: ".RecentlyNext",
                    prevEl: ".RecentlyPrev",
                },
                noSwipingClass: "swiper-no-swiping",
            },
            list_show_more: false,
            show_list: false,
            is_add_loading: false,
        }
    },
    computed: {
        swiper() {
            return this.$refs.recently.$swiper
        },
        ...mapState({
            website: (state) => state.webSiteInfo.website,
        }),
    },
    created() {
        if (process.client) {
            let recentlyProducts = JSON.parse(window.localStorage.getItem("recentlyProducts"))
            if (recentlyProducts && recentlyProducts.length) {
                this.$axios
                    .post("/api/special_index", {
                        products_ids: recentlyProducts,
                        products_status: 1,
                        page: this.type !== "cart" ? "product_info" : undefined,
                    })
                    .then((res) => {
                        if (res.data && res.data.length) {
                            this.list.splice(0, this.list.length, ...res.data)
                            let arr = []
                            res.data.map((item) => {
                                arr.push(item.products_id)
                            })
                            this.$cookies.set("recentlyProducts", arr)
                        }
                    })
            }
        }
    },
    mounted() {},
    methods: {
        //显示加购弹框
        showAddCartPopup() {
            this.show_add_cart_popup = true
        },
        // 隐藏加购弹框
        hideAddCartPopup() {
            this.show_add_cart_popup = false
        },
        // 展开收起
        toogleList() {
            this.list_show_more = !this.list_show_more
        },
        // 加购
        addToCart(data) {
            console.log("add_rerently")
            console.log(data)
            this.is_add_loading = true
            this.$refs.addCartPopup.addToCart(data.products_id)
        },
        // 格式化评分
        format(num) {
            if (num) {
                return Math.ceil(num)
            } else {
                return 0
            }
        },
        displayCartPopup(value) {
            this.is_add_loading = false
            if (value) {
                this.showAddCartPopup()
            }
        },
    },
}
</script>
<style lang="scss" scoped>
.recently_viewed {
    max-width: 1200px;
    width: 84vw;
    margin: 0 auto;
    @media (max-width: 1200px) {
        width: 94vw;
    }
    @media (max-width: 960px) {
        width: 100%;
        padding: 0 0 24px;
    }
    .recently_viewed_box {
        .title {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 20px 0 12px;
            border-bottom: 1px solid #e5e5e5;
            margin-bottom: 12px;
            > span {
                @include font16;
                color: $textColor1;
                font-weight: 600;
            }
            .iconfont_down {
                display: none;
                color: $textColor3;
                @include font14;
                transition: all 0.3s;
                transform: rotateX(-180deg);
                &.iconfont_down_up {
                    transform: rotateX(0);
                }
            }
        }
        .content,
        .content_m {
            position: relative;
            padding-bottom: 40px;
            .item_box {
                height: 100%;
                border-radius: 4px;
                background-color: #fafbfb;
                padding: 8px 20px 20px;
                display: flex;
                flex-direction: column;
                // border: 1px solid #eaeaea;
                position: relative;
                transition: all 0.3s;
                .img_box {
                    > img {
                        width: 140px;
                        height: 140px;
                        display: block;
                        margin: 0 auto;
                        mix-blend-mode: multiply;
                    }
                }
                .bottom_box {
                    flex: 1;
                    display: flex;
                    flex-direction: column;
                    justify-content: space-between;
                    .name {
                        @include font14;
                        color: $textColor1;
                        font-weight: 600;
                        -webkit-box-orient: vertical;
                        -webkit-line-clamp: 2;
                        display: -webkit-box;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        // text-align: center;
                    }
                    .other {
                        margin-top: 12px;
                        .price {
                            // margin-top: 6px;
                            @include font14;
                            color: $textColor1;
                            font-weight: 600;
                        }
                        .star {
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            .star_item {
                                display: flex;
                                align-items: center;
                                margin-right: 2px;
                                .iconfont {
                                    height: 14px;
                                    line-height: 1;
                                    font-size: 14px;
                                    color: #d5d5d5;

                                    &.iconfont_yellow {
                                        color: #ffd748;
                                    }
                                }
                            }
                            .rating {
                                @include font13;
                                color: $textColor3;
                                margin-left: 8px;
                            }
                        }
                    }
                }
                .item_add {
                    display: none;
                    position: absolute;
                    right: 20px;
                    bottom: 15px;
                    border-radius: 4px;
                    background: $bgColor5;
                    padding: 6px 8px;
                    align-items: center;
                    justify-content: center;
                    color: $textColor7;
                    cursor: pointer;
                    transition: 0.3s all;
                    .iconfont {
                        font-size: 12px;
                        line-height: 1;
                        &.hide_icon {
                            display: none;
                        }
                    }
                    span {
                        @include font12;
                        margin-left: 4px;
                        &.hide_icon {
                            margin-left: 0;
                        }
                    }
                    &::before {
                        display: block;
                        content: " ";
                        width: 100%;
                        height: 100%;
                        top: 0;
                        left: 0;
                        right: 0;
                        bottom: 0;
                        position: absolute;
                        background: #19191a;
                        border-radius: 4px;
                        opacity: 0;
                        transition: all 0.3s;
                    }
                    &:hover {
                        &::before {
                            opacity: 0.2;
                        }
                    }
                }
                &:hover {
                    background-color: $bgColor3;
                    box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.1);
                    .item_add,
                    .item_add_new {
                        display: flex;
                        @media (max-width: 960px) {
                            display: none;
                        }
                    }
                }
                // @media (max-width: 768px) {
                //     .item_add {
                //         width: 36px;
                //         height: 36px;
                //     }
                // }
                .item_add_new {
                    display: none;
                    position: absolute;
                    right: 20px;
                    bottom: 15px;
                    ::v-deep {
                        .fs-button {
                            height: 32px;
                            padding: 0 8px;
                            .iconfont_cart {
                                width: 14px;
                                font-size: 14px;
                                line-height: 1;
                            }
                            .add_txt {
                                margin-left: 4px;
                                @include font12;
                            }
                        }
                    }
                }
            }
            &:hover {
                ::v-deep {
                    .swiper-button-prev,
                    .swiper-button-next {
                        opacity: 1;
                    }
                }
            }
        }
        .content_m {
            display: none;
        }
        @media (max-width: 1024px) {
            .content,
            .content_m {
                .item_box {
                    .item_add {
                        display: flex;
                        bottom: 18px;
                        .iconfont {
                            &.hide_icon {
                                display: block;
                            }
                        }
                        span {
                            display: none;
                        }
                    }
                }
            }
        }
        @media (max-width: 960px) {
            .title {
                padding: 20px 16px 12px;
                margin-bottom: 24px;
                > span {
                    @include font16;
                    font-weight: 600;
                }
                .iconfont_down {
                    display: block;
                }
            }
            .content {
                display: none;
            }
            .content_m {
                display: block;
                padding: 0 16px;
                .list_box {
                    display: grid;
                    grid-template-columns: repeat(2, 1fr);
                    grid-gap: 12px;
                    padding-bottom: 0;
                }
                .item_box {
                    padding: 8px 20px 20px;
                    .bottom_box {
                        // padding: 0 12px;
                        .name {
                            @include font13;
                        }
                    }
                    .item_add {
                        display: none;
                        right: 20px;
                        bottom: 18px;
                    }
                }
                .spotlights_box {
                    display: flex;
                    justify-content: center;
                    margin-top: 12px;
                    .spotlights_btn {
                        display: flex;
                        min-width: 140px;
                        color: $textColor6;
                        cursor: pointer;
                        align-items: center;
                        justify-content: center;
                        &:hover {
                            .spotlights_btn_info {
                                text-decoration: underline;
                            }
                        }
                        .spotlights_btn_info {
                            @include font13;
                        }
                        .iconfont_arrow {
                            display: inline-block;
                            width: 12px;
                            height: 12px;
                            font-size: 12px;
                            margin: 0 0 0 4px;
                            transition: all 0.3s;
                            &.iconfont_arrow_down {
                                transform: rotateX(180deg);
                            }
                        }
                    }
                }
            }
        }
    }
}
::v-deep {
    .swiper-btn-box {
        width: 100%;
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        .swiper-button-prev {
            color: #fff;
            font-size: 20px;
            width: 40px;
            height: 40px;
            text-align: center;
            background: rgba(0, 0, 0, 0.2);
            border-radius: 50%;
            transition: all 0.3s;
            left: -80px;
            // left: 12px;
            right: auto;
            margin-top: -54px;
            opacity: 0;
            z-index: 10;
            pointer-events: auto;

            // @media (max-width: 1024px) {
            //     display: none;
            // }

            &:after {
                content: "\f048";
                font-family: "iconfont";
                font-size: 20px;
            }

            &:hover {
                background: rgba(0, 0, 0, 0.4);
            }
        }

        .swiper-button-next {
            color: #fff;
            font-size: 20px;
            width: 40px;
            height: 40px;
            margin-top: -54px;
            text-align: center;
            background: rgba(0, 0, 0, 0.2);
            right: -80px;
            left: auto;
            border-radius: 50%;
            transition: all 0.3s;
            z-index: 10;
            opacity: 0;
            pointer-events: auto;

            // @media (max-width: 1024px) {
            //     display: none;
            // }

            &:after {
                content: "\f047";
                font-family: "iconfont";
                font-size: 20px;
            }

            &:hover {
                background: rgba(0, 0, 0, 0.4);
            }
        }
        .swiper-button-disabled {
            background: rgba(0, 0, 0, 0.05);
            &:hover {
                background: rgba(0, 0, 0, 0.05);
            }
        }

        @media (max-width: 1200px) {
            .swiper-button-prev {
                left: 12px;
            }
            .swiper-button-next {
                right: 12px;
            }
        }
    }
    .swiper-container {
        z-index: 0;
        margin: 0 -12px;
        padding: 12px 12px 28px;
        // padding-bottom: 28px;
        .swiper-slide {
            height: auto;
            > a {
                text-decoration: none;
            }
        }
        .swiper-pagination {
            bottom: 0;
            display: flex;
            justify-content: center;

            .swiper-pagination-bullet {
                opacity: 0.4;
                transition: all 0.3s;
                background: #707070;
                vertical-align: bottom;

                &.swiper-pagination-bullet-active {
                    background: #707070;
                    width: 20px;
                    border-radius: 4px;
                    opacity: 1;
                }
            }
        }
    }
}
</style>
