<template>
    <div>
        <div class="product-box" v-if="productForm && Object.values(productForm).length">
            <h3>{{ productForm.title }}</h3>
            <div class="card">
                <div class="table">
                    <div class="tr head-tr">
                        <div class="th info">{{ $c("pages.RMAReturn.item") }}</div>
                        <div class="th quantity">{{ $c("pages.RMAReturn.returnQuantity") }}</div>
                        <div class="th price">{{ $c("pages.RMAReturn.unitPrice") }}</div>
                        <div class="th total">{{ $c("pages.RMAReturn.rmaTotal") }}</div>
                    </div>
                    <div class="tr body-tr" v-for="(item, index) in productForm.list" :key="index">
                        <div class="td info">
                            <div class="product-info">
                                <img :src="item.product_image" alt="product" class="img" />
                                <dl class="label">
                                    <dt>
                                        <nuxt-link target="_blank" :to="localePath({ name: 'products', params: { id: item.products_id } })">
                                            {{ item.products_name }}
                                        </nuxt-link>
                                    </dt>
                                    <dd class="small">
                                        <span>FS P/N: {{ item.products_model }}</span> <span>#{{ item.products_id }}</span>
                                    </dd>
                                    <dd class="strong-m" v-html="item.products_num + ' x ' + item.price_str"></dd>
                                </dl>
                            </div>
                        </div>
                        <div class="td quantity">{{ item.products_num }}</div>
                        <div class="td price" v-html="item.price_str"></div>
                        <div class="td total" v-html="item.total_price_str"></div>
                    </div>
                </div>
                <div class="total-box">
                    <dl class="total-sum">
                        <dt>{{ $c("pages.RMA.RMATotal") }}:</dt>
                        <dd v-html="productForm.total"></dd>
                    </dl>
                </div>
            </div>
        </div>
        <div>
            <div class="card" v-if="Object.keys(topForm).length > 0">
                <content-item :class="{ p0: index === 0 }" v-for="(item, index) in topForm.list" :option="item" :key="index">
                    <template #productDes="{ product }">
                        <div class="descrip">
                            <p class="descrip-box" @click="isExtend = !isExtend">
                                <span>{{ $c("pages.CaseDetail.detailsPage.productDevelopment.productTips") }}</span
                                ><span class="iconfont" :class="{ active: isExtend }">&#xe704;</span>
                            </p>
                            <slide-down>
                                <ul class="descrip-box-content" v-show="isExtend">
                                    <li v-for="({ products_options_name, products_values_name }, k) in product" :key="k">
                                        <span>{{ `${products_options_name} - ${products_values_name}` }}</span>
                                    </li>
                                </ul>
                            </slide-down>
                        </div>
                    </template>
                </content-item>
            </div>
        </div>

        <!-- 内容展示区 -->

        <div class="details-box" v-if="Object.values(botForm).length > 1">
            <h3>{{ botForm.title }}</h3>
            <div class="card">
                <content-item :class="{ p0: index === 0 }" v-for="(item, index) in botForm.list" :option="item" :key="index" />
            </div>
        </div>
        <!-- 多个内容 -->
        <template v-if="arrForm.length > 0">
            <div class="details-box" v-for="(i, k) in arrForm" :key="k">
                <h3>{{ i.title }}</h3>
                <div class="card">
                    <content-item :class="{ p0: index === 0 }" v-for="(item, index) in i.list" :option="item" :key="index" />
                </div>
            </div>
        </template>
    </div>
</template>

<script>
import ContentItem from "./FormItem.vue"
import SlideDown from "@/components/SlideDown/SlideDown.vue"
import transMixins from "@/components/AccountAddress/getTransMixins"
import { mapState, mapGetters } from "vuex"
export default {
    props: {
        caseinfo: {
            type: Object,
            default: () => {},
        },
    },
    mixins: [transMixins],
    components: {
        ContentItem,
        SlideDown,
    },
    data() {
        return {
            topForm: {},
            botForm: {},
            arrForm: [],
            productForm: {},
            isExtend: false,
        }
    },
    computed: {
        ...mapState({
            website: (state) => state.webSiteInfo.website,
            select_country_code: (state) => state.selectCountry.select_country_code,
        }),
        ...mapGetters({
            isCn: "webSiteInfo/isCn",
            isCnTr: "webSiteInfo/isCnTr",
        }),
        block() {
            return this.website === "es" ? ", " : ","
        },
        taxLabel() {
            const vatCountryList = [
                "AT",
                "DE",
                "LU",
                "CH",
                "NL",
                "MT",
                "DK",
                "FI",
                "SE",
                "CY",
                "HR",
                "CZ",
                "LT",
                "PL",
                "SK",
                "SI",
                "BG",
                "EE",
                "GR",
                "HU",
                "RO",
                "LV",
                "NO",
                "BE",
                "FR",
                "IT",
                "ES",
                "PT",
                "GB",
                "IE",
                "IS",
            ]
            if (vatCountryList.includes(this.select_country_code)) {
                return "VAT"
            } else if (this.select_country_code === "MD") {
                return "IDNO"
            } else if (this.select_country_code === "MC") {
                return "NIE"
            } else if (this.select_country_code === "BR") {
                return "CNPJ"
            } else if (this.select_country_code === "MX") {
                return "RFC"
            } else if (this.select_country_code === "PA") {
                return "RUC"
            } else if (["PR", "US"].includes(this.select_country_code)) {
                return "EIN"
            } else if (this.select_country_code === "CA") {
                return "BN"
            } else if (this.select_country_code === "ID") {
                return "NIB"
            } else if (["MY", "MM", "FJ"].includes(this.select_country_code)) {
                return "CRN"
            } else if (this.select_country_code === "SG") {
                return "UEN"
            } else if (this.select_country_code === "VN") {
                return "MSD"
            } else if (this.select_country_code === "JP") {
                return "CN"
            } else if (this.select_country_code === "AU") {
                return "ABN"
            } else if (this.select_country_code === "NZ") {
                return "NZBN"
            } else {
                return ""
            }
        },
    },

    methods: {
        // 格式化内容
        getContent(info) {
            console.log("infoinfoinfoinfo", info)
            switch (info.created_type) {
                // 新增 Enterprise Switches表单字段与 PicOS® Data Center Switches一致
                // picos switch  Data Center
                case 89:
                case 84:
                    let picosSwitchesContent = info.details.extraInfo
                    if (picosSwitchesContent.request_type_value === 1) {
                        //1-->Hardware Support Request  2--->Feature Support Request
                        this.topForm = {
                            list: [
                                { title: this.$c("single.ModifyRequest.common.requestType"), content: picosSwitchesContent.request_type, type: "inline", isLinFitsrt: true },
                                {
                                    title: this.$c("single.ModifyRequest.picosSwitches.vendor"),
                                    content:
                                        picosSwitchesContent.hardware_vendor_others.length > 0 ? `${this.$c("form.form.other_txt")}-${picosSwitchesContent.hardware_vendor_others}` : picosSwitchesContent.hardware_vendor,
                                    type: "inline",
                                    isLinFitsrt: true,
                                },
                                {
                                    title: this.$c("single.PicosRequest.hardware.switchmodel"),
                                    content: picosSwitchesContent.switch_model,
                                    type: "inline",
                                },
                                { title: this.$c("single.PicosRequest.hardware.switchASIC"), content: picosSwitchesContent.switch_asic, type: "inline", isLinFitsrt: true },
                                { title: this.$c("single.PicosRequest.hardware.port"), content: picosSwitchesContent.port_configuration, type: "inline" },
                                { title: this.$c("single.PicosRequest.hardware.critical"), content: picosSwitchesContent.protocol_requirements, type: "inline", isLinFitsrt: true },
                                {
                                    title: this.$c("single.PicosRequest.hardware.switchinstalled"),
                                    content: picosSwitchesContent.os_installed === 1 ? this.$c("form.form.yes_txt") : this.$c("form.form.no_txt"),
                                    type: "inline",
                                    isLinFitsrt: true,
                                },
                                { title: this.$c("single.ModifyRequest.picosSwitches.os_version"), content: picosSwitchesContent.os_version || "/", type: "inline", isLinFitsrt: true },
                            ],
                        }
                    } else {
                        this.topForm = {
                            list: [
                                { title: this.$c("single.ModifyRequest.common.requestType"), content: picosSwitchesContent.request_type, type: "inline", isLinFitsrt: true },
                                {
                                    title: this.$c("single.PicosRequest.feature.featurerequest"),
                                    content: picosSwitchesContent.feature_request,
                                    type: "inline",
                                    isLinFitsrt: true,
                                },
                                { title: this.$c("single.PicosRequest.hardware.switchmodel"), content: picosSwitchesContent.platform_name, type: "inline" },
                            ],
                        }
                    }
                    this.botForm = {
                        title: this.$c("pages.CaseDetail.detailsPage.common.caseInformation"),
                        list: [
                            { title: this.$c("pages.CaseDetail.detailsPage.technicalSupport.createdBy"), content: info.details.create_by, type: "inline", isLinFitsrt: true },
                            { title: this.$c("pages.CaseDetail.detailsPage.technicalSupport.email"), content: info.details.email, type: "inline" },
                            { title: this.$c("pages.CaseDetail.detailsPage.technicalSupport.phoneNumber"), content: info.details.phone, type: "inline", isLinFitsrt: true },
                            { title: this.$c("form.form.country"), content: info.details.country, type: "inline" },
                            { title: this.$c("pages.CaseDetail.detailsPage.askDemo.companyName"), content: info.details.company_name, type: "inline" },
                        ],
                    }
                    break
                // ampcon-dc
                case 85:
                    let ampConDcContent = info.details.extraInfo
                    if (ampConDcContent.request_type_value === 1) {
                        //1-->Hardware Support Request  2--->Feature Support Request
                        this.topForm = {
                            list: [
                                { title: this.$c("single.ModifyRequest.common.requestType"), content: ampConDcContent.request_type, type: "inline", isLinFitsrt: true },
                                { title: this.$c("single.ModifyRequest.common.applicationScenario"), content: ampConDcContent.application_scenario, type: "inline", isLinFitsrt: true },
                                {
                                    title: this.$c("single.ModifyRequest.ampConDC.device_type"),
                                    content: ampConDcContent.device_type_others.length > 0 ? ampConDcContent.device_type_others : ampConDcContent.device_type,
                                    type: "inline",
                                    isLinFitsrt: true,
                                },
                                {
                                    title: this.$c("single.ModifyRequest.ampConDC.vendor"),
                                    content:
                                        ampConDcContent.vendor_others.length > 0
                                            ? `${this.$c("form.form.other_txt")}-${String(ampConDcContent.vendor_others)}`
                                            : [ampConDcContent.vendor_switch, ampConDcContent.vendor_network].find((val) => val !== undefined && val !== null && val !== ""),
                                    type: "inline",
                                },
                                { title: this.$c("single.ModifyRequest.ampConDC.device_model"), content: ampConDcContent.device_model, type: "inline", isLinFitsrt: true },
                                { title: this.$c("single.ModifyRequest.ampConDC.os_version"), content: ampConDcContent.os_version, type: "inline" },
                                {
                                    title: this.$c("single.ModifyRequest.ampConDC.current_management_platform"),
                                    content: ampConDcContent.current_management_platform.length > 0 ? ampConDcContent.current_management_platform : "/",
                                    type: "inline",
                                },
                            ],
                        }
                    } else {
                        this.topForm = {
                            list: [
                                { title: this.$c("single.ModifyRequest.common.requestType"), content: ampConDcContent.request_type, type: "inline", isLinFitsrt: true },
                                { title: this.$c("single.ModifyRequest.common.applicationScenario"), content: ampConDcContent.application_scenario, type: "inline", isLinFitsrt: true },
                                {
                                    title: this.$c("single.ModifyRequest.ampConCampus.feature_request"),
                                    content: ampConDcContent.feature_request_others.length > 0 ? `${this.$c("form.form.other_txt")}-${String(ampConDcContent.feature_request_others)}` : ampConDcContent.feature_request,
                                    type: "inline",
                                    isLinFitsrt: true,
                                },
                            ],
                        }
                    }
                    this.botForm = {
                        title: this.$c("pages.CaseDetail.detailsPage.common.caseInformation"),
                        list: [
                            { title: this.$c("pages.CaseDetail.detailsPage.technicalSupport.createdBy"), content: info.details.create_by, type: "inline", isLinFitsrt: true },
                            { title: this.$c("pages.CaseDetail.detailsPage.technicalSupport.email"), content: info.details.email, type: "inline" },
                            { title: this.$c("pages.CaseDetail.detailsPage.technicalSupport.phoneNumber"), content: info.details.phone, type: "inline", isLinFitsrt: true },
                            { title: this.$c("form.form.country"), content: info.details.country, type: "inline" },
                            { title: this.$c("pages.CaseDetail.detailsPage.askDemo.companyName"), content: info.details.company_name, type: "inline" },
                        ],
                    }
                    break
                //AmpCon-Campus Management Platform
                case 86:
                    let ampConCampusContent = info.details.extraInfo
                    if (ampConCampusContent.request_type_value === 1) {
                        //1-->Hardware Support Request  2--->Feature Support Request
                        this.topForm = {
                            list: [
                                { title: this.$c("single.ModifyRequest.common.requestType"), content: ampConCampusContent.request_type, type: "inline", isLinFitsrt: true },
                                { title: this.$c("single.ModifyRequest.common.applicationScenario"), content: ampConCampusContent.application_scenario, type: "inline", isLinFitsrt: true },
                                {
                                    title: this.$c("single.ModifyRequest.ampConDC.device_type"),
                                    content: ampConCampusContent.device_type_others.length > 0 ? ampConCampusContent.device_type_others : ampConCampusContent.device_type,
                                    type: "inline",
                                    isLinFitsrt: true,
                                },
                                {
                                    title: this.$c("single.ModifyRequest.ampConDC.vendor"),
                                    content:
                                        ampConCampusContent.vendor_others.length > 0
                                            ? `${this.$c("form.form.other_txt")}-${String(ampConCampusContent.vendor_others)}`
                                            : [ampConCampusContent.vendor_switch, ampConCampusContent.vendor_router, ampConCampusContent.vendor_firewall, ampConCampusContent.vendor_ap].find(
                                                  (val) => val !== undefined && val !== null && val !== ""
                                              ),
                                    type: "inline",
                                    isLinFitsrt: true,
                                },
                                { title: this.$c("single.ModifyRequest.ampConDC.device_model"), content: ampConCampusContent.device_model, type: "inline" },
                                { title: this.$c("single.ModifyRequest.ampConDC.os_version"), content: ampConCampusContent.os_version, type: "inline", isLinFitsrt: true },
                                {
                                    title: this.$c("single.ModifyRequest.ampConDC.current_management_platform"),
                                    content: ampConCampusContent.current_management_platform.length > 0 ? ampConCampusContent.current_management_platform : "/",
                                    type: "inline",
                                },
                            ],
                        }
                    } else {
                        this.topForm = {
                            list: [
                                { title: this.$c("single.ModifyRequest.common.requestType"), content: ampConCampusContent.request_type, type: "inline", isLinFitsrt: true },
                                { title: this.$c("single.ModifyRequest.common.applicationScenario"), content: ampConCampusContent.application_scenario, type: "inline", isLinFitsrt: true },
                                {
                                    title: this.$c("single.ModifyRequest.ampConCampus.feature_request"),
                                    content: ampConCampusContent.feature_request_others.length > 0 ? ampConCampusContent.feature_request_others : ampConCampusContent.feature_request,
                                    type: "inline",
                                    isLinFitsrt: true,
                                },
                            ],
                        }
                    }
                    this.botForm = {
                        title: this.$c("pages.CaseDetail.detailsPage.common.caseInformation"),
                        list: [
                            { title: this.$c("pages.CaseDetail.detailsPage.technicalSupport.createdBy"), content: info.details.create_by, type: "inline", isLinFitsrt: true },
                            { title: this.$c("pages.CaseDetail.detailsPage.technicalSupport.email"), content: info.details.email, type: "inline" },
                            { title: this.$c("pages.CaseDetail.detailsPage.technicalSupport.phoneNumber"), content: info.details.phone, type: "inline", isLinFitsrt: true },
                            { title: this.$c("form.form.country"), content: info.details.country, type: "inline" },
                            { title: this.$c("pages.CaseDetail.detailsPage.askDemo.companyName"), content: info.details.company_name, type: "inline" },
                        ],
                    }
                    break
                //AmpCon-T Management Platform
                case 87:
                    let ampConTContent = info.details.extraInfo
                    if (ampConTContent.request_type_value === 1) {
                        //1-->Hardware Support Request  2--->Feature Support Request
                        this.topForm = {
                            title: ampConTContent.request_type,
                            list: [
                                { title: this.$c("single.ModifyRequest.common.requestType"), content: ampConTContent.request_type, type: "inline", isLinFitsrt: true },
                                { title: this.$c("single.ModifyRequest.common.applicationScenario"), content: ampConTContent.application_scenario, type: "inline", isLinFitsrt: true },

                                {
                                    title: this.$c("single.ModifyRequest.ampConDC.vendor"),
                                    content: ampConTContent.vendor_others.length > 0 ? `${this.$c("form.form.other_txt")}-${String(ampConTContent.vendor_others)}` : ampConTContent.vendor,
                                    type: "inline",
                                    isLinFitsrt: true,
                                },
                                { title: this.$c("single.ModifyRequest.ampConDC.device_model"), content: ampConTContent.device_model, type: "inline" },
                                { title: this.$c("single.ModifyRequest.ampConDC.os_version"), content: ampConTContent.os_version, type: "inline", isLinFitsrt: true },
                                {
                                    title: this.$c("single.ModifyRequest.ampConDC.current_management_platform"),
                                    content: ampConTContent.current_management_platform.length > 0 ? ampConTContent.current_management_platform : "/",
                                    type: "inline",
                                },
                            ],
                        }
                    } else {
                        this.topForm = {
                            title: ampConTContent.request_type,
                            list: [
                                { title: this.$c("single.ModifyRequest.common.requestType"), content: ampConTContent.request_type, type: "inline", isLinFitsrt: true },
                                { title: this.$c("single.ModifyRequest.common.applicationScenario"), content: ampConTContent.application_scenario, type: "inline", isLinFitsrt: true },
                                {
                                    title: this.$c("single.ModifyRequest.ampConCampus.feature_request"),
                                    content: ampConTContent.feature_request_others.length > 0 ? `${this.$c("form.form.other_txt")}-${String(ampConTContent.feature_request_others)}` : ampConTContent.feature_request,
                                    type: "inline",
                                    isLinFitsrt: true,
                                },
                            ],
                        }
                    }
                    this.botForm = {
                        title: this.$c("pages.CaseDetail.detailsPage.common.caseInformation"),
                        list: [
                            { title: this.$c("pages.CaseDetail.detailsPage.technicalSupport.createdBy"), content: info.details.create_by, type: "inline", isLinFitsrt: true },
                            { title: this.$c("pages.CaseDetail.detailsPage.technicalSupport.email"), content: info.details.email, type: "inline" },
                            { title: this.$c("pages.CaseDetail.detailsPage.technicalSupport.phoneNumber"), content: info.details.phone, type: "inline", isLinFitsrt: true },
                            { title: this.$c("form.form.country"), content: info.details.country, type: "inline" },
                            { title: this.$c("pages.CaseDetail.detailsPage.askDemo.companyName"), content: info.details.company_name, type: "inline", isLinFitsrt: true },
                        ],
                    }
                    break

                // optical transceiver
                case 88:
                    let opticalContent = info.details.extraInfo
                    this.topForm = {
                        list: [
                            { title: this.$c("single.ModifyRequest.opticalTransceivers.pn"), content: opticalContent.pn, type: "inline", isLinFitsrt: true },
                            { title: this.$c("single.ModifyRequest.common.requestType"), content: opticalContent.request_type, type: "inline" },
                            { title: this.$c("single.ModifyRequest.ampConDC.device_model"), content: opticalContent.device_model || "/", type: "inline", isLinFitsrt: true },
                            { title: this.$c("single.ModifyRequest.ampConDC.os_version"), content: opticalContent.os_version || "/", type: "inline" },
                        ],
                    }
                    this.botForm = {
                        title: this.$c("pages.CaseDetail.detailsPage.common.caseInformation"),
                        list: [
                            { title: this.$c("pages.CaseDetail.detailsPage.technicalSupport.createdBy"), content: info.details.create_by, type: "inline", isLinFitsrt: true },
                            { title: this.$c("pages.CaseDetail.detailsPage.technicalSupport.email"), content: info.details.email, type: "inline" },
                            { title: this.$c("pages.CaseDetail.detailsPage.technicalSupport.phoneNumber"), content: info.details.phone, type: "inline", isLinFitsrt: true },
                            { title: this.$c("form.form.country"), content: info.details.country, type: "inline" },
                            { title: this.$c("pages.CaseDetail.detailsPage.askDemo.companyName"), content: info.details.company_name, type: "inline", isLinFitsrt: true },
                        ],
                    }
                    break
                //PicOS硬件适配留资
                case 80:
                    let hardwareContent = info.details.hardware_info
                    this.topForm = {
                        title: "",
                        list: [
                            { title: this.$c("single.PicosRequest.hardware.switchvendor"), content: hardwareContent.switch_vendor, type: "inline", isLinFitsrt: true },
                            { title: this.$c("single.PicosRequest.hardware.switchmodel"), content: hardwareContent.switch_model, type: "inline" },
                            { title: this.$c("single.PicosRequest.hardware.switchASIC"), content: hardwareContent.switch_asic, type: "inline" },
                            { title: this.$c("single.PicosRequest.hardware.port"), content: hardwareContent.port_configuration, type: "inline" },
                            { title: this.$c("single.PicosRequest.hardware.critical"), content: hardwareContent.protocol_requirements, type: "inline" },
                            { title: this.$c("single.PicosRequest.hardware.switchinstalled"), content: hardwareContent.switchinstalled, type: "inline" },
                            { title: this.$c("single.PicosRequest.hardware.system"), content: hardwareContent.system_version, type: "inline" },
                        ],
                    }
                    this.botForm = {
                        title: this.$c("pages.CaseDetail.detailsPage.common.caseInformation"),
                        list: [
                            { title: this.$c("pages.CaseDetail.detailsPage.technicalSupport.createdBy"), content: info.details.create_by, type: "inline", isLinFitsrt: true },
                            { title: this.$c("pages.CaseDetail.detailsPage.askDemo.companyName"), content: info.details.company_name, type: "inline" },
                            { title: this.$c("pages.CaseDetail.detailsPage.technicalSupport.email"), content: info.details.email, isLinFitsrt: true, type: "inline" },
                            { title: this.$c("pages.CaseDetail.detailsPage.technicalSupport.phoneNumber"), content: info.details.phone, type: "inline" },
                        ],
                    }
                    break

                //PicOS软件适配留资
                case 81:
                    let featureContent = info.details.feature_info
                    this.topForm = {
                        title: "",
                        list: [
                            { title: this.$c("single.PicosRequest.feature.featurerequest"), content: featureContent.feature_request, type: "inline", isLinFitsrt: true },
                            { title: this.$c("single.PicosRequest.feature.platformname"), content: featureContent.platform_name, type: "inline" },
                        ],
                    }
                    this.botForm = {
                        title: this.$c("pages.CaseDetail.detailsPage.common.caseInformation"),
                        list: [
                            { title: this.$c("pages.CaseDetail.detailsPage.technicalSupport.createdBy"), content: info.details.create_by, type: "inline", isLinFitsrt: true },
                            { title: this.$c("pages.CaseDetail.detailsPage.askDemo.companyName"), content: info.details.company_name, type: "inline" },
                            { title: this.$c("pages.CaseDetail.detailsPage.technicalSupport.email"), content: info.details.email, isLinFitsrt: true, type: "inline" },
                            { title: this.$c("pages.CaseDetail.detailsPage.technicalSupport.phoneNumber"), content: info.details.phone, type: "inline" },
                        ],
                    }
                    break
                //新增产品安装服务需求
                case 44:
                    let contentDetail = info.details.requirement_info ? info.details.requirement_info : info.details.requirement
                    this.topForm = {
                        title: "",
                        list: [
                            { title: this.$c("pages.CaseDetail.detailsPage.askDemo.briefProjectDescription"), content: info.details.description, type: "block" },
                            { type: "product", products: info.details.products_info, title: this.$c("pages.CaseDetail.detailsPage.askDemo.sampleProduct") },
                            { title: this.$c("pages.CaseDetail.detailsPage.Solution.type"), content: info.details.type, type: "inline", isLinFitsrt: true },
                            {
                                title: this.$c("pages.Products.installationPopup.ServiceRequirement"),
                                content: contentDetail,
                                type: "inline",
                            },
                            { title: this.$c("pages.CaseDetail.detailsPage.technicalSupport.createdBy"), content: info.details.create_by, type: "inline", isLinFitsrt: true },
                            { title: this.$c("pages.CaseDetail.detailsPage.technicalSupport.email"), content: info.details.email, type: "inline" },
                            { title: this.$c("pages.CaseDetail.detailsPage.remoteTest.countryRegion"), content: info.details.country, isLinFitsrt: true, type: "inline" },
                            { title: this.$c("pages.CaseDetail.detailsPage.askDemo.state"), content: info.details.state, type: "inline" },
                            { title: this.$c("pages.CaseDetail.detailsPage.technicalSupport.phoneNumber"), content: info.details.phone.length > 2 ? info.details.phone : "/", type: "inline" },
                        ],
                    }
                    break
                //新增产品安装服务需求
                case 48:
                    this.topForm = {
                        title: "",
                        list: [
                            { title: this.$c("pages.CaseDetail.detailsPage.askDemo.briefProjectDescription"), content: info.details.description, type: "block" },
                            { type: "product", products: info.details.products_info, title: this.$c("pages.CaseDetail.detailsPage.askDemo.sampleProduct") },
                            { title: this.$c("pages.CaseDetail.detailsPage.Solution.type"), content: info.details.type, type: "inline", isLinFitsrt: true },
                            { title: this.$c("pages.CaseDetail.detailsPage.technicalSupport.createdBy"), content: info.details.create_by, type: "inline" },
                            { title: this.$c("pages.CaseDetail.detailsPage.technicalSupport.email"), content: info.details.email, type: "inline", isLinFitsrt: true },
                            { title: this.$c("pages.CaseDetail.detailsPage.technicalSupport.phoneNumber"), content: info.details.phone.length > 2 ? info.details.phone : "/", type: "inline" },
                            { title: this.$c("pages.CaseDetail.detailsPage.remoteTest.countryRegion"), content: info.details.country, type: "inline", isLinFitsrt: true },
                            { title: this.$c("pages.CaseDetail.detailsPage.askDemo.state"), content: info.details.state, type: "inline" },
                        ],
                    }
                    break
                // 样品申请
                case 9:
                    console.log("样品申请")
                    this.topForm = {
                        title: "",
                        list: [
                            { title: this.$c("pages.CaseDetail.detailsPage.askDemo.briefProjectDescription"), content: info.details.description, type: "block" },
                            { type: "product", products: info.details.products, title: this.$c("pages.CaseDetail.detailsPage.askDemo.sampleProduct") },
                            { title: this.$c("pages.CaseDetail.detailsPage.askDemo.sourceFrom"), content: info.details.source_from, isLinFitsrt: true, type: "inline" },
                        ],
                    }

                    const oldList = [
                        { title: this.$c("pages.CaseDetail.detailsPage.askDemo.zipCode"), content: info.details.zip_code, isLinFitsrt: true, type: "inline" },
                        { title: this.$c("pages.CaseDetail.detailsPage.askDemo.phoneNumber"), content: info.details.phone, type: "inline" },
                        { title: this.$c("pages.CaseDetail.detailsPage.askDemo.companyName"), content: info.details.company_name, isLinFitsrt: true, type: "inline" },
                        { title: this.$c("pages.CaseDetail.detailsPage.askDemo.jobTitle"), content: info.details.job_title, type: "inline" },
                        { title: this.$c("pages.CaseDetail.detailsPage.askDemo.address"), content: info.details.address, isLinFitsrt: true, type: "inline" },
                        { title: this.$c("pages.CaseDetail.detailsPage.askDemo.address2"), content: info.details.address2, type: "inline" },
                        { title: this.$c("pages.CaseDetail.detailsPage.askDemo.state"), content: info.details.state, isLinFitsrt: true, type: "inline" },
                        { title: this.$c("pages.CaseDetail.detailsPage.askDemo.city"), content: info.details.city, type: "inline" },
                    ]
                    const newsList = [{ title: this.$c("pages.CaseDetail.detailsPage.askDemo.phoneNumber"), content: info.details.phone, type: "inline", isLinFitsrt: true }]
                    const maxList = info.service_type === 17 ? newsList : oldList
                    this.botForm = {
                        title: this.$c("pages.CaseDetail.detailsPage.askDemo.sampleDliveredTo"),
                        list: [
                            { title: this.$c("pages.CaseDetail.detailsPage.application.createdBy"), content: info.details.create_by, isLinFitsrt: true, type: "inline" },
                            { title: this.$c("pages.CaseDetail.detailsPage.askDemo.type"), content: info.details.type, type: "inline" },
                            { title: this.$c("pages.CaseDetail.detailsPage.askDemo.countryRegion"), content: info.details.country, isLinFitsrt: true, type: "inline" },
                            { title: this.$c("pages.CaseDetail.detailsPage.askDemo.email"), content: info.details.email, type: "inline" },
                            ...maxList,
                        ],
                    }
                    break
                // 样品申请 - 延期申请
                case 43:
                    console.log("样品申请")
                    this.topForm = {
                        title: "",
                        list: [
                            { title: this.$c("pages.CaseDetail.detailsPage.askDemo.briefProjectDescription"), content: info.details.brie || info.details.brief, type: "block" },
                            { type: "product", products: info.details.products, title: this.$c("pages.CaseDetail.detailsPage.askDemo.sampleProduct") },
                            { title: this.$c("pages.CaseDetail.detailsPage.askDemo.sourceFrom"), content: info.details.source_from, isLinFitsrt: true, type: "inline" },
                            { title: this.$c("pages.CaseDetail.detailsPage.askDemo.order"), content: info.details.orders_number, type: "inline" },
                            { title: this.$c("pages.CaseDetail.detailsPage.askDemo.extendedPeriod"), content: info.details.extend_period, isLinFitsrt: true, type: "inline" },
                            { title: this.$c("pages.CaseDetail.detailsPage.askDemo.reasonForExtension"), content: info.details.description, type: "inline" },
                        ],
                    }

                    this.botForm = {
                        title: this.$c("pages.CaseDetail.detailsPage.askDemo.sampleDliveredTo"),
                        list: [
                            { title: this.$c("pages.CaseDetail.detailsPage.application.createdBy"), content: info.details.create_by, isLinFitsrt: true, type: "inline" },
                            { title: this.$c("pages.CaseDetail.detailsPage.askDemo.type"), content: info.details.type, type: "inline" },
                            { title: this.$c("pages.CaseDetail.detailsPage.askDemo.countryRegion"), content: info.details.country, isLinFitsrt: true, type: "inline" },
                            { title: this.$c("pages.CaseDetail.detailsPage.askDemo.email"), content: info.details.email, type: "inline" },
                            { title: this.$c("pages.CaseDetail.detailsPage.askDemo.phoneNumber"), content: info.details.phone, type: "inline", isLinFitsrt: true },
                        ],
                    }
                    break
                case 70:
                case 11:
                    if (info.service_type == 25) {
                        console.log("软件定制")
                        if (info.details.company_name && info.details.company_name?.trim().length > 0) {
                            console.log(info.details.products_info.length, "长度")
                            if (info.details.products_info.length > 0) {
                                this.topForm = {
                                    title: "",
                                    list: [
                                        { title: this.$c("pages.CaseDetail.detailsPage.askDemo.briefProjectDescription"), content: info.details.description, type: "block" },
                                        { type: "product", products: info.details.products_info, title: this.$c("pages.CaseDetail.detailsPage.emailUs.product") },
                                        { title: this.$c("pages.CaseDetail.detailsPage.Solution.type"), content: info.details.type, type: "inline", isLinFitsrt: true },
                                        { title: this.$c("pages.CaseDetail.detailsPage.technicalSupport.createdBy"), content: info.details.create_by, type: "inline" },
                                        { title: this.$c("pages.CaseDetail.detailsPage.technicalSupport.email"), content: info.details.email, type: "inline", isLinFitsrt: true },
                                        { title: this.$c("pages.CaseDetail.detailsPage.technicalSupport.phoneNumber"), content: info.details.phone.length > 2 ? info.details.phone : "/", type: "inline" },
                                        { title: this.$c("pages.CaseDetail.detailsPage.technicalSupport.country"), content: info.details.country, type: "inline", isLinFitsrt: true },
                                        { title: this.$c("form.form.company_name"), content: info.details.company_name, type: "inline" },
                                        { title: this.$c("pages.CaseDetail.detailsPage.technicalSupport.sourceFrom"), content: info.details.source_from, type: "inline" },
                                    ],
                                }
                            } else {
                                this.topForm = {
                                    title: "",
                                    list: [
                                        { title: this.$c("pages.CaseDetail.detailsPage.askDemo.briefProjectDescription"), content: info.details.description, type: "block" },
                                        { title: this.$c("pages.CaseDetail.detailsPage.Solution.type"), content: info.details.type, type: "inline", isLinFitsrt: true },
                                        { title: this.$c("pages.CaseDetail.detailsPage.technicalSupport.createdBy"), content: info.details.create_by, type: "inline" },
                                        { title: this.$c("pages.CaseDetail.detailsPage.technicalSupport.email"), content: info.details.email, type: "inline", isLinFitsrt: true },
                                        { title: this.$c("pages.CaseDetail.detailsPage.technicalSupport.phoneNumber"), content: info.details.phone.length > 2 ? info.details.phone : "/", type: "inline" },
                                        { title: this.$c("pages.CaseDetail.detailsPage.technicalSupport.country"), content: info.details.country, type: "inline", isLinFitsrt: true },
                                        { title: this.$c("form.form.company_name"), content: info.details.company_name, type: "inline" },
                                        { title: this.$c("pages.CaseDetail.detailsPage.technicalSupport.sourceFrom"), content: info.details.source_from, type: "inline" },
                                    ],
                                }
                            }
                        } else {
                            if (info.details.products_info && info.details.products_info.length > 0) {
                                this.topForm = {
                                    title: "",
                                    list: [
                                        { title: this.$c("pages.CaseDetail.detailsPage.askDemo.briefProjectDescription"), content: info.details.description, type: "block" },
                                        { type: "product", products: info.details.products_info, title: this.$c("pages.CaseDetail.detailsPage.emailUs.product") },
                                        { title: this.$c("pages.CaseDetail.detailsPage.Solution.type"), content: info.details.type, type: "inline", isLinFitsrt: true },
                                        { title: this.$c("pages.CaseDetail.detailsPage.technicalSupport.createdBy"), content: info.details.create_by, type: "inline" },
                                        { title: this.$c("pages.CaseDetail.detailsPage.technicalSupport.email"), content: info.details.email, type: "inline", isLinFitsrt: true },
                                        { title: this.$c("pages.CaseDetail.detailsPage.technicalSupport.phoneNumber"), content: info.details.phone.length > 2 ? info.details.phone : "/", type: "inline" },
                                        { title: this.$c("pages.CaseDetail.detailsPage.technicalSupport.country"), content: info.details.country, type: "inline", isLinFitsrt: true },
                                        { title: this.$c("pages.CaseDetail.detailsPage.technicalSupport.sourceFrom"), content: info.details.source_from, type: "inline" },
                                    ],
                                }
                            } else {
                                this.topForm = {
                                    title: "",
                                    list: [
                                        { title: this.$c("pages.CaseDetail.detailsPage.askDemo.briefProjectDescription"), content: info.details.description, type: "block" },
                                        { title: this.$c("pages.CaseDetail.detailsPage.Solution.type"), content: info.details.type, type: "inline", isLinFitsrt: true },
                                        { title: this.$c("pages.CaseDetail.detailsPage.technicalSupport.createdBy"), content: info.details.create_by, type: "inline" },
                                        { title: this.$c("pages.CaseDetail.detailsPage.technicalSupport.email"), content: info.details.email, type: "inline", isLinFitsrt: true },
                                        { title: this.$c("pages.CaseDetail.detailsPage.technicalSupport.phoneNumber"), content: info.details.phone.length > 2 ? info.details.phone : "/", type: "inline" },
                                        { title: this.$c("pages.CaseDetail.detailsPage.technicalSupport.country"), content: info.details.country, type: "inline", isLinFitsrt: true },
                                        { title: this.$c("pages.CaseDetail.detailsPage.technicalSupport.sourceFrom"), content: info.details.source_from, type: "inline" },
                                    ],
                                }
                            }
                        }
                    } else {
                        this.topForm = {
                            title: "",
                            list: [
                                { title: this.$c("pages.CaseDetail.detailsPage.technicalSupport.briefProjectDescription"), content: info.details.description, type: "block" },
                                { title: this.$c("pages.CaseDetail.detailsPage.technicalSupport.type"), content: info.details.type, type: "inline", isLinFitsrt: true },
                                { title: this.$c("pages.CaseDetail.detailsPage.technicalSupport.createdBy"), content: info.details.create_by, type: "inline" },
                                { title: this.$c("pages.CaseDetail.detailsPage.technicalSupport.email"), content: info.details.email, isLinFitsrt: true, type: "inline" },
                                { title: this.$c("pages.CaseDetail.detailsPage.technicalSupport.phoneNumber"), content: info.details.phone.length > 2 ? info.details.phone : "/", type: "inline" },
                                { title: this.$c("pages.CaseDetail.detailsPage.technicalSupport.country"), content: info.details.country, type: "inline", isLinFitsrt: true },
                                { title: this.$c("pages.CaseDetail.detailsPage.technicalSupport.sourceFrom"), content: info.details.source_from, type: "inline" },
                            ],
                        }
                    }

                    if (this.website === "sg") {
                        this.topForm.list.splice(5, 1)
                    }
                    break
                case 12:
                    const { source, question = [] } = info.details
                    if (source === 18) {
                        // 解决方案
                        this.topForm = {
                            title: "",
                            list: [
                                { title: this.$c("pages.CaseDetail.detailsPage.technicalSupport.briefProjectDescription"), content: info.details.description, type: "block" },
                                { title: this.$c("pages.CaseDetail.detailsPage.technicalSupport.type"), content: info.details.type, type: "inline", isLinFitsrt: true },
                                { title: this.$c("pages.CaseDetail.detailsPage.technicalSupport.createdBy"), content: info.details.create_by, type: "inline" },
                                { title: this.$c("pages.CaseDetail.detailsPage.remoteTest.countryRegion"), content: info.details.country, type: "inline", isLinFitsrt: true },
                                { title: this.$c("pages.CaseDetail.detailsPage.technicalSupport.email"), content: info.details.email, type: "inline" },
                                { title: this.$c("pages.CaseDetail.detailsPage.technicalSupport.phoneNumber"), content: info.details.phone.length > 2 ? info.details.phone : "/", type: "inline", isLinFitsrt: true },
                                { title: this.$c("pages.CaseDetail.detailsPage.askDemo.state"), content: info.details.state, type: "inline" },
                                { title: this.$c("pages.CaseDetail.detailsPage.Solution.jobTitle"), content: info.details.job_title, type: "inline", isLinFitsrt: true },
                                { title: this.$c("pages.CaseDetail.detailsPage.Solution.industry"), content: info.details.industry, type: "inline" },
                            ],
                        }
                        if (question.length > 0) {
                            this.botForm = {
                                title: this.$c("pages.CaseDetail.detailsPage.Solution.design.questionSelect"),
                                list: question.map(({ title, answer }) => {
                                    return {
                                        title,
                                        content: answer,
                                        type: "block",
                                    }
                                }),
                            }
                        }
                    } else {
                        console.log("方案设计")
                        this.topForm = {
                            title: "",
                            list: [
                                { title: this.$c("pages.CaseDetail.detailsPage.Solution.briefProjectDescription"), content: info.details.description, type: "block" },
                                { title: this.$c("pages.CaseDetail.detailsPage.Solution.type"), content: info.details.type, type: "inline", isLinFitsrt: true },
                                { title: this.$c("pages.CaseDetail.detailsPage.Solution.createdBy"), content: info.details.create_by, type: "inline" },
                                { title: this.$c("pages.CaseDetail.detailsPage.Solution.email"), content: info.details.email, isLinFitsrt: true, type: "inline" },
                                { title: this.$c("pages.CaseDetail.detailsPage.Solution.phoneNumber"), content: info.details.phone, type: "inline" },
                                { title: this.$c("pages.CaseDetail.detailsPage.Solution.jobTitle"), content: info.details.job_title, isLinFitsrt: true, type: "inline" },
                                { title: this.$c("pages.CaseDetail.detailsPage.Solution.industry"), content: info.details.industry, type: "inline" },
                                { title: this.$c("pages.CaseDetail.detailsPage.Solution.country"), content: info.details.country, type: "inline", isLinFitsrt: true },
                                // {title:this.$c('pages.CaseDetail.detailsPage.Solution.interestSolution'),content:info.details.interest_solution,type:'inline',href:'####'},
                                { title: this.$c("pages.CaseDetail.detailsPage.Solution.sourceFrom"), content: info.details.source_from, type: "inline" },
                            ],
                        }
                    }
                    break
                case 13:
                    console.log("Demo 远程")
                    const { details } = info
                    this.topForm.title = ""
                    if (details.is_new) {
                        this.topForm.list = [
                            { title: this.$c("pages.CaseDetail.detailsPage.remoteTest.otherRequirements"), content: details.other_requirements, type: "block" },
                            { title: this.$c("pages.CaseDetail.detailsPage.remoteTest.testPurpose"), content: details.test_purpose, type: "block" },
                            { title: this.$c("pages.CaseDetail.detailsPage.remoteTest.testProduct"), content: details.test_product, type: "block" },
                            { title: this.$c("pages.CaseDetail.detailsPage.remoteTest.productID"), content: details.product_id, type: "block" },
                            // { title: 'Equipment Function', content: details.phone, type: "block",lineTitle: 'Equipment Function'},
                            // { title: 'Switch', content: details.job_title, type: "block" },
                            // { title: 'Wireless', content: details.industry, type: "block" },
                            // { title: 'Other', content: details.country, type: "block", },
                            // {title:this.$c('pages.CaseDetail.detailsPage.Solution.interestSolution'),content:details.interest_solution,type:'inline',href:'####'},
                            { title: this.$c("pages.CaseDetail.detailsPage.remoteTest.onlineTechnical"), content: details.online_technical_support, type: "block" },
                        ]
                        const arr = []
                        details.equipment_function?.forEach((item, index) => {
                            if (index === 0) {
                                arr.push({ title: item.type, content: item.list, type: "block", lineTitle: this.$c("pages.CaseDetail.detailsPage.remoteTest.equipmentFunction") })
                            } else {
                                arr.push({ title: item.type, content: item.list, type: "block" })
                            }
                        })
                        this.topForm.list.splice(4, 0, ...arr)
                    } else {
                        this.topForm.list = [
                            { title: this.$c("pages.CaseDetail.detailsPage.remoteTest.briefProjectDescription"), content: info.details.description, type: "block" },
                            { title: this.$c("pages.CaseDetail.detailsPage.remoteTest.type"), content: info.details.type, type: "inline", isLinFitsrt: true },
                            { title: this.$c("pages.CaseDetail.detailsPage.remoteTest.createdBy"), content: info.details.create_by, type: "inline" },
                            { title: this.$c("pages.CaseDetail.detailsPage.remoteTest.email"), content: info.details.email, isLinFitsrt: true, type: "inline" },
                            { title: this.$c("pages.CaseDetail.detailsPage.remoteTest.phoneNumber"), content: info.details.phone, type: "inline" },
                            { title: this.$c("pages.CaseDetail.detailsPage.remoteTest.country"), content: info.details.country, isLinFitsrt: true, type: "inline" },
                            { title: this.$c("pages.CaseDetail.detailsPage.remoteTest.industry"), content: info.details.industry, type: "inline" },
                            { title: this.$c("pages.CaseDetail.detailsPage.remoteTest.company"), content: info.details.company_name, isLinFitsrt: true, type: "inline" },
                            { title: this.$c("pages.CaseDetail.detailsPage.concactSele.companySize"), content: info.details.company_size, type: "inline" },
                            { title: this.$c("pages.CaseDetail.detailsPage.remoteTest.areasOfInterest"), content: info.details.areas_of_interest, isLinFitsrt: true, type: "inline" },
                            { title: this.$c("pages.CaseDetail.detailsPage.remoteTest.model"), content: info.details.model[0].name, type: "inline", href: info.details.model[0].link, clickEvent: this.GaModel },
                            { title: this.$c("pages.CaseDetail.detailsPage.remoteTest.function"), content: info.details.function.join("/"), isLinFitsrt: true, type: "inline" },
                            { title: this.$c("pages.CaseDetail.detailsPage.remoteTest.time"), content: info.details.time, type: "inline" },
                            { title: this.$c("pages.CaseDetail.detailsPage.remoteTest.sourceFrom"), content: info.details.source_from, isLinFitsrt: true, type: "inline" },
                        ]
                    }
                    // 底部用户信息
                    this.botForm = {
                        title: this.$c("pages.CaseDetail.detailsPage.afterServe.caseInformation"),
                        list: [
                            { title: this.$c("pages.CaseDetail.detailsPage.remoteTest.name"), content: details.name, isLinFitsrt: true, type: "inline" },
                            { title: this.$c("pages.CaseDetail.detailsPage.remoteTest.phoneNumber"), content: details.phone, type: "inline" },
                            { title: this.$c("pages.CaseDetail.detailsPage.remoteTest.companyName"), content: details.company_name, isLinFitsrt: true, type: "inline" },
                            { title: this.$c("pages.CaseDetail.detailsPage.remoteTest.businessEmail"), content: details.email, type: "inline" },
                            { title: this.$c("pages.CaseDetail.detailsPage.remoteTest.countryRegion"), content: details.country, isLinFitsrt: true, type: "inline" },
                            { title: this.$c("pages.CaseDetail.detailsPage.remoteTest.jobTitle"), content: details.job_title, type: "inline" },
                            { title: this.$c("pages.CaseDetail.detailsPage.remoteTest.industry"), content: details.industry, isLinFitsrt: true, type: "inline" },
                        ],
                    }

                    break
                case 14:
                    console.log("账户中心case申请")
                    if (info.is_product_type === 1) {
                        // 产品类型
                        this.topForm = {
                            title: "",
                            list: [
                                { title: this.$c("pages.RequestTechSupport.createNewCase.topic"), content: info.details.topic, type: "inline", isLinFitsrt: true },
                                { title: this.$c("pages.CaseDetail.detailsPage.emailUs.briefProjectDescription"), content: info.details.description, type: "block" },
                                { type: "product", title: this.$c("pages.CaseDetail.detailsPage.spartPart.productID"), products: info.details?.apply_products },
                            ],
                        }
                        this.botForm = {
                            title: this.$c("pages.CaseDetail.detailsPage.afterServe.caseInformation"),
                            list: [
                                { title: this.$c("pages.CaseDetail.detailsPage.emailUs.type"), content: info.details.type, type: "inline", isLinFitsrt: true },
                                { title: this.$c("pages.CaseDetail.detailsPage.emailUs.product"), content: info.details.apply_cate, type: "inline" },
                                { title: this.$c("pages.CaseDetail.detailsPage.emailUs.createdBy"), content: info.details.create_by, isLinFitsrt: true, type: "inline" },
                                { title: this.$c("pages.CaseDetail.detailsPage.emailUs.email"), content: info.details.email, type: "inline" },
                                { title: this.$c("pages.CaseDetail.detailsPage.emailUs.phoneNumber"), content: info.details.phone, isLinFitsrt: true, type: "inline" },
                                { title: this.$c("pages.CaseDetail.detailsPage.emailUs.country"), content: info.details.country, type: "inline" },
                                { title: this.$c("pages.CaseDetail.detailsPage.emailUs.sourceFrom"), content: info.details.source_from, type: "inline", isLinFitsrt: true },
                            ],
                        }
                    } else {
                        // 业务类型
                        this.topForm = {
                            title: "",
                            list: [
                                { title: this.$c("pages.CaseDetail.detailsPage.emailUs.briefProjectDescription"), content: info.details.description, type: "block" },
                                { title: this.$c("pages.CaseDetail.detailsPage.emailUs.type"), content: info.details.type, type: "inline", isLinFitsrt: true },
                                { title: this.$c("pages.CaseDetail.detailsPage.emailUs.createdBy"), content: info.details.create_by, type: "inline" },
                                { title: this.$c("pages.CaseDetail.detailsPage.emailUs.email"), content: info.details.email, isLinFitsrt: true, type: "inline" },
                                { title: this.$c("pages.CaseDetail.detailsPage.emailUs.phoneNumber"), content: info.details.phone, type: "inline" },
                                { title: this.$c("pages.CaseDetail.detailsPage.emailUs.country"), content: info.details.country, type: "inline", isLinFitsrt: true },
                                { title: this.$c("pages.CaseDetail.detailsPage.emailUs.sourceFrom"), content: info.details.source_from, type: "inline" },
                            ],
                        }
                    }

                    break
                case 15:
                    console.log("帐期申请")
                    this.topForm = {
                        title: "",
                        list: [
                            { title: this.$c("pages.CaseDetail.detailsPage.application.briefProjectDescription"), content: info.details.description, type: "block" },
                            { title: this.$c("pages.CaseDetail.detailsPage.application.createdBy"), content: info.details.create_by, type: "inline", isLinFitsrt: true },
                            { title: this.$c("pages.CaseDetail.detailsPage.application.company"), content: info.details.company_name, type: "inline" },
                            { title: this.$c("pages.CaseDetail.detailsPage.application.type"), content: info.details.type, type: "inline", isLinFitsrt: true },
                            { title: this.$c("pages.CaseDetail.detailsPage.application.phoneNumber"), content: info.details.phone, type: "inline" },
                            { title: this.$c("pages.CaseDetail.detailsPage.application.email"), content: info.details.email, isLinFitsrt: true, type: "inline" },
                            { title: this.$c("pages.CaseDetail.detailsPage.application.sourceFrom"), content: info.details.source_from, type: "inline" },
                            { title: this.$c("pages.CaseDetail.detailsPage.application.country"), content: info.details.country, type: "inline", isLinFitsrt: true },
                        ],
                    }
                    break
                case 16:
                    console.log("产品询价")
                    this.topForm = {
                        title: "",
                        list: [
                            { title: this.$c("pages.CaseDetail.detailsPage.emailUs.briefProjectDescription"), content: info.details.description, type: "block" },
                            { type: "product", products: info.details.products },
                            { title: this.$c("pages.CaseDetail.detailsPage.emailUs.type"), content: info.details.type, type: "inline", isLinFitsrt: true },
                            { title: this.$c("pages.CaseDetail.detailsPage.emailUs.createdBy"), content: info.details.create_by, type: "inline" },
                            { title: this.$c("pages.CaseDetail.detailsPage.emailUs.email"), content: info.details.email, isLinFitsrt: true, type: "inline" },
                            { title: this.$c("pages.CaseDetail.detailsPage.emailUs.country"), content: info.details.country, type: "inline" },
                            { title: this.$c("pages.CaseDetail.detailsPage.emailUs.phoneNumber"), content: info.details.phone, type: "inline", isLinFitsrt: true },
                            { title: this.$c("pages.CaseDetail.detailsPage.emailUs.sourceFrom"), content: info.details.source_from, type: "inline" },
                        ],
                    }
                    break
                case 17:
                    console.log("锐捷产品询盘")
                    this.topForm = {
                        title: "",
                        list: [
                            { title: this.$c("pages.CaseDetail.detailsPage.networks.briefProjectDescription"), content: info.details.description, type: "block" },
                            { title: this.$c("pages.CaseDetail.detailsPage.networks.type"), content: info.details.type, type: "inline", isLinFitsrt: true },
                            { title: this.$c("pages.CaseDetail.detailsPage.networks.createdBy"), content: info.details.create_by, type: "inline" },
                            { title: this.$c("pages.CaseDetail.detailsPage.networks.email"), content: info.details.email, isLinFitsrt: true, type: "inline" },
                            { title: this.$c("pages.CaseDetail.detailsPage.networks.phoneNumber"), content: info.details.phone, type: "inline" },
                            { title: this.$c("pages.CaseDetail.detailsPage.networks.industry"), content: info.details.industry, type: "inline", isLinFitsrt: true },
                            { title: this.$c("pages.CaseDetail.detailsPage.networks.networkManagement"), content: info.details.network, type: "inline" },
                            { title: this.$c("pages.CaseDetail.detailsPage.networks.backupNeeds"), content: info.details.backup_needs, type: "inline", isLinFitsrt: true },
                            // {title:"Interest Solution",content:'supportticket.solution_1',type:'inline',href:'####'},
                            { title: this.$c("pages.CaseDetail.detailsPage.networks.sourceFrom"), content: info.details.source_from, type: "inline" },
                        ],
                    }
                    break
                case 18:
                case 34:
                    console.log("非质量申请")
                    this.topForm = {
                        title: "",
                        list: [
                            { title: this.$c("pages.CaseDetail.detailsPage.afterServe.briefProjectDescription"), content: info.details.description, type: "block" },
                            { type: "product", hiddeQty: true, products: info.details.products },
                        ],
                    }
                    //case 详情区
                    this.botForm = {
                        title: this.$c("pages.CaseDetail.detailsPage.afterServe.caseInformation"),
                        list: [
                            { title: this.$c("pages.CaseDetail.detailsPage.afterServe.afterSalesReason"), content: info.details.after_sales_reason, type: "inline", isLinFitsrt: true },
                            { title: this.$c("pages.CaseDetail.detailsPage.afterServe.orderNumber"), content: info.details.order_number, type: "inline" },
                            { title: this.$c("pages.CaseDetail.detailsPage.afterServe.timeOfReceipt"), content: info.details.time, isLinFitsrt: true, type: "inline" },
                            { title: this.$c("pages.CaseDetail.detailsPage.afterServe.type"), content: info.details.type, type: "inline" },
                            { title: this.$c("pages.CaseDetail.detailsPage.afterServe.sourceFrom"), content: info.details.source_from, type: "inline", isLinFitsrt: true },
                        ],
                    }
                    break
                case 19:
                    console.log("E-rate")
                    this.topForm = {
                        title: "",
                        list: [
                            { title: this.$c("pages.CaseDetail.detailsPage.e_rate.briefProjectDescription"), content: info.details.description, type: "block" },
                            { title: this.$c("pages.CaseDetail.detailsPage.e_rate.type"), content: info.details.type, type: "inline", isLinFitsrt: true },
                            { title: this.$c("pages.CaseDetail.detailsPage.e_rate.createdBy"), content: info.details.create_by, type: "inline" },
                            { title: this.$c("pages.CaseDetail.detailsPage.e_rate.email"), content: info.details.email, isLinFitsrt: true, type: "inline" },
                            { title: this.$c("pages.CaseDetail.detailsPage.e_rate.phoneNumber"), content: info.details.phone, type: "inline" },
                            { title: this.$c("pages.CaseDetail.detailsPage.e_rate.country"), content: info.details.country, type: "inline", isLinFitsrt: true },
                            { title: this.$c("pages.CaseDetail.detailsPage.e_rate.sourceFrom"), content: info.details.source_from, type: "inline" },
                        ],
                    }
                    break
                case 29:
                    console.log("质量问题")
                    // 4种类型  // product_system_code == ‘EN’/LT/OM/IW
                    let getPductsStructure = (product) => {
                        let result = []
                        let titleArr = []
                        let link = []
                        const describe = product?.describe || []
                        switch (product.product_system_code) {
                            case "EN": //企业网
                                link = this.$c("pages.CaseDetail.detailsPage.afterServe_quelit.link2")
                                titleArr = Object.values(link)
                                result = [
                                    { title: this.$c("pages.CaseDetail.detailsPage.afterServe_quelit.numberOfFaults"), content: product.faults_num, type: "inline", isLinFitsrt: true },
                                    { title: this.$c("pages.CaseDetail.detailsPage.afterServe_quelit.SN"), content: product.sn_number, type: "inline" },
                                    { title: this.$c("pages.CaseDetail.detailsPage.afterServe_quelit.deviceVersion"), content: product.version, type: "inline", isLinFitsrt: true },
                                    { title: this.$c("pages.CaseDetail.detailsPage.afterServe_quelit.faultTime"), content: product.fault_time, type: "inline" },
                                    ...describe.map((item, index) => {
                                        return {
                                            title: titleArr[index],
                                            content: item,
                                            type: "block",
                                        }
                                    }),
                                ]
                                break
                            case "LT": //光传输
                                result = [
                                    { title: this.$c("pages.CaseDetail.detailsPage.afterServe_quelit.numberOfFaults"), content: product.faults_num, type: "inline", isLinFitsrt: true },
                                    { title: this.$c("pages.CaseDetail.detailsPage.afterServe_quelit.SN"), content: product.sn_number, type: "inline" },
                                    { title: this.$c("pages.CaseDetail.detailsPage.afterServe_quelit.deviceVersion"), content: product.version, type: "inline", isLinFitsrt: true },
                                    { title: this.$c("pages.CaseDetail.detailsPage.afterServe_quelit.faultTime"), content: product.fault_time, type: "inline" },
                                    { title: this.$c("pages.CaseDetail.detailsPage.afterServe_quelit.link2.text1"), content: product.describe[0], type: "block" },
                                ]
                                break
                            case "OM": //光模块
                                link = this.$c("pages.CaseDetail.detailsPage.afterServe_quelit.link")
                                titleArr = Object.values(link)
                                const linkEndMap = {
                                    1: this.$c("pages.RequestTechSupport.siteA"),
                                    2: this.$c("pages.RequestTechSupport.siteB"),
                                }
                                const linkEnd = (product.link_end || [])?.map((i) => {
                                    const { device_link, equipment_model, equipment_version } = i
                                    return `${linkEndMap[device_link]}, ${equipment_model},${equipment_version}`
                                })
                                result = [
                                    { title: this.$c("pages.CaseDetail.detailsPage.afterServe_quelit.numberOfFaults"), content: product.faults_num, type: "inline", isLinFitsrt: true },
                                    { title: this.$c("pages.CaseDetail.detailsPage.afterServe_quelit.SN"), content: product.sn_number, type: "inline" },
                                    { title: this.$c("pages.CaseDetail.detailsPage.afterServe_quelit.linkDistance"), content: product.link_distance, type: "inline", isLinFitsrt: true },
                                    { title: this.$c("pages.CaseDetail.detailsPage.afterServe_quelit.faultTime"), content: product.fault_time, type: "inline" },
                                    { title: this.$c("pages.CaseDetail.detailsPage.afterServe_quelit.linkEnd"), content: linkEnd.join(" / "), type: "block" },
                                    ...describe.map((item, index) => {
                                        return {
                                            title: titleArr[index],
                                            content: item,
                                            type: "block",
                                        }
                                    }),
                                ]
                                break
                            case "IW": //综合布线
                                link = this.$c("pages.CaseDetail.detailsPage.afterServe_quelit.link3")
                                titleArr = Object.values(link)
                                result = [
                                    { title: this.$c("pages.CaseDetail.detailsPage.afterServe_quelit.numberOfFaults"), content: product.faults_num, type: "inline", isLinFitsrt: true },
                                    { title: this.$c("pages.CaseDetail.detailsPage.afterServe_quelit.SN"), content: product.sn_number, type: "inline" },
                                    ...describe.map((item, index) => {
                                        return {
                                            title: titleArr[index],
                                            content: item,
                                            type: "block",
                                        }
                                    }),
                                ]
                                break
                        }
                        result.unshift({ type: "product", hiddeQty: true, products: [product] })
                        return result
                    }
                    this.topForm = {
                        title: "",
                        list: [{ title: this.$c("pages.CaseDetail.detailsPage.afterServe_quelit.briefProjectDescription"), content: info.details.description, type: "block" }].concat(
                            info.details?.products?.map(getPductsStructure)?.flat()
                        ),
                    }
                    //case infomation
                    this.botForm = {
                        title: this.$c("pages.CaseDetail.detailsPage.afterServe_quelit.caseInformation"),
                        list: [
                            { title: this.$c("pages.CaseDetail.detailsPage.afterServe_quelit.afterSalesReason"), content: info.details.after_sales_reason, isLinFitsrt: true, type: "inline" },
                            { title: this.$c("pages.CaseDetail.detailsPage.afterServe_quelit.orderNumber"), content: info.details.order_number, type: "inline" },
                            { title: this.$c("pages.CaseDetail.detailsPage.afterServe_quelit.timeOfReceipt"), content: info.details.time_of_receipt, isLinFitsrt: true, type: "inline" },
                            { title: this.$c("pages.CaseDetail.detailsPage.afterServe_quelit.type"), content: info.details.type, type: "inline" },
                            { title: this.$c("pages.CaseDetail.detailsPage.afterServe_quelit.sourceFrom"), content: info.details.source_from, isLinFitsrt: true, type: "inline" },
                        ],
                    }
                    break
                case 31:
                case 66:
                case 76:
                case 63:
                    // console.log("联系销售")
                    // this.topForm = {
                    //     title: "",
                    //     list: [
                    //         { title: this.$c("pages.CaseDetail.detailsPage.concactSele.briefProjectDescription"), content: info.details.description, type: "block" },
                    //         { title: this.$c("pages.CaseDetail.detailsPage.concactSele.type"), content: info.details.type, type: "inline", isLinFitsrt: true },
                    //         { title: this.$c("pages.CaseDetail.detailsPage.concactSele.createdBy"), content: info.details.create_by, type: "inline" },
                    //         { title: this.$c("pages.CaseDetail.detailsPage.concactSele.email"), content: info.details.email, isLinFitsrt: true, type: "inline" },
                    //         { title: this.$c("pages.CaseDetail.detailsPage.concactSele.phoneNumber"), content: info.details.phone.length > 2 ? info.details.phone : "/", type: "inline" },
                    //         { title: this.$c("pages.CaseDetail.detailsPage.concactSele.jobTitle"), content: info.details.job_title, isLinFitsrt: true, type: "inline" },
                    //         { title: this.$c("pages.CaseDetail.detailsPage.concactSele.industry"), content: info.details.industry, type: "inline" },
                    //         { title: this.$c("pages.CaseDetail.detailsPage.concactSele.country"), content: info.details.country, isLinFitsrt: true, type: "inline" },
                    //         { title: this.$c("pages.CaseDetail.detailsPage.concactSele.companySize"), content: info.details.company_size, type: "inline" },
                    //         { title: this.$c("pages.CaseDetail.detailsPage.concactSele.companyName"), content: info.details.company_name, isLinFitsrt: true, type: "inline" },
                    //         { title: this.$c("pages.CaseDetail.detailsPage.concactSele.sourceFrom"), content: info.details.source_from, type: "inline" },
                    //     ],
                    // }
                    // if (this.website === "sg") {
                    //     this.topForm.list.splice(5, 5)
                    // }
                    // console.log("6666联系销售", this.topForm)
                    console.log("问题咨询")
                    let totalList = [
                        { title: this.$c("pages.CaseDetail.detailsPage.emailUs.briefProjectDescription"), content: info.details.description, type: "block" },
                        { type: "product", products: info.details.products_info, title: this.$c("pages.CaseDetail.detailsPage.emailUs.consulted") },
                        { title: this.$c("pages.CaseDetail.detailsPage.emailUs.direction"), content: info.details.consultation_direction, type: "block" },
                        { title: this.$c("pages.CaseDetail.detailsPage.emailUs.type"), content: info.details.type, type: "inline", isLinFitsrt: true },
                        { title: this.$c("pages.CaseDetail.detailsPage.emailUs.createdBy"), content: info.details.create_by, type: "inline" },
                        { title: this.$c("pages.CaseDetail.detailsPage.emailUs.email"), content: info.details.email, isLinFitsrt: true, type: "inline" },
                        { title: this.$c("pages.CaseDetail.detailsPage.concactSele.companyName"), content: info.details.company_name, type: "inline" },
                        { title: this.$c("pages.CaseDetail.detailsPage.emailUs.sourceFrom"), content: info.details.source_from, isLinFitsrt: true, type: "inline" },
                        { title: this.$c("pages.CaseDetail.detailsPage.emailUs.websiteLink"), content: info.details.website_link, isLinFitsrt: true, type: "inline" },
                    ]
                    const { business_category = "", application_scenario = "" } = info.details
                    if (business_category) {
                        totalList.push({
                            title: this.$c("pages.CaseDetail.detailsPage.Solution.businessCategory"),
                            content: business_category,
                            type: "inline",
                        })
                    }
                    if (application_scenario) {
                        totalList.push({
                            title: this.$c("pages.CaseDetail.detailsPage.Solution.applicationScenario"),
                            content: application_scenario,
                            type: "inline",
                            isLinFitsrt: true,
                        })
                    }
                    if (this.isCn || this.isCnTr) {
                        totalList.splice(6, 0, { title: this.$c("pages.CaseDetail.detailsPage.emailUs.phoneNumber"), content: info.details.phone, type: "inline" })
                    }
                    //去除空数据
                    totalList = totalList.filter((item) => {
                        if (item.type === "product") {
                            if (item.products?.length) {
                                return true
                            } else {
                                return false
                            }
                        } else {
                            return item.content
                        }
                    })
                    this.topForm = {
                        title: "",
                        list: totalList,
                    }
                    break
                case 32:
                    console.log("视频预约")
                    this.topForm = {
                        title: "",
                        list: [
                            { title: this.$c("pages.CaseDetail.detailsPage.videoMeeting.briefProjectDescription"), content: info.details.description, type: "block" },
                            { title: this.$c("pages.CaseDetail.detailsPage.videoMeeting.email"), content: info.details.email, isLinFitsrt: true, type: "inline" },
                            { title: this.$c("pages.CaseDetail.detailsPage.videoMeeting.phoneNumber"), content: info.details.phone, type: "inline" },
                            { title: this.$c("pages.CaseDetail.detailsPage.videoMeeting.sourceFrom"), content: info.details.source_from, type: "inline", isLinFitsrt: true },
                        ],
                    }
                    this.botForm = {
                        title: this.$c("pages.CaseDetail.detailsPage.videoMeeting.appointmentDetails"),
                        list: [
                            { title: this.$c("pages.CaseDetail.detailsPage.videoMeeting.appointmentTime"), content: info.details.time, type: "inline", isLinFitsrt: true },
                            { title: this.$c("pages.CaseDetail.detailsPage.videoMeeting.company"), content: info.details.company, type: "inline" },
                            { title: this.$c("pages.CaseDetail.detailsPage.videoMeeting.address"), content: info.details.address, isLinFitsrt: true, type: "inline" },
                            { title: this.$c("pages.CaseDetail.detailsPage.videoMeeting.meetingTheme"), content: info.details.meeting_theme, type: "inline" },
                            { title: this.$c("pages.CaseDetail.detailsPage.videoMeeting.meetingContent"), content: info.details.meeting_content, type: "inline", isLinFitsrt: true },
                            { title: this.$c("pages.CaseDetail.detailsPage.videoMeeting.createdBy"), content: info.details.create_by, type: "inline" },
                        ],
                    }
                    break
                case 33:
                    console.log("备件申请")
                    //备件申请 产品列表
                    this.topForm = {
                        title: "",
                        list: [
                            { title: this.$c("pages.CaseDetail.detailsPage.spartPart.briefProjectDescription"), content: info.details?.description, type: "block" },
                            { type: "product", title: this.$c("pages.CaseDetail.detailsPage.spartPart.productID"), products: info.details?.products },
                        ],
                    }
                    //备件申请详情
                    this.botForm = {
                        title: this.$c("pages.CaseDetail.detailsPage.afterServe.caseInformation"),
                        list: [
                            { title: this.$c("pages.CaseDetail.detailsPage.spartPart.afterSale"), content: info.details?.after_sales_reason, type: "inline", isLinFitsrt: true },
                            { title: this.$c("pages.CaseDetail.detailsPage.spartPart.oderNumber"), content: info.details?.order_number, type: "inline" },
                            { title: this.$c("pages.CaseDetail.detailsPage.spartPart.address"), content: this.getAddressString(info.details.address), type: "block" },
                            { title: this.$c("pages.CaseDetail.detailsPage.spartPart.type"), content: info.details?.type, isLinFitsrt: true, type: "inline" },
                            { title: this.$c("pages.CaseDetail.detailsPage.spartPart.sourceFrom"), content: info.details?.source_from, type: "inline" },
                        ],
                    }
                    break
                // case 34:
                //     console.log('非质量申请');
                //     break
                case 35:
                    console.log("兼容")
                    var { test_requirements, test_product = {}, specific_want, test_type } = info.details
                    const testProductList = Object.entries(test_product)
                        .map((i, index) => {
                            const [key, value] = i
                            const maxDeviceModel = value === "other" ? this.$c("single.transceiverTestForm.otherTxt") : value
                            let brandData = { title: this.$c("pages.CaseDetail.detailsPage.compatible.brand"), content: key, type: "inline", isLinFitsrt: true }
                            const deviceModelData = { title: this.$c("pages.CaseDetail.detailsPage.compatible.deviceModel"), content: maxDeviceModel, type: "block", isGroupItem: true }
                            if (index === 0) {
                                brandData.classifyTitle = this.$c("pages.CaseDetail.detailsPage.compatible.testProduct")
                            } else {
                                brandData.isGroupItem = true
                            }
                            return [brandData, deviceModelData]
                        })
                        .flat()
                    const testTypeLabel = test_type
                        .map((i) => {
                            return this.$c("single.transceiverTestForm.form")[0].options[i - 1].txt
                        })
                        .join(this.block)
                    this.topForm = {
                        title: "",
                        list: [
                            { title: this.$c("pages.CaseDetail.detailsPage.compatible.testRequirements"), content: test_requirements, type: "block" },
                            {
                                title: this.$c("pages.CaseDetail.detailsPage.compatible.testType"),
                                content: testTypeLabel,
                                type: "inline",
                                classifyTitle: this.$c("pages.CaseDetail.detailsPage.compatible.brandAndProduct"),
                                isLinFitsrt: true,
                            },
                            { title: this.$c("pages.CaseDetail.detailsPage.compatible.specialTransceiver"), content: specific_want, type: "inline" },
                            ...testProductList,
                        ],
                    }

                    var { name, phone, company_name, email, company_size, country, industry } = info.details
                    this.botForm = {
                        title: this.$c("pages.CaseDetail.detailsPage.common.caseInformation"),
                        list: [
                            { title: this.$c("pages.CaseDetail.detailsPage.common.name"), content: name, type: "inline", isLinFitsrt: true },
                            { title: this.$c("pages.CaseDetail.detailsPage.common.phoneNumber"), content: phone, type: "inline" },
                            { title: this.$c("pages.CaseDetail.detailsPage.common.companyName"), content: company_name, type: "inline", isLinFitsrt: true },
                            { title: this.$c("pages.CaseDetail.detailsPage.common.email"), content: email, type: "inline" },
                            { title: this.$c("pages.CaseDetail.detailsPage.common.companySize"), content: company_size, type: "inline", isLinFitsrt: true },
                            { title: this.$c("pages.CaseDetail.detailsPage.common.countryRegion"), content: country, type: "inline" },
                            { title: this.$c("pages.CaseDetail.detailsPage.common.industry"), content: industry, type: "inline", isLinFitsrt: true },
                        ],
                    }
                    break
                case 36:
                    console.log("认证")

                    var { au_type, au_type_other, purpose_type, purpose_type_other, holder_require_type, order_number, product_number, content, would_time } = info.details
                    const authFormRequest = `${au_type.join(this.block)}${au_type_other && au_type && au_type?.length > 0 ? this.block : ""}${au_type_other}`
                    const certification = `${purpose_type}${purpose_type_other && purpose_type ? "," : ""}${purpose_type_other}`
                    this.topForm = {
                        title: "",
                        list: [
                            { title: this.$c("pages.CaseDetail.detailsPage.authForm.description"), content, type: "block" },
                            {
                                title: this.$c("pages.CaseDetail.detailsPage.authForm.request"),
                                content: authFormRequest,
                                type: "inline",
                                classifyTitle: this.$c("pages.CaseDetail.detailsPage.authForm.reInfo"),
                                isLinFitsrt: true,
                            },
                            { title: this.$c("pages.CaseDetail.detailsPage.authForm.certification"), content: certification, type: "inline" },
                            {
                                title: this.$c("pages.CaseDetail.detailsPage.authForm.orderNumber"),
                                content: order_number,
                                type: "inline",
                                classifyTitle: this.$c("pages.CaseDetail.detailsPage.authForm.proInfo"),
                                isLinFitsrt: true,
                            },
                            { title: this.$c("pages.CaseDetail.detailsPage.authForm.partNumber"), content: product_number, type: "block", isGroupItem: true },
                            { title: this.$c("pages.CaseDetail.detailsPage.authForm.holder"), content: holder_require_type, type: "inline", isGroupItem: true, isLinFitsrt: true },
                            { title: this.$c("pages.CaseDetail.detailsPage.authForm.wouldTime"), content: would_time, type: "inline", isGroupItem: true },
                        ],
                    }

                    var { name, phone, company_name, email, country, industry, job_title } = info.details
                    this.botForm = {
                        title: this.$c("pages.CaseDetail.detailsPage.common.caseInformation"),
                        list: [
                            { title: this.$c("pages.CaseDetail.detailsPage.common.name"), content: name, type: "inline", isLinFitsrt: true },
                            { title: this.$c("pages.CaseDetail.detailsPage.common.phoneNumber"), content: phone, type: "inline" },
                            { title: this.$c("pages.CaseDetail.detailsPage.common.companyName"), content: company_name, type: "inline", isLinFitsrt: true },
                            { title: this.$c("pages.CaseDetail.detailsPage.common.email"), content: email, type: "inline" },
                            { title: this.$c("pages.CaseDetail.detailsPage.common.countryRegion"), content: country, type: "inline", isLinFitsrt: true },
                            { title: this.$c("pages.CaseDetail.detailsPage.common.jobTitle"), content: job_title, type: "inline" },
                            { title: this.$c("pages.CaseDetail.detailsPage.common.industry"), content: industry, type: "inline", isLinFitsrt: true },
                        ],
                    }
                    break
                case 37:
                    console.log("解决方案")
                    switch (info.solution_type) {
                        case 1:
                            console.log("1.OTN Solution（光网络）")
                            var {
                                specific_verification_requirements,
                                fiber_type,
                                number_of_fiber,
                                other_requirements,
                                spans_link,
                                network_capacity_upgrade_plan,
                                other_customized_demands,
                                additional_requirements,
                                channel_requirements_info,
                                use_compatible_transceivers_allowed,
                                channel_required_now_num,
                                channel_required_future_num,
                            } = info.details
                            let channelrequirementsinfo = []
                            channel_requirements_info.forEach((i) => {
                                const { channel, protocol, vendor, model } = i
                                channelrequirementsinfo.push(
                                    ...[
                                        {
                                            title: this.$c("single.SolutionDesign.OTN_Solution.value[9]"),
                                            content: channel,
                                            classifyTitle: this.$c("single.SolutionDesign.OTN_Solution.option[5]"),
                                            type: "inline",
                                            isLinFitsrt: true,
                                        },
                                        { title: this.$c("single.SolutionDesign.OTN_Solution.option[5]"), content: channel, type: "inline", isLinFitsrt: true },
                                        { title: this.$c("single.SolutionDesign.OTN_Solution.value[10]"), content: protocol, type: "inline" },
                                        { title: this.$c("single.SolutionDesign.OTN_Solution.value[11]"), content: vendor, type: "inline", isLinFitsrt: true },
                                        { title: this.$c("single.SolutionDesign.OTN_Solution.value[12]"), content: model, type: "inline" },
                                    ]
                                )
                            })
                            let spansLink = []
                            spans_link.forEach((i) => {
                                const { initial_point, termination_point, bussiness_capacity, distance, loss } = i
                                spansLink.push(
                                    ...[
                                        {
                                            title: this.$c("pages.CaseDetail.detailsPage.OTNSolution.initialPoint"),
                                            content: initial_point,
                                            classifyTitle: this.$c("pages.CaseDetail.detailsPage.OTNSolution.spansLink"),
                                            type: "inline",
                                            isLinFitsrt: true,
                                        },
                                        { title: this.$c("pages.CaseDetail.detailsPage.OTNSolution.TermPoint"), content: termination_point, type: "inline", isGroupItem: true },
                                        { title: this.$c("pages.CaseDetail.detailsPage.OTNSolution.busCap"), content: bussiness_capacity, type: "inline", isLinFitsrt: true, isGroupItem: true },
                                        { title: this.$c("pages.CaseDetail.detailsPage.OTNSolution.distance"), content: distance, type: "inline", isGroupItem: true },
                                        { title: this.$c("pages.CaseDetail.detailsPage.OTNSolution.loss"), content: loss, type: "block", isGroupItem: true },
                                    ]
                                )
                            })
                            this.topForm = {
                                title: "",
                                list: [
                                    { title: this.$c("pages.CaseDetail.detailsPage.common.specRequire"), content: specific_verification_requirements, type: "block" },
                                    { title: this.$c("pages.CaseDetail.detailsPage.OTNSolution.fiberType"), content: fiber_type, type: "inline", isLinFitsrt: true },
                                    { title: this.$c("pages.CaseDetail.detailsPage.OTNSolution.NumberOfFiber"), content: number_of_fiber, type: "inline" },
                                    { title: this.$c("pages.CaseDetail.detailsPage.common.othRequire"), content: other_requirements, type: "block" },
                                    ...spansLink,
                                    {
                                        title: this.$c("pages.CaseDetail.detailsPage.OTNSolution.channelsNow"),
                                        content: channel_required_now_num,
                                        classifyTitle: this.$c("pages.CaseDetail.detailsPage.OTNSolution.channelRequirements"),
                                        type: "inline",
                                        isLinFitsrt: true,
                                    },
                                    { title: this.$c("pages.CaseDetail.detailsPage.OTNSolution.channelsFuture"), content: channel_required_future_num, type: "inline", isGroupItem: true },
                                    ...channelrequirementsinfo,
                                    {
                                        title: this.$c("pages.CaseDetail.detailsPage.OTNSolution.allowedCompatibleTransceivers"),
                                        content: use_compatible_transceivers_allowed === 1 ? this.$c("single.SolutionDesign.Network_Solution.value[35]") : this.$c("single.SolutionDesign.Network_Solution.value[36]"),
                                        type: "block",
                                    },
                                    { title: this.$c("pages.CaseDetail.detailsPage.OTNSolution.otherDemands"), content: other_customized_demands, type: "inline", isLinFitsrt: true },
                                    { title: this.$c("pages.CaseDetail.detailsPage.OTNSolution.addRequire"), content: additional_requirements, type: "inline" },
                                ],
                            }
                            break
                        case 4:
                            console.log("2.Date center solution（数据中心）")
                            var {
                                specific_verification_requirements,
                                scenario,
                                access_bandwidth,
                                functional_requirements,
                                server_requirements,
                                airflow_direction,
                                transmission_medium,
                                number_of_servers,
                                other_requirements,
                            } = info.details
                            this.topForm = {
                                title: "",
                                list: [
                                    { title: this.$c("pages.CaseDetail.detailsPage.common.specRequire"), content: specific_verification_requirements, type: "block" },
                                    {
                                        title: this.$c("pages.CaseDetail.detailsPage.dateCenterSolution.scenario"),
                                        content: scenario,
                                        classifyTitle: this.$c("pages.CaseDetail.detailsPage.dateCenterSolution.interRequire"),
                                        type: "inline",
                                        isLinFitsrt: true,
                                    },
                                    { title: this.$c("pages.CaseDetail.detailsPage.dateCenterSolution.accessBandwidth"), content: access_bandwidth, type: "inline", isGroupItem: true },
                                    { title: this.$c("pages.CaseDetail.detailsPage.dateCenterSolution.funcRequire"), content: functional_requirements, type: "block", isGroupItem: true },
                                    { title: this.$c("pages.CaseDetail.detailsPage.dateCenterSolution.serRequire"), content: server_requirements, type: "block", isGroupItem: true },
                                    { title: this.$c("pages.CaseDetail.detailsPage.dateCenterSolution.airDirection"), content: airflow_direction, type: "inline", isLinFitsrt: true },
                                    { title: this.$c("pages.CaseDetail.detailsPage.common.transMedium"), content: transmission_medium, type: "inline" },
                                    { title: this.$c("pages.CaseDetail.detailsPage.dateCenterSolution.numServers"), content: number_of_servers, type: "inline", isLinFitsrt: true },
                                    { title: this.$c("pages.CaseDetail.detailsPage.common.othRequire"), content: other_requirements, type: "inline" },
                                ],
                            }
                            break
                        case 2:
                            console.log("3.breakCampus Network solution-wired（园区有线网）")
                            var {
                                specific_verification_requirements,
                                terminal_type,
                                number_of_terminal,
                                airflow_direction,
                                transmission_medium,
                                transmission_rate,
                                quantity,
                                model_number,
                                function: functionLabel,
                                other,
                            } = info.details
                            this.topForm = {
                                title: "",
                                list: [
                                    { title: this.$c("pages.CaseDetail.detailsPage.common.specRequire"), content: specific_verification_requirements, type: "block" },
                                    { title: this.$c("pages.CaseDetail.detailsPage.bkNetworkSolution.terType"), content: terminal_type, type: "inline", isLinFitsrt: true },
                                    { title: this.$c("pages.CaseDetail.detailsPage.bkNetworkSolution.numOfTerminal"), content: number_of_terminal, type: "inline" },
                                    {
                                        title: this.$c("pages.CaseDetail.detailsPage.bkNetworkSolution.airDirection"),
                                        content: airflow_direction,
                                        type: "inline",
                                        classifyTitle: this.$c("pages.CaseDetail.detailsPage.common.specRequire"),
                                        isLinFitsrt: true,
                                    },
                                    { title: this.$c("pages.CaseDetail.detailsPage.common.transMedium"), content: transmission_medium, type: "inline", isGroupItem: true },
                                    { title: this.$c("pages.CaseDetail.detailsPage.bkNetworkSolution.transRate"), content: transmission_rate, type: "inline", isGroupItem: true, isLinFitsrt: true },
                                    { title: this.$c("pages.CaseDetail.detailsPage.bkNetworkSolution.quantity"), content: quantity, type: "inline", isGroupItem: true },
                                    { title: this.$c("pages.CaseDetail.detailsPage.bkNetworkSolution.modelNumber"), content: model_number, type: "inline", isGroupItem: true, isLinFitsrt: true },

                                    {
                                        title: this.$c("pages.CaseDetail.detailsPage.bkNetworkSolution.function"),
                                        content: functionLabel,
                                        type: "inline",
                                        classifyTitle: this.$c("pages.CaseDetail.detailsPage.bkNetworkSolution.reqOfSoftWare"),
                                        isLinFitsrt: true,
                                    },
                                    { title: this.$c("pages.CaseDetail.detailsPage.bkNetworkSolution.other"), content: other, type: "inline", isGroupItem: true },
                                ],
                            }
                            break
                        case 3:
                            console.log("4.Campus network solution-wireless（园区无线网）")
                            var {
                                specific_verification_requirements,
                                application_environment,
                                coverage_aera,
                                other_requirements,
                                coverage_area_plan,
                                number_of_floors,
                                number_of_rooms,
                                client_devices,
                                wall_thickness,
                                height_of_ceilings,
                                device_applications,
                                radio_protocol_type,
                                spatial_streams,
                                other_information,
                                max_throughput,
                                dual_tri_bands,
                                water_protection_ratings,
                                installation,
                                install_other_requirements,
                                manage_method,
                                power_supply,
                                manage_method_other,
                                power_supply_other,
                            } = info.details
                            this.topForm = {
                                title: "",
                                list: [
                                    { title: this.$c("pages.CaseDetail.detailsPage.common.specRequire"), content: specific_verification_requirements, type: "block" },
                                    {
                                        title: this.$c("pages.CaseDetail.detailsPage.camNetWorkSolution.appEnvironment"),
                                        content: application_environment,
                                        type: "inline",
                                        classifyTitle: this.$c("pages.CaseDetail.detailsPage.camNetWorkSolution.appScenario"),
                                        isLinFitsrt: true,
                                    },

                                    { title: this.$c("pages.CaseDetail.detailsPage.camNetWorkSolution.covArea"), content: coverage_aera, type: "inline", isGroupItem: true },
                                    { title: this.$c("pages.CaseDetail.detailsPage.common.othRequire"), content: other_requirements, type: "block", isGroupItem: true },

                                    {
                                        title: this.$c("pages.CaseDetail.detailsPage.camNetWorkSolution.covArea"),
                                        content: coverage_area_plan,
                                        type: "inline",
                                        classifyTitle: this.$c("pages.CaseDetail.detailsPage.camNetWorkSolution.covAreaPlan"),
                                        isLinFitsrt: true,
                                    },
                                    { title: this.$c("pages.CaseDetail.detailsPage.camNetWorkSolution.numFloors"), content: number_of_floors, type: "inline", isGroupItem: true },
                                    { title: this.$c("pages.CaseDetail.detailsPage.camNetWorkSolution.numRooms"), content: number_of_rooms, type: "inline", isGroupItem: true, isLinFitsrt: true },
                                    { title: this.$c("pages.CaseDetail.detailsPage.camNetWorkSolution.cliRoom"), content: client_devices, type: "inline", isGroupItem: true },
                                    { title: this.$c("pages.CaseDetail.detailsPage.camNetWorkSolution.walRooms"), content: wall_thickness, type: "inline", isGroupItem: true, isLinFitsrt: true },
                                    { title: this.$c("pages.CaseDetail.detailsPage.camNetWorkSolution.heiCeillings"), content: height_of_ceilings, type: "inline", isGroupItem: true },
                                    { title: this.$c("pages.CaseDetail.detailsPage.camNetWorkSolution.devApplications"), content: device_applications, type: "inline", isGroupItem: true, isLinFitsrt: true },
                                    { title: this.$c("pages.CaseDetail.detailsPage.camNetWorkSolution.radType"), content: radio_protocol_type, type: "inline", isGroupItem: true },
                                    { title: this.$c("pages.CaseDetail.detailsPage.camNetWorkSolution.spaStreams"), content: spatial_streams, type: "inline", isGroupItem: true, isLinFitsrt: true },
                                    { title: this.$c("pages.CaseDetail.detailsPage.camNetWorkSolution.othInfo"), content: other_information, type: "block", isGroupItem: true },

                                    {
                                        title: this.$c("pages.CaseDetail.detailsPage.camNetWorkSolution.maxThroughput"),
                                        content: max_throughput,
                                        type: "inline",
                                        classifyTitle: this.$c("pages.CaseDetail.detailsPage.camNetWorkSolution.qeqAccess"),
                                        isLinFitsrt: true,
                                    },
                                    { title: this.$c("pages.CaseDetail.detailsPage.camNetWorkSolution.duaBands"), content: dual_tri_bands, type: "inline", isGroupItem: true },
                                    { title: this.$c("pages.CaseDetail.detailsPage.camNetWorkSolution.warRating"), content: water_protection_ratings, type: "inline", isGroupItem: true, isLinFitsrt: true },
                                    { title: this.$c("pages.CaseDetail.detailsPage.camNetWorkSolution.installation"), content: installation, type: "inline", isGroupItem: true },
                                    { title: this.$c("pages.CaseDetail.detailsPage.common.othRequire"), content: install_other_requirements, type: "block", isGroupItem: true },

                                    { title: this.$c("pages.CaseDetail.detailsPage.camNetWorkSolution.manMethod"), content: manage_method, type: "inline", isLinFitsrt: true },
                                    { title: this.$c("pages.CaseDetail.detailsPage.camNetWorkSolution.powSupply"), content: power_supply, type: "inline" },
                                    { title: this.$c("pages.CaseDetail.detailsPage.common.othRequire"), content: manage_method_other, type: "block" },
                                    { title: this.$c("pages.CaseDetail.detailsPage.common.othRequire"), content: power_supply_other, type: "block" },
                                ],
                            }
                            break
                        case 5:
                            console.log("5.Enterprise intelligence solutions/surveillance netword（企业智能-安防监控）")
                            var {
                                specific_verification_requirements,
                                monitoring_area,
                                monitoring_environment,
                                monitoring_square,
                                other_environment_description,
                                surveillance_radius,
                                surveillance_ratio,
                                surveillance_pixel,
                                power_supply,
                                coding_format,
                                camera_appearance_other,
                                surve_camera_type,
                                functional_devices,
                                video_period,
                                camera_type_other,
                                need_a_nvr,
                                describe,
                            } = info.details
                            this.topForm = {
                                title: "",
                                list: [
                                    { title: this.$c("pages.CaseDetail.detailsPage.common.specRequire"), content: specific_verification_requirements, type: "block" },
                                    {
                                        title: this.$c("pages.CaseDetail.detailsPage.entIntSolution.monArea"),
                                        content: monitoring_area,
                                        type: "inline",
                                        classifyTitle: this.$c("pages.CaseDetail.detailsPage.entIntSolution.environment"),
                                        isLinFitsrt: true,
                                    },
                                    { title: this.$c("pages.CaseDetail.detailsPage.entIntSolution.monEnvironment"), content: monitoring_environment, type: "inline", isGroupItem: true },
                                    { title: this.$c("pages.CaseDetail.detailsPage.entIntSolution.monSquare"), content: monitoring_square, type: "inline", isGroupItem: true, isLinFitsrt: true },
                                    { title: this.$c("pages.CaseDetail.detailsPage.common.othEnvDescription"), content: other_environment_description, type: "block", isGroupItem: true },

                                    {
                                        title: this.$c("pages.CaseDetail.detailsPage.entIntSolution.surRadius"),
                                        content: surveillance_radius,
                                        type: "inline",
                                        classifyTitle: this.$c("pages.CaseDetail.detailsPage.entIntSolution.camAppearance"),
                                        isLinFitsrt: true,
                                    },
                                    { title: this.$c("pages.CaseDetail.detailsPage.entIntSolution.surRatio"), content: surveillance_ratio, type: "inline", isGroupItem: true },
                                    { title: this.$c("pages.CaseDetail.detailsPage.entIntSolution.surPixel"), content: surveillance_pixel, type: "inline", isGroupItem: true, isLinFitsrt: true },
                                    { title: this.$c("pages.CaseDetail.detailsPage.entIntSolution.powSupply"), content: power_supply, type: "inline", isGroupItem: true },
                                    { title: this.$c("pages.CaseDetail.detailsPage.entIntSolution.codFormat"), content: coding_format, type: "inline", isGroupItem: true, isLinFitsrt: true },
                                    { title: this.$c("pages.CaseDetail.detailsPage.common.othRequire"), content: camera_appearance_other, type: "block", isGroupItem: true },

                                    {
                                        title: this.$c("pages.CaseDetail.detailsPage.entIntSolution.surType"),
                                        content: surve_camera_type,
                                        type: "inline",
                                        classifyTitle: this.$c("pages.CaseDetail.detailsPage.entIntSolution.camAppearance"),
                                        isLinFitsrt: true,
                                    },
                                    { title: this.$c("pages.CaseDetail.detailsPage.entIntSolution.funDevices"), content: functional_devices, type: "inline", isGroupItem: true },
                                    { title: this.$c("pages.CaseDetail.detailsPage.entIntSolution.vidPeriod"), content: video_period, type: "inline", isGroupItem: true, isLinFitsrt: true },
                                    { title: this.$c("pages.CaseDetail.detailsPage.common.othRequire"), content: camera_type_other, type: "block", isGroupItem: true },

                                    { title: this.$c("pages.CaseDetail.detailsPage.entIntSolution.neeNVR"), content: need_a_nvr, type: "inline", isLinFitsrt: true },
                                    { title: this.$c("pages.CaseDetail.detailsPage.entIntSolution.describe"), content: describe, type: "block" },
                                ],
                            }
                            break
                        case 6:
                            console.log("6.Enterprise Intelligence Solutions / Video Conference（企业智能-视频会议）")
                            var { specific_verification_requirements, number_of_meeting_rooms, number_of_users_rooms, meeting_rooms_meters, meeting_rooms_other, install_type, install_other } = info.details
                            this.topForm = {
                                title: "",
                                list: [
                                    { title: this.$c("pages.CaseDetail.detailsPage.common.specRequire"), content: specific_verification_requirements, type: "block" },
                                    {
                                        title: this.$c("pages.CaseDetail.detailsPage.entIntSolutionVideo.numMeetingRooms"),
                                        content: number_of_meeting_rooms,
                                        type: "inline",
                                        classifyTitle: this.$c("pages.CaseDetail.detailsPage.entIntSolutionVideo.meeRooms"),
                                        isLinFitsrt: true,
                                    },
                                    { title: this.$c("pages.CaseDetail.detailsPage.entIntSolutionVideo.numUsersRoom"), content: number_of_users_rooms, type: "inline", isGroupItem: true },
                                    { title: this.$c("pages.CaseDetail.detailsPage.entIntSolutionVideo.meeSquareMeters"), content: meeting_rooms_meters, type: "inline", isGroupItem: true, isLinFitsrt: true },
                                    { title: this.$c("pages.CaseDetail.detailsPage.common.othEnvDescription"), content: meeting_rooms_other, type: "block", isGroupItem: true },

                                    {
                                        title: this.$c("pages.CaseDetail.detailsPage.entIntSolutionVideo.installType"),
                                        content: install_type,
                                        type: "inline",
                                        classifyTitle: this.$c("pages.CaseDetail.detailsPage.entIntSolutionVideo.install"),
                                        isLinFitsrt: true,
                                    },
                                    { title: this.$c("pages.CaseDetail.detailsPage.common.othRequire"), content: install_other, type: "block", isGroupItem: true },
                                ],
                            }
                            break
                        case 7:
                            console.log("7.Enterprise Intelligence Solutions/VoIP(企业智能-VoIP)")
                            var { specific_verification_requirements, number_of_access_users, voip_access_type, voip_line_access_to, specific_demands_other, consultation_type, voip_solution_other } = info.details
                            this.topForm = {
                                title: "",
                                list: [
                                    { title: this.$c("pages.CaseDetail.detailsPage.common.specRequire"), content: specific_verification_requirements, type: "block" },
                                    {
                                        title: this.$c("pages.CaseDetail.detailsPage.entIntSolutionVoIP.numUsers"),
                                        content: number_of_access_users,
                                        type: "inline",
                                        classifyTitle: this.$c("pages.CaseDetail.detailsPage.entIntSolutionVoIP.speDemands"),
                                        isLinFitsrt: true,
                                    },
                                    { title: this.$c("pages.CaseDetail.detailsPage.entIntSolutionVoIP.VolPType"), content: voip_access_type, type: "inline", isGroupItem: true },
                                    { title: this.$c("pages.CaseDetail.detailsPage.entIntSolutionVoIP.VolPTo"), content: voip_line_access_to, type: "inline", isGroupItem: true, isLinFitsrt: true },
                                    { title: this.$c("pages.CaseDetail.detailsPage.common.othEnvDescription"), content: specific_demands_other, type: "block", isGroupItem: true },

                                    {
                                        title: this.$c("pages.CaseDetail.detailsPage.entIntSolutionVoIP.VolPSolution"),
                                        content: consultation_type,
                                        type: "inline",
                                        classifyTitle: this.$c("pages.CaseDetail.detailsPage.entIntSolutionVoIP.conType"),
                                        isLinFitsrt: true,
                                    },
                                    { title: this.$c("pages.CaseDetail.detailsPage.common.othRequire"), content: voip_solution_other, type: "block", isGroupItem: true },
                                ],
                            }
                            break
                        case 8:
                            console.log("8.PON Solution(无源光网络)")
                            var {
                                specific_verification_requirements,
                                terminals_type,
                                terminals_number,
                                specific_demands_other,
                                fttx_type,
                                existing_equipment,
                                cable_resources_1_other,
                                split_ratio,
                                uplink_bandwidth,
                                cable_resources_2_other,
                            } = info.details
                            this.topForm = {
                                title: "",
                                list: [
                                    { title: this.$c("pages.CaseDetail.detailsPage.common.specRequire"), content: specific_verification_requirements, type: "block" },
                                    {
                                        title: this.$c("pages.CaseDetail.detailsPage.PONSolution.terType"),
                                        content: terminals_type,
                                        type: "inline",
                                        classifyTitle: this.$c("pages.CaseDetail.detailsPage.PONSolution.speDemands"),
                                        isLinFitsrt: true,
                                    },
                                    { title: this.$c("pages.CaseDetail.detailsPage.PONSolution.terNumber"), content: terminals_number, type: "inline", isGroupItem: true },
                                    { title: this.$c("pages.CaseDetail.detailsPage.common.othRequire"), content: specific_demands_other, type: "block", isGroupItem: true },

                                    {
                                        title: this.$c("pages.CaseDetail.detailsPage.PONSolution.FTTType"),
                                        content: fttx_type,
                                        type: "inline",
                                        classifyTitle: this.$c("pages.CaseDetail.detailsPage.PONSolution.cabResources"),
                                        isLinFitsrt: true,
                                    },
                                    { title: this.$c("pages.CaseDetail.detailsPage.PONSolution.exiEquipment"), content: existing_equipment, type: "inline", isGroupItem: true },
                                    { title: this.$c("pages.CaseDetail.detailsPage.common.othRequire"), content: cable_resources_1_other, type: "block", isGroupItem: true },

                                    {
                                        title: this.$c("pages.CaseDetail.detailsPage.PONSolution.splRatio"),
                                        content: split_ratio,
                                        type: "inline",
                                        classifyTitle: this.$c("pages.CaseDetail.detailsPage.PONSolution.cabResources"),
                                        isLinFitsrt: true,
                                    },
                                    { title: this.$c("pages.CaseDetail.detailsPage.PONSolution.uplBandwidth"), content: uplink_bandwidth, type: "inline", isGroupItem: true },
                                    { title: this.$c("pages.CaseDetail.detailsPage.common.othRequire"), content: cable_resources_2_other, type: "block", isGroupItem: true },
                                ],
                            }
                            break
                        case 9:
                            console.log("10.Integrated Cabling Solutions/Indoor(综合布线室内)")
                            var { specific_verification_requirements, cabling_system, rock_quantity, rock_diatance, connections } = info.details
                            const connectionsList = connections.map((i, index) => {
                                let item = { title: "", content: i, type: "inline" }
                                if (index === 0) {
                                    item.classifyTitle = this.$c("pages.CaseDetail.detailsPage.intCabSolutionsIndoor.transType")
                                } else {
                                    item.isNoTitleGroupItem = true
                                }
                                if (index % 2 == 0) {
                                    item.isLinFitsrt = true
                                }
                                return item
                            })
                            this.topForm = {
                                title: "",
                                list: [
                                    { title: this.$c("pages.CaseDetail.detailsPage.common.specRequire"), content: specific_verification_requirements, type: "block" },
                                    {
                                        title: this.$c("pages.CaseDetail.detailsPage.intCabSolutionsIndoor.cabSystem"),
                                        content: cabling_system,
                                        type: "inline",
                                        classifyTitle: this.$c("pages.CaseDetail.detailsPage.intCabSolutionsIndoor.dataInfo"),
                                        isLinFitsrt: true,
                                    },
                                    { title: this.$c("pages.CaseDetail.detailsPage.intCabSolutionsIndoor.rockQuantity"), content: rock_quantity, type: "inline", isGroupItem: true, isLinFitsrt: true },
                                    { title: this.$c("pages.CaseDetail.detailsPage.intCabSolutionsIndoor.rockDistance"), content: rock_diatance, type: "inline", isGroupItem: true },
                                    ...connectionsList,
                                ],
                            }
                            break
                        case 10:
                            console.log("9.Integrated Cabling Solutions/Outdoor(综合布线室外)")
                            var {
                                specific_verification_requirements,
                                cabling_for,
                                cable_type,
                                cable_management,
                                rack_cabinet_quantity,
                                rack_distance,
                                other_requirements,
                                transmission_medium,
                                product_parameter_information,
                            } = info.details
                            this.topForm = {
                                title: "",
                                list: [
                                    { title: this.$c("pages.CaseDetail.detailsPage.common.specRequire"), content: specific_verification_requirements, type: "block" },
                                    {
                                        title: this.$c("pages.CaseDetail.detailsPage.intCabSolutionsOutdoor.cabFor"),
                                        content: cabling_for,
                                        type: "inline",
                                        classifyTitle: this.$c("pages.CaseDetail.detailsPage.intCabSolutionsOutdoor.cabInfo"),
                                        isLinFitsrt: true,
                                    },
                                    { title: this.$c("pages.CaseDetail.detailsPage.intCabSolutionsOutdoor.cabType"), content: cable_type, type: "inline", isGroupItem: true, isGroupItem: true },
                                    { title: this.$c("pages.CaseDetail.detailsPage.intCabSolutionsOutdoor.cabManagement"), content: cable_management, type: "inline", isLinFitsrt: true },
                                    { title: this.$c("pages.CaseDetail.detailsPage.intCabSolutionsOutdoor.rockQuantity"), content: rack_cabinet_quantity, type: "inline", isGroupItem: true },
                                    { title: this.$c("pages.CaseDetail.detailsPage.intCabSolutionsOutdoor.rockDistance"), content: rack_distance, type: "inline", isLinFitsrt: true, isGroupItem: true },
                                    { title: this.$c("pages.CaseDetail.detailsPage.common.othRequire"), content: other_requirements, type: "block", isGroupItem: true },
                                    { title: this.$c("pages.CaseDetail.detailsPage.common.transMedium"), content: transmission_medium, type: "inline", isLinFitsrt: true },
                                    { title: this.$c("pages.CaseDetail.detailsPage.intCabSolutionsOutdoor.proInfo"), content: product_parameter_information, type: "block" },
                                ],
                            }
                            break
                        case 12:
                            console.log("12.ROCE Networking")
                            var {
                                nic_speed,
                                nic_quantity,
                                nic_type,
                                switch_management_platform,
                                expected_convergence_ratio,
                                message_for_detail,
                                scenario,
                                operating_system,
                                network_type,
                                bandwidth_information,
                                other_requirements,
                            } = info.details
                            this.topForm = {
                                title: "",
                                list: [
                                    { title: this.$c("pages.CaseDetail.detailsPage.roceNetworking.nicSpeed"), content: nic_speed, type: "inline", isLinFitsrt: true },
                                    { title: this.$c("pages.CaseDetail.detailsPage.roceNetworking.nicQuantity"), content: nic_quantity, type: "inline" },
                                    { title: this.$c("pages.CaseDetail.detailsPage.roceNetworking.nicType"), content: nic_type, type: "inline", isLinFitsrt: true },
                                    { title: this.$c("pages.CaseDetail.detailsPage.roceNetworking.switchManagementPlatform"), content: switch_management_platform, type: "inline" },
                                    { title: this.$c("pages.CaseDetail.detailsPage.roceNetworking.expectedConvergenceRatio"), content: expected_convergence_ratio, type: "inline", isLinFitsrt: true },
                                    { title: this.$c("pages.CaseDetail.detailsPage.roceNetworking.messageForDetail"), content: message_for_detail, type: "inline" },
                                    { title: this.$c("pages.CaseDetail.detailsPage.roceNetworking.scenario"), content: scenario, type: "inline", isLinFitsrt: true },
                                    { title: this.$c("pages.CaseDetail.detailsPage.roceNetworking.operatingSystem"), content: operating_system, type: "inline" },
                                    { title: this.$c("pages.CaseDetail.detailsPage.roceNetworking.networkType"), content: network_type, type: "inline", isLinFitsrt: true },
                                    { title: this.$c("pages.CaseDetail.detailsPage.roceNetworking.bandwidthInformation"), content: bandwidth_information, type: "inline" },
                                    { title: this.$c("pages.CaseDetail.detailsPage.roceNetworking.otherRequirements"), content: other_requirements, type: "inline", isLinFitsrt: true },
                                ],
                            }
                            break
                        case 11:
                            console.log("11.InfiniBand Networking")
                            var { gpu_type, gpus_quantity, servers_quantity, other_requirements, message_for_detail, scenario, cpu_type, operating_system, network_type, bandwidth_information } = info.details
                            this.topForm = {
                                title: "",
                                list: [
                                    { title: this.$c("pages.CaseDetail.detailsPage.infiniBandNetworking.gpuType"), content: gpu_type, type: "inline", isLinFitsrt: true },
                                    { title: this.$c("pages.CaseDetail.detailsPage.infiniBandNetworking.gpusQuantity"), content: gpus_quantity, type: "inline" },
                                    { title: this.$c("pages.CaseDetail.detailsPage.infiniBandNetworking.serversQuantity"), content: servers_quantity, type: "inline", isLinFitsrt: true },
                                    { title: this.$c("pages.CaseDetail.detailsPage.infiniBandNetworking.otherRequirements"), content: other_requirements, type: "inline" },
                                    { title: this.$c("pages.CaseDetail.detailsPage.infiniBandNetworking.messageForDetail"), content: message_for_detail, type: "inline", isLinFitsrt: true },
                                    { title: this.$c("pages.CaseDetail.detailsPage.infiniBandNetworking.scenario"), content: scenario, type: "inline" },
                                    { title: this.$c("pages.CaseDetail.detailsPage.infiniBandNetworking.cpuType"), content: cpu_type, type: "inline", isLinFitsrt: true },
                                    { title: this.$c("pages.CaseDetail.detailsPage.infiniBandNetworking.operatingSystem"), content: operating_system, type: "inline" },
                                    { title: this.$c("pages.CaseDetail.detailsPage.infiniBandNetworking.networkType"), content: network_type, type: "inline", isLinFitsrt: true },
                                    { title: this.$c("pages.CaseDetail.detailsPage.infiniBandNetworking.bandwidthInformation"), content: bandwidth_information, type: "inline" },
                                ],
                            }
                            break
                    }

                    var { name, phone, company_name, email, job_title, country, industry } = info.details.case_information
                    this.botForm = {
                        title: this.$c("pages.CaseDetail.detailsPage.common.caseInformation"),
                        list: [
                            { title: this.$c("pages.CaseDetail.detailsPage.common.name"), content: name, type: "inline", isLinFitsrt: true },
                            { title: this.$c("pages.CaseDetail.detailsPage.common.phoneNumber"), content: phone, type: "inline" },
                            { title: this.$c("pages.CaseDetail.detailsPage.common.companyName"), content: company_name, type: "inline", isLinFitsrt: true },
                            { title: this.$c("pages.CaseDetail.detailsPage.common.email"), content: info.details.email, type: "inline" },
                            { title: this.$c("pages.CaseDetail.detailsPage.common.countryRegion"), content: country, type: "inline", isLinFitsrt: true },
                            { title: this.$c("pages.CaseDetail.detailsPage.common.jobTitle"), content: job_title, type: "inline" },
                            { title: this.$c("pages.CaseDetail.detailsPage.common.industry"), content: industry, type: "inline", isLinFitsrt: true },
                        ],
                    }
                    break
                case 39:
                    console.log("交换机")
                    var { new_switch_network, new_switch_scenario, requirement_desc, purchase_time_frame, budget, similar_reference, quantity, decision_maker } = info.details
                    const reqDescriptionList = requirement_desc.map((item, index) => {
                        const { key, desc } = item
                        let brandData = { title: key, content: desc, type: "inline" }
                        if (index === 0) {
                            brandData.classifyTitle = this.$c("pages.CaseDetail.detailsPage.exchange.reqDescription")
                        } else {
                            brandData.isGroupItem = true
                        }
                        if (index % 2 === 0) {
                            brandData.isLinFitsrt = true
                        }
                        return brandData
                    })

                    this.topForm = {
                        title: "",
                        list: [
                            { title: this.$c("pages.CaseDetail.detailsPage.exchange.swDeployment"), content: new_switch_network, type: "block" },
                            { title: this.$c("pages.CaseDetail.detailsPage.exchange.netScenario"), content: new_switch_scenario, type: "block" },

                            ...reqDescriptionList,
                            { title: this.$c("pages.CaseDetail.detailsPage.exchange.simReference"), content: similar_reference, type: "block" },
                            {
                                title: this.$c("pages.CaseDetail.detailsPage.exchange.purFrame"),
                                content: purchase_time_frame,
                                type: "inline",
                                classifyTitle: this.$c("pages.CaseDetail.detailsPage.exchange.inqInformation"),
                                isLinFitsrt: true,
                            },
                            { title: this.$c("pages.CaseDetail.detailsPage.exchange.budget"), content: budget, type: "inline", isGroupItem: true },
                            { title: this.$c("pages.CaseDetail.detailsPage.exchange.quantity"), content: quantity, type: "inline", isGroupItem: true, isLinFitsrt: true },
                            { title: this.$c("pages.CaseDetail.detailsPage.exchange.decMarker"), content: decision_maker, type: "inline", isGroupItem: true },
                        ],
                    }

                    var { name, phone, company_name, email, country, industry, job_title } = info.details
                    this.botForm = {
                        title: this.$c("pages.CaseDetail.detailsPage.common.caseInformation"),
                        list: [
                            { title: this.$c("pages.CaseDetail.detailsPage.common.name"), content: name, type: "inline", isLinFitsrt: true },
                            { title: this.$c("pages.CaseDetail.detailsPage.common.phoneNumber"), content: phone, type: "inline" },
                            { title: this.$c("pages.CaseDetail.detailsPage.common.companyName"), content: company_name, type: "inline", isLinFitsrt: true },
                            { title: this.$c("pages.CaseDetail.detailsPage.common.email"), content: email, type: "inline" },
                            { title: this.$c("pages.CaseDetail.detailsPage.common.countryRegion"), content: country, type: "inline", isLinFitsrt: true },
                            { title: this.$c("pages.CaseDetail.detailsPage.common.jobTitle"), content: job_title, type: "inline" },
                            { title: this.$c("pages.CaseDetail.detailsPage.common.industry"), content: industry, type: "inline", isLinFitsrt: true },
                        ],
                    }
                    break
                case 40:
                    console.log("测试报告")
                    // 40 交换机测试报告申请
                    var { test_requirements, test_purpose, product_id, test_device, test_func } = info.details
                    this.topForm = {
                        title: "",
                        list: [
                            { title: this.$c("pages.CaseDetail.detailsPage.compatible.testRequirements"), content: test_requirements, type: "block" },
                            { title: this.$c("pages.CaseDetail.detailsPage.remoteTest.testPurpose"), content: test_purpose, type: "block" },
                            { title: this.$c("pages.CaseDetail.detailsPage.remoteTest.productID"), content: product_id, type: "inline", isLinFitsrt: true },
                            { title: this.$c("pages.CaseDetail.detailsPage.remoteTest.testDevice"), content: test_device, type: "inline" },
                            { title: this.$c("pages.CaseDetail.detailsPage.remoteTest.equipmentFunction"), content: this.getEquipmentFun(test_func), type: "block" },
                        ],
                    }

                    var { name, phone, company_name, email, country, industry, company_size } = info.details
                    this.botForm = {
                        title: this.$c("pages.CaseDetail.detailsPage.common.caseInformation"),
                        list: [
                            { title: this.$c("pages.CaseDetail.detailsPage.common.name"), content: name, type: "inline", isLinFitsrt: true },
                            { title: this.$c("pages.CaseDetail.detailsPage.common.phoneNumber"), content: phone, type: "inline" },
                            { title: this.$c("pages.CaseDetail.detailsPage.common.companyName"), content: company_name, type: "inline", isLinFitsrt: true },
                            { title: this.$c("pages.CaseDetail.detailsPage.common.email"), content: email, type: "inline" },
                            { title: this.$c("pages.CaseDetail.detailsPage.concactSele.companySize"), content: company_size, type: "inline", isLinFitsrt: true },
                            { title: this.$c("pages.CaseDetail.detailsPage.common.countryRegion"), content: country, type: "inline" },
                            { title: this.$c("pages.CaseDetail.detailsPage.common.industry"), content: industry, type: "inline", isLinFitsrt: true },
                        ],
                    }
                    break
                case 41:
                    console.log("测试报告")
                    //41 其他产品测试报告申请
                    var { test_requirements, test_purpose, product_id } = info.details
                    this.topForm = {
                        title: "",
                        list: [
                            { title: this.$c("pages.CaseDetail.detailsPage.compatible.testRequirements"), content: test_requirements, type: "block" },
                            { title: this.$c("pages.CaseDetail.detailsPage.remoteTest.testPurpose"), content: test_purpose, type: "block" },
                            { title: this.$c("pages.CaseDetail.detailsPage.remoteTest.productID"), content: product_id, type: "block" },
                        ],
                    }

                    var { name, phone, company_name, email, country, industry, company_size } = info.details
                    this.botForm = {
                        title: this.$c("pages.CaseDetail.detailsPage.common.caseInformation"),
                        list: [
                            { title: this.$c("pages.CaseDetail.detailsPage.common.name"), content: name, type: "inline", isLinFitsrt: true },
                            { title: this.$c("pages.CaseDetail.detailsPage.common.phoneNumber"), content: phone, type: "inline" },
                            { title: this.$c("pages.CaseDetail.detailsPage.common.companyName"), content: company_name, type: "inline", isLinFitsrt: true },
                            { title: this.$c("pages.CaseDetail.detailsPage.common.email"), content: email, type: "inline" },
                            { title: this.$c("pages.CaseDetail.detailsPage.concactSele.companySize"), content: company_size, type: "inline", isLinFitsrt: true },
                            { title: this.$c("pages.CaseDetail.detailsPage.common.countryRegion"), content: country, type: "inline" },
                            { title: this.$c("pages.CaseDetail.detailsPage.common.industry"), content: industry, type: "inline", isLinFitsrt: true },
                        ],
                    }
                    break
                case 42:
                    //42 测试申请  Test Request
                    var { test_method, test_product, test_purpose, test_time, device_model, test_requirements } = info.details
                    const basicList = [
                        { title: this.$c("pages.CaseDetail.detailsPage.testRequest.testMethod"), content: test_method, type: "inline", isLinFitsrt: true },
                        { title: this.$c("pages.CaseDetail.detailsPage.testRequest.testProduct"), content: test_product, type: "inline" },
                        { title: this.$c("pages.CaseDetail.detailsPage.testRequest.testPrupose"), content: test_purpose, type: "inline", isLinFitsrt: true },
                    ]
                    let list = []
                    if (test_time) {
                        // test Report
                        list = basicList.concat([
                            { title: this.$c("pages.CaseDetail.detailsPage.testRequest.testTime"), content: test_time, type: "inline" },
                            // { title: this.$c("pages.CaseDetail.detailsPage.testRequest.deviceModel"), content: device_model, type: "block" },
                            { title: this.$c("pages.CaseDetail.detailsPage.testRequest.testRequirements"), content: test_requirements, type: "block" },
                        ])
                    } else {
                        //demo Report
                        list = basicList.concat([
                            // { title: this.$c("pages.CaseDetail.detailsPage.testRequest.testTime"), content: test_time, type: "inline" },
                            { title: this.$c("pages.CaseDetail.detailsPage.testRequest.deviceModel"), content: device_model, type: "inline" },
                            { title: this.$c("pages.CaseDetail.detailsPage.testRequest.testRequirements"), content: test_requirements, type: "block" },
                        ])
                    }
                    this.topForm = {
                        title: "",
                        list,
                    }
                    // 底部用户信息
                    var { name, phone, country, email } = info.details
                    this.botForm = {
                        title: this.$c("pages.CaseDetail.detailsPage.common.caseInformation"),
                        list: [
                            { title: this.$c("pages.CaseDetail.detailsPage.common.name"), content: name, type: "inline", isLinFitsrt: true },
                            { title: this.$c("pages.CaseDetail.detailsPage.common.phoneNumber"), content: phone, type: "inline" },
                            { title: this.$c("pages.CaseDetail.detailsPage.common.countryRegion"), content: country, type: "inline", isLinFitsrt: true },
                            { title: this.$c("pages.CaseDetail.detailsPage.common.email"), content: email, type: "inline" },
                        ],
                    }
                    break
                case 46:
                    //46 产品定制服务
                    const { common, components, services, performance, brief_content } = info.details
                    let topFormList = []
                    if (brief_content) {
                        topFormList.push({ title: this.$c("pages.CaseDetail.detailsPage.askDemo.briefProjectDescription"), content: brief_content, type: "block" })
                    }
                    topFormList = topFormList.concat([
                        { title: this.$c("pages.CaseDetail.detailsPage.productDevelopment.required"), content: info.details.estimated_number, type: "block" },
                        { type: "customProduct", products: info.details.products, title: this.$c("pages.CaseDetail.detailsPage.productDevelopment.customProduct"), hiddeQty: true, style: { border: "none" } },
                        { title: this.$c("pages.CaseDetail.detailsPage.technicalSupport.type"), content: common.case_type, type: "inline", isLinFitsrt: true },
                        { title: this.$c("pages.CaseDetail.detailsPage.technicalSupport.createdBy"), content: common.created_by, type: "inline" },
                        { title: this.$c("pages.CaseDetail.detailsPage.productPrice.country"), content: common.country, type: "inline", isLinFitsrt: true },
                        { title: this.$c("pages.CaseDetail.detailsPage.technicalSupport.email"), content: info.details.email, type: "inline" },
                        { title: this.$c("pages.CaseDetail.detailsPage.technicalSupport.phoneNumber"), content: common.phone?.length > 2 ? common.phone : "/", type: "inline", isLinFitsrt: true },
                    ])
                    if (common && common.state) {
                        topFormList.push({ title: this.$c("pages.CaseDetail.detailsPage.askDemo.state"), content: common.state || "--", type: "inline" })
                    }
                    this.topForm = {
                        title: "",
                        list: topFormList,
                    }
                    // 底部用户信息
                    var { name, phone, country, email } = info.details
                    if (components && components.length > 0) {
                        const list = components.map(({ title, content }, index) => {
                            return {
                                title,
                                content,
                                type: "inline",
                                isLinFitsrt: (index + 1) % 2 === 1,
                            }
                        })
                        this.arrForm.push({
                            title: this.$c("pages.CaseDetail.detailsPage.productDevelopment.components"),
                            list,
                        })
                    }
                    if (services && services.length > 0) {
                        const list = services.map(({ title, content }) => {
                            return {
                                title,
                                content,
                                type: "block",
                            }
                        })
                        this.arrForm.push({
                            title: this.$c("pages.CaseDetail.detailsPage.productDevelopment.services"),
                            list,
                        })
                    }
                    if (performance && performance.length > 0) {
                        const list = performance.map(({ title, content }) => {
                            return {
                                title,
                                content,
                                type: "block",
                            }
                        })
                        this.arrForm.push({
                            title: this.$c("pages.CaseDetail.detailsPage.productDevelopment.performance"),
                            list,
                        })
                    }
                    break

                case 47:
                    //预约参观智能产业园
                    console.log("预约参观智能产业园")
                    this.topForm = {
                        title: "",
                        list: [
                            { title: this.$c("pages.CaseDetail.detailsPage.common.phoneNumber"), content: info.details.phone, type: "inline", isLinFitsrt: true },
                            { title: this.$c("pages.CaseDetail.detailsPage.application.email"), content: info.details.email, type: "inline" },
                            { title: this.$c("pages.CaseDetail.detailsPage.common.jobTitle"), content: info.details.job, type: "inline", isLinFitsrt: true },
                            { title: this.$c("pages.CaseDetail.detailsPage.application.createdBy"), content: info.details.creator, type: "inline" },
                        ],
                    }
                    this.botForm = {
                        title: this.$c("pages.CaseDetail.detailsPage.videoMeeting.appointmentDetails"),
                        list: [
                            { title: "来访目的", content: info.details.visit_purpose, type: "block" },
                            { title: this.$c("pages.CaseDetail.detailsPage.application.briefProjectDescription"), content: info.details.description, type: "block" },
                            { title: this.$c("pages.CaseDetail.detailsPage.videoMeeting.appointmentTime"), content: info.details.time, type: "inline", isLinFitsrt: true },
                            { title: this.$c("pages.CaseDetail.detailsPage.videoMeeting.company"), content: info.details.company_name, type: "inline" },
                            { title: "公司地址", content: info.details.company_address, type: "inline", isLinFitsrt: true },
                            { title: "来访人数", content: info.details.visit_number, type: "inline" },
                        ],
                    }
                    break
                case 50:
                    // 新的RMA
                    this.productForm = {
                        title: this.$c("pages.CaseDetail.detailsPage.authForm.proInfo"),
                        list: info.details.products?.products,
                        total: info.details.products?.total_price_str,
                    }
                    var {
                        phone,
                        email,
                        return_information: { comments, navigation_name, rma_type, refund_method },
                        return_address: {
                            entry_firstname,
                            entry_lastname,
                            entry_country,
                            entry_street_address,
                            entry_company,
                            entry_tax_number,
                            entry_eori,
                            entry_suburb,
                            entry_city,
                            entry_postcode,
                            entry_company_type,
                            state,
                            entry_state,
                            entry_country_id,
                        },
                        sale_type,
                    } = info.details
                    const language_id = info.language_id
                    let otherForm = refund_method && sale_type === 0 ? [{ title: this.$c("pages.RMA.refundTo"), content: refund_method, isLinFitsrt: true, type: "inline" }] : []
                    this.topForm = {
                        title: this.$c("pages.RMA.returnInformation"),
                        list: [
                            { title: this.$c("pages.RequestTechSupport.reasonsForReturn"), content: navigation_name, isLinFitsrt: true, type: "inline" },
                            { title: this.$c("pages.RMA.ruturnType"), content: rma_type, type: "inline" },
                            ...otherForm,
                            { title: this.$c("form.form.comment"), content: comments, isLinFitsrt: !otherForm.length, type: "inline" },
                        ],
                    }
                    let obj = []
                    const trans = this.getTarnsMixins(entry_country_id)
                    //中文
                    if (language_id === 6) {
                        obj = [
                            { title: this.$c("pages.MyAccount.settings.firstName"), content: entry_firstname },
                            { title: this.$c("pages.CaseDetail.detailsPage.common.phoneNumber"), content: phone },
                            { title: this.$c("form.form.address"), content: `${entry_state} ${entry_city} ${entry_suburb}` },
                            { title: this.$c("form.form.detail_address"), content: entry_street_address },
                            { title: this.$c("form.form.zip_code"), content: entry_postcode },
                        ]
                    } else {
                        obj = [
                            {
                                title: this.$c("pages.MyAccount.settings.firstName"),
                                content: entry_firstname,
                            },
                            {
                                title: this.$c("pages.MyAccount.settings.lastName"),
                                content: entry_lastname,
                            },
                            // {
                            //     title: this.$c("pages.CaseDetail.detailsPage.common.email"),
                            //     content: email,
                            // },
                            {
                                title: this.$c("pages.CaseDetail.detailsPage.askDemo.countryRegion"),
                                content: entry_country,
                            },
                        ]
                        if (state || entry_state) {
                            obj.push({
                                title: trans.state,
                                content: state || entry_state,
                            })
                        }
                        obj.push(
                            ...[
                                {
                                    title: this.$c("pages.CaseDetail.detailsPage.common.phoneNumber"),
                                    content: phone,
                                },
                                {
                                    title: this.$c("pages.confirmOrder.address.addressType"),
                                    content: entry_company_type,
                                },
                            ]
                        )
                        if (entry_company) {
                            obj.push({
                                title: this.$c("pages.confirmOrder.address.companyName"),
                                content: entry_company,
                            })
                        }
                        if (entry_tax_number) {
                            obj.push({
                                title: trans.tax,
                                content: entry_tax_number,
                            })
                        }
                        if (entry_eori) {
                            obj.push({
                                title: this.$c("common.basic.addressHandle.EORINumber"),
                                content: entry_eori,
                            })
                        }
                        obj.push(
                            ...[
                                {
                                    title: this.website === "sg" ? "Postal code" : trans.code,
                                    content: entry_postcode,
                                },
                                {
                                    title: trans.ad,
                                    content: entry_street_address,
                                },
                            ]
                        )
                        if (entry_suburb) {
                            obj.push({
                                title: trans.ad2,
                                content: entry_suburb,
                            })
                        }
                        if (entry_city) {
                            obj.push({
                                title: trans.city,
                                content: entry_city,
                            })
                        }
                    }
                    obj = obj.map((item, index) => ({
                        ...item,
                        type: "inline",
                        isLinFitsrt: index % 2 === 0,
                    }))
                    this.botForm = {
                        title: this.$c("pages.RMA.returnAddress"),
                        list: [...obj],
                    }
                    break
                case 51:
                    console.log("quote")
                    this.topForm = {
                        title: "",
                        list: [
                            { title: this.$c("pages.CaseDetail.detailsPage.quote.productRequirements"), content: info.details.description, type: "block" },
                            { type: "product", products: info.details.products_info, title: this.$c("pages.CaseDetail.detailsPage.quote.quoteProduct") },
                            { title: this.$c("pages.CaseDetail.detailsPage.askDemo.sourceFrom"), content: info.details.source_from, isLinFitsrt: true, type: "inline" },
                        ],
                    }

                    this.botForm = {
                        title: this.$c("pages.CaseDetail.detailsPage.common.caseInformation"),
                        list: [
                            { title: this.$c("pages.CaseDetail.detailsPage.application.createdBy"), content: info.details.create_by, isLinFitsrt: true, type: "inline" },
                            { title: this.$c("pages.CaseDetail.detailsPage.askDemo.type"), content: info.details.type, type: "inline" },
                            { title: this.$c("pages.CaseDetail.detailsPage.askDemo.countryRegion"), content: info.details.country, isLinFitsrt: true, type: "inline" },
                            { title: this.$c("pages.CaseDetail.detailsPage.askDemo.email"), content: info.details.email, type: "inline" },
                            { title: this.$c("pages.CaseDetail.detailsPage.networks.phoneNumber"), content: info.details.phone, type: "inline" },
                        ],
                    }
                    break
                case 56:
                    console.log(info.details, "账期申请！！！！！")
                    const { business_info, contact_info, account_pay, submit_type } = info.details
                    if (business_info && Object.keys(business_info).length > 0) {
                        this.arrForm.push({
                            title: this.$c("pages.NetTermsApplication.businessInformation.title"),
                            list: [
                                { title: this.$c("pages.CaseDetail.detailsPage.common.companyName"), content: business_info.company_name, type: "inline", isLinFitsrt: true },
                                { title: this.$c("pages.CaseDetail.detailsPage.spartPart.address"), content: business_info.address, type: "inline" },
                                { title: this.$c("pages.CaseDetail.detailsPage.networks.phoneNumber"), content: business_info.phone_number, type: "inline", isLinFitsrt: true },
                                { title: this.$c("pages.CaseDetail.detailsPage.askDemo.city"), content: business_info.city, type: "inline" },
                                { title: this.$c("pages.CaseDetail.detailsPage.askDemo.state"), content: business_info.state, type: "inline", isLinFitsrt: true },
                                { title: this.$c("pages.CaseDetail.detailsPage.application.country"), content: business_info.country, type: "inline" },
                                { title: this.$c("pages.CaseDetail.detailsPage.askDemo.zipCode"), content: business_info.zip_code, type: "inline", isLinFitsrt: true },
                                { title: this.$c("pages.NetTermsApplication.applicationType.from.yearStated").replace("*", ""), content: business_info.year_stated, type: "inline" },
                                { title: this.$c("pages.NetTermsApplication.applicationType.from.federalID"), content: business_info.tax_id, type: "inline", isLinFitsrt: true },
                                { title: this.$c("pages.NetTermsApplication.applicationType.from.dunsNumber"), content: business_info.duns_number, type: "inline" },
                                { title: this.$c("pages.NetTermsApplication.applicationType.from.website").replace("*", ""), content: business_info.website, type: "inline", isLinFitsrt: true },
                            ],
                        })
                    }
                    if (contact_info && Object.keys(contact_info).length > 0) {
                        if (submit_type === 2) {
                            this.arrForm.push({
                                title: this.$c("pages.NetTermsApplication.applicationType.contactInformation.title"),
                                list: [
                                    { title: this.$c("pages.CaseDetail.netApplication.firstName"), content: contact_info.first_name, type: "inline", isLinFitsrt: true },
                                    { title: this.$c("pages.CaseDetail.netApplication.lastName"), content: contact_info.last_name, type: "inline" },
                                    { title: this.$c("pages.CaseDetail.netApplication.businessEmail"), content: contact_info.email, type: "inline", isLinFitsrt: true },
                                    { title: this.$c("pages.CaseDetail.detailsPage.application.country"), content: contact_info.country, type: "inline" },
                                    { title: this.$c("pages.CaseDetail.detailsPage.askDemo.state"), content: contact_info.state, type: "inline", isLinFitsrt: true },
                                    { title: this.$c("pages.CaseDetail.detailsPage.networks.phoneNumber"), content: contact_info.phone, type: "inline" },
                                    { title: this.$c("form.form.comment"), content: contact_info.description, type: "inline" },
                                ],
                            })
                        } else {
                            this.arrForm.push({
                                title: this.$c("pages.NetTermsApplication.applicationType.contactInformation.title"),
                                list: [
                                    { title: this.$c("pages.CaseDetail.netApplication.firstName"), content: contact_info.first_name, type: "inline", isLinFitsrt: true },
                                    { title: this.$c("pages.CaseDetail.netApplication.lastName"), content: contact_info.last_name, type: "inline" },
                                    { title: this.$c("pages.CaseDetail.netApplication.title"), content: contact_info.title, type: "inline", isLinFitsrt: true },
                                    { title: this.$c("pages.CaseDetail.netApplication.businessEmail"), content: info.details.email, type: "inline" },
                                    { title: this.$c("pages.CaseDetail.detailsPage.networks.phoneNumber"), content: contact_info.phone, type: "inline", isLinFitsrt: true },
                                ],
                            })
                        }
                    }
                    if (account_pay && Object.keys(account_pay).length > 0) {
                        this.arrForm.push({
                            title: this.$c("pages.CaseDetail.contactInformation.accountsPayable"),
                            list: [
                                { title: this.$c("pages.CaseDetail.netApplication.firstName"), content: account_pay.first_name, type: "inline", isLinFitsrt: true },
                                { title: this.$c("pages.CaseDetail.netApplication.lastName"), content: account_pay.last_name, type: "inline" },
                                { title: this.$c("pages.CaseDetail.netApplication.title"), content: account_pay.title, type: "inline", isLinFitsrt: true },
                                { title: this.$c("pages.CaseDetail.netApplication.businessEmail"), content: account_pay.email, type: "inline" },
                                { title: this.$c("pages.CaseDetail.detailsPage.networks.phoneNumber"), content: account_pay.phone, type: "inline", isLinFitsrt: true },
                            ],
                        })
                    }
                    break
                case 53:
                case 54: {
                    console.log(info.details, "企业账户申请")
                    const { business_info, contact_info, business_address, email } = info.details
                    if (this.website === "cn") {
                        if (contact_info && Object.keys(contact_info).length > 0) {
                            this.arrForm.push({
                                title: this.$c("single.BusinessAccount.contactInfo.title"),
                                list: [
                                    { title: "姓名", content: `${contact_info.first_name} ${contact_info.last_name}`, type: "inline", isLinFitsrt: true },
                                    { title: "手机号", content: contact_info.customers_telephone, type: "inline" },
                                    { title: this.$c("pages.CaseDetail.contactInformation.accountEmail"), content: email, type: "inline", isLinFitsrt: true },
                                ],
                            })
                        }
                        if (business_info && Object.keys(business_info).length > 0) {
                            let listArr = [
                                { title: this.$c("pages.CaseDetail.contactInformation.registeredName"), content: business_info.business_name, type: "inline", isLinFitsrt: true },
                                { title: "统一社会信息代码", content: business_info.tax_id, type: "inline" },
                                // { title: this.$c("pages.CaseDetail.contactInformation.employeeSize"), content: business_info.employee_sie, type: "inline", isLinFitsrt: true },
                            ]
                            if (business_info.phone_number) {
                                listArr.push({ title: "企业联系方式（选填）", content: business_info.phone_number, type: "inline" })
                            }
                            this.arrForm.push({
                                title: "企业信息",
                                list: listArr,
                            })
                        }
                        if (business_address && Object.keys(business_address).length > 0) {
                            this.arrForm.push({
                                title: "企业注册地址",
                                list: [
                                    { title: "地区", content: business_address.state, type: "inline", isLinFitsrt: true },
                                    { title: this.$c("pages.CaseDetail.detailsPage.askDemo.zipCode"), content: business_address.zip_code, type: "inline" },
                                    { title: "详细地址", content: business_address.address, type: "inline", isLinFitsrt: true },
                                ],
                            })
                        }
                    } else {
                        if (contact_info && Object.keys(contact_info).length > 0) {
                            this.arrForm.push({
                                title: this.$c("pages.CaseDetail.contactInformation.contact"),
                                list: [
                                    { title: this.$c("pages.CaseDetail.netApplication.firstName"), content: contact_info.first_name, type: "inline", isLinFitsrt: true },
                                    { title: this.$c("pages.CaseDetail.netApplication.lastName"), content: contact_info.last_name, type: "inline" },
                                    { title: this.$c("pages.CaseDetail.contactInformation.accountEmail"), content: email, type: "inline", isLinFitsrt: true },
                                ],
                            })
                        }
                        if (business_info && Object.keys(business_info).length > 0) {
                            let listArr = []
                            if (business_info.business_name) {
                                listArr = [
                                    { title: this.$c("pages.Login.org_form.name.label"), content: business_info.business_name, type: "inline", isLinFitsrt: true },
                                    { title: this.$c("pages.CaseDetail.detailsPage.askDemo.countryRegion"), content: business_info.country, type: "inline" },
                                    { title: this.$c("pages.Login.org_form.street_address.label"), content: business_info.address, type: "inline", isLinFitsrt: true },
                                    { title: this.$c("pages.Login.org_form.street_address.label") + 2, content: business_info.address2, type: "inline" },
                                    { title: this.$c("pages.CaseDetail.detailsPage.askDemo.city"), content: business_info.city, type: "inline", isLinFitsrt: true },
                                    { title: this.$c("pages.CaseDetail.detailsPage.askDemo.state"), content: business_info.state, type: "inline" },
                                    { title: this.$c("pages.CaseDetail.detailsPage.askDemo.zipCode"), content: business_info.zip_code, type: "inline", isLinFitsrt: true },
                                    { title: this.$c("pages.CaseDetail.contactInformation.businessNumber"), content: business_info.phone_number, type: "inline" },
                                    { title: this.$c("pages.CaseDetail.contactInformation.taxID"), content: business_info.tax_id, type: "inline", isLinFitsrt: true },
                                    // { title: this.$c("pages.CaseDetail.contactInformation.employeeSize"), content: business_info.employee_size, type: "inline" },
                                ]
                            } else {
                                listArr = [
                                    { title: this.$c("pages.CaseDetail.contactInformation.registeredName"), content: business_info.business_name, type: "inline", isLinFitsrt: true },
                                    // { title: this.$c("pages.CaseDetail.contactInformation.employeeSize"), content: business_info.employee_sie, type: "inline" },
                                ]
                            }

                            // if (business_info.phone_number) {
                            //     listArr.push({ title: this.$c("pages.CaseDetail.contactInformation.businessNumber"), content: business_info.phone_number, type: "inline", isLinFitsrt: listArr.length === 2 })
                            // }
                            this.arrForm.push({
                                title: this.$c("pages.Login.org_form.title"),
                                list: listArr,
                            })
                        }
                        // if (business_address && Object.keys(business_address).length > 0) {
                        //     this.arrForm.push({
                        //         title: this.$c("pages.CaseDetail.contactInformation.accountsPayable"),
                        //         list: [
                        //             { title: this.$c("pages.CaseDetail.detailsPage.askDemo.countryRegion"), content: business_address.country, type: "inline", isLinFitsrt: true },
                        //             { title: this.$c("pages.CaseDetail.detailsPage.askDemo.city"), content: business_address.city, type: "inline" },
                        //             { title: this.$c("pages.CaseDetail.detailsPage.askDemo.state"), content: business_address.state, type: "inline", isLinFitsrt: true },
                        //             { title: this.$c("pages.CaseDetail.detailsPage.askDemo.zipCode"), content: business_address.zip_code, type: "inline" },
                        //             { title: this.$c("pages.CaseDetail.detailsPage.spartPart.address"), content: business_address.address, type: "inline", isLinFitsrt: true },
                        //         ],
                        //     })
                        // }
                    }
                    break
                }
                case 57: {
                    console.log("FS-Box申请")
                    this.topForm = {
                        title: "",
                        list: [
                            { title: this.$c("pages.CaseDetail.detailsPage.askDemo.briefProjectDescription"), content: info.details.comments || "--", type: "block" },
                            { type: "product", products: info.details.products, title: this.$c("pages.CaseDetail.detailsPage.askDemo.fsBox") },
                            { title: this.$c("pages.CaseDetail.detailsPage.askDemo.sourceFrom"), content: info.details.source, isLinFitsrt: true, type: "inline" },
                        ],
                    }

                    this.botForm = {
                        title: this.$c("pages.CaseDetail.detailsPage.askDemo.boxDeliveredTo"),
                        list: [
                            {
                                title: this.$c("pages.CaseDetail.detailsPage.application.createdBy"),
                                content: `${info.details.delivered_to.first_name} ${info.details.delivered_to.last_name}`,
                                isLinFitsrt: true,
                                type: "inline",
                            },
                            { title: this.$c("pages.CaseDetail.detailsPage.askDemo.type"), content: info.details.delivered_to.type, type: "inline" },
                            { title: this.$c("pages.CaseDetail.detailsPage.askDemo.countryRegion"), content: info.details.delivered_to.country, isLinFitsrt: true, type: "inline" },
                            // { title: this.$c("pages.CaseDetail.detailsPage.askDemo.email"), content: info.details.delivered_to.email, type: "inline" },
                            { title: this.$c("pages.CaseDetail.detailsPage.askDemo.email"), content: info.details.email, type: "inline" },
                            { title: this.$c("pages.CaseDetail.detailsPage.askDemo.phoneNumber"), content: info.details.delivered_to.customers_telephone, type: "inline", isLinFitsrt: true },
                        ],
                    }
                    break
                }
                case 65: {
                    console.log("技术支持")
                    this.topForm = {
                        title: "",
                        list: [
                            { title: this.$c("pages.CaseDetail.detailsPage.technicalSupport.briefProjectDescription"), content: info.details.description, type: "block" },
                            { title: this.$c("pages.CaseDetail.detailsPage.technicalSupport.type"), content: info.details.type, type: "inline", isLinFitsrt: true },
                            { title: this.$c("pages.CaseDetail.detailsPage.technicalSupport.createdBy"), content: info.details.create_by, type: "inline" },
                            { title: this.$c("pages.CaseDetail.detailsPage.technicalSupport.email"), content: info.details.email, isLinFitsrt: true, type: "inline" },
                            { title: this.$c("pages.CaseDetail.detailsPage.technicalSupport.phoneNumber"), content: info.details.phone.length > 2 ? info.details.phone : "/", type: "inline" },
                            { title: this.$c("pages.CaseDetail.detailsPage.technicalSupport.country"), content: info.details.country, type: "inline", isLinFitsrt: true },
                            { title: this.$c("pages.CaseDetail.detailsPage.technicalSupport.sourceFrom"), content: info.details.source_from, type: "inline" },
                        ],
                    }
                    if (this.website === "sg") {
                        this.topForm.list.splice(5, 1)
                    }
                    break
                }
                case 64: {
                    console.log(info, "Project Inquiry")
                    this.topForm = {
                        title: "",
                        list: [
                            { title: this.$c("pages.CaseDetail.inquiry.brief"), content: info.details.description || "--", type: "block" },
                            { title: this.$c("pages.CaseDetail.inquiry.inquiry"), content: info.details.products_id, isLinFitsrt: true, type: "inline" },
                        ],
                    }
                    this.botForm = {
                        title: this.$c("pages.CaseDetail.inquiry.caseInfo"),
                        list: [
                            {
                                title: this.$c("pages.CaseDetail.inquiry.createdBy"),
                                content: info.details.create_by,
                                isLinFitsrt: true,
                                type: "inline",
                            },
                            { title: this.$c("pages.CaseDetail.inquiry.inquiryMethod"), content: info.details.inquiry_method, type: "inline" },
                            { title: this.$c("pages.CaseDetail.inquiry.inquiryType"), content: info.details.inquiry_type, type: "block" },
                            { title: this.$c("pages.CaseDetail.inquiry.country"), content: info.details.country, type: "inline", isLinFitsrt: true },
                            { title: this.$c("pages.CaseDetail.inquiry.businessEmail"), content: info.details.email, type: "inline" },
                            { title: this.$c("pages.CaseDetail.inquiry.phoneNumber"), content: info.details.phone, type: "inline", isLinFitsrt: true },
                        ],
                    }
                    const { business_category = "", application_scenario = "" } = info.details
                    if (business_category) {
                        botForm.push({
                            title: this.$c("pages.CaseDetail.detailsPage.Solution.businessCategory"),
                            content: business_category,
                            type: "inline",
                        })
                    }
                    if (application_scenario) {
                        botForm.push({
                            title: this.$c("pages.CaseDetail.detailsPage.Solution.applicationScenario"),
                            content: application_scenario,
                            type: "inline",
                            isLinFitsrt: true,
                        })
                    }
                    break
                }
                case 77: {
                    this.topForm = {
                        title: "",
                        list: [
                            { title: this.$c("pages.CaseDetail.detailsPage.askDemo.briefProjectDescription"), content: info.details.description, type: "block" },
                            { title: this.$c("pages.CaseDetail.detailsPage.Solution.type"), content: info.details.type, type: "inline", isLinFitsrt: true },
                            { title: this.$c("pages.CaseDetail.detailsPage.technicalSupport.createdBy"), content: info.details.create_by, type: "inline" },
                            { title: this.$c("pages.CaseDetail.detailsPage.technicalSupport.email"), content: info.details.email, isLinFitsrt: true, type: "inline" },
                            { title: this.$c("pages.CaseDetail.detailsPage.askDemo.sourceFrom"), content: info.details.source_from, type: "inline" },
                            { title: this.$c("pages.CaseDetail.detailsPage.emailUs.websiteLink"), content: info.details.website_link, isLinFitsrt: true, type: "inline" },
                        ],
                    }

                    break
                }
                case 78: {
                    //open API 开放平台
                    this.arrForm = [
                        {
                            title: this.$c("pages.NetTermsApplication.topSwitch.contact"),
                            list: [
                                { title: this.$c("pages.CaseDetail.netApplication.firstName"), content: info.details.first_name, type: "inline", isLinFitsrt: true },
                                { title: this.$c("pages.CaseDetail.netApplication.lastName"), content: info.details.last_name, type: "inline" },
                                { title: this.$c("pages.CaseDetail.netApplication.businessEmail"), content: info.details.email, type: "inline", isLinFitsrt: true },
                            ],
                        },
                        {
                            title: this.$c("pages.NetTermsApplication.applyInformation.title"),
                            list: [
                                {
                                    title: this.$c("pages.NetTermsApplication.applyInformation.organizationName"),
                                    content: info.details.apply_information?.organization_name || "",
                                    isLinFitsrt: true,
                                    type: "inline",
                                },
                                { title: this.$c("pages.NetTermsApplication.applyInformation.des"), content: info.details.apply_information?.description || "", type: "inline" },
                            ],
                        },
                    ]
                    break
                }
                case 79: {
                    this.topForm = {
                        title: "",
                        list: [
                            { title: this.$c("pages.CaseDetail.detailsPage.askDemo.projectName"), content: info.details.project_name, type: "block" },
                            { title: this.$c("pages.CaseDetail.detailsPage.Solution.type"), content: info.details.case_type, type: "inline", isLinFitsrt: true },
                            { title: this.$c("pages.CaseDetail.detailsPage.technicalSupport.createdBy"), content: info.details.customers_name, type: "inline" },
                            { title: this.$c("pages.CaseDetail.detailsPage.technicalSupport.email"), content: info.details.customers_email_address, type: "inline", isLinFitsrt: true },
                            { title: this.$c("pages.CaseDetail.detailsPage.remoteTest.countryRegion"), content: info.details.country, type: "inline" },
                            { title: this.$c("pages.CaseDetail.detailsPage.askDemo.city"), content: info.details.city, type: "inline", isLinFitsrt: true },
                            { title: this.$c("pages.CaseDetail.detailsPage.askDemo.deliverDate"), content: info.details.delivery_date, type: "inline" },
                            { title: this.$c("pages.CaseDetail.detailsPage.askDemo.deliveryMethod"), content: info.details.shipping_method, type: "inline", isLinFitsrt: true },
                            { title: this.$c("pages.CaseDetail.detailsPage.askDemo.additionalSupportServices"), content: info.details.additional_service_description, type: "inline" },
                            {
                                title: this.$c("pages.CaseDetail.detailsPage.askDemo.preferredCommunicationMethod"),
                                content: info.details.communication_description,
                                tips: info.details.communication_description,
                                type: "inline",
                                isLinFitsrt: true,
                            },
                            { title: this.$c("pages.CaseDetail.detailsPage.askDemo.additionalRequirements"), content: info.details.other_requirements, type: "inline", tips: info.details.other_requirements },
                        ],
                    }
                    break
                }
                case 82: {
                    //400G RoCE无损网络解决方案
                    this.topForm = {
                        title: "",
                        list: [
                            { title: this.$c("pages.CaseDetail.detailsPage.askDemo.briefProjectDescription"), content: info.details.description, type: "block" },
                            { title: this.$c("pages.CaseDetail.detailsPage.Solution.type"), content: info.details.type, type: "inline", isLinFitsrt: true },
                            { title: this.$c("pages.CaseDetail.detailsPage.technicalSupport.createdBy"), content: info.details.create_by, type: "inline" },
                            { title: this.$c("pages.CaseDetail.detailsPage.technicalSupport.email"), content: info.details.email, isLinFitsrt: true, type: "inline" },
                            { title: this.$c("pages.CaseDetail.detailsPage.askDemo.sourceFrom"), content: info.details.source_from, type: "inline" },
                            { title: this.$c("pages.CaseDetail.detailsPage.emailUs.websiteLink"), content: info.details.website_link, isLinFitsrt: true, type: "inline" },
                            { title: this.$c("pages.CaseDetail.detailsPage.askDemo.jobTitle"), content: info.details.job_title, type: "inline" },
                            { title: this.$c("pages.NetTermsApplication.businessInformation.form.employees"), content: info.details.employees, isLinFitsrt: true, type: "inline" },
                            { title: this.$c("pages.CaseDetail.detailsPage.Solution.industry"), content: info.details.industry, type: "inline" },
                        ],
                    }
                    break
                }
                case 83: {
                    this.topForm = {
                        title: "",
                        list: [
                            { title: this.$c("pages.CaseDetail.detailsPage.askDemo.briefProjectDescription"), content: info.details.description, type: "block" },
                            { title: this.$c("pages.CaseDetail.detailsPage.Solution.type"), content: info.details.type, type: "inline", isLinFitsrt: true },
                            { title: this.$c("pages.CaseDetail.detailsPage.technicalSupport.createdBy"), content: info.details.create_by, type: "inline" },
                            { title: this.$c("pages.CaseDetail.detailsPage.technicalSupport.email"), content: info.details.email, type: "inline", isLinFitsrt: true },
                            { title: this.$c("pages.CaseDetail.detailsPage.technicalSupport.phoneNumber"), content: info.details.phone.length > 2 ? info.details.phone : "/", type: "inline" },
                            { title: this.$c("pages.CaseDetail.detailsPage.technicalSupport.country"), content: info.details.country, type: "inline", isLinFitsrt: true },
                            // { title: this.$c("form.form.company_name"), content: info.details.company_name, type: "inline" },
                            { title: this.$c("pages.CaseDetail.detailsPage.technicalSupport.sourceFrom"), content: info.details.source_from, type: "inline" },
                        ],
                    }
                }
                default:
                    break
            }
        },
        //获取地址字符串
        getAddressString(address) {
            if (Object.values(address).length > 0) {
                const { entry_firstname, entry_lastname, entry_company, entry_address1, entry_postcode, entry_country_name, entry_telephone, entry_city } = address
                return `${entry_firstname} ${entry_lastname} | ${entry_company}  ${entry_address1},${entry_city},${entry_postcode},${entry_country_name},${entry_telephone}`.replace("undefined", "")
            }
            return ""
        },
        // 事件处理
        GaModel(target) {
            window.dataLayer &&
                window.dataLayer.push({
                    event: "uaEvent",
                    eventCategory: "Personal Hub_Case Page Detail",
                    eventAction: "model_click",
                    eventLabel: target.href,
                    nonInteraction: false,
                })
        },
        getEquipmentFun(target = "") {
            const option = this.$c("single.TestReportForm.switch_form")[3].options
            const arr = target.split(",")
            let result = []
            arr.forEach((item) => {
                if (option[item]) {
                    result.push(option[item])
                }
            })
            return result.join(",")
        },
    },
    watch: {
        caseinfo: {
            handler(val) {
                this.getContent(val)
            },
            immediate: true,
        },
    },
}
</script>

<style lang="scss" scoped>
.card {
    padding: 32px 36px;
    border-radius: 12px;
    background-color: #fff;
    margin-bottom: 24px;
    box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.1);
    padding-top: calc(32px - 24px);
}
.details-box {
    margin-top: 16px;
    > h3 {
        @include font20;
        margin-bottom: 20px;
    }
    .card {
        margin-bottom: 0;
    }
}
.descrip {
    .descrip-box {
        color: #0060bf;
        @include font14;
        display: flex;
        align-items: center;
        column-gap: 12px;
        cursor: pointer;
        .iconfont {
            @include font12;
            transition: all 0.2s;
            &.active {
                transform: rotateX(-180deg);
            }
        }
    }
}
.descrip-box-content {
    color: $textColor3;
    @include font12;
}
.product-box {
    h3 {
        margin: 8px 0 20px;
    }
    .card {
        padding: 8px 0 52px;
    }
}

.table {
    word-break: break-word;
    .tr {
        display: flex;
        @include font14();
        &.head-tr {
            font-weight: 600;
            padding: 11.5px 24px;
            border-bottom: 1px solid #e5e5e5;
        }
        &.body-tr {
            margin-top: 20px;
            padding: 0 24px;
        }

        .th,
        .td {
            display: flex;
            align-items: flex-start;
            padding: 0 12px;
        }
        .info {
            flex: 3;
        }
        .quantity {
            flex: 1;
            justify-content: center;
            text-align: center;
            min-width: 140px;
        }
        .price {
            flex: 1;
            justify-content: flex-end;
            min-width: 100px;
        }
        .total {
            flex: 1;
            justify-content: flex-end;
            min-width: 100px;
        }
    }
    .product-info {
        display: flex;
        align-items: flex-start;
        .img {
            width: 80px;
            height: 80px;
            margin-right: 20px;
        }
        .label {
            dt {
                @include font14();
                a {
                    color: #19191a;
                }
            }
            .small {
                @include font13();
                color: #707070;
                margin-top: 8px;
                display: flex;
                > span {
                    margin-right: 8px;
                    &:last-child {
                        margin-right: 0;
                    }
                }
            }
            .strong-m {
                @include font14();
                font-weight: 600;
                margin-top: 8px;
                display: none;
            }
        }
    }
}
.total-box {
    padding: 0 36px;
    margin-top: 20px;
    .total-sum,
    .total-item {
        margin-top: 12px;
        display: flex;
        justify-content: flex-end;
        dt {
            flex: 1;
            text-align: right;
        }
        dd {
            margin-left: 24px;
            text-align: right;
        }
    }
    .total-item {
        @include font14();
    }
    .total-sum {
        @include font16();
        font-weight: 600;
    }
}
@media (max-width: 414px) {
    .card {
        padding: 20px 16px;
        .p0 {
            padding-top: 0;
        }
    }
}
@include mediaM {
    .product-box {
        .card {
            padding: 20px 0;
        }
    }
    .table {
        padding-bottom: 0;
        .tr {
            &.head-tr,
            .quantity,
            .price,
            .total {
                display: none;
            }
            &.body-tr {
                padding: 20px 0;
                margin: 0 16px;
                border-bottom: 1px solid #e5e5e5;
            }
        }
        .product-info .label .strong-m {
            display: block;
        }
    }
    .total-box {
        padding: 0 16px;
        .total-sum,
        .total-item {
            justify-content: space-between;
            dt {
                flex: none;
            }
        }
    }
}
</style>
