<template>
    <div class="invoice_detail" v-loading.fullscreen="loading">
        <client-only>
            <template v-if="info.templateType === 9">
                <!-- PI发票 -->
                <JPPIBill v-if="type === '1' || !info.is_real" :type="type" :info="info" id="pdfCentent"></JPPIBill>
                <!-- 真实发票 -->
                <JPBill v-else :info="info" id="pdfCentent" :type="type"></JPBill>
            </template>
            <template v-else>
                <!-- PI为虚拟发票，客户未真实付款前，看到的都是虚拟发票  1 武汉仓 2澳大利亚仓 3德国仓 4美东仓 5新加坡仓 6深圳仓 7俄罗斯仓 8英国仓 9日本仓-->
                <div v-if="type == 1 && info.templateType == 8">
                    <UKPIBill :info="info" id="pdfCentent" @downloadXML="downloadXML" />
                </div>
                <div v-else-if="type == 1 && info.templateType !== 3">
                    <Bill :info="info" id="pdfCentent" @downloadXML="downloadXML" />
                </div>

                <div v-else-if="type == 1 && info.templateType === 3">
                    <EUBill :info="info" id="pdfCentent" @downloadXML="downloadXML" />
                </div>
                <div v-else-if="info.templateType == 2">
                    <AUBill :info="info" id="pdfCentent" />
                </div>
                <div v-else-if="info.templateType == 3">
                    <DEBill :info="info" id="pdfCentent" @downloadXML="downloadXML" />
                </div>
                <div v-else-if="info.templateType == 4">
                    <ESBill :info="info" id="pdfCentent" @downloadXML="downloadXML" />
                </div>
                <div v-else-if="info.templateType == 5">
                    <SGBill :info="info" id="pdfCentent" />
                </div>
                <div v-else-if="info.templateType == 7">
                    <RUBill :info="info" id="pdfCentent" />
                </div>
                <div v-else-if="info.templateType == 8">
                    <UKBill :info="info" id="pdfCentent" @downloadXML="downloadXML" />
                </div>
                <div v-else-if="[0, 1].includes(info.templateType)">
                    <CNBill :info="info" id="pdfCentent" />
                </div>
            </template>
        </client-only>
    </div>
</template>

<script>
import FsButton from "@/components/FsButton/FsButton"
import Bill from "./components/Bill"
import DEBill from "./components/DEBill"
import AUBill from "./components/AUBill"
import ESBill from "./components/ESBill"
import SGBill from "./components/SGBill"
import RUBill from "./components/RUBill"
import CNBill from "./components/CNBill"
import EUBill from "./components/EUBill"
import JPPIBill from "./components/JPPIBill"
import JPBill from "./components/JPBill"
import UKBill from "./components/UKBill"
import UKPIBill from "./components/UKPIBill.vue"
import { mapState } from "vuex"
export default {
    layout: "print",
    components: {
        FsButton,
        Bill,
        DEBill,
        AUBill,
        ESBill,
        SGBill,
        RUBill,
        CNBill,
        EUBill,
        JPPIBill,
        UKBill,
        JPBill,
        UKPIBill,
    },

    data() {
        return {
            info: {},
            loading: false,
            type: 0,
        }
    },
    computed: {
        ...mapState({
            pageGroup: (state) => state.ga.pageGroup,
            website: (state) => state.webSiteInfo.website,
        }),
        fileName() {
            // 发票命名规则   INVOICE 号_FS订单号_付款状态.pdf   type 1 虚拟发票 固定为 PI_FS订单号_付款状态.pdf
            const { type } = this.$route.query
            const { payment_status, orders_number, track_in_number } = this.info
            return [type * 1 === 1 ? "PI" : track_in_number, orders_number, payment_status].join("_")
        },
    },
    asyncData({ app, route, query, redirect, $c }) {
        let info = {}
        let { type: invoice_type, id: orders_id, orderType: orders_type } = route.query
        return app.$axios
            .post(`/api/orders/orderInvoice/${orders_id}`, { orders_id, invoice_type, orders_type })
            .then((res) => {
                let newType = 0
                if (res.code === 200) {
                    res.status === "error" && route.go(-1)

                    info = res.data[0]

                    info.ordersType = orders_type
                    if (res.data[0].invoice_type) {
                        newType = res.data[0].invoice_type
                    }
                    return { info, type: newType }
                } else {
                    redirect(app.localePath("/404.html"))
                }
            })
            .catch((err) => {
                if (err.code === 422) {
                    redirect(app.localePath("/404.html"))
                }
            })
    },
    mounted() {
        console.log(this.info)
    },
    watch: {
        info(n) {
            this.$nextTick(() => {
                if (this.$route.query?.handle == "print") {
                    this.print()
                }
            })
        },
    },
    methods: {
        print() {
            try {
                document.execCommand("print", false, null)
            } catch {
                window.print()
            }
            if (window.dataLayer) {
                window.dataLayer.push({
                    event: "uaEvent",
                    eventCategory: this.pageGroup,
                    eventAction: "invoice_operate",
                    eventLabel: "Print",
                    nonInteraction: false,
                })
            }
        },
        downloadXML() {
            let { type: invoice_type, id: orders_id, orderType: orders_type } = this.$route.query
            this.$axios
                .post(`/api/orders/orderInvoiceXml`, { orders_id, invoice_type, orders_type })
                .then((response) => {
                    const xmlData = response
                    const blob = new Blob([xmlData], { type: "text/xml" })
                    const url = window.URL.createObjectURL(blob)
                    const link = document.createElement("a")
                    link.href = url
                    link.setAttribute("download", `${this.fileName}.xml`)
                    document.body.appendChild(link)
                    link.click()
                    document.body.removeChild(link)
                    window.URL.revokeObjectURL(url)
                })
                .catch((error) => {
                    console.error("请求出错:", error)
                })
        },
        download() {
            this.exportSavePdf(this.fileName || "Invoice")
            if (window.dataLayer) {
                window.dataLayer.push({
                    event: "uaEvent",
                    eventCategory: this.pageGroup,
                    eventAction: "invoice_operate",
                    eventLabel: "Download",
                    nonInteraction: false,
                })
            }
        },
    },
}
</script>

<style lang="scss" scoped>
.invoice_detail {
    ::v-deep button {
        word-break: keep-all;
    }
    @media print {
        ::v-deep .noprint {
            visibility: hidden;
        }
    }
    // ::v-deep .official {
    //     .head {
    //         position: relative;
    //         > div:first-child {
    //             width: 100%;
    //             justify-content: space-between;
    //             span {
    //                 border: none;
    //             }
    //         }
    //         .noprint {
    //             position: absolute;
    //             right: 0;
    //             top: -54px;
    //         }
    //     }
    // }
}
</style>
