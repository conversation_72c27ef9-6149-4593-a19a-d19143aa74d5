<template>
    <div class="user_page" v-loading="loading">
        <div class="nav">
            <role-select :options="roleList" :value="searchRoleNotTeam" @change="selectRoleChange"></role-select>
            <FsSelectSearch class="search_input" :placeholder="$c('pages.UserSetting.userSetting.user_table.emaiAddress')" v-model="searchKeys" maxlength="100" @search="handleSearch" @clear="clearSearch" />
            <div class="nav_btn" v-if="showNavOption && isCreateUserPower">
                <template v-if="website === 'cn'">
                    <fs-button type="black" @click="initPopup(1)">{{ $c("pages.UserSetting.userSetting.createNewUser") }}</fs-button>
                    <fs-button type="blackline" @click="initPopup(2)">{{ $c("pages.UserSetting.userSetting.linkUser") }}</fs-button>
                </template>
                <fs-button v-else type="blackline" @click="handleAddUser">{{ $c("pages.UserSetting.userSetting.addUser") }}</fs-button>
            </div>
        </div>
        <div class="table">
            <table>
                <thead>
                    <tr>
                        <th class="table_header_item" v-for="{ name, key } in tableHeaderOption" :key="key">
                            <b>{{ name }}</b>
                        </th>
                    </tr>
                </thead>
                <tbody v-if="userList.length">
                    <tr v-for="t in userList" :key="t.id">
                        <td class="table_body_item tr_left">
                            <span>{{ t | getUserName }}</span>
                            <div class="teams_tips" v-if="t.teamNameList">{{ t.teamNameList.join(",") }}</div>
                        </td>
                        <td class="table_body_item">
                            <span>{{ t.customersEmailAddress }}</span>
                        </td>
                        <td class="table_body_item">
                            <span>{{ t.roleStr }}</span>
                        </td>
                        <td class="table_body_item">
                            <!-- 1提交申请，2加入组织，3被邀请人拒绝，4过期，5删除 -->
                            <template v-if="t.status === 1">
                                <span slot="trigger" class="reject_text_p"
                                    ><b>{{ t.statusStr }}</b></span
                                >
                                <fs-popover position="bottom" v-if="t.joinFromType === 1" :popperStyle="{ padding: '12px 16px' }">
                                    <div>{{ $c("pages.UserSetting.userSetting.approvalToJoin") }}</div>
                                </fs-popover>
                                <div class="teams_tips" v-if="t.expiredTime">{{ t.expiredTime }}</div>
                            </template>
                            <template v-else-if="t.status === 3">
                                <span slot="trigger" class="reject_text_p"
                                    ><b>{{ t.statusStr }}</b></span
                                >
                                <fs-popover position="bottom" v-if="t.joinFromType === 1">
                                    <div>{{ $c("pages.UserSetting.userSetting.refusedThisApplication") }}</div>
                                </fs-popover>
                                <fs-popover position="bottom" v-else>
                                    <div>{{ $c("pages.UserSetting.userSetting.rejectedReason.case1") }}</div>
                                </fs-popover>
                            </template>
                            <template v-else>
                                <span
                                    ><b>{{ t.statusStr }}</b></span
                                >
                            </template>
                        </td>

                        <td class="table_body_item tr_right" style="white-space: nowrap">
                            <fs-button v-if="t.buttonList.accept" type="blackline" @click="handelOption(7, t)">{{ $c("pages.UserSetting.newSetting.accept") }}</fs-button>
                            <fs-popover isFocusPopper isAccessible position="right" :icon="false" v-if="t.showOption" trigger="click" :closeIcon="false" :popperStyle="{ padding: '0 20px', 'z-index': 80 }">
                                <span slot="trigger" class="iconfont btn_trigger">&#xf113;</span>
                                <ul class="option_list">
                                    <li class="list_item" v-if="t.buttonList.editRole" @click="handelOption(1, t)">{{ $c("pages.UserSetting.newSetting.editRole") }}</li>
                                    <li class="list_item" v-if="t.buttonList.addToTeam" @click="handelOption(2, t)">{{ $c("pages.UserSetting.newSetting.addToTeam") }}</li>
                                    <li class="list_item" v-if="t.buttonList.removeUser" @click="handelOption(3, t)">{{ $c("pages.UserSetting.userSetting.btn.removeUser") }}</li>
                                    <li class="list_item" v-if="t.buttonList.resendInvitation" @click="handelOption(4, t)">{{ $c("pages.UserSetting.newSetting.reSend") }}</li>
                                    <li class="list_item" v-if="t.buttonList.deleteUser" @click="handelOption(5, t)">{{ $c("pages.UserSetting.newSetting.delete") }}</li>
                                    <li class="list_item" v-if="t.buttonList.refuse" @click="handelOption(6, t)">{{ $c("pages.UserSetting.newSetting.refuse") }}</li>
                                </ul>
                            </fs-popover>
                            <span v-else> &nbsp;</span>
                        </td>
                    </tr>
                </tbody>
            </table>
            <div class="empty_box" v-if="!userList.length && !loading">
                <b>{{ $c("pages.UserSetting.newSetting.userEmpty.title") }}</b>
                <p>{{ $c("pages.UserSetting.newSetting.userEmpty.content") }}</p>
            </div>
        </div>
        <div class="table_m">
            <ul class="table_m_item" v-for="t in userList" :key="t.id">
                <li>
                    <p>
                        <b>{{ $c("pages.UserSetting.newSetting.name") }}:</b>
                        <span>{{ t | getUserName }}</span>
                    </p>
                </li>
                <li>
                    <p>
                        <b>{{ $c("pages.UserSetting.userSetting.user_table.emaiAddress") }}:</b>
                        <span>{{ t.customersEmailAddress }}</span>
                    </p>
                </li>
                <li>
                    <p>
                        <b>{{ $c("pages.UserSetting.newSetting.roles") }}:</b>
                        <span>{{ t.roleStr }}</span>
                    </p>
                </li>
                <li>
                    <p>
                        <b>{{ $c("pages.UserSetting.userSetting.user_table.status") }}:</b>

                        <template v-if="t.status === 1">
                            <fs-popover v-if="t.joinFromType === 1">
                                <span slot="trigger" class="reject_text"
                                    ><p>{{ t.statusStr }}</p></span
                                >
                                <div>{{ $c("pages.UserSetting.userSetting.approvalToJoin") }}</div>
                            </fs-popover>
                        </template>
                        <template v-else-if="t.status === 3">
                            <fs-popover>
                                <span slot="trigger" class="reject_text"
                                    ><p>{{ t.statusStr }}</p></span
                                >
                                <div v-if="t.joinFromType === 1">{{ $c("pages.UserSetting.userSetting.refusedThisApplication") }}</div>
                                <div v-else>{{ $c("pages.UserSetting.userSetting.rejectedReason.case1") }}</div>
                            </fs-popover>
                        </template>
                        <template v-else>
                            <span>{{ t.statusStr }}</span>
                        </template>
                    </p>
                </li>
                <li>
                    <p>
                        <b>{{ $c("pages.UserSetting.userSetting.group") }}:</b>
                        <span v-if="t.teamNameList">{{ t.teamNameList.join(",") }}</span>
                    </p>
                </li>
                <li class="options" v-if="t.showOption">
                    <div class="other_box" v-if="t.showMPopoverList && t.mPopoverList">
                        <fs-popover :icon="false" :isAlwaysShowBubbles="true" trigger="click" :closeIcon="false" :popperStyle="{ padding: '0 20px' }">
                            <span slot="trigger" class="iconfont">&#xf113;</span>
                            <ul class="option_list">
                                <li class="list_item" v-if="t.mPopoverList.editRole" @click="handelOption(1, t)">{{ $c("pages.UserSetting.newSetting.editRole") }}</li>
                                <li class="list_item" v-if="t.mPopoverList.addToTeam" @click="handelOption(2, t)">{{ $c("pages.UserSetting.newSetting.addToTeam") }}</li>
                                <li class="list_item" v-if="t.mPopoverList.removeUser" @click="handelOption(3, t)">{{ $c("pages.UserSetting.userSetting.btn.removeUser") }}</li>
                                <li class="list_item" v-if="t.mPopoverList.resendInvitation" @click="handelOption(4, t)">{{ $c("pages.UserSetting.newSetting.reSend") }}</li>
                                <li class="list_item" v-if="t.mPopoverList.deleteUser" @click="handelOption(5, t)">{{ $c("pages.UserSetting.newSetting.delete") }}</li>
                            </ul>
                        </fs-popover>
                    </div>
                    <div class="btn" v-if="t.mBtnList">
                        <fs-button type="blackline" v-if="t.mBtnList.editRole" class="list_item" @click="handelOption(1, t)">{{ $c("pages.UserSetting.newSetting.editRole") }}</fs-button>
                        <fs-button type="blackline" v-if="t.mBtnList.addToTeam" class="list_item" @click="handelOption(2, t)">{{ $c("pages.UserSetting.newSetting.addToTeam") }}</fs-button>
                        <fs-button type="blackline" v-if="t.mBtnList.removeUser" class="list_item" @click="handelOption(3, t)">{{ $c("pages.UserSetting.userSetting.btn.removeUser") }}</fs-button>
                        <fs-button type="blackline" v-if="t.mBtnList.resendInvitation" class="list_item" @click="handelOption(4, t)">{{ $c("pages.UserSetting.newSetting.reSend") }}</fs-button>
                        <fs-button type="blackline" v-if="t.mBtnList.deleteUser" class="list_item" @click="handelOption(5, t)">{{ $c("pages.UserSetting.newSetting.delete") }}</fs-button>
                        <fs-button type="blackline" v-if="t.mBtnList.refuse" class="list_item" @click="handelOption(6, t)">{{ $c("pages.UserSetting.newSetting.refuse") }}</fs-button>
                        <fs-button type="blackline" v-if="t.mBtnList.accept" class="list_item" @click="handelOption(7, t)">{{ $c("pages.UserSetting.newSetting.accept") }}</fs-button>
                    </div>
                </li>
            </ul>
            <div class="empty_m" v-if="!userList.length">
                <b>{{ $c("pages.UserSetting.newSetting.userEmpty.title") }}</b>
                <p>{{ $c("pages.UserSetting.newSetting.userEmpty.content") }}</p>
            </div>
        </div>
        <!-- 分页器 -->
        <fs-pagination class="pagination_box" @changeCurrentPage="changeCurrentPage" :pageConfig="pageConfig"></fs-pagination>
        <!-- Create New User -->
        <fs-popup
            :loading="popupLoading"
            :show="isShowPopup"
            isMDrawer
            :class="{ is_link_popup: popupType === 2 }"
            :title="popupType === 1 ? $c('pages.UserSetting.userSetting.createNewUser') : $c('pages.UserSetting.userSetting.linkUser')"
            @close="initPopup">
            <div class="new_user_popup">
                <template v-if="!showSuccess && !showSecondLink.show">
                    <div class="popup_container">
                        <div class="popup_container_title">
                            <b>{{ $c("pages.UserSetting.userSetting.linkUser") }}</b>
                            <span class="popup_container_box" @click="initPopup">
                                <i class="iconfont iconfont_close">&#xf30a;</i>
                            </span>
                        </div>
                        <p class="tips">
                            <span class="iconfont">&#xe718;</span>
                            <span>{{ popupType === 1 ? $c("pages.UserSetting.newSetting.tips.text1") : $c("pages.UserSetting.newSetting.tips.text2") }}</span>
                        </p>
                        <div class="creat_form">
                            <!-- 关联用户 -->
                            <div v-if="popupType === 2" class="user_info">
                                <p>{{ $c("pages.UserSetting.userSetting.popupForm.userInformation") }}</p>
                                <LinkUserGroup ref="linkUserRef" />
                            </div>
                            <!-- 创建用户 -->
                            <template v-else>
                                <h3>{{ $c("pages.UserSetting.userSetting.popupForm.userInformation") }}</h3>
                                <ul v-if="website === 'cn'">
                                    <li class="form_item">
                                        <span>{{ $c("pages.MyAccount.settings.userName") }}</span>
                                        <input type="text" v-model.trim="form.account" maxlength="33" @blur="validateError('first_name')" />
                                        <validate-error :error="errors.account"></validate-error>
                                    </li>
                                    <li class="form_item">
                                        <span>{{ $c("form.form.phone_number") }}</span>
                                        <input type="text" v-model="form.phone" maxlength="12" @blur="validateError('phone')" />
                                        <validate-error :error="errors.phone"></validate-error>
                                    </li>
                                </ul>
                                <ul v-else>
                                    <li class="form_item">
                                        <span>{{ $c("pages.UserSetting.newSetting.firstName") }}</span>
                                        <input type="text" v-model.trim="form.first_name" maxlength="33" @blur="validateError('first_name')" />
                                        <validate-error :error="errors.first_name"></validate-error>
                                    </li>
                                    <li class="form_item">
                                        <span>{{ $c("pages.UserSetting.newSetting.lastName") }}</span>
                                        <input type="text" v-model="form.last_name" maxlength="33" @blur="validateError('last_name')" />
                                        <validate-error :error="errors.last_name"></validate-error>
                                    </li>
                                </ul>
                                <div class="form_item">
                                    <span>{{ $c("pages.UserSetting.newSetting.businessEmail") }}</span>
                                    <input type="text" v-model.trim="form.email" @blur="validateError('email')" />
                                    <validate-error :error="errors.email"></validate-error>
                                </div>
                            </template>

                            <div class="form_item_team">
                                <span>{{ $c("pages.UserSetting.newSetting.popupText.selectTeamLink") }}</span>
                                <div class="form_item">
                                    <fs-checkbox-select :options="teamList" :placeholder="$t('components.qa.select.option01')" @change="handleCheckChange"></fs-checkbox-select>
                                </div>
                            </div>
                            <p class="developer_tips" v-if="developerTips">
                                <span class="iconfont">&#xe718;</span><span> {{ developerTips }}</span>
                            </p>
                        </div>
                        <!-- Assign Roles -->
                        <assign-roles @change="rolesChange" v-model="form.role" :isDeveloperDisabled="isDeveloperDisabled"></assign-roles>
                    </div>
                    <div class="btn">
                        <fs-button type="grayline" @click="initPopup">{{ $c("pages.UserSetting.userSetting.btn.cancel") }}</fs-button>
                        <fs-button v-if="popupType === 1" :disabled="form.role == 0" @click="sureCreate">{{ $c("pages.AddressBook.create") }}</fs-button>
                        <fs-button v-else @click="sureLink" :disabled="form.role == 0">{{ $c("pages.UserSetting.userSetting.btn.submit") }}</fs-button>
                    </div>
                </template>
                <template v-if="showSecondLink.show">
                    <p>{{ showSecondLink.content }}</p>
                    <div class="btn secondlink_btn">
                        <fs-button type="grayline" @click="handelSecondClose">{{ $c("pages.UserSetting.userSetting.btn.cancel") }}</fs-button>
                        <fs-button @click="handleSecondLink">{{ $c("pages.UserSetting.newSetting.link") }}</fs-button>
                    </div>
                </template>
            </div>
        </fs-popup>
        <link-result-popup :show="isShowLinkResultPopup && showSuccess" :userListErrors="errorLinkList" @close="closeResult" />
        <result-popup :show="!isShowLinkResultPopup && showSuccess" :resultErrors="resultErrors" :info="submitResult" :type="errorType" @close="closeResult" @linkUser="linkUser"></result-popup>
        <option-popup
            :show="optionType !== 0"
            :isGroupOne="currentGroupIsOneLevel"
            :info="currentUser"
            :toTeamList="toTeamList"
            :type="optionType"
            @close="initPopup"
            @success="handleSuccess"
            :developerTips="developerTips"></option-popup>
        <fs-popup :show="isShowAccept" :title="$c('pages.UserSetting.userSetting.joinApplication')" @close="initPopup" :isMDrawer="true">
            <div class="new_user_popup">
                <template>
                    <div class="popup_container">
                        <!-- <p class="tips" v-html="$c('pages.UserSetting.userSetting.pleaseRefuse').replace(/test/, currentUser.customersEmailAddress).replace('xxxx', `mailto:<EMAIL>`)">
                            <span class="iconfont">&#xf039;</span>
                        </p> -->
                        <div class="tips">
                            <span class="iconfont">&#xf039;</span>
                            <p v-html="$c('pages.UserSetting.userSetting.pleaseRoles').replace(/test/, currentUser.customersEmailAddress).replace('xxxx', `mailto:${currentUser.customersEmailAddress}`)"></p>
                        </div>

                        <div class="creat_form">
                            <div class="form_item_team">
                                <h3>{{ $t("pages.UserSetting.newSetting.popupText.selectTeam") }}</h3>
                                <div class="form_item">
                                    <FsCheckboxAcceptSelect :options="teamAcceptList" :placeholder="$t('components.qa.select.option01')" @change="handleCheckChange"></FsCheckboxAcceptSelect>
                                </div>
                            </div>
                        </div>

                        <assign-roles @change="rolesChange" v-model="form.role"></assign-roles>
                    </div>
                    <div class="btn">
                        <fs-button type="grayline" @click="initPopup">{{ $c("pages.UserSetting.userSetting.btn.cancel") }}</fs-button>
                        <fs-button @click="sureAccept" :disabled="form.role == 0">{{ $c("pages.UserSetting.userSetting.btn.submit") }}</fs-button>
                    </div>
                </template>
            </div>
        </fs-popup>

        <fs-popup :show="isShowReject" :title="$c('pages.UserSetting.userSetting.joinApplication')" @close="initPopup" :isMDrawer="true">
            <div class="new_user_popup">
                <template>
                    <!-- <p>I agree to FS's   <a  :href="`mailto:${managerInfo.admin_email}`">{{ managerInfo.admin_email }}</a></p> -->
                    <p class="content" v-html="$c('pages.UserSetting.userSetting.pleaseRefuse').replace(/test/, currentUser.customersEmailAddress).replace('xxxx', `mailto:${currentUser.customersEmailAddress}`)"></p>
                    <div class="btn">
                        <fs-button type="grayline" @click="initPopup">{{ $c("pages.UserSetting.userSetting.btn.cancel") }}</fs-button>
                        <fs-button @click="updateMember">{{ $c("pages.UserSetting.userSetting.btn.submit") }}</fs-button>
                    </div>
                </template>
            </div>
        </fs-popup>
        <!-- Owner编辑时不可编辑弹窗 -->
        <fs-popup :show="ownerPopupShow" :title="ownerPopupTitle" width="480px" @close="handleOwnerPopupClose">
            <div class="owner_popup">
                <p class="tips_box" v-if="ownerPopupType == 3">{{ $t("pages.UserSetting.newSetting.popupText.devOwnerRemove").replace("xxxx", currentUser.customersEmailAddress || "") }}</p>
                <p class="tips_box" v-else>{{ $t("pages.UserSetting.newSetting.popupText.devChangeRole").replace("xxxx", currentUser.customersEmailAddress || "") }}</p>
            </div>
        </fs-popup>
        <!-- 创建用户和关联用户弹窗 -->
        <AddUserPopup :isShowPopup="showAddUserPopup" @close="initPopup"></AddUserPopup>
    </div>
</template>

<script>
import FsSelect from "@/components/FsSelect/FsSelect.vue"
import FsButton from "@/components/FsButton/FsButton.vue"
import FsPopover from "@/components/FsPopover/index.vue"
import FsPagination from "@/components/FsPagination/FsPagination.vue"
import FsPopup from "@/components/FsPopupNew/FsPopupNew.vue"
import ResultPopup from "./Popup/NewReultPopup.vue"
import OptionPopup from "./Popup/OptionPopup.vue"
import AssignRoles from "./AssignRoles/AssignRoles.vue"
import RoleSelect from "./RoleSelect.vue"
import LinkResultPopup from "./Popup/LinkResult.vue"
import userSettingAPiMinxi from "../UserSettingAPI"
import LinkUserGroup from "@/components/LinkUserGroup/LinkUserGroup.vue"
import ValidateError from "@/components/ValidateError/ValidateError.vue"
import FsSelectSearch from "@/components/FsSelectSearch"
import FsCheckboxSelect from "@/pages/UserSettings/components/FsCheckboxSelect/FsCheckboxLinkSelect.vue"
import FsCheckboxAcceptSelect from "@/pages/UserSettings/components/FsCheckboxSelect/FsCheckboxSelect.vue"
import { email_valdate, cn_mobile_tel } from "@/constants/validate"
import { mapState, mapActions } from "vuex"
import AddUserPopup from "./AddUserPopup.vue"
export default {
    props: {
        showNavOption: {
            type: Boolean,
            default: true,
        },
        teamInfo: {
            type: Object,
            default: () => {},
        },
    },
    mixins: [userSettingAPiMinxi],
    components: {
        FsSelect,
        FsButton,
        FsPopover,
        FsPagination,
        FsPopup,
        ResultPopup,
        OptionPopup,
        AssignRoles,
        RoleSelect,
        ValidateError,
        FsCheckboxSelect,
        FsCheckboxAcceptSelect,
        LinkUserGroup,
        LinkResultPopup,
        FsSelectSearch,
        AddUserPopup,
    },
    data() {
        return {
            loading: false,
            popupLoading: false,
            //弹窗类型
            popupType: 0, //1-create  2-link   0-null
            isShowAccept: false,
            isShowReject: false,
            showSuccess: false,
            searchKeys: "",
            searchStatus: 2,
            searchRoleNotTeam: "",
            roleList: [
                { name: this.$c("pages.UserSetting.newSetting.userOption.allRoles"), value: "all" },
                { name: this.$c("pages.UserSetting.newSetting.userOption.admin"), value: "administrator" },
                { name: this.$c("pages.UserSetting.newSetting.userOption.buyer"), value: "buyer" },
                { name: this.$c("pages.UserSetting.newSetting.userOption.finance"), value: "finance" },
            ],
            tableHeaderOption: [
                { key: "name", name: this.$c("pages.UserSetting.newSetting.name") },
                { key: "email", name: this.$c("pages.UserSetting.userSetting.user_table.emaiAddress") },
                { key: "roles", name: this.$c("pages.UserSetting.newSetting.roles") },
                { key: "status", name: this.$c("pages.UserSetting.userSetting.user_table.status") },
                { key: "option", name: "" },
            ],
            statausOptions: [
                { name: this.$c("pages.UserSetting.newSetting.statusOption.allStatus"), value: 0 },
                { name: this.$c("pages.UserSetting.newSetting.statusOption.active"), value: 2 },
                { name: this.$c("pages.UserSetting.newSetting.statusOption.pending"), value: 1 },
                { name: this.$c("pages.UserSetting.newSetting.statusOption.rejected"), value: 3 },
                { name: this.$c("pages.UserSetting.newSetting.statusOption.expire"), value: 4 },
            ],
            userList: [],
            pageConfig: {
                pageSize: 10,
                pageNo: 1,
                total: 1,
            },
            form: {
                first_name: "",
                last_name: "",
                email: "",
                account: "",
                phone: "",
                role: 0,
                roleAccept: "",
                team: [],
            },
            errors: {
                first_name: "",
                last_name: "",
                email: "",
                account: "",
                phone: "",
            },
            errorType: 0, //提交的结果类型   /1-create  2-link   0-null
            optionType: 0, //0 默认不展示  1:Edit Role  2:Add to another team 3:Remove User   4:Resend  5:Delete
            currentUser: {},
            toTeamList: [],
            isCreateUserPower: false,
            submitResult: {
                //创建/关联用户结果类型
                isSuccess: false,
                // 创建失败提示语
                message: "",
            },
            showSecondLink: {
                show: false,
                content: "",
            }, //link User时展示二次确认
            // 错误类型
            resultErrors: "",
            toTeamListLink: [],
            // link失败用户列表
            errorLinkList: [],
            //是否展示link结果弹窗
            isShowLinkResultPopup: false,
            //当前数据的是否是一级组
            currentGroupIsOneLevel: false,
            ownerPopupShow: false,
            ownerPopupType: 0,
            //是否展示新的登录和注册一体弹窗
            showAddUserPopup: false,
        }
    },
    methods: {
        ...mapActions({
            getUserInfo: "userInfo/getUserInfo",
        }),
        initForm() {
            this.form = {
                first_name: "",
                last_name: "",
                email: "",
                account: "",
                role: 0,
                team: [],
            }
        },
        changeCurrentPage(val) {
            this.pageConfig.pageNo = val
            this.getUserList()
        },
        handleCheckChange(val) {
            console.log(val, "valsss")
            this.form.team = val
        },
        rolesChange(val) {
            this.form.role = val
            const obj = {
                1: "administrator",
                2: "buyer",
                3: "finance",
            }
            this.form.roleAccept = obj[val]
        },
        async sureCreate() {
            if (!this.validatorAll()) {
                return
            }
            this.gaFunctin("create_new_user", "Confirm Submit")
            let params = {}
            if (this.website === "cn") {
                const { account, email, role, phone } = this.form
                params = {
                    first_name: account,
                    email,
                    role,
                    phone,
                    // team_id: this.teamId || this.ininTeamID.id,
                    // type: this.teamType || this.ininTeamID.type,
                    team_ids: this.form.team,
                }
            } else {
                const { first_name, last_name, role, email } = this.form
                params = {
                    first_name,
                    last_name,
                    email,
                    role,
                    // team_id: this.teamId || this.ininTeamID.id,
                    // type: this.teamType || this.ininTeamID.type,
                    team_ids: this.form.team,
                }
            }
            this.popupLoading = true
            const { code, errors, message } = await this.ADD_USER_ORGANIZATION_API(params).finally(() => (this.popupLoading = false))
            if (code === 200) {
                this.initForm()
                this.submitResult.isSuccess = true
            } else {
                this.errorType = 1
                this.resultErrors = errors.errorType
                this.submitResult.message = message
                this.gaFunctin("create_new_user_result", this.resultErrors)
            }
            this.popupType = 0
            this.showSuccess = true
        },
        async sureAccept() {
            this.loading = true
            this.gaFunctin("create_join_application", "join_application_btn")
            const params = {
                id: this.currentUser.id,
                toRoleNotTeam: this.form.roleAccept,
                toTeamIdList: this.form.team,
                customersNumberNew: this.currentUser.customersNumberNew,
            }
            const { code, message } = await this.UPDATE_BATCHUSER_ROLE_API(params)
            this.loading = false
            if (code === 200) {
                this.initPopup()
                this.getUserList()
            }
        },
        async updateMember() {
            this.gaFunctin("reject_check", "reject_check_btn")
            let params = {}

            params = {
                id: this.currentUser.id,
                customersNumberNew: this.currentUser.customersNumberNew,
                status: 3,
            }
            const { code, errors, message } = await this.UPCATE_MEMBER_STATUS_API(params)
            if (code === 200) {
                this.showSuccess = true
                this.submitResult.isSuccess = true
                this.initPopup()
                this.getUserList()
            } else {
                this.resultErrors = errors.errorType
                this.submitResult.message = message
            }
        },
        async sureLink(option = {}) {
            const target = this.$refs.linkUserRef
            if (!target) return
            const userList = target.handleValidateAll()
            if (!userList) return
            if (!option.force) {
                this.gaFunctin("link_user", "Confirm Submit")
            }
            const { role, team: team_ids } = this.form
            const params = { role, team_ids, userList }
            this.popupLoading = true
            const { code, data, status, message, errors } = await this.LINK_USER_ORGANIZATION_API({ ...params, ...option }).finally(() => (this.popupLoading = false))
            if (code == 200 && status == "success") {
                this.popupType = 0
                this.showSuccess = true
                this.submitResult.isSuccess = true
                //刷新页面
                // this.getUserList()
                const { failData: errorLinkList = [] } = data
                this.errorLinkList = errorLinkList
                this.gaFunctin("link_user_result", "success")
            } else if (code == 400 && message) {
                this.isShowLinkResultPopup = false
                const { errorType } = errors
                if (["term_confirm", "admin_confirm"].includes(errorType)) {
                    this.showSecondLink.show = true
                    this.showSecondLink.content = message
                    this.resultErrors = errors.errorType
                } else {
                    this.popupType = 0
                    this.showSuccess = true
                    this.submitResult.isSuccess = false
                    this.submitResult.message = message
                    this.resultErrors = errors.errorType
                    this.errorType = 2
                }
                this.gaFunctin("link_user_result", errorType)
            } else if (res.code == 422) {
                const target = this.$refs.linkUserRef
                errors &&
                    Object.entries(errors).forEach(([key, [message] = []]) => {
                        if (target && key && message) {
                            target.setValidateError(key, message)
                        }
                    })
            }
        },
        //非中文站点合并创建和关联
        handleAddUser() {
            this.showAddUserPopup = true
        },
        handleSecondLink() {
            this.gaFunctin("link_user", `Link Again_${this.resultErrors}`)
            this.sureLink({ force: 1 })
        },
        handelSecondClose() {
            this.gaFunctin("link_user", `Cancel Link Again_${this.resultErrors}`)
            this.initPopup(0, false)
        },
        initPopup(val = 0, hasGa = true) {
            if (val === 1 || val === 2) {
                this.showPopupLinkTeam()
                this.isShowLinkResultPopup = val === 2
            }
            if (hasGa) {
                //是否触发埋点
                if (val === 1) {
                    // 创建用户
                    this.gaFunctin("page_operate", "Create New User")
                }
                if (this.popupType === 1 && val === 0) {
                    // 取消创建
                    this.gaFunctin("create_new_user", "Cancel Submit")
                }
                if (val === 2) {
                    // 关联用户
                    this.gaFunctin("page_operate", "Link User")
                }
                if (this.popupType === 2 && val === 0) {
                    //取消关联
                    this.gaFunctin("link_user", "Cancel Submit")
                }
            }
            this.popupType = val
            this.showSuccess = false
            this.isShowAccept = false
            this.isShowReject = false
            this.errorType = 0
            this.optionType = 0
            this.showAddUserPopup = false
            this.showSecondLink.show = false
            this.showSecondLink.content = ""
            this.initForm()
        },
        async showPopupLinkTeam() {
            const params = {}
            this.$axios
                .get("/api/companyAccount/getARoleTeams", params)
                .then((res) => {
                    if (res.code === 200) {
                        //处理成功的逻辑
                        this.toTeamListLink = res.data
                    }
                })
                // .catch(({ errors: {} }) => {
                .catch((err) => {
                    console.log(err, "errors")
                    this.resultType = err.errors?.errorType || err.errors?.code + ""
                    this.gaFunctin("remove_user_result", "fail")
                    const { code, errors, message } = err
                    if (code === 400 && errors.code === 3001) {
                        this.isRemoveResult = true
                        this.removeResultType = 2
                    } else {
                        this.$message.error(message)
                    }
                })
        },
        gaFunctin(eventAction, eventLabel) {
            console.log("页面", this.pageGroup)
            window.dataLayer &&
                window.dataLayer.push({
                    event: "uaEvent",
                    eventCategory: this.pageGroup,
                    eventAction,
                    eventLabel,
                    nonInteraction: false,
                })
        },
        clearSearch() {
            this.searchKeys = ""
            this.getUserList()
        },
        closeResult() {
            this.getUserList()
            this.initPopup()
        },
        handelOption(type, val) {
            console.log(type, val, "22222222")
            if (type === 1) {
                //编辑用户权限
                this.gaFunctin("page_operate", "Edit User")
                if (val.openApiOwner) {
                    this.ownerPopupShow = true
                    this.ownerPopupType = type
                    this.currentUser = val
                    return
                }
            } else if (type === 3) {
                //removerUser
                this.gaFunctin("page_operate", "Remove User")
                if (val.openApiOwner) {
                    this.ownerPopupShow = true
                    this.ownerPopupType = type
                    this.currentUser = val
                    return
                }
            } else if (type === 2) {
                // add to Team
                this.gaFunctin("page_operate", "Add User to Team")
            } else if (type === 5) {
                // delete
                this.gaFunctin("page_operate", "Delete User")
            }
            if (type === 4) {
                const { id } = val
                this.reSendInvite(id, val)
            } else if (type === 6) {
                this.isShowReject = true
                this.currentUser = val
            } else if (type === 7) {
                this.showPopupLinkTeam()
                this.isShowAccept = true
                this.currentUser = val
                this.gaFunctin("page_operate", "Accept User")
            } else {
                this.optionType = type
                this.currentUser = val
            }
        },
        // 重新发送邀请
        async reSendInvite(id, val) {
            this.loading = true
            this.gaFunctin("page_operate", "Re-sent Invitation")
            const { code, message } = await this.RESEND_ACTIVATE_EMAIL_API(id)
            this.loading = false
            if (code === 200) {
                this.currentUser = val
                this.optionType = 4
                this.gaFunctin("resent_invitation_result", "success")
            } else {
                this.$message.error(message)
                this.gaFunctin("resent_invitation_result", "fail")
            }
        },
        selectRoleChange(val) {
            console.log(val, "valsss")
            this.searchRoleNotTeam = val ? val : ""
            this.getUserList()
        },
        statusChange(val) {
            this.searchStatus = val.value
            this.getUserList()
        },
        async getUserList() {
            this.loading = true
            const params = {
                page: this.pageConfig.pageNo,
                page_num: this.pageConfig.pageSize,
                searchRoleNotTeam: this.searchRoleNotTeam === "all" ? "" : this.searchRoleNotTeam,
                searchStatus: this.searchStatus,
                searchTeamId: this.teamId,
                search: this.searchKeys,
            }
            const res = await this.GET_USER_LIST_API(params)
            this.loading = false
            const {
                code,
                data: { memberList, toTeamList, isCreateUserPower, currentType },
            } = res
            if (code === 200) {
                this.getUserInfo()
                const { list, totalCount } = memberList
                let newList = []
                if (list && list.length) {
                    newList = list?.map((item) => {
                        const { buttonList = [] } = item
                        let showOption = false
                        let showOptionArr = Object.values(buttonList)
                        // 兼容M端样式
                        let mBtnList = {}
                        let mPopoverList = {}
                        let showMPopoverList = false
                        if (showOptionArr.length && showOptionArr.some((i) => i)) {
                            showOption = true
                            // 枚举按钮权限
                            const arr = ["editRole", "addToTeam", "resendInvitation", "deleteUser", "refuse", "accept", "removeUser"]
                            const mBtnArr = []
                            arr.forEach((k) => {
                                if (buttonList[k]) {
                                    if (mBtnArr.length < 2) {
                                        mBtnArr.push(k)
                                        mBtnList = { ...mBtnList, [k]: true }
                                        mPopoverList = { ...mPopoverList, [k]: false }
                                    } else {
                                        mBtnList = { ...mBtnList, [k]: false }
                                        mPopoverList = { ...mPopoverList, [k]: true }
                                        showMPopoverList = showMPopoverList ? showMPopoverList : true
                                    }
                                } else {
                                    mBtnList = { ...mBtnList, [k]: false }
                                    mPopoverList = { ...mPopoverList, [k]: false }
                                }
                            })
                        }
                        return { ...item, showOption: showOption, mBtnList, mPopoverList, showMPopoverList }
                    })
                } else {
                    newList = []
                }
                this.isCreateUserPower = isCreateUserPower
                this.userList = newList
                this.pageConfig.total = totalCount
                this.toTeamList = toTeamList
                this.currentGroupIsOneLevel = currentType === 1
            } else {
            }
        },
        handleSearch() {
            this.getUserList()
        },
        linkUser() {
            // console.log(111111, "result_popup_content")
            this.initPopup()
            this.popupType = 2
        },
        // 校验函数
        validateError(type) {
            if (type === "first_name") {
                //验证firstName
                let str = ""
                let first_name = ""
                if (this.website === "cn") {
                    first_name = this.form.account
                } else {
                    first_name = this.form.first_name
                }
                if (first_name.length < 1) {
                    str = this.$c("pages.UserSetting.userSetting.popup.validateError.text1")
                } else if (first_name.length < 2) {
                    str = this.$c("pages.UserSetting.userSetting.popup.validateError.text2")
                } else if (first_name.length > 40) {
                    str = this.$c("pages.UserSetting.userSetting.popup.validateError.text3")
                } else {
                    str = ""
                }
                if (this.website === "cn") {
                    this.errors.account = str ? "请输入你的用户名" : ""
                } else {
                    this.errors.first_name = str
                }
            } else if (type === "last_name") {
                //验证lastName
                let str = ""
                const { last_name } = this.form
                if (last_name.length < 1) {
                    str = this.$c("pages.UserSetting.userSetting.popup.validateError.text4")
                } else if (last_name.length < 2) {
                    str = this.$c("pages.UserSetting.userSetting.popup.validateError.text5")
                } else if (last_name.length > 40) {
                    str = this.$c("pages.UserSetting.userSetting.popup.validateError.text6")
                } else {
                    str = ""
                }
                this.errors.last_name = str
            } else if (type === "email") {
                //验证邮箱
                let str = ""
                const { email } = this.form
                if (email.length < 1) {
                    str = this.$c("form.validate.email.email_required")
                } else if (!email_valdate.test(email)) {
                    str = this.$c("form.validate.email.email_valid")
                } else {
                    str = ""
                }
                this.errors.email = str
            } else if (type === "phone") {
                //验证电话
                let str = ""
                if (this.website === "cn") {
                    const { phone = "" } = this.form
                    if (phone.length < 1) {
                        str = this.$c("form.validate.phone.phone_required")
                    } else if ((this.website === "cn" && phone.length && !cn_mobile_tel.test(phone)) || phone.length > 13) {
                        str = "请输入正确的电话号码"
                    } else {
                        str = ""
                    }
                }
                this.errors.phone = str
            }
        },
        validatorAll() {
            let arr = []
            if (this.website === "cn") {
                arr = ["first_name", "email", "phone"]
            } else {
                arr = ["first_name", "last_name", "email"]
            }
            arr.forEach((k) => {
                this.validateError(k)
            })
            const result = Object.values(this.errors)
            // console.log(result, "result")
            const isPass = result.some((i) => i)
            return !isPass
        },
        handleSuccess() {
            this.initPopup()
            this.getUserList()
        },
        handleOwnerPopupClose() {
            this.ownerPopupShow = false
            this.ownerPopupType = 0
        },
    },
    computed: {
        ...mapState({
            pageGroup: (state) => state.ga.pageGroup,
            website: (state) => state.webSiteInfo.website,
            ininTeamID: (state) => {
                let id = 0
                let type = 1
                const {
                    companyInfo: { allTeamList = [] },
                } = state.userInfo.userInfo
                if (allTeamList && allTeamList[0]) {
                    id = allTeamList[0].id
                    type = allTeamList[0].type
                }
                return { id, type }
            },
        }),
        teamAcceptList() {
            // console.log(this.toTeamList, "this.toTeamList")
            if (this.toTeamListLink.length > 0) {
                const arr = this.toTeamListLink.map(({ id, name, type }) => ({ name, value: id, type }))
                console.log(arr, "this.toTeamList")
                return arr
            } else {
                return []
            }
        },
        teamList() {
            // console.log(this.toTeamList, "this.toTeamList")
            if (this.toTeamListLink.length > 0) {
                // const arr = this.toTeamListLink.map(({ id, name, type }) => ({ name, value: id, type }))
                const arr = this.toTeamListLink.map(({ id, name, type }, index) => {
                    console.log(this.teamId, "teamid")
                    // 判断当前元素是否为选中
                    if (this.teamId == 0) {
                        const isCheckElement = index === 0
                        return {
                            name,
                            value: id,
                            type,
                            checked: isCheckElement ? true : false,
                        }
                    } else {
                        const isCheckElement = id === this.teamId
                        return {
                            name,
                            value: id,
                            type,
                            checked: isCheckElement ? true : false,
                        }
                    }
                })
                return arr
            } else {
                return []
            }
        },
        isShowPopup() {
            return [1, 2].includes(this.popupType)
        },
        teamId() {
            console.log(this.teamInfo, "this.teamInfo")
            return this.teamInfo.id || 0
        },
        teamType() {
            return this.teamInfo.type || 0
        },
        //状态筛选是否点击
        isSelectStatus() {
            return this.searchStatus !== 0
        },
        //developer是否禁用状态
        isDeveloperDisabled() {
            const result = this.form.team.some((item) => item.type !== 1)
            return result
        },
        developerTips() {
            // 筛选出teamList中的一级组
            const firstLevelGroup = this.teamList.filter((item) => item.type === 1)
            if (firstLevelGroup.length) {
                const listString = firstLevelGroup.map((item) => item.name)?.join(";") || ""
                return this.$c("pages.UserSetting.newSetting.assignRoles.developerTips").replace("XXXX", listString)
            } else {
                return ""
            }
        },
        ownerPopupTitle() {
            return this.ownerPopupType === 3 ? this.$t("pages.UserSetting.newSetting.popupText.title6") : this.$t("pages.UserSetting.newSetting.popupText.title4")
        },
    },
    mounted() {
        this.getUserList()
    },
    watch: {
        teamId(val) {
            this.getUserList()
        },
    },
    filters: {
        getUserName({ customersFirstname, customersLastname }) {
            return `${customersFirstname}.${customersLastname}`
        },
    },
}
</script>

<style lang="scss" scoped>
.user_page {
    padding-top: 28px;
    .nav {
        display: flex;
        // column-gap: 12px;
        .role_select {
            width: 220px;
        }
        .search_input {
            width: 400px;
            margin-left: 12px;
            margin-right: auto;
        }
        .nav_btn {
            display: flex;
            column-gap: 20px;
        }
    }
    .table {
        margin-top: 20px;
        box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.1);
        border-radius: 12px;
        width: 100%;
        > table {
            width: 100%;
        }
        @include font14;
        .table_header_item {
            background: #f7f7f7;
            padding: 12px 24px;
            text-align: left;
            &:first-of-type {
                border-radius: 12px 0 0 0px;
            }
            &:last-of-type {
                border-radius: 0 12px 0 0;
            }
            .status_box {
                display: flex;
                align-items: center;
            }
            .status_triger {
                font-weight: normal;
                font-size: 14px;
                color: $textColor3;
                &:hover {
                    color: $textColor1;
                }
                &.active {
                    color: $textColor1;
                }
            }
        }
        .table_body_item {
            padding: 20px 24px;
            border-bottom: 1px solid #e5e5e5;
            vertical-align: middle;
            &:first-of-type {
                width: 40%;
            }
            &:nth-of-type(2) {
                width: 10%;
            }
            &:nth-of-type(3) {
                width: 10%;
            }
            &:nth-of-type(4) {
                width: 35%;
            }
            &:last-of-type {
                width: 5%;
                text-align: right;
                vertical-align: middle;
            }
            .reject_text {
                margin-right: 4px;
            }
            .reject_text_p {
                margin-right: -4px;
            }
            .btn_trigger {
                display: flex;
                justify-content: center;
                align-items: center;
                width: 28px;
                height: 28px;
                font-size: 16px;
                color: #707070;
                padding: 6px;
                &:hover {
                    color: #19191a;
                    background-color: rgba(25, 25, 26, 0.04);
                    border-radius: 3px;
                }
            }
        }
        tbody {
            > tr {
                &:last-of-type {
                    .table_body_item {
                        border: none;
                    }
                }
                &:not(:last-of-type) {
                    .tr_left {
                        position: relative;
                        &::before {
                            content: "";
                            width: 24px;
                            height: 4px;
                            background-color: #fff;
                            position: absolute;
                            left: 0;
                            bottom: -2px;
                        }
                    }
                    .tr_right {
                        position: relative;
                        &::before {
                            content: "";
                            width: 24px;
                            height: 4px;
                            background-color: #fff;
                            position: absolute;
                            right: 0;
                            bottom: -2px;
                        }
                    }
                }
            }
        }
    }
    .table_m {
        display: none;
    }
    .pagination_box {
        margin-top: 20px;
    }
}
.empty_box {
    width: 100%;
    height: 384px;
    display: flex;
    flex-direction: column;
    text-align: center;
    justify-content: center;
    @include font16;
    row-gap: 8px;
    > p {
        color: $textColor3;
    }
}
.owner_popup {
    padding: 12px 24px 24px;
    color: #707070;
    max-width: 480px;
    @include font14;
}
.status_select_item {
    font-weight: normal;
    margin: -20px;
    padding: 7px 0;
    z-index: 15;
    position: relative;
    > li {
        cursor: pointer;
        padding: 7px 16px;
        color: $textColor1;
        &:hover {
            background-color: #f7f7f7;
        }
    }
}
.option_list {
    @include font13;
    margin-left: -20px;
    margin-right: -20px;
    padding: 4px;
    .list_item {
        cursor: pointer;
        min-width: max-content;
        @include font13();
        color: #19191a;
        padding: 8px 16px;
        text-align: center;
        position: relative;
        z-index: 12;
        &:hover {
            background-color: #f7f7f7;
        }
        &.no_pointer {
            cursor: default;
        }
        &:first-child {
            border-radius: 3px 3px 0 0;
        }
        &:last-child {
            border-radius: 0 0 3px 3px;
        }
    }
}

.teams_tips {
    color: $textColor3;
    @include font13;
}
.new_user_popup {
    // padding: 20px 32px;
    width: 680px;
    border-radius: 3px;
    .tips {
        margin: 16px 24px 20px;
        padding: 10px 16px;
        background-color: rgba(0, 96, 191, 0.04);
        display: flex;
        column-gap: 8px;
        @include font12;
        color: $textColor3;
        border-radius: 4px;
        .iconfont {
            color: #0060bf;
            // font-weight: 600;
        }
        @media (max-width: 768px) {
            margin: 16px 20px;
        }
    }
    .content {
        display: flex;
        column-gap: 8px;
        @include font14;
        color: #19191a;
        margin-bottom: 20px;
    }
    .creat_form {
        margin: 0 24px 16px;
        @media (max-width: 768px) {
            margin: 0 16px 16px;
        }
        > h3 {
            @include font16;
            margin-bottom: 8px;
            &.assign_roles {
                margin-top: 20px;
                display: flex;
                align-items: center;
                column-gap: 4px;
            }
        }

        > ul {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            column-gap: 20px;
            margin-bottom: 12px;
            align-items: flex-start;
        }
        .user_info {
            > p {
                font-weight: 600;
                @include font14;
            }
        }
        .form_item {
            display: grid;
            grid-template-columns: repeat(1, 1fr);
            row-gap: 4px;
            > span {
                @include font12;
            }
        }
        .form_item_team {
            display: grid;
            grid-template-columns: repeat(1, 1fr);
            row-gap: 4px;
            margin-top: 12px;
            > span {
                @include font12;
            }
            > h3 {
                @include font16;
                margin-bottom: 8px;
            }
        }
        .developer_tips {
            display: flex;
            align-items: center;
            @include font13;
            color: #707070;
            column-gap: 4px;
            margin-bottom: 16px;
            margin-top: 4px;
        }
        .radio_group {
            .radio_item {
                display: flex;
                column-gap: 8px;
                @include font14;
                margin-bottom: 20px;
                .radio_tips {
                    margin-top: 4px;
                    color: $textColor3;
                }
                .iconfont {
                    cursor: pointer;
                    position: relative;
                    &.disabled {
                        cursor: not-allowed;
                        color: #ccc;
                        background-color: #f7f7f7;
                    }
                }
                .disable_icon {
                    width: 18px;
                    height: 18px;
                    border: 1px solid #ccc;
                    background-color: #f7f7f7;
                    border-radius: 50%;
                    cursor: not-allowed;
                    margin-top: 3px;
                }
            }
        }
    }
    .btn {
        width: max-content;
        margin-left: auto;
        // display: flex;
        // justify-content: flex-end;
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        column-gap: 16px;
        padding: 0 24px 24px;
        @media (max-width: 768px) {
            padding: 0 16px 16px;
        }
    }
    .error_result {
        margin-bottom: 20px;
    }
}
.role_selec_m {
    display: none;
}
.secondlink_btn {
    margin-top: 40px;
}
.popup_container_title {
    display: none;
}
::v-deep .fs-popup {
    @media (max-width: 768px) {
        border-radius: 0 !important;
    }
}

::v-deep .fs-popup.isMDrawer .fs-popup-ctn {
    @media (max-width: 768px) {
        border-radius: 0;
    }
}

.is_link_popup {
    &::v-deep {
        .fs-popup-header {
            display: none;
        }
    }
    .popup_container_title {
        display: flex;
        justify-content: space-between;
        margin: 0 16px 16px;
        padding: 16px 0 16px 8px;
        border-bottom: 1px solid #dee0e3;
        b {
            font-size: 18px;
            line-height: 26px;
        }
        .iconfont {
            cursor: pointer;
        }
        .popup_container_box {
            width: 28px;
            height: 28px;
            padding: 4px;
            text-align: center;
            &:hover {
                border-radius: 4px;
                background: rgba(25, 25, 26, 0.04);
            }
        }
        .iconfont_close {
            width: 20px;
            height: 20px;
        }
        @media (max-width: 768px) {
            padding-left: 0;
        }
    }
}

@media (max-width: 768px) {
    .user_page {
        padding-top: 24px;
        .nav {
            flex-direction: column;
            row-gap: 12px;
            .role_select {
                width: 100%;
                display: none;
            }
            .search_input {
                width: 100%;
                margin-left: 0;
            }
        }
        .nav_btn {
            display: flex;
            flex-direction: column;
            row-gap: 12px;
        }
        .table {
            display: none;
        }
        .table_m {
            margin-top: 24px;
            display: block;
            .table_m_item {
                box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.1);
                border-radius: 12px;
                margin-bottom: 20px;
                > li {
                    padding: 0 16px;
                    @include font14;
                    &:first-of-type {
                        background-color: #f7f7f7;
                        > p {
                            border-bottom: none;
                        }
                    }
                    &:last-of-type {
                        > p {
                            border-bottom: none;
                        }
                    }
                    > p {
                        display: flex;
                        padding: 16px 0;
                        justify-content: space-between;
                        border-bottom: 1px solid #e5e5e5;
                    }
                }
                .reject_text {
                    margin-right: 4px;
                }
                .options {
                    padding: 20px 16px;
                    position: relative;
                    .other_box {
                        text-align: right;
                        margin-bottom: 20px;
                        .iconfont {
                            // color: $textColor3;
                            color: #4b4b4d;
                            &:hover {
                                color: $textColor1;
                            }
                        }
                    }
                    .btn {
                        display: flex;
                        flex-direction: column;
                        row-gap: 12px;
                    }
                }
            }
        }
    }
    .new_user_popup {
        width: 100%;
        // padding: 20px 16px;
        height: 100%;
        display: flex;
        flex-direction: column;
        .popup_container {
            flex: 1;
            overflow-y: scroll;
            .creat_form {
                > ul {
                    grid-template-columns: repeat(1, 1fr);
                    row-gap: 12px;
                }
            }
        }
        .btn {
            display: flex;
            flex-direction: column-reverse;
            row-gap: 12px;
            width: 100%;
        }
    }
    .table_m {
        &::v-deep {
            .option_list {
                .list_item {
                    margin-bottom: 8px;
                    margin-top: 8px;
                    color: $textColor1;
                }
            }
        }
    }
    .empty_m {
        height: 335px;
        box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.1);
        border-radius: 12px;
        padding: 0 16px;
        text-align: center;
        display: flex;
        flex-direction: column;
        justify-content: center;
        @include font16;
        > p {
            @include font14;
        }
    }
    .secondlink_btn {
        margin-top: auto;
    }
}
</style>
