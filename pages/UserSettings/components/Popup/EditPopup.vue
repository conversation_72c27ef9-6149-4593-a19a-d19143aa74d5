<template>
    <fs-popup :show="type !== 0" :title="getPopupTitle" @close="handleClose" v-loading.fullscreen="loading" :mobileWidth="mobileWidth" :class="{ canArchive: teamUserNumber === 0 }">
        <div class="popup_container">
            <template v-if="[1, 2].includes(type)">
                <div class="tips" v-if="type === 1">
                    <span class="iconfont">&#xe718;</span>
                    <p>{{ $c("pages.UserSetting.newSetting.assignRoles.popup.tips.tips1") }}</p>
                </div>
                <div class="container">
                    <p>{{ $c("pages.UserSetting.newSetting.assignRoles.teamName") }}</p>
                    <input type="text" v-model="teamName" :maxlength="51" @blur="validateName" />
                    <validate-error :error="errorText"></validate-error>
                </div>
                <div class="btn">
                    <fs-button v-if="type == 1" @click="handleCreateNew">{{ $c("pages.AddressBook.create") }}</fs-button>
                    <fs-button v-else-if="type == 2" @click="handleEditTeam">{{ $c("pages.UserSetting.userSetting.btn.submit") }}</fs-button>
                </div>
            </template>
            <div v-else-if="type === 3" class="archive_team">
                <h3>{{ $c("pages.UserSetting.newSetting.archive.tips").replace("xxxx", teamName) }}</h3>
                <!-- <p v-if="teamUserNumber !== 0">{{ $c("pages.UserSetting.newSetting.archive.policy.text1") }}</p> -->
                <ul v-if="teamUserNumber !== 0" class="archive_team_policy">
                    <li>{{ $c("pages.UserSetting.newSetting.archive.policy.text4").replace("xxxx", teamName).replace("aaaa", teamNumber) }}</li>
                </ul>
                <ul v-else class="archive_team_policy">
                    <li>{{ $c("pages.UserSetting.newSetting.archive.policy.text2") }}</li>
                    <li>{{ $c("pages.UserSetting.newSetting.archive.policy.text3") }}</li>
                </ul>
                <!-- <ul v-else-if="teamUserNumber === 1" class="archive_team_policy">
                    <li>{{ $c("pages.UserSetting.newSetting.archive.policy.text2") }}</li>
                    <li>{{ $c("pages.UserSetting.newSetting.archive.policy.text3") }}</li>
                </ul>
                <ul v-else-if="teamUserNumber === 2" class="archive_team_policy">
                    <li>{{ $c("pages.UserSetting.newSetting.archive.policy.text4").replace("xxxx", teamName).replace("aaaa", teamNumber) }}</li>
                </ul> -->
                <div class="btn">
                    <!-- <fs-button v-if="teamUserNumber === 2" @click="handleClose">{{ $c("pages.UserSetting.userSetting.btn.close") }}</fs-button> -->
                    <fs-button v-if="teamUserNumber !== 0" @click="handleClose">{{ $c("pages.UserSetting.userSetting.btn.close") }}</fs-button>
                    <template v-else>
                        <fs-button type="grayline" @click="handleClose">{{ $c("pages.UserSetting.userSetting.btn.cancel") }}</fs-button>
                        <fs-button @click="handleArchive">{{ $c("pages.UserSetting.newSetting.archive.archive") }}</fs-button>
                    </template>
                </div>
                <!-- <div class="btn_archive">
                    <span v-if="teamUserNumber !== 0" type="grayline" @click="handleClose">{{ $c("pages.UserSetting.userSetting.btn.cancel") }}</span>
                    <span v-if="teamUserNumber === 0" @click="handleArchive">{{ $c("pages.UserSetting.newSetting.archive.archive") }}</span>
                </div> -->
            </div>
        </div>
    </fs-popup>
</template>

<script>
import FsPopup from "@/components/FsPopupNew/FsPopupNew.vue"
import FsButton from "@/components/FsButton/FsButton.vue"
import ValidateError from "../../../../components/ValidateError/ValidateError.vue"
import userSettingAPiMinxi from "../../UserSettingAPI"
export default {
    props: {
        info: {
            type: Object,
            default: () => {},
        },
        type: {
            type: Number,
            default: 1,
        },
    },
    mixins: [userSettingAPiMinxi],
    components: {
        FsPopup,
        FsButton,
        ValidateError,
    },
    data() {
        return {
            loading: false,
            teamName: "",
            teamNumber: 0,
            errorText: "",
        }
    },
    methods: {
        async handleCreateNew() {
            if (!this.validateName()) return
            const { code, message } = await this.UPDATE_TEAM_INFO_API({ name: this.teamName })
            if (code === 200) {
                this.$emit("success")
            } else {
                this.$message.error(message)
            }
        },
        async handleEditTeam() {
            if (!this.validateName()) return
            const params = {
                id: this.info.id,
                name: this.teamName,
            }
            const { code, message } = await this.UPDATE_TEAM_INFO_API(params)
            if (code === 200) {
                this.$emit("success")
            } else {
                this.$message.error(message)
            }
        },
        async handleArchive() {
            const { code, message } = await this.ARCHIVE_TEAM_API({ id: this.info.id })
            if (code === 200) {
                this.$emit("success")
            } else {
                this.$message.error(message)
            }
        },
        handleClose() {
            this.$emit("close")
            this.errorText = ""
        },
        validateName() {
            let isPass = true
            const value = this.teamName
            if (value.length > 2 && value.length < 60) {
                this.errorText = ""
            } else {
                this.errorText = this.$t("pages.UserSetting.userSetting.popup.validateError.text9")
                isPass = false
            }
            return isPass
        },
    },
    computed: {
        getPopupTitle() {
            const titleEnumObj = {
                1: this.$t("pages.UserSetting.newSetting.popupText.title1"),
                2: this.$t("pages.UserSetting.newSetting.popupText.title2"),
                3: this.$t("pages.UserSetting.newSetting.popupText.title3"),
            }
            return titleEnumObj[this.type] || ""
        },
        teamUserNumber() {
            return this.info.count
        },
        mobileWidth() {
            //当类型为合并的时候，同时用户数量为0
            // return (this.teamUserNumber === 0&&this.type===3) ? "350px" : ""
            return ""
        },
    },
    watch: {
        info: {
            handler(val) {
                this.teamName = val.name || ""
                this.teamNumber = val.count
            },
        },
    },
}
</script>

<style lang="scss" scoped>
.popup_container {
    width: 680px;
    padding: 20px 32px;
    .tips {
        display: flex;
        column-gap: 8px;
        @include font14;
        padding: 10px 16px;
        background-color: rgba(0, 96, 191, 0.04);
        color: $textColor3;
        margin-bottom: 20px;
        .iconfont {
            color: #0060bf;
        }
    }
    .container {
        @include font12;
        padding-bottom: 20px;
        > p {
            margin-bottom: 4px;
        }
        &:has(.error_info) {
            input {
                border-color: #c00;
            }
        }
    }
    .btn {
        text-align: right;
        margin-top: 20px;
    }
    .btn_archive {
        display: none;
    }
    .archive_team {
        > h3 {
            @include font16;
            margin-bottom: 12px;
            font-weight: normal;
        }
        > p {
            @include font14;
            color: $textColor3;
            padding-bottom: 20px;
        }
        .btn {
            // display: grid;
            // grid-template-columns: repeat(2,1fr);
            display: flex;
            width: max-content;
            margin-left: auto;
            column-gap: 12px;
        }
        .archive_team_policy {
            @include font14;
            color: $textColor3;
            > li {
                position: relative;
                padding-left: 12px;
                &::before {
                    content: "";
                    width: 4px;
                    height: 4px;
                    border-radius: 50%;
                    position: absolute;
                    top: 11px;
                    left: 0;
                    transform: translateY(-50%);
                    background-color: $textColor3;
                }
            }
        }
    }
}
@media (max-width: 768px) {
    .popup_container {
        width: auto;
        padding: 24px 16px;
        height: 100%;
        display: flex;
        flex-direction: column;
        .btn {
            margin-top: auto;
            .fs-button {
                width: 100%;
            }
        }
        .archive_team {
            height: 100%;
            display: flex;
            flex-direction: column;
            .btn {
                margin-top: auto;
                width: 100%;
                flex-direction: column-reverse;
                row-gap: 12px;
                .fs-button {
                    width: 100%;
                }
            }
        }
    }
    .canArchive {
        &::v-deep {
            .fs-popup .fs-popup-header {
                // display: none;
            }
            .btn_archive {
                display: block;
            }
        }
    }
    :v-deep {
        .popper-computer-left {
            &:after {
                background-color: #fff;
            }
        }
    }
}
</style>
