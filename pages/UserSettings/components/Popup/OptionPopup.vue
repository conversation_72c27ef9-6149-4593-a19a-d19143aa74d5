<template>
    <fs-popup
        class="option_popup"
        :show="show"
        :title="getTitle"
        @close="close"
        :autoContent="!getTitle"
        :loading="loading"
        :isMDrawer="true"
        :mobileWidth="(isRemoveResult && removeResultType === 1) || type === 4 ? '350px' : ''"
        :class="{ result_popup: isRemoveResult, result_success: isRemoveResult && removeResultType === 1, result_err: isRemoveResult && removeResultType === 2, isApiPopupType: isApiPopupType }">
        <template v-if="!isRemoveResult">
            <!-- 新增删除api相关弹窗 -->
            <div class="popup_container main_popup_container api_box" v-if="isApiPopupType">
                <!-- 当移除人员非Api Owner时-->
                <p class="tips_box" v-if="isApiOwner">{{ $t("pages.UserSetting.newSetting.popupText.devOwnerRemove").replace("xxxx", info.customersEmailAddress) }}</p>
            </div>
            <div class="popup_container main_popup_container" v-else-if="type !== 4">
                <assign-roles v-if="type === 1" :info="info" :isDeveloperDisabled="isDeveloperDisabled" @change="rolesChange"></assign-roles>
                <div v-else-if="type === 2" class="team_select">
                    <h3>{{ $t("pages.UserSetting.newSetting.popupText.selectTeam") }}</h3>
                    <div class="form_item">
                        <!-- <span>{{ $t("pages.UserSetting.newSetting.team") }}</span> -->
                        <fs-checkbox-select :options="teamList" :placeholder="$t('components.qa.select.option01')" @change="handleCheckChange"></fs-checkbox-select>
                        <p class="developer_tips" v-if="developerTips">
                            <span class="iconfont">&#xe718;</span><span> {{ developerTips }}</span>
                        </p>
                    </div>
                    <assign-roles @change="rolesChange" :info="info" :isDeveloperDisabled="isDeveloperDisabled" :isDisAbledRole="true"></assign-roles>
                </div>
                <div v-else-if="type === 3" class="remove_box">
                    <h4>{{ $t("pages.UserSetting.newSetting.popupText.removeTips").replace("xxxx", info.customersEmailAddress) }}</h4>
                    <ul class="tips_list">
                        <li v-for="(i, index) in removeTips" :key="index">
                            <p>{{ i }}</p>
                        </li>
                    </ul>
                </div>
                <div v-else-if="type === 5" class="remove_box">
                    <h4>{{ $t("pages.UserSetting.newSetting.popupText.deleteTips").replace("xxxx", info.customersEmailAddress) }}</h4>
                    <p>{{ $t("pages.UserSetting.newSetting.popupText.deleteTips1") }}</p>
                </div>
                <!-- 按钮区域 -->
                <div class="btn">
                    <fs-button type="grayline" @click="handleClose">{{ $t("pages.UserSetting.userSetting.btn.cancel") }}</fs-button>
                    <fs-button v-if="type === 3" :loading="btnLoading" @click="handleRemove">{{ $t("pages.UserSetting.userSetting.user_table.remove") }}</fs-button>
                    <fs-button v-else-if="type === 5" :loading="btnLoading" @click="handleDelate">{{ $t("pages.UserSetting.newSetting.delete") }}</fs-button>
                    <fs-button v-else :disabled="submitDisabled" @click="handleSubmit">{{ $t("pages.UserSetting.userSetting.btn.submit") }}</fs-button>
                </div>
            </div>
            <div v-else class="popup_container">
                <div class="resend_tips">
                    <span class="iconfont success_icon">&#xf060;</span>
                    <span class="content_main">{{ $t("pages.UserSetting.newSetting.popupText.resentTips").replace("xxxx", info.customersEmailAddress) }}</span>
                    <span class="iconfont close close_btn_pc" @click="$emit('close')">&#xf30a;</span>
                    <span class="close_btn_m" @click="$emit('close')">{{ $c("pages.UserSetting.userSetting.btn.close") }}</span>
                </div>
            </div>
        </template>
        <!-- <div class="remove_popup" :class="{ remove_success: removeResultType === 1, remove_err: removeResultType === 2 }"
            v-show="isRemoveResult"> -->
        <div class="remove_popup" v-show="isRemoveResult">
            <!-- 移除成功！ -->
            <div v-if="removeResultType === 1" class="remove_success">
                <span class="iconfont success_icon">&#xf052;</span>
                <div class="success_content">
                    <p>{{ $c("pages.UserSetting.userSetting.popup.removeSuccess.resmoveTitle").replace("xxxx", info.customersEmailAddress) }}</p>
                    <p>{{ $c("pages.UserSetting.userSetting.popup.removeSuccess.tips") }}</p>
                </div>
                <span class="iconfont close_icon" @click.stop="handelSuccess">&#xf30a;</span>
                <div class="btn_m" @click.stop="handelSuccess">{{ $c("pages.UserSetting.userSetting.btn.close") }}</div>
            </div>
            <!-- 失败状态 -->
            <div v-if="removeResultType === 2" class="remove_err">
                <div class="err_content">
                    <h3>{{ $c("pages.UserSetting.userSetting.popup.removeError.reason1.title") }}</h3>
                    <p>{{ $c("pages.UserSetting.userSetting.popup.removeError.reason1.resontext") }}</p>
                    <p>{{ $c("pages.UserSetting.userSetting.popup.removeError.reason1.text1") }}</p>
                    <div class="other_problem">
                        <a href="javascript:;" @click.stop="handleOther">{{ $c("pages.UserSetting.userSetting.btn.otherProblem") }}</a>
                    </div>
                </div>
            </div>
        </div>
    </fs-popup>
</template>

<script>
import FsPopup from "../../../../components/FsPopupNew/FsPopupNew.vue"
import AssignRoles from "../AssignRoles/AssignRoles.vue"
import FsButton from "../../../../components/FsButton/FsButton.vue"
import FsCheckboxSelect from "../FsCheckboxSelect/FsCheckboxSelect.vue"
import userSettingAPiMinxi from "../../UserSettingAPI"
import { mapState } from "vuex"
export default {
    props: {
        show: {
            type: Boolean,
            default: false,
        },
        title: {
            type: String,
            default: "",
        },
        type: {
            type: Number,
            default: 0,
        },
        info: {
            type: Object,
            default: () => {},
        },
        toTeamList: {
            type: Array,
            default: () => [],
        },
        isGroupOne: {
            type: Boolean,
            default: false,
        },
        developerTips: {
            type: String,
            default: "",
        },
    },
    mixins: [userSettingAPiMinxi],
    components: {
        FsPopup,
        AssignRoles,
        FsButton,
        FsCheckboxSelect,
    },
    data() {
        return {
            // teamList: [],
            form: {
                team: [],
            },
            roles: "",
            isRemoveResult: false,
            removeResultType: 1,
            resultType: "",
            //加载状态
            loading: false,
            btnLoading: false,
            // removeTips: [this.$t("pages.UserSetting.newSetting.popupText.removeErrorTips.text1"), this.$t("pages.UserSetting.newSetting.popupText.removeErrorTips.text2")],
        }
    },
    methods: {
        handleCheckChange(val) {
            console.log(val, "valsss")
            this.form.team = val
        },
        rolesChange(val) {
            const obj = {
                1: "administrator",
                2: "buyer",
                3: "finance",
                7: "developer",
            }
            if (!obj[val]) return
            this.roles = obj[val]
        },
        initPopup() {
            this.btnLoading = false
        },
        handleClose() {
            this.initPopup()
            console.log(this.type, "type")
            if (this.type === 1) {
                // 取消权限编辑
                this.gaFunctin("edit_user", "Cancel Submit")
            } else if (this.type === 3) {
                // 取消移除用户
                this.gaFunctin("remove_user", "Cancel Submit")
            } else if (this.type === 2) {
                //add to team
                this.gaFunctin("add_user", "Cancel Submit")
            } else if (this.type === 5) {
                // delete
                this.gaFunctin("delete_user", "Cancel Submit")
            }
            this.$emit("close")
        },
        async handleSubmit() {
            this.loading = true
            if (this.type === 1) {
                // 修改role
                const params = {
                    id: this.info.id,
                    toRoleNotTeam: this.roles,
                    customersNumberNew: this.info.customersNumberNew,
                }
                this.gaFunctin("edit_user", "Confirm Submit")
                this.updateRole(params)
            } else if (this.type === 2) {
                this.gaFunctin("add_user", "Confirm Submit")
                const params = {
                    id: this.info.id,
                    toRoleNotTeam: this.roles,
                    toTeamIdList: this.form.team,
                    customersNumberNew: this.info.customersNumberNew,
                }
                const { code, message } = await this.UPDATE_TEAM_GROUP_API(params).finally(() => (this.loading = false))
                if (code === 200) {
                    //处理成功的逻辑
                    // this.$emit("close")
                    this.$emit("success")
                }
            }
        },
        async handleDelate() {
            this.btnLoading = true
            this.gaFunctin("delete_user", "Confirm Submit")
            const { id, customersNumberNew } = this.info
            const { code, message } = await this.DELETE_USER_API({ id, customersNumberNew })
            this.btnLoading = false
            if (code === 200) {
                //处理成功的逻辑
                // this.$emit("close")
                this.$emit("success", { type: "delete", info: this.info })
            }
        },
        async updateRole(params) {
            const { code } = await this.UPDATE_USER_ORGANIZATION_API(params).finally(() => (this.loading = false))
            if (code === 200) {
                // this.$emit("close")
                this.$emit("success")
            }
        },
        async handleRemove() {
            this.btnLoading = true
            this.resultType = ""
            this.gaFunctin("remove_user", "Confirm Submit")
            const { id, customersNumberNew } = this.info
            this.$axios
                .post("/api/companyAccount/removeCompanyUser", { id, customersNumberNew })
                .then((res) => {
                    if (res.code === 200) {
                        //处理成功的逻辑
                        // this.$emit("close")
                        this.isRemoveResult = true
                        this.removeResultType = 1
                        this.gaFunctin("remove_user_result", "success")
                    }
                })
                // .catch(({ errors: {} }) => {
                .catch((err) => {
                    console.log(err, "errors")
                    this.resultType = err.errors?.errorType || err.errors?.code + ""
                    this.gaFunctin("remove_user_result", "fail")
                    const { code, errors, message } = err
                    if (code === 400 && errors.code === 3001) {
                        this.isRemoveResult = true
                        this.removeResultType = 2
                    } else {
                        this.$message.error(message)
                    }
                })
                .finally(() => {
                    this.btnLoading = false
                })
        },
        close() {
            this.isRemoveResult = false
            this.removeResultType = 1
            this.$emit("close")
        },
        handelSuccess() {
            this.isRemoveResult = false
            this.removeResultType = 1
            this.$emit("success")
        },
        handleOther() {
            this.gaFunctin("remove_user_result", `${this.resultType}_Contact Sales`)
            this.$router.push(this.localePath("/live_chat_service_mail.html"))
        },
        gaFunctin(eventAction, eventLabel) {
            window.dataLayer &&
                window.dataLayer.push({
                    event: "uaEvent",
                    eventCategory: this.pageGroup,
                    eventAction,
                    eventLabel,
                    nonInteraction: false,
                })
        },
    },
    computed: {
        ...mapState({
            pageGroup: (state) => state.ga.pageGroup,
        }),
        getTitle() {
            if (this.isRemoveResult) {
                return this.removeResultType === 1 ? "" : this.$c("pages.UserSetting.userSetting.popup.removeError.title")
            } else {
                //    type取值 //0 默认不展示  1:Edit Role  2:Add to another team 3:Remove User   4:Resend  5:Delete
                switch (this.type) {
                    case 1:
                        return this.$t("pages.UserSetting.newSetting.popupText.title4")
                    case 2:
                        return this.$t("pages.UserSetting.newSetting.addToTeam")
                    case 3:
                        return this.$t("pages.UserSetting.newSetting.popupText.title6")
                    case 4:
                        return ""
                    case 5:
                        return this.$t("pages.UserSetting.newSetting.popupText.title7")
                    default:
                        return ""
                }
            }
        },
        submitDisabled() {
            let disabled = false
            //type=1  编辑
            if (this.type === 1 && !this.roles.length) {
                disabled = true
            }
            if (this.type === 2) {
                if (!(this.roles.length && this.form.team.length)) {
                    disabled = true
                }
            }
            return disabled
        },
        teamList() {
            // console.log(this.toTeamList, "this.toTeamList")
            if (this.toTeamList.length > 0) {
                const arr = this.toTeamList.map(({ id, name }) => ({ name, value: id }))
                console.log(arr, "this.toTeamList")
                return arr
            } else {
                return []
            }
        },
        removeTips() {
            let arr = [this.$t("pages.UserSetting.newSetting.popupText.removeErrorTips.text3"), this.$t("pages.UserSetting.newSetting.popupText.removeErrorTips.text2")]
            const { teamCount } = this.info
            console.log(this.info, "this.info")
            if (teamCount && teamCount > 1) {
                arr = [this.$t("pages.UserSetting.newSetting.popupText.removeErrorTips.text1"), this.$t("pages.UserSetting.newSetting.popupText.removeErrorTips.text2")]
            }
            return arr
        },
        isApiOwner() {
            const { openApiOwner = false } = this.info
            return openApiOwner
        },
        //是否时api类型用户相关的对话框
        isApiPopupType() {
            const { type = 0 } = this.info
            return type === 3 && this.isApiOwner
        },
        //是否禁用developer
        isDeveloperDisabled() {
            //当类型为编辑角色时，且为非一级组的时候，禁用developer
            if (this.type === 1) {
                return !this.isGroupOne
            } else if (this.type === 2) {
                return !!(this.hasSelectGroupList.length && !this.hasSelectGroupList.some((item) => item.type === 1))
            } else {
                return false
            }
        },
        //选中的team列表
        hasSelectGroupList() {
            if (this.form.team.length) {
                return this.toTeamList.filter((item) => this.form.team.includes(item.id))
            } else {
                return []
            }
        },
        // developerTips() {
        //     if (this.roles === "developer") {
        //         //当所选的组中存在1级组且存在多个组时出现提示
        //         if (this.hasSelectGroupList.length > 1 && this.hasSelectGroupList.some((item) => item.type === 1)) {
        //             const list = this.hasSelectGroupList.filter((item) => item.type === 1)
        //             const listString = list.map((item) => item.name)?.join(";") || ""
        //             return this.$c("pages.UserSetting.newSetting.assignRoles.developerTips").replace("XXXX", listString)
        //         } else {
        //             return ""
        //         }
        //     } else {
        //         return ""
        //     }
        //     // 筛选出teamList中的一级组
        //     const firstLevelGroup = this.teamList.filter((item) => item.type === 1)
        //     if (firstLevelGroup.length) {
        //         const listString = firstLevelGroup.map((item) => item.name)?.join(";") || ""
        //         return this.$c("pages.UserSetting.newSetting.assignRoles.developerTips").replace("XXXX", listString)
        //     } else {
        //         return ""
        //     }
        // },
    },
}
</script>

<style lang="scss" scoped>
.option_popup {
    ::v-deep {
        .fs-popup {
            z-index: 117;
        }
    }
}

.popup_container {
    padding: 16px 24px 0;
    width: 680px;
    border-radius: 3px;
    &.api_box {
        padding: 16px 24px;
    }
}

// .remove_popup {
//     width: 680px;
//     padding: 20px 32px;

//     .footer_btn {
//         display: flex;
//         justify-content: flex-end;
//         padding-top: 20px;

//         .fs-button {
//             // margin-left: 12px;
//         }
//     }

//     .footer_btn_m {
//         display: none;
//     }

//     .remove_popup_content {
//         margin-bottom: 20px;

//         h3 {
//             @include font16;
//             font-weight: normal;
//             margin-bottom: 12px;
//         }

//         >p {
//             @include font14;
//             color: $textColor3;
//         }
//     }
// }

.team_select {
    > h3 {
        @include font16;
        margin-bottom: 8px;
    }

    .form_item {
        @include font12;
        margin-bottom: 16px;
        display: flex;
        flex-direction: column;
        row-gap: 4px;
    }
}

.remove_box {
    @include font14;
    color: $textColor3;
    margin-bottom: 24px;
    @media (max-width: 768px) {
        margin-bottom: 20px;
    }

    > h4 {
        @include font16;
        color: $textColor1;
        font-weight: normal;
        margin-bottom: 12px;
    }
    .tips_list {
        > li {
            padding-left: 12px;
            position: relative;

            &:not(:last-of-type) {
                margin-bottom: 12px;
            }

            &::before {
                content: "";
                width: 4px;
                height: 4px;
                border-radius: 50%;
                background-color: $textColor3;
                position: absolute;
                left: 0;
                top: 9px;
            }
        }
    }
}
.developer_tips {
    display: flex;
    align-items: center;
    @include font13;
    color: #707070;
    column-gap: 4px;
    margin-top: 4px;
}

.btn {
    display: grid;
    width: max-content;
    margin-left: auto;
    column-gap: 16px;
    grid-template-columns: repeat(2, 1fr);
    margin-bottom: 24px;
    @media (max-width: 768px) {
        margin-bottom: 0;
    }
}

.resend_tips {
    display: flex;
    align-items: center;
    column-gap: 8px;

    .iconfont {
        color: #10a300;
    }

    .close {
        margin-left: auto;
        color: #707070;
        cursor: pointer;
    }

    .close_btn_m {
        display: none;
    }
}

.result_popup {
    // &::v-deep {
    //     .fs-popup-ctn {
    //         .fs-popup-header {
    //             display: none;
    //         }
    //     }
    // }
}

.remove_success {
    width: 680px;
    padding: 20px 32px;
    display: flex;
    justify-content: space-between;

    .success_icon {
        color: #10a300;
        margin-right: 8px;
        @include font20;
        font-weight: 600;
    }

    .success_content {
        flex: 1;

        p {
            @include font14;
            color: $textColor3;

            &:first-child {
                @include font16;
                color: $textColor1;
                margin-bottom: 12px;
            }
        }
    }

    .close_icon {
        color: #707070;
        margin-left: 20px;
        cursor: pointer;
    }

    .btn_m {
        display: none;
    }
}

.remove_err {
    width: 680px;
    padding: 20px 32px;

    .err_content {
        > h3 {
            @include font16;
            font-weight: normal;
            margin-bottom: 8px;
        }

        > p {
            &:first-of-type {
                @include font14;
                color: $textColor3;
                margin-bottom: 20px;
                display: flex;
                align-items: center;

                &::before {
                    display: inline-block;
                    content: "";
                    width: 4px;
                    height: 4px;
                    border-radius: 50%;
                    background-color: #707070;
                    margin-right: 8px;
                }
            }

            &:nth-of-type(2) {
                @include font16;
                color: $textColor1;
                margin-bottom: 20px;
            }
        }

        .other_problem {
            display: flex;
            margin-bottom: 20px;
            @include font14;

            > a {
                margin-right: 3px;
            }
        }
    }
}
.isApiPopupType {
    .popup_container {
        width: 480px;
    }
    .tips_box {
        // margin: 16px 0;
        @include font14;
        color: #707070;
    }
    &::v-deep {
        .fs-popup-ctn {
            .fs-popup-header {
                border-bottom: none;
                padding: 24px 14px 0 24px;
                position: relative;
                .title_box {
                    .title {
                        @include font18;
                    }
                }
            }
        }
        .iconfont_close_box {
            padding: 0;
            position: absolute;
            top: 16px;
            right: 16px;
            padding: 4px;
        }
        .iconfont_close {
            font-size: 20px;
            line-height: 20px;
        }
    }
}

@media (max-width: 768px) {
    .popup_container {
        width: 100%;

        &.main_popup_container {
            height: 100%;
            display: flex;
            padding: 16px;
            flex-direction: column;
        }

        .btn {
            margin-top: auto;
            width: 100%;
            display: flex;
            flex-direction: column-reverse;
            row-gap: 12px;
        }
    }

    .remove_popup {
        flex-direction: column;
        align-items: center;
        padding: 0;
        padding-top: 32px;
        width: auto;
        min-width: 350px;
        width: 350px;
        height: 100%;

        .remove_popup_content {
            padding: 0 36px;
            text-align: center;

            > h3 {
                @include font16;
                font-weight: normal;
                margin-bottom: 8px;
            }

            > p {
                @include font14;
                color: $textColor3;
            }
        }

        .footer_btn {
            display: none;
        }

        .footer_btn_m {
            display: flex;
            border-top: 1px solid #e5e5e5;
            margin-top: 24px;

            > p {
                flex: 1;
                line-height: 48px;
                font-size: 14px;
                text-align: center;
                cursor: pointer;

                &:first-child {
                    border-right: 1px solid #e5e5e5;
                }
            }
        }
    }

    .result_popup {
        &.result_success {
            &::v-deep {
                .fs-popup-ctn {
                    width: auto;
                    //min-width: 380px;
                    // min-width: 350px;
                    // width: 350px;
                    height: auto;
                }
            }

            .remove_success {
                // width: 350px;
                width: 100%;
                padding: 0;
                padding-top: 32px;
                flex-direction: column;
                text-align: center;
                .success_content {
                    padding: 0px 36px;
                    margin-bottom: 24px;
                    text-align: center;
                }

                .success_icon {
                    font-size: 32px;
                    margin-bottom: 12px;
                }
            }

            .close_icon {
                display: none;
            }

            .btn_m {
                width: 100%;
                text-align: center;
                display: block;
                line-height: 48px;
                border-top: 1px solid #e5e5e5;
            }
        }

        &.result_err {
            .remove_err {
                width: 100%;
                padding: 20px 16px;
            }
        }
    }

    ::v-deep {
        .ckeck_popup {
            .fs-popup-ctn {
                width: auto;
                height: auto;

                .fs-popup-header {
                    display: none;
                }
            }
        }
    }

    .resend_tips {
        display: flex;
        flex-direction: column;
        text-align: center;
        @include font16;

        .close_btn_m {
            display: block;
            padding: 13px 0;
            @include font14;
            border-top: 1px solid #e5e5e5;
            width: 100%;
            margin-top: 25px;
        }

        .content_main {
            padding: 0 36px;
        }

        .close_btn_pc {
            display: none;
        }

        .success_icon {
            font-size: 32px;
            margin-bottom: 12px;
        }
    }

    .popup_container {
        &:has(.resend_tips) {
            padding: 32px 0 0 0;
        }
    }
    .isApiPopupType {
        &::v-deep {
            .fs-popup {
                z-index: 999;
                height: 100%;
            }
            .popup_container {
                width: 100%;
            }
        }
    }
}
.remove_popup {
    &:has(.remove_err) {
        padding-top: 0;
    }
}
</style>
