<template>
    <fs-popup
        :loading="popupLoading"
        :show="isShowPopup"
        :isMDrawer="!hasSubmitRequest"
        class="is_link_popup"
        :hideHeader="true"
        :appendBody="true"
        :title="$c('pages.UserSetting.userSetting.addUser')"
        @close="initPopup">
        <div v-if="hasSubmitRequest" class="result_popup">
            <template v-if="isSuccess">
                <div class="title">
                    <span>
                        <span class="iconfont success_icon">&#xf052;</span>
                        <b>{{ errorUserContent.length ? $c("pages.UserSetting.userSetting.invitations_tips.title1") : $c("pages.UserSetting.userSetting.invitations_tips.title2") }}</b>
                    </span>
                    <div class="close_icon_box">
                        <span class="iconfont close_icon" @click="initPopup">&#xf30a;</span>
                    </div>
                </div>
                <div class="result_popup_content">
                    <div class="content" v-if="errorUserContent.length">
                        <div class="tips_box">
                            <span class="iconfont error_icon">&#xe66f;</span>
                            <p>{{ $c("pages.UserSetting.userSetting.invitations_tips.errorTips") }}</p>
                        </div>
                        <p class="error_user">
                            {{ errorUserContent.join(",") }}
                        </p>
                    </div>
                    <div class="tips" v-else>{{ $c("pages.UserSetting.userSetting.invitations_tips.tips1") }}</div>
                </div>
            </template>
            <div v-else>
                <div class="error_content">
                    <span class="iconfont error_icon">&#xe66f;</span>
                    <div>
                        <p>{{ errorSubmitMessage || $c("pages.UserSetting.userSetting.invitations_tips.errorTips1") }}</p>
                        <div class="close_icon_box">
                            <span class="iconfont close_icon" @click="initPopup">&#xf30a;</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="new_user_popup" v-else>
            <div class="popup_container_title">
                <b>{{ $c("pages.UserSetting.userSetting.addUser") }}</b>
                <div class="icon_box">
                    <span class="iconfont iconfont_close" @click="initPopup">&#xf30a;</span>
                </div>
            </div>
            <div class="popup_container">
                <!-- 提示语 -->
                <p class="tips">
                    <span class="iconfont">&#xe718;</span>
                    <span>{{ $c("pages.UserSetting.userSetting.addUserTipsText") }}</span>
                </p>
                <div class="container">
                    <!-- email -->
                    <div class="form_item">
                        <p class="label_box">{{ $c("pages.UserSetting.newSetting.businessEmail") }}</p>
                        <input type="text" v-model.trim="form.userList" @blur="validateEmail" @input="handleEmailChange" :placeholder="$c('pages.UserSetting.userSetting.inputEmailPlaceholder')" />
                        <validate-error :error="errors.userList"></validate-error>
                    </div>
                    <!-- team -->
                    <div class="form_item">
                        <p class="label_box">{{ $t("pages.UserSetting.newSetting.popupText.selectTeam") }}</p>
                        <FsCheckboxAcceptSelect class="fs_checkbox_roles" :options="teamListNew" :placeholder="$t('components.qa.select.option01')" @change="handleCheckChange"></FsCheckboxAcceptSelect>
                        <p class="developer_tips" v-if="developerTips">
                            <span class="iconfont">&#xe718;</span><span> {{ developerTips }}</span>
                        </p>
                        <validate-error :error="errors.team_ids"></validate-error>
                    </div>
                    <assign-roles class="assign_roles_box" @change="rolesChange" :isDeveloperDisabled="isDeveloperDisabled"></assign-roles>
                </div>
            </div>
            <div class="footer">
                <FsButton type="white" @click="initPopup">{{ $t("pages.UserSetting.userSetting.btn.cancel") }}</FsButton>
                <FsButton @click="handleSubmit" :disabled="disableSubmit">{{ $t("pages.UserSetting.userSetting.btn.submit") }}</FsButton>
            </div>
        </div>
    </fs-popup>
</template>

<script>
import FsPopup from "../../../components/FsPopupNew/FsPopupNew.vue"
import ValidateError from "@/components/ValidateError/ValidateError.vue"
import AssignRoles from "./AssignRoles/AssignRoles.vue"
import FsCheckboxAcceptSelect from "@/pages/UserSettings/components/FsCheckboxSelect/FsCheckboxSelect.vue"
import FsButton from "../../../components/FsButton/FsButton.vue"
import { email_valdate } from "@/constants/validate"
import { mapState } from "vuex"
export default {
    components: {
        FsPopup,
        ValidateError,
        AssignRoles,
        FsCheckboxAcceptSelect,
        FsButton,
    },
    props: {
        isShowPopup: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {
            popupLoading: false,
            hasSubmitRequest: false,
            isSuccess: true,
            popupType: 2,
            form: {
                userList: "",
                role: 0,
                team_ids: [],
            },
            errors: {
                userList: "",
                team_ids: "",
            },
            toTeamList: [],
            errorUserContent: [],
            errorSubmitMessage: "",
        }
    },
    methods: {
        initPopup() {
            this.hasSubmitRequest = false
            this.isSuccess = true
            this.form = {
                userList: "",
                role: 0,
                team_ids: [],
            }
            this.errors = {
                email: "",
                team_ids: "",
            }
            this.$emit("close")
        },
        rolesChange(val) {
            this.form.role = val
        },
        handleCheckChange(val) {
            this.form.team_ids = val
            if (val.length > 10) {
                this.errors.team_ids = this.$c("pages.UserSetting.userSetting.validate.max_team")
            }
        },
        handleEmailChange() {
            const arr = this.form.userList.split(";")
            const result = [...new Set(arr)]
            if (result.length > 4) {
                this.form.userList = result.slice(0, 3).join(";")
            } else {
                this.form.userList = result.join(";")
            }
        },
        validateEmail() {
            const arr = this.form.userList.split(";")
            if (arr.length < 1) {
                this.errors.userList = this.$c("form.validate.email.email_required")
            } else if (arr.length > 3) {
                this.errors.userList = this.$c("pages.UserSetting.userSetting.validate.emailMax")
            } else {
                const hasErrorEmail = arr.some((i) => !email_valdate.test(i))
                this.errors.userList = hasErrorEmail ? this.$c("form.validate.email.email_valid") : ""
            }
            return !this.errors.userList.length
        },
        handleSubmit() {
            const isEmailValidatePass = this.validateEmail()
            const team_ids = this.selectedTeam.map(({ id, type }) => ({ team_id: id, type }))
            const params = {
                ...this.form,
                userList: this.form.userList.split(";").map((item) => ({ email: item })),
                team_ids,
            }
            if (isEmailValidatePass) {
                this.popupLoading = true
                this.$axios
                    .post("/api/companyAccount/addLinkUserToOrganizationBatch", params)
                    .then((res) => {
                        const {
                            code,
                            data: { failData = [] },
                        } = res
                        if (code === 200) {
                            this.hasSubmitRequest = true
                            this.isSuccess = true
                            this.errorUserContent = failData
                        }
                    })
                    .catch((err) => {
                        if (err.code === 401) {
                            location.href = this.localePath({ name: "login", query: { redirect: this.$route.fullPath } })
                        }
                        if (err.code === 422) {
                            //处理邮箱和小组的校验
                            const { team = "", email = "" } = err.errors
                            if (team) {
                                this.errors.team_ids = team
                            }
                            if (email) {
                                this.errors.userList = email
                            }
                            if (!(team || email)) {
                                this.hasSubmitRequest = true
                                this.errorSubmitMessage = err.message
                            }
                        } else {
                            this.hasSubmitRequest = true
                            this.isSuccess = false
                            this.errorSubmitMessage = err.message
                        }
                    })
                    .finally(() => {
                        this.popupLoading = false
                    })
            }
        },
        async getLinkTeam() {
            if (this.isLogin == 1 && this.userInfo.isCompanyOrganizationUser) {
                this.$axios
                    .get("/api/companyAccount/getARoleTeams", {})
                    .then((res) => {
                        if (res.code === 200) {
                            //处理成功的逻辑
                            this.toTeamList = res.data
                        }
                    })
                    .catch((err) => {
                        console.log(err, "errors")
                        const { code, errors, message } = err
                        this.$message.error(message)
                    })
            }
        },
    },
    computed: {
        ...mapState({
            isLogin: (state) => state.userInfo.isLogin,
            userInfo: (state) => state.userInfo.userInfo,
        }),
        teamListNew() {
            if (this.toTeamList.length > 0) {
                const arr = this.toTeamList.map(({ id, name, type }, index) => {
                    // 判断当前元素是否为数组的第一个元素
                    const isFirstElement = index === 0
                    // 根据判断结果动态设置 check 的值
                    return {
                        name,
                        value: id,
                        type,
                        checked: isFirstElement ? true : false,
                    }
                })
                this.handleCheckChange([arr[0].value])
                return arr
            } else {
                return []
            }
        },
        getEmailList() {
            const arr = this.form.userList.split(";")
            return JSON.stringify(arr)
        },
        disableSubmit() {
            return !this.form.role && this.form.team_ids.length > 0
        },
        selectedTeam() {
            const arr = this.toTeamList.filter((item) => this.form.team_ids.includes(item.id))
            return arr
        },
        //developer是否禁用状态   当所选的组存在非一级组时，禁用
        isDeveloperDisabled() {
            const result = this.selectedTeam.some((item) => item.type !== 1)
            return result
        },
        developerTips() {
            // 筛选出teamList中的一级组
            const firstLevelGroup = this.selectedTeam.filter((item) => item.type === 1)
            if (firstLevelGroup.length) {
                const listString = firstLevelGroup.map((item) => item.name)?.join(";") || ""
                return this.$c("pages.UserSetting.newSetting.assignRoles.developerTips").replace("XXXX", listString)
            } else {
                return ""
            }
        },
        successTitle() {
            return this.errorUserContent.length > 1 ? $c("pages.UserSetting.userSetting.invitations_tips.title1") : $c("pages.UserSetting.userSetting.invitations_tips.title2")
        },
    },
    beforeMount() {
        this.getLinkTeam()
    },
}
</script>

<style lang="scss" scoped>
.new_user_popup {
    width: 750px;
    padding: 0 24px;
    .popup_container_title {
        display: flex;
        justify-content: space-between;
        padding: 16px 0;
        border-bottom: 1px solid #dee0e3;
        @include font18;
        .iconfont {
            @include font20;
            height: 20px;
            line-height: 20px;
            color: #707070;
        }
        .icon_box {
            display: flex;
            justify-content: center;
            align-items: center;
            width: 28px;
            height: 28px;
            cursor: pointer;
            &:hover {
                background-color: rgba(25, 25, 26, 0.04);
                .iconfont {
                    color: $textColor1;
                }
            }
        }
    }
    .popup_container {
        padding: 16px 0;
        .container {
            .form_item {
                margin-bottom: 16px;
                .label_box {
                    @include font12;
                    color: #707070;
                    margin-bottom: 4px;
                }
                input {
                    background-color: #f6f6f8;
                    border-radius: 4px;
                    border-color: transparent;
                }
                .fs_checkbox_roles {
                    &::v-deep {
                        .fs-checkbox-active {
                            background-color: #f6f6f8;
                            border-color: transparent;
                        }
                    }
                }
            }
        }
    }
    .tips {
        padding: 10px 16px;
        background-color: rgba(0, 96, 191, 0.04);
        display: flex;
        column-gap: 8px;
        @include font12;
        color: $textColor3;
        margin-bottom: 20px;
        border-radius: 4px;
        .iconfont {
            color: #0060bf;
            // font-weight: 600;
        }
    }
    .footer {
        display: flex;
        justify-content: flex-end;
        column-gap: 16px;
        margin-bottom: 24px;
        .fs-button {
            height: 36px;
        }
    }
    .developer_tips {
        display: flex;
        align-items: center;
        @include font13;
        color: #707070;
        column-gap: 4px;
        margin-bottom: 16px;
        margin-top: 4px;
    }
    .assign_roles_box {
        &::v-deep {
            .assign_roles {
                color: #707070;
                > b {
                    font-weight: 400;
                    @include font12;
                }
            }
        }
    }
}
.result_popup {
    width: 480px;
    padding: 24px;
    @include font16;
    position: relative;
    .title {
        display: flex;
        justify-content: space-between;
        align-items: center;
        color: $textColor1;
        .close_icon {
            color: $textColor2;
            width: 20px;
            height: 20px;
            line-height: 20px;
            font-size: 20px;
            cursor: pointer;
        }
        .close_icon_box {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 32px;
            width: 32px;
            position: absolute;
            right: 16px;
            top: 16px;
            border-radius: 4px;
            &:hover {
                background-color: rgba(25, 25, 26, 0.04);
                .iconfont_close {
                    color: $textColor1;
                }
            }
        }
    }
    .result_popup_content {
        padding-top: 8px;
        .tips_box {
            display: flex;
            column-gap: 8px;
            align-items: start;
            .error_icon {
                color: #c00000;
            }
        }
        .tips {
            color: $textColor3;
            @include font14;
            margin-left: 28px;
            margin-top: 4px;
        }
        .content {
            color: $textColor3;
            border-radius: 8px;
            @include font14;
            padding: 16px;
            background: rgba(192, 0, 0, 0.04);
            color: #c00000;
            .error_user {
                padding-left: 24px;
            }
        }
    }

    .success_icon {
        color: #10a300;
        margin-right: 8px;
    }
    .close_icon {
        cursor: pointer;
    }
    .error_title {
        display: flex;
        justify-content: end;
    }
}
::v-deep {
    .fs-popup {
        .fs-popup-ctn {
            border-radius: 8px;
            .fs-popup-close {
                display: none;
            }
        }
    }
}
.error_content {
    display: flex;
    align-items: start;
    column-gap: 8px;
    .error_icon {
        font-size: 20px;
        line-height: 20px;
        color: #c00;
    }
    > div {
        display: flex;
        flex: 1;
        @include font16;
        justify-content: space-between;
        .close_icon_box {
            position: absolute;
            top: 16px;
            right: 16px;
            width: 32px;
            height: 32px;
            justify-content: center;
            align-items: center;
            .close_icon {
                font-size: 20px;
                line-height: 20px;
            }
        }
    }
}
@media (max-width: 768px) {
    .new_user_popup {
        width: auto;
        height: 100vh;
        padding: 0;
        display: flex;
        flex-direction: column;
        .popup_container_title,
        .popup_container,
        .footer {
            padding-left: 16px;
            padding-right: 16px;
        }
        .popup_container {
            flex: 1;
            overflow-y: scroll;
            padding-bottom: 0;
        }
        .footer {
            padding-top: 16px;
            flex-direction: column;
            row-gap: 16px;
        }
    }
    ::v-deep {
        .fs-popup {
            border-radius: 0 !important;
            .fs-popup-ctn {
                min-width: 350px;
                width: 382px;
                max-width: 100vw;
                // width: auto;
                height: 100vh;
                .fs-popup-body {
                    overflow: hidden;
                }
                &:has(.result_popup) {
                    height: auto;
                }
            }
        }
    }
    .result_popup {
        flex-direction: column;
        align-items: center;
        padding: 20px;
        width: auto;
        height: 100%;
    }
    // .error_content {
    //     display: flex;
    //     align-items: start;
    //     column-gap: 8px;
    //     .error_icon {
    //         font-size: 20px;
    //         line-height: 20px;
    //         color: #c00;
    //     }
    //     > div {
    //         display: flex;
    //         flex: 1;
    //         @include font16;
    //         justify-content: space-between;
    //         .close_icon_box {
    //             position: absolute;
    //             top: 16px;
    //             right: 16px;
    //             width: 32px;
    //             height: 32px;
    //             justify-content: center;
    //             align-items: center;
    //             .close_icon {
    //                 font-size: 20px;
    //                 line-height: 20px;
    //             }
    //         }
    //     }
    // }
}
</style>
