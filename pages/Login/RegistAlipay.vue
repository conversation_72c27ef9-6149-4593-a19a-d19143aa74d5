<template>
    <div class="empty">
        <div class="load" v-loading="loading"></div>
        <validate-message :message="err_msg"></validate-message>
    </div>
</template>

<script>
import { setCookieOptions } from "@/util/util"
import { mapState, mapActions } from "vuex"
import ValidateMessage from "@/components/ValidateMessage/ValidateMessage"

export default {
    name: "regist_alipay",
    layout: "empty",
    components: {
        ValidateMessage,
    },
    data() {
        return {
            err_msg: "",
            loading: true,
        }
    },
    mounted() {
        console.log("this.$route.query", this.$route.query)
        const data = this.$route.query
        console.log(data, "data")
        // return
        if (Object.keys(data).length < 1) {
            this.loading = false
            this.err_msg = "Error:Params is not defind!"
            setTimeout(() => {
                this.$router.replace(this.localePath({ name: "login" }))
            }, 2500)
            return
        }
        // return

        this.$axios
            .get("/api/login/alipayCallback", { params: data })
            .then((res) => {
                if (res.code != 200) {
                    this.loading = false
                    this.err_msg = res.message
                    setTimeout(() => {
                        this.$router.replace(this.localePath({ name: "login" }))
                    }, 1500)
                } else {
                    this.loading = false
                    if (res.data.access_token) {
                        this.$cookies.set("token_new", res.data.access_token)
                        this.getCart()
                        if (window.yaCounter71412688) {
                            yaCounter71412688.reachGoal("registrations", function () {})
                        }
                        this.getUserInfo(() => {
                            if (window.dataLayer) {
                                window.dataLayer.push({
                                    event: "uaEvent",
                                    eventCategory: "Login Page",
                                    eventAction: "login",
                                    eventLabel: "undefined",
                                    nonInteraction: false,
                                    loginStatus: `Login_${this.gaLoginString}`,
                                    userId: `${this.userInfo ? AES.encrypt(`${this.userInfo.customers_level}${this.userInfo.customers_id}`, `_-yu_xuan_3507-_`, `fs_com_phone2016`) : ""}`,
                                })
                            }
                        })
                        if (this.$route.query.redirect) {
                            if (Array.isArray(this.$route.query.redirect)) {
                                this.$router.replace(this.localePath({ path: this.$route.query.redirect[this.$route.query.redirect.length - 1] }))
                            } else {
                                this.$router.replace(this.localePath({ path: this.$route.query.redirect }))
                            }
                        } else {
                            this.$router.replace(this.localePath({ name: "home" }))
                        }
                    }
                    if (res.data.action && res.data.action == "RegistOrBind") {
                        this.$router.replace(this.localePath({ name: "cn_bind_account", query: { code: res.data.code, type: res.data.type } }))
                    }
                }
            })
            .catch((e) => {
                this.loading = false
                this.err_msg = e.message
                setTimeout(() => {
                    this.$router.replace(this.localePath({ name: "login" }))
                }, 1500)
            })
    },
    methods: {
        ...mapActions({
            getUserInfo: "userInfo/getUserInfo",
            getCart: "cart/getCart",
        }),
    },
}
</script>

<style lang="scss" scoped>
.empty {
    height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    .load {
        position: fixed;
        width: 100%;
        height: 100%;
        background-color: $bgColor2;
    }
}
</style>
