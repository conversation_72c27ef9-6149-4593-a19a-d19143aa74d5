<template>
    <div class="login_box">
        <!-- <h2 class="welcome">{{ $c("pages.Login.Sign_In") }}</h2> -->
        <div class="reg">
            {{ $c("pages.Login.Sign_in_to_FS_or") }}
            <nuxt-link :to="localePath({ path: '/register.html' })">{{ $c("pages.Login.Create_an_account") }}</nuxt-link>
        </div>
        <validate-message :message="actionsMsg" :type="actionsType"></validate-message>
        <form class="form" @submit.prevent="submit">
            <validate-message :message="err_msg"></validate-message>
            <div class="form_item">
                <div class="label">{{ accountLabel }}</div>
                <div class="inp_box">
                    <input
                        type="text"
                        class="text_box"
                        aria-label="email"
                        :class="{ error_input: errors.email }"
                        @focus="titlePoint('Email Address')"
                        v-model.trim="form.email"
                        @input="inputCheck('email')"
                        @blur="inputCheck('email')" />
                    <validate-error :error="errors.email"></validate-error>
                </div>
            </div>
            <div class="form_item form_item_pwd">
                <div class="label">
                    {{ $c("pages.Login.Password") }}
                </div>
                <div class="inp_box">
                    <input
                        :type="eye ? 'text' : 'password'"
                        :aria-label="eye ? 'text' : 'password'"
                        :class="{ error_input: errors.pwd }"
                        @focus="titlePoint('Password')"
                        class="pwd text_box"
                        v-model.trim="form.pwd"
                        @blur="inputCheck('pwd')" />
                    <validate-error :error="errors.pwd"></validate-error>
                    <password-over :value="showEye" class="hoverTip" :icon="false" position="top" trigger="hover">
                        <button type="button" class="eye icon iconfont" @focus="showEye = true" @blur="showEye = false" tabindex="0" :class="{ show: eye }" @click.stop="toogleEye" slot="trigger">
                            {{ eye ? "&#xe748;" : "&#xe706;" }}
                        </button>
                        <p>{{ eye ? $c("pages.Login.show_word") : $c("pages.Login.Hide_word") }}</p>
                    </password-over>
                </div>
            </div>
            <div>
                <!-- <g-recaptcha ref="grecaptcha" @getValidateCode="getValidateCode" @getGoogleRecaptcha="getGoogleRecaptcha"></g-recaptcha> -->
            </div>
            <div class="remember_box">
                <!-- <div class="left-box">
                    <label class="chk_box"><input type="checkbox" tabindex="0" @change="chkChange" v-model="form.chk" />{{ $c("pages.Login.Keep_me_signed_in") }}</label>
                    <fs-popover isAccessible>
                        <p class="login_tip">
                            {{ $c("pages.Login.To_keep_your_account_secure") }}
                        </p>
                    </fs-popover>
                </div> -->
                <nuxt-link tabindex="0" :to="localePath({ name: 'forgot_password' })">{{ $c("pages.Login.Forgot_password") }}</nuxt-link>
            </div>
            <fs-button class="sign_in_btn" tabindex="0" type="red" :loading="form.loading" htmlType="submit">{{ $c("pages.Login.Sign_In") }}</fs-button>
            <div class="reg_bottom">
                <!-- <nuxt-link tabindex="-1" class="create_btn" :to="localePath({ path: '/register.html' + routerParams })">
                    <span tabindex="0" :style="{ width: `100%` }" @click="createPoint">{{ $c("pages.Login.Create_an_account") }}</span>
                </nuxt-link> -->
                <a tabindex="-1" class="create_btn" :href="redirectPath">
                    <span tabindex="0" :style="{ width: `100%` }" @click="createPoint">{{ $c("pages.Login.Create_an_account") }}</span>
                </a>
                <!-- <nuxt-link  :to="localePath({ path: '/register.html' })">{{$c('pages.Login.Create_an_account')}}</nuxt-link> -->
            </div>
            <div class="third_login_box">
                <div class="third_title">
                    <span class="line"></span>
                    <span class="text">{{ $c("pages.Login.or_sign_in_with") }}</span>
                    <span class="line"></span>
                </div>
                <div class="third_login">
                    <a v-for="item in icon" :key="item.type" class="aicon" tabindex="0" role="link" :title="item.title" @click="toThirdParty(item.type)">
                        <img class="icon iconfont" :src="item.icon" alt="" />
                        <img :src="item.url" />
                    </a>
                </div>
            </div>
            <!-- <div class="reg_bottom">
                <div class="reg_bottom_title">
                    <span class="line"></span>
                    <span class="text">{{ $c("pages.Login.Or") }}</span>
                    <span class="line"></span>
                </div>
                <nuxt-link tabindex="-1" class="create_btn" :to="localePath({ path: '/register.html' })">
                    <fs-button type="gray" tabindex="0" :style="{ width: `100%` }" @click="createPoint">{{ $c("pages.Login.Create_an_account") }}</fs-button>
                </nuxt-link>
            </div> -->
        </form>
    </div>
</template>

<script>
import { mapState, mapGetters, mapActions } from "vuex"
import ValidateError from "@/components/ValidateError/ValidateError"
import FsButton from "@/components/FsButton/FsButton"
import FsPopover from "@/components/FsPopover"
import PasswordOver from "./PasswordOver"
import ValidateMessage from "@/components/ValidateMessage/ValidateMessage"
import { email_valdate, SG_PHONE_REGEXP } from "@/constants/validate"
import AES from "@/util/AES.js"
import GRecaptcha from "@/components/GRecaptcha/GRecaptcha"
import { parseQueryString, setCookieOptions } from "@/util/util.js"
import { isNeedGrecaptcha, getRecaptchaToken } from "@/util/grecaptchaHost"

export default {
    layout: "account",
    name: "Login",
    components: {
        ValidateError,
        FsButton,
        FsPopover,
        PasswordOver,
        ValidateMessage,
        GRecaptcha,
    },
    data() {
        return {
            err_msg: "",
            eye: false,
            form: {
                email: "",
                pwd: "",
                chk: true,
                loading: false,
            },
            errors: {
                email: "",
                pwd: "",
            },
            icon: [
                {
                    type: "google",
                    // icon: "&#xf302;",
                    url: "https://resource.fs.com/mall/generalImg/20250307172611ndelep.svg",
                    icon: "https://resource.fs.com/mall/generalImg/20250307172221bbkgfk.svg",
                    title: this.$c("pages.Login.Sign_in_with_google"),
                },
                {
                    type: "paypal",
                    // icon: "&#xf304;",
                    url: "https://resource.fs.com/mall/generalImg/20250307172611qb69ai.svg",
                    icon: "https://resource.fs.com/mall/generalImg/20250307172221r6z5hh.svg",
                    title: this.$c("pages.Login.Sign_in_with_Paypal"),
                },
                {
                    type: "facebook",
                    // icon: "&#xf303;",
                    url: "https://resource.fs.com/mall/generalImg/202503071726110pqvhm.svg",
                    icon: "https://resource.fs.com/mall/generalImg/2025030717222161qecm.svg",
                    title: this.$c("pages.Login.Sign_in_with_Facebook"),
                },
                {
                    type: "linkin",
                    // icon: "&#xf301;",
                    url: "https://resource.fs.com/mall/generalImg/202503071726113i4zf8.svg",
                    icon: "https://resource.fs.com/mall/generalImg/202503071722218qyie9.svg",
                    title: this.$c("pages.Login.Sign_in_with_Linkedin"),
                },
            ],
            actionsType: null,
            actionsMsg: "",
            timer: null,
            isntLogin: true,
            recaptchaTp: false,
            recaptchaVal: "",
            boxGiftCartId: "",
            showEye: false,
            showTips: false,
        }
    },
    computed: {
        ...mapState({
            isMobile: (state) => state.device.isMobile,
            isLogin: (state) => state.userInfo.isLogin,
            userInfo: (state) => state.userInfo.userInfo,
        }),
        ...mapGetters({
            gaLoginString: "userInfo/gaLoginString",
            isSingapore: "webSiteInfo/isSingapore",
        }),
        accountLabel() {
            return this.isSingapore ? "Email address or Phone number" : this.$c("pages.Login.Email_Address")
        },
        //当是由open Api平台跳转过来时，点击注册跳转到Open API平台的注册页面
        redirectPath() {
            const { source = "", redirect = "" } = this.$route.query
            let redirectPath = "/register.html"
            if (source === "api" && redirect) {
                const { host, protocol } = new URL(redirect)
                redirectPath = `${protocol}//${host}/register`
            } else {
                redirectPath = this.localePath({ path: redirectPath })
            }
            return redirectPath
        },
    },
    asyncData({ app, $axios, route, query, redirect }) {
        console.log(route)
        if (query.params) {
        } else if (query.pass) {
        } else {
            return $axios.post("/api/user/info?getMore=1").then((res) => {
                if (res.data.isLogin) {
                    redirect(app.localePath({ name: "my-account" }))
                } else {
                    if (query && query.redirect) {
                        if (Array.isArray(query.redirect)) {
                            redirect(app.localePath({ path: route.path, query: Object.assign({}, query, { redirect: query.redirect[query.redirect.length - 1] }) }))
                        }
                    }
                }
            })
        }
    },
    mounted() {
        if (window && window.localStorage && window.localStorage.getItem("fs_email_submitted")) {
            window.localStorage.removeItem("fs_email_submitted")
        }
        console.log(AES.encrypt("AaBbCc321", "_-yu_xuan_3507-_", "fs_com_phone2016"))
        if (this.$route.query.params && this.$route.query.params.length) {
            this.getActionType("email")
        }
        if (this.$route.query.pass && this.$route.query.pass.length) {
            this.getActionType("pass")
        }
        if (this.$route.query.boxGiftCartId && this.$route.query.boxGiftCartId.length) {
            this.boxGiftCartId = this.$route.query.boxGiftCartId
        }
    },
    methods: {
        ...mapActions({
            getUserInfo: "userInfo/getUserInfo",
            getCart: "cart/getCart",
        }),
        toogleEye() {
            this.eye = !this.eye
        },
        chkChange() {
            this.$cookies.set("keep_signed", this.form.chk)
            if (this.form.chk && window.dataLayer) {
                window.dataLayer.push({
                    event: "uaEvent",
                    eventCategory: "Login Page",
                    eventAction: "keep_sign_in",
                    eventLabel: "undefined",
                    nonInteraction: false,
                })
            }
        },
        inputCheck(attr) {
            const val = this.form[attr]

            const map = {
                email: () => {
                    if (this.isSingapore) {
                        if (!val.replace(/\s+/g, "")) return "Please enter your email address or phone number."
                        if (!SG_PHONE_REGEXP.test(val) && !email_valdate.test(this.form[attr])) return "Please enter a valid email address or phone number."
                    } else {
                        if (!val.replace(/\s+/g, "")) return this.$c("form.validate.email.email_required")
                        if (!email_valdate.test(val)) return this.$c("form.validate.email.email_valid")
                    }
                },
                pwd: () => {
                    if (!val.replace(/\s+/g, "")) return this.$c("form.validate.password.password_required")
                },
            }
            this.errors[attr] = map[attr] ? map[attr]() || "" : ""
            return this.errors[attr]
        },
        verifyWrapper(attrs = []) {
            attrs.forEach((attr) => {
                this.inputCheck(attr)
            })
            return attrs.some((attr) => this.errors[attr])
        },
        async submit() {
            const attrs = ["email", "pwd"]
            if (this.form.loading || this.verifyWrapper(attrs)) {
                return
            }
            this.form.loading = true
            let requestHeader = {}
            // if (this.isntLogin) {
            //     const { recaptchaTp, headers = {}, errorMsg = "grecaptcha_error" } = await getRecaptchaToken()
            //     if (!recaptchaTp) {
            //         this.form.loading = false
            //         this.$message.error(this.$c(`pages.Login.regist.${errorMsg}`))
            //         return
            //     }
            //     if (headers) {
            //         requestHeader = headers
            //     }
            // }

            // this.$axios.post("/api/user/login", { customers_email_address: this.form.email, customers_password:
            // this.form.pwd, keep_signed: this.form.chk ? 1 : 0 }).then(res => {

            // 无线选择器处理
            let cartId
            const { redirect, cartId: queryCartId } = this.$route.query
            if (redirect) {
                let query_query = parseQueryString(this.$route.query.redirect)
                query_query.wireless_product && (cartId = query_query.wireless_product)
            }

            // 无线光模块方案选择工具
            let opticalModuleCartId
            if (queryCartId) {
                opticalModuleCartId = queryCartId
            }

            const params = {
                customers_email_address: this.form.email,
                customers_password: AES.encrypt(this.form.pwd, "_-yu_xuan_3507-_", "fs_com_phone2016"),
                keep_signed: this.form.chk ? 1 : 0,
                cartId,
                opticalModuleCartId,
                redirect: this.$route.query.redirect || "",
            }
            this.$axios
                .post("/api/user/login", params, { headers: { ...requestHeader, boxGiftCartId: this.boxGiftCartId } })
                .then((res) => {
                    this.$cookies.remove("login")
                    if (res.data.access_token) {
                        this.$cookies.set("token_new", res.data.access_token)
                    }
                    this.getUserInfo(() => {
                        console.log(this.userInfo)
                        if (window.dataLayer) {
                            window.dataLayer.push({
                                event: "uaEvent",
                                eventCategory: "Login Page",
                                eventAction: "login",
                                eventLabel: "undefined",
                                nonInteraction: false,
                                loginStatus: `Login_${this.gaLoginString}`,
                                userId: `${this.userInfo ? AES.encrypt(`${this.userInfo.customers_level}${this.userInfo.customers_id}`, `_-yu_xuan_3507-_`, `fs_com_phone2016`) : ""}`,
                            })
                        }
                    })
                    this.getCart()
                    console.log("____111")
                    console.log(this.$route.query.redirect)
                    console.log(this.localePath({ path: this.localePath({ path: this.$route.query.redirect }) }))
                    if (this.$route.query.redirect) {
                        if (Array.isArray(this.$route.query.redirect)) {
                            location.href = this.$route.query.redirect[this.$route.query.redirect.length - 1]
                        } else {
                            try {
                                let pica8_p_id = window.sessionStorage.getItem("pica8_p_id")
                                if (pica8_p_id && this.$route.query.redirect.includes("pica8-resources")) {
                                    this.$axios
                                        .post("/api/sendPicOSVSoftwareEmail")
                                        .then((res) => {})
                                        .catch((err) => {})
                                }
                                window.sessionStorage.removeItem("pica8_p_id")
                            } catch (e) {}
                            location.href = this.$route.query.redirect
                        }
                    } else {
                        location.href = this.localePath({ path: "/" })
                    }

                    this.initGrecaptcha()
                    if (this.$refs.grecaptcha) {
                        this.$refs.grecaptcha.grecaptchaError = ""
                    }
                })
                .catch((err) => {
                    this.initGrecaptcha()
                    this.form.loading = false
                    if (window.dataLayer) {
                        window.dataLayer.push({
                            event: "uaEvent",
                            eventCategory: "Login Page",
                            eventAction: "login_fail",
                            eventLabel: "undefined",
                            nonInteraction: false,
                        })
                    }
                    if (err.code === 400) {
                        this.err_msg = err.message
                        setTimeout(() => {
                            this.err_msg = ""
                        }, 3000)
                    } else if (err.code === 422) {
                        this.errors.email = err.errors.customers_email_address
                        this.errors.pwd = err.errors.customers_password
                    }

                    if (err.code === 403) {
                        this.$message.error(err.message)
                    }
                    if (isNeedGrecaptcha(window.location.hostname)) {
                        if (err.code === 409 && this.$refs.grecaptcha) {
                            this.$refs.grecaptcha.grecaptchaError = this.$c("pages.Login.regist.grecaptcha_error")
                        }
                        this.isntLogin = true
                        if (this.timer) {
                            clearInterval(this.timer)
                        }
                        this.timer = setInterval(() => {
                            // this.isntLogin = false
                            if (this.$refs.grecaptcha) {
                                this.$refs.grecaptcha.grecaptchaError = ""
                            }
                        }, 10000)
                    }
                })
                .finally(() => {
                    this.$cookies.remove("dismissInvitationsFromCompany")
                    this.$cookies.remove("dismissInvitationTip")
                    this.$cookies.remove("dismissRequestToJoinTeamsTip")
                })
        },
        // 跳转到第三方
        async toThirdParty(type) {
            let url = ""
            if (type === "google") {
                url = "/api/login/socialite/google"
            } else if (type === "paypal") {
                url = "/api/login/socialite/paypal"
            } else if (type === "facebook") {
                url = "/api/login/socialite/facebook"
            } else if (type === "linkin") {
                url = "/api/login/socialite/linkedin"
            }
            if (window.dataLayer) {
                window.dataLayer.push({
                    event: "uaEvent",
                    eventCategory: "Login Page",
                    eventAction: "login_with_other_socialmethod",
                    eventLabel: type,
                    nonInteraction: false,
                })
            }
            const res = await this.$axios.get(url)
            window.location.href = res.data.redirectUrl
        },
        titlePoint(title) {
            if (window.dataLayer) {
                window.dataLayer.push({
                    event: "uaEvent",
                    eventCategory: "Login Page",
                    eventAction: "login_information",
                    eventLabel: `Login-${title} Input`,
                    nonInteraction: false,
                })
            }
        },
        createPoint() {
            if (window.dataLayer) {
                window.dataLayer.push({
                    event: "uaEvent",
                    eventCategory: "Login Page",
                    eventAction: "create_account",
                    eventLabel: "Create an account",
                    nonInteraction: false,
                })
            }
        },
        // 谷歌人机校验
        getValidateCode(val) {
            this.recaptchaTp = val.recaptchaTp
            if (val.recaptchaTp) {
                this.recaptchaVal = val.recaptchaVal
                this.submit()
            }
        },
        getGoogleRecaptcha(val) {
            this.recaptchaTp = val.recaptchaTp
            if (val.recaptchaTp) {
                this.recaptchaVal = val.recaptchaVal
            }
        },
        // 获取激活状态
        getActionType(str) {
            if (str == "email") {
                this.$axios
                    .post("/api/user/active", {
                        params: this.$route.query.params,
                    })
                    .then((res) => {
                        if (res.code === 200) {
                            this.actionsMsg = this.$c("pages.StateFeedback.success2")
                            this.actionsType = "success"
                            // history.pushState({}, "", this.localePath({ name: "login" }))
                            if (res.data.resultType === 1) {
                                //组织注册结果页
                                this.$router.push(this.localePath({ name: "apply_result", query: { type: "3", number: res.data.caseNumber } }))
                            } else if (res.data.resultType === 2) {
                                // 申请加入页面
                                this.$router.push(this.localePath({ path: "/apply_join.html", query: { id: res.data.cid, from: "active" } }))
                            } else if (res.data.resultType === 3) {
                                //重复申请页
                                this.$router.push(this.localePath({ name: "apply_result", query: { type: "4" } }))
                            }
                        }
                    })
                    .catch((error) => {
                        if (error.code === 400) {
                            this.actionsMsg = this.$c("pages.StateFeedback.error")
                            this.actionsType = "error"
                            history.pushState({}, "", this.localePath({ name: "login" }))
                        } else if (error.code === 422) {
                            this.actionsMsg = this.$c("pages.StateFeedback.error")
                            this.actionsType = "error"
                            history.pushState({}, "", this.localePath({ name: "login" }))
                        }
                    })
            } else {
                if (this.$route.query.pass == "yes") {
                    this.actionsMsg = this.isSingapore ? "Congratulation! Your account is now activated. Sign in with your email address or phone number and password." : this.$c("pages.StateFeedback.success")
                    this.actionsType = "success"
                } else {
                    this.actionsMsg = this.$c("pages.StateFeedback.error")
                    this.actionsType = "error"
                }
                history.pushState({}, "", this.localePath({ name: "login" }))
            }
        },
        // 初始化谷歌验证
        initGrecaptcha() {
            this.recaptchaTp = false
            this.recaptchaVal = ""
        },
    },
}
</script>

<style lang="scss" scoped>
.login_box {
    width: 100%;
    &::v-deep {
        .validate-message {
            width: 100%;
        }
    }
}
.welcome {
    text-align: center;
    margin: 16px 0 20px 0;
    display: none;
    @include font26;
}
.reg {
    @include font13;
    color: $textColor1;
    text-align: center;
    display: none;
    > a {
        color: $textColor8;
    }
}
.form {
    margin-top: 0px !important;
    .validate-message {
        margin-bottom: 10px;
    }
}
.forgot_box {
    display: flex;
    justify-content: flex-end;
    a {
        color: $textColor3;
        @include font12;
    }
}

.form_item {
    margin-bottom: 16px;
    &.form_item_pwd {
    }
    .label {
        display: flex;
        align-items: center;
        justify-content: space-between;
        color: $textColor3;
        margin-bottom: 4px;
        @include font12;
        a {
            color: #4b4b4d;
        }
    }
    .pwd {
        padding-right: 44px;
    }
    .inp_box {
        position: relative;
        .eye {
            font-size: 16px;
            width: 28px;
            height: 28px;
            padding: 6px;
            color: $textColor3;
            cursor: pointer;
            &:hover {
                color: #19191a;
                border-radius: 4px;
                background: #ffffff;
            }
        }
        /* .error_input {
            @include errorInput;
        } */
        .fs-popover.hoverTip {
            position: absolute;
            top: 7px;
            right: 7px;
            @include mediaM {
                right: 0;
                top: 1px;
                width: 40px;
                height: 40px;
                padding: 13px 12px;
                display: flex;
                align-items: center;
                justify-content: center;
                .eye:hover {
                    background-color: transparent;
                    border-radius: 0;
                }
            }
        }
        .text_box {
            &:-webkit-autofill,
            &:-webkit-autofill:active,
            &:-webkit-autofill:focus,
            &:-webkit-autofill:hover {
                -webkit-box-shadow: 0 0 0px 1000px #f6f6f8 inset !important;
            }
            border: 1px solid #fff;
            &:hover {
                background: linear-gradient(0deg, rgba(25, 25, 26, 0.04), rgba(25, 25, 26, 0.04)), #f6f6f8 !important;
            }
            &:focus {
                border: 1px solid #707070;
            }
        }
    }
}
.sign_in_btn {
    width: 100%;
    margin: 16px 0;
}

.remember_box {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .left-box {
        display: flex;
        align-items: center;
    }
    > a {
        @include font13;
        // color: #707070;
        color: #0060bf;
    }
    .iconfont-tip {
        font-size: 16px;
        display: inline-block;
        font-weight: normal;
        width: 16px;
        height: 16px;
        line-height: 1;
        color: $textColor3;
        cursor: pointer;
        position: relative;
    }
    .chk_box {
        display: flex;
        align-items: center;
        color: $textColor3;
        @include font13;
        cursor: pointer;
        > input {
            width: 18px;
            height: 18px;
            font-size: 18px;
            margin-right: 8px;
            &:before {
                display: block;
            }
        }
    }
    .login_tip {
        @include pc_tip;
        min-width: 300px;
        @media (max-width: 960px) {
            @include m_tip;
        }
    }
}

.third_login_box {
    // margin-bottom: 32px;
    margin-top: 16px;
    // margin-bottom: 24px;
    .third_title {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 12px;
        justify-content: center;
        .text {
            color: $textColor3;
            @include font13;
            padding: 0;
        }
        .line {
            display: none;
            flex: 1 1 auto;
            height: 1px;
            background: $bgColor2;
        }
    }
    .third_login {
        display: flex;
        // justify-content: flex-start;
        justify-content: center;
        column-gap: 8px;
        .aicon {
            display: flex;
            align-items: center;
            // height: 16px;
            color: $textColor3;
            // margin-right: 24px;
            cursor: pointer;
            &:last-child {
                margin-right: 0;
            }
            img {
                width: 16px;
                height: 16px;
                display: none;
            }
            .iconfont {
                display: block;
            }
            &:hover {
                text-decoration: none;
                .iconfont {
                    display: none;
                }
                img {
                    display: block;
                }
            }

            .third_login_popover {
                padding: 4px;
            }
        }
    }
}
.reg_bottom {
    // @include font14;
    // color: $textColor1;
    // font-weight: 600;
    // text-align: center;
    // display: block;
    // margin-top: 24px;
    // margin-bottom: 12px;
    padding: 11px 24px;
    background-color: #f6f6f8;
    border-radius: 4px;
    text-align: center;

    > a {
        @include font12;
    }
    .reg_bottom_title {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 16px;
        .text {
            color: $textColor3;
            @include font13;
            padding: 0 12px;
        }
        .line {
            flex: 1 1 auto;
            height: 1px;
            background: $bgColor2;
        }
    }
    .create_btn {
        margin-bottom: 16px;
        cursor: pointer;
        .fs-button {
            // border-radius: 9999px;
            color: #0060bf;
            &:hover {
                &::before {
                    // border-radius: 9999px;
                }
            }
        }
    }
}
@media (max-width: 768px) {
    .welcome {
        display: block;
    }
}
</style>
