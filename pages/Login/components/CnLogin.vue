<template>
    <div class="cn_login_form">
        <!-- <h2 class="welcome">{{$c('pages.Login.Welcome')}}</h2> -->
        <div class="reg">
            {{ $c("pages.Login.Sign_in_to_FS_or") }}
            <nuxt-link :to="localePath({ path: '/register.html' })">{{ $c("pages.Login.Create_an_account") }}</nuxt-link>
        </div>
        <validate-message :message="actionsMsg" :type="actionsType"></validate-message>
        <ul class="cn_login_type">
            <li v-for="(t, i) in login_type_list" :key="i" @click.stop="selectLoginType(t)" :class="{ active: login_type == t.type }">{{ t.name }}</li>
        </ul>
        <validate-message :message="err_msg"></validate-message>

        <form @submit.prevent="submit" class="form" v-if="login_type == 'account'">
            <div class="form_item">
                <!-- <div class="label">{{$c('pages.Login.Email_Address')}}</div> -->
                <div class="label">{{ $c("form.form.phone_number") }}/{{ $c("form.form.email_ddress") }}</div>
                <div class="inp_box">
                    <input type="text" class="text_input" :class="{ err_input: errors.account }" @focus="titlePoint('Email Address')" v-model.trim="form.account" @input="inputCheck('account')" />
                    <!-- <validate-error :error="errors.account"></validate-error> -->
                    <p class="new_err_tips" v-show="errors.account">
                        <i class="icon iconfont">&#xe66a;</i>
                        <span>{{ errors.account }}</span>
                    </p>
                </div>
            </div>
            <div class="form_item form_item_pwd">
                <div class="label">
                    {{ $c("pages.Login.Password") }}
                </div>
                <div class="inp_box">
                    <input :type="eye ? 'text' : 'password'" :class="{ err_input: errors.pwd }" @focus="titlePoint('Password')" class="pwd text_input" v-model.trim="form.pwd" @blur="inputCheck('pwd')" />
                    <!-- <validate-error :error="errors.pwd"></validate-error> -->
                    <p class="new_err_tips" v-show="errors.pwd">
                        <i class="icon iconfont">&#xe66a;</i>
                        <span>{{ errors.pwd }}</span>
                    </p>
                    <password-over class="hoverTip" :icon="false" position="top">
                        <span class="eye icon iconfont" :class="{ show: eye }" @click.stop="toogleEye" slot="trigger">{{ eye ? "&#xe748;" : "&#xe706;" }}</span>
                        <p>{{ eye ? $c("pages.Login.show_word") : $c("pages.Login.Hide_word") }}</p>
                    </password-over>
                    <!-- <span class="eye icon iconfont" :class="{ show: eye }" @click.stop="toogleEye">{{ eye ? "&#xf153;" : "&#xe706;" }}</span> -->
                </div>
            </div>
            <div class="remember_box">
                <!-- <div>
                    <label class="chk_box">
                        <input type="checkbox" @change="chkChange" v-model="chk" />
                        {{ $c("pages.Login.Keep_me_signed_in") }}
                    </label>
                    <fs-popover>
                        <p class="login_tip">
                            {{ $c("pages.Login.To_keep_your_account_secure") }}
                        </p>
                    </fs-popover>
                </div> -->
                <nuxt-link :to="localePath({ name: 'forgot_password' })">
                    {{ $c("pages.Login.Forgot_password") }}
                </nuxt-link>
            </div>
            <fs-button class="sign_in_btn" type="red" :loading="submit_loading" htmlType="submit">
                {{ $c("pages.Login.Sign_In") }}
                <!-- 登录 -->
            </fs-button>
        </form>
        <form @submit.prevent="submit" class="form" v-if="login_type == 'verification'">
            <!-- <validate-message :message="err_msg"></validate-message> -->
            <div class="form_item">
                <!-- <div class="label">{{$c('pages.Login.Email_Address')}}</div> -->
                <div class="label">{{ $c("form.form.phone_number") }}</div>
                <div class="inp_box">
                    <input type="text" :class="{ err_input: errors.account }" @focus="titlePoint('Email Address')" v-model.trim="form.account" @input="inputCheck('account')" />
                    <!-- <validate-error :error="errors.account"></validate-error> -->
                    <p class="new_err_tips" v-show="errors.account">
                        <i class="icon iconfont">&#xe66a;</i>
                        <span>{{ errors.account }}</span>
                    </p>
                </div>
            </div>
            <div class="form_item form_item_verification">
                <!-- <div class="label">{{ $c("pages.Login.Email_Address") }}</div> -->
                <div class="label">{{ $c("form.validate.verification.name") }}</div>
                <div class="inp_box input_box_verification">
                    <span @click.stop="getVerification" :class="{ disabled: verification.loading }">{{ verification.txt }}</span>
                    <input type="text" :class="{ err_input: errors.code }" @focus="titlePoint('Email Address')" v-model.trim="form.code" @input="inputCheck('code')" />
                    <!-- <validate-error :error="errors.code"></validate-error> -->
                    <p class="new_err_tips" v-show="errors.code">
                        <i class="icon iconfont">&#xe66a;</i>
                        <span>{{ errors.code }}</span>
                    </p>
                </div>
            </div>

            <div class="remember_box">
                <!-- <div>
                    <label class="chk_box">
                        <input type="checkbox" @change="chkChange" v-model="chk" />
                        {{ $c("pages.Login.Keep_me_signed_in") }}
                    </label>
                    <fs-popover>
                        <p class="login_tip">
                            {{ $c("pages.Login.To_keep_your_account_secure") }}
                        </p>
                    </fs-popover>
                </div> -->
                <nuxt-link :to="localePath({ name: 'forgot_password' })">
                    {{ $c("pages.Login.Forgot_password") }}
                </nuxt-link>
            </div>
            <fs-button class="sign_in_btn" type="red" :loading="submit_loading" htmlType="submit">
                {{ $c("pages.Login.Sign_In") }}
                <!-- 登录 -->
            </fs-button>
        </form>
        <fs-button class="create_btn" type="gray" :style="{ width: `100%` }" @click="createPoint">{{ $c("pages.Login.Create_an_account") }}</fs-button>

        <div class="third_login_box" v-if="!isCnTr">
            <div class="top">
                <div class="third_title">
                    <span class="line"></span>
                    <span class="text">
                        {{ $c("pages.Login.or_sign_in_with") }}
                    </span>
                    <span class="line"></span>
                </div>
                <div class="third_login">
                    <a class="iconfont" :class="`icon_${item.type}`" v-for="item in icon" :key="item.type" :title="item.title" @click="toThirdParty(item.type)"> </a>
                </div>
            </div>
            <!-- <div class="agreement">
                <p
                    class="info1"
                    v-html="
                        $c('pages.Login.I_agree_to_FSs_Login')
                            .replace('XXX1', localePath({ name: 'privacy-policy' }))
                            .replace('XXX2', localePath({ name: 'terms-of-use' }))
                    "></p>
            </div> -->
            <div class="none">
                <img src="https://resource.fs.com/mall/generalImg/202308251159102cnlxr.png" alt="" />
                <img src="https://resource.fs.com/mall/generalImg/20230825115910167fa9.png" alt="" />
                <img src="https://resource.fs.com/mall/generalImg/202308251159094wt1q4.png" alt="" />
                <img src="https://resource.fs.com/mall/generalImg/20230825115909ynmu1z.png" alt="" />
            </div>
        </div>
        <!-- <div class="reg_bottom">
            <div class="reg_bottom_title">
                <span class="line"></span> -->
        <!-- <span class="text">{{ $c("components.smallComponents.pHeader.newCustomer") }}</span> -->
        <!-- <span class="text">{{ $c("pages.Login.Or") }}</span>
                <span class="line"></span>
            </div> -->
        <!-- <nuxt-link class="create_btn" :to="localePath({ path: '/register.html' })"> -->
        <!-- <fs-button class="create_btn" type="gray" :style="{ width: `100%` }" @click="createPoint">{{ $c("pages.Login.Create_an_account") }}</fs-button> -->
        <!-- </nuxt-link> -->
        <!-- <nuxt-link  :to="localePath({ path: '/register.html' })">{{$c('pages.Login.Create_an_account')}}</nuxt-link> -->
        <!-- </div> -->
        <form v-if="alipay_data.action" id="alipaysubmit" name="alipaysubmit" :action="alipay_data.action" method="GET">
            <input type="hidden" v-for="(k, j) in Object.keys(alipay_data.data)" :key="j" :name="k" :value="alipay_data.data[k]" />
            <input type="submit" value="提交" style="display: none" />
        </form>
    </div>
</template>

<script>
import { mapState, mapGetters, mapActions } from "vuex"
import ValidateError from "@/components/ValidateError/ValidateError"
import FsButton from "@/components/FsButton/FsButton"
import FsPopover from "@/components/FsPopover"
import ValidateMessage from "@/components/ValidateMessage/ValidateMessage"
import { setCookieOptions, parseQueryString } from "@/util/util"
import { cn_all_phone, cn_mobile_tel, cn_email } from "@/constants/validate"
import AES from "@/util/AES.js"
import PasswordOver from "./PasswordOver"
import { isNeedGrecaptcha, getRecaptchaToken } from "../../../util/grecaptchaHost"

export default {
    components: {
        FsButton,
        FsPopover,
        ValidateError,
        ValidateMessage,
        PasswordOver,
    },
    mounted() {
        if (this.$route.query.params && this.$route.query.params.length) {
            this.getActionType("email")
        }
        if (this.$route.query.pass && this.$route.query.pass.length) {
            this.getActionType("pass")
        }
        this.getAlipayLoginInfo()
    },

    data() {
        return {
            err_msg: "",
            actionsType: null,
            actionsMsg: "",
            login_type_list: [
                { name: `${this.$c("pages.OrderDetail.bill.accountNo")}${this.$c("pages.Login.Sign_In")}`, type: `account` },
                { name: `${this.$c("form.validate.verification.name")}${this.$c("pages.Login.Sign_In")}`, type: `verification` },
            ],
            login_type: `account`,
            chk: false,
            submit_loading: false,
            form: {
                account: "",
                pwd: "",
                code: "",
            },
            errors: {
                account: "",
                pwd: "",
                code: "",
            },
            eye: false,
            icon: [
                {
                    type: `weixin`,
                    url: `https://cn.fs.com/weixin/wechatlogin.php`,
                    title: "微信",
                },
                {
                    type: `alipay`,
                    url: `https://cn.fs.com/weixin/wechatlogin.php`,
                    title: "支付宝",
                },
            ],
            isntLogin: true,
            recaptchaTp: false,
            recaptchaVal: "",
            verification: {
                time: 60,
                txt: this.$c("form.validate.verification.get"),
                loading: false,
                timeOut: null,
                ticket: "",
                randstr: "",
                key: "",
            },
            API: {
                login: `/api/user/login`, //登录
                loginByCode: `/api/user/loginByCode`, //验证码登录
                verificationCodes: `/api/user/verificationCodes`, //发送验证码
                checkVerification: `/api/user/checkVerification`, //验证是否需要滑动验证
            },
            alipay_data: {},
        }
    },
    computed: {
        ...mapState({
            isMobile: (state) => state.device.isMobile,
            isLogin: (state) => state.userInfo.isLogin,
            userInfo: (state) => state.userInfo.userInfo,
            website: (state) => state.webSiteInfo.website,
        }),
        ...mapGetters({
            gaLoginString: "userInfo/gaLoginString",
            isCnTr: "webSiteInfo/isCnTr",
        }),
    },
    methods: {
        ...mapActions({
            getUserInfo: "userInfo/getUserInfo",
            getCart: "cart/getCart",
        }),
        // 获取激活状态
        getActionType(str) {
            if (str == "email") {
                this.$axios
                    .post("/api/user/active", {
                        params: this.$route.query.params,
                    })
                    .then((res) => {
                        if (res.code === 200) {
                            this.actionsMsg = this.$c("pages.StateFeedback.success2")
                            this.actionsType = "success"
                            history.pushState({}, "", this.localePath({ name: "login" }))
                        }
                    })
                    .catch((error) => {
                        if (error.code === 400) {
                            this.actionsMsg = this.$c("pages.StateFeedback.error")
                            this.actionsType = "error"
                            history.pushState({}, "", this.localePath({ name: "login" }))
                        } else if (error.code === 422) {
                            this.actionsMsg = this.$c("pages.StateFeedback.error")
                            this.actionsType = "error"
                            history.pushState({}, "", this.localePath({ name: "login" }))
                        }
                    })
            } else {
                if (this.$route.query.pass == "yes") {
                    this.actionsMsg = this.$c("pages.StateFeedback.success")
                    this.actionsType = "success"
                } else {
                    this.actionsMsg = this.$c("pages.StateFeedback.error")
                    this.actionsType = "error"
                }
                history.pushState({}, "", this.localePath({ name: "login" }))
            }
        },
        selectLoginType(item) {
            if (item.type == this.login_type) return
            this.login_type = item.type
            for (let item in this.errors) {
                this.errors[item] = ""
            }
        },
        inputCheck(attr) {
            let flag = false
            if (attr === "account") {
                if (!this.form[attr].replace(/\s+/g, "")) {
                    if (this.login_type == "account") {
                        this.errors[attr] = this.$c("form.validate.phone_email.required")
                    } else {
                        this.errors[attr] = this.$c("form.validate.phone.phone_required")
                    }
                    flag = true
                } else {
                    if (this.login_type == "account") {
                        if (!(cn_all_phone.test(this.form[attr]) || cn_mobile_tel.test(this.form[attr]) || cn_email.test(this.form[attr]))) {
                            //cn_mobile_tel
                            this.errors[attr] = this.$c("form.validate.phone_email.validate")
                            flag = true
                        } else {
                            this.errors[attr] = ""
                        }
                    } else {
                        if (!(cn_all_phone.test(this.form[attr]) || cn_mobile_tel.test(this.form[attr]))) {
                            this.errors[attr] = this.$c("form.validate.phone.phone_min")
                            flag = true
                        } else {
                            this.errors[attr] = ""
                        }
                    }
                }
            } else if (attr === "pwd") {
                if (!this.form[attr].replace(/\s+/g, "")) {
                    this.errors[attr] = this.$c("form.validate.password.password_required")
                    flag = true
                } else {
                    this.errors[attr] = ""
                }
            } else if (attr === "code") {
                if (!String(this.form[attr]).replace(/\s+/g, "")) {
                    this.errors[attr] = this.$c("form.validate.verification.required")
                    flag = true
                } else {
                    this.errors[attr] = ""
                }
            }
            return flag
        },
        titlePoint(n) {
            this.$emit("titlePoint", n)
        },
        toogleEye() {
            this.eye = !this.eye
        },
        chkChange() {
            this.$cookies.set("keep_signed", this.chk)
            if (this.chk && window.dataLayer) {
                window.dataLayer.push({
                    event: "uaEvent",
                    eventCategory: "Login Page",
                    eventAction: "keep_sign_in",
                    eventLabel: "undefined",
                    nonInteraction: false,
                })
            }
        },
        // 跳转到第三方
        async toThirdParty(type) {
            let url = ""
            if (type === "weixin") {
                url = "/api/login/wechatLogin"
                const res = await this.$axios.get(url)
                if (res && res.code == 200) {
                    window.location.href = res.data.url
                }
            } else if (type === "alipay") {
                document.forms["alipaysubmit"].submit()
            }
            if (window.dataLayer) {
                window.dataLayer.push({
                    event: "uaEvent",
                    eventCategory: "Login Page",
                    eventAction: "login_with_other_socialmethod",
                    eventLabel: type,
                    nonInteraction: false,
                })
            }
        },

        getVerification() {
            if (this.verification.loading) return
            if (this.inputCheck("account")) return
            // let params = {
            //     type: "verification_number",
            //     mobile: this.form.account,
            // }
            this.verification.loading = true
            this.$axios
                // .post(this.API.checkVerification, params)
                .post("/api/user/isHasRegister", { customers_name: this.form.account })
                .then((res) => {
                    // this.waitVerification()
                    if (res.code == 200 && res.data && res.data.is_has) {
                        this.varify()
                        // this.verification.loading = false;
                    } else {
                        this.verification.loading = false
                        this.err_msg = this.$c("pages.Login.Account_no_exists").replace("XXX", this.localePath({ name: "register" }))
                        setTimeout(() => {
                            this.err_msg = ""
                        }, 3000)
                    }
                })
                .catch((err) => {
                    this.verification.loading = false
                    if (err.code === 400) {
                        this.err_msg = err.message
                        setTimeout(() => {
                            this.err_msg = ""
                        }, 3000)
                    }
                })
            // }
        },
        waitVerification() {
            // let time = this.verification.time;
            let _ts = this
            if (_ts.verification.time == 0) {
                _ts.verification.loading = false
                clearTimeout(_ts.verification.timeOut)
                _ts.verification.txt = _ts.$c("form.validate.verification.get")
                _ts.verification.time = 60
            } else {
                _ts.verification.time = _ts.verification.time - 1
                _ts.verification.txt = `${_ts.$c("form.validate.verification.again")}${_ts.verification.time}s`

                _ts.verification.loading = true
                _ts.verification.timeOut = setTimeout(() => {
                    _ts.waitVerification()
                }, 1000)
            }
        },
        createPoint() {
            if (window.dataLayer) {
                window.dataLayer.push({
                    event: "uaEvent",
                    eventCategory: "Login Page",
                    eventAction: "create_account",
                    eventLabel: "Create an account",
                    nonInteraction: false,
                })
            }
            this.$router.push(this.localePath({ path: "/register.html" }))
        },
        async submit() {
            let loginApi, params
            if (this.login_type == "account") {
                loginApi = this.API.login
                params = {
                    // login_type: 1,
                    customers_email_address: this.form.account,
                    customers_password: AES.encrypt(this.form.pwd, "_-yu_xuan_3507-_", "fs_com_phone2016"),
                    keep_signed: this.chk ? 1 : 0,
                }
                let arr = [],
                    attr = ["account", "pwd"]
                attr.map((item) => {
                    let f = this.inputCheck(item)
                    arr.push(f)
                })
                if (arr.includes(true)) {
                    return
                }
            } else {
                loginApi = this.API.loginByCode
                params = {
                    login_type: 2,
                    verification_key: this.verification.key,
                    verification_code: this.form.code,
                    customers_name: this.form.account,
                    keep_signed: this.chk ? 1 : 0,
                }
                let arr_01 = [],
                    attr_01 = ["account", "code"]
                attr_01.map((item) => {
                    let f_01 = this.inputCheck(item)
                    arr_01.push(f_01)
                })
                if (arr_01.includes(true)) {
                    return
                }
            }
            // return;

            if (this.submit_loading) {
                return
            }
            // 无线选择器处理
            const { redirect, cartId: queryCartId } = this.$route.query
            if (redirect) {
                let query_query = parseQueryString(this.$route.query.redirect)
                if (query_query.wireless_product) {
                    params.cartId = query_query.wireless_product
                }
            }

            // 无线光模块方案选择工具
            if (queryCartId) {
                params.opticalModuleCartId = queryCartId
            }
            let requestHeader = {}
            // if (this.isntLogin) {
            //     const { recaptchaTp, headers = {}, errorMsg = "grecaptcha_error" } = await getRecaptchaToken()
            //     if (!recaptchaTp) {
            //         this.$message.error(this.$c(`pages.Login.regist.${errorMsg}`))
            //         return
            //     }
            //     if (headers) {
            //         requestHeader = headers
            //     }
            // }
            this.submit_loading = true
            this.$axios
                .post(loginApi, params, { headers: { ...requestHeader } })
                .then((res) => {
                    if (res.code === 200 && res.status === "sensiWords" && this.website === "cn") {
                        for (let key in res.errors) {
                            this.errors[key] = this.$c("form.form.errors.sensiWords")
                        }
                        this.submit_loading = false
                        return
                    }
                    this.submit_loading = true
                    this.$cookies.remove("login")
                    clearTimeout(this.verification.timeOut)
                    if (res.data.access_token) {
                        console.log("loginloginloginloginloginloginlogin")
                        console.log(res.data.access_token)
                        console.log("loginloginloginloginloginloginlogin")
                        this.$cookies.set("token_new", res.data.access_token)
                        console.log(this.$cookies.get("token_new"))
                        console.log("loginloginloginloginloginloginlogin")
                    }
                    this.getUserInfo(() => {
                        if (window.dataLayer) {
                            window.dataLayer.push({
                                event: "uaEvent",
                                eventCategory: "Login Page",
                                eventAction: "login",
                                eventLabel: "undefined",
                                nonInteraction: false,
                                loginStatus: `Login_${this.gaLoginString}`,
                                userId: `${this.userInfo ? AES.encrypt(`${this.userInfo.customers_level}${this.userInfo.customers_id}`, `_-yu_xuan_3507-_`, `fs_com_phone2016`) : ""}`,
                            })
                        }
                    })
                    this.getCart()
                    if (this.$route.query.redirect) {
                        if (Array.isArray(this.$route.query.redirect)) {
                            location.href = this.$route.query.redirect[this.$route.query.redirect.length - 1]
                        } else {
                            try {
                                let pica8_p_id = window.sessionStorage.getItem("pica8_p_id")
                                if (pica8_p_id && this.$route.query.redirect.includes("pica8-resources")) {
                                    this.$axios
                                        .post("/api/sendPicOSVSoftwareEmail")
                                        .then((res) => {})
                                        .catch((err) => {})
                                }
                                window.sessionStorage.removeItem("pica8_p_id")
                            } catch (e) {}
                            location.href = this.$route.query.redirect
                        }
                    } else {
                        location.href = this.localePath({ path: "/" })
                    }

                    this.initGrecaptcha()
                })
                .catch((err) => {
                    this.initGrecaptcha()
                    this.submit_loading = false
                    if (window.dataLayer) {
                        window.dataLayer.push({
                            event: "uaEvent",
                            eventCategory: "Login Page",
                            eventAction: "login_fail",
                            eventLabel: "undefined",
                            nonInteraction: false,
                        })
                    }
                    if (err.code === 400) {
                        this.err_msg = err.message
                        setTimeout(() => {
                            this.err_msg = ""
                        }, 3000)
                    } else if (err.code === 422) {
                        this.$message.error(err.message)
                        // this.errors.email = err.errors.customers_email_address;
                        // this.errors.pwd = err.errors.customers_password
                    }

                    if (err.code === 403) {
                        this.$message.error(err.message)
                    }
                    if (err.code === 500) {
                        this.$message.error(err.message)
                    }
                    if (isNeedGrecaptcha(window.location.hostname)) {
                        this.isntLogin = true
                        if (this.timer) {
                            clearInterval(this.timer)
                        }
                    }
                })
                .finally(() => {
                    this.$cookies.remove("dismissInvitationsFromCompany")
                    this.$cookies.remove("dismissInvitationTip")
                    this.$cookies.remove("dismissRequestToJoinTeamsTip")
                })
        },
        varify() {
            let _this = this
            let appid = "2088583036" // 腾讯云控制台中对应这个项目的 appid
            //生成一个滑块验证码对象
            let captcha = new TencentCaptcha(appid, function (res) {
                // 用户滑动结束或者关闭弹窗，腾讯返回的内容

                if (res.ret === 0) {
                    //成功，传递数据给后台进行验证
                    _this.verification.ticket = res.ticket
                    _this.verification.randstr = res.randstr
                    _this.$axios
                        .post(_this.API.verificationCodes, {
                            mobile: _this.form.account,
                            ticket: _this.verification.ticket,
                            randstr: _this.verification.randstr,
                        })
                        .then((r) => {
                            if (r.code == 200) {
                                // _this.verification.loading = false;
                                _this.verification.key = r.data.key
                                _this.waitVerification()
                            }
                        })
                        .catch((err) => {
                            _this.verification.loading = false
                            if (err.code === 400) {
                                _this.err_msg = err.message
                                setTimeout(() => {
                                    _this.err_msg = ""
                                }, 3000)
                            }
                        })
                } else {
                    _this.verification.loading = false
                }
            })
            // 滑块显示
            captcha.show()
        },
        getAlipayLoginInfo() {
            let url = "/api/login/alipayLogin"
            this.$axios
                .get(url)
                .then((res) => {
                    if (res && res.code == 200) {
                        this.alipay_data = res.data
                    }
                })
                .catch((e) => {})
        },
        // 初始化谷歌验证
        initGrecaptcha() {
            this.recaptchaTp = false
            this.recaptchaVal = ""
        },
    },
}
</script>

<style lang="scss" scoped>
.cn_login_form {
    ::v-deep {
        .validate-message {
            a {
                color: #c00000;
                text-decoration: underline;
            }
        }
    }
    .cn_login_type {
        padding-top: 16px;
        display: flex;
        margin-bottom: 24px;
        justify-content: center;
        column-gap: 48px;
        li {
            @include font16;
            color: $textColor1;
            position: relative;
            padding-bottom: 8px;
            cursor: pointer;
            // &:first-child {
            //     margin-right: 32px;
            // }
            &.active {
                font-weight: 600;
                &::after {
                    content: "";
                    position: absolute;
                    width: 100%;
                    height: 3px;
                    background-color: #c00000;
                    bottom: -3px;
                    border-radius: 3px;
                    left: 0;
                }
            }
        }
    }
    .form {
        margin-top: 16px;
    }
    .form_item {
        margin-bottom: 16px;
        .new_err_tips {
            margin-top: 4px;
            color: $textColor4;
            @include font12;
            display: flex;
            align-items: center;
            .icon {
                margin-right: 4px;
            }
        }
        .inp_box {
            position: relative;
        }
        .eye {
            font-size: 16px;
            width: 28px;
            height: 28px;
            padding: 6px;
            color: $textColor3;
            cursor: pointer;
            &:hover {
                color: #19191a;
                border-radius: 4px;
                background: #ffffff;
            }
        }
        /* .error_input {
            @include errorInput;
        } */
        .fs-popover.hoverTip {
            position: absolute;
            top: 7px;
            right: 7px;
            @include mediaM {
                right: 0;
                top: 1px;
                width: 40px;
                height: 40px;
                padding: 13px 12px;
                display: flex;
                align-items: center;
                justify-content: center;
                .eye:hover {
                    background-color: transparent;
                    border-radius: 0;
                }
            }
        }
        .err_input {
            // border-color: $textColor4;
        }
        &.form_item_verification {
            margin-bottom: 0;
        }
        .label {
            color: $textColor3;
            @include font14;
            margin-bottom: 4px;
            a {
                color: $textColor3;
                @include font13;
            }
        }
        .input_box_verification {
            position: relative;
            > span {
                position: absolute;
                color: #0060bf;
                @include font14;
                padding: 9px 16px;
                background-color: #f6f6f6;
                right: 1px;
                border-top-right-radius: 2px;
                border-bottom-right-radius: 2px;
                top: 1px;
                cursor: pointer;
                &:hover {
                    text-decoration: underline;
                }
                &.disabled {
                    cursor: default;
                    pointer-events: none;
                    color: #ccc;
                }
            }
        }

        &.form_item_pwd {
            margin-bottom: 0;
        }
        .inp_box {
            input {
                // &:-webkit-autofill,
                // &:-webkit-autofill:active,
                // &:-webkit-autofill:focus,
                // &:-webkit-autofill:hover {
                //     -webkit-box-shadow: 0 0 0px 1000px #f6f6f8 inset !important;
                // }
                &:-webkit-autofill,
                &:-webkit-autofill:active {
                    -webkit-box-shadow: 0 0 0px 1000px #f6f6f8 inset !important;
                }
                &:-webkit-autofill,
                &:-webkit-autofill:active {
                    background: linear-gradient(0deg, rgba(25, 25, 26, 0.04), rgba(25, 25, 26, 0.04)), #f6f6f8;
                }
                &:-internal-autofill-selected {
                    border: 1px solid #fff !important;
                }
                border: 1px solid transparent !important;
                &:focus {
                    border: 1px solid #707070 !important;
                }
                &.err_input {
                    // border: 1px solid #c00000;
                }
                &:hover {
                    background: linear-gradient(0deg, rgba(25, 25, 26, 0.04), rgba(25, 25, 26, 0.04)), #f6f6f8;
                }
            }
        }
    }
    .sign_in_btn {
        margin: 6px 0 0 0;
        font-weight: 600;
    }
    .create_btn {
        margin-top: 16px;
        color: #0060bf;
    }
    .remember_box {
        margin-top: 16px;
        display: flex;
        justify-content: space-between;
        > div {
            display: flex;
            align-items: center;
            .chk_box {
                display: flex;
                align-items: center;
            }
        }
        a {
            // color: $textColor3;
            @include font13;
        }

        label {
            font-size: 13px;
            line-height: 22px;
        }
        input[type="checkbox"] {
            width: 16px;
            height: 16px;
        }
    }
    .third_login_box {
        margin-top: 16px;
        // margin-bottom: 24px;
        display: flex;
        justify-content: center;
        // align-items: center;
        flex-direction: column;
        .top {
            display: flex;
            flex-direction: column;
            .third_title {
                margin-bottom: 12px;
            }
            .third_login {
                display: flex;
                justify-content: center;
                column-gap: 24px;
            }
        }
        .agreement {
            margin-top: 12px;
            color: $textColor3;
            @include font13;
            ::v-deep a {
                color: $textColor1;
                text-decoration: underline;
            }
        }
        .third_login {
            margin-left: 12px;
            a {
                cursor: pointer;
                width: 24px;
                height: 24px;
                margin: 0 12px 0 0;
                &.icon_weixin {
                    @include bgcover("https://resource.fs.com/mall/generalImg/20230825115909ynmu1z.svg");
                    &:hover {
                        @include bgcover("https://resource.fs.com/mall/generalImg/202308251159094wt1q4.svg");
                    }
                }
                &.icon_alipay {
                    @include bgcover("https://resource.fs.com/mall/generalImg/20230825115910167fa9.svg");
                    &:hover {
                        @include bgcover("https://resource.fs.com/mall/generalImg/202308251159102cnlxr.svg");
                    }
                }
                &:last-child {
                    margin: 0;
                }
            }
        }
        .none {
            display: none;
        }
    }
    .reg_bottom {
        // @include font14;
        // color: $textColor1;
        // font-weight: 600;
        // text-align: center;
        // display: block;
        margin-top: 16px;
        // margin-bottom: 12px;
        .reg_bottom_title {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 16px;
            .text {
                color: $textColor3;
                @include font14;
                padding: 0 12px;
            }
            .line {
                flex: 1 1 auto;
                height: 1px;
                background: $bgColor2;
            }
        }
    }
}
@media (max-width: 1024px) {
    .cn_login_form .cn_login_type {
        margin-bottom: 39px;
        padding-top: 36px;
    }
}
@media (max-width: 480px) {
    .cn_login_form {
        .fs-button {
            // margin-bottom: 16px;
        }
    }
}
</style>
