<template>
    <div class="default_login_form">
        <div class="cn_login_form">
            <!-- <h2 class="welcome">{{$c('pages.Login.Welcome')}}</h2> -->
            <div class="reg">
                {{ $c("pages.Login.Sign_in_to_FS_or") }}
                <nuxt-link :to="localePath({ path: '/register.html' })">{{ $c("pages.Login.Create_an_account") }}</nuxt-link>
            </div>
            <ul class="cn_login_type">
                <li v-for="(t, i) in login_type_list" :key="i" @click.stop="selectBindType(t)" :class="{ active: login_type == t.type }">{{ t.name }}</li>
            </ul>
            <validate-message :message="err_msg"></validate-message>

            <form @submit.prevent="submit" class="form" v-if="login_type == 'has'">
                <div class="form_item">
                    <div class="label">{{ $c("form.form.phone_number") }}/{{ $c("form.form.email_ddress") }}</div>
                    <div class="inp_box">
                        <input type="text" :class="{ err_input: errors.account }" @focus="titlePoint('Email Address')" v-model.trim="form.account" @blur.stop="inputCheck('account')" />
                        <p class="new_err_tips" v-show="errors.account">
                            <i class="icon iconfont">&#xe66a;</i>
                            <span>{{ errors.account }}</span>
                        </p>
                    </div>
                </div>
                <div class="form_item form_item_verification">
                    <div class="label">{{ $c("form.validate.verification.name") }}</div>
                    <div class="inp_box input_box_verification">
                        <span @click.stop="getVerification" :class="{ disabled: verification.loading }">{{ verification.txt }}</span>
                        <input type="text" :class="{ err_input: errors.code }" @focus="titlePoint('Email Address')" v-model.trim="form.code" @blur.stop="inputCheck('code')" />
                        <p class="new_err_tips" v-show="errors.code">
                            <i class="icon iconfont">&#xe66a;</i>
                            <span>{{ errors.code }}</span>
                        </p>
                    </div>
                </div>
                <div class="form_item agreement_bd">
                    <div class="agreement_box">
                        <input type="checkbox" class="chk" v-model="form.thirdChecked" @change.stop="inputCheck('thirdChecked')" nls_fa_el_name="thirdChecked" />
                        <div class="agreement">
                            <p
                                class="info1"
                                v-html="
                                    $c('pages.Login.I_agree_to_FSs_Login_bind')
                                        .replace('XXX1', localePath({ name: 'privacy-policy' }))
                                        .replace('XXX2', localePath({ name: 'terms-of-use' }))
                                "></p>
                            <p
                                class="info2"
                                v-html="
                                    $c('pages.Login.By_clicking_the_button_below')
                                        .replace('XXX1', localePath({ name: 'privacy-policy' }))
                                        .replace('XXX2', localePath({ name: 'terms-of-use' }))
                                "></p>
                        </div>
                    </div>
                    <p class="new_err_tips" v-show="errors.thirdChecked">
                        <i class="icon iconfont">&#xe66a;</i>
                        <span>{{ errors.thirdChecked }}</span>
                    </p>
                </div>
                <fs-button class="sign_in_btn" type="red" :loading="submit_loading" htmlType="submit">
                    {{ $c("pages.Login.Bind_Submit") }}
                </fs-button>
            </form>
            <form @submit.prevent="submit" class="form" v-if="login_type == 'hasnt'">
                <div class="user_name_box">
                    <div class="form_item">
                        <div class="label">{{ $c("form.form.last_name01") }}</div>
                        <div class="inp_box">
                            <input type="text" :class="{ error_input: errors.last_name }" @focus.stop="titlePoint('Last Name')" v-model="form.last_name" @blur.stop="inputCheck('last_name')" nls_fa_el_name="last_name" />
                            <p class="new_err_tips" v-show="errors.last_name">
                                <i class="icon iconfont">&#xe66a;</i>
                                <span>{{ errors.last_name }}</span>
                            </p>
                        </div>
                    </div>
                    <div class="form_item">
                        <div class="label">{{ $c("form.form.first_name01") }}</div>
                        <div class="inp_box">
                            <input
                                type="text"
                                :class="{ error_input: errors.first_name }"
                                @focus.stop="titlePoint('First Name')"
                                v-model="form.first_name"
                                @blur.stop="inputCheck('first_name')"
                                nls_fa_el_name="first_name" />
                            <p class="new_err_tips" v-show="errors.first_name">
                                <i class="icon iconfont">&#xe66a;</i>
                                <span>{{ errors.first_name }}</span>
                            </p>
                        </div>
                    </div>
                </div>
                <div class="form_item">
                    <div class="label">{{ $c("form.form.phone_number") }}/{{ $c("form.form.email_ddress") }}</div>
                    <div class="inp_box">
                        <input type="text" :class="{ err_input: errors.account }" @focus="titlePoint('Email Address')" v-model.trim="form.account" @blur.stop="inputCheck('account')" />
                        <p class="new_err_tips" v-show="errors.account">
                            <i class="icon iconfont">&#xe66a;</i>
                            <span>{{ errors.account }}</span>
                        </p>
                    </div>
                </div>
                <div class="form_item form_item_verification">
                    <div class="label">{{ $c("form.validate.verification.name") }}</div>
                    <div class="inp_box input_box_verification">
                        <span @click.stop="getVerification" :class="{ disabled: verification.loading }">{{ verification.txt }}</span>
                        <input type="text" :class="{ err_input: errors.code }" @focus="titlePoint('Email Address')" v-model.trim="form.code" @blur.stop="inputCheck('code')" />
                        <p class="new_err_tips" v-show="errors.code">
                            <i class="icon iconfont">&#xe66a;</i>
                            <span>{{ errors.code }}</span>
                        </p>
                    </div>
                </div>
                <div class="form_item">
                    <div class="label">{{ $c("form.form.password") }}</div>
                    <div class="inp_box">
                        <PwdVerify ref="pwdVerify" v-model="form.password" @focus="titlePoint('Password')"></PwdVerify>
                        <p class="new_err_tips" v-show="errors.password">
                            <i class="icon iconfont">&#xe66a;</i>
                            <span>{{ errors.password }}</span>
                        </p>
                    </div>
                </div>
                <div class="form_item">
                    <div class="label">
                        {{ $c("form.form.company_name") }}
                        {{ OptionalType ? `(${$c("form.form.Optional")})` : "" }}
                    </div>
                    <div class="inp_box">
                        <input
                            type="text"
                            :class="{ error_input: errors.customers_company }"
                            @focus.stop="titlePoint('Company Name')"
                            v-model="form.customers_company"
                            @blur.stop="inputCheck('customers_company')"
                            nls_fa_el_name="company_company" />
                        <p class="new_err_tips" v-show="errors.customers_company">
                            <i class="icon iconfont">&#xe66a;</i>
                            <span>{{ errors.customers_company }}</span>
                        </p>
                    </div>
                </div>
                <div class="form_item agreement_bd">
                    <div class="agreement_box">
                        <input type="checkbox" class="chk" v-model="form.checked" @change.stop="inputCheck('checked')" nls_fa_el_name="checked" />
                        <div class="agreement">
                            <p
                                class="info1"
                                v-html="
                                    $c('pages.Login.I_agree_to_FSs')
                                        .replace('XXX1', localePath({ name: 'privacy-policy' }))
                                        .replace('XXX2', localePath({ name: 'terms-of-use' }))
                                "></p>
                            <p
                                class="info2"
                                v-html="
                                    $c('pages.Login.By_clicking_the_button_below')
                                        .replace('XXX1', localePath({ name: 'privacy-policy' }))
                                        .replace('XXX2', localePath({ name: 'terms-of-use' }))
                                "></p>
                        </div>
                    </div>
                    <p class="new_err_tips" v-show="errors.checked">
                        <i class="icon iconfont">&#xe66a;</i>
                        <span>{{ errors.checked }}</span>
                    </p>
                </div>
                <fs-button class="sign_in_btn" type="red" :loading="submit_loading" htmlType="submit">
                    {{ $c("pages.Login.Bind_Submit") }}
                </fs-button>
            </form>
        </div>
    </div>
</template>

<script>
import { mapState, mapGetters, mapActions } from "vuex"
import ValidateError from "@/components/ValidateError/ValidateError"
import FsButton from "@/components/FsButton/FsButton"
import FsPopover from "@/components/FsPopover"
import ValidateMessage from "@/components/ValidateMessage/ValidateMessage"
import { setCookieOptions } from "@/util/util"
import GRecaptcha from "@/components/GRecaptcha/GRecaptcha"
import { cn_mobile_tel, cn_email, company_name_validate } from "@/constants/validate"
import AES from "@/util/AES.js"
import PasswordOver from "./components/PasswordOver"
import PwdVerify from "./components/PwdVerify.vue"
import { isNeedGrecaptcha } from "../../util/grecaptchaHost"

export default {
    layout: "account",
    name: "cn_bind_account",
    components: {
        FsButton,
        FsPopover,
        GRecaptcha,
        ValidateError,
        ValidateMessage,
        PasswordOver,
        PwdVerify,
    },
    head() {
        let s = ["cn", "hk", "tw", "mo"].includes(this.website) ? { src: "https://turing.captcha.qcloud.com/TCaptcha.js" } : {}
        return {
            script: [s],
        }
    },
    mounted() {
        if (!(this.$route.query && this.$route.query.code)) {
            this.err_msg = "Error:Code is not defind!"
            setTimeout(() => {
                this.$router.replace(this.localePath({ name: "login" }))
            }, 2000)
            return
        }
    },
    data() {
        return {
            err_msg: "",
            login_type_list: [
                { name: `已有账号`, type: `has` },
                { name: `还没有账号`, type: `hasnt` },
            ],
            login_type: `has`,
            // chk: false,
            submit_loading: false,
            form: {
                first_name: "",
                last_name: "",
                account: "",
                password: "",
                code: "",
                customers_company: "",
                checked: true,
                thirdChecked: false,
            },
            errors: {
                first_name: "",
                last_name: "",
                account: "",
                password: "",
                code: "",
                customers_company: "",
                checked: "",
                thirdChecked: "",
            },
            eye: false,
            icon: [
                {
                    type: `weixin`,
                    url: `https://cn.fs.com/weixin/wechatlogin.php`,
                    title: "微信",
                },
                {
                    type: `alipay`,
                    url: `https://cn.fs.com/weixin/wechatlogin.php`,
                    title: "支付宝",
                },
            ],
            isntLogin: false,
            verification: {
                time: 60,
                txt: this.$c("form.validate.verification.get"),
                loading: false,
                timeOut: null,
                ticket: "",
                randstr: "",
                key: "",
            },
            API: {
                wechatBind: `/api/login/wechatBind`, //登录
                alipayBind: `/api/login/alipayBind`, //登录
                registerCn: `/api/user/registerCn`, // 注册接口
                loginByCode: `/api/user/loginByCode`, //验证码登录
                verificationCodes: `/api/user/verificationCodes`, //发送验证码
                checkVerification: `/api/user/checkVerification`, //验证是否需要滑动验证
            },
            OptionalType: true,
        }
    },
    computed: {
        ...mapState({
            isMobile: (state) => state.device.isMobile,
            isLogin: (state) => state.userInfo.isLogin,
            userInfo: (state) => state.userInfo.userInfo,
            website: (state) => state.webSiteInfo.website,
        }),
        ...mapGetters({
            gaLoginString: "userInfo/gaLoginString",
        }),
    },
    methods: {
        ...mapActions({
            getUserInfo: "userInfo/getUserInfo",
            getCart: "cart/getCart",
        }),
        selectBindType(item) {
            if (item.type == this.login_type) return
            this.login_type = item.type
            for (let item in this.errors) {
                this.errors[item] = ""
            }
        },
        inputCheck(attr) {
            let flag = false
            if (attr === "first_name") {
                if (!this.form[attr].replace(/\s+/g, "")) {
                    this.errors[attr] = this.$c("form.validate.first_name.first_name_required")
                    flag = true
                } else {
                    if (this.form[attr].replace(/\s+/g, "").length > 40) {
                        this.errors[attr] = this.$c("form.validate.first_name.first_name_max")
                        flag = true
                    } else if (this.form[attr].replace(/\s+/g, "").length < 1) {
                        this.errors[attr] = this.$c("form.validate.first_name.first_name_min")
                        flag = true
                    } else {
                        this.errors[attr] = ""
                    }
                }
            } else if (attr === "last_name") {
                if (!this.form[attr].replace(/\s+/g, "")) {
                    this.errors[attr] = this.$c("form.validate.last_name.last_name_required")
                    flag = true
                } else {
                    if (this.form[attr].replace(/\s+/g, "").length > 40) {
                        this.errors[attr] = this.$c("form.validate.last_name.last_name_max")
                        flag = true
                    } else if (this.form[attr].replace(/\s+/g, "").length < 1) {
                        this.errors[attr] = this.$c("form.validate.last_name.last_name_min")
                        flag = true
                    } else {
                        this.errors[attr] = ""
                    }
                }
            } else if (attr === "account") {
                //账户
                if (!this.form[attr].replace(/\s+/g, "")) {
                    this.errors[attr] = this.$c("form.validate.phone_email.required")
                    flag = true
                } else {
                    if (!(cn_mobile_tel.test(this.form[attr]) || cn_email.test(this.form[attr]))) {
                        this.errors[attr] = this.$c("form.validate.phone_email.validate")
                        flag = true
                    } else {
                        this.errors[attr] = ""
                    }
                }
            } else if (attr === "password") {
                //密码
                if (!this.form[attr].replace(/\s+/g, "")) {
                    this.errors[attr] = this.$c("form.validate.password.password_required")
                    flag = true
                } else {
                    this.errors[attr] = ""
                }
            } else if (attr === "code") {
                //验证码
                if (!String(this.form[attr]).replace(/\s+/g, "")) {
                    this.errors[attr] = "请输入验证码"
                    flag = true
                } else {
                    this.errors[attr] = ""
                }
            } else if (attr === "customers_company") {
                //公司名
                this.OptionalType = true
                if (!this.form[attr].replace(/\s+/g, "")) {
                    this.errors[attr] = ""
                } else {
                    if (!company_name_validate.test(this.form[attr].replace(/\s+/g, ""))) {
                        this.errors[attr] = this.$c("form.validate.company_name.company_name_validate")
                        flag = true
                    } else {
                        this.errors[attr] = ""
                    }
                }
            } else if (attr === "checked") {
                // if (!this.isMobile) {
                if (this.form[attr]) {
                    this.errors[attr] = ""
                } else {
                    this.errors[attr] = this.$c("pages.Login.Please_make_sure_you_agree_Privacy_Policy")
                    flag = true
                }
                // }
            } else if (attr === "thirdChecked") {
                // if (!this.isMobile) {
                if (this.form[attr]) {
                    this.errors[attr] = ""
                } else {
                    this.errors[attr] = this.$c("pages.Login.Please_make_sure_you_agree_Privacy_Policy")
                    flag = true
                }
                // }
            }
            // thirdChecked
            return flag
        },
        toogleEye() {
            this.eye = !this.eye
        },

        getVerification() {
            if (this.verification.loading) return
            if (this.inputCheck("account")) return
            this.verification.loading = true
            if (this.login_type == "hasnt") {
                let params = {
                    type: "verification_number",
                    mobile: this.form.account,
                }
                this.verification.loading = true
                this.$axios
                    .post(this.API.checkVerification, params)
                    .then((res) => {
                        if (res.code == 200 && res.data && res.data.status && res.data.status == 1) {
                            this.varify()
                        } else {
                            this.verification.loading = false
                        }
                    })
                    .catch((err) => {
                        this.verification.loading = false
                        if (err.code === 400) {
                            this.err_msg = err.message
                            setTimeout(() => {
                                this.err_msg = ""
                            }, 3000)
                        }
                    })
            } else {
                this.$axios
                    .post("/api/user/isHasRegister", { customers_name: this.form.account })
                    .then((res) => {
                        if (res.code == 200 && res.data && res.data.is_has) {
                            this.varify()
                            // this.verification.loading = false;
                        } else {
                            this.verification.loading = false
                            this.err_msg = this.$c("pages.Login.Account_no_exists").replace("XXX", this.localePath({ name: "register" }))
                            setTimeout(() => {
                                this.err_msg = ""
                            }, 3000)
                        }
                    })
                    .catch((err) => {
                        this.verification.loading = false
                        if (err.code === 400) {
                            this.err_msg = err.message
                            setTimeout(() => {
                                this.err_msg = ""
                            }, 3000)
                        }
                    })
                // }
            }
        },
        waitVerification() {
            // let time = this.verification.time;
            let _ts = this
            if (_ts.verification.time == 0) {
                _ts.verification.loading = false
                clearTimeout(_ts.verification.timeOut)
                _ts.verification.txt = _ts.$c("form.validate.verification.get")
                _ts.verification.time = 60
            } else {
                _ts.verification.time = _ts.verification.time - 1
                _ts.verification.txt = `${_ts.$c("form.validate.verification.again")}${_ts.verification.time}s`

                _ts.verification.loading = true
                _ts.verification.timeOut = setTimeout(() => {
                    _ts.waitVerification()
                }, 1000)
            }
        },
        submit() {
            let arr = [],
                attr = []
            if (this.login_type == "hasnt") {
                attr = ["first_name", "last_name", "account", "code", "password", "customers_company", "checked"]
                attr.map((item) => {
                    let f = this.inputCheck(item)
                    arr.push(f)
                })
                if (arr.includes(true) || this.is_regist || this.submit_loading) {
                    if (window.dataLayer) {
                        window.dataLayer.push({
                            event: "uaEvent",
                            eventCategory: "Regist Page",
                            eventAction: "sign_up_fail",
                            eventLabel: "undefined",
                            nonInteraction: false,
                        })
                    }
                    return
                }

                this.submit_loading = true

                let formData = new FormData()

                formData.append("register_type", 1)
                formData.append("type_code", this.$route.query.code)
                formData.append("type", this.$route.query.type)
                formData.append("first_name", this.form.first_name)
                formData.append("last_name", this.form.last_name)
                formData.append("customers_name", this.form.account)
                formData.append("verification_key", this.verification.key)
                formData.append("verification_code", this.form.code)
                formData.append("password", AES.encrypt(this.form.password, "_-yu_xuan_3507-_", "fs_com_phone2016"))
                // formData.append("type", 'verification_number');

                this.$axios
                    .post(this.API.registerCn, formData)
                    .then((res) => {
                        this.submit_loading = true
                        if (res.code !== 200) {
                            this.err_msg = res.message
                            setTimeout(() => {
                                this.err_msg = ""
                            }, 2000)
                            return
                        }
                        if (res.data.access_token) {
                            this.$cookies.set("token_new", res.data.access_token)
                        }
                        clearTimeout(this.verification.timeOut)
                        //this.getUserInfo();
                        this.getCart()
                        if (window.dataLayer) {
                            window.dataLayer.push({
                                event: "uaEvent",
                                eventCategory: "Regist Page",
                                eventAction: "sign_up",
                                eventLabel: "undefined",
                                nonInteraction: false,
                            })
                        }
                        if (window.yaCounter71412688) {
                            yaCounter71412688.reachGoal("registrations", function () {})
                        }
                        if (window.VWO) {
                            window.VWO = window.VWO || []
                            let formInstance = document.querySelector(".regist_box .form")
                            window.VWO.push(["nls.formAnalysis.markSuccess", formInstance, 1])
                        }

                        //跳转到绑定成功页面，type为1
                        this.$router.replace(
                            this.localePath({
                                name: "reg-success",
                                query: {
                                    type: 1,
                                },
                            })
                        )

                        // if (this.$route.query.redirect) {
                        //     if (Array.isArray(this.$route.query.redirect)) {
                        //         this.$router.replace(this.localePath({ path: this.$route.query.redirect[this.$route.query.redirect.length - 1] }))
                        //     } else {
                        //         this.$router.replace(this.localePath({ path: this.$route.query.redirect }))
                        //     }
                        // } else {
                        //     this.$router.replace(this.localePath({ name: "home" }))
                        // }
                        // this.$router.replace(this.localePath({ name: "state-feedback", query: { type: 1, regEmail: AES.encrypt(this.form.email, "_-yu_xuan_3507-_", "fs_com_phone2016") } }))

                        if (this.$refs.grecaptcha) {
                            this.$refs.grecaptcha.grecaptchaError = ""
                        }
                    })
                    .catch((err) => {
                        this.submit_loading = false
                        if (window.dataLayer) {
                            window.dataLayer.push({
                                event: "uaEvent",
                                eventCategory: "Regist Page",
                                eventAction: "sign_up_fail",
                                eventLabel: "undefined",
                                nonInteraction: false,
                            })
                        }
                        if (window.VWO) {
                            window.VWO = window.VWO || []
                            let formInstance = document.querySelector(".regist_box .form")
                            window.VWO.push(["nls.formAnalysis.markSuccess", formInstance, 0])
                        }
                        if (err.code === 422) {
                            if (err.errors) {
                                for (let attr in err.errors) {
                                    console.log(attr)
                                    if (attr == "verification_key") {
                                        this.errors.code = err.errors[attr]
                                    } else {
                                        this.errors[attr] = err.errors[attr]
                                    }
                                }
                            }
                        }

                        if (isNeedGrecaptcha(window.location.hostname)) {
                            if (err.code === 409 && this.$refs.grecaptcha) {
                                this.$refs.grecaptcha.grecaptchaError = this.$c("pages.Login.regist.grecaptcha_error")
                            }
                        }
                        if (err.code === 403) {
                            this.$message.error(err.message)
                        }
                        if (err.code === 400) {
                            this.$message.error(err.message)
                        }
                    })
            } else {
                attr = ["account", "code", "thirdChecked"]
                attr.map((item) => {
                    let f = this.inputCheck(item)
                    arr.push(f)
                })
                if (arr.includes(true)) {
                    return
                }

                // return;

                if (this.submit_loading) {
                    return
                }

                let params, url
                params = {
                    verification_key: this.verification.key,
                    verification_code: this.form.code,
                    phone: this.form.account,
                    code: this.$route.query.code,
                }
                if (this.$route.query.type == "wechat") {
                    url = this.API.wechatBind
                } else if (this.$route.query.type == "alipay") {
                    url = this.API.alipayBind
                }
                this.submit_loading = true
                this.$axios
                    .post(url, params)
                    .then((res) => {
                        this.submit_loading = true
                        if (res.code !== 200) {
                            this.err_msg = res.message
                            setTimeout(() => {
                                this.err_msg = ""
                            }, 2000)
                            return
                        }
                        this.submit_loading = true
                        this.$cookies.remove("login")
                        clearTimeout(this.verification.timeOut)
                        if (res.data.access_token) {
                            this.$cookies.set("token_new", res.data.access_token)
                        }
                        this.getUserInfo(() => {
                            if (window.dataLayer) {
                                window.dataLayer.push({
                                    event: "uaEvent",
                                    eventCategory: "Login Page",
                                    eventAction: "login",
                                    eventLabel: "undefined",
                                    nonInteraction: false,
                                    loginStatus: `Login_${this.gaLoginString}`,
                                    userId: `${this.userInfo ? AES.encrypt(`${this.userInfo.customers_level}${this.userInfo.customers_id}`, `_-yu_xuan_3507-_`, `fs_com_phone2016`) : ""}`,
                                })
                            }
                        })
                        this.getCart()
                        if (this.$route.query.redirect) {
                            if (Array.isArray(this.$route.query.redirect)) {
                                this.$router.replace(this.localePath({ path: this.$route.query.redirect[this.$route.query.redirect.length - 1] }))
                            } else {
                                this.$router.replace(this.localePath({ path: this.$route.query.redirect }))
                            }
                        } else {
                            this.$router.replace(this.localePath({ name: "home" }))
                        }

                        if (this.$refs.grecaptcha) {
                            this.$refs.grecaptcha.grecaptchaError = ""
                        }
                    })
                    .catch((err) => {
                        this.submit_loading = false
                        if (window.dataLayer) {
                            window.dataLayer.push({
                                event: "uaEvent",
                                eventCategory: "Login Page",
                                eventAction: "login_fail",
                                eventLabel: "undefined",
                                nonInteraction: false,
                            })
                        }
                        if (err.code === 400) {
                            this.err_msg = err.message
                            setTimeout(() => {
                                this.err_msg = ""
                            }, 3000)
                        } else if (err.code === 422) {
                            this.$message.error(err.message)
                            // this.errors.email = err.errors.customers_email_address;
                            // this.errors.password = err.errors.customers_password
                        }

                        if (err.code === 403) {
                            this.$message.error(err.message)
                        }
                        if (err.code === 500) {
                            this.$message.error(err.message)
                        }
                        if (isNeedGrecaptcha(window.location.hostname)) {
                            if (err.code === 409 && this.$refs.grecaptcha) {
                                this.$refs.grecaptcha.grecaptchaError = this.$c("pages.Login.regist.grecaptcha_error")
                            }
                            this.isntLogin = true
                            if (this.timer) {
                                clearInterval(this.timer)
                            }
                            this.timer = setInterval(() => {
                                this.isntLogin = false

                                if (this.$refs.grecaptcha) {
                                    this.$refs.grecaptcha.grecaptchaError = ""
                                }
                            }, 10000)
                        }
                    })
            }
        },
        varify() {
            let _this = this
            let appid = "2088583036" // 腾讯云控制台中对应这个项目的 appid
            //生成一个滑块验证码对象
            let captcha = new TencentCaptcha(appid, function (res) {
                // 用户滑动结束或者关闭弹窗，腾讯返回的内容

                if (res.ret === 0) {
                    //成功，传递数据给后台进行验证
                    _this.verification.ticket = res.ticket
                    _this.verification.randstr = res.randstr
                    let params = {
                        mobile: _this.form.account,
                        ticket: _this.verification.ticket,
                        randstr: _this.verification.randstr,
                    }
                    if (_this.login_type == "hasnt") {
                        params.is_register = 1
                    } else {
                        params.is_register = 0
                    }
                    _this.$axios
                        .post(_this.API.verificationCodes, params)
                        .then((r) => {
                            if (r.code == 200) {
                                // _this.verification.loading = false;
                                _this.verification.key = r.data.key
                                _this.waitVerification()
                            }
                        })
                        .catch((err) => {
                            _this.verification.loading = false
                            if (err.code === 400) {
                                _this.err_msg = err.message
                                setTimeout(() => {
                                    _this.err_msg = ""
                                }, 3000)
                            }
                        })
                } else {
                    _this.verification.loading = false
                }
            })
            // 滑块显示
            captcha.show()
        },
        titlePoint(title) {
            if (title == "Company Name") {
                this.OptionalType = false
            }
            console.log(title)
            // if (window.dataLayer) {
            //     window.dataLayer.push({
            //         event: "uaEvent",
            //         eventCategory: "Regist Page",
            //         eventAction: "regist_information",
            //         eventLabel: `Regist-${title} Input`,
            //         nonInteraction: false,
            //     })
            // }
        },
    },
}
</script>

<style lang="scss" scoped>
.login_box {
    width: 100%;
    &::v-deep {
        .validate-message {
            width: 100%;
        }
    }
}
.welcome {
    text-align: center;
    margin: 16px 0 20px 0;
    display: none;
    @include font26;
}
.reg {
    @include font13;
    color: $textColor1;
    text-align: center;
    display: none;
    > a {
        color: $textColor8;
    }
}
.form {
    margin-top: 20px;
    .validate-message {
        margin-bottom: 10px;
    }
}
.forgot_box {
    display: flex;
    justify-content: flex-end;
    a {
        color: $textColor3;
        @include font12;
    }
}

.form_item {
    .inp_box {
        position: relative;
        .eye {
            // position: absolute;
            // font-size: 16px;
            color: $textColor3;
            right: 12px;
            width: 16px;
            height: 16px;
            text-align: left;
            top: 12px;
            cursor: pointer;
            user-select: none;
            &.show {
                font-size: 16px;
            }
        }
        .error_input {
            @include errorInput;
        }
    }
}

@media (max-width: 768px) {
    .welcome {
        display: block;
    }
}

.cn_login_form {
    .user_name_box {
        display: flex;
        justify-content: space-between;
        .form_item {
            width: calc(50% - 8px);
        }
    }
    .cn_login_type {
        padding-top: 16px;
        display: flex;
        margin-bottom: 39px;
        li {
            @include font16;
            color: $textColor1;
            position: relative;
            padding-bottom: 8px;
            cursor: pointer;
            &:first-child {
                margin-right: 32px;
            }
            &.active {
                font-weight: 600;
                &::after {
                    content: "";
                    position: absolute;
                    width: 100%;
                    height: 3px;
                    background-color: #c00000;
                    bottom: -3px;
                    border-radius: 3px;
                    left: 0;
                }
            }
        }
    }
    .form {
        margin-top: 16px;
    }
    .form_item {
        margin-bottom: 16px;
        .new_err_tips {
            margin-top: 4px;
            color: $textColor4;
            @include font12;
            display: flex;
            align-items: center;
            .icon {
                margin-right: 4px;
            }
        }
        .eye {
            // position: absolute;
            // font-size: 16px;
            color: $textColor3;
            right: 12px;
            width: 16px;
            height: 16px;
            text-align: left;
            top: 12px;
            cursor: pointer;
            user-select: none;
            &.show {
                font-size: 16px;
            }
            &:hover {
                color: #19191a;
            }
        }
        .err_input {
            @include errorInput;
        }
        .label {
            color: $textColor3;
            @include font12;
            margin-bottom: 4px;
        }
        .input_box_verification {
            position: relative;
            > span {
                position: absolute;
                color: $textColor1;
                @include font14;
                padding: 9px 16px;
                background-color: #f6f6f6;
                right: 1px;
                border-top-right-radius: 2px;
                border-bottom-right-radius: 2px;
                top: 1px;
                cursor: pointer;
                &.disabled {
                    cursor: default;
                    pointer-events: none;
                    color: $textColor3;
                }
            }
        }
        .fs-popover.hoverTip {
            position: absolute;
            top: 12px;
            right: 12px;
        }
        &.form_item_password {
            margin-bottom: 0;
        }
        .agreement_box {
            display: flex;
            align-items: flex-start;
            .chk {
                margin-top: 1px;
                margin-right: 8px;
                width: 18px;
                height: 18px;
                font-size: 18px;
                // @media (max-width: 960px) {
                //     display: none;
                // }
            }
            .agreement {
                @include font13;
                color: $textColor3;
                &::v-deep {
                    a {
                        color: $textColor1;
                        text-decoration: underline;
                    }
                }
                .info1 {
                    // @media (max-width: 960px) {
                    //     display: none;
                    // }
                }
                .info2 {
                    display: none;
                    // @media (max-width: 960px) {
                    //     display: inline-block;
                    // }
                }
            }
            &:hover {
                cursor: pointer;
                input[type="checkbox"] {
                    &:before {
                        color: #707070;
                    }
                }
            }
        }
    }
    .sign_in_btn {
        font-weight: 600;
        height: 48px;
        width: 100%;
        margin: 16px 0 0;
    }
    .remember_box {
        display: flex;
        justify-content: space-between;
        > div {
            display: flex;
            align-items: center;
            .chk_box {
                display: flex;
                align-items: center;
            }
        }
        a {
            color: $textColor3;
            @include font13;
        }

        label {
            font-size: 13px;
            line-height: 22px;
        }
        input[type="checkbox"] {
            width: 16px;
            height: 16px;
        }
    }
}
@media (max-width: 1024px) {
    .cn_login_form .cn_login_type {
        margin-bottom: 39px;
        padding-top: 36px;
    }
}
@media (max-width: 480px) {
    .cn_login_form {
        .fs-button {
            margin-bottom: 16px;
        }
    }
}
</style>
