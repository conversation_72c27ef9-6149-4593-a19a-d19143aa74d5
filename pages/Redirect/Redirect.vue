<template>
    <div class="redirect_box"></div>
</template>

<script>
import { parseQueryString, setCookieOptions } from "@/util/util"
export default {
    name: "Redirect",
    data() {
        return {
            err_msg: "",
        }
    },
    asyncData({ app, $axios, route, query, redirect }) {
        console.log(route)
        if (route.path) {
            if (route.path.includes("/download.html")) {
                redirect(app.localePath({ path: "/products_support.html" }))
            } else if (route.path.includes("/service/help_center.html")) {
                redirect(app.localePath({ path: "/service/fs_support.html" }))
            } else if (route.path.includes("/service/fs_center.html")) {
                redirect(app.localePath({ path: "/service/fs_support.html" }))
            } else if (route.path.includes("/punchoutProcess")) {
                const copyFullpath = decodeURIComponent(route.fullPath)
                if (copyFullpath.includes("&amp;")) {
                    console.log("11_22_33_44_55")
                    console.log(copyFullpath.replaceAll("&amp;", "&"))
                    redirect(copyFullpath.replaceAll("&amp;", "&"))
                } else {
                    let q = query.redirect
                    q = q.replace(/^http:\/\/[^/]+/, "")
                    if (q) {
                        let api = q.substr(0, q.indexOf("?"))
                        let p = parseQueryString(q)
                        console.log("redirect_123")
                        console.log(q)
                        console.log(api)
                        console.log(p)
                        console.log(route.query)
                        if (api) {
                            return $axios
                                .post(api, p)
                                .then((res) => {
                                    app.$cookies.set("token_new", res.data.access_token)
                                    app.$cookies.set("punchout_return_url", res.data.ReturnURL)
                                    app.$cookies.set("punchout_buyer_cookie", res.data.BuyerCookie)
                                    redirect(app.localePath({ name: "shopping-cart" }))
                                })
                                .catch((err) => {
                                    redirect(app.localePath({ name: "home" }))
                                })
                        } else {
                            redirect(app.localePath({ name: "home" }))
                        }
                    }
                }
            }
        }
    },
    mounted() {},
    methods: {},
}
</script>

<style lang="scss" scoped>
.redirect_box {
    min-height: 500px;
    background: $bgColor1;
}
</style>
