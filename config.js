export const api_config = {
    test: {
        proxy: "https://test-shop.whgxwl.com:9527",
        server: "https://test-platapi-gateway.whgxwl.com:9527/collector",
        client: "https://test-platapi-gateway.whgxwl.com:9527/collector",
        gateway_api_server: "https://test-platapi-gateway.whgxwl.com:9527",
        gateway_api_client: "https://test-platapi-gateway.whgxwl.com:9527",
        dist_url: "/_nuxt/",
        cn_fs_api: "https://testcn-platapi-gateway.whgxwl.com:9527/collector",
        cn_gateway_api: "https://testcn-platapi-gateway.whgxwl.com:9527",
    },
    auth: {
        proxy: "https://test-shop-sso.whgxwl.com:9527",
        server: "https://test-platapi-gateway-sap.whgxwl.com:9527/collector",
        client: "https://test-platapi-gateway-sap.whgxwl.com:9527/collector",
        gateway_api_server: "https://test-platapi-gateway-sap.whgxwl.com:9527/",
        gateway_api_client: "https://test-platapi-gateway-sap.whgxwl.com:9527/",
        dist_url: "/_nuxt/",
    },
    sap: {
        proxy: "https://test-platapi-sap-gateway.whgxwl.com",
        server: "https://test-platapi-sap-gateway.whgxwl.com/collector",
        client: "https://test-platapi-sap-gateway.whgxwl.com/collector",
        gateway_api_server: "https://test-platapi-sap-gateway.whgxwl.com/",
        gateway_api_client: "https://test-platapi-sap-gateway.whgxwl.com/",
        dist_url: "/_nuxt/",
        cn_fs_api: "https://testcn-platapi-gateway.whgxwl.com:9527/collector",
        cn_gateway_api: "https://testcn-platapi-gateway.whgxwl.com:9527",
    },
    website: {
        proxy: "https://test-platapi-gateway.whgxwl.com:9527/",
        server: "https://test-platapi-gateway.whgxwl.com:9527/website",
        client: "https://test-platapi-gateway.whgxwl.com:9527/website",
        gateway_api_server: "https://test-platapi-gateway.whgxwl.com:9527",
        gateway_api_client: "https://test-platapi-gateway.whgxwl.com:9527",
        dist_url: "/_nuxt/",
    },
    releasebak: {
        proxy: "https://test-shop-zendesk.whgxwl.com/",
        server: "https://test-shop-zendesk.whgxwl.com/",
        client: "https://test-shop-zendesk.whgxwl.com/",
        gateway_api_server: "https://platform-gateway.whgxwl.com",
        gateway_api_client: "https://platform-gateway.whgxwl.com",
        dist_url: "/_nuxt/",
    },
    release_tw: {
        proxy: "https://test-shop-order-api.whgxwl.com/",
        server: "https://test-shop-order-api.whgxwl.com/",
        client: "https://test-shop-order-api.whgxwl.com/",
        gateway_api_server: "https://platform-gateway.whgxwl.com",
        gateway_api_client: "https://platform-gateway.whgxwl.com",
        dist_url: "/_nuxt/",
    },
    sit: {
        proxy: "https://sit-platapi-gateway.whgxwl.com:9527/",
        server: "https://sit-platapi-gateway.whgxwl.com:9527/collector/",
        client: "https://sit-platapi-gateway.whgxwl.com:9527/collector/",
        gateway_api_server: "https://sit-platapi-gateway.whgxwl.com:9527/",
        gateway_api_client: "https://sit-platapi-gateway.whgxwl.com:9527/",
        dist_url: "/_nuxt/",
    },
    release: {
        proxy: "https://pre-platapi-gateway.whgxwl.com/",
        server: "https://pre-platapi-gateway.whgxwl.com/collector/",
        client: "https://pre-platapi-gateway.whgxwl.com/collector/",
        gateway_api_server: "https://pre-platapi-gateway.whgxwl.com/",
        gateway_api_client: "https://pre-platapi-gateway.whgxwl.com/",
        dist_url: "https://front-resource.fs.com/fs-platform-pre/client/",
    },
    release_hk: {
        proxy: "https://test-fschina.whgxwl.com/",
        server: "https://test-fschina.whgxwl.com/",
        client: "https://test-fschina.whgxwl.com/",
        gateway_api_server: "https://platform-gateway.whgxwl.com",
        gateway_api_client: "https://platform-gateway.whgxwl.com",
        dist_url: "/_nuxt/",
    },
    prod_cn: {
        proxy: "https://cn-platform-gateway.fs.com/",
        server: "https://cn-platform-gateway.fs.com/collector/",
        client: "https://cn-platform-gateway.fs.com/collector/",
        gateway_api_server: "https://platform-gateway.fs.com",
        gateway_api_client: "https://platform-gateway.fs.com",
        dist_url: "https://front-resource.fs.com/cnfs-front/client/",
        // dist_url: "/_nuxt/",
    },
    prod_cn_local: {
        proxy: "https://cn-platform-gateway.fs.com/",
        server: "https://cn-platform-gateway.fs.com/collector/",
        client: "https://cn-platform-gateway.fs.com/collector/",
        client_hk: "https://gat-api.fs.com/collector/",
        gateway_api_server: "https://cn-order.fs.com",
        gateway_api_client: "https://cn-order.fs.com",
        gateway_api_client_hk: "https://platform-gateway.fs.com",
        dist_url: "https://front-resource.fs.com/compliance-cnfs-front/client/",
    },
    release_cn_local: {
        proxy: "https://testcng-newfs.whgxwl.com/",
        server: "https://testcng-newfs.whgxwl.com/",
        client: "https://testcng-newfs.whgxwl.com/",
        gateway_api_server: "https://platform-gateway.whgxwl.com",
        gateway_api_client: "https://platform-gateway.whgxwl.com",
        dist_url: "/_nuxt/",
    },
    release_en_local: {
        proxy: "https://testing-newfs.whgxwl.com/",
        server: "http://testing-newfs.whgxwl.com/",
        client: "https://testing-newfs.whgxwl.com/",
        gateway_api_server: "https://platform-gateway.whgxwl.com",
        gateway_api_client: "https://platform-gateway.whgxwl.com",
        dist_url: "/_nuxt/",
    },
    release_cate: {
        proxy: "https://api.fs.com/",
        server: "https://api.fs.com/",
        client: "https://api.fs.com/",
        gateway_api_server: "https://platform-gateway.whgxwl.com",
        gateway_api_client: "https://platform-gateway.whgxwl.com",
        dist_url: "https://front-resource.fs.com/test-fs-platform-cate/client/",
    },
    prod: {
        proxy: "https://platform-gateway.fs.com/collector/",
        server: "https://platform-gateway.fs.com/collector/",
        client: "https://platform-gateway.fs.com/collector/",
        gateway_api_server: "https://platform-gateway.fs.com",
        gateway_api_client: "https://platform-gateway.fs.com",
        dist_url: "https://front-resource.fs.com/fs-platform/client/",
    },
}
