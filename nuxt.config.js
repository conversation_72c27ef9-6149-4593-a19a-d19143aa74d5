import router from "./router"
import i18n from "./i18n/i18n.js"
import path from "path"
import { format, transports } from "winston"
require("winston-daily-rotate-file")
const { combine, timestamp } = format
import { api_config } from "./config.js"
const https = require("https")
const fs = require("fs")
import { feishuBotBugReportData } from "./util/util"
import { detectDevice } from "./util/detectDevice"
let version = ""

const isProd = process.env.NODE_ENV === "production" && ["prod", "prod_cn_local"].includes(process.env.ENV) ? true : false

console.log(process.env.NODE_ENV)
console.log(process.env.ENV)
console.log(isProd)

// 判断本地是否开启https,需要本地加上证书，并使用参数--server-https ,eg: npm run dev --  --server-https
console.log(process.argv)
console.log(process.env.NODE_ENV)
const args = process.argv.splice(2)
const isServerHttps = process.env.NODE_ENV === "development" && args.find((i) => i === "--server-https")
const serverHttps = isServerHttps
    ? {
          key: fs.readFileSync(path.resolve(__dirname, "./ssl/key.pem")),
          cert: fs.readFileSync(path.resolve(__dirname, "./ssl/cert.pem")),
      }
    : false

const config = {
    rootDir: process.cwd(),
    buildDir: process.cwd() + "/.nuxt/",
    env: {
        NODE_ENV: process.env.NODE_ENV,
        ENV: process.env.ENV,
        NODE_AREA: process.env.NODE_AREA,
        // NODE_WEBSITE: process.env.NODE_WEBSITE || "EN",
    },
    head: {
        meta: [
            {
                name: "theme-color",
                content: "#FEFFFF",
            },
        ],
        // meta: [...commonMeta],
        // link: [...commonLink],
        // script: [
        //     {
        //         type: "application/ld+json",
        //         json: schemaOrganization,
        //     },
        // ],
    },
    loading: false,
    server: {
        port: 4000,
        host: "0.0.0.0",
        https: serverHttps,
    },
    css: [`@/static/iconfont/iconfont.css`, `@/assets/scss/reset.scss`, `swiper/css/swiper.css`, `video.js/dist/video-js.css`],
    styleResources: {
        scss: [`@/assets/scss/variable.scss`, `@/assets/scss/mixin.scss`],
    },
    serverMiddleware: [
        {
            path: "/nuxtApi/bd",
            handler: "~/server/nuxtApi/bd.js",
        },
    ],
    hooks: {
        render: {
            route(url, result) {
                result.html = result.html.replace(/ data-n-head=".*?"/gi, "").replace(/ data-hid=".*?"/gi, "")
            },
            errorMiddleware(app) {
                app.use((error, req, res, next) => {
                    if (process.env.NODE_ENV === "production" && ["prod", "prod_cn", "prod_cn_local"].includes(process.env.ENV)) {
                        console.log("errorMiddlewareerrorMiddlewareerrorMiddleware")
                        let error_info = error.stack || error.message || ""
                        if (error_info && error_info.toLowerCase().includes("token")) {
                        } else {
                            let url = ``
                            if (req && req.headers) {
                                if (["prod_cn", "prod_cn_local"].includes(process.env.ENV)) {
                                    url = `https://cn.fs.com${req.url}`
                                } else {
                                    url = `https://www.fs.com${req.url}`
                                }
                            }
                            console.log(req)
                            let { os, osVersion, browser, browserVersion } = detectDevice(req.headers["user-agent"] ? req.headers["user-agent"] : "")
                            const dataString = JSON.stringify(feishuBotBugReportData({ url: url, error: error_info, isServer: true, os, osVersion, browser, browserVersion, app_version: version }))
                            const options = {
                                protocol: "https:",
                                hostname: "open.feishu.cn",
                                port: 443,
                                method: "POST",
                                path: "/open-apis/bot/v2/hook/d91358fc-c4b1-4097-a22d-88faf95117e0",
                                headers: {
                                    "Content-Type": "application/json; charset=utf-8",
                                },
                            }
                            const request = https.request(options, (res) => {
                                res.on("data", () => {})
                                res.on("end", () => {})
                                res.on("error", (err) => {})
                            })
                            request.write(dataString)
                            request.end()
                            console.log("errorMiddlewareerrorMiddlewareerrorMiddleware")
                        }
                    }
                    next(error)
                })
            },
        },
    },

    plugins: [
        "@/plugins/axios.js",
        "@/plugins/beforeRouter.js",
        "@/plugins/shopCartLimit.js",
        { src: "@/plugins/loading.js", ssr: false },
        { src: "~/plugins/jsonld" },
        { src: "@/plugins/message.js", ssr: false },
        { src: "@/plugins/vueLazyLoad.js", ssr: false },
        { src: "@/plugins/vueAwesomeSwiper.js", ssr: false },
        { src: "@/plugins/resize.js", ssr: false },
        { src: "@/plugins/exportSavePDF.js", ssr: false },
        "@/plugins/handleLink.js",
        "@/plugins/localeLink.js",
        { src: "@/plugins/gtm.js", ssr: false },
        { src: "@/plugins/bd.js", ssr: false },
        { src: "@/plugins/metrika.js", ssr: false },
        // { src: "@/plugins/linkedin.js", ssr: false },
        { src: "@/plugins/errorHandler.js", ssr: false },
        { src: "@/plugins/c-inject.js" },
        { src: "@/plugins/cookies.js" },
        { src: "@/plugins/liveChat.js", ssr: false },
        { src: "@/plugins/bdRequest.js" },
    ],
    components: false,
    buildModules: [],
    modules: [
        "@nuxtjs/style-resources",
        "@nuxtjs/component-cache",
        "@nuxtjs/proxy",
        "@nuxtjs/axios",
        "cookie-universal-nuxt",
        ["@nuxtjs/i18n", i18n],
        [
            "nuxt-winston-log",
            {
                logPath: "./logs",
                logName: `${process.env.npm_package_name}.log`,
            },
        ],
    ],
    winstonLog: {
        loggerOptions: {
            transports: [
                new transports.DailyRotateFile({
                    format: combine(
                        timestamp({
                            format: "YYYY-MM-DD hh:mm:ss A ZZ",
                        }),
                        format.json()
                    ),
                    level: "info",
                    filename: path.resolve(process.cwd(), "./logs", `info-%DATE%.log`),
                    datePattern: "YYYY-MM-DD",
                    zippedArchive: true,
                    maxFiles: "14d",
                    maxSize: "10m",
                }),
                new transports.DailyRotateFile({
                    format: combine(
                        timestamp({
                            format: "YYYY-MM-DD hh:mm:ss A ZZ",
                        }),
                        format.json()
                    ),
                    level: "error",
                    filename: path.resolve(process.cwd(), "./logs", `error-%DATE%.log`),
                    maxFiles: "14d",
                    maxSize: "10m",
                }),
            ],
        },
    },
    axios: {
        proxy: true,
        retry: true,
    },
    router,
    render: {
        resourceHints: false,
        bundleRenderer: {
            shouldPreload: (file, type) => {
                return ["script", "style", "font"].includes(type)
            },
            shouldPrefetch: (file, type) => {
                return ["style", "font"].includes(type)
            },
        },
    },
    build: {
        extend(config, { isDev, isClient }) {
            if (isDev && isClient) {
                config.devtool = "source-map"
            }
            if (!isDev && isClient) {
                config.performance = {
                    hints: "warning",
                    hints: "error",
                    hints: false,
                    maxAssetSize: 150000,
                    maxEntrypointSize: 400000,
                }
                config.optimization = {
                    splitChunks: {
                        minSize: 150000,
                        maxSize: 400000,
                        cacheGroups: {
                            common: {
                                name: `chunk-common`,
                                chunks: "all",
                                minChunks: 2,
                                maxInitialRequests: 8,
                                priority: 1,
                                reuseExistingChunk: true,
                            },
                            vendors: {
                                name: `chunk-vendors`,
                                test: /[\\/]node_modules[\\/]/,
                                chunks: "all",
                                enforce: true,
                                priority: 2,
                                reuseExistingChunk: true,
                            },
                        },
                    },
                }
            }

            config.node = {
                fs: "empty",
            }
            return config
        },
        publicPath: process.env.NODE_ENV === "development" ? "/_nuxt/" : api_config[process.env.ENV].dist_url,
        babel: {
            compact: false,
        },
        parallel: true,
        cache: true,
        hardSource: true,
    },
}

if (process.env.NODE_ENV === "development") {
    config.proxy = {
        "/api": {
            target: api_config[process.env.ENV].proxy,
            changeOrigin: true,
        },
        "/order-api": {
            target: api_config[process.env.ENV].gateway_api_client,
            changeOrigin: true,
        },
        "/cms": {
            target: api_config[process.env.ENV].gateway_api_client,
            changeOrigin: true,
        },
    }
}

if (process.env.NODE_ENV === "production" && ["prod", "prod_cn", "prod_cn_local", "release", "release_cate"].includes(process.env.ENV)) {
    config.build.babel.plugins = [
        [
            "transform-remove-console",
            {
                exclude: ["error", "warn"],
            },
        ],
    ]
    // config.build.analyze = true
    config.build.hardSource = false
    config.build.cache = false
    // config.build.extractCSS = {
    // 	ignoreOrder: true
    // };
}

if (isProd && version) {
    // config.build.filenames = {
    //     app: () => `[chunkhash]-${version}.js`,
    //     chunk: () => `[chunkhash]-${version}.js`,
    // }
    config.env.NUXT_VERSION = version
}

// if (process.env.ENV === "dev" || process.env.ENV === "release") {
// 	config.head.script.push({
// 		src: 'https://cdn.weglot.com/weglot.min.js'
// 	})
// }

export default config
