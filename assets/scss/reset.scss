@charset "utf-8";

a,
abbr,
acronym,
address,
applet,
article,
aside,
audio,
b,
big,
blockquote,
body,
button,
canvas,
caption,
center,
cite,
code,
dd,
del,
details,
dfn,
div,
dl,
dt,
em,
embed,
fieldset,
figcaption,
figure,
footer,
form,
h1,
h2,
h3,
h4,
h5,
h6,
header,
hgroup,
html,
i,
iframe,
img,
input,
ins,
kbd,
label,
legend,
li,
mark,
menu,
nav,
object,
ol,
output,
p,
pre,
q,
ruby,
s,
samp,
section,
select,
small,
span,
strike,
strong,
sub,
summary,
sup,
table,
tbody,
td,
textarea,
tfoot,
th,
thead,
time,
tr,
tt,
u,
ul,
var,
video {
    margin: 0;
    padding: 0;
    border: 0;
    outline: none;
    -webkit-appearance: none;
    border: none;
    box-sizing: border-box;
}

b,
strong {
    font-weight: 600;
}

// .video-js .vjs-progress-control .vjs-play-progress:before {
//     top: -0.443333em;
// }

body,
html {
    //   -webkit-overflow-scrolling: touch;
    position: relative;
    touch-action: manipulation;
    font-weight: 400;
    //   -webkit-tap-highlight-color: transparent;
    -webkit-text-size-adjust: none;
    -webkit-font-smoothing: antialiased;
    font-style: normal;
    -webkit-text-size-adjust: none;
}

.html_gray {
    -webkit-filter: grayscale(100%);
    -moz-filter: grayscale(100%);
    -ms-filter: grayscale(100%);
    -o-filter: grayscale(100%);
    filter: grayscale(100%);
}

article,
aside,
details,
figcaption,
figure,
footer,
header,
hgroup,
menu,
nav,
section {
    display: block;
}

ol,
ul {
    list-style: none;
}

blockquote,
q {
    quotes: none;
}

blockquote:after,
blockquote:before,
q:after,
q:before {
    content: "";
    content: none;
}

table {
    border-collapse: collapse;
    border-spacing: 0;
}

textarea {
    overflow: auto;
    vertical-align: top;
    resize: vertical;
}

[hidden] {
    display: none;
}

a:active,
a:hover {
    outline: 0;
}

img {
    border: 0;
}

legend {
    border: 0;
    padding: 0;
    white-space: normal;
}

button,
input,
select,
textarea {
    outline: none;
    -webkit-appearance: none;
    border: none;
    background: none;
}

button,
input {
    line-height: normal;
}

button,
select {
    text-transform: none;
}

button,
input,
select,
textarea {
    -webkit-appearance: button;
}

button::-moz-focus-inner,
input::-moz-focus-inner {
    border: 0;
    padding: 0;
}

textarea {
    overflow: auto;
}

table {
    border-collapse: collapse;
    border-spacing: 0;
}

a {
    text-decoration: none;
    color: #0060bf;

    &:hover {
        text-decoration: underline;
    }

    @media (max-width: 768px) {
        &:hover {
            text-decoration: none;
        }
    }
}

h1,
h2,
h3,
h4,
h5,
h6 {
    font-weight: 600;
}

.fixScroll {
    // position: fixed;
    // width: 100%;
    // z-index: 0;
    overflow: hidden !important;
}

input:-webkit-autofill,
input:-webkit-autofill:active,
input:-webkit-autofill:focus,
input:-webkit-autofill:hover {
    -webkit-box-shadow: 0 0 0 1000px #f6f6f8 inset;
}

textarea:-webkit-autofill,
textarea:-webkit-autofill:active,
textarea:-webkit-autofill:focus,
textarea:-webkit-autofill:hover {
    -webkit-box-shadow: 0 0 0 1000px #f6f6f8 inset;
}

input:focus::-webkit-input-placeholder {
    color: transparent;
}

input:focus:-moz-placeholder {
    color: transparent;
}

input:focus::-moz-placeholder {
    color: transparent;
}

input:focus:-ms-input-placeholder {
    color: transparent;
}

textarea:focus::-webkit-input-placeholder {
    color: transparent;
}

textarea:focus:-moz-placeholder {
    color: transparent;
}

textarea:focus::-moz-placeholder {
    color: transparent;
}

textarea:focus:-ms-input-placeholder {
    color: transparent;
}

input::-webkit-input-placeholder {
    /* WebKit browsers */
    color: #707070;
    @include font13;
}

input:-moz-placeholder {
    /* Mozilla Firefox 4 to 18 */
    color: #707070;
    @include font13;
}

input::-moz-placeholder {
    /* Mozilla Firefox 19+ */
    color: #707070;
    @include font13;
}

input:-ms-input-placeholder {
    /* Internet Explorer 10+ */
    color: #707070;
    @include font13;
}

textarea::-webkit-input-placeholder {
    /* WebKit browsers */
    color: #707070;
    @include font13;
}

textarea:-moz-placeholder {
    /* Mozilla Firefox 4 to 18 */
    color: #707070;
    @include font13;
}

textarea::-moz-placeholder {
    /* Mozilla Firefox 19+ */
    color: #707070;
    @include font13;
}

textarea:-ms-input-placeholder {
    /* Internet Explorer 10+ */
    color: #707070;
    @include font13;
}

input {
    display: block;
    width: 100%;
    height: 42px;
    @include font13;
    border: 1px solid #e5e5e5;
    border-radius: 4px;
    padding: 10px 12px;
    transition: all 0.3s;
    color: $textColor1;

    &:focus {
        border: 1px solid #19191a;
    }
    &.is_new {
        background: #f6f6f8;
        border: 1px solid transparent;
        transition: all 0.3s;
        &:not(:disabled) {
            &:focus {
                border: 1px solid #707070;
                background: linear-gradient(0deg, rgba(25, 25, 26, 0.04), rgba(25, 25, 26, 0.04)), #f6f6f8;
            }
            &:hover {
                background: linear-gradient(0deg, rgba(25, 25, 26, 0.04), rgba(25, 25, 26, 0.04)), #f6f6f8;
            }
        }

        &:disabled {
            color: #ccc;
            background: #f6f6f8;
            cursor: not-allowed;
        }
        &::-webkit-input-placeholder {
            @include font12;
        }

        &:-moz-placeholder {
            @include font12;
        }

        &::-moz-placeholder {
            @include font12;
        }

        &:-ms-input-placeholder {
            @include font12;
        }
    }
}

input[type="checkbox"] {
    appearance: none;
    -webkit-appearance: none;
    display: inline-block;
    width: 18px;
    height: 18px;
    border: none;
    border-radius: 0;
    padding: 0;
    line-height: 1;
    background: none;
    background-color: transparent;
    outline: none;
    position: relative;
    vertical-align: middle;
    font-size: 18px;

    // @media (max-width: 960px) {
    //     width: 20px;
    //     height: 20px;
    //     font-size: 20px;
    // }

    &:before {
        content: "\f043";
        color: rgba(25, 25, 26, 0.3);
        font-family: "iconfont" !important;
        -webkit-font-smoothing: antialiased;
        cursor: pointer;
        transition: all 0.3s;
    }

    &:hover {
        &:before {
            content: "\f043";
            color: #707070;
        }
    }

    &[halfChecked="true"] {
        &::before {
            content: "\e287";
            color: #707070;
        }
    }

    &[halfChecked="true"]:hover {
        &::before {
            color: #707070;
        }
    }

    &:checked {
        &:before {
            color: #707070;
            font-family: "iconfont" !important;
            -webkit-font-smoothing: antialiased;
            content: "\f186";
        }

        &:hover {
            &:before {
                color: #707070;
            }
        }
    }

    &:disabled {
        &:before {
            color: rgba(25, 25, 26, 0.3);
        }

        &:before {
            color: rgba(25, 25, 26, 0.3);
        }
    }
}

input[type="radio"] {
    appearance: none;
    -webkit-appearance: none;
    display: inline-block;
    width: 18px;
    height: 18px;
    border: none;
    border-radius: 0;
    padding: 0;
    line-height: 1;
    background: none;
    background-color: transparent;
    outline: none;
    position: relative;
    font-size: 18px;
    margin-right: 8px;

    &:before {
        content: "\f051";
        color: rgba(25, 25, 26, 0.3);
        font-family: "iconfont" !important;
        -webkit-font-smoothing: antialiased;
        cursor: pointer;
    }

    &:hover {
        &:before {
            content: "\f051";
            color: #707070;
        }
    }

    &:checked {
        &:before {
            color: #707070;
            font-family: "iconfont" !important;
            -webkit-font-smoothing: antialiased;
            content: "\f050";
        }

        &:hover {
            &:before {
                color: #707070;
            }
        }
    }

    &:disabled {
        &:before {
            color: rgba(25, 25, 26, 0.3);
            opacity: 0.8;
        }
    }
}

input[type="radio"]:hover,
input[type="checkbox"]:hover {
    @media (max-width: 1024px) {
        &::before {
            color: rgba(25, 25, 26, 0.3);
        }
    }
}

input[type="checkbox"]:indeterminate:hover {
    color: rgba(25, 25, 26, 0.3);
}

select {
    appearance: none;
    -moz-appearance: none;
    -webkit-appearance: none;
    height: 42px;
    width: 100%;
    position: relative;
    color: $textColor1;
    border: 1px solid $borderColor2;
    border-radius: 3px;
    padding: 0 32px 0 12px;
    background-image: url("https://resource.fs.com/mall/generalImg/20241219140745u94akk.svg");
    background-repeat: no-repeat;
    background-position: right 10px center;
    background-size: 14px;
    font-size: 13px;
}

textarea {
    display: block;
    height: 110px;
    width: 100%;
    border-radius: 3px;
    border: 1px solid $borderColor2;
    transition: all 0.3s;
    color: $textColor1;
    font-size: 13px;
    padding: 8px 12px;
    resize: none;

    &:focus {
        border: 1px solid $textColor1;
    }
    &.is_new {
        background: #f6f6f8;
        border: 1px solid transparent;
        transition: all 0.3s;
        &:not(:disabled) {
            &:focus {
                border: 1px solid #707070;
                background: linear-gradient(0deg, rgba(25, 25, 26, 0.04), rgba(25, 25, 26, 0.04)), #f6f6f8;
            }
            &:hover {
                background: linear-gradient(0deg, rgba(25, 25, 26, 0.04), rgba(25, 25, 26, 0.04)), #f6f6f8;
            }
        }

        &:disabled {
            color: #cccccc;
            cursor: not-allowed;
        }
        &::-webkit-input-placeholder {
            @include font12;
        }

        &:-moz-placeholder {
            @include font12;
        }

        &::-moz-placeholder {
            @include font12;
        }

        &:-ms-input-placeholder {
            @include font12;
        }
    }
}

input,
select,
textarea {
    &:disabled {
        background-color: $bgColor1;
    }
}

.print_bg {
    background: $bgColor1;
}

.clearfix:after {
    content: " ";
    display: block;
    clear: both;
    visibility: hidden;
    height: 0;
}

// div[id*="trustbadge-container"] ._owyw4l {
//     bottom: 290px !important;
// }

// div[id*="trustbadge-container"] {
//     z-index: 200000 !important;

//     ._12n8yed {
//         inset: auto 20px 102px auto !important;
//     }
// }

// 渐隐渐现
.fade-enter-active,
.fade-leave-active {
    transition: opacity 0.3s;
}

.fade-enter,
.fade-leave-to {
    opacity: 0;
}

// 左侧滑入
.slide-left-enter-active,
.slide-left-leave-active {
    transition: all 0.3s;
}

.slide-left-enter,
.slide-left-leave-to {
    transform: translate3d(-100%, 0, 0);
}

// 左侧滑入左侧滑出
.slide-left2-enter-active,
.slide-left2-leave-active {
    transition: all 0.3s;
}
.slide-left2-enter,
.slide-left2-leave-to {
    transform: translate3d(-100%, 0, 0);
}

// 右侧滑入
.slide-right-enter-active,
.slide-right-leave-active {
    transition: all 0.3s;
}

.slide-right-enter,
.slide-right-leave-to {
    transform: translate3d(100%, 0, 0);
}

.slide-down-enter-active,
.slide-down-leave-active {
    transition: all 0.3s;
}

.slide-down-enter,
.slide-down-leave-to {
    transform: translate3d(0, 100%, 0);
}

//大动效
.slide-up-enter,
.slide-up-leave-to {
    opacity: 0;
    transform: translateY(-30px);
}

.slide-up-enter-active {
    transition: all 0.3s;
}

.slide-up-enter-to {
    opacity: 1;
}

.slide-up-leave-active {
    transition: all 0.3s;
}

@keyframes run-scale {
    0% {
        transform: scale(0);
    }

    50% {
        transform: scale(1.3);
    }

    100% {
        transform: scale(1);
    }
}

// 路由跳转进度条
#nprogress {
    .bar {
        background: #0060bf !important;
    }

    .peg {
        box-shadow: 0 0 10px #0060bf, 0 0 5px #0060bf !important;
    }

    .spinner-icon {
        border-top-color: #0060bf !important;
        border-left-color: #0060bf !important;
    }
}

// 国旗icon

.country-code {
    display: inline-block;
    background-image: url(https://img-en.fs.com/includes/templates/fiberstore/images/fs-new/country.png);
    background-repeat: no-repeat;
    width: 16px;
    height: 16px;
    margin-right: 5px;

    &.country-af {
        background-position: 0 0;
    }

    &.country-al {
        background-position: -60px 0;
    }

    &.country-dz {
        background-position: -100px 0;
    }

    &.country-as {
        background-position: -120px 0;
    }

    &.country-ad {
        background-position: -140px 0;
    }

    &.country-ao {
        background-position: -160px 0;
    }

    &.country-ai {
        background-position: -180px 0;
    }

    &.country-aq {
        background-position: -200px 0;
    }

    &.country-ag {
        background-position: -220px 0;
    }

    &.country-ar {
        background-position: -260px 0;
    }

    &.country-am {
        background-position: -280px 0;
    }

    &.country-aw {
        background-position: -300px 0;
    }

    &.country-au,
    &.country-hm {
        background-position: -340px 0;
    }

    &.country-at {
        background-position: -360px 0;
    }

    &.country-az {
        background-position: -380px 0;
    }

    &.country-bs {
        background-position: -400px 0;
    }

    &.country-bh {
        background-position: -420px 0;
    }

    &.country-bd {
        background-position: -440px 0;
    }

    &.country-bb {
        background-position: -460px 0;
    }

    &.country-by {
        background-position: 0 -20px;
    }

    &.country-be {
        background-position: -20px -20px;
    }

    &.country-bz {
        background-position: -40px -20px;
    }

    &.country-bj {
        background-position: -60px -20px;
    }

    &.country-bm {
        background-position: -80px -20px;
    }

    &.country-bt {
        background-position: -100px -20px;
    }

    &.country-bo {
        background-position: -120px -20px;
    }

    &.country-ba {
        background-position: -140px -20px;
    }

    &.country-bw {
        background-position: -160px -20px;
    }

    &.country-br {
        background-position: -200px -20px;
    }

    &.country-io {
        background-position: -220px -20px;
    }

    &.country-bn {
        background-position: -240px -20px;
    }

    &.country-bg {
        background-position: -260px -20px;
    }

    &.country-bf {
        background-position: -280px -20px;
    }

    &.country-bi {
        background-position: -300px -20px;
    }

    &.country-kh {
        background-position: -320px -20px;
    }

    &.country-cm {
        background-position: -340px -20px;
    }

    &.country-ca {
        background-position: -360px -20px;
    }

    &.country-cv {
        background-position: -380px -20px;
    }

    &.country-ky {
        background-position: -440px -20px;
    }

    &.country-cf {
        background-position: -460px -20px;
    }

    &.country-td {
        background-position: -480px -20px;
    }

    &.country-cl {
        background-position: 0 -40px;
    }

    &.country-cn {
        background-position: -20px -40px;
    }

    &.country-cx {
        background-position: -40px -40px;
    }

    &.country-cc {
        background-position: -80px -40px;
    }

    &.country-co {
        background-position: -100px -40px;
    }

    &.country-km {
        background-position: -140px -40px;
    }

    &.country-cg {
        background-position: -160px -40px;
    }

    &.country-ck {
        background-position: -200px -40px;
    }

    &.country-cr {
        background-position: -220px -40px;
    }

    &.country-ci {
        background-position: -240px -40px;
    }

    &.country-hr {
        background-position: -260px -40px;
    }

    &.country-cu {
        background-position: -280px -40px;
    }

    &.country-cy {
        background-position: -320px -40px;
    }

    &.country-cz {
        background-position: -340px -40px;
    }

    &.country-dk {
        background-position: -360px -40px;
    }

    &.country-dj {
        background-position: -380px -40px;
    }

    &.country-dm {
        background-position: -400px -40px;
    }

    &.country-do {
        background-position: -420px -40px;
    }

    &.country-ec {
        background-position: -440px -40px;
    }

    &.country-eg {
        background-position: -460px -40px;
    }

    &.country-sv {
        background-position: -480px -40px;
    }

    &.country-gq {
        background-position: -20px -60px;
    }

    &.country-er {
        background-position: -40px -60px;
    }

    &.country-ee {
        background-position: -60px -60px;
    }

    &.country-et {
        background-position: -80px -60px;
    }

    &.country-eu {
        background-position: -100px -60px;
    }

    &.country-fk {
        background-position: -120px -60px;
    }

    &.country-fj {
        background-position: -180px -60px;
    }

    &.country-fi,
    &.country-ax {
        background-position: -200px -60px;
    }

    &.country-fr {
        background-position: -220px -60px;
    }

    &.country-mf {
        background-position: -220px -60px;
    }

    &.country-tf {
        background-position: -240px -60px;
    }

    &.country-gf {
        background-position: -260px -60px;
    }

    &.country-ga {
        background-position: -280px -60px;
    }

    &.country-gm {
        background-position: -320px -60px;
    }

    &.country-ge {
        background-position: -340px -60px;
    }

    &.country-de {
        background-position: -360px -60px;
    }

    &.country-gh {
        background-position: -380px -60px;
    }

    &.country-gi {
        background-position: -400px -60px;
    }

    &.country-gr {
        background-position: -420px -60px;
    }

    &.country-gl {
        background-position: -440px -60px;
    }

    &.country-gd {
        background-position: -460px -60px;
    }

    &.country-gt {
        background-position: 0 -80px;
    }

    &.country-gu {
        background-position: -20px -80px;
    }

    &.country-gg {
        background-position: -40px -80px;
    }

    &.country-gn {
        background-position: -60px -80px;
    }

    &.country-gw {
        background-position: -80px -80px;
    }

    &.country-gy {
        background-position: -100px -80px;
    }

    &.country-ht {
        background-position: -120px -80px;
    }

    &.country-hn {
        background-position: -160px -80px;
    }

    &.country-hk {
        background-position: -180px -80px;
    }

    &.country-hu {
        background-position: -200px -80px;
    }

    &.country-is {
        background-position: -240px -80px;
    }

    &.country-in {
        background-position: -280px -80px;
    }

    &.country-id {
        background-position: -300px -80px;
    }

    &.country-ir {
        background-position: -320px -80px;
    }

    &.country-iq {
        background-position: -340px -80px;
    }

    &.country-ie {
        background-position: -360px -80px;
    }

    &.country-il {
        background-position: -420px -80px;
    }

    &.country-it {
        background-position: -440px -80px;
    }

    &.country-jm {
        background-position: -460px -80px;
    }

    &.country-jp {
        background-position: -480px -80px;
    }

    &.country-je {
        background-position: 0 -100px;
    }

    &.country-jo {
        background-position: -20px -100px;
    }

    &.country-kz {
        background-position: -40px -100px;
    }

    &.country-ke {
        background-position: -60px -100px;
    }

    &.country-ki {
        background-position: -80px -100px;
    }

    &.country-kw {
        background-position: -120px -100px;
    }

    &.country-kg {
        background-position: -140px -100px;
    }

    &.country-lv {
        background-position: -180px -100px;
    }

    &.country-lb {
        background-position: -200px -100px;
    }

    &.country-ls {
        background-position: -220px -100px;
    }

    &.country-lr {
        background-position: -240px -100px;
    }

    &.country-li {
        background-position: -280px -100px;
    }

    &.country-lt {
        background-position: -300px -100px;
    }

    &.country-lu {
        background-position: -320px -100px;
    }

    &.country-mk {
        background-position: -360px -100px;
    }

    &.country-mg {
        background-position: -380px -100px;
    }

    &.country-mw {
        background-position: -400px -100px;
    }

    &.country-my {
        background-position: -420px -100px;
    }

    &.country-mv {
        background-position: -440px -100px;
    }

    &.country-ml {
        background-position: -460px -100px;
    }

    &.country-mt {
        background-position: -480px -100px;
    }

    &.country-mh {
        background-position: 0 -120px;
    }

    &.country-mq {
        background-position: -20px -120px;
    }

    &.country-mr {
        background-position: -40px -120px;
    }

    &.country-mu {
        background-position: -60px -120px;
    }

    &.country-yt {
        background-position: -80px -120px;
    }

    &.country-mx {
        background-position: -100px -120px;
    }

    &.country-fm {
        background-position: -120px -120px;
    }

    &.country-md {
        background-position: -140px -120px;
    }

    &.country-mc {
        background-position: -160px -120px;
    }

    &.country-mn {
        background-position: -180px -120px;
    }

    &.country-me {
        background-position: -200px -120px;
    }

    &.country-ms {
        background-position: -220px -120px;
    }

    &.country-ma {
        background-position: -240px -120px;
    }

    &.country-mz {
        background-position: -260px -120px;
    }

    &.country-mm {
        background-position: -280px -120px;
    }

    &.country-na {
        background-position: -300px -120px;
    }

    &.country-nr {
        background-position: -340px -120px;
    }

    &.country-np {
        background-position: -360px -120px;
    }

    &.country-an {
        background-position: -380px -120px;
    }

    &.country-nl {
        background-position: -400px -120px;
    }

    &.country-nz {
        background-position: -440px -120px;
    }

    &.country-ni {
        background-position: -460px -120px;
    }

    &.country-ne {
        background-position: -480px -120px;
    }

    &.country-ng {
        background-position: 0 -140px;
    }

    &.country-nu {
        background-position: -20px -140px;
    }

    &.country-nf {
        background-position: -40px -140px;
    }

    &.country-mp {
        background-position: -120px -140px;
    }

    &.country-no,
    &.country-bv {
        background-position: -140px -140px;
    }

    &.country-om {
        background-position: -220px -140px;
    }

    &.country-pk {
        background-position: -260px -140px;
    }

    &.country-pw {
        background-position: -280px -140px;
    }

    &.country-ps {
        background-position: -300px -140px;
    }

    &.country-pa {
        background-position: -320px -140px;
    }

    &.country-pg {
        background-position: -340px -140px;
    }

    &.country-py {
        background-position: -360px -140px;
    }

    &.country-pe {
        background-position: -380px -140px;
    }

    &.country-ph {
        background-position: -400px -140px;
    }

    &.country-pn {
        background-position: -420px -140px;
    }

    &.country-pl {
        background-position: -440px -140px;
    }

    &.country-pt {
        background-position: -460px -140px;
    }

    &.country-pr {
        background-position: -480px -140px;
    }

    &.country-qa {
        background-position: 0 -160px;
    }

    &.country-ro {
        background-position: -60px -160px;
    }

    &.country-ru {
        background-position: -80px -160px;
    }

    &.country-rw {
        background-position: -100px -160px;
    }

    &.country-lc {
        background-position: -160px -160px;
    }

    &.country-sm {
        background-position: -240px -160px;
    }

    &.country-st {
        background-position: -260px -160px;
    }

    &.country-sa {
        background-position: -280px -160px;
    }

    &.country-sn {
        background-position: -320px -160px;
    }

    &.country-rs {
        background-position: -340px -160px;
    }

    &.country-sc {
        background-position: -360px -160px;
    }

    &.country-sl {
        background-position: -380px -160px;
    }

    &.country-sg {
        background-position: -400px -160px;
    }

    &.country-sk {
        background-position: -440px -160px;
    }

    &.country-si {
        background-position: -460px -160px;
    }

    &.country-sb {
        background-position: -480px -160px;
    }

    &.country-so {
        background-position: 0 -180px;
    }

    &.country-xs {
        background-position: -20px -180px;
    }

    &.country-za {
        background-position: -40px -180px;
    }

    &.country-kp {
        background-position: -80px -180px;
    }

    &.country-es {
        background-position: -120px -180px;
    }

    &.country-lk {
        background-position: -140px -180px;
    }

    &.country-kn {
        background-position: -160px -180px;
    }

    &.country-vc {
        background-position: -180px -180px;
    }

    &.country-sd {
        background-position: -200px -180px;
    }

    &.country-sr {
        background-position: -220px -180px;
    }

    &.country-sj {
        background-position: -240px -180px;
    }

    &.country-sz {
        background-position: -260px -180px;
    }

    &.country-se {
        background-position: -280px -180px;
    }

    &.country-ch {
        background-position: -300px -180px;
    }

    &.country-sy {
        background-position: -320px -180px;
    }

    &.country-tw {
        // background-position: -360px -180px;
        background-position: -20px -40px;
    }

    &.country-tj {
        background-position: -380px -180px;
    }

    &.country-tz {
        background-position: -400px -180px;
    }

    &.country-th {
        background-position: -420px -180px;
    }

    &.country-tl {
        background-position: -440px -180px;
    }

    &.country-tg {
        background-position: -460px -180px;
    }

    &.country-tk {
        background-position: -480px -180px;
    }

    &.country-to {
        background-position: 0 -200px;
    }

    &.country-tt {
        background-position: -20px -200px;
    }

    &.country-tn {
        background-position: -60px -200px;
    }

    &.country-tr {
        background-position: -80px -200px;
    }

    &.country-tm {
        background-position: -100px -200px;
    }

    &.country-tc {
        background-position: -120px -200px;
    }

    &.country-tv {
        background-position: -140px -200px;
    }

    &.country-ug {
        background-position: -160px -200px;
    }

    &.country-ua {
        background-position: -180px -200px;
    }

    &.country-ae {
        background-position: -240px -200px;
    }

    &.country-uk,
    &.country-gb,
    &.country-gs,
    &.country-im {
        background-position: -260px -200px;
    }

    &.country-us,
    &.country-um {
        background-position: -320px -200px;
    }

    &.country-uy {
        background-position: -340px -200px;
    }

    &.country-uz {
        background-position: -360px -200px;
    }

    &.country-vu {
        background-position: -380px -200px;
    }

    &.country-va {
        background-position: -400px -200px;
    }

    &.country-ve {
        background-position: -420px -200px;
    }

    &.country-vn {
        background-position: -440px -200px;
    }

    &.country-vg {
        background-position: -460px -200px;
    }

    &.country-vi {
        background-position: -480px -200px;
    }

    &.country-eh {
        background-position: -40px -220px;
    }

    &.country-ye {
        background-position: -100px -220px;
    }

    &.country-zm {
        background-position: -120px -220px;
    }

    &.country-zw {
        background-position: -140px -220px;
    }

    &.country-tp {
        background-position: -160px -220px;
    }

    &.country-fo {
        background-position: -180px -220px;
    }

    &.country-gp {
        background-position: -200px -220px;
    }

    &.country-kr {
        background-position: -220px -220px;
    }

    &.country-la {
        background-position: -240px -220px;
    }

    &.country-ly {
        background-position: -260px -220px;
    }

    &.country-mo {
        background-position: -280px -220px;
    }

    &.country-ic {
        background-position: -320px -220px;
    }

    &.country-cd {
        background-position: -340px -220px;
    }

    &.country-xy {
        background-position: -360px -220px;
    }

    &.country-pf {
        background-position: -380px -220px;
    }

    &.country-sh {
        background-position: -400px -220px;
    }

    &.country-re {
        background-position: -220px -60px;
    }

    &.country-pm {
        background-position: -220px -60px;
    }

    &.country-nc {
        background-position: -220px -60px;
    }

    &.country-bl {
        background-position: -420px -220px;
    }

    &.country-xb {
        background-position: -440px -220px;
    }

    &.country-xc {
        background-position: -460px -220px;
    }

    &.country-xm {
        background-position: -480px -220px;
    }

    &.country-bq {
        background-position: 0 -240px;
    }

    &.country-xe {
        background-position: -20px -240px;
    }
}

.slide-left-enter,
.slide-left-leave-to {
    transform: translate3d(-100%, 0, 0);
}

.slide-right-enter,
.slide-right-leave-to {
    transform: translate3d(100%, 0, 0);
}

.slide-bottom-enter,
.slide-bottom-leave-to {
    transform: translate(0, 100%);
}

// #MyCustomTrustbadge {
//     position: relative;
//     margin-top: 18px;

//     ._1gwv20v {
//         ._lk7o4h {
//             position: relative !important;
//             border: 1px solid #ccc !important;
//             border-radius: 3px !important;
//             padding: 11px 16px !important;
//             box-sizing: border-box !important;

//             ._thsmae {
//                 width: 100% !important;
//                 align-items: center !important;

//                 ._1qiwh36 {
//                     width: 58px !important;
//                     height: 58px !important;
//                     padding: 0 !important;
//                     margin: 0 !important;
//                     margin-right: 12px !important;

//                     ._upwhbk {
//                         display: block !important;
//                         margin: auto !important;
//                         width: 58px !important;
//                         height: 58px !important;
//                     }
//                 }

//                 ._argvb9 {
//                     flex: 1 !important;
//                     display: flex !important;
//                     flex-direction: column !important;
//                     justify-content: center !important;
//                     padding: 12px 0 0 0 !important;
//                     align-items: center !important;
//                     height: 58px !important;

//                     ._s7xc8z {
//                         color: #707070 !important;
//                         font-size: 12px !important;
//                         line-height: 18px !important;
//                         font-display: swap !important;
//                         padding: 0 !important;
//                         margin-top: 2px !important;
//                     }

//                     ._8pqgf9 {
//                         color: #707070 !important;
//                         font-display: swap !important;
//                         font-size: 13px !important;
//                         margin-top: 4px !important;
//                         height: 13px !important;
//                         line-height: 13px !important;
//                         margin-bottom: -2px !important;

//                         > span {
//                             display: inline-block !important;
//                             height: 13px !important;
//                             color: #707070 !important;
//                             font-display: swap !important;
//                             line-height: 13px !important;
//                             font-size: 13px !important;
//                         }
//                     }
//                 }
//             }

//             ._zbxp0s {
//                 display: none !important;
//             }

//             ._1iu1jow {
//                 display: none !important;
//             }

//             ._qcra45 {
//                 padding: 0 !important;
//                 top: 6px !important;
//                 font-size: 13px !important;
//                 color: #19191a !important;
//                 white-space: nowrap !important;
//                 margin: 0 !important;
//                 position: absolute !important;
//                 text-align: center !important;
//                 width: calc(100% - 32px) !important;
//                 padding-left: 70px !important;
//                 left: 16px !important;
//                 text-align: center !important;
//                 font-display: swap !important;
//             }
//         }
//     }

//     &.MyCustomTrustbadge-de {
//         ._1gwv20v {
//             ._lk7o4h {
//                 width: 182px !important;
//             }
//         }
//     }

//     &.MyCustomTrustbadge-de-en {
//         ._1gwv20v {
//             ._lk7o4h {
//                 width: 204px !important;
//             }
//         }
//     }

//     &.MyCustomTrustbadge-fr {
//         ._1gwv20v {
//             ._lk7o4h {
//                 width: 223px !important;
//             }
//         }
//     }

//     &.MyCustomTrustbadge-es {
//         ._1gwv20v {
//             ._lk7o4h {
//                 width: 254px !important;
//             }
//         }
//     }

//     &.MyCustomTrustbadge-mx {
//         ._1gwv20v {
//             ._lk7o4h {
//                 width: 254px !important;
//             }
//         }
//     }

//     &.MyCustomTrustbadge-it {
//         ._1gwv20v {
//             ._lk7o4h {
//                 width: 233px !important;
//             }
//         }
//     }
// }

.fsLiveChat {
    position: fixed;
    width: 392px;
    height: 632px;
    bottom: 32px;
    right: -408px;
    z-index: 999;
    transition: right 0.3s ease-in-out;
    &.show {
        right: 68px;
        opacity: 1;
    }
    @media (min-width: 768px) and (max-width: 1024px) {
        &.show {
            right: 72px;
            bottom: 24px;
        }
    }
    // 媒体查询移动端
    @media (max-width: 767px) {
        width: 100%;
        height: 100%;
        bottom: 0;
        right: -100%;
        &.show {
            right: 0;
            z-index: 99999;
        }
    }
    @media (max-height: 700px) {
        bottom: 0;
        height: 100%;
    }
}
//animate 动画
@keyframes LiveChatLeftToRight {
    0% {
        left: 0;
    }

    50% {
        left: 47px;
    }

    100% {
        left: 0;
    }
}

@keyframes LiveChatRightToLeft {
    0% {
        right: 0;
    }

    50% {
        right: 47px;
    }

    100% {
        right: 0;
    }
}

@-webkit-keyframes animation-skeleton-wave {
    0% {
        -webkit-transform: translateX(-100%);
        -moz-transform: translateX(-100%);
        -ms-transform: translateX(-100%);
        transform: translateX(-100%);
    }

    50% {
        -webkit-transform: translateX(100%);
        -moz-transform: translateX(100%);
        -ms-transform: translateX(100%);
        transform: translateX(100%);
    }

    100% {
        -webkit-transform: translateX(100%);
        -moz-transform: translateX(100%);
        -ms-transform: translateX(100%);
        transform: translateX(100%);
    }
}

@keyframes animation-skeleton-wave {
    0% {
        -webkit-transform: translateX(-100%);
        -moz-transform: translateX(-100%);
        -ms-transform: translateX(-100%);
        transform: translateX(-100%);
    }

    50% {
        -webkit-transform: translateX(100%);
        -moz-transform: translateX(100%);
        -ms-transform: translateX(100%);
        transform: translateX(100%);
    }

    100% {
        -webkit-transform: translateX(100%);
        -moz-transform: translateX(100%);
        -ms-transform: translateX(100%);
        transform: translateX(100%);
    }
}

*[tabindex]:focus-visible {
    @include focusVisible;
}
