export default {
    sparePartRequest: "Spare Part Request",
    spareOrder: "Spare Order",
    totaltips: "If the product you purchased cannot be used normally, you can submit spare parts request. Your AM will reach you to help you out on the spare part you required.",
    supportTick: {
        statusTips: {
            approving: "The application has been approved and the order number will be displayed when the order is shipped.",
            review: "The result will be notified to you via email.",
            reject: "Sorry to tell you that your spare part request has not been approved.",
            approved: "You can check the status by clicking xxx",
        },
        supportDetail: "Support Detail",
        caseInformation: "Case Information",
        afterReason: "After- sales reason",
        sparePartRequest: "Spare Part Request",
        orderNumber: "Order Number",
        shippingAddress: "Shipping Address",
        caseDescription: "Case Description",
        spareNumber: "Spare number",
        approved: "Approved",
        underReview: "Under Review",
        rejected: "Rejected",
    },
    // returnTimeTips:'Please return the spare part(s) before xxx.',
    returnTimeTips: "Your order has been successfully delivered. Please return the product within 15 days after receiving the replacement product. If you have any questions, please contact your Account Manager.",
    formError: {
        content: "Please fill in the content.",
    },
    content: "Content",
    edit: "Edit",
    cancel: "Cancel",
    save: "Save",
    submit: "Submit",
    update: "Update Address",
    return: "Return",
    allow: `Allow files of type JPG, JPEG, PDF, PNG, DOC, DOCX, XLS, XLSX. Maximum file size 5M.`,
    policy: {
        agreement_agree: "Please make sure you agree to our Privacy Policy and Notice at Collection and Terms of Use.",
        spare_agree: "Please make sure you agree to our Spare Part Request Agreement.",
        spare_part_request_agreement: "Spare Part Request Agreement.",
        agreement: `I agree to FS's <a href="xxxx " target="_blank" style="color:#19191a">Privacy Policy and Notice at Collection</a> and <a href="yyyy" target="_blank" style="color:#19191a">Terms of Use</a>.`,
        agree_title: "Spare Part Request Agreement",
        sparePopup: [
            {
                tit: "Spare Parts Service Description: ",
                txt: "This is a service that entitles a customer to receive advance replacement of hardware after FS deems a spare part is necessary and a Return Material Authorization (RMA) number is generated. The replacement equipment may be new or equivalent to new in performance and reliability.",
            },
            {
                tit: "Customer Responsibilities:",
                txt: "To enable FS to provide the best possible support and service, the customer will be required to return spare parts to FS within 15 days upon receipt of the replaced or repaired product, and will be responsible for parts damaged or lost during transit. In the event the equipment is not returned within this period or the returned equipment does not meet FS acceptance criteria, FS reserves the right to charge you the list price of the spare parts provided at that time.",
            },
        ],
        read_and_understood: "I agree I have read and understood <a href='javacript:;' onClick='sparePopup()'>XXXXX</a>",
    },
    spare: "Spare",
}
