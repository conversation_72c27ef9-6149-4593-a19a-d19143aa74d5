export default {
    head: {
        title: `Confirm Your Orders - FS`,
        description: `Before going to the payment page, please check your shopping address, billing address, and cost as well as the total cost of the order.`,
    },
    form: {
        first_name_required: `Please enter your first name.`,
        first_name_required2: `Your First Name is required.`,
        first_name_max: `First name must be 40 characters maximum.`,
        first_entry_name_max: `First name must be 35 characters maximum.`,
        first_name_min: `First name must be 2 characters minimum.`,
        last_name_required: `Please enter your last name.`,
        last_name_required2: `Your Last Name is required.`,
        last_name_max: `Last name must be 40 characters maximum.`,
        last_entry_name_max: `Last name must be 35 characters maximum.`,
        last_name_min: `Last name must be 2 characters minimum.`,
        tax_required: `Your VAT/TAX NUMBER is required.`,
        tax_validate: `Please enter a valid VAT NUMBER. eg:`,

        email_required: `Please enter your email address.`,
        email_validate: `Please enter a valid email address. (eg:<EMAIL>)`,
        email_validate2: `Please enter a valid email address. `,
        email_exist: `Account already exists. Click here to`,
        email_exist_sign_in: `sign in.`,
        email_re_enter: `Please re-enter your email address.`,
        email_match: `New email address must match.`,
        phone_required: `Please enter your phone number.`,
        phone_required2: `Your Phone Number is required.`,
        phone_min: `Your phone number must be at least 6 digits.`,
        phone_validate: `Allow digits only, at least 7 ones.`,
        password_required: `Please enter your password.`,
        password_validate: `6 characters minimum; at least one letter and one number. Special characters(_ ? @ ! # $ % & * .) allowed.`,
        password_current: `Please enter your current password.`,
        password_new: `Please enter your new password.`,
        password_confirm: `Please confirm your mew password.`,
        password_match: `New password must match.`,
        comments_questions_required: `Comments/Questions cannot be empty.`,

        company_name_required: `Your Company Name is required.`,
        company_name_validate: `Company name must be between 3 and 120 characters long.`,
        address_type_required: `Your Address Type is required.`,
        address_required: `Your Address is required.`,
        address_validate: `Address line 1 must be between 2 and 35 characters long.`,
        address2_required: `Your Address2 is required.`,
        address2_validate: `Address line 2 must be between 1 and 35 characters long.`,
        city_required: `Your City is required.`,
        state_required: `Your State/Province/Region is required.`,
        zip_code_required: `Your Zip Code is required.`,
        zip_code_validate: `Your ZIP/postal code should be at least 3 characters long.`,
        accountConsisted: `Please enter a valid account consisted of 9 numbers`,
        accountConsisted2: `Please enter a 6-character combination of digits or letters.`,
        cityMaxLength: "City 40 characters maximum.", //
        zipCodeMaxLength: "Zip Code 10 characters maximum.", //
        phoneMaxLength: "Phone Number 11 characters maximum.", //
        phoneIsNumber: "Phone Number can only enter numeric characters.", //
        jpAddressLimit: "Please enter Japanese or English",
    },
    pickTip: `Due to courier delivery personnel shortage, delivery speed might be affected. We recommend warehouse pickup for urgent orders.`,
    returnTip: `Are you sure you want to return to your Shopping Cart?`,
    stayInCheckout: `Stay in Checkout`,
    stayInQuote: `Stay in Quote`,
    returnToCart2: `Return to Cart`,
    confirmOrder: `Confirm Order`,
    summary: `Summary`,
    item: ` item`,
    items: ` items`,
    subtotal: `Subtotal`,
    shippingCost: `Shipping Cost`,
    free: `Free`,
    total: `Total`,
    confirmTip: `By confirming your order, you agree to FS's <a target="_blank" href="/policies/privacy_policy.html">Privacy Policy and Notice at Collection</a> and <a target="_blank" href="/policies/terms_of_use.html">Terms of Use</a>.`,
    liveChat: `Live Chat`,
    orCall: `or call`,
    shareShippingNews: `Share Shipping News`,
    inc: `FS.com Inc.`,
    termsOfUse: `Terms of Use`,
    privacyPolicy: `Privacy Policy and Notice at Collection`,
    paymentMethod: `Payment Method`,
    choosePayment: `Choose Payment`,
    newShippingAddress: `New Shipping Address`,
    newBillingAddress: `New Billing Address`,
    instructions: `Instructions: `,
    shippingConfirmed: "Shipping Cost to be Confirmed.",
    estShippingCost: "Est. Shipping Cost",
    defaultCustomerNote: "The default customer note is required for #73579 & #73598.",
    ddp: {
        include: `(Including DDP trade conditions)`,
        tax: `TAX`,
        insurance: `Insurance`,
        clearance: `Customs Clearance Fee`,
        title: `About taxes`,
        content: `Your products will be shipped from our Asia Warehouse. During international transportation, the products may be spot checked by the Japanese customs and will be charged customs duties. Since our products are tax-free, they are generally subject to 10% consumption tax and customs clearance fees. If you pay taxes in advance, you do not need to pay any additional fees (including taxes) after the product is shipped.`,
    },
    tax: {
        importFeesIncluded: `Import Fees included.`,
        importFeesIncludedTitle: `Import Fees included`,
        importFeesIncludedTxtUSA: `The item will be shipped from Asia global warehouse via  <a href="XXXX" target='_blank'> Global Shipping Program (GSP)</a>. Import fees included at the time of purchase plus customs clearance are handled by FS. Sales tax will be included at checkout.<a href="XXXX" target='_blank'> Learn more</a>`,
        importFeesIncludedTxtPR: `The item will be shipped from Asia global warehouse via <a href="XXXX" target='_blank'> Global Shipping Program (GSP)</a>. Import fees included at the time of purchase plus customs clearance are handled by FS.`,
        importFeesIncludedTxtAU: `The item will be shipped from Asia global warehouse via <a href="XXXX" target='_blank'> Global Shipping Program (GSP)</a>.  Import fees included at the time of purchase plus customs clearance are handled by FS.<a href="XXXX" target='_blank'>Learn more</a> ；`,
        importFees: `Import Fees Excluded.`,
        importFeesTitle: `Import Fees Excluded`,
        seeDetails: `See details`,
        seeDetails2: `See Details`,
        aboutFees: `About Import Fees`,
        contentFromUS: `Items will be shipped from FS  U.S. Warehouse or Asia Warehouse based on current stock, and only product and shipping cost are charged. However, possible import fees may be charged sometimes by customs agencies and should be paid at the time of delivery by recipient.  If you need our help, you may contact us anytime.`,
        applyForTax: `Apply for Tax Exempt`,
        aboutVAT: `Incl. VAT`,
        contentDEVAT: `<p>All items are shipped from FS Germany warehouse, and in accordance with the laws governing members of the European Union.</p><br><p><a href="/" target="_blank">FS.com</a> GmbH is obliged to charge VAT on all orders delivered to member countries of the EU (including Northern Ireland). But VAT can be exempted if a valid VAT number is offered which match the delivery and billing adresses (Excluding Germany).</p>`,
        contentDE: `All items are shipped from FS Germany warehouse, and for orders shipped to non-Europe Union countries, VAT and possible import fees may be charged and should be paid to customs agencies by recipient once the package reaches the specified destination.</p><p>FS has no control over these charges, you could contact your local customs office for further information or you may contact us for help anytime.`,
        contentFromAU: `All items are shipped from FS Australia Warehouse, and only product and shipping cost are charged. However, possible import  fees may be charged sometimes by customs agencies and should be paid at the time of delivery by recipient. If you need our help, you may contact us anytime.`,
        contentAU: `For orders dispatched to locations within Australia, FS is obliged to charge GST on product value and shipping fees at the rate of 10%.`,
        contentUk: `All items are shipped from the FS UK warehouse and  in accordance with UK law. <br><a href="/" target="_blank">FS.com</a> INNOVATION LTD is obliged to charge VAT on all orders delivered to mainland UK (excluding Northern Ireland). `,
        GSTIncluded: `GST included`,
        orderSummary: `Order Summary`,
        aboutGST: `About GST`,
        contentFromSGGST: `For orders dispatched from Singapore warehouse and delivered to locations within Singapore, FS is obliged to charge GST on product value and shipping fees at the rate of XX.`,
        partiallyGSTIncluded: `Partially GST included`,
        partiallyGSTExcluded: `Partially GST excluded <span class="iconfont iconfont_right" style="font-size: 12px;">&#xe703;</span>`,
        contentFromSG: `For orders dispatched from Singapore warehouse and delivered to locations within Singapore, FS is obliged to charge GST on product value and shipping fees at the rate of XX. However, if the products in the order are out of stock, we will ship them from our Asia Wareshoue directly. In this case, GST will not be charged and it will be paid together with import fees to the customs agencies by recipient at time of delivery. If you need our help, you may contact us anytime.`,
        contentSG: `For orders delivered to the destinations outside Singapore, we will only charge product value and shipping fees.</p><p>However, possible import  fees may be charged sometimes by customs agencies and should be paid at the time of delivery by recipient.  If you need our help, you may contact us anytime.`,
        excludingTaxes: `Excluding Taxes`,
        aboutDuties: `About Duties and Taxes`,
        contentRUIndividual: `For orders from natural person and shipped from our international warehouse,we will ONLY charge product value and shipping fees.Any tariff or import duties caused by customs clearance should be declared and borne by the recipient.From January 1, 2020, the threshold for duty free purchases has been lowered to 200 € and up to 31 kg per package.If you are interested in other delivery methods or want to pay by cashless payment, please contact your account manager.`,
        VATIncluded: `VAT included`,
        contentRU: `In accordance with Chapter 21 of the Tax Code of the Russian Federation, <a href="/" target="_blank">FS.com</a> Ltd is obliged to charge VAT on all orders delivered to Russia. </p><p>All products from our catalog are subject to standard VAT of 20% of the cost in accordance with the General Tax Law of Russia.</p><p>You will know the total amount including VAT before making the payment, if you fill in all the necessary information about the order (including the type of enterprise and delivery address).`,
        contentCN: `All items are shipped from FS Asia Warehouse, and only product and shipping cost are charged.<br>However, possible import  fees may be charged sometimes by customs agencies and should be paid at the time of delivery by recipient.  If you need our help, you may contact us anytime.`,
        contentJP: `The prices displayed on our website already include VAT, so there will be no charge after shipment.`,
        WHTExcluded: `Import Fees and WHT Excluded`,
        WHTExcludedCtn: `All items are shipped from FS Asia Warehouse, and only product and shipping cost are charged. Since the price of our products is shown as tax free, and FS, as an Indian non resident company, has no obligation to withhold tax in accordance with Indian tax law, we recommend that you do not deduct taxes when making payment. Please make a total payment to ensure that the corresponding order amount is paid to us, otherwise, we will need to charge you our deducted taxes again. Thank you for your cooperation and understanding. However, possible import fees may be charged sometimes by customs agencies and should be paid at the time of delivery by recipient. If you need our help, you may contact us anytime.`,
        WHTIncl: `Import Fees Excl. And WHT Incl`,
        WHTInclCtn: `All items are shipped from FS Asia Warehouse, and only product and shipping cost are charged. Since the purchase contains software service products, the order has included additional fees, which is 10% of the value of the software item. At the same time, we provide TRC documents for your use. Please contact your account manager. However, possible import fees may be charged sometimes by customs agencies and should be paid at the time of delivery by recipient. If you need our help, you may contact us anytime.`,
        AdditionalFees: `Additional Fees`,
        forwarderExplain: "Import Fees included.",
        npwpTip: "To ensure a smooth delivery, please provide the following:<br>For businesses: Indonesian Tax Number.<br>For individuals: Tax Number, ID Number, Indonesian Passport, or Driver's License.",
    },
    pickup: {
        pickup: `Pickup`,
        deliverTo: `Deliver to your Location`,
        pickupAtW: `Pickup at Warehouse`,
        pickupDate: `Pickup Date`,
        pickupInfo: `Pickup Information`,
        pickupCost: `Pickup Cost`,
        estimated: `Estimated`,
        pickupAt: `PICKUP AT`,
        atTheLatest: `XXXX at the latest`,
        choosePickup: `Choose Pickup`,
        tip: `Tips for Pick-up Services`,
        what: `What to Bring`,
        pickupPolicy: "Pickup Policy",
        sgPickupTip: `The person picking up the goods, you or someone you entrust, needs to provide the order number to pick up your order.`,
        thePerson: `The person picking up the goods, you or someone you entrust, needs to provide the order number and a valid ID to pick up your order, typically a driver's license or state-issued ID card.`,
        heavyTipS: `Please bring carts or other auxiliary equipment to help you collect heavy items.`,
        when: `When to Arrive`,
        arrive: `Arrive at the warehouse at your scheduled time. If you can't arrive on time, you can change your pickup time so we can serve you better.`,
        where: `Where to Park`,
        weOffer: `We offer free customer parking, making your pickup quick and convenient.`,
        notice: `Notice`,
        needsTo: `Needs to provide the order number and a valid ID to pick up your order, typically a driver's license or state-issued ID card.`,
        success: `Pickup information changed successfully.`,
        preparing: `Preparing`,
        toChange: `If you need to change the pick-up time and other information, you can contact the account manager.`,
        jpWhen: `When to Arrive`,
        jpWhenTip: `Once the ordered product arrives at the Japan office, we will send you an email with an appointment for a pickup time. If you need to change your pickup time, please change it in <nuxt-link :to="XXXX"My Account</nuxt-link> or call 03-5826-8305.`,
        wherePick: `Where to Pick up`,
        Recommend: `Recommend`,
    },
    address: {
        defaultAddress: `Set as the default address`,
        shippingAddress: `Shipping Address`,
        change: `Change`,
        addNewAddress: `Add New Address`,
        creatNewAddress: `Creat a New Address`,
        billingAddress: `Billing Address`,
        basicInfo: `Basic Information`,
        firstName: `First name`,
        lastName: `Last name`,
        countryRegion: `Country/Region`,
        addressType: `Address type`,
        companyName: `Company name`,
        optional: `optional`,
        addressInfo: `Address Information`,
        phoneNumber: `Phone number`,
        holderAd: `Street address, c/o`,
        holderAd2: `Apt, Suite, floor, etc.`,
        holderState: `State/Province/Region`,
        addShippingAddress: `New Shipping Address`,
        addBillingAddress: `Add Billing Address`,
        editShippingAddress: `Edit Shipping Address`,
        editBillingAddress: `Edit Billing Address`,
        sameAs: `Billing address same as shipping address`,
        chooseBillingAddress: `Choose Billing Address`,
        chooseShippingAddress: `Choose Shipping Address`,
        shippingTip: `A signature is required for delivery. We do not ship to PO Boxes.`,
        sgPhoneTip: `In order to deliver successfully, please fill in a phone number starting with 8 or 9.`,
        back: `Back`,
        save: `Save`,
        edit: `Edit`,
        remove: `Remove`,
        cancel: `Cancel`,
        delete: `Delete`,
        delTip: `Are you sure you want to delete this address?`,
        businessType: `Business type`,
        individualType: `Individual type`,
        taxTip: `To speed up customs clearance, please fill in a valid Tax Identification Number.`,
        taxTip2: `You can exempt from VAT tax if you have a valid Tax Identification Number.`,
        taxTip3: `According to Mexican Customs' 2025 regulations, to avoid potential delays, additional fees, or returns, kindly provide the recipient's RFC number and full name.`,
        editShippingAd: `Edit Your Shipping Address`,
        editBillingAd: `Edit Your Billing Address`,
        confirmShip: `Confirm Ship to Address`,
        taxNumber: `Tax Number`,
        address: `Address`,
        address2: `Address2`,
        city: `City`,
        stateProvinceRegion: `State/Province/Region`,
        zipCode: `Zip code`,
        suburb: `Suburb`,
        state: `State`,
        postcode: `Postcode`,
        GST: `GST registration No.`,
        VATTAXNumber: `VAT/TAX Number`,
        ukVATTAXNumber: `VAT/TAX (Required for Northern Ireland)`,
        streetandHouse: `Street and house number`,
        additionalAddress: `Additional address`,
        business: `Business`,
        residential: `Residential`,
        private: `Private`,
        naturalPerson: `Natural person`,
        legalPerson: `Legal person`,
        num8: `8-digit number`,
        sgAd1ph: `Street address,building,c/o,etc`,
        sgAd2ph: `House number or apartment, suite, floor, etc`,
        sgAd2Tip: `Accurately fill in the details such as house number, which is more conducive to the service of express transportation to you`,
        streetandHouseUk: `House number and street`,
        ukAd2ph: `House name/Flat name`,
        choosePickup: `Choose Pickup at Warehouse`,
    },
    self: {
        photo: `Your Name on Photo ID is required.`,
        email: `Your Email Address is required.`,
        phone: `Your Contact Number is required.`,
        time: `Your Pick up time is required.`,
    },
    payment: {
        creditDebitCard: `Credit/Debit Card`,
        cardContent: `FS accepts all following cards and P-Cards. The selection of your credit card and the entry of your credit card data takes place after clicking "Confirm Order".`,
        payPalPayPalCredit: `PayPal/PayPal Credit`,
        creditContent: `Clicking "confirm order" later will take you to your PayPal account to complete payment. Afterward, you'll be able to track order status.`,
        elecCheckContent: `We only accept Electronic checks issued by US banks. It may take 4-5 business days for us to process the fund. Payment details will be filled at checkout.`,
        electronicCheck: `Electronic Check`,
        bankAccountName: `Bank Account Name`,
        bankAccountNumber: `Bank Account Number`,
        accountType: `Account Type`,
        confirmBankAccountNumber: `Confirm Bank Account Number`,
        routingNumber: `ABA /ACH routing number`,
        confirmNumber: `Confirm Number`,
        checking: `Checking`,
        saving: `Saving`,
        isRequired: `is required`,
        confirmVal: `Please enter the same value again.`,
        netTerms: `Net Terms`,
        paymentTerms: `Payment Terms`,
        netTermsContent: `Please upload your Purchase Order File after submit the order and the order will be processed timely.`,
        onlinePaymentContent: `You will be redirected to the online portal of your bank to login to your iDEAL account and complete the payment. After you remit the payment, usually it will be received by FS in 1-2 working days.`,
        SOFORTOnlinePayment: `SOFORT Online Payment`,
        IDEALOnlinePayment: `IDEAL Online Payment`,
        eNETSContent: `We offer you a real-time internet payment.`,
        bankContent: `Allow 1-3 working days for your payment to be received via bank transfer.`,
        quotePaymentTip: `The payment method here is preselected, payment details will be completed at order checkout. Click "Request Quote" to view your quote details.`,
    },
    payone: {
        IBAN: "IBAN:",
        bankgrouptype: "BankGroupType:",
        ibanRequired: "IBAN is required, please fill in the correct IBAN.",
        ibanLimit: "The length of IBAN must be between 10 and 30 characters.",
        ibanError: "IBAN verification error, please try again.",
    },
    order: {
        orderDetail: `Order Detail`,
        quoteDetail: `Quote Detail`,
        editCart: `Edit Cart`,
        poNumber: `PO number`,
        poHolder: `Please enter your PO Number`,
        deliveryInstructions: `Delivery Instructions (Optional)`,

        overnightTip: `If your order contains configurations or reprogram service, please note that there will be a delay as the products will need to be serviced by our engineers and go through Quality Control Verification. If you have questions, please contact your account manager or call XXXX.`,
        overDeliveryTip: `Make sure your package arrives safely by entering any relevant delivery instructions here. We'll expedite the process for you.`,
        overDeliveryHolder: `Eg: Urgent Order,Ticket Number, Delivery Time, Delivery Location(front/back door), etc.`,
        overCommentsTip: `Please describe your requirements for the order here.The account manager will confirm your needs before shipping, which may affect shipping timeliness.`,
        overCommentsHolder: `Eg: Order Notes, Blind Shipment, Products/Package/Invoice Requirements, etc.`,

        deliveryHolder: `Ticket number, delivery time, etc.`,
        deliveryTip: `Remark information will be attached to the package, to help us and courier understand your requirements for delivery. Please ensure all information is complete and accurate.`,
        commentsTip: `Please describe your requirements for the order here, we'll proceed immediately.`,
        quoteDescription: `Quote Description (Optional)`,
        quoteHolder: `Enter quote description as reference`,
        afterConfirm: `After confirming shipping and billing address, order shipment and shipping information will be generated accordingly.`,
        delivery: `Delivery`,
        yourItem: `Item`,
        productID: `Product ID`,
        unitPrice: `Price`,
        qty: `Qty`,
        total: `Total`,

        note: `The item cannot be delivered to China, please note it.`,
        heavy: `Heavy`,
        importFees: `Import Fees`,
        included: `included`,
        theItem: `The item will be shipped from Asia global warehouse via <a target="_blank" href="/global-shipping-program-107.html">Global Shipping Program (GSP)</a>. Import fees included at the time of purchase plus customs clearance are handled by FS. Sales tax will be included at checkout. <a target="_blank" href="/specials/global-shipping-program-107.html">Learn more</a>`,
        comments: `Comments (Optional)`,
        commentsHolder: `Please enter your comments`,
        accountHolder: `Express Account`,
        selectDelivery: `Select Delivery Option`,
        shippingCost: `Shipping Cost`,
        total: `Total`,
        free: `Free`,

        photoId: `Name on Identity verification documents`,
        emailAddress: `Email Address`,
        contactNumber: `Contact Number`,
        pickUpTime: `Pick up time`,
        pickUpDate: `Pick up date`,
        dateSmall: "Please refer to the email notification for the pickup time. It takes 1-2 hours to go through order processing and prepare the package well. In addition, the warehouse is closed on public holidays.",
        dateSmallDE: "If the pickup person is someone else, please fill in the Pickup information on their behalf.",
        dateSmallNoSg: "The valid ID Card of the pickup person should be presented at the time of pick up.", //
        pickInfo: "Pick-up information",
        pickSmall01: [
            "Your order is ready to be picked up. If you need to authorise someone else to pick up your order, please download the form and fill it out by yourself.",
            "It is essential to bring the completed authorisation document to pick up the goods.",
        ],
        pickSubTitle01: "Method one: Fill in the authorizer online",
        pickCheckboxLabel: "If checked, it is deemed that the person is authorized to pick up the goods.。。。",
        pickSubTitle02: "Method two: Authorizer for downloading files",
        pickSmall02: "When picking up the goods, you need to carry the completed authorization document。.",
        pickDownloadBtn: "Authorization Document",
        authorizer: "Authorizer",
        authorizedPerson: "Authorized person",
        errorGeneral: "Please enter relevant information",
        errorCheckbox: "To continue you need to agree to the above prompts",
        pickingInformation: "Picking Information",
        estimatedPickupTime: `Estimated pickup time`,
        delayPickTip: `Please refer to the delivery email notice for the exact time of self delivery. If you have any questions, please contact your account manager.`,
    },
    createQuote: `Request Quote`,
    previewPDF: `Preview PDF`,
    returnToCart: `Return to Cart`,
    create: `Request Quote`,
    home: "Home",
    cart: `Cart`,

    inventory: `Inventory, Delivery Date, Estimated Tax and Shipping Cost are subject to change and will be recalculate at checkout.`,
    ru: {
        edit: `Edit`,
        remove: `Remove`,
        cancel: `Cancel`,
        delete: `Delete`,
        delTip: `Delete this information?`,
        cashlessPayment: `Cashless Payment`,
        afterPlacing: `After placing the order, our sales will process your order immediately and contact you by phone or email.`,
        newCashless: `New Cashless Payment Information`,
        uploadCard: `UPLOAD COMPANY BUSINESS CARD`,
        allow: `Allow files of type JPG, JPEG, PDF, PNG, DOC, DOCX, XLS, XLSX. Maximum file size 5M.`,
        info: `CASHLESS PAYMENT INFORMATION`,
        organ: `Name of the organization`,
        legalAddress: `Legal address`,
        inn: `INN`,
        kpp: `KPP`,
        bic: `BIC`,
        bankName: `Bank name`,
        email: `E-mail`,
        phone: `Phone number`,
        save: `Save`,
        your: `Your`,
        isRequired: `is required`,
        editInfo: `Edit Payment Information`,
        paymentInfo: `Please edit your payment information.`,
        form: {
            organ: `Your Name of the organization is required.`,
            legalAddress: `Your Legal address is required.`,
            inn: `Your INN is required.`,
            kpp: `Your KPP is required.`,
            bic: `Your BIC is required.`,
            bankName: `Your Bank name is required.`,
            email: `Your E-mail is required.`,
            phone: `Your Phone number is required.`,
        },
    },
    quote: {
        success: `Your Quote has been created successfully`,
        youCan: `You can view and convert this quote to an order in “`,
        accountQuote: `Account / Quote`,
        trans: `“`,
        ifYou: `If you have questions with configuration, pricing and contract verification, please contact your account manager.`,
        yourFeedback: `Your feedback will help us provide you a better experience on Quote function, we'd love to hear from you.`,
        clickHere: `Click here`,
        toLeave: ` to leave feedback.`,
        share: `Share via Email`,
        back: `Back to Shopping`,
    },
    pickUpAt: `Pick Up At`,
    pickupTimeEstimated: `Pickup time estimated XXXX`,
    pickupTime: `Pickup time XXX`,
    confirmOrders: "Confirm Order",
    lalamoveTip: "LALAMOVE allows you to schedule delivery and view driver information.",
    freeItem: "I want one for free: I accept the conditions",
    confirmTrialOrder: `Confirm Trial Order`,

    additionalInformation: "Additional information", //
    identityNumList: ["IEC number", "ID number", "Passport number", "Driver's license number"], //
    identityNumErrorList: ["Your IEC number is required.", "Your ID number is required.", "Your passport number is required.", "Your driver's license number is required."], //
    DHLTips: "<NAME_EMAIL> in your DHL account.", //
    chinaSendTips: "Since Armenia International Express does not accept lithium battery products, please confirm with your carrier in advance whether the destination can accept them.", //
    ukBankTransferReplenish: "FS also accepts the following electronic payments.",
    shippingAccount: {
        saveShipping: "Save my shipping account", //
        newShipping: "New Shipping Account", //
        label: "Shipped by Your Account:", //
        account: "Account#",
    },
    scheduled: {
        scheduledDelivery: "Scheduled Delivery",
        sagawaScheduledDelivery: "Sagawa Scheduled Delivery",
        yamatoScheduledDelivery: "Yamato Scheduled Delivery",
        jpScheduledValueErr: "Please select scheduled Delivery.",
    },
}
