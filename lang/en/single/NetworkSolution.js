export default {
    title: "Solution Selector",
    selectIndustry: "Select Your Industry",
    selectIndustryTxt: "FS wired &amp; wireless campus network solutions are optimized for different industries. Let's find the network for you. What’s your industry?",
    previousQuestion: "Previous Question",
    networkUsed: "How will your network be used?",
    previousQuestion: "Previous Question",
    backupNeeds: "Do you have serious backup needs?",
    back: "Back",
    startOver: "Start Over",
    customSolution: "Your custom solution is complete!",
    exploreDetails: "Please explore all the details below.",
    INDUSTRY: "INDUSTRY",
    BANDWIDTHREQUIREMENT: "BANDWIDTH REQUIREMENT",
    BACKUPNEEDS: "BACKUP NEEDS",
    basedAnswers: "Based on your answers, here are the products we recommend to securely connect your business, empower your teams to collaborate easily.",
    switch: "Switch",
    wifiProducts: "Wi-Fi Products",
    gateway: "Gateway",
    buying: "Buying the tech you need is this easy.",
    satisfied: "Satisfied with this solution?",
    buyNow: "Buy Now",
    stillQuestions: "Still have questions?",
    contactUs: "Contact Us Now",
    submitted: `Your request <span>xxxx1</span> has been submitted successfully.`,
    submittedTip: `We will reply you within 1-3 hours during working days. You can also discuss the case with our team online, and trace updates in “ <a href="xxxx2">Case Center</a> ” with FS account.`,
    caseCenter: "Case Center",
    industryData: {
        education: "Education",
        manufacturing: "Manufacturing",
        hotel: "Hotel",
        retail: "Retail",
        internet: "Internet",
        other: "Other",
    },
    bandwidthData: {
        tit1: "Lower bandwidth",
        txt1: "Now I only have internet browsing, voice, video and file-sharing apps like Google Docs &amp; SharePoint, but may have new mobile devices later.",
        alt1: "Fs Lower.svg",
        tit2: "Higher bandwidth",
        txt2: "I have file-sharing apps, IP voice, 4K video, live streaming and UCC applications like Slack, Zoom &amp; Skype.",
        alt2: "Fs Higher.svg",
    },
    backupDtata: {
        tit1: "Yes",
        txt1: "My business needs the most reliable connectivity and secure backup.",
        alt1: "Fs Yes.svg",
        tit2: "No",
        txt2: "I have file-sharing apps, IP voice, 4K video, live streaming and UCC applications like Slack, Zoom &amp; Skype.",
        alt2: "Fs No.svg",
    },
    byNow: {
        popTit: "Product list",
        item: "Item",
        Qty: "Qty (pcs)",
        unitPrice: "Unit Price",
        addCart: "Add to Cart",
    },
    contactUsPop: {
        tit: "Contact Information",
        txt1: " Not the products you're looking for? Welcome to contact us, we are here to help.",
        txt2: `Already have an account? <a href="xxxx3" >Sign in</a> or <a href="yyyy2" >Create an account</a>`,
        signIn: "Sign in",
        CreateAccount: "Create an account",
        agreement: `I agree to FS's <a href="xxxx4 " target="_blank" style="color:#19191a">Privacy Policy and Notice at Collection</a>and<a href="yyyy3" target="_blank" style="color:#19191a">Terms of Use</a> .`,
        privacyPolicy: "Privacy Policy and Notice at Collection",
        termsUse: "Terms of Use",
        firstName: "First Name",
        lastName: "Last Name",
        emailAddress: "Email Address",
        phoneNumber: "Phone Number",
        comments: "Comments",
        commentsHolder: "Please briefly describe your questions.",
        commentsRequired: "Please describe your questions so that we can handle your request quicker.",
        uplodTip: "Allow files of type PDF, JPG, PNG.Maximum file size 5M.",
        submit: "Submit",
        uploadFile: "upload file",
        first_name: {
            first_name_required: "Please enter your first name.",
            first_name_max: "First name must be 40 characters maximum.",
            first_entry_name_max: "First name must be 35 characters maximum.",
            first_name_min: "First name must be 2 characters minimum.",
        },
        last_name: {
            last_name_required: "Please enter your last name.",
            last_name_max: "Last name must be 40 characters maximum.",
            last_entry_name_max: "Last name must be 35 characters maximum.",
            last_name_min: "Last name must be 2 characters minimum.",
        },
        email: {
            email_required: "Please enter your email address.",
            email_validate: "Please enter a valid email address. (eg:<EMAIL>)",
            email_validate2: "Please enter a valid email address. ",
            email_exist: "Account already exists. Click here to",
            email_exist_sign_in: "sign in.",
            email_re_enter: "Please re-enter your email address.",
            email_match: "New email address must match.",
        },
        phone: {
            phone_required: "Please enter your phone number.",
            phone_min: "Your phone number must be at least 6 digits.",
        },
        address: {
            tit: "Address ",
            address_required: "Your Address is required.",
            address_validate: "Address line 1 must be between 4 and 35 characters long.",
        },
        agreement_agree: "Please make sure you agree to our Privacy Policy and Notice at Collection and Terms of Use.",
    },
}
