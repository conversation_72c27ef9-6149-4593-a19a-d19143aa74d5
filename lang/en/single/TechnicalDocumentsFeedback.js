export default {
    tit: `Doc Feedback`,
    form: {
        partNumber: `Part number`,
        partPl: `ID / Part No. `,
        doctype: `Doctype`,
        url: `URL`,
        problemsWithDoc: `Problems with documentation`,
        problemOptions: [
            `The command line is inconvenient/not obvious`,
            `Grammar/spelling errors`,
            `The Link can't be opened`,
            `Supported features cannot be implemented according to the configuration`,
            `Documentation errors or missing information`,
            `Add examples or graphs`,
            `Unreasonable phenomena occur during the configuration process`,
            `other`,
        ],
        applicationScenarios: `Application scenarios`,
        applicationOptions1: [
            {
                name: `Pto P network`,
                value: 1,
            },
            {
                name: `MLAG and Stacking typical networking scenarios`,
                value: 2,
            },
            {
                name: `Typical ring networking`,
                value: 3,
            },
            {
                name: `Medium and large enterprise networks`,
                value: 4,
            },
            {
                name: `Small business networks`,
                value: 5,
            },
            {
                name: `Data Center Network`,
                value: 6,
            },
        ],
        applicationScenariosChild: `Function`,
        applicationOptions2: [
            {
                name: `How to manage`,
                value: [4],
            },
            {
                name: `Port security`,
                value: [4],
            },
            {
                name: `Static routes`,
                value: [1, 3, 4, 5],
            },
            {
                name: `VLAN`,
                value: [1, 2, 3, 4, 5],
            },
            {
                name: `DHCP`,
                value: [1, 3, 4, 5],
            },
            {
                name: `ERPS`,
                value: [3],
            },
            {
                name: `Port aggregation`,
                value: [6],
            },
            {
                name: `Load redundancy`,
                value: [6],
            },
            {
                name: `LACP`,
                value: [1, 2],
            },
            {
                name: `VRRP`,
                value: [2],
            },
            {
                name: `Spanning-tree`,
                value: [3],
            },
            {
                name: `EVPN`,
                value: [6],
            },
            {
                name: `Centralized/distributed VXLAN`,
                value: [6],
            },
            {
                name: `Qos`,
                value: [4],
            },
            {
                name: `BGP`,
                value: [6],
            },
            {
                name: `Web`,
                value: [4, 5],
            },
            {
                name: `MLAG`,
                value: [2, 3, 6],
            },
            {
                name: `stack`,
                value: [6],
            },
            {
                name: `STACKING`,
                value: [2, 3],
            },
            {
                name: `Double-active/multi-active`,
                value: [6],
            },
            {
                name: `OSPF/ISIS`,
                value: [6],
            },
            {
                name: `AC's Management of AP`,
                value: [5],
            },
            {
                name: `other`,
                value: [1, 2, 3, 4, 5, 6],
            },
        ],
        remarks: `Remarks (supplementary information)`,
        remarkPl: [`You can describe the specific location of problem in the PDF;`, `Provide your product firmware version, which is more helpful to help you solve problems and maintain documentation information.`],
        uploadFile1: `Please upload configuration and log information`,
        uploadTip1: `Please use a DOC, DOCX or TXT file. Maximum size 5M`,
        uploadFile2: `Please upload a network topology diagram to help test and verify`,
        uploadTip2: `Please use a PDF, JPG, PNG. Maximum size 5M`,
        name: `Name`,
        email: `Email`,
        submitTip: `I agree to FS's <a href="XXXX1" target="_blank">Privacy Policy and Notice at Collection</a> and <a href="XXXX2" target="_blank">Terms of Use</a>.`,
    },
    errors: {
        select_error: `This field is required`,
        name_error: `Please enter your name`,
        email_error: `Please enter your business email`,
    },
}
