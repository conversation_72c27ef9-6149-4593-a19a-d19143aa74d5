export default {
    banner: "FS Demo Zone",
    crumbs: ["Home", "FS Demo Zone"],
    left: {
        title: "Try FS Products",
        font: "Our remote testing service allows users to deploy and connect to products running in our lab, access these products remotely to operate them.",
        demo: {
            title: "What FS demo can do for me:",
            txt01: "100+ functions experience",
            txt02: "Performance tests",
            txt03: "Branded switch compatibility",
            txt04: "Standard application scenarios",
            txt05: "Customized solutions",
        },
        expect: {
            title: "What can I expect?",
            txt01: "User scenarios simulation, on-site operation feeling",
            txt02: "No delay, no screen freezing",
            txt03: "1 minute access, 30 minutes experience",
            txt04: "One-on-One technical engineer online support",
        },
    },
    right: {
        account: 'Already have an account? <a href="login.html" class="log">Sign in</a> or <a href="register.html" class="reg">Create an account</a>.',
        please_select: `Please Select Areas of Interest`,
        which_switch: "Which model are you interested in？",
        which_function: "Which functions would you like to try?",
        hot_search: "Hot Search",
        search: `Search`,
        time: "Please schedule a time",
        comment: "Comments (Optional):",
        post_issue: "Don't find what you want? Try to post your issues.",
        agree: `I agree to FS's XXXXX and YYYYY.`,
        privacy_policy: "Privacy Policy and Notice at Collection",
        terms_of_use: "Terms of Use",
        success: {
            title: "Your request XXXXX has been submitted successfully.",
            we_will_reply_you: "We will reply you within 1-3 hours during working days. You can also discuss the case with our team online, and trace updates in xxxxx with FS account.",
            support_tickets: `“Support Tickets”`,
        },
        submit: `Submit`,
    },
    there_is_no_result_for: `There is no result for "XXX", please double check your spelling.`,
    Eg: `Eg: `,
}
