export default {
    banner: {
        title: "PicOS® for H100 InfiniBand Solution",
        desc: "High-performance, scalability, and security for HPC",
        crumbList: [
            {
                name: "Home",
                url: "/",
            },
            {
                name: "HPC Networking",
                url: "/solutions/hpc-networking-2000.html",
            },
            {
                name: "Solution ID: S3000",
                url: "/solutions/picos-for-h100-infiniband-solution-S3000.html",
            },
        ],
    },
    nav: {
        list: ["Introduction", "Products", "PicOS®", "AmpCon-DC", "Benefits", "Resources"],
        btn: "Contact Us",
    },
    introduction: {
        title: "PicOS® and AmpCon-DC Management Platform for NVIDIA® InfiniBand H100 Network",
        desc: "The FS switches can utilize the advanced PicOS® and AmpCon-DC management platform to empower data center operators to efficiently provision, monitor, manage and maintain the modern data center fabric, realizing higher utilization and reducing overall opex.",
        tagTittle: ["Overview", "InfiniBand Network", "Management Network", "Storage Network"],
        tagSubTitle: [
            `Based on the NVIDIA® H100 GPU, along with <a href="xxxx" target="_blank">PicOS®</a> software and <a href="nnnn" target="_blank">AmpCon-DC management platform</a>, the FS H100 Infiniband solution is tailored according to the network topology of HPC architecture, including infiniband network, management network, and storage network, to meet various business needs.`,
            "Powered by NVIDIA® H100 GPU and InfiniBand switches, the Infiniband network features ultra-low latency and high bandwidth, ensuring lossless transmission with flow control and CRC redundancy checks.",
            "The FS switches can utilize the advanced PicOS® software and AmpCon-DC management platform feature sets to empower customers to efficiently provision, monitor, manage, preventatively troubleshoot, and maintain the HPC infrastructure, realizing higher utilization and reducing overall opex.",
            "FS PicOS® switches support the BGP protocol with powerful routing control capabilities while ensuring the optimal forwarding path and low-latency forwarding status of the storage network. Meanwhile, it is flexible and can scale to meet specific capacity and bandwidth requirements.",
        ],
        tagDesc: [
            {
                title: "InfiniBand Lossless Transmission",
                desc: "The H100 architecture, powered by InfiniBand switches, offers ultra-low latency and 800Gbps high bandwidth, ensuring lossless transmission with flow control and CRC checks.",
            },
            {
                title: "PicOS® Operating System",
                desc: "PicOS® eliminates single-supplier dependency on critical networking infrastructure to deliver a more resilient, programmable, and scalable networking operating system (NOS) at a lower TCO.",
            },
            {
                title: "AmpCon-DC Management Platform",
                desc: "AmpCon-DC empowers data center operators to efficiently provision, monitor, manage, and preventatively troubleshoot and maintain their data center fabric.",
            },
        ],
    },
    products: {
        title: "Product List",
        tabTitList: ["InfiniBand Network", "Management Network", "Storage Network"],
        list: [
            [
                {
                    title: "QM9700/9790 InfiniBand Switches",
                    descTitle: ["MQM9700-NS2F", "MQM9790-NS2F"],
                },
                {
                    title: "ConnectX®-6/7 InfiniBand Adapters",
                    descTitle: ["MCX75510AAS-NEAT", "MCX75310AAC-NEAT", "MCX75310AAS-NEAT"],
                },
                {
                    title: "800G/400G InfiniBand Transceivers",
                    descTitle: ["OSFP-SR8-800G", "OSFP-SR4-400G-FL"],
                },
                {
                    title: "Fiber Cables",
                    descTitle: ["12FMTPOM4"],
                },
            ],
            [
                {
                    title: "AmpCon-DC Management Platform",
                    descTitle: ["P-AC-EE-B1"],
                },
                {
                    title: "PicOS® Switches",
                    descTitle: ["N8550-64C", "N8550-48B8C", "N5850-48S6Q", "S5810-48TS"],
                },
                {
                    title: "Optical Transceiver Module",
                    descTitle: ["QSFP28-SR4-100G", "QSFP28-SFP28-CVR", "SFP28-25GSR-85", "SFP-10GSR-85"],
                },
                {
                    title: "Fiber Cables",
                    descTitle: ["12FMTPOM4", "OM4LCDX", "C6ASFTPSGPVC"],
                },
            ],
            [
                {
                    title: "PicOS® Switches",
                    descTitle: ["N8550-64C"],
                },
                {
                    title: "ConnectX®-6 Ethernet Adapters",
                    descTitle: ["MCX623106AN-CDAT"],
                },
                {
                    title: "Optical Transceiver Module",
                    descTitle: ["QSFP28-SR4-100G"],
                },
                {
                    title: "Fiber Cables",
                    descTitle: ["12FMTPOM4"],
                },
            ],
        ],

        list1: [
            [
                {
                    title: "QM9700/9790 InfiniBand Switches",
                    descTitle: ["MQM9700-NS2F", "MQM9790-NS2F"],
                },
                {
                    title: "ConnectX®-6/7 InfiniBand Adapters",
                    descTitle: ["MCX75510AAS-NEAT", "MCX75310AAC-NEAT", "MCX75310AAS-NEAT"],
                },
                {
                    title: "800G/400G InfiniBand Transceivers",
                    descTitle: ["OSFP-SR8-800G", "OSFP-SR4-400G-FL"],
                },
                {
                    title: "Fiber Cables",
                    descTitle: ["12FMTPOM4"],
                },

                {
                    title: "AmpCon-DC Management Platform",
                    descTitle: ["P-AC-EE-B1"],
                },
                // {
                //     title: "PicOS® Switches",
                //     descTitle: ["N8550-64C", "N8550-48B8C", "N5850-48S6Q", "S5870-48T6S"],
                // },
                // {
                //     title: "Optical Transceiver Module",
                //     descTitle: ["QSFP28-SR4-100G", "QSFP28-SFP28-CVR", "SFP28-25GSR-85", "SFP-10GSR-85"],
                // },
                // {
                //     title: "Fiber Cables",
                //     descTitle: ["12FMTPOM4", "OM4LCDX", "C6ASFTPSGPVC"],
                // },
            ],
            [
                {
                    title: "AmpCon-DC Management Platform",
                    descTitle: ["P-AC-EE-B1"],
                },
                {
                    title: "PicOS® Switches",
                    descTitle: ["N8550-64C", "N8550-48B8C", "N5850-48S6Q", "S5870-48T6S"],
                },
            ],
            [
                {
                    title: "PicOS® Switches",
                    descTitle: ["N8550-64C"],
                },
                {
                    title: "ConnectX®-6 Ethernet Adapters",
                    descTitle: ["MCX623106AN-CDAT"],
                },
                {
                    title: "Optical Transceiver Module",
                    descTitle: ["QSFP28-SR4-100G"],
                },
                {
                    title: "Fiber Cables",
                    descTitle: ["12FMTPOM4"],
                },
            ],
        ],
    },
    picos: {
        title: "More Resilient & Efficient Network Operations at a Lower TCO",
        desc: `With <a href="xxxx" target="_blank">PicOS®</a>, deliver highly resilient, highly reliable, programmable networks that are leaner and more scalable than their monolithic predecessors.`,
        list: [
            {
                title: "Full Interoperability",
                subTitle:
                    "Supports OpenFlow, SNMP, and gNMI protocols, provides API application programming interfaces, delivers interoperability with Cisco and other third-party infrastructure for progressive migration and upgrade.",
            },
            {
                title: "Network Virtualization",
                subTitle: "Use an open solution with spine-leaf arrays to support flexible and scalable virtualization architectures without Cisco chassis solutions.",
            },
            {
                title: "Hardened Security",
                subTitle: "Enables complex ACL without exhausting TCAM resources to flexibly control network traffic, ensuring data security without sacrificing hardware performance.",
            },
            {
                title: "Network Flexibility",
                subTitle:
                    "CrossFlow™ technology enables SDN/OpenFlow traffic with production Layer 2/Layer 3 traffic on the same switch ports, forming Open Intent-based Networking (OIBN) for enhanced network service flexibility.",
            },
        ],
    },
    ampcon: {
        title: "Automate End-to-End Network Lifecycle Management",
        desc: "Prevent misconfigurations and downtime with end-to-end networking lifecycle management, complete with automated provisioning, maintenance, compliance checking, and upgrades.",
        list: [
            {
                title: "Zero-Touch Provisioning",
                subTitle: "Scale easily and securely from anywhere with automated provisioning and policy enforcement.",
            },
            {
                title: "Configuration Management",
                subTitle: "Push updates, patches, and bug fixes to a single or group of switches to save labor, prevent downtime.",
            },
            {
                title: "Backups & Compliance",
                subTitle: "Automate configuration backups, operation logs, and compliance checks against a “golden config” with intelligent rollback to minimize errors.",
            },
            {
                title: "Switch Visibility",
                subTitle: "Provides detailed inventory of all switches, including hardware details, software version, configuration and more.",
            },
            {
                title: "Agentless Automation",
                subTitle: "Write Ansible Playbooks to create and schedule customized workflows with easy-to-read reports.",
            },
            {
                title: "PicOS-V Pre-configuration",
                subTitle: `<a href="xxxx" target="_blank">AmpCon-DC management platform</a> can be pre-configured with <a href="nnnn" target="_blank">PicOS-V</a> in virtualization scenario and then moved to the data center.`,
            },
        ],
    },
    benefits: {
        title: "Explore the Benefits of  H100 InfiniBand Network",
        list: [
            {
                title: "Unified Management Platform",
                desc: `<a href="xxxx" target="_blank">PicOS®</a> with <a href="nnnn" target="_blank">AmpCon-DC management platform</a> enables unified configuration, monitoring and maintenance of management and storage networks, eliminating costly downtime and time-consuming manual tasks.`,
            },
            {
                title: "Powerful InfiniBand Architecture",
                desc: "FS provides highly reliable InfiniBand network architectures and professional technical services, delivering customized network designs and product lists within 48 hours.",
            },
            {
                title: "Cost-effective Solution",
                desc: "By partnering with NVIDIA®, FS can deliver original products such as InfiniBand switches and adapters, cutting costs by 30% through its rich product ecosystem.",
            },
            {
                title: "Top-notch R&D and Testing",
                desc: "FS has a first-class R&D center and uses NVIDIA® MQM9790 switches, ConnectX®-7 network cards and other products to build a test platform for full load transmission testing to ensure stability.",
            },
            {
                title: "Partition Design",
                desc: "The network partition design (computing, storage, in-band management, and out-of-band management) helps isolate different business areas and reduce traffic complexity.",
            },
            {
                title: "Flexible Scalability",
                desc: "The Spine-Leaf layer 2 network architecture meets current network operation requirements while providing flexibility and reliability for future business expansion.",
            },
        ],
        list1: [
            {
                title: "Unified Management Platform112",
                desc: `<a href="xxxx" target="_blank">PicOS®</a> with <a href="nnnn" target="_blank">AmpCon-DC management platform</a> enables unified configuration, monitoring and maintenance of management and storage networks, eliminating costly downtime and time-consuming manual tasks.`,
            },
            {
                title: "Powerful InfiniBand Architecture",
                desc: "FS provides highly reliable InfiniBand network architectures and professional technical services, delivering customized network designs and product lists within 48 hours.",
            },
            {
                title: "Cost-effective Solution",
                desc: "By partnering with NVIDIA®, FS can deliver original products such as InfiniBand switches and adapters, cutting costs by 30% through its rich product ecosystem.",
            },
            {
                title: "Top-notch R&D and Testing",
                desc: "FS has a first-class R&D center and uses NVIDIA® MQM9790 switches, ConnectX®-7 network cards and other products to build a test platform for full load transmission testing to ensure stability.",
            },
            {
                title: "Partition Design",
                desc: "The network partition design (computing, storage, in-band management, and out-of-band management) helps isolate different business areas and reduce traffic complexity.",
            },
            // {
            //     title: "Flexible Scalability",
            //     desc: "The Spine-Leaf layer 2 network architecture meets current network operation requirements while providing flexibility and reliability for future business expansion.",
            // },
            // {
            //     title: "Flexible Scalability",
            //     desc: "The Spine-Leaf layer 2 network architecture meets current network operation requirements while providing flexibility and reliability for future business expansion.",
            // },
        ],
    },
    resources: {
        title: "Resources",
        list: [
            {
                title: "Feature Solutions",
                linkText: ["PicOS® for Automated Driving Solution", "Empower HPC with RoCE Network", "Boost HPC with FS InfiniBand Transceivers and Cables Solution"],
            },
            {
                title: "Blogs",
                linkText: ["InfiniBand Network and Architecture Overview", "Pica8 AmpCon-DC: Your Open Network Automation Partner", "Tips on Choosing InfiniBand Products for HPC Computing"],
            },
            {
                title: "News",
                linkText: [
                    "FS Switches Now Support PicOS® for Unified Networking Experience",
                    "The Rise of HPC Data Centers: FS Empowering Next-gen Data Centers",
                    "FS Unveils Next-Gen High-Speed Network Connectivity Solution to Accelerate the Era of HPC",
                ],
            },
            {
                title: "Documents",
                linkText: ["PicOS® for H100 InfiniBand Solution Test Report", "H100 InfiniBand Solution Delivery Manual", "800G&400G Transceiver Acceptance Testing Guide"],
            },
        ],
        list1: [
            {
                title: "Feature Solutions111",
                linkText: ["PicOS® for Automated Driving Solution", "Empower HPC with RoCE Network", "Boost HPC with FS InfiniBand Transceivers and Cables Solution"],
            },
            {
                title: "Blogs",
                linkText: ["InfiniBand Network and Architecture Overview", "Pica8 AmpCon-DC: Your Open Network Automation Partner", "Tips on Choosing InfiniBand Products for HPC Computing"],
            },
            // {
            //     title: "News",
            //     linkText: [
            //         "FS Switches Now Support PicOS® for Unified Networking Experience",
            //         "The Rise of HPC Data Centers: FS Empowering Next-gen Data Centers",
            //         "FS Unveils Next-Gen High-Speed Network Connectivity Solution to Accelerate the Era of HPC",
            //     ],
            // },
        ],
    },
    contactUs: {
        title: "Ready to get started?",
        desc: "Have a question about purchasing a solution that you are interested in? Fill in the form and we’ll respond in 1 working day.",
    },
    headTop: {
        href: "/solutions/picos-for-h100-infiniband-solution-S3000.html",
        name: "H100 InfiniBand Solution",
    },
    meta: {
        title: "PicOS® and AmpCon-DC for NVIDIA® InfiniBand H100 Network",
        description:
            "Discover how FS H100 InfiniBand solutions revolutionize high-performance computing networks, boosting efficiency by 20%. With PicOS software and AmpCon Network Controller, users enjoy seamless configuration and maintenance, enhancing security and accelerating HPC workflows.",
    },
}
