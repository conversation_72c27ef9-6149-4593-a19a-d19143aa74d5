export default {
    first_name: {
        first_name_required: "Please enter your first name.",
        first_name_max: "First name must be 40 characters maximum.",
        first_entry_name_max: "First name must be 35 characters maximum.",
        first_name_min: "First name must be 2 characters minimum.",
    },
    last_name: {
        last_name_required: "Please enter your last name.",
        last_name_max: "Last name must be 40 characters maximum.",
        last_entry_name_max: "Last name must be 35 characters maximum.",
        last_name_min: "Last name must be 2 characters minimum.",
    },
    email: {
        email_required: "Please enter your email address.",
        business_email_required: "Please enter your business email.",
        email_valid: "Please enter a valid email address.",
        email_validate2: "Please enter a valid email address. (eg:<EMAIL>)",
        email_validate3: `Please do not fill in an email address including "admin/support/postmaster/abuse", which may cause errors when using the account.`,
    },
    phone: {
        phone_required: "Please enter your phone number.",
        phone_min: "Your phone number must be at least 6 digits.",
        phone_validate: "Allow digits only, at least 7 ones.",
        phone_validate_us: `Your phone number should be a 10-digit number.`,
    },
    password: {
        password_required: "Please enter your password.",
        password_validate: "6 characters minimum and 32 characters maximum; at least one letter and one number. Special characters(_ ? @ ! # $ % & * .) allowed.",
        password_validate2: "Password must be 32 characters maximum.",
        password_confirm: `Please enter your password.`,
        password_match: `Passwords do not match. please try again.`,
    },
    register_password: {
        valid: "Please enter a valid password.",
        condition_list: ["At least one letter", "At least one number", "6-32 characters", "No spaces"],
        tips: "Special characters(_ ? @ ! # $ % & * .) allowed.",
    },
    ret_code: {
        ret_code_required: `Reset code is required.`, //新增 2021-08-03
    },
    company: {
        company_required: "Your Company Name is required.",
        company_validate: "Company Name must be between 1 and 120 characters long.",
        company_size: "Please select your company size.",
    },
    address_type: {
        address_type_required: "Your Address Type is required.",
    },
    address: {
        address_required: "Your Address is required.",
        address_validate: "Address line 1 must be between 4 and 35 characters long.",
    },
    address2: {
        address2_required: "Your Address2 is required.",
        address2_validate: "Address line 2 must be between 4 and 35 characters long.",
    },
    city: {
        city_required: "Your City is required.",
        city_validate: "City must be between 2 and 40 characters long.",
    },
    state: {
        state_required: "Your State/Province/Region is required.",
    },
    agreement: {
        agree: "Please make sure you agree to our Privacy Policy and Notice at Collection and Terms of Use.",
    },
    zip_code: {
        zip_code_required: "Your Zip Code is required.",
        zip_code_validate: "Your ZIP/postal code should be at least 3 characters long.",
    },
    industry: {
        industry_required: "Please select industry.",
    },
    function: {
        function_required: "Please select at least one function.",
    },
    time: {
        time_required: "Please select time.",
    },
    date: {
        date_required: "Please select date.",
    },
    comments_questions: {
        comments_questions_required: "Comments/Questions cannot be empty.",
    },
    half_validate: `Please enter half - Angle English Numbers.`,
    zip_code_match: `The zip code and address don't match.`,
    tel_error: `The phone number entered is wrong.`,
    valid_phone_number: `Please enter a valid phone number.`,
    valid_post_number: `Please enter a valid postcode.`,
    aggree_policy_new: "I have read and agree to the FS.COM <a href='BBBB' target='_blank'>Terms and Conditions </a> and acknowledge the Privacy <a href='AAAA' target='_blank'> Policy and Notice at Collection</a>.",
}
