export default {
    first_name: "First name",
    last_name: "Last name",
    first_name01: "First Name",
    last_name01: "Last Name",
    full_name: "Please enter your full name.",
    country: "Country/Region",
    country2: "Country",
    your_country: "Your Country",
    your_country_region: "Your Country/Region",
    email: "Email",
    email_ddress: "Email address",
    email_business: "Business email",
    job_title: "Job title",
    interest_type: "What do you need the sample for?",
    interest_type_options: ["Exsiting devices replacement", "Environmental test", "Product test before formal purchase", "End user's requirement", "Security test", "Functional test"],
    put_mail_address: "Put Email Address",
    phone: "Phone",
    phone_number: "Phone number",
    optional: "optional",
    zip_code: "Zip Code",
    zip_code_ph: "Enter your Zip Code",
    address_ph: "Street address, c/o",
    city_ph: "Enter your City",
    address: "Address",
    address_2: "Address 2",
    detail_address: "Detailed address",
    city: "City",
    state_province_region: "State / Province",
    state_province: "State / Province",
    comment: "Comments",
    inquriy_details: "Inquriy Details",
    select_contact: "Please select at least one contact method.",
    comments_placeholder: "Describe more details about your request.",
    Comments_Questions: "Comments/Questions",
    Optional: "optional",
    password: "Password",
    forgot_password: "Forgot password",
    create_an_account: "Create an account",
    sign_in: "Sign in",
    company_name: "Company name",
    industry: "Industry",
    company_size: "Company size",
    enter_product_id: "Please fill in the product ID here for searching",
    add: "Add",
    yes_txt: "Yes",
    no_txt: "No",
    prev: "Previous",
    next: "Next",
    submit: "Submit",
    agree_txt01: "I agree I have read and understood",
    agree_txt02: "I agree to FS's",
    agree_txt03: "I agree to FS's",
    and: "and",
    sample_application_agreement: "Sample Application Agreement",
    privacy_policy: "Privacy Policy and Notice at Collection",
    terms_of_use: "Terms of Use",
    privacy_policy1: "Privacy Policy and Notice at Collection",
    terms_of_use1: "Terms of Use",
    please_enterpro: "Please enter product ID and click “Add to Sample”",
    please_describe: "Please describe how you intend to use the item.",
    please_select: "Please select",
    already_have_an_account: "Already have an account?",
    subject: "Subject",
    select_subject_options: ["Please select a subject type", "Order & Payment Issue", "Order Status", "After Sales & RMA", "Product & Technical Support", "Others"],
    allow_files_of_type: "Allow files of type PDF, JPG, PNG.",
    maximum_size_5M: "Maximum size 5M.",
    upload_file: "Upload file",
    other_txt: "Other",
    how_would: "How would you like us to contact you?",
    have_question: "Have a question about purchasing a product or solution that you are interested in? Fill in the form and we’ll respond in 1 working day.",
    fs_will_not: `FS will not provide your information to the third parties. For more information on how FS manages, uses, and protects your information please refer to <a href="xxxx" target='_blank'>Term of Use</a>.`,
    errors: {
        products_id_error: "The online product ID can't be empty.",
        products_id_error01: "The product ID (1) was not found in our records.",
        entry_firstname_error: "Please enter your first name.",
        entry_lastname_error: "Please enter your last name.",
        email_address_error: "Please enter your email address.",
        email_address_error01: "Please enter a valid email address.",
        email_address_error02: "This email is already registered.",
        entry_telephone_error: "Please enter your phone number.",
        entry_telephone_error01: "Your phone number must be at least 6 digits.",
        entry_postcode_error: "Your Zip Code is required.",
        entry_postcode_error01: "Your ZIP/postal code should be at least 3 characters long.",
        entry_street_address_error: "Your Address is required.",
        entry_street_address_line1_error: `Address line 1 must be between 4 and 35 characters long.`,
        entry_street_address_line2_error: `Your address line 2 should be at least 2 characters long.`,
        entry_city_error: "Your City is required.",
        state_error: "Your State is required.",
        check1_error: "Please make sure you agree to Product Testing Agreement.",
        check2_error: "Please make sure you agree to our Privacy Policy and Notice at Collection and Terms of Use.",
        comments_error: "Please write down more comments about your request.",
        subject_type_error: "Please select the subject.",
        rating_error: "Please rate your experience.",
        topic_error: "Please select a topic.",
        content_error: "Please enter more than 10 characters.",
        email_error: "Please enter a valid email address.",
        email_business_error: "Please enter your business email.",
        email_business_error01: "Please enter a valid business email.",
        company_name_error: "Please enter your company name.",
        job_title_error: "Please enter your job title.",
        interest_type_error: "This field is required.",
        select_error: "Please select at least one.",
        input_error: "Please enter the detail.",
        sensiWords: "内容包含不适当的词汇，请重新编辑",
    },
    tell_tit: "Tell us a little about yourself.",
    success: {
        tit: "Submitted Successfully",
        txt: `We will reply you within 1 working day. You can also discuss the case with our team online, and trace updates in "%XXXX%Cases%ZZZZ%" under your FS account.`,
        txt1: `We will reply you within 1 working day. You can also discuss the case with our team online, and trace updates in "%XXXX%Cases%ZZZZ%" with FS account.`,
        btn_txt: "Return to Homepage",
    },
    upload: "Please upload your Application Form.",
    TaxMax: "Tax Number 20 characters maximum.",
    TaxMaxAdress: "Tax Number 40 characters maximum.",
    addressLimit: "Address must be between 1 and 40 characters long.",
    cityMax: "City 50 characters maximum.",
    phoneMax: "Phone Number 11 characters maximum.",
    phoneType: "Phone Number can only enter numeric characters.",
    zipCodeMax: "Zip Code 10 characters maximum.",
    PONumberMax: "PO Number 40 characters maximum.",
    frCompanyTip: "Please complete the legal form of the company.",
    CNlimitTip: `Orders on this site cannot be delivered to China. Please kindly go to <u style="cursor: pointer;">FS China</u> if you wish to deliver to China.`,
    jpFormPlaceHolder: {
        firstName: "Example:Yamamoto",
        lastName: "Example:Genryuusai Shigekuni",
        email: "Example:<EMAIL>",
        telephone: "Example:+81090XXXXXX",
        company: "Example:Co., Ltd",
        position: "Example:employee",
        zipCode: "Example:5300001",
        city: "Example:Nagoya",
    },
    interest_type_products: "Tell us how our product can help you",
    sampleRequestPopupTitle: "Free Product Trial",
    sampleRequestPopupSuccessTitle: "We have received your trial application.",
    sampleRequestPopupSuccessDesc: `We will contact you within 1 working day. You can also discuss the case with our team online, and trace updates in "<a href="XXX">Cases</a>" with your FS account.`,
    installationRequestPopupSuccesstitle: `We have received your Free Online Installation and Configuration Service.`,
    installationRequestPopupSuccessDesc: `We will contact you within 1 working day. You can check your application status in cases.`,
    integrationServicesPopupSuccesstitle: `We have received your integration service request.`,
    integrationServicesPopupSuccessDesc: `We will contact you within 1 working day. You can check your application status in cases.`,
    viewCases: "View Cases",
    letUsContactYou: "Let us contact you",
    trialCart: "Trial Cart",
    summaryTips: "Product charges can be made after a 30-day trial.",
    sampleRequestTips: {
        title: "Steps to begin product trial",
        stepList: [
            {
                title: "Submit",
                describe: "Submit trial application form.",
            },
            {
                title: "Place order",
                describe:
                    "Once the application is approved, you can place a trial order without Product charges. Shipping and delivery cost (if not eligible for free shipping), and additional charges in transit (e.g. tariff for customs clearance) should be borne by yourself.",
            },
            {
                title: "Try product",
                describe: "Try out product for 30 days.",
            },
            {
                title: "Buy/return",
                describe: "Choose to buy or return product after testing.",
            },
        ],
    },
    enterProID: "Enter Product SKU",
    productSKU: "Product SKU",
    enterFillProId: "Please fill in the Product ID",
    placeHolder: {
        firstname: "Please enter your last name.",
        lastname: "Please enter your first name.",
        company: "e.g., FS Japan co., LTD.",
        tax_number: "e.g., T0123456789001",
        postcode: "e.g., 143-0006",
        city: "e.g., daejeon district",
        telephone: "e.g., 0312345678",
        address: "e.g., heiwajima 4-1-23",
        address2: "e.g., 5th floor of JS progress building",
        provice: "State",
        searchCode: "Search zip code",
        nodata: "There is an error in the zip code.",
        short: "Enter 7 digits.",
        false: "I found no corresponding address.",
        province: "Please select",
        provinceNone: "Your State is required.",
        cityNone: "Your City is required.",
        addressNone: "Your Address is required.",
        addressLength: "Please enter at least 4 characters and fill in the street number (Banchi).",
    },
    howCanHelp: "How can we help?",
    howCanHelpOptions: ["Product Recommendation", "Delivery Time", "Inventory", "Product price"],
    signedSuccessfully: "Signed successfully!",
    signedSuccessfullyExplain: "The sample order is in the processing phase. Please reach out to your account manager to get more information if necessary.",
    jpPostMatch: "The zip code and address don't match.",
    jpPostcodeMatchErr: `No matching address is found, please check whether the address is correct, or manually fill in the address.`,
    businessCategory: "Business Category",
    applicationScenario: "Application Scenario",
    whatIsYourProject: "What's Your Project Interest, or How We Can Best Help You?",
    brieflyOutlineYour: "Briefly outline your interest or project requirements, such as project baclication scenario, involved devices,scales, budget, and other information.",
    option: {
        business_category: [
            {
                value: 1,
                name: "Distributor/Wholesaler",
            },
            {
                value: 2,
                name: "(ISP) Internet Service Providers",
            },
            {
                value: 3,
                name: "Enterprises Networks",
            },
            {
                value: 4,
                name: "Communication Devices and Technical Services",
            },
            {
                value: 5,
                name: "Government",
            },
            {
                value: 6,
                name: "Financial Services",
            },
            {
                value: 7,
                name: "Manufacturing",
            },
            {
                value: 8,
                name: "Energy",
            },
            {
                value: 9,
                name: "Management & Consulting",
            },
            {
                value: 10,
                name: "Telecom",
            },
            {
                value: 11,
                name: "Retail",
            },
            {
                value: 99,
                name: "Others",
            },
        ],
        application_scenario: [
            {
                value: 1,
                name: "HPC Networking",
            },
            {
                value: 2,
                name: "Internet Data Center",
            },
            {
                value: 3,
                name: "Enterprise LAN",
            },
            {
                value: 4,
                name: "Enterprise WLAN",
            },
            {
                value: 5,
                name: "Surveillance System",
            },
            {
                value: 6,
                name: "Optical Networking",
            },
            {
                value: 7,
                name: "Data Center Cabling",
            },
            {
                value: 8,
                name: "Enterprise Network Cabling",
            },
            {
                value: 99,
                name: "Others",
            },
        ],
    },
}
