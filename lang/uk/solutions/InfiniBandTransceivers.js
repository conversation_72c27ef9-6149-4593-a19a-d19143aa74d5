export default {
    banner: {
        title: "InfiniBand Transceivers and Cables Connectivity Solution Overview",
        desc: "Explore InfiniBand transceivers and cables for GPU-accelerated computing.",
        crumbList: [
            {
                name: "Home",
                url: "/",
            },
            {
                name: "HPC Networking",
                url: "/solutions/hpc-networking-2000.html",
            },
            {
                name: "Solution ID: S3001",
                url: "/solutions/infiniband-transceivers-and-cables-connectivity-solution-overview-S3001.html",
            },
        ],
    },
    nav: {
        list: ["Overview", "Connectivity", "Products", "Resources"],
        btn: "Contact Us",
    },
    overview: {
        title: "Overview",
        desc: "FS provides the line of 800G NDR, 400G NDR, 200G HDR, 100G EDR and 56/40G FDR in InfiniBand products for high-speed data centers and high-computing applications. These products enables flexible connections between switches, switch-to-Smart NIC, and GPU servers, meeting various distance and performance requirements.",
        introduction: [
            {
                title: "InfiniBand Optical Transceivers",
                subTitle: [
                    "Built-in brand chip to ensure stable data transmission",
                    "High bandwith, low cost, low latency, long reaches",
                    "Qualified for InfiniBand NDR, HDR, EDR and FDR end-to-end systems",
                    "100% verified by the original, perfectly compatible with NVIDIA devices such as the QM9700/8700 series, CX6/7 adapters, etc",
                ],
            },
            {
                title: "InfiniBand Cables",
                subTitle: [
                    "Lowest-cost, lowest-latency, low insertion losses and high-speed interconnect",
                    "For higher bandwidth in hyperscale data centers and cloud infrastructure",
                    "Simplifies patching and offers a cost-effective way for short links",
                    "100% verified by the original, perfectly compatible with NVIDIA devices such as the QM9700/8700 series, CX6/7 adapters, etc",
                ],
            },
        ],
    },
    connectivitys: {
        title: "Connectivity",
        type: [
            {
                title: "800G NDR InfiniBand Transceivers",
                model: [
                    {
                        modelTitle: "OSFP-SR8-800G",
                        modelContent: [
                            `Use the <a href="/products/205113.html">800G OSFP SR8</a>(Finned-top) along with <a href="/products/208163.html">MMF MPO-12(APC) cables</a> for a direct connection between NVIDIA Quantum-2 IB switches.`,
                            `Use the <a href="/products/205113.html">800G OSFP SR8</a>(Finned-top) along with two <a href="/products/200963.html">400G OSFP SR4</a>(Flat top), and <a href="/products/208163.html">MMF MPO-12(APC) cables</a> to establish a connection between the NVIDIA Quantum-2 IB switch and two <a href="/products/177272.html">400G OSFP NDR NICs</a>.`,
                        ],
                    },
                    {
                        modelTitle: "OSFP-SR8-800G-FL",
                        modelContent: [
                            `Use the <a href="/products/229687.html">800G OSFP SR8</a>(Flat-top) along with <a href="/products/208163.html">MMF MPO-12(APC) cables</a> to establish a connection between the NVIDIA Quantum-2 IB switch and the DGX H100 GPU server.`,
                        ],
                    },
                    {
                        modelTitle: "OSFP-DR8-800G",
                        modelContent: [
                            `Use the <a href="/products/205319.html">800G OSFP DR8</a>(Finned-top) along with <a href="/products/68018.html">SMF MPO-12(APC) cables</a> for a direct connection between NVIDIA Quantum-2 IB switches.`,
                            `Use the <a href="/products/205319.html">800G OSFP DR8</a>(Finned-top) along with two <a href="/products/205271.html">400G OSFP DR4</a>(Flat top), and <a href="/products/68018.html">SMF MPO-12(APC) cables</a> to establish a connection between the NVIDIA Quantum-2 IB switch and two <a href="/products/177272.html">400G OSFP NDR NICS</a>.`,
                        ],
                    },
                    {
                        modelTitle: "OSFP-2FR4-800G",
                        modelContent: [
                            `Use the <a href="/products/205315.html">800G OSFP 2FR4</a>(Finned-top) along with <a href="/products/40191.html">SMF LC(UPC) cables</a> for a direct connection between NVIDIA Quantum-2 IB switches.`,
                        ],
                    },
                ],
            },
            {
                title: "800G NDR InfiniBand Cables",
                model: [
                    {
                        modelTitle: "OSFP-800G-PCxxx",
                        modelContent: [`Use the <a href="/products/204993.html">800G OSFP(Finned-top) DAC</a> for a direct connection between NVIDIA Quantum-2 IB switches.`],
                    },
                    {
                        modelTitle: "OSFP-800G-2QPCxxx",
                        modelContent: [
                            `Use the <a href="/products/213769.html">800G OSFP(Finned-top) to 2x400G QSFP112 DAC</a> to establish a connection between the NVIDIA Quantum-2 IB switch and two 400G QSFP112 NDR NICs.`,
                        ],
                    },
                    {
                        modelTitle: "OSFP-800G-2OFLPCxxx",
                        modelContent: [
                            `Use the <a href="/products/205005.html">800G OSFP(Finned-top) to 2x400G OSFP(Flat-top) DAC</a> to establish a connection between the NVIDIA Quantum-2 IB switch and two <a href="/products/177272.html">400G OSFP NDR NICs</a>.`,
                        ],
                    },
                    {
                        modelTitle: "OSFP-800G-4QPCxxx",
                        modelContent: [
                            `Use the <a href="/products/210701.html">800G OSFP(Finned-top) to 4x200G QSFP112 DAC</a> to establish a connection between the NVIDIA Quantum-2 IB switch and two 200G Dual-Port QSFP112 NDR NICs.`,
                        ],
                    },
                    {
                        modelTitle: "OSFP-800G-4OFLPCxxx",
                        modelContent: [
                            `Use the <a href="/products/204983.html">800G OSFP(Finned-top) to 4x200G OSFP(Flat-top) DAC</a> to establish a connection between the NVIDIA Quantum-2 IB switch and four <a href="/products/177272.html">200G OSFP NDR NICs</a>.`,
                        ],
                    },
                ],
            },
            {
                title: "400G NDR InfiniBand Transceivers",
                model: [
                    {
                        modelTitle: "OSFP-SR4-400G-FL",
                        modelContent: [
                            `Use the <a href="/products/205113.html">800G OSFP SR8</a>(Finned-top) along with two <a href="/products/200963.html">400G OSFP SR4</a>(Flat top), and <a href="/products/208163.html">MMF MPO-12(APC) cables</a> to establish a connection between the NVIDIA Quantum-2 IB switch and two <a href="/products/177272.html">400G OSFP NDR NICs</a>.`,
                        ],
                    },
                    {
                        modelTitle: "OSFP-DR4-400G-FL",
                        modelContent: [
                            `Use the <a href="/products/205319.html">800G OSFP DR8</a>(Finned-top) along with two <a href="/products/205271.html">400G OSFP DR4</a>(Flat top), and <a href="/products/68018.html">SMF MPO-12(APC) cables</a> to establish a connection between the NVIDIA Quantum-2 IB switch and two <a href="/products/177272.html">400G OSFP NDR NICS</a>.`,
                        ],
                    },
                ],
            },
            {
                title: "400G NDR InfiniBand Cables",
                model: [
                    {
                        modelTitle: "OSFPFL-400G-PCxxx",
                        modelContent: [`Use the <a href="/products/219557.html">400G OSFP(Flat-top) DAC</a> for a direct connection between <a href="/products/212161.html">400G OSFP NDR NICs</a>.`],
                    },
                    {
                        modelTitle: "OSFP-400G-2QPCxxx",
                        modelContent: [
                            `Use the <a href="/products/205391.html">400G OSFP(Finned-top) to 2x200G QSFP56 DAC</a> for a direct connection between NVIDIA Quantum IB switches.`,
                            `Use the <a href="/products/205391.html">400G OSFP(Finned-top) to 2x200G QSFP56 DAC</a> to establish a connection between the NVIDIA Quantum-2 IB switch and two <a href="/products/168437.html">200G QSFP56 HDR NICs</a>.`,
                        ],
                    },
                    {
                        modelTitle: "OSFP-400G-4QPCxxx",
                        modelContent: [
                            `Use the <a href="/products/224827.html">400G OSFP(Finned-top) to 4x100G QSFP56 DAC</a>for a direct connection between NVIDIA Quantum IB switches.`,
                            `Use the <a href="/products/224827.html">400G OSFP(Finned-top) to 4x100G QSFP56 DAC</a> to establish a connection between the NVIDIA Quantum-2 IB switch and two <a href="/products/168436.html">100G Dual-Port QSFP56 HDR NICs</a>.`,
                        ],
                    },
                    {
                        modelTitle: "OSFP-400G-2QAOxxx",
                        modelContent: [
                            `Use the <a href="/products/205337.html">400G OSFP(Finned-top) to 2x200G QSFP56 AOC</a> for a direct connection between NVIDIA Quantum-2 IB switches.`,
                            `Use the <a href="/products/205337.html">400G OSFP(Finned-top) to 2x200G QSFP56 AOC</a> to establish a connection between the NVIDIA Quantum-2 IB switch and two <a href="/products/168437.html">200G QSFP56 HDR NICs</a>.`,
                        ],
                    },
                ],
            },
            {
                title: "200G HDR InfiniBand Transceivers",
                model: [
                    {
                        modelTitle: "QSFP-SR4-200G",
                        modelContent: [
                            `Use the <a href="/products/167425.html">200G QSFP56 SR4</a> along with <a href="/products/68017.html">MMF MPO-12(UPC) cables</a> for a direct connection between NVIDIA Quantum IB switches.`,
                        ],
                    },
                    // {
                    //     modelTitle: "QSFP-FR4-200G",
                    //     modelContent: [
                    //         `Use the <a href="/products/205349.html">200G QSFP56 FR4</a> along with <a href="/products/40191.html">SMF LC(UPC) cables</a>  for a direct connection between NVIDIA Quantum IB switches.`,
                    //     ],
                    // },
                ],
            },
            {
                title: "200G HDR InfiniBand Cables",
                model: [
                    {
                        modelTitle: "QSFP-200G-PCxxx",
                        modelContent: [
                            `Use the <a href="/products/205043.html">200G QSFP56 DAC</a> for a direct connection between NVIDIA Quantum IB switches.`,
                            `Use the <a href="/products/205043.html">200G QSFP56 DAC</a> for a direct connection between the NVIDIA Quantum IB switch and <a href="/products/168437.html">200G QSFP56 HDR NICs</a>.`,
                        ],
                    },
                    {
                        modelTitle: "QSFP-200G-ACxxx",
                        modelContent: [
                            `Use the <a href="/products/148856.html">200G QSFP56 ACC</a> for a direct connection between NVIDIA Quantum IB switches.`,
                            `Use the <a href="/products/148856.html">200G QSFP56 ACC</a> for a direct connection between the NVIDIA Quantum IB switch and <a href="/products/168437.html">200G QSFP56 HDR NICs</a>.`,
                        ],
                    },
                    {
                        modelTitle: "QSFP-200G-AOxxx",
                        modelContent: [
                            `Use the <a href="/products/166751.html">200G QSFP56 AOC</a> for a direct connection between NVIDIA Quantum IB switches.`,
                            `Use the <a href="/products/166751.html">200G QSFP56 AOC</a> for a direct connection between the NVIDIA Quantum IB switch and <a href="/products/168437.html">200G QSFP56 HDR NICs</a>.`,
                        ],
                    },
                    {
                        modelTitle: "QSFP-200G-2QPCxxx",
                        modelContent: [
                            `Use the <a href="/products/167426.html">200G QSFP56 to 2x100G QSFP56 DAC</a> for a direct connection between NVIDIA Quantum IB switches.`,
                            `Use the <a href="/products/167426.html">200G QSFP56 to 2x100G QSFP56 DAC</a> to establish a connection between the NVIDIA Quantum IB switch and <a href="/products/168436.html">100G Dual-Port QSFP56 HDR100 NICs</a>.`,
                        ],
                    },
                    {
                        modelTitle: "QSFP-200G-2QACxxx",
                        modelContent: [
                            `Use the <a href="/products/166186.html">200G QSFP56 to 2x100G QSFP56 ACC</a> for a direct connection between NVIDIA Quantum IB switches.`,
                            `Use the <a href="/products/166186.html">200G QSFP56 to 2x100G QSFP56 ACC</a> to establish a connection between the NVIDIA Quantum IB switch and <a href="/products/168436.html">100G Dual-Port QSFP56 HDR100 NICs</a>.`,
                        ],
                    },
                    {
                        modelTitle: "QSFP-200G-2QAOxxx",
                        modelContent: [
                            `Use the <a href="/products/166755.html">200G QSFP56 to 2x100G QSFP56 AOC</a> for a direct connection between NVIDIA Quantum IB switches.`,
                            `Use the <a href="/products/166755.html">200G QSFP56 to 2x100G QSFP56 AOC</a>  to establish a connection between the NVIDIA Quantum IB switch and <a href="/products/168436.html">100G Dual-Port QSFP56 HDR100 NICs</a>.`,
                        ],
                    },
                    {
                        modelTitle: "QSFP-2Q200G-2QAOxxx",
                        modelContent: [`Use the <a href="/products/205367.html">2x200G QSFP56 to 2x200G QSFP56 AOC</a> for a direct connection between NVIDIA Quantum IB switches.`],
                    },
                ],
            },
        ],
    },
    products: {
        title: "Product List",
        category: [
            {
                title: "InfiniBand Transceivers",
                headerArr: ["Category", "Configuration", "Form Factor", "Wavelength", "Reach", "Connector", "Price"],
                list: [
                    ["OSFP-SR8-800G", "8x100G-PAM4 Electrical to Dual 4x100G-PAM4 Optical Parallel", "OSFP Finned Top", "850nm", "50m", "Dual MPO-12/APC", "US$799.00"],
                    ["OSFP-SR8-800G-FL", "8x100G-PAM4 Electrical to Dual 4x100G-PAM4 Optical Parallel", "OSFP Flat Top", "850nm", "50m", "Dual MPO-12/APC", "US$999.00"],
                    ["OSFP-DR8-800G", "8x100G-PAM4 Electrical to Dual 4x100G-PAM4 Optical Parallel", "OSFP Finned Top", "1310nm", "500m", "Dual MPO-12/APC", "US$1,199.00"],
                    ["OSFP-2FR4-800G", "8x100G-PAM4 Electrical to Dual 4x100G-PAM4 Optical Multiplexed", "OSFP Finned Top", "1310nm", "2km", "Duplex LC/UPC", "US$1,499.00"],
                    ["OSFP-SR4-400G-FL", "4x100G-PAM4 Electrical to 4x100G-PAM4 Optical Parallel", "OSFP Flat Top", "850nm", "50m", "MPO-12/APC", "US$699.00"],
                    ["OSFP-DR4-400G-FL", "4x 100G-PAM4 Electrical to 4x 100G-PAM4 Optical Parallel", "OSFP Flat Top", "1310nm", "500m", "MPO-12/APC", "US$899.00"],
                    ["QSFP-SR4-200G", "4x 50G-PAM4 Electrical to 4x 50G-PAM4 Optical Parallel", "QSFP56", "850nm", "100m", "MPO-12/UPC", "US$239.00"],
                ],
            },
            {
                title: "InfiniBand DAC Cables",
                headerArr: ["Category", "Configuration", "Form Factor", "Lengths (Meters)", "Cable Jacket", "Price"],
                list: [
                    ["OSFP-800G-PCxxx", "8x 100G-PAM4 to 8x 100G-PAM4", "OSFP Finned Top", "0.5m, 1m, 1.5m", "PVC (OFNR)", "US$129.00-US$169.00"],
                    ["OSFP-800G-2QPCxxx", "8x 100G-PAM4 to 8x 100G-PAM4", "OSFP Finned Top to 2 x QSFP112", "0.5m, 1m, 1.5m, 2m", "PVC (OFNR)", "US$179.00-US$239.00"],
                    ["OSFP-800G-2OFLPCxxx", "8x 100G-PAM4 to Dual 4x 100G-PAM4", "OSFP Finned Top to 2 x OSFP Flat Top", "0.5m, 1m, 1.5m, 2m", "PVC (OFNR)", "US$179.00-US$239.00"],
                    ["OSFP-800G-4QPCxxx", "Dual 4x 50G-PAM4 to Quad 2x 50G-PAM4", "OSFP Finned Top to 4 x QSFP56", "0.5m, 1m, 1.5m, 2m, 2.5m, 3m", "PVC (OFNR)", "US$199.00-US$259.00"],
                    ["OSFP-800G-4OFLPCxxx", "8x 100G-PAM4 to Quad 2x 100G-PAM4", "OSFP Finned Top to 4 x OSFP Flat Top", "0.5m, 1m, 1.5m, 2m", "PVC (OFNR)", "US$279.00-US$369.00"],
                    ["OSFPFL-400G-PCxxx", "4x 100G-PAM4 to 4x 100G-PAM4", "OSFP Flat Top", "0.5m, 1m, 1.5m, 2m", "PVC (OFNR)", "US$119.00-US$179.00"],
                    ["OSFP-400G-2QPCxxx", " Dual 4x 50G-PAM4 to Dual 4x 50G-PAM4", "OSFP Finned Top to 2 x QSFP56", "1m, 1.5m, 2m", "OFNP", "US$109.00-US$139.00"],
                    ["OSFP-400G-4QPCxxx", "Dual 4x 50G-PAM4 to Quad 2x 50G-PAM4", "OSFP Finned Top to 4 x QSFP56", "0.5m, 1m, 1.5m, 2m, 2.5m, 3m", "PVC (OFNR)", "US$129.00-US$219.00"],
                    ["QSFP-200G-PCxxx", "4x 50G-PAM4 to 4x 50G-PAM4", "QSFP56", "0.5m, 1m, 1.5m, 2m", "PVC (OFNR)", "US$64.00-US$79.00"],
                    ["QSFP-200G-ACxxx", "4x 50G-PAM4 to 4x 50G-PAM4", "QSFP56", "3m, 5m, 7m", "PVC (OFNR)", "US$180.00-US$260.00"],
                    ["QSFP-200G-2QPCxxx", "4x 50G-PAM4 to Dual 2x 50G-PAM4", "QSFP56 to 2 x QSFP56", "0.5m, 1m, 1.5m, 2m", "PVC (OFNR)", "US$74.00-US$86.00"],
                    ["QSFP-200G-2QACxxx", "4x 50G-PAM4 to Dual 2x 50G-PAM4", "QSFP56 to 2 x QSFP56", "3m, 5m, 7m", "PVC (OFNR)", "US$200.00-US$270.00"],
                ],
            },
            {
                title: "InfiniBand AOC Cables",
                headerArr: ["Category", "Configuration", "Form Factor", "Lengths (Meters)", "Cable Jacket", "Price"],
                list: [
                    ["OSFP-400G-2QAOxxx", "Dual 4x50G-PAM4 to Dual 4x50G-PAM4", "OSFP Finned Top to 2 x QSFP56", "3m, 5m, 10m, 15m, 20m, 30m", "LSZH", "US$999.00-US$1,134.00"],
                    ["QSFP-200G-AOxxx", "4x 50G-PAM4 to 4x 50G-PAM4", "QSFP56", "1m, 2m, 3m, 5m, 10m, 15m, 20m, 30m, 50m, 100m", "LSZH", "US$529.00-US$727.00"],
                    ["QSFP-200G-2QAOxxx", "4x 50G-PAM4 to Dual 2x 50G-PAM4", "QSFP56 to 2 x QSFP56", "1m, 2m, 3m, 5m, 10m, 15m, 20m, 30m, 50m, 100m", "OFNP", "US$719.00-US$777.00"],
                    ["QSFP-2Q200G-2QAOxxx", "4x 25G-NRZ to 4x 25G-NRZ", "2 x QSFP56 to 2 x QSFP56", "1m, 2m, 3m, 5m, 10m, 15m, 20m, 30m, 50m, 100m", "OFNP", "US$1179.00-US$1499.00"],
                ],
            },
        ],
    },
    resources: {
        title: "Resources",
        list: [
            {
                title: "Feature Solutions",
                linkText: ["PicOS® for H100 InfiniBand Solution", "Boost HPC with FS InfiniBand Transceivers and Cables Solution", "HPC-defined Automated Driving Solution"],
            },
            {
                title: "Case Studies",
                linkText: ["FS Helps an Autonomous Vehicle Startup to Build a Data Center Network"],
            },
            {
                title: "Blogs",
                linkText: ["FS InfiniBand Transceivers and Cables Complete Guide", "Tips on Choosing InfiniBand Products for HPC Computing", "Need for Speed – InfiniBand Network Bandwidth Evolution"],
            },
            {
                title: "Videos",
                linkText: [
                    "OSFP-SR8-800G Optical Performance Test | FS",
                    "OSFP-SR8-800G Infiniband Optical Transceiver Module Overall Introduction | FS",
                    "800G OSFP to 2x OSFP Direct Attach Copper Breakout Cable Overall Introduction | FS",
                ],
            },
        ],
    },
    meta: {
        title: "InfiniBand Transceivers and Cables Connectivity Solution Overview | FS",
        description: "FS provides 40G, 56G, 100G, 200G, 400G, 800G, InfiniBand DAC, AOC cables and optical transceivers, network cards and end-to-end solutions.",
    },
}
