export default {
    netTermsApplication: "Net Terms Application",
    asterisk: "Asterisk(*) indicates required fields",
    netTerms: "Net Terms",
    businessInformation: {
        title: "Business Information",
        tipText:
            "To speed up the auditing process, please provide as much further information as possible, including Company Registration Certificate,Public authority certification/self-declaration documents, Tax exemption, Document, W9 Form, Financial Report.",
        form: {
            companyName: "Company Name*",
            address: "Address*",
            phone: "Phone*",
            city: "City*",
            state: "State/Province",
            country: "Country*",
            zip: "Postcode*",
            year: "Year Started*",
            federal: "Federal ID/Tax ID (for U.S. customers)",
            duns: "DUNS Number",
            vat_number: "VAT Number",
            web: "Website*",
            businessScope: "Business Scope*",
            capital: "Capital",
            employees: "Employees",
            established: "Established",
            annualSales: "Annual Sales",
            businessPartners: "Business Partners",
            tips: "In order to shorten the review period, we would appreciate it if you could submit the following corporate verification documents along with the credit application form. A certified copy of the company registration or certificate of current matters, financial statements, or financial reports.",
        },
    },
    institutionType: {
        title: "Institution Type",
        tips: "Please select the appropriate option below to indicate your institution type. Note that the information required may vary based on your selection.",
        public: "Public Institution",
        company: "Company",
        form: {
            postcode: "Postcode*",
            ABN: "ABN*",
            industryDivision: "Industry Division*",
            estimated: "Estimated Monthly Spend*",
            choose: "Choose an option",
        },
    },
    billingInformation: {
        title: "Billing Information",
        tips: 'We will save the billing address you provided in your <a href="xxxx" target="_blank">Account-Address Book</a> for future use. To meet tax regulations, please provide the company VAT/TAX Number promptly for accuracy.',
        same_as: "Same as business information",
        form: {
            company: "Company Name*",
            address: "Address*",
            phone: "Phone*",
            city: "City*",
            state: "State/Province",
            country: "Country*",
            zip: "Postcode*",
            reason: "The reason for the inconsistency with the business address*",
        },
    },
    applicationType: {
        title: "Application Type",
        netTerm: "Net Term*",
        amount: "Amount*",
        bankReference: {
            title: "Bank Reference*",
            text1: "A Bank Reference Letter or Bank Details are requested.",
            upload: "Upload the Bank Reference file",
            fill: "Fill in the information below",
            form: {
                bankName: "Bank Name*",
                contactName: "Contact Name*",
                address: "Address*",
                phone: "Phone*",
                city: "City*",
                state: "State/Province",
                country: "Country*",
                zip: "Postcode*",
                account: "Account Number*",
            },
        },
        businessReference: {
            title: "Business Reference*",
            text: "Please provide us at least three (3) other companies with whom your business has established credit and from whom you have purchased in the last three (3) months.",
            text1: `If you're a company, sole trader, club, non-government sechool or canteen, you need to complete the "Business Refference"`,
            text2: `If you're a charity, not- for-profit, government body or governiment school, "Business Reference" is unnecessary.`,
            upload: "Upload the trade reference file",
            fill: "Fill in the information below",
            email: "Email*",
        },
        contactInformation: {
            title: "Your Contact Information",
            termsConditions: "Terms and Conditions",
            companys: {
                title1: "Reference #1",
                title2: "Reference #2",
                title3: "Reference #3",
            },
        },
        submitApplication: "Sign and Submit Application",
        from: {
            companyName: "Company Name*",
            address: "Address*",
            phone: "Phone*",
            city: "City*",
            state: "State/Province",
            country: "Country*",
            zipCode: "*Zip Code",
            yearStated: "Year Stated*",
            federalID: "Federal ID/Tax ID (for U.S. customers)",
            dunsNumber: "DUNS Number",
            website: "Website*",
            first_name: "First Name*",
            last_name: "Last Name*",
            title: "Title*",
            businessEmail: "Business Email*",
            phoneNumber: "Phone Number*",
        },
        netList: {
            text1: "Net 15",
            text2: "Net 30",
            text3: "Net 45",
            text4: "Net 60",
        },
        amountList: {
            name1: "USD $",
            name2: "EUR €",
            name3: "GBP £",
            name4: "CAD C$",
            name5: "AUD $",
            name6: "CNY ￥",
            name7: "CHF Fr.",
            name8: "HKD $",
            name9: "JPY ¥",
            name10: "BRL R$",
            name11: "NOK kr.",
            name12: "DKK kr.",
            name13: "SEK Kr.",
            name14: "MXN $",
            name15: "NZD $",
            name16: "SGD S$",
            name17: "RUB ₽",
            name18: "RUB $",
            name19: "MYR RM",
            name20: "THB ฿",
            name21: "PHP ₱",
        },
        attentionList: {
            text1: "1. All Net30 invoices must be paid in full within 30 days of the date of shipping. ",
            text2: `2. If the purchaser (hereby refers to the company listed in the "business information", the same below) has a reason of force majeure and has informed FS in written notice before the due date, the payment can be deferred to 15 calendar days after the due date. `,
            text3: `3. If the purchaser fails to make any payment within 15 calendar days after due date without prejudice to any other right or remedy available to the FS, FS shall limit the purchaser's credit and be entitled to charge purchaser interest at the rate of one percent (1%) of the overdue payment per month until payment in full is made (any partial month will be treated as a full month for the purpose of calculating interest). `,
            text4: `4. After the purchaser receives the goods, if an after-sales issue is caused by FS, the purchaser is allowed to suspend the payment until the purchaser receives the repaired or replaced product. However if the after-sales issue is caused by the purchaser, the purchaser cannot refuse to pay for the order. `,
            text5: `5. If a credit order is to be canceled, please inform your account manager in advance in written notice. FS will review, give you feedback, and instruct you on the cancellation process. If the order cancellation is caused by FS (for example defect or wrong products received), the purchaser is allowed to cancel the order. If the order cancellation is caused by the purchaser, and the purchaser has not formally stated and negotiated with FS in advance, the order cannot be canceled. `,
            text6: `6. By submitting this application, you authorize FS to make inquiries into the banking and business trade references that you have provided. `,
            text7: `7. All information provided by purchaser under this agreement is confidential and proprietary to the purchaser, FS shall not disclose or make publicany information to a third party unless the disclosure, publicity and application ofthe confidential information is approved by the purchaser in writing by certified email. `,
            text8: `8. To the extent that you have any controversies or disputes, please call xxxx or send emails to your account manager in FS.`,
            text9: `9. The purchaser has the right to modify the relevant information through Net Terms Management. The purchaser is responsible for confirming the modified information's authenticity, accuracy, and completeness. Also, the purchaser is responsible for any changes made and is not required to sign another document due to the information change.`,
            text10: `10. The data provided by the purchaser has obtained the necessary authorization from the data subject for disclosure and use, and FS can receive and use it as per usual. The purchaser should avoid FS.com from incurring any claims caused by the data subject.`,
            text11: `6. All information provided by purchaser under this agreement is confidential and proprietary to the purchaser, FS shall not disclose or make publicany information to a third party unless the disclosure, publicity and application ofthe confidential information is approved by the purchaser in writing by certified email. `,
            text12: `7. To the extent that you have any controversies or disputes, please call xxxx or send emails to your account manager in FS.`,
            text13: `8. The purchaser has the right to modify the relevant information through Net Terms Management. The purchaser is responsible for confirming the modified information's authenticity, accuracy, and completeness. Also, the purchaser is responsible for any changes made and is not required to sign another document due to the information change.`,
            text14: `9. The data provided by the purchaser has obtained the necessary authorization from the data subject for disclosure and use, and FS can receive and use it as per usual. The purchaser should avoid FS.com from incurring any claims caused by the data subject.`,
        },
        policys: {
            text1: `I agree to FS's <a href="javascript:;" class="policy">Privacy Policy and Notice at Collection</a> and <a href="javascript:;" class="trems">Terms of Use</a>.`,
            text2: `I am authorized to sign this application on behalf of company/organization/institution listed above.`,
            text3: `I hereby certify that above all information contained is complete and accurate. All information has been furnished with the understanding that it is to be used to determine the amount and conditions of the credit to be extended.`,
        },
    },
    apply: {
        // errtext: "Net Terms Application is only available for Business Accounts. If you have any questions, contact your account manager freely.",
        errtext: "  Net Terms Application is only available for enterprises, organizations and governments. If you have any questions, <a href='xxxx' target='_blank'>contact your account manager freely.</a>",
        errtext2: "Please note that you can only submit Online Net Terms Application once. If failed for the first time, you can fill in the PDF Application Form and contact your account manager.",
        errsubmit: "Please note that you can only submit Offline Net Terms Application once. If failed for the first time, you can fill in the PDF Application Form and contact your account manager.",
    },
    inlineTips: {
        business: `Your company's registered address is automatically filled in here and cannot be edited. If you need to update the address, please <a href="xxxx" target='_blank'>contact your account manager</a> for assistance.`,
        billing: `According to our company's credit management requirements for EU countries/regions, your billing address for Net Terms must match your company'sregistered address.For future orders using Net Terms payment, only this address can be used as the billing address.`,
        billing2: `According to our company's credit management requirements, your Company name and Country for Net Terms must match your company's registered address.`,
        vat_number: `You can exempt from VAT tax if you have a valid Tax Identification Number.`,
    },
    validate: {
        first_name: {
            first_name_required: "Please enter your first name.",
            first_name_max: "First name must be 40 characters maximum.",
            first_entry_name_max: "First name must be 35 characters maximum.",
            first_name_min: "First name must be 2 characters minimum.",
        },
        last_name: {
            last_name_required: "Please enter your last name.",
            last_name_max: "Last name must be 40 characters maximum.",
            last_entry_name_max: "Last name must be 35 characters maximum.",
            last_name_min: "Last name must be 2 characters minimum.",
        },
        email: {
            email_required: "Please enter your email address.",
            email_valid: "Please enter a valid email address.",
            email_validate2: "Please enter a valid email address. (eg:<EMAIL>)",
        },
        phone: {
            phone_required: "This field is required.",
            phone_min: "Your phone number must be at least 6 digits.",
            phone_validate: "Phone must be 40 characters maximum.",
        },
        company: {
            company_required: "This field is required.",
            company_validate: "company name must be 120 characters maximum.",
        },
        address: {
            address_required: "This field is required.",
            address_validate: "Address must be 100 characters maximum.",
            address_validate1: "Address must be between 4 and 35 characters.",
        },
        city: {
            city_required: "This field is required.",
            city_validate: "City must be between 2 and 40 characters long.",
        },
        state: {
            state_required: "This field is required.",
            state_validate: "State/Province must be between 1 and 100 characters long.",
        },
        country: {
            country_required: "Please select the country",
        },
        agreement: {
            agreePolicy: "Please make sure you agree to our Privacy Policy and Notice at Collection and Terms of Use.",
        },
        zip_code: {
            zip_code_required: "This field is required.",
            zip_code_validate: "ZIP Code must contain five digit.",
        },
        yearStated: {
            yearStated_required: "This field is required.",
            yearStated_validate: "Year Stated must be between 1 and 100 characters long.",
        },
        federal: {
            federal_required: "This field is required.",
            federal_validate: "VAT Number must contain nine digits.",
        },
        DUNS: {
            DUNS_required: "This field is required.",
            DUNS_validate: "DUNS Number must contain nine digits.",
        },
        website: {
            website_required: "This field is required.",
            website_validate: "Website must be 1280 characters maximum.",
        },
        reason: {
            reason_required: "This field is required.",
            reason_validate: "reason must be between 1 and 200 characters long.",
        },
        amount: {
            amount_required: "The amount is required",
            amount_validate: "amount must be between 1 and 200 characters long.",
        },
        file: {
            file_required: "this field is required.",
        },
        title: {
            title_require: "This field is required.",
            title_validate: "Title must be 100 characters maximum.",
        },
        bankName: {
            bankName_required: "This field is required.",
            bankName_validate: "Bank Name must be 100 characters maximum.",
        },
        contactName: {
            contactName_required: "This field is required.",
            contactName_validate: "Contact Name must be 100 characters maximum.",
        },
        accountNumber: {
            accountNumber_required: "This field is required.",
            contactName_validate: "Account Number must be 100 characters maximum.",
        },
        policys: {
            agreePolicy: "Please make sure you agree to our Privacy Policy and Notice at Collection and Terms of Use.",
            policys_required: "This field is required.",
        },
        abn: {
            validate: "ABN must contain eleven digit.",
        },
        payMumber: {
            require: "The amount is required",
        },
    },
    noSelect: "Not applicable for this country",
    errorTsips: `Sorry, we're experiencing a delay in generating the signature file. Please try again later.`,
    titleList: {
        choose: "Choose an option",
        management: "Management",
        procurement: "Procurement",
        sales: "Sales",
        administration: "Administration",
        accounting: "Accounting",
        engineering: "Engineering",
        logisticsandstorage: "Logisticsandstorage",
        marketing: "Marketing",
        operation: "Operation",
        owner: "Owner",
        other: "Other",
        inpPlaceholder: "Search your title",
    },
    headOffice: {
        location: "Head Office Location",
        postalCode: "Postal Code*",
        PostalSearch: "Postal Search",
        stateProvince: "State/Province*",
        city: "City*",
        address: "Address*",
        phoneNumber: "Phone Number*",
    },
    officeLocation: {
        office: "Office Location",
        deliveryAddress: "Delivery address",
        sameLocation: "Same as the head office location",
        sameOffice: "Same as the office location",
    },
    billingAddress: {
        bill: "Billing Address",
        payeeBank: "Payee Bank",
        bankName: "Bank Name",
        bankCode: "Bank Code",
        branchName: "Branch Name",
        branchCode: "Branch Code",
        typeDeposit: "Type of Deposit",
        ordinaryDeposit: "Ordinary Deposit",
        checkingAccount: "Checking account",
        accountNo: "Account No.",
        accountName: "Account Name",
        accountKatakana: "Account Name(Katakana)",
    },
    paymentTerms: {
        title: "Payment Terms",
        tips1: "Payment for products should be made at the end of each month (bank transfer, paypal, credit card).",
        NetTerm: "Net Term*",
        content1: "month-end closing, next month-end payment",
        amount: "Amount*",
        tips2: "If you would like to request payment terms other than those listed above, please fill out the form below or contact our dedicated account manager.",
    },
    contactInformation: {
        title: "Contact Information",
        tips: "We will use the information you provide if we need to contact you regarding your credit application.",
        lastName: "Last Name*.",
        firstName: "First Name*.",
        jobTitle: "Title*.",
        businessEmail: "Business Email*.",
        phoneNumber: "Phone Number*",
    },
    applyInformation: {
        title: "Apply Information",
        organizationName: "Organization Name",
        des: "Description of how you will use the API(s)",
    },
    accountsPayable: {
        title: "Accounts Payable Contact Information",
        tips: `After your business account's net terms is approved, this will be usedif we need to contact you about your account.`,
        isSame: "Same as my Contact Information",
        lastName: "Last Name*.",
        firstName: "First Name*.",
        jobTitle: "Title*.",
        businessEmail: "Business Email*.",
        phoneNumber: "Phone Number*",
    },
    remarks: {
        remarks: "Remarks",
        content: `Please submit one of the following documents to prove your identity: business card, employee ID, proof of employment, etc.`,
        list: "In order to shorten the review period, we would appreciate it if you could submit the following corporate verification documents along with the credit application form. A certified copy of the company registration or certificate of current matters, financial statements, or financial reports.",
        plan: "Annual purchase plan",
    },
    termsConditions: "Terms and Conditions",
    unsameResaon: "The reason for the inconsistency with the head office location or office location: ",
    employeeList: {
        option0: "Public institutions (e.g., education, healthcare, government offices, etc.)",
        option1: "More than 1000 employees",
        option2: "200~1000 employees",
        option3: "50 ~ 200 employees",
        option4: "Less than 50 employees",
    },
    upload: `Upload File`,
    placeholders: {
        eg: "e.g., Manufacturing",
        eg1: "e.g., Manufacturing",
        eg2: "e.g., 10 million yen",
        eg3: "Please select",
        eg4: "e.g., 2020",
        eg5: "e.g., 100 million yen",
        eg6: "e.g., Bank of Mitsubishi UFJ",
        eg7: "e.g., 0005",
        eg8: "e.g., Tokyo Branch",
        eg9: "e.g., 123",
        eg10: "e.g.,********",
        eg11: "e.g., Taro Yamada",
        eg12: "e.g., Yamada Taro",
    },
    pdfFile: {
        title: "Confidential Credit Application Form",
    },
    accountPay: {
        title: "Accounts Payable Contact Information",
        tips: `After your business account's net terms is approved, this will be used if we need to contact you about your account.`,
        isSame: "Same as my contact information",
        form: {
            firstName: "First Name*",
            lastName: "Last Name*",
            title: "Title*",
            email: "Business Email*",
            phone: "Phone Number*",
        },
    },
    success: {
        title: "We have received your net terms application",
        content: 'We will contact you soon. You can track your application progress in <a href="xxxx">cases</a>.',
    },
    jpPolicy: {
        text1: "1. The information provided above will be used for credit assessment purposes.  A credit limit will be established based on the results of the review and your annual purchase plan.",
        text2: '2. If the purchaser (hereby refers to the company listed in the "business information", the same below) has a reason of force majeure and has informed FS in the written notice before the due date, the payment can be deferred to 15 calendar days after the due date.',
        text3: `3. If the purchaser fails to make any payment within 15 calendar days after the due date without prejudice to any other right or remedy available to the FS, FS shall limit the purchaser's credit and be entitled to charge the purchaser interest at the rate of one percent (1%) of the overdue payment per month until payment in full is made (any partial month will be treated as a full month to calculate interest).`,
        text4: "4. After the purchaser receives the goods, if an after-sales issue is caused by FS, the purchaser is allowed to suspend the payment until the purchaser receives the repaired or replaced product. However, if the after-sales issue is caused by the purchaser, the purchaser cannot refuse to pay for the order.",
        text5: "5. If a credit order is to be canceled, please inform your account manager in advance of the written notice. FS will review, give you feedback, and instruct you on the cancellation process. If the order cancellation is caused by FS (for example defect or wrong products received), the purchaser is allowed to cancel the order. If the order cancellation is caused by the purchaser, and the purchaser has not formally stated and negotiated with FS in advance, the order cannot be canceled.",
        text6: "6. All information provided by the purchaser under this agreement is confidential and proprietary to the purchaser. FS shall not disclose or make public any information to a third party unless the disclosure, publicity and application of the confidential information have been approved in writing by the purchaser.",
        text7: "7. To the extent that you have any controversies or disputes, please call xxxx  or send emails to your account manager in FS.",
        text8: `8. The purchaser has the right to modify the relevant information through Net Terms Management. The purchaser is responsible for confirming the modified information's authenticity, accuracy, and completeness. Also, the purchaser is responsible for any changes made and is not required to sign another document due to the information change.`,
        text9: `9. The data provided by the purchaser has obtained the necessary authorization from the data subject for disclosure and use, and FS can receive and use it as per usual. The purchaser should avoid FS.com from incurring any claims caused by the data subject.`,
    },
    topSwitch: {
        basic: `Basic Information`,
        payment: `Payment Information`,
        contact: `Contact Information`,
    },
}
