export default {
    community: {
        title: "Community",
        more: "View more",
    },
    cookie: {
        txt01: `We use cookies to ensure that we give you the best experience on our website. By continuing to use this site you agree to our use of cookies in accordance with our <a href="policies/privacy_policy.html">Cookie Policy</a>.`,
        txt02: `We use cookies to ensure that we give you the best experience on our website. By clicking on "Accept" or continuing to use this site, you agree to our use of cookies in accordance with our <a href="policies/privacy_policy.html">Cookie Policy</a>. You can refuse the use of cookies <a class="disAgreeGoogle" href="javascript:;">here</a>.`,
        accept: "Accept",
        declineAll: "Decline",
        txt03: `Welcome to <a href="/" target="_blank">FS.COM</a>. To offer you a better experience, we use cookies to enable some features of the website. Cookies help us see what interests you most about FS; allow you to easily share articles via social media; and offer many other advantages for the website. For further information please read our <a href='policies/privacy_policy.html'>Privacy Policy and Notice at Collection</a>.`,
        text04: `Manage Setting`,
        tips: {
            title1: "Cookie Preference Center",
            p1: `When you visit any website, it may store or retrieve information on your browser, mostly in the form of cookies. This information might be about you, your preferences or your device and is mostly used to make the site work as you expect it to. The information does not usually directly identify you, but it can give you a more personalized web experience. Because we respect your right to privacy, you can choose not to allow some types of cookies. Click on the different category headings to find out more and change our default settings. However, blocking some types of cookies may impact your experience of the site and the services we are able to offer.`,
            p2: `You can accept or decline all but Strictly Necessary Cookies, or customize your cookie settings below. You can change your cookie settings at any time.  To learn more about how FS processes personal data, please visit our <a href="policies/privacy_policy.html">cookie policy</a>.`,
            title2: `Manage Consent Preferences`,
            p3: `These cookies may be set through our site by our advertising partners. They may be used by those companies to build a profile of your interests and show you relevant ads on otther sites. They do not store directly personal information, but are based on uniquely identifying your browser and internet device. If you do not allow these cookies, you will experience less targeted advertising.`,
            p4: `These cookies are necessary to enable the basic features of this site to function, such as providing secure log in or remembering how far you are through an order, the cookies that are strictly necessary for us to provide information society services that you request. These cookies do not store any personally identifiable information. `,
            btn: {
                allow: "Allow All",
                reject: "",
                confirm: "Confirm My Choice",
            },
        },
        cookiesType: {
            strictly: "Strictly Necessary Cookies",
            mark: "Marketing Cookies",
        },
        alwaysActive: "Always Active",
        cookieSetting: "Cookie Settings",
    },
    list_span: "Learn more",
    pica: {
        title: "FS and Pica8 Partner to Empower Open Networking",
        content: "1G to 400G PicOS® License | AmpCon™ | PicOS-V",
        button_text: "Learn more",
        m_img: `https://resource.fs.com/mall/generalImg/20231020151127ee9u8a.jpg`,
    },
    cookieTipNew: {
        txt01: `By clicking "Accept All Cookies", you agree to the storing of cookies on your device to enhance site navigation, analyse site usage, and assist in our marketing efforts. <a href="XXXX">Cookie Policy</a>`,
        cookieSetting: `Cookie Settings`,
        acceptCookies: `Accept All Cookies`,
        rejectAll: `Reject All`,
    },
    cookieTipPop: {
        title: `Privacy Preference Center`,
        desc01: `When you visit any website, it may store or retrieve information on your browser, mostly in the form of cookies. This information might be about you, your preferences or your device and is mostly used to make the site work as yoou expect it to. The information does not usually directly identify you, biut it can give you a more personalized web experience. Because we respect your right to  privacy, you can choose not to allow some types of cookies. Click on the different category headings to find out more and change our default settings. However, blocking some types of cookies may impact your experience of the site and the services we are able to offer.`,
        desc02: `More information`,
        manage: `Manage Consent Preferences`,
        option1: `Strictly Necessary Cookies`,
        content1: `These cookies are necessary to enable the basic features of this site to function, such as providing secure login or remembering how far you are through an order, the cookies that are strictly necessary for us to provide information society services that you request. These cookies do not store any personally identifiable information.`,
        btn01: `Always Active`,
        option2: `Performance Cookies`,
        content2: `These cookies allow us to count visits and traffic sources so we can measure and improve the performance of our site. They help us to know which pages are the most and least popular and see how visitors move around the site. All information these cookies collect is aggregated and therefore anonymous. If you do not allow these cookies we will not know when you have visited our site, and will not be able to monitor its performance.`,
        btn02: `Allow All`,
        btn03: `Reject All`,
        btn04: `Confirm My Choice`,
    },
}
