export default {
    head: {
        title: "Product Return Form - FS",
        description: "Please complete this form so we can begin processing your return (RMA). If you have questions, please read our return policy or contact us.",
    },
    title: "Product Return Form",
    tip: "Please complete the form to submit return request",
    orderInformation: "Order Information",
    orderNumber: "Order Number",
    selectServiceType: "Select service type",
    serviceType: "Service Type",
    ID: "ID*",
    QTY: "QTY*",
    productID: "Product ID & Quantities returned",
    reason: "Reason(s) for return",
    uploadholder: "Briefly describe the problem so that your request can be handled fast.",
    uploadTip: "Please use a PDF, JPG, PNG, DOC, DOCX, XLS, XLSX or TXT file. Maximum size 5M.",
    uploadTit: "Your request has been submitted successfully.",
    contactDetails: "Contact Details",
    firstName: "First Name",
    lastName: "Last Name",
    country: "Country/Region",
    emailAddress: "Email Address",
    phoneNumber: "Phone Number",
    zipCode: "Postal Code",
    apt: "Apt, Suite, Floor, etc.",
    stateHolder: "please select states",
    agreement: `I agree to FS's <a href="xxxx " target="_blank" style="color:#19191a">Privacy Policy and Notice at Collection</a> and <a href="yyyy" target="_blank" style="color:#19191a">Terms of Use</a> .`,
    privacyPolicy: "Privacy Policy and Notice at Collection",
    termsUse: "Terms of Use",
    submit: "Submit",
    successTipTit: "Your request has been submitted successfully.",
    successTipTxt: "FS After-Sale Center approved this RMA request. Please contact your account manager to get the RMA number and follow the instructions to process it.",
    returnRefund: "Return&Refund",
    replacement: "Replacement",
    maintenance: "Maintenance",
    uploadFile: "Upload file",
    addSeriesNumber: {
        save: "Save",
        tit: "Add series Number ",
        tip1: " Please fill in the serial number of the optic module so that we can quickly find the module that you need an after-sale service.",
        tip2: `Different serial numbers can be seperated by "/".`,
    },
    first_name: {
        first_name_required: "Please enter your first name.",
        first_name_max: "First name must be 40 characters maximum.",
        first_entry_name_max: "First name must be 35 characters maximum.",
        first_name_min: "First name must be 2 characters minimum.",
    },
    last_name: {
        last_name_required: "Please enter your last name.",
        last_name_max: "Last name must be 40 characters maximum.",
        last_entry_name_max: "Last name must be 35 characters maximum.",
        last_name_min: "Last name must be 2 characters minimum.",
    },
    email: {
        email_required: "Please enter your email address.",
        email_validate: "Please enter a valid email address. (eg:<EMAIL>)",
        email_validate2: "Please enter a valid email address. ",
        email_exist: "Account already exists. Click here to",
        email_exist_sign_in: "sign in.",
        email_re_enter: "Please re-enter your email address.",
        email_match: "New email address must match.",
        email_validate_qq: "Invalid email address, please check it and try again.",
    },
    phone: {
        phone_required: "Please enter your phone number.",
        phone_min: "Your phone number must be at least 6 digits.",
    },
    address: {
        tit: "Address Line 1",
        address_required: "Your Address is required.",
        address_validate: "Address line 1 must be between 4 and 35 characters long.",
    },
    address2: {
        tit: "Address Line 2",
        address2_required: "Your Address2 is required.",
        address2_validate: "Address line 2 should contain a maximum of 35 characters.",
    },
    city: {
        tit: "City",
        city_required: "Your City is required.",
    },
    state: {
        tit: "State/Province/Region",
        tip: "please select states",
        state_required: "Your State/Province/Region is required.",
    },
    zip_code: {
        zip_code_required: "Your postcode is required.",
        zip_code_validate: "Your ZIP/postal code should be at least 3 characters long.",
    },

    agreement_agree: "Please make sure you agree to our Privacy Policy and Notice at Collection and Terms of Use.",

    order_number: {
        order_number_required: "The order number can't be empty.",
        order_number_eg: "Please enter a valid order number. eg:FS180808001234",
    },
    service_type: {
        service_type_required: "Please select Service Type.",
    },
    products_id: {
        products_id_required: "Please enter product ID.",
        products_id_required1: "Please enter the correct product number.",
    },
    products_num: {
        products_num_required: "Please enter product QTY.",
    },
    review_content: {
        review_content_required: "please explain your reason(s) for return.",
    },
    tickets_service_type: {
        tickets_service_type_required: "Please choose the service type.",
    },
    state_check: {
        state_check_required: "Please select the state/territory in which you hold your tax exemption.",
    },
    // 5.23新增
    AdditionalComments: "Additional Comments",
    ServiceTypeList: [
        "Product Function/Usage Issues",
        "Package Damaged",
        "Package Didn't Arrive",
        "Missing Items or Parts",
        "No Longer Need",
        "Order the Wrong Products",
        "Receive Wrong Items",
        "Need Technical Documentation",
        "Product Optimization Suggestions",
        "Spare Part Request",
        "Other Issues",
    ],
    supportType: "Case Type",
    supportTypeList: ["Request RMA", "Request Tech Support"],
    timeOfReceipt: "Time of Receipt",
    DeviceVersion: "Device Version",
    SN: "S/N",
    FaultTime: "Fault Time",
    linkEnd: "Link End",
    siteA: "Site A",
    siteB: "Site B",
    DeviceBoardModel: "Device/Board Model",
    please_choose_time: "Please choose a right time.",
    please_provide_more_details: "Please provide more details about your spare part request",
    please_provide_a_statement: "Please provide a statement that POD is not signed by yourself and a declaration instead of an oath",
    please_provide_the_picture: "Please provide the picture or video of unpacking",
    support_type: {
        support_type_required: "Please select Case Type.",
    },
    enter_correct_product: "Please enter the correct product ID.",
    device_version_tips: "Device version can be separated by “, . / ”.",
    sn_tips: "Faulty product S/N numbers can be separated by “, . / ”.",
    fault_time_tips: "If there are multiple product failures, please fill in the earliest fault time.",
    description_original: "Please describe the problems encountered during the use of the product.",
    please_describle_your_question: "Please describe your questions so that we can handle your request quicker.",
    your_tech_support: "Your Tech Support was approved.",
    we_have_received: "We have received your request, our tech guys will check it ASAP,please contact your account manager to get solved plan.",
    declaration: "Declaration instead of an oath",
    read_and_understood: "I agree I have read and understood <a href='javacript:;' onClick='sparePopup()'>XXXXX</a>",
    spare_part_request_agreement: "Spare Part Request Agreement. ",
    spare_agree: "Please make sure you agree to our Spare Part Request Agreement.",
    sparePopup: [
        {
            tit: "Spare Parts Service Description: ",
            txt: "This is a service that entitles a customer to receive advance replacement of hardware after FS deems a spare part is necessary and a Return Material Authorization (RMA) number is generated. The replacement equipment may be new or equivalent to new in performance and reliability.",
        },
        {
            tit: "Customer Responsibilities:",
            txt: "To enable FS to provide the best possible support and service, the customer will be required to return spare parts to FS within 15 days upon receipt of the replaced or repaired product, and will be responsible for parts damaged or lost during transit. In the event the equipment is not returned within this period or the returned equipment does not meet FS acceptance criteria, FS reserves the right to charge you the list price of the spare parts provided at that time.",
        },
    ],
    agree_title: `Spare Part Request Agreement`,
    left_content: {
        tit02: "After Sales Service",
        title: "After Sales Service",
        part1: {
            title: "Request Tech Support",
            des: "You can submit a form for a problem with the product, and your account manager and technical experts will locate the problem and propose a solution quickly.",
        },
        part2: {
            title: "Request RMA",
            des: "You can apply for return, replace or repair directly.",
        },
    },
    Confirmation: "Confirmation",
    content: "Delete Returned Product?",
    Cancel: "Cancel",
    Delete: "Delete",
}
