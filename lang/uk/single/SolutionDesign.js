export default {
    second_title: "What is your specific application scenario?",
    banner: ["What's your solution application scenario?", "What are your solution requirements?", "Tell us a little about yourself", "Solution Design"],
    crumbList: ["Home", "FS Solution", "Request a Consult"],
    left: {
        title: "Tailored to Your Unique Business Needs",
        desc: [
            {
                title: "Free Technical Support",
                txt: "Provide free & tailored solutions and services for your businesses",
            },
            {
                title: "Professional Lab",
                txt: "Test each product with original brand devices",
            },
            {
                title: "80% Same-day Shipping",
                txt: "Immediate shipping for in-stock items",
            },
            {
                title: "Fast Response",
                txt: "Direct and immediate assistance from an expert",
            },
        ],
    },
    right: {
        fill_in_the_form: "Please fill in the form below. Our Experts will contact you quickly.",
        already_have_an_account: "Already have an account? xxxx or yyyyy",
        sign_in: "Sign in",
        create_an_account: "Create an account",
        Company: "Company",
        job_title: "Job title",
        job_select: [
            {
                value: "Technical staff",
                option: "Technical staff",
            },
            {
                value: "Procurement staff",
                option: "Procurement staff",
            },
            {
                value: "Other",
                option: "Other",
            },
        ],
        industry_select: [
            {
                value: "Internet",
                option: "Internet",
            },
            {
                value: "IDC & EDC",
                option: "IDC & EDC",
            },
            {
                value: "Education",
                option: "Education",
            },
            {
                value: "small business",
                option: "small business",
            },
            {
                value: "Retail & Hospitality",
                option: "Retail & Hospitality",
            },
            {
                value: "Manufacturing",
                option: "Manufacturing",
            },
            {
                value: "Government",
                option: "Government",
            },
            {
                value: "Media&Entertainment",
                option: "Media&Entertainment",
            },
            {
                value: "Other",
                option: "Other",
            },
        ],
        brief_project_description: "Brief Project Description",
        brief_placeholder: "Please descripe the project details, involved project background, budget, traffic demands,etc. Your comments will help FS respond more quickly.",
        file_tip: "Please use a PDF, JPG, PNG, DOC, DOCX, XLS, XLSX or TXT file. Maximum size 5M.",
        agree_txt: "I agree to FS's xxxxx and yyyyy",
        privacy_policy: "Privacy Policy and Notice at Collection",
        terms_of_use: "Terms of Use",
        errors: {
            company_error: "Please enter your company name.",
            description_error: "Please enter your brief project description.",
            country_code_error: "Please enter country/region",
            industry_error: "Please select industry.",
            industry_error01: "Please enter your industry.",
            job_title_error: "Please select job title.",
        },
        success: {
            your_request: "Your request ",
            has_been_submitted_successfully: " has been submitted successfully.",
            we_will_reply_you: "We will reply you within 1-3 hours during working days. You can also discuss the case with our team online, and trace updates in xxxxx with FS account.",
            support_tickets: `“Support Tickets”`,
        },
    },
    public: {
        one: [
            `Solution Design`,
            `Design, deploy, and verify with the best experts from FS.com approach to help you successfully.`,
            `Solution Select`,
            `Basic information`,
            `Submit / Wait for technical reply`,
            `Next`,
            "back",
        ],
        two: [`Tell us a little about yourself.`, `Enter your first name`, `Enter your last name`, `Business email`, `Enter your business email`, `Company name`, `Enter your company name`, `Submit`],
        three: [
            `Submitted Successfully`,
            `We will reply you within 6-12 hours during business days. You can also discuss the case with our team online, and trace updates in "<a href=XXXX >Cases</a>" with FS account.`,
            // `We will reply you within 1-3 hours during working days.`,
            `Return to Homepage`,
        ],
        type: [
            `1. Please select your solution type`,
            `Please select`,
            `OTN Solution`,
            `Campus Network Solution / Wired`,
            `Campus Network Solution / Wireless`,
            `Internet Data Center`,
            `Enterprise Intelligence Solutions / Surveillance Network`,
            `Enterprise Intelligence Solutions / Video Conference`,
            `Enterprise Intelligence Solutions / VoIP`,
            `PON Solution`,
            `Integrated Cabling Solutions / Indoor`,
            `Integrated Cabling Solutions / Outdoor`,
        ],
        type_new: [
            `Campus Network Solution`,
            `Access Network`,
            `Enterprise WLAN`,
            `Enterprise Intelligence Solutions`,
            `Surveillance System`,
            `Video Conference`,
            `VoIP`,
            `Data Center Cabling`,
            `Indoor`,
            `Outdoor`,
            `HPC Networking`,
            `InfiniBand Networking`,
            `RoCE Networking`,
            `Enterprise LAN`,
            `Optical Networking`,
        ],
        type_new_title: [
            "What are your InfiniBand Networking Solution requirements?",
            "What are your RoCE Networking Solution requirements?",
            "What are your Internet Data Center Solution requirements?",
            "What are your Access Network Solution requirements?",
            "What are your Video Conference Solution requirements?",
            "What are your VoIP Solution requirements?",
            "What are your Enterprise WLAN Solution requirements?",
            "What are your Surveillance System Solution requirements?",
            "What are your OTN Solution requirements?",
            "What are your PON Solution requirements?",
            "What are your Indoor Solution requirements?",
            "What are your Outdoor Solution requirements?",
        ],
        tips: [`Please select at least one.`, `Other`, `You can add other notes here`, `Other requirements`, `Enter more details`, `Please enter the detail.`],
        upload: [
            `. Please fill in your specific verification requirements`,
            `FS will evaluate your specific requirements for verifying the solution to prove whether your specific verification could be achieved.`,
            `FS could offer product test reports, and scenario solution verification, please fill in your specific verification requirements if any questions.`,
            `Upload file`,
            `Please upload a network topology diagram. Allow files of type PDF, JPG, PNG. Maximum size 5M.`,
            `Please upload your floor plan. Allow files of type JPEG, PNG, PDF, CAD. Maximum size 5M.`,
            `Upload DC Layout. Allow files of type RAR，ZIP，DOC，DOCX，PDF, JPG, PNG...Maximum size 5M.`,
            `Upload the points map. Allow files of type RAR，ZIP，DOC，DOCX，PDF, JPG, PNG...Maximum size 5M.`,
        ],
        add: [`Spans in the whole link`, `Add another`, `Delete`, `Confirmation`, `Delete this spans？`, `Cancel`, `Delete this information？`, `Delete this channels？`],
        error: `Form submission failed`,
    },
    OTN_Solution: {
        title: [
            `1. What's the fiber type in your network connection?   Can you tell if fiber is single fiber or dual fiber?`,
            `2. How many spans are in the whole link? And what's the business plan between them?`,
            `3. About the channel requirements`,
            `4. About the comaptible transceivers`,
            `5. Do you have additional requirements like Network protection, Monitoring, and other customized demands?`,
        ],
        option: [
            `Fiber Type`,
            `Number of fiber`,
            `Spans in the whole link`,
            `How many channels required now?`,
            `How many channels required in the future?`,
            `Channel`,
            `Is the use of Compatible Transceivers allowed?`,
            `Additional requirements`,
            `Other customized demands`,
        ],
        value: [
            `G.652`,
            `G.655 `,
            `Single fiber`,
            `Dual fiber`,
            `Initial point`,
            `Termination point`,
            `Business Capacity`,
            `Distance`,
            `Loss@1310nm or Loss@1550nm（Please enter at least one item）`,
            `Channel #`,
            `Protocol (e.g. 16/8/4G FC, 100/40/10GE)`,
            `Switch vendor (make and model)`,
            `Switch model Firmware version`,
            `Optical Line protection`,
            `1+1 hot-backup protection`,
            `Optical Performance Monitoring`,
        ],
    },
    Wired_Solution: {
        title: [
            `1. Could you please provide us the type and number of terminals? `,
            `2. Could you please describe your hardware needs in detail`,
            `3. How about the functional requirements of software?`,
            `4. Leave a message for other project descriptions in detail `,
        ],
        option: [`Terminal Type`, `Number of Terminal`, `Airflow direction`, `Transmission medium`, `Transmission rate`, `Quantity`, `Model number`, `Function`],
        value: [
            `Computer`,
            `Camera`,
            `Printer`,
            `Server`,
            `IP phone`,
            `Enter the number of terminal`,
            `Front to back`,
            `Back to front`,
            `Twisted pair`,
            `Fiber`,
            `1G`,
            `10G`,
            `25G`,
            `40G`,
            `100G`,
            `Enter the model number`,
            `Stacking`,
            `M-Lag`,
            `Security features`,
            `We are capable of working out a professional solution to maximize the utilization of the equipment and link resources also to save costs and improve business reliability.`,
        ],
    },
    Data_Center: {
        title: [
            `1. Please offer us your Data center type, data center interconnection requirements`,
            `2. Please offer us your bandwidth information, and functional requirements`,
            `3. Leave a message for other project descriptions in detail `,
        ],
        option: [`Scenario`, `Access bandwidth`, `Functional requirements`, `Server requirements`, `Airflow direction`, `Transmission medium`, `Number of servers`],
        value: [
            `Internet data center`,
            `Cloud computing data center`,
            `Small office data center`,
            `Multi-data center interconnection`,
            `Data center storage network`,
            `10G`,
            `40G`,
            `100G`,
            `400G`,
            `Vlan`,
            `evpn`,
            `Roce`,
            `M-LAG`,
            `OpenFlow`,
            `Including memory size, CPU specification, hard disk size, etc.`,
            `Front to back`,
            `Back to front`,
            `Twisted pair`,
            `Fiber`,
            `We are capable of working out a professional solution to maximize the utilization of the equipment and link resources also to save costs and improve business reliability.`,
        ],
    },
    Wireless_Solution: {
        title: [
            `1. What is your wireless network application scenario?`,
            `2. What is your floor plan and coverage area plan?`,
            `3. What radio protocol will be used?  How many spatial streams (MIMO) are needed?`,
            `4. Any other specifications are required for the access point?`,
            `5. How would you like to the network？`,
            `6. How are the access points being powered?`,
            `7. Do you have any other requirements for this wireless network solution?（Optional）`,
        ],
        option: [
            `Application Environment`,
            `Coverage area`,
            `Coverage area plan`,
            `Device Applications`,
            `Please upload your floor plan`,
            `Radio protocol type`,
            `Spatial streams (MIMO)`,
            `Other specifications`,
            `Installation`,
            `Manage method`,
            `Power Supply`,
            `Other Information `,
        ],
        value: [
            `Office`,
            `Villa`,
            `Warehouse`,
            `Education`,
            `Hotels`,
            `Hospitals`,
            `Campus Network`,
            `Indoor`,
            `Outdoor`,
            `Coverage Area`,
            `Number of Floors`,
            `Number of Rooms/Areas per Floor`,
            `Client Devices per Room/Area`,
            `Wall Thickness and Material btw Rooms/Area`,
            `Height of ceilings`,
            `Email`,
            `Text`,
            `Voice`,
            `Video`,
            `Live Streaming`,
            `802.11B`,
            `802.11G`,
            `802.11N`,
            `ac(Wi-Fi5）`,
            `ax(Wi-Fi6)`,
            `2x2 MIMO: 2 Spatial Streams`,
            `3x3 MIMO: 3 Spatial Streams`,
            `4x4 MIMO: 4 Spatial Streams`,
            `Max Throughput`,
            `Dual/Tri Bands`,
            `Water/Dust Protection Ratings`,
            `Ceiling`,
            `Wall`,
            `Panel`,
            `Pole`,
            `Public Cloud`,
            `Self-deployable software`,
            `Hardware Controller`,
            `Standalone mode for each Access Point`,
            `Hybrid (please specify)`,
            `PoE from Switches / Gateways`,
            `PoE Injector`,
            `AC/DC Adapter`,
            `Do you have any existing wlan, switches, gateways, firewalls, routers, NAC, NMS, or IoT Controllers that requires the access point be compatible with? Any other customized requirements?`,
        ],
    },
    VoIP_Solution: {
        title: [`1. How many users in the whole demands? What is the number of outside lines?`, `2. What's the main VoIP  solution to be consulted?`, `3. Leave a message for other project descriptions in detail `],
        option: [`Number of access users`, `VoIP  access type`, `VoIP  line access to`, `Consultation type`],
        value: [
            `Enter the number of access users`,
            `Ordinary voice gateway access`,
            `Router access`,
            `IP Phone`,
            `E1/T1 trunk line`,
            `PSTN analog outside line`,
            `IVR`,
            `Voice conferencing`,
            `Call recording`,
            `We are capable of working out a professional solution to maximize the utilization of the equipment and link resources also to save costs and improve business reliability.`,
        ],
    },
    Video_Solution: {
        title: [
            `1. How many meeting rooms are there? And how many users/ in each meeting rooms?`,
            `2. How would you prefer to install your video conferencing equipment?`,
            `3. Do you have any upgrade plans to expand network capacity for future business? `,
        ],
        option: [`Number of meeting rooms`, `Number of users per room`, `Meeting rooms square meters`, `Install type`],
        value: [`Enter the number of meeting rooms`, `Enter the number of users per room`, `Enter the area of meeting room`, `Wall mount`, `Ceiling mount`, `Leave a message for other project descriptions in detail`],
    },
    Network_Solution: {
        title: [
            `1. What environment/scene will your surveillance network be in? And how about the required coverage?`,
            `2. Is there a specific requirement for camera appearance?`,
            `3. Which camera type will you prefer?`,
            `4. Do you need a NVR to store data in real time?`,
        ],
        option: [
            `Monitoring area`,
            `Monitoring environment`,
            `Monitoring square meters`,
            `Other enviroment description `,
            `Surveillance camera turning radius`,
            `Surveillance camera resolution ratio`,
            `Surveillance camera Device pixel`,
            `Power supply`,
            `Coding format`,
            `Surveillance camera Type`,
            `Functional surveillance devices`,
            `Video storage Period`,
            `Need a NVR`,
        ],
        value: [
            `Office\\ Building`,
            `Supermarket`,
            `Hospital`,
            `School`,
            `Highway station`,
            `Indoor`,
            `Outdoor`,
            `360°`,
            `180°`,
            `360P`,
            `480P`,
            `720P`,
            `1080P`,
            `2K`,
            `4K`,
            `PoE(802.3af)`,
            `Passive PoE`,
            `PoE(802.3at)`,
            `DC`,
            `AC`,
            `H.264`,
            `H.265`,
            `Dome Camera`,
            `Bullet Camera`,
            `C-mount Camera`,
            `Turret Camera`,
            `Intelligent perimeter`,
            `Face capture`,
            `Face recognition`,
            `People counting`,
            `Temperature check`,
            `15 days`,
            `1 month`,
            `2 months`,
            `3 months`,
            `Yes`,
            `No`,
            `Please tell us how many monitoring nodes？And whether there is a requirement for the hard disk capacity，like 2T，4T，etc...`,
        ],
    },
    PON_Solution: {
        title: [
            `1. Could you please provide us the type and number of terminals?`,
            `2. What's your optical fiber cable resources?`,
            `3. What are your requirements regarding uplink/downlink bandwidth, and split ratio?`,
            `4. Leave a message for other project descriptions in detail `,
        ],
        option: [`Terminals Type`, `Terminals number`, `FTTx Type`, `Existing equipment`, `Split ratio`, `Uplink/downlink bandwidth`],
        value: [
            `Computer`,
            `Printer`,
            `Camera`,
            `IPTV`,
            `IP phone`,
            `FTTC`,
            `FTTB`,
            `FTTH`,
            `Enter the OLT Model No. and ONU Model No`,
            `1:64`,
            `1:32`,
            `Enter the Uplink/downlink  bandwidth`,
            `We are capable of working out a professional solution to maximize the utilization of the equipment and link resources also to save costs and improve business reliability.`,
        ],
    },
    Outdoor_Solution: {
        title: [
            `1. What's the cabling types, and cabling components do you have?`,
            `2. Please offer us your cabling solution transmission requirements, and the points map`,
            `3. What kind of existing products are you using? How about their related parameters?`,
            `4. Do you have any upgrade plans to expand network capacity for future business? `,
        ],
        option: [`Cabling for`, `Cable type`, `Cable management`, `Rack/Cabinet Quantity`, `Rack/Cabinet Distance`, `Transmission medium`],
        value: [
            `Buildings`,
            `Floors`,
            `Campus`,
            `Direct-buried`,
            `ADSS`,
            `Duct`,
            `Patch panels`,
            `Connectors`,
            `Adapters`,
            `Enter the Rack/Cabinet distance (M or KM)`,
            `Single-mode optical cable`,
            `Multi-mode optical cable`,
            `CAT5e copper cable`,
            `CAT6 copper cable`,
            `Upload the points map`,
            `Leave a message for other project descriptions in detail`,
        ],
    },
    Indoor_Solution: {
        title: [
            `1. Please offer the layout of your data room, describe the interconnect and distance between racks.`,
            `2. What is transmission method? How many connections are there respectively?`,
            `3. Do you have any upgrade plans to expand network capacity for future business? `,
        ],
        option: [`Cabling System`, `Rack/Cabinet Quantity`, `Rack/Cabinet Distance`, `Transmission method/Cable Type/Connections`],
        value: [
            `Copper system`,
            `Fiber system`,
            `Enter the Rack/Cabinet quantity`,
            `Enter the Rack/Cabinet distance (M or KM )`,
            `Upload DC Layout`,
            `1G-1G`,
            `10G-10G`,
            `40G-40G`,
            `100G-100G`,
            `Multimode`,
            `Singlemode`,
            `Leave a message for other project descriptions in detail`,
            `Transmission method`,
            `The number of connections`,
        ],
    },
    guide_page: {
        options: [
            { title: "Solution Design", des: "Design, deploy, and verify your solution with the best experts." },
            { title: "Test Support", des: "Choose the test items and test method to  test the feasibility of products and solutions before you buy." },
        ],
        title: "Get Technical Advice and Guidance",
    },
    infiniBand_networking: {
        title: [
            "1. Please provide us with your InfiniBand Network application scenarios and basic needs",
            "2. Please provide us with your InfiniBand Network business needs",
            "3. Leave a message for other project descriptions in detail",
        ],
        option: ["Scenario", "GPU Type", "Number of GPUs", "CPU Type", "Operating System", "Network Type", "Bandwidth Information", "Number of Servers", "Other Requirements"],
        value: [
            "Deep Learning",
            "High-performance computing",
            "VDI",
            "Cloud scenario",
            "3rd Gen Intel® Xeon®",
            "4th Gen Intel® Xeon® ",
            "5th Gen Intel® Xeon®",
            "AMD",
            "Microsoft Windows Server",
            "VMware ESXi",
            "Red Hat Enterprise Linux",
            "CentOS",
            "Computing power networking",
            "Storage networking",
            "In-band networking",
            "Out-of-band networking",
            "400G",
            "200G",
            "100G",
            "40G",
            "800G",
        ],
    },
    roce_Networking: {
        title: [
            "1. Please provide us with your ROCE Network application scenarios and basic needs",
            "2. Please provide us with your ROCE Network business needs",
            "3. Leave a message for other project descriptions in detail",
        ],
        option: [
            "Scenario",
            "NIC Speed",
            "Number of NICs",
            "NIC Type",
            "Operating System",
            "Switch Management Platform",
            "Network Type",
            "Bandwidth Information",
            "Expected Convergence Ratio",
            "Other Equipment Requirements",
        ],
        value: [
            "Deep Learning",
            "High-performance computing",
            "Data center",
            "Cloud scenario",
            "Microsoft Windows Server",
            "VMware ESXi",
            "Red Hat Enterprise Linux",
            "CentOS",
            "Spine",
            "Leaf",
            "Server",
            "400G",
            "200G",
            "100G",
            "40G",
            "Server",
            "Switching",
            "Optical Transceivers",
            "Cables",
        ],
    },
}
