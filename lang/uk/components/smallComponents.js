export default {
    qtyBoxTips: "The products qty must be between 1 and 5000.",
    See_all: `See All xxxx Solutions`,
    selectCountry: "Select Country / Region",
    previewPDF: `Preview PDF`,
    orderProcess: {
        cart: "Basket",
        confirmOrder: "Confirm Order",
        orderPayment: "Order Payment",
        poConfirmed: "PO Confirmed",
    },
    recentlyViewed: "Recently Viewed",
    searchCountry: "Search your country / region",
    searchCountry2: "Search your country",
    searchRegion: "Search your region", //
    noCountryResult: "No results found",
    singleFooter: {
        copyRight: "Copyright © 2009-XXX YYYY All Rights Reserved.",
        policy: "Privacy Policy and Notice at Collection",
        terms: "Terms of Use",
    },
    uploadFile: {
        fileEmpty: "Please select files",
        maxSizeError: "Maximum file size XXXXX.",
        multipleError: "limit XXXXX files",
    },
    fsFooter: {
        feedBack: "Report a Bug",
        subscribe: {
            title: "Subscribe",
            error: "Thank you for subscription!",
        },
        app: "Mobile Apps",
        email: "Your Email Address",
        maps: "Site Map",
        Call_us_at: `Call us on`,
    },
    search: {
        cancel: "Cancel",
        recentSearch: "Recent Search",
        hotSearch: "Hot Search",
        change: "Change",
        mPlaceholder: "Search...",
        pcPlaceholder: "Search",
        isntEn: "All Results",
        isDB: "The product xxxx is no longer available online. You can request an offer.",
        isDD: "The product xxxx is no longer available online. The like product yyyy is recommended as follows.",
        newPlaceholder: "Please enter the details.",
    },
    mSideBar: {
        login: "Account / Sign in",
        helpSetting: "Help & Setting",
        email: "Email Us",
        liveChat: "Live Chat",
        menu: "Main Menu",
        myAccount: "Homepage",
        accountSetting: "Account Setting",
        orderHistory: "Order History",
        orderReview: "Order Review",
        activeQuote: "Active Quote",
        savedCarts: "Saved Baskets",
        recentlyViewed: "Recently Viewed",
        signOut: "Sign Out",
        viewAll: "View All",
    },
    pHeader: {
        countryRegion: "Country/Region",
        languageCurrency: "Language/Currency",
        help: "Need Help?",
        liveChat: "Live Chat with us",
        technicalSupport: "Ask an expert",
        askExpert: "Tech Support",
        message: "Send us a message",
        email: "Email Now",
        call: "Want to call?",
        sign: "Sign in",
        newCustomer: "New customer?",
        createAccount: "Create an account",
        hello: "Hello, XXX",
        account: "Account #",
        accountCap: "ACCOUNT",
        cart: "Basket",
        cartLoading: "Basket loading",
        more: "more items ...",
        items: "XXX Items",
        item: `XXX Item`,
        viewCart: "View Basket",
        empty: "Your Shopping Basket is Empty.",
        hot: "Hot",
        new: "New",
        quoteHistory: "Quote History",
        Save: `Save`,
        sign_short: "Sign In",
    },
    newPHeader: {
        welcome: "Welcome back xxx!",
        myAccount: "My Account",
        manageProfile: "Manage Profile",
        myOrders: "My Orders",
        myQuotes: "My Quotes",
        mySupport: "My Support",
        returnRefund: "Return & Refund",
        sign: "Sign in",
        signUp: "Sign up",
        signOut: "Sign out",
        createAccount: "Create an Account",
        more: "more items ...",
        viewCart: "View Cart",
        account: "Account",
        shoppBusiness: "Shopping for your business?",
        certifyBusiness: "Certify a Free Business Account",
        certifyBusinessTip: `FS business account will help manage your company's IT investment from one convenient location. <a href="XXX">Learn more</a>`,
        businessAccountFont: "Business Account for XXX",
        addTeamMember: "Add Team Member",
    },
    solutions: {
        solution: "Solutions",
        Industry: {
            title: "By Industry",
            one: {
                title: "Higher Education",
                list: {
                    one: "VoIP for Campus",
                    two: "Video Surveillance for Campus",
                    three: "University Data Centers",
                    four: "Campus All-optical Network",
                },
            },
            two: {
                title: "Enterprise",
                list: {
                    one: "SMB Private Cloud",
                    two: "SMB Office Network",
                    three: "Office All-optical Network",
                    four: "VoIP for Enterprise Branch",
                    five: "Network Traffic Monitoring",
                },
            },
            three: {
                title: "Hospitality",
                list: {
                    one: "VoIP for Hotel",
                    two: "Smart Hotel Network",
                    three: "Smart Hotel Wireless",
                    four: "Smart Hotel Surveillance",
                    five: "Hotel All-Optical Network",
                },
            },
            four: {
                title: "Entertainment",
                list: {
                    one: "Bar Security",
                    two: "Amusement Park",
                },
            },
            five: {
                title: "Internet Service Provider",
                list: {
                    one: "Optical Fiber Expansion",
                    two: "Automated Transfer Platform",
                },
            },
            six: {
                title: "Retail",
                list: {
                    one: "Shopping Mall Wireless",
                    two: "Supermarket Surveillance",
                },
            },
            seven: {
                title: "Government",
                list: {
                    one: "VoIP for Government",
                },
            },
            eight: {
                title: "Manufacturing",
                list: {
                    one: "Factory Automation System Network",
                },
            },
        },
        Scenario: {
            title: "By Scenario",
            one: {
                title: "Data Center",
                list: {
                    one: "CDN Data Center",
                    two: "Network Visibility",
                    three: "All-electric Access",
                    four: "High-density Cabling",
                    five: "25G/100G Spine-Leaf",
                },
            },
            two: {
                title: "OTN",
                list: {
                    one: "High Capacity OTN",
                    two: "DC Disaster Recovery",
                    three: "Data Center Interconnect",
                    four: "Multi-service Hybrid Access",
                    five: "Long-distance Transmission",
                },
            },
            three: {
                title: "Video Surveillance",
                list: {
                    one: "SME",
                    two: "Family Villa",
                    three: "Smart Parking",
                    four: "Industrial Park",
                    five: "Internet Building",
                },
            },
            four: {
                title: "Wireless",
                list: {
                    one: "SME Wireless Network",
                    two: "Campus Wireless Network",
                    three: "Wisdom Wireless Network",
                    four: "Wireless Product Selector",
                },
            },
        },
    },
    MFsHeader: {
        explain: "Get your network solution more efficiently",
        entry: "View",
        backTo: "Back to Main Menu",
        backToXXXX: "Back to XXXX",
    },
    MFsFooter: {
        mobile: "Mobile Apps",
        ios: "iOS App",
        android: "Android App",
    },
    textNew: `New`,
    textHot: `Hot`,
    jpFax: `FAX: 03-5763-5625`,
    footerSubscribe: {
        Follow_us_and_the_future_of_network: `Follow us and the future of network`,
        Stay_in_touch: `Stay in Touch`,
        Enter_your_email: `Email`,
        Subscribe: `Subscribe`,
        Download_FS_APP: `Download FS APP`,
        Please_enter_your_email_address: "Please enter your email address.",
        Please_enter_a_valid_email_address: "Please enter a valid email address.",
        Welcome_to_FS_You_be_sent: "Welcome to FS.com! You'll be sent the next issue of our newsletter shortly.",
        ios_app_link: "https://apps.apple.com/us/app/fs-com/id1441371183?l=zh&amp;ls=1",
        android_app_link: "https://play.google.com/store/apps/details?id=cn.com.sf.fiberstore",
    },
}
