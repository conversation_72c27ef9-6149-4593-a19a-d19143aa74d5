export default {
    first_name: {
        first_name_required: "Veuillez entrer votre prénom.",
        first_name_max: "Le prénom doit comporter 40 caractères maximum.",
        first_entry_name_max: "Le prénom doit comporter 35 caractères maximum.",
        first_name_min: "Le prénom doit comporter au moins 2 caractères.",
    },
    last_name: {
        last_name_required: "Veuillez entrer votre nom.",
        last_name_max: "Le nom doit comporter 40 caractères maximum.",
        last_entry_name_max: "Le nom doit comporter 35 caractères maximum.",
        last_name_min: "Le nom doit comporter au moins 2 caractères.",
    },
    email: {
        email_required: "Veuillez entrer votre adresse e-mail.",
        business_email_required: "Veuillez entrer votre adresse e-mail commercial.",
        email_valid: "Veuillez entrer une adresse e-mail valide.",
        email_validate2: "Veuillez entrer une adresse e-mail valide.",
        email_validate3: `Veuillez ne pas entrer une adresse e-mail comprenant "admin/support/postmaster/abuse", ce qui pourrait entraîner des erreurs lors de l'utilisation du compte.`,
    },
    phone: {
        phone_required: "Votre numéro de téléphone est requis.",
        phone_min: "Votre numéro de téléphone doit comporter au moins 6 chiffres.",
        phone_validate: "Autoriser uniquement les chiffres, au moins 7 chiffres.",
        phone_validate_us: `Votre numéro de téléphone doit être un numéro à 10 chiffres.`,
    },
    password: {
        password_required: "Veuillez entrer votre mot de passe.",
        password_validate: "6 caractères au minimum et 32 caractères au maximum; avec au moins une lettre et un chiffre. Caractères spéciaux (_ ? @ ! # $ % & * .) autorisés.",
        password_validate2: "Les mots de passe ne doivent pas dépasser 32 caractères.",
        password_confirm: `Veuillez entrer votre mot de passe.`,
        password_match: `Les mots de passe ne correspondent pas. Veuillez réessayer.`,
    },
    register_password: {
        valid: "Veuillez entrer un mot de passe valide.",
        condition_list: ["Au moins une lettre", "Au moins un chiffre", "6-32 caractères", "Pas d'espace"],
        tips: "Caractères spéciaux (_ ? @ ! # $ % & * .) autorisés.",
    },
    ret_code: {
        ret_code_required: `Le code de réinitialisation est requis.`, //新增 2021-08-03
    },
    company: {
        company_required: "Le Nom de votre Entreprise est requis.",
        company_validate: "Le Nom de l'Entreprise doit comprendre entre 1 et 120 caractères.",
        company_size: "Veuillez sélectionner la taille de votre entreprise.",
    },
    address_type: {
        address_type_required: "Votre type d'adresse est requis.",
    },
    address: {
        address_required: "Votre Adresse est requise.",
        address_validate: "La ligne d'adresse 1 doit contenir 4 à 300 caractères.",
    },
    address2: {
        address2_required: "Votre adresse 2 est requise.",
        address2_validate: "L'adresse ligne 2 doit comporter entre 4 et 35 caractères.",
    },
    city: {
        city_required: "Votre Ville est requise.",
        city_validate: "La longueur de la ville doit être comprise entre 2 et 40 caractères.",
    },
    state: {
        state_required: "Votre Gouvernement/Province/Région est requis.",
    },
    agreement: {
        agree: "Veuillez vous assurer que vous acceptez notre Politique de Confidentialité et nos Conditions d'Utilisation.",
    },
    zip_code: {
        zip_code_required: "Votre Code Postal est requis.",
        zip_code_validate: "Votre code postal devrait contenir au moins 3 caractères.",
    },
    industry: {
        industry_required: "Veuillez sélectionner l'industrie.",
    },
    function: {
        function_required: "Veuillez sélectionner au moins une fonction.",
    },
    time: {
        time_required: "Veuillez sélectionner l'heure.",
    },
    date: {
        date_required: "Veuillez sélectionner la date.",
    },
    comments_questions: {
        comments_questions_required: "Les commentaires/Questions ne peuvent pas être vides.",
    },
    half_validate: `Veuillez entrer des chiffres anglais à demi-angle.`,
    zip_code_match: `Le code postal et l’adresse ne correspondent pas.`,
    tel_error: `Le numéro de téléphone saisi est incorrect.`,
    valid_phone_number: `Veuillez entrer un numéro de téléphone valide.`,
    valid_post_number: `Please enter a valid postcode.`,
    aggree_policy_new:
        "J’ai lu et j’accepte les FS.COM <a href='BBBB' target='_blank'>Conditions générales</a> et je reconnais avoir pris connaissance de la <a href='AAAA' target='_blank'>Politique de confidentialité et de l’Avis de collecte</a>.",
}
