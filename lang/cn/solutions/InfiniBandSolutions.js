export default {
    banner: {
        title: "PicOS® H100 InfiniBand解决方案",
        desc: "为高性能计算提供高性能、可扩展性和安全性",
        crumbList: [
            {
                name: "首页",
                url: "/",
            },
            {
                name: "高性能计算网络",
                url: "/solutions/hpc-networking-2000.html",
            },
            {
                name: "解决方案编号：S3000",
                url: "/solutions/picos-for-h100-infiniband-solution-S3000.html",
            },
        ],
    },
    nav: {
        list: ["介绍", "产品", "PicOS®", "AmpCon-DC", "优势", "资源"],
        btn: "联系我们",
    },
    introduction: {
        title: "用于NVIDIA® InfiniBand H100网络的PicOS®和AmpCon-DC管理平台",
        desc: "飞速（FS）交换机可利用先进的PicOS和AmpCon-DC管理平台，使数据中心运营商能够高效地配置、监控、管理和维护现代数据中心结构，从而提高利用率并降低总体运营成本。",
        tagTittle: ["概述", "InfiniBand网络", "管理网络", "存储网络"],
        tagSubTitle: [
            "基于NVIDIA® H100 GPU，飞速（FS）H100 Infiniband解决方案采用PicOS®软件和AmpCon-DC管理平台，根据HPC架构的网络拓扑结构（包括InfiniBand 网络、管理网络和存储网络）量身定制，满足各种业务需求。",
            "Infiniband网络由NVIDIA® H100 GPU和InfiniBand交换机提供支持，具有超低延迟和高带宽的特点，可通过流量控制和CRC冗余检查确保无损传输。",
            "飞速（FS）交换机可利用先进的PicOS®软件和AmpCon-DC管理平台功能，使客户能够高效地配置、监控、管理、预防性故障排除和维护高性能计算基础设施，从而提高利用率并降低总体运营成本。",
            "飞速（FS）PicOS®交换机支持BGP协议，具有强大的路由控制能力，可确保存储网络的最佳转发路径和低延迟转发状态。同时，它还具有灵活性和可扩展性，可满足特定的容量和带宽要求。",
        ],
        tagDesc: [
            {
                title: "InfiniBand无损传输",
                desc: "H100架构由InfiniBand交换机提供支持，具有超低延迟和800Gbps的高带宽，通过流量控制和CRC校验确保无损传输。",
            },
            {
                title: "PicOS®操作系统",
                desc: "PicOS®消除了关键网络基础设施对单一供应商的依赖，以更低的总体成本提供更具弹性、可编程和可扩展的网络操作系统（NOS）。",
            },
            {
                title: "AmpCon-DC管理平台",
                desc: "AmpCon-DC使数据中心运营商能够高效地配置、监控、管理和预防性地排除故障并维护数据中心结构。",
            },
        ],
    },
    products: {
        title: "产品清单",
        tabTitList: ["InfiniBand网络", "管理网络", "存储网络"],
        list: [
            [
                {
                    title: "QM9700/9790 InfiniBand交换机",
                    descTitle: ["MQM9700-NS2F", "MQM9790-NS2F"],
                },
                {
                    title: "ConnectX®-6/7 InfiniBand网卡",
                    descTitle: ["MCX75510AAS-NEAT", "MCX75310AAC-NEAT", "MCX75310AAS-NEAT"],
                },
                {
                    title: "800G/400G InfiniBand光模块",
                    descTitle: ["OSFP-SR8-800G", "OSFP-SR4-400G-FL"],
                },
                {
                    title: "光纤跳线",
                    descTitle: ["12FMTPOM4"],
                },
            ],
            [
                {
                    title: "AmpCon-DC管理平台",
                    descTitle: [" P-AC-EE-B1"],
                },
                {
                    title: "PicOS®交换机",
                    descTitle: ["N8550-64C", "N8550-48B8C", "N5850-48S6Q", "S5810-48TS"],
                },
                {
                    title: "光模块",
                    descTitle: ["QSFP28-SR4-100G", "QSFP28-SFP28-CVR", "SFP28-25GSR-85", "SFP-10GSR-85"],
                },
                {
                    title: "光纤跳线",
                    descTitle: ["12FMTPOM4", "OM4LCDX", "C6ASFTPSGPVC"],
                },
            ],
            [
                {
                    title: "PicOS®交换机",
                    descTitle: ["N8550-64C"],
                },
                {
                    title: "ConnectX®-6以太网网卡",
                    descTitle: ["MCX623106AN-CDAT"],
                },
                {
                    title: "光模块",
                    descTitle: ["QSFP28-SR4-100G"],
                },
                {
                    title: "光纤跳线",
                    descTitle: ["12FMTPOM4"],
                },
            ],
        ],
    },
    picos: {
        title: "以更低的总体拥有成本实现更灵活、更高效的网络运营",
        desc: "PicOS®提供高弹性、高可靠性、可编程的网络，这些网络比以前的单体网络更为精简和可扩展。",
        list: [
            {
                title: "全面互操作性",
                subTitle: "支持OpenFlow、SNMP和gNMI协议，提供API应用程序编程接口，提供与思科和其他第三方基础设施的互操作性，以便逐步迁移和升级。",
            },
            {
                title: "网络虚拟化",
                subTitle: "使用带有脊叶阵列的开放式解决方案，无需思科（Cisco）机箱解决方案即可支持灵活、可扩展的虚拟化架构。",
            },
            {
                title: "强化安全",
                subTitle: "在不耗尽TCAM资源的情况下启用复杂的ACL，灵活控制网络流量，确保数据安全，同时不影响硬件性能。",
            },
            {
                title: "网络灵活性",
                subTitle: "CrossFlow™技术使得SDN/OpenFlow流量能够与生产级别的第二层/第三层流量在同一交换机端口上共存，从而形成了基于开放意图（OIBN）的网络服务架构，增强灵活性。",
            },
        ],
    },
    ampcon: {
        title: "实现端到端网络生命周期管理自动化",
        desc: "通过端到端的网络生命周期管理，包括自动化的配置、维护、合规检查和升级，防止配置错误和停机。",
        list: [
            {
                title: "零接触调配",
                subTitle: "通过自动调配和策略执行，从任何地方轻松、安全地进行扩展。",
            },
            {
                title: "配置管理",
                subTitle: "向单个或一组交换机推送更新、补丁和错误修复，节省人力，防止停机。",
            },
            {
                title: "备份&合规性",
                subTitle: `根据 "黄金配置 "自动进行配置备份、操作日志和合规性检查，并通过智能回滚将错误降至最低。`,
            },
            {
                title: "交换机可见性",
                subTitle: "提供所有交换机的详细清单，包括硬件详情、软件版本、配置等。",
            },
            {
                title: "无代理自动化",
                subTitle: "编写Ansible Playbooks来创建和调度定制化的工作流程，并生成易于阅读的报告。",
            },
            {
                title: "PicOS-V预配置",
                subTitle: "AmpCon-DC管理平台可在虚拟化场景中使用PicOS-V进行预配置，然后移至数据中心。",
            },
        ],
    },
    benefits: {
        title: "探索H100 InfiniBand网络的优势",
        list: [
            {
                title: "统一管理平台",
                desc: "配有AmpCon-DC管理平台的PicOS可对管理和存储网络进行统一配置、监控和维护，从而避免了代价高昂的停机时间和耗时的手动任务。",
            },
            {
                title: "强大的InfiniBand架构",
                desc: "飞速（FS）提供高度可靠的InfiniBand网络架构和专业技术服务，能够在48小时内提供定制的网络设计和产品清单。",
            },
            {
                title: "经济高效解决方案",
                desc: "通过与英伟达（NVIDIA）合作，飞速（FS）可以提供原装产品，如InfiniBand交换机和网卡，通过其丰富的产品生态系统，降低30%成本。",
            },
            {
                title: "先进的研发和测试",
                desc: "飞速（FS）拥有先进的研发中心，并使用NVIDIA® MQM9790交换机、ConnectX-7网卡和其他产品构建了一个全负载传输测试平台，以确保稳定性。",
            },
            {
                title: "分区设计",
                desc: "网络分区设计（计算、存储、带内管理和带外管理）有助于隔离不同的业务领域，降低流量复杂性。",
            },
            {
                title: "灵活可扩展性",
                desc: "脊叶两层架构满足当前网络运营要求，同时为未来业务扩展提供灵活性和可靠性。",
            },
        ],
    },
    resources: {
        title: "资源",
        list: [
            {
                title: "解决方案",
                linkText: ["PicOS®自动驾驶解决方案", "利用 RoCE 网络为高性能计算赋能", "飞速（FS）InfiniBand光模块和线缆解决方案"],
            },
            {
                title: "技术博文",
                linkText: ["InfiniBand 网络和架构概述", "您对InfiniBand网络内计算了解多少？", "为高性能计算（HPC）选择InfiniBand产品的建议"],
            },
            {
                title: "公司新闻",
                linkText: ["飞速（FS）InfiniBand解决方案构建HPC网络", "飞速（FS）InfiniBand光模块和线缆指南", "飞速（FS）与 Pica8宣布战略合作，共同推动开放网络建设"],
            },
            {
                title: "文档",
                linkText: ["PicOS®用于H100 InfiniBand解决方案测试报告", "H100 InfiniBand解决方案手册", "800G&400G光模块测试指南"],
            },
        ],
    },
    contactUs: {
        title: "准备好开始了吗？",
        desc: "如果您对感兴趣的解决方案有任何疑问，请填写下方表格，我们将在1个工作日内给予回复。",
    },
    headTop: {
        href: "/solutions/picos-for-h100-infiniband-solution-S3000.html",
        name: "H100 InfiniBand解决方案",
    },
    meta: {
        title: "PicOS®和AmpCon-DC用于NVIDIA® InfiniBand H100网络",
        description: "探索飞速（FS）H100 InfiniBand解决方案如何革新高性能计算网络，提高效率达20%。借助PicOS软件和AmpCon网络控制器，用户可以享受无缝配置和维护，增强安全性并加速高性能计算工作流程。",
    },
}
