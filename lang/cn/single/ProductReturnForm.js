export default {
    head: {
        title: "产品退货表 - FS",
        description: "请填写此表，以便我们可以开始处理您的退货（RMA）。如果你有问题，请阅读我们的退货政策或联系我们。",
    },
    title: "产品退货表",
    tip: "请填写表格以提交退货请求",
    orderInformation: "订单信息",
    orderNumber: "订单号",
    selectServiceType: "选择服务",
    serviceType: "服务类型",
    ID: "产品ID*",
    QTY: "产品数量*",
    productID: "申请售后的产品信息",
    reason: "原因描述",
    uploadholder: "请简要描述您申请售后的原因，便于我们快速处理您的申请。",
    uploadTip: "请使用PDF, JPG, PNG, DOC, DOCX, XLS, XLSX或TXT文件。最大尺寸为5M。",
    uploadTit: "您的请求已成功提交。",
    contactDetails: "联系方式",
    firstName: "First Name",
    lastName: "Last Name",
    country: "Country/Region",
    emailAddress: "Email Address",
    phoneNumber: "Phone Number",
    zipCode: "邮政编码",
    apt: "Apt, Suite, Floor, etc.",
    stateHolder: "please select states",
    agreement: `我同意飞速（FS）的 <a href="xxxx " target="_blank" style="color:#19191a">隐私政策</a> 和 <a href="yyyy" target="_blank" style="color:#19191a">使用条款</a> 。`,
    privacyPolicy: "Privacy Policy and Notice at Collection",
    termsUse: "Terms of Use",
    submit: "提交",
    successTipTit: "Your request has been submitted successfully.",
    successTipTxt: "FS After-Sale Center approved this RMA request. Please contact your account manager to get the RMA number and follow the instructions to process it.",
    returnRefund: "退货&退款",
    replacement: "更换",
    maintenance: "维修",
    uploadFile: "上传文件",
    addSeriesNumber: {
        save: "保存",
        tit: "添加系列号",
        tip1: " 请填写光学模块的序列号，这样我们可以快速找到你需要售后服务的模块。",
        tip2: `不同的序列号可以用"/"隔开。`,
    },
    first_name: {
        first_name_required: "请输入你的名字。",
        first_name_max: "名字必须是40个字符以上。",
        first_entry_name_max: "名字必须是35个字符以上。",
        first_name_min: "名字必须是最少2个字符。",
    },
    last_name: {
        last_name_required: "请输入你的姓氏。",
        last_name_max: "姓氏最大必须是40个字符。",
        last_entry_name_max: "姓氏最大必须是35个字符。",
        last_name_min: "姓氏必须是最少2个字符。",
    },
    email: {
        email_required: "请输入你的电子邮件地址。",
        email_validate: "请输入一个有效的电子邮件地址。(eg:<EMAIL>)",
        email_validate2: "请输入一个有效的电子邮件地址。",
        email_exist: "账户已经存在。点击这里",
        email_exist_sign_in: "登录。",
        email_re_enter: "请重新输入你的电子邮件地址。",
        email_match: "新的电子邮件地址必须匹配。",
        email_validate_qq: "无效的电子邮件地址，请检查后重试。",
    },
    phone: {
        phone_required: "请输入你的电话号码。",
        phone_min: "您的电话号码必须至少是6位数。",
    },
    address: {
        tit: "地址线1*",
        address_required: "您的地址是必须的。",
        address_validate: "地址行1的长度必须在4到35个字符之间。",
    },
    address2: {
        tit: "地址行2",
        address2_required: "您的地址2是必须的。",
        address2_validate: "地址行2应该包含最多35个字符。",
    },
    city: {
        tit: "城市",
        city_required: "你的城市是必须的。",
    },
    state: {
        tit: "州/省/地区",
        tip: "请选择州",
        state_required: "您的州/省/地区是必须的。",
    },
    zip_code: {
        zip_code_required: "您的邮政编码是必须的。",
        zip_code_validate: "你的邮政编码应该至少有3个字符长。",
    },

    agreement_agree: "请确认您同意我们的隐私政策和使用条款。",

    order_number: {
        order_number_required: "订单号不能为空。",
        order_number_eg: "请输入一个有效的订单号。 例如:FS180808001234",
    },
    service_type: {
        service_type_required: "请选择服务类型。",
    },
    products_id: {
        products_id_required: "请输入产品ID。",
    },
    products_num: {
        products_num_required: "请输入产品的数量。",
    },
    review_content: {
        review_content_required: "请解释你的退货原因。",
    },
    tickets_service_type: {
        tickets_service_type_required: "请选择服务类型。",
    },
    state_check: {
        state_check_required: "请选择你持有免税权的州/地区。",
    },
    // 5.23新增
    AdditionalComments: "附加评论",
    ServiceTypeList: ["产品功能/使用问题", "包裹损坏", "包裹未送达", "缺失的物品或零件", "不再需要", "订购错误的产品", "收到错误的物品", "需要技术文件", "产品优化建议", "备件要求", "其他问题"],
    supportType: "服务类型",
    supportTypeList: ["申请退换货", "申请技术支持"],
    timeOfReceipt: "收货时间",
    DeviceVersion: "设备版本",
    SN: "S/N",
    FaultTime: "故障时间",
    linkEnd: "链接结束",
    siteA: "站点A",
    siteB: "站点B",
    DeviceBoardModel: "设备/板卡模型",
    please_choose_time: "请选择一个合适的时间。",
    please_provide_more_details: "请提供关于您的备件请求的更多细节",
    please_provide_a_statement: "请提供一份声明，说明POD不是由你自己签署的，并提供一份声明而不是宣誓",
    Please_provide_the_picture: "请提供开箱的图片或视频",
    support_type: {
        support_type_required: "请选择服务类型。",
    },
    enter_correct_product: "请输入正确的产品ID。",
    device_version_tips: "设备版本可以用“, . / ”.",
    sn_tips: "错误的产品S/N号码可以用“, . / ”.",
    fault_time_tips: "如果有多个产品故障，请填写最早的故障时间",
    description_original: "请描述在使用产品过程中遇到的问题。",
    please_describle_your_question: "请描述您的问题，以便我们能更快地处理您的请求。",
    your_tech_support: "Your Tech Support was approved.",
    we_have_received: "We have received your request, our tech guys will check it ASAP,please contact your account manager to get solved plan.",
    declaration: "Declaration instead of an oath",
    read_and_understood: "I agree I have read and understood <a href='javacript:;' onClick='sparePopup()'>XXXXX</a>",
    spare_part_request_agreement: "Spare Part Request Agreement. ",
    spare_agree: "Please make sure you agree to our Spare Part Request Agreement.",
    sparePopup: [
        {
            tit: "备件服务说明：",
            txt: "在 FS 认为您需要备件并生成退货授权（RMA）编号后，您将会获得设备的备件服务。备件一般是全新品，或者在性能和可靠性方面与新设备相当的产品。",
        },
        {
            tit: "客户的责任:",
            txt: "为了使FS能够提供更好的支持和服务，客户将被要求在收到更换或维修的产品后15天内将备件返还给FS，并对运输过程中损坏或丢失的部件负责。如果设备没有在这段时间内返回，或者返回的设备不符合FS的验收标准，FS有权向您收取当时提供的备件的清单价格。",
        },
    ],
    agree_title: `备件请求协议`,
    left_content: {
        tit02: "服务类型",
        title: "售后服务申请",
        part1: {
            title: "技术支持",
            des: "您可以提交产品相关问题的申请，您的客户经理和技术专家将会快速处理分析问题，并提供相应解决方案。",
        },
        part2: {
            title: "退换货申请",
            des: "您可以提交退货，换货或维修的申请。",
        },
    },
    Confirmation: "确认",
    content: "删除退回产品？",
    Cancel: "取消",
    Delete: "删除",
}
