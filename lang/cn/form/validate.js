export default {
    first_last_name: {
        required: "请输入您的姓名。",
        len: "姓名长度最小为2个字，最长为20个字",
    },
    first_name: {
        first_name_required: "请输入您的名字。",
        first_name_max: "名字最多只能包含 40 个字符。",
        first_name_min: "名字必须至少包含 2 个字符。",
    },
    last_name: {
        last_name_required: "请输入您的姓氏。",
        last_name_max: "姓氏最多只能包含 40 个字符。",
        last_name_min: "姓氏必须至少包含 2 个字符。",
    },
    email: {
        email_required: "请输入您的邮箱地址。",
        business_email_required: "请输入您的邮箱地址。",
        email_valid: "请输入正确格式的邮箱账号。",
        email_validate2: "请输入正确格式的邮箱账号。(例：<EMAIL>)",
        email_validate3: `Please do not fill in an email address including "admin/support/postmaster/abuse", which may cause errors when using the account.`,
    },
    phone: {
        phone_required: "请输入您的电话号码。",
        phone_min: "请输入正确格式的电话号码。",
        phone_validate: "Allow digits only, at least 7 ones.",
        phone_validate_us: `您的电话号码应该是 10 位数字。`,
    },
    phone_email: {
        required: "请输入您的手机号或邮箱账号。",
        validate: "请输入正确格式的手机号或邮箱账号。",
    },
    verification: {
        name: "验证码",
        get: "获取验证码",
        required: "请输入验证码。",
        again: "重试",
        validate: "请输入正确格式的验证码。",
    },
    password: {
        password_required: "请输入密码",
        password_validate: "请使用字母、数字和符号两种及以上的组合，6-32个字符。",
        password_validate2: "密码最多为32个字符。",
        password_confirm: `请输入密码。`,
        password_match: `密码不匹配。请再试一次。`,
    },
    register_password: {
        valid: "请输入有效的密码",
        condition_list: ["至少一个字母", "至少一个数字", "6-32个字符", "没有空格"],
        tips: "允许特殊字符(_ ? @ ! # $ % & * .) ",
    },
    ret_code: {
        ret_code_required: `Reset code is required.`, //新增 2021-08-03
    },
    company: {
        company_required: "您的公司名称是必填的。",
        company_validate: "Company Name must be between 1 and 120 characters long.",
        company_size: "Please select your company size.",
    },
    address_type: {
        address_type_required: "Your Address Type is required.",
    },
    address: {
        address_required: "您的地址信息是必填的。",
        address_validate: "Address line 1 must be between 4 and 35 characters long.",
    },
    address2: {
        address2_required: "Your Address2 is required.",
        address2_validate: "Address line 2 must be between 4 and 35 characters long.",
    },
    city: {
        city_required: "Your City is required.",
    },
    state: {
        state_required: "Your State/Province/Region is required.",
    },
    agreement: {
        agree: "请确保您同意我们的隐私政策和使用条款。",
    },
    zip_code: {
        zip_code_required: "Your Zip Code is required.",
        zip_code_validate: "Your ZIP/postal code should be at least 3 characters long.",
    },
    industry: {
        industry_required: "请选择行业",
    },
    function: {
        function_required: "Please select at least one function.",
    },
    time: {
        time_required: "请确定选择时间。",
    },
    date: {
        date_required: "Please select date.",
    },
    comments_questions: {
        comments_questions_required: "Comments/Questions cannot be empty.",
    },
    half_validate: `请以半角格式输入字符。`,
    zip_code_match: `邮编与地址不匹配。`,
    tel_error: `您输入的电话号码有误。`,
    valid_phone_number: `请输入一个有效的电话号码。`,
    valid_post_number: `Please enter a valid postcode.`,
    aggree_policy_new: "我已阅读并同意 FS.COM 的<a href='BBBB' target='_blank'>《条款和条件》</a>，并确认已知晓<a href='AAAA' target='_blank'>《隐私政策和信息收集通知》</a>。",
}
