export default {
    first_name: {
        first_name_required: "Dies ist ein Pflich<PERSON>feld.",
        first_name_max: "Der Vorname darf maximal 40 Zeichen lang sein.",
        first_entry_name_max: "Der Vorname darf maximal 35 Zeichen lang sein.",
        first_name_min: "Der Vorname muss mindestens 2 Zeichen lang sein.",
    },
    last_name: {
        last_name_required: "Dies ist ein Pf<PERSON>feld.",
        last_name_max: "Der Nachname darf maximal 40 Zeichen lang sein.",
        last_entry_name_max: "Der Nachname darf maximal 35 Zeichen lang sein.",
        last_name_min: "Der Nachname muss mindestens 2 Zeichen lang sein.",
    },
    email: {
        email_required: "Dies ist ein Pflichtfeld.",
        business_email_required: "Bitte geben Sie Ihre geschäftliche E-Mail-Adresse ein.",
        email_valid: "Bitte geben Sie eine gültige E-Mail-Adresse ein.",
        email_validate2: "Bitte geben Sie eine gültige E-Mail-Adresse ein. (z.B. <EMAIL>)",
        email_validate3: "Bitte geben Sie keine E-Mail-Adresse ein, die „admin“, „support“, „postmaster“ oder „abuse“ enthält, da dies zu Fehlern bei der Nutzung des Kontos führen kann.",
    },
    phone: {
        phone_required: "Dies ist ein Pflichtfeld.",
        phone_min: "Ihre Telefonnummer muss mindestens 6 Ziffern sein.",
        phone_validate: "Es sind nur Ziffern erlaubt. Bitte geben Sie mindestens sieben Ziffern ein.",
        phone_validate_us: `Ihre Telefonnummer sollte eine 10-stellige Nummer sein.`,
    },
    password: {
        password_required: "Dies ist ein Pflichtfeld.",
        password_validate: "Das Passwort muss mindestens 6 Zeichen und darf maximal 32 Zeichen enthalten; Sonderzeichen (_ ? @ ! # $ % & * .) sind zulässig.",
        password_validate2: "Das Passwort darf maximal 32 Zeichen enthalten.",
        password_confirm: `Dies ist ein Pflichtfeld.`,
        password_match: `Passwörter stimmen nicht überein. Bitte versuche es erneut.`,
    },
    register_password: {
        valid: "Geben Sie bitte ein gültiges Passwort ein.",
        condition_list: ["Mindestens ein Buchstabe", "Mindestens eine Ziffer", "6 bis 32 Zeichen", "Kein Leerzeichen"],
        tips: "Sonderzeichen (_ ? @ ! # $ % & * .) sind erlaubt.",
    },
    ret_code: {
        ret_code_required: `Dies ist ein Pflichtfeld.`, //新增 2021-08-03
    },
    company: {
        company_required: "Dies ist ein Pflichtfeld.",
        company_validate: "Der Firmenname muss zwischen 1 und 120 Zeichen lang sein.",
        company_size: "Bitte wählen Sie Ihre Betriebsgröße aus.",
    },
    address_type: {
        address_type_required: "Dies ist ein Pflichtfeld.",
    },
    address: {
        address_required: "Dies ist ein Pflichtfeld.",
        address_validate: "Die Adresse muss zwischen 4 und 35 Zeichen lang sein.",
    },
    address2: {
        address2_required: "Dies ist ein Pflichtfeld.",
        address2_validate: "Der Adresszusatz muss zwischen 4 und 35 Zeichen lang sein.",
    },
    city: {
        city_required: "Dies ist ein Pflichtfeld.",
        city_validate: "Stadt muss zwischen zwei und 40 Zeichen lang sein.",
    },
    state: {
        state_required: "Dies ist ein Pflichtfeld.",
    },
    agreement: {
        agree: "Bitte stimmen Sie unsere Datenschutzerklärung und AGB zu.",
    },
    zip_code: {
        zip_code_required: "Dies ist ein Pflichtfeld.",
        zip_code_validate: "Ihre Postleitzahl muss mindestens 3 Zeichen lang sein.",
    },
    industry: {
        industry_required: "Dies ist ein Pflichtfeld.",
    },
    function: {
        function_required: "Dies ist ein Pflichtfeld.",
    },
    time: {
        time_required: "Dies ist ein Pflichtfeld.",
    },
    date: {
        date_required: "Dies ist ein Pflichtfeld.",
    },
    comments_questions: {
        comments_questions_required: "Dies ist ein Pflichtfeld.",
    },
    half_validate: `Bitte geben Sie halbe - englische Winkelzahlen ein.`,
    zip_code_match: `Die Postleitzahl und die Adresse stimmen nicht überein.`,
    tel_error: `Die eingegebene Telefonnummer ist falsch.`,
    valid_phone_number: `Bitte geben Sie eine gültige Telefonnummer ein`,
    valid_post_number: `Please enter a valid postcode.`,
    aggree_policy_new:
        "Ich habe die FS.COM <a href='BBBB' target='_blank'>Allgemeinen Geschäftsbedingungen</a> gelesen und stimme ihnen zu und bestätige die <a href='AAAA' target='_blank'>Datenschutzerklärung sowie den Hinweis zur Datenerhebung</a>.",
}
