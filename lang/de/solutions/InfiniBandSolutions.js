export default {
    banner: {
        title: "PicOS® für H100 InfiniBand-Lösung",
        desc: "Hohe Leistung, Skalierbarkeit und Sicherheit für HPC",
        crumbList: [
            {
                name: "Startseite",
                url: "/",
            },
            {
                name: "HPC-Netzwerke",
                url: "/solutions/hpc-networking-2000.html",
            },
            {
                name: "Lösung-ID: S3000",
                url: "/solutions/picos-for-h100-infiniband-solution-S3000.html",
            },
        ],
    },
    nav: {
        list: ["Einführung", "Produkte", "PicOS®", "AmpCon-DC", "Vorteile", "Ressourcen"],
        btn: "Kontakt",
    },
    introduction: {
        title: "PicOS® und AmpCon-DC management platform für NVIDIA® InfiniBand H100 Netzwerk",
        desc: "Die FS-Switches können die fortschrittliche PicOS®- und AmpCon-DC management platform nutzen, um Rechenzentrumsbetreibern die effiziente Bereitstellung, Überwachung, Verwaltung und Wartung der modernen Rechenzentrumsstruktur zu ermöglichen, wodurch eine höhere Auslastung erzielt und die Gesamtbetriebskosten gesenkt werden.",
        tagTittle: ["Überblick", "InfiniBand-Netzwerk", "Managementnetzwerk", "Speichernetzwerk"],
        tagSubTitle: [
            `Basierend auf der NVIDIA® H100-GPU, zusammen mit der <a href="xxxx" target="_blank">PicOS®</a>-Software und der <a href="nnnn" target="_blank">AmpCon-DC management platform</a> ist die FS H100 Infiniband-Lösung auf die Netzwerktopologie der HPC-Architektur zugeschnitten, einschließlich Infiniband-Netzwerk, Verwaltungsnetzwerk und Speichernetzwerk, um verschiedenen Unternehmen gerecht zu werden Bedürfnisse.`,
            "Angetrieben durch NVIDIA® H100 GPU und InfiniBand-Switches zeichnet sich das Infiniband-Netzwerk durch extrem niedrige Latenz und hohe Bandbreite aus und gewährleistet eine verlustfreie Übertragung mit Flusskontrolle und CRC-Redundanzprüfungen.",
            "Die FS-Switches können die fortschrittliche PicOS®-Software und die Funktionssätze der AmpCon-DC management platform nutzen, um Kunden in die Lage zu versetzen, die HPC-Infrastruktur effizient bereitzustellen, zu überwachen, zu verwalten, vorbeugend Fehler zu beheben und zu warten, wodurch eine höhere Auslastung realisiert und die Gesamtbetriebskosten gesenkt werden.",
            "FS PicOS®-Switches unterstützen das BGP-Protokoll mit leistungsstarken Routing-Steuerungsfunktionen und stellen gleichzeitig den optimalen Weiterleitungspfad und den Weiterleitungsstatus des Speichernetzwerks mit geringer Latenz sicher. Gleichzeitig ist es flexibel und kann skaliert werden, um spezifische Kapazitäts- und Bandbreitenanforderungen zu erfüllen.",
        ],
        tagDesc: [
            {
                title: "Verlustfreie InfiniBand-Übertragung",
                desc: "Die H100-Architektur mit InfiniBand-Switches bietet eine extrem niedrige Latenz und eine hohe Bandbreite von 800 Gbps und sorgt so für eine verlustfreie Übertragung mit Flusskontrolle und CRC-Prüfungen.",
            },
            {
                title: "PicOS®-Betriebssystem",
                desc: "PicOS® beseitigt die Abhängigkeit eines einzelnen Anbieters von kritischer Netzwerkinfrastruktur und bietet ein robusteres, programmierbareres und skalierbareres Netzwerkbetriebssystem (NOS) zu geringeren Gesamtbetriebskosten.",
            },
            {
                title: "AmpCon-DC Management Platform",
                desc: "AmpCon-DC ermöglicht Rechenzentrumsbetreibern die effiziente Bereitstellung, Überwachung, Verwaltung sowie vorbeugende Fehlerbehebung und Wartung ihrer Rechenzentrumsstruktur.",
            },
        ],
    },
    products: {
        title: "Produktliste",
        tabTitList: ["InfiniBand-Netzwerk", "Managementnetzwerk", "Speichernetzwerk"],
        list: [
            [
                {
                    title: "QM9700/9790 InfiniBand-Switches",
                    descTitle: ["MQM9700-NS2F", "MQM9790-NS2F"],
                },
                {
                    title: "ConnectX®-6/7 InfiniBand-Adapter",
                    descTitle: ["MCX75510AAS-NEAT", "MCX75310AAC-NEAT", "MCX75310AAS-NEAT"],
                },
                {
                    title: "800G/400G InfiniBand-Transceiver",
                    descTitle: ["OSFP-SR8-800G", "OSFP-SR4-400G-FL"],
                },
                {
                    title: "Glasfaserkabel",
                    descTitle: ["12FMTPOM4"],
                },
            ],
            [
                {
                    title: "AmpCon-DC Management Platform",
                    descTitle: [" P-AC-EE-B1"],
                },
                {
                    title: "PicOS®-Switches",
                    descTitle: ["N8550-64C", "N8550-48B8C", "N5850-48S6Q", "S5810-48TS"],
                },
                {
                    title: "Optisches Transceivermodul",
                    descTitle: ["QSFP28-SR4-100G", "QSFP28-SFP28-CVR", "SFP28-25GSR-85", "SFP-10GSR-85"],
                },
                {
                    title: "Glasfaserkabel",
                    descTitle: ["12FMTPOM4", "OM4LCDX", "C6ASFTPSGPVC"],
                },
            ],
            [
                {
                    title: "PicOS®-Switches",
                    descTitle: ["N8550-64C"],
                },
                {
                    title: "ConnectX®-6 Ethernet-Adapter",
                    descTitle: ["MCX623106AN-CDAT"],
                },
                {
                    title: "Optisches Transceivermodul",
                    descTitle: ["QSFP28-SR4-100G"],
                },
                {
                    title: "Glasfaserkabel",
                    descTitle: ["12FMTPOM4"],
                },
            ],
        ],
    },
    picos: {
        title: "Robusterer und effizienterer Netzwerkbetrieb bei geringeren Gesamtbetriebskosten",
        desc: `Mit <a href="xxxx" target="_blank">PicOS®</a> werden hochresiliente, hochzuverlässige, programmierbare Netzwerke geliefert, die schlanker und skalierbarer sind als ihre monolithischen Vorgänger.`,
        list: [
            {
                title: "Volle Interoperabilität",
                subTitle:
                    "Unterstützt OpenFlow-, SNMP- und gNMI-Protokolle, stellt API-Anwendungsprogrammierschnittstellen bereit und bietet Interoperabilität mit Cisco und anderen Infrastrukturen von Drittanbietern für progressive Migration und Upgrades.",
            },
            {
                title: "Netzwerkvirtualisierung",
                subTitle: "Nutzen Sie eine offene Lösung mit Spine-Leaf-Arrays, um flexible und skalierbare Virtualisierungsarchitekturen zu unterstützen.",
            },
            {
                title: "Gehärtete Sicherheit",
                subTitle:
                    "Ermöglicht komplexe ACL, ohne die TCAM-Ressourcen zu erschöpfen, um den Netzwerkverkehr flexibel zu steuern und so die Datensicherheit zu gewährleisten, ohne die Hardwareleistung zu beeinträchtigen.",
            },
            {
                title: "Netzwerkflexibilität",
                subTitle:
                    "Die CrossFlow™-Technologie ermöglicht SDN/OpenFlow-Verkehr mit Produktions-Layer-2/Layer-3-Datenverkehr auf denselben Switch-Ports und bildet so Open Intent-based Networking (OIBN) für eine verbesserte Flexibilität der Netzwerkdienste.",
            },
        ],
    },
    ampcon: {
        title: "Automatisierung des End-to-End-Netzwerklebenszyklusmanagements",
        desc: "Vermeidung von Fehlkonfigurationen und Ausfallzeiten durch ein durchgängiges Netzwerk-Lebenszyklusmanagement mit automatischer Bereitstellung, Wartung, Konformitätsprüfung und Upgrades.",
        list: [
            {
                title: "Zero-Touch-Bereitstellung",
                subTitle: "Einfache und sichere Skalierung von überall aus mit automatischer Bereitstellung und Durchsetzung von Richtlinien.",
            },
            {
                title: "Konfigurationsmanagement",
                subTitle: "Aktualisierungen, Patches und Fehlerbehebungen an einen einzelnen oder eine Gruppe von Switches senden, um Arbeitsaufwand zu sparen und Ausfallzeiten zu verhindern.",
            },
            {
                title: "Backups und Compliance",
                subTitle: `Automatisierung der Konfigurationssicherungen, Betriebsprotokolle und Compliance-Prüfungen gegen eine "goldene Konfiguration" mit intelligentem Rollback, um Fehler zu minimieren.`,
            },
            {
                title: "Sichtbarkeit von Switch",
                subTitle: "Bietet eine detaillierte Bestandsaufnahme aller Switches, einschließlich Hardwaredetails, Softwareversion, Konfiguration und mehr.",
            },
            {
                title: "Agentenlose Automatisierung",
                subTitle: "Schreibt Sie Ansible Playbooks, um individuelle Workflows mit leicht lesbaren Berichten zu erstellen und zu planen.",
            },
            {
                title: "PicOS-V-Vorkonfiguration",
                subTitle: `<a href="xxxx" target="_blank">AmpCon-DC management platform</a> kann mit <a href="nnnn" target="_blank">PicOS-V</a> im Virtualisierungsszenario vorkonfiguriert und dann ins Rechenzentrum verschoben werden.`,
            },
        ],
    },
    benefits: {
        title: "Entdecken Sie die Vorteile des H100 InfiniBand-Netzwerks",
        list: [
            {
                title: "Einheitliche Verwaltungsplattform",
                desc: `<a href="xxxx" target="_blank">PicOS®</a> mit <a href="nnnn" target="_blank">AmpCon-DC management platform</a> ermöglicht eine einheitliche Konfiguration, Überwachung und Wartung von Verwaltungs- und Speichernetzwerken und eliminiert so kostspielige Ausfallzeiten und zeitaufwändige manuelle Aufgaben.`,
            },
            {
                title: "Leistungsstarke InfiniBand-Architektur",
                desc: "FS bietet äußerst zuverlässige InfiniBand-Netzwerkarchitekturen und professionelle technische Dienstleistungen und liefert innerhalb von 48 Stunden maßgeschneiderte Netzwerkdesigns und Produktlisten.",
            },
            {
                title: "Kosteneffiziente Lösung",
                desc: "Durch die Partnerschaft mit NVIDIA® kann FS Originalprodukte wie InfiniBand-Switches und -Adapter liefern und durch sein umfangreiches Produkt-Ökosystem die Kosten um 30% senken.",
            },
            {
                title: "Erstklassige F&E und Tests",
                desc: "FS verfügt über ein erstklassiges Forschungs- und Entwicklungszentrum und nutzt NVIDIA® MQM9790-Switches, ConnectX®-7-Netzwerkkarten und andere Produkte, um eine Testplattform für Volllastübertragungstests aufzubauen, um Stabilität sicherzustellen.",
            },
            {
                title: "Partitionsdesign",
                desc: "Das Design der Netzwerkpartitionen (Computing, Speicher, In-Band-Management und Out-of-Band-Management) trägt dazu bei, verschiedene Geschäftsbereiche zu isolieren und die Verkehrskomplexität zu reduzieren.",
            },
            {
                title: "Flexible Skalierbarkeit",
                desc: "Die Spine-Leaf-Layer-2-Netzwerkarchitektur erfüllt die aktuellen Anforderungen an den Netzwerkbetrieb und bietet gleichzeitig Flexibilität und Zuverlässigkeit für zukünftige Geschäftserweiterungen.",
            },
        ],
    },
    resources: {
        title: "Ressourcen",
        list: [
            {
                title: "Funktionslösungen",
                linkText: ["PicOS® für automatisierte Fahrlösung", "Verbesserung von HPC durch HGC H100 RoCE Netzwerk", "Verstärken Sie HPC mit FS InfiniBand-Transceivern und -Kabeln"],
            },
            {
                title: "Blogs",
                linkText: [
                    "Überblick zum Netzwerk und zur Architektur von InfiniBand",
                    "Pica8 AmpCon-DC: Ihr Partner für offene Netzwerkautomatisierung",
                    "Tipps zu geeigneten InfiniBand-Produkten für High-Performance-Computing",
                ],
            },
            {
                title: "Nachrichten",
                linkText: [
                    "Switches von FS unterstützen jetzt PicOS® für ein einheitliches Netzwerkerlebnis",
                    "Der Aufstieg von HPC-Rechenzentren: FS stärkt Rechenzentren der nächsten Generation",
                    "FS stellt Hochgeschwindigkeits-Netzwerkkonnektivitätslösung der nächsten Generation vor, um das Zeitalter von HPC zu beschleunigen",
                ],
            },
            {
                title: "Dokumente",
                linkText: ["PicOS® für H100 InfiniBand-Lösung Testbericht", "H100 InfiniBand-Lösung Lieferhandbuch", "800G&400G Transceiver Abnahmetest-Leitfaden"],
            },
        ],
    },
    contactUs: {
        title: "Sie sind startklar?",
        desc: "Haben Sie eine Frage zum Kauf einer Lösung, an der Sie interessiert sind? Füllen Sie das Formular aus und wir antworten innerhalb eines Arbeitstags.",
    },
    headTop: {
        href: "/solutions/picos-for-h100-infiniband-solution-S3000.html",
        name: "H100 InfiniBand-Lösung",
    },
    meta: {
        title: "PicOS® und AmpCon-DC für NVIDIA® InfiniBand H100 Netzwerk",
        description:
            "Entdecken Sie, wie FS H100 InfiniBand-Lösungen Hochleistungs-Computing-Netzwerke revolutionieren und die Effizienz um 20 % steigern. Mit der PicOS-Software und dem AmpCon-Netzwerkcontroller genießen Benutzer eine nahtlose Konfiguration und Wartung, was die Sicherheit erhöht und HPC-Workflows beschleunigt.",
    },
}
