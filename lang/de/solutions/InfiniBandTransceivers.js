export default {
    banner: {
        title: "Übersicht über die Konnektivitätslösung für InfiniBand-Transceiver und -Kabel",
        desc: "Entdecken Sie InfiniBand-Transceiver und -Kabel für GPU-beschleunigtes Rechnen.",
        crumbList: [
            {
                name: "Startseite",
                url: "/",
            },
            {
                name: "HPC-Netzwerke",
                url: "/solutions/hpc-networking-2000.html",
            },
            {
                name: "Lösung-ID: S3001",
                url: "/solutions/infiniband-transceivers-and-cables-connectivity-solution-overview-S3001.html",
            },
        ],
    },
    nav: {
        list: ["Übersicht", "Konnektivitäten", "Produkte", "Ressourcen"],
        btn: "Kontakt",
    },
    overview: {
        title: "Übersicht",
        desc: "FS bietet eine Reihe von 800G NDR-, 400G NDR-, 200G HDR-, 100G EDR- und 56/40G FDR-InfiniBand-Produkten für Hochgeschwindigkeits-Rechenzentren und High-Computing-Anwendungen. Diese Produkte ermöglichen flexible Verbindungen zwischen Switches, Switch-to-Smart NIC und GPU-Servern und erfüllen verschiedene Entfernungs- und Leistungsanforderungen.",
        introduction: [
            {
                title: "Optische InfiniBand-Transceiver",
                subTitle: [
                    "Eingebauter Markenchip für eine stabile Datenübertragung",
                    "Hohe Bandbreite, niedrige Kosten, geringe Latenz, hohe Reichweite",
                    "Zertifiziert für InfiniBand-End-to-End-Systeme wie NDR, HDR, EDR und FDR",
                    "100 % vom Hersteller verifiziert, perfekt kompatibel mit NVIDIA-Geräten wie der QM9700/8700-Serie, den CX6/7-Adaptern usw.",
                ],
            },
            {
                title: "InfiniBand-Kabel",
                subTitle: [
                    "Kostengünstige Verbindungen mit niedriger Latenz, geringe Einfügeverlusten und hoher Geschwindigkeit",
                    "Für höhere Bandbreiten in Hyperscale-Rechenzentren und Cloud-Infrastrukturen",
                    "Vereinfacht das Patching und bietet eine kostengünstige Möglichkeit für kurze Verbindungen",
                    "100 % vom Hersteller verifiziert, perfekt kompatibel mit NVIDIA-Geräten wie der QM9700/8700-Serie, den CX6/7-Adaptern usw.",
                ],
            },
        ],
    },
    connectivitys: {
        title: "Konnektivitäten",
        type: [
            {
                title: "800G NDR InfiniBand Transceiver",
                model: [
                    {
                        modelTitle: "OSFP-SR8-800G",
                        modelContent: [
                            `Verwenden Sie den <a href="/products/205113.html">800G OSFP SR8</a>(mit gerippter Oberfläche) zusammen mit den <a href="/products/208163.html">MMF MPO-12(APC) Kabeln</a> für eine direkte Verbindung mit NVIDIA Quantum-2 IB Switches.`,
                            `Verwenden Sie den <a href="/products/205113.html">800G OSFP SR8</a> (mit gerippter Oberfläche) zusammen mit zwei <a href="/products/200963.html">400G OSFP SR4 (mit flacher Oberfläche) </a>und <a href="/products/208163.html">MMF MPO-12 (APC) Kabeln</a>, um eine Verbindung zwischen dem NVIDIA Quantum-2 IB Switch und zwei<a href="/products/168437.html">200G QSFP56 HDR NICs</a> herzustellen.`,
                        ],
                    },
                    {
                        modelTitle: "OSFP-SR8-800G-FL",
                        modelContent: [
                            `Verwenden Sie den <a href="/products/205113.html">800G OSFP SR8</a> (mit flacher Oberfläche) zusammen mit den <a href="/products/208163.html">MMF MPO-12(APC) Kabeln</a>, um eine Verbindung zwischen dem NVIDIA Quantum-2 IB Switch und dem DGX H100 GPU-Server herzustellen.`,
                        ],
                    },
                    {
                        modelTitle: "OSFP-DR8-800G",
                        modelContent: [
                            `Verwenden Sie den <a href="/products/205319.html">800G OSFP DR8</a> (mit gerippter Oberfläche) zusammen mit den <a href="/products/68018.html">SMF MPO-12 (APC) Kabeln</a>  für eine direkte Verbindung mit NVIDIA Quantum-2 IB Switches.`,
                            `Verwenden Sie den  <a href="/products/205319.html">800G OSFP DR8</a> (mit gerippter Oberfläche) zusammen mit zwei 400G OSFP DR4 (mit flacher Oberfläche) und <a href="/products/68018.html">SMF MPO-12 (APC) Kabeln</a> , um eine Verbindung zwischen dem NVIDIA Quantum-2 IB Switch und zwei<a href="/products/168437.html">200G QSFP56 HDR NICs</a> herzustellen.`,
                        ],
                    },
                    {
                        modelTitle: "OSFP-2FR4-800G",
                        modelContent: [
                            `Verwenden Sie den <a href="/products/205315.html">800G OSFP 2FR4</a> (mit gerippter Oberfläche) zusammen mit <a href="/products/40191.html">SMF LC (UPC) Kabeln</a> für eine direkte Verbindung mit NVIDIA Quantum-2 IB Switches.`,
                        ],
                    },
                ],
            },
            {
                title: "800G NDR InfiniBand Kabel",
                model: [
                    {
                        modelTitle: "OSFP-800G-PCxxx",
                        modelContent: [`Verwenden Sie das<a href="/products/204993.html"> 800G OSFP (mit gerippter Oberfläche) DAC</a> für eine direkte Verbindung mit NVIDIA Quantum-2 IB Switches.`],
                    },
                    {
                        modelTitle: "OSFP-800G-2QPCxxx",
                        modelContent: [
                            `Verwenden Sie das  <a href="/products/213769.html">800G OSFP (mit gerippter Oberfläche)auf 2x400G QSFP112 DAC</a>, um eine Verbindung zwischen dem NVIDIA Quantum-2 IB Switch und zwei 400G QSFP112 NDR NICs herzustellen.`,
                        ],
                    },
                    {
                        modelTitle: "OSFP-800G-2OFLPCxxx",
                        modelContent: [
                            `Verwenden Sie das <a href="/products/205005.html"> 800G OSFP (mit gerippter Oberfläche)auf 2x<a href="/products/219557.html">400G OSFP (mit flacher Oberfläche) DAC</a></a>, um eine Verbindung zwischen dem NVIDIA Quantum-2 IB Switch und zwei<a href="/products/168437.html">200G QSFP56 HDR NICs</a> herzustellen.`,
                        ],
                    },
                    {
                        modelTitle: "OSFP-800G-4QPCxxx",
                        modelContent: [
                            `Verwenden Sie das <a href="/products/210701.html"> 800G OSFP (mit gerippter Oberfläche)auf 4x200G QSFP112 DAC</a>, um eine Verbindung zwischen dem NVIDIA Quantum-2 IB Switch und zwei 200G Dual-Port QSFP112 NDR NICs herzustellen.`,
                        ],
                    },
                    {
                        modelTitle: "OSFP-800G-4OFLPCxxx",
                        modelContent: [
                            `Verwenden Sie das  <a href="/products/204983.html">800G OSFP (mit gerippter Oberfläche)auf 4x200G OSFP (mit flacher Oberfläche) DAC</a>, um eine Verbindung zwischen dem NVIDIA Quantum-2 IB Switch und vier  <a href="/products/177272.html">200G OSFP NDR NICs</a>herzustellen.`,
                        ],
                    },
                ],
            },
            {
                title: "400G NDR InfiniBand Transceiver",
                model: [
                    {
                        modelTitle: "OSFP-SR4-400G-FL",
                        modelContent: [
                            `Verwenden Sie den <a href="/products/205113.html">800G OSFP SR8</a>  (mit gerippter Oberfläche) zusammen mit zwei <a href="/products/200963.html">400G OSFP SR4 (mit flacher Oberfläche) </a>und den <a href="/products/208163.html">MMF MPO-12 (APC) Kabeln</a>, um eine Verbindung zwischen dem NVIDIA Quantum-2 IB Switch und zwei <a href="/products/177272.html">400G OSFP NDR NICs</a> herzustellen.`,
                        ],
                    },
                    {
                        modelTitle: "OSFP-DR4-400G-FL",
                        modelContent: [
                            `Verwenden Sie den <a href="/products/205319.html">800G OSFP DR8</a> (mit gerippter Oberfläche) zusammen mit zwei 400G OSFP DR4 (mit flacher Oberfläche) und den <a href="/products/68018.html">SMF MPO-12 (APC) Kabeln</a> , um eine Verbindung zwischen dem NVIDIA Quantum-2 IB Switch und zwei<a href="/products/177272.html">400G OSFP NDR NICs</a> herzustellen.`,
                        ],
                    },
                    {
                        modelTitle: "QSFP112-SR4-400G",
                        modelContent: [
                            `Verwenden Sie das <a href="/products/205319.html">800G OSFP SR8</a>(mit gerippter Oberfläche) zusammen mit zwei <a href="/products/241109.html">400G QSFP112 SR4</a> und <a href="/products/208163.html">MMF MPO-12(APC) Kabeln</a>, um eine Verbindung zwischen dem NVIDIA Quantum-2 IB Switch und zwei <a href="/products/212163.html">400G QSFP112 NDR NICS</a> herzustellen.`,
                        ],
                    },
                    {
                        modelTitle: "QSFP112-DR4-400G",
                        modelContent: [
                            `Verwenden Sie das  <a href="/products/205319.html">800G OSFP DR8</a> (mit gerippter Oberfläche) zusammen mit zwei <a href="/products/241111.html">400G QSFP112 DR4</a> und <a href="/products/68018.html">SMF MPO-12(APC) Kabeln</a> , um eine Verbindung zwischen dem NVIDIA Quantum-2 IB Switch und zwei <a href="/products/212163.html">400G QSFP112 NDR NICS</a> herzustellen.`,
                        ],
                    },
                    {
                        modelTitle: "QSFP112-FR4-400G",
                        modelContent: [
                            `Verwenden Sie das <a href="/products/205315.html">800G OSFP 2FR4</a> (mit gerippter Oberfläche) zusammen mit zwei <a href="/products/241113.html">400G QSFP112 FR4</a> und <a href="/products/40191.html">SMF LC(UPC) Kabeln</a> , um eine Verbindung zwischen dem NVIDIA Quantum-2 IB Switch und zwei <a href="/products/212163.html">400G QSFP112 NDR NICS</a> herzustellen.`,
                        ],
                    },
                    {
                        modelTitle: "QSFP112-XDR4-400G",
                        modelContent: [
                            `Verwenden Sie das 800G OSFP XDR4 (mit gerippter Oberfläche) zusammen mit zwei <a href="/products/241115.html">400G QSFP112 XDR4</a> und <a href="/products/68018.html">SMF MPO-12(APC) Kabeln</a>, um eine Verbindung zwischen dem NVIDIA Quantum-2 IB Switch und zwei <a href="/products/212163.html">400G QSFP112 NDR NICS</a> herzustellen.`,
                        ],
                    },
                ],
            },
            {
                title: "400G NDR InfiniBand Kabel",
                model: [
                    {
                        modelTitle: "OSFPFL-400G-PCxxx",
                        modelContent: [
                            `Verwenden Sie das <a href="/products/219557.html">400G OSFP (mit flacher Oberfläche) DAC</a> für eine direkte Verbindung mit <a href="/products/168437.html">200G QSFP56 HDR NICs</a>.`,
                        ],
                    },
                    {
                        modelTitle: "OSFP-400G-2QPCxxx",
                        modelContent: [
                            `Verwenden Sie das<a href="/products/205391.html"> 400G OSFP (mit gerippter Oberfläche) auf 2x200G QSFP56 DAC</a> für eine direkte Verbindung mit NVIDIA Quantum IB Switches.`,
                            `Verwenden Sie das<a href="/products/205391.html"> 400G OSFP (mit gerippter Oberfläche) auf 2x200G QSFP56 DAC</a> , um eine Verbindung zwischen dem NVIDIA Quantum-2 IB Switch und zwei <a href="/products/168437.html">200G QSFP56 HDR NICs</a> herzustellen.`,
                        ],
                    },
                    {
                        modelTitle: "OSFP-400G-4QPCxxx",
                        modelContent: [
                            `Verwenden Sie das <a href="/products/224827.html"> 400G OSFP (mit gerippter Oberfläche) auf 4x100G QSFP56 DAC</a> für eine direkte Verbindung mit NVIDIA Quantum IB Switches.`,
                            `Verwenden Sie das <a href="/products/224827.html"> 400G OSFP (mit gerippter Oberfläche) auf 4x100G QSFP56 DAC</a> , um eine Verbindung zwischen dem NVIDIA Quantum-2 IB Switch und zwei <a href="/products/168436.html">100G Dual-Port QSFP56 HDR NICs</a> herzustellen.`,
                        ],
                    },
                    {
                        modelTitle: "OSFP-400G-2QAOxxx",
                        modelContent: [
                            `Verwenden Sie das<a href="/products/205337.html">400G OSFP(Finned-top) to 2x200G QSFP56 AOC</a> für eine direkte Verbindung mit NVIDIA Quantum-2 IB Switches.`,
                            `Verwenden Sie das<a href="/products/205337.html">400G OSFP(Finned-top) to 2x200G QSFP56 AOC</a> , um eine Verbindung zwischen dem NVIDIA Quantum-2 IB Switch und zwei <a href="/products/168437.html">200G QSFP56 HDR NICs</a> herzustellen.`,
                        ],
                    },
                ],
            },
            {
                title: "200G HDR InfiniBand Transceiver",
                model: [
                    {
                        modelTitle: "QSFP-SR4-200G",
                        modelContent: [
                            `Verwenden Sie den <a href="/products/205349.html">200G QSFP56 FR4</a> zusammen mit den <a href="/products/68017.html">MMF MPO-12 (UPC) Kabeln</a> für eine direkte Verbindung mit NVIDIA Quantum IB Switches.`,
                        ],
                    },
                    // {
                    //     modelTitle: "QSFP-FR4-200G",
                    //     modelContent: [
                    //         `Verwenden Sie den <a href="/products/205349.html">200G QSFP56 FR4</a>  zusammen mit <a href="/products/40191.html">SMF LC (UPC) Kabeln</a> für eine direkte Verbindung mit NVIDIA Quantum IB Switches.`,
                    //     ],
                    // },
                ],
            },
            {
                title: "200G HDR InfiniBand Kabel",
                model: [
                    {
                        modelTitle: "QSFP-200G-PCxxx",
                        modelContent: [
                            `Verwenden Sie das <a href="/products/205043.html">200G QSFP56 DAC</a> für eine direkte Verbindung mit NVIDIA Quantum IB Switches.`,
                            `Verwenden Sie das <a href="/products/205043.html">200G QSFP56 DAC</a> für eine direkte Verbindung zwischen dem NVIDIA Quantum IB Switch und<a href="/products/168437.html">200G QSFP56 HDR NICs</a>.`,
                        ],
                    },
                    {
                        modelTitle: "QSFP-200G-ACxxx",
                        modelContent: [
                            `Verwenden Sie das <a href="/products/148856.html">200G QSFP56 ACC</a> für eine direkte Verbindung mit NVIDIA Quantum IB Switches.`,
                            `Verwenden Sie das <a href="/products/148856.html">200G QSFP56 ACC</a> für eine direkte Verbindung zwischen dem NVIDIA Quantum IB Switch und<a href="/products/168437.html">200G QSFP56 HDR NICs</a>.`,
                        ],
                    },
                    {
                        modelTitle: "QSFP-200G-AOxxx",
                        modelContent: [
                            `Verwenden Sie das <a href="/products/166751.html">200G QSFP56 AOC</a> für eine direkte Verbindung mit NVIDIA Quantum IB Switches.`,
                            `Verwenden Sie das  <a href="/products/166751.html">200G QSFP56 AOC</a>  für eine direkte Verbindung zwischen dem NVIDIA Quantum IB Switch und<a href="/products/168437.html">200G QSFP56 HDR NICs</a>.`,
                        ],
                    },
                    {
                        modelTitle: "QSFP-200G-2QPCxxx",
                        modelContent: [
                            `Verwenden Sie das <a href="/products/167426.html">200G QSFP56 auf 2x100G QSFP56 DAC</a> für eine direkte Verbindung mit NVIDIA Quantum IB Switches.`,
                            `Verwenden Sie das <a href="/products/167426.html">200G QSFP56 auf 2x100G QSFP56 DAC</a>, um eine Verbindung zwischen dem NVIDIA Quantum IB Switch und <a href="/products/168436.html">100G Dual-Port QSFP56 HDR100 NICs</a> herzustellen.`,
                        ],
                    },
                    {
                        modelTitle: "QSFP-200G-2QACxxx",
                        modelContent: [
                            `Verwenden Sie das <a href="/products/166186.html">200G QSFP56 auf 2x100G QSFP56 ACC</a> für eine direkte Verbindung mit NVIDIA Quantum IB Switches.`,
                            `Verwenden Sie das <a href="/products/166186.html">200G QSFP56 auf 2x100G QSFP56 ACC</a>, um eine Verbindung zwischen dem NVIDIA Quantum IB Switch und <a href="/products/168436.html">100G Dual-Port QSFP56 HDR100 NICs</a> herzustellen.`,
                        ],
                    },
                    {
                        modelTitle: "QSFP-200G-2QAOxxx",
                        modelContent: [
                            `Verwenden Sie das  <a href="/products/166755.html">200G QSFP56 auf 2x100G QSFP56 AOC</a> für eine direkte Verbindung mit NVIDIA Quantum IB Switches.`,
                            `Verwenden Sie das  <a href="/products/166755.html">200G QSFP56 auf 2x100G QSFP56 AOC</a>, um eine Verbindung zwischen dem NVIDIA Quantum IB Switch und <a href="/products/168436.html">100G Dual-Port QSFP56 HDR100 NICs</a> herzustellen.`,
                        ],
                    },
                    {
                        modelTitle: "QSFP-2Q200G-2QAOxxx",
                        modelContent: [`Verwenden Sie das<a href="/products/205367.html">2x200G QSFP56 auf 2x200G QSFP56 AOC</a> für eine direkte Verbindung mit NVIDIA Quantum IB Switches.`],
                    },
                ],
            },
        ],
    },
    products: {
        title: "Produktliste",
        category: [
            {
                title: "InfiniBand-Transceiver",
                headerArr: ["Kategorie", "Konfiguration", "Formfaktor", "Wellenlänge", "Reichweite", "Stecker", "Preis"],
                list: [
                    ["OSFP-SR8-800G", "8x100G-PAM4 Elektrisch zu Dual 4x100G-PAM4 Optisch Parallel", "OSFP Rippenplatte", "850nm", "50m", "Dual MPO-12/APC", "950,81 € (inkl. MwSt.)"],
                    ["OSFP-SR8-800G-FL", "8x100G-PAM4 Elektrisch zu Dual 4x100G-PAM4 Optisch Parallel", "OSFP Flaches Oberteil", "850nm", "50m", "Dual MPO-12/APC", "1.187,62 € (inkl. MwSt.)"],
                    ["OSFP-DR8-800G", "8x100G-PAM4 Elektrisch zu Dual 4x100G-PAM4 Optisch Parallel", "OSFP Rippenplatte", "1310nm", "500m", "Dual MPO-12/APC", "1.425,62 € (inkl. MwSt.)"],
                    ["OSFP-2FR4-800G", "8x100G-PAM4 Elektrisch zu Dual 4x100G-PAM4 Optisch Multiplexed", "OSFP Rippenplatte", "1310nm", "2km", "Duplex LC/UPC", "1.782,62 € (inkl. MwSt.)"],
                    ["OSFP-SR4-400G-FL", "4x100G-PAM4 Elektrisch zu 4x100G-PAM4 Optisch Parallel", "OSFP Flaches Oberteil", "850nm", "50m", "MPO-12/APC", "831,81 € (inkl. MwSt.)"],
                    ["OSFP-DR4-400G-FL", "4x 100G-PAM4 Elektrisch zu 4x 100G-PAM4 Optisch Parallel", "OSFP Flaches Oberteil", "1310nm", "500m", "MPO-12/APC", "1.068,62 € (inkl. MwSt.)"],
                    ["QSFP-SR4-200G", "4x 50G-PAM4 Elektrisch zu 4x 50G-PAM4 Optisch Parallel", "QSFP56", "850nm", "100m", "MPO-12/UPC", "284,41 € (inkl. MwSt.)"],
                ],
            },
            {
                title: "InfiniBand DAC-Kabel",
                headerArr: ["Kategorie", "Konfiguration", "Formfaktor", "Längen (Meter)", "Kabelmantel", "Preis"],
                list: [
                    ["OSFP-800G-PCxxx", "8x 100G-PAM4 bis 8x 100G-PAM4", "OSFP Rippenplatte", "0.5m, 1m, 1.5m", "PVC (OFNR)", "153,51 € -201,11 € (inkl. MwSt.)"],
                    ["OSFP-800G-2QPCxxx", "8x 100G-PAM4 bis 8x 100G-PAM4", "OSFP Rippenplatte zu 2 x QSFP112", "0.5m, 1m, 1.5m, 2m", "PVC (OFNR)", "213,01 € -284,41 € (inkl. MwSt.)"],
                    ["OSFP-800G-2OFLPCxxx", "8x 100G-PAM4 bis Dual 4x 100G-PAM4", "OSFP Rippenplatte zu 2 x OSFP Flachplatte", "0.5m, 1m, 1.5m, 2m", "PVC (OFNR)", "213,01 €-284,41 € (inkl. MwSt.)"],
                    ["OSFP-800G-4QPCxxx", "Zweimal 4x 50G-PAM4 bis Vierfach 2x 50G-PAM4", "OSFP Rippenplatte zu 4 x QSFP56", "0.5m, 1m, 1.5m, 2m, 2.5m, 3m", "PVC (OFNR)", "213,01 €-284,41 € (inkl. MwSt.)"],
                    ["OSFP-800G-4OFLPCxxx", "8x 100G-PAM4 bis Vierfach 2x 100G-PAM4", "OSFP Rippenplatte zu 4 x OSFP Flachplatte", "0.5m, 1m, 1.5m, 2m", "PVC (OFNR)", "332,01 €-439,11 € (inkl. MwSt.)"],
                    ["OSFPFL-400G-PCxxx", "4x 100G-PAM4 bis 4x 100G-PAM4", "OSFP Flaches Oberteil", "0.5m, 1m, 1.5m, 2m", "PVC (OFNR)", "141,61 €-213,01 € (inkl. MwSt.)"],
                    ["OSFP-400G-2QPCxxx", "Dual 4x 50G-PAM4 zu Dual 4x 50G-PAM4", "OSFP Rippenplatte zu 2 x QSFP56", "1m, 1.5m, 2m", "OFNP", "213,01 € -165,41 € (inkl. MwSt.)"],
                    ["OSFP-400G-4QPCxxx", "Dual 4x 50G-PAM4 bis Quad 2x 50G-PAM4", "OSFP Rippenplatte zu 4 x QSFP56", "0.5m, 1m, 1.5m, 2m, 2.5m, 3m", "PVC (OFNR)", "153,51 €-260,61 € (inkl. MwSt.)"],
                    ["QSFP-200G-PCxxx", "4x 50G-PAM4 bis 4x 50G-PAM4", "QSFP56", "0.5m, 1m, 1.5m, 2m", "PVC (OFNR)", "76,16 €-94,01 € (inkl. MwSt.)"],
                    ["QSFP-200G-ACxxx", "4x 50G-PAM4 bis 4x 50G-PAM4", "QSFP56", "3m, 5m, 7m", "PVC (OFNR)", "214,20 €-309,40 € (inkl. MwSt.)"],
                    ["QSFP-200G-2QPCxxx", "4x 50G-PAM4 bis Dual 2x 50G-PAM4", "QSFP56 zu 2 x QSFP56", "0.5m, 1m, 1.5m, 2m", "PVC (OFNR)", "88,06 €-102,34 € (inkl. MwSt.)"],
                    ["QSFP-200G-2QACxxx", "4x 50G-PAM4 zu Dual 2x 50G-PAM4", "QSFP56 zu 2 x QSFP56", "3m, 5m, 7m", "PVC (OFNR)", "88,06 €-102,34 € (inkl. MwSt.)"],
                ],
            },
            {
                title: "InfiniBand AOC-Kabel",
                headerArr: ["Kategorie", "Konfiguration", "Formfaktor", "Längen (Meter)", "Kabelmantel", "Preis"],
                list: [
                    ["OSFP-400G-2QAOxxx", "Dual 4x50G-PAM4 zu Dual 4x50G-PAM4", "Dual 4x50G-PAM4 zu Dual 4x50G-PAM4", "3m, 5m, 10m, 15m, 20m, 30m", "LSZH", "1.187,62 €-1.348,27 € (inkl. MwSt.)"],
                    ["QSFP-200G-AOxxx", "4x 50G-PAM4 zu 4x 50G-PAM4", "4x 50G-PAM4 zu 4x 50G-PAM4", "1m, 2m, 3m, 5m, 10m, 15m, 20m, 30m, 50m, 100m", "LSZH", "629,51 €-865,13 € (inkl. MwSt.)"],
                    ["QSFP-200G-2QAOxxx", "4x 50G-PAM4 zu zweimal 2x 50G-PAM4", "4x 50G-PAM4 zu zweimal 2x 50G-PAM4", "1m, 2m, 3m, 5m, 10m, 15m, 20m, 30m, 50m, 100m", "OFNP", "855,61 €-924,63 € (inkl. MwSt.)"],
                    ["QSFP-2Q200G-2QAOxxx", "4x 25G-NRZ zu 4x 25G-NRZ", "4x 25G-NRZ zu 4x 25G-NRZ", "1m, 2m, 3m, 5m, 10m, 15m, 20m, 30m, 50m, 100m", "OFNP", "1.401,82 € -1.782,62 € (inkl. MwSt.)"],
                ],
            },
        ],
    },
    resources: {
        title: "Ressourcen",
        list: [
            {
                title: "Weitere Lösungen",
                linkText: ["PicOS® für H100 InfiniBand-Lösung", "Verstärken Sie HPC mit FS InfiniBand-Transceivern und -Kabeln", "HPC-gestützte Lösung für autonomes Fahren"],
            },
            {
                title: "Fallstudien",
                linkText: ["FS hilft einem Startup für autonome Fahrzeuge beim Aufbau eines Rechenzentrumsnetzes"],
            },
            {
                title: "Blogs",
                linkText: [
                    "Vollständiger Leitfaden für InfiniBand-Transceiver und InfiniBand-Kabel von FS",
                    "Tipps zu geeigneten InfiniBand-Produkten für High-Performance-Computing",
                    "Need for Speed – Entwicklung der InfiniBand-Netzwerkbandbreite",
                ],
            },
            {
                title: "Videos",
                linkText: [
                    "Optischer OSFP-SR8-800G Leistungstest | FS",
                    "Einführung zum optischen OSFP-SR8-800G Infiniband-Transceivermodul | FS",
                    "Einführung zum 800G OSFP auf 2x OSFP Direct Attach Kupfer-Breakout-Kabel | FS",
                ],
            },
        ],
    },
    meta: {
        title: "Übersicht über die Konnektivitätslösung für InfiniBand-Transceiver und -Kabel | FS",
        description: "FS bietet Lösungen für 40G, 56G, 100G, 200G, 400G, 800G, InfiniBand DAC, AOC-Kabel und optische Transceiver, Netzwerkkarten und End-to-End-Technologien.",
    },
}
