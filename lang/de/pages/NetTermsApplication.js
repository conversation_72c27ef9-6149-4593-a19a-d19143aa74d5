export default {
    netTermsApplication: "Antrag für Kauf auf Rechnung",
    asterisk: "<PERSON>chen(*) kennzeichnen Pflichtfelder.",
    netTerms: "Kauf auf Rechnung",
    businessInformation: {
        title: "Geschäftsinformationen",
        tipText:
            "Um den Prüfungsprozess zu beschleunigen, stellen Sie bitte so viele weitere Informationen wie möglich zur Verfügung, einschließlich der Bescheinigung über die Eintragung des Unternehmens, Bescheinigung einer öffentlichen Behörde/Selbstauskunft, Steuererklärung, Dokument,  W9-Formular, Finanzbericht.",
        form: {
            companyName: "Firmenname*",
            address: " Adresse*",
            phone: "Telefon*",
            city: "Stadt*",
            state: "Bundesland/Provinz",
            country: " Land/Region*",
            zip: "Postleitzahl*",
            year: "Geschäftsjahr*",
            federal: "Ust-ID-Nr.",
            duns: "DUNS-Nummer",
            vat_number: "VAT-Nummer",
            web: "Website*",
            businessScope: "Geschäftsumfang*",
            capital: "Kapital",
            employees: "Beschäftigte",
            established: "Gegründet",
            annualSales: "Jährlicher Umsatz",
            businessPartners: "Geschäftspartner",
            tips: "In order to shorten the review period, we would appreciate it if you could submit the following corporate verification documents along with the credit application form. A certified copy of the company registration or certificate of current matters, financial statements, or financial reports.",
        },
    },
    institutionType: {
        title: "Art der Einrichtung",
        tips: "Bitte wählen Sie die entsprechende Option unten aus, um Ihren Einrichtungstyp anzugeben. Beachten Sie, dass die erforderlichen Informationen je nach Auswahl variieren können.",
        public: "Öffentliche Einrichtung",
        company: "Unternehmen",
        form: {
            postcode: "Postleitzahl*",
            ABN: "ABN*",
            industryDivision: "Industrieabteilung*",
            estimated: "Geschätzte monatliche Ausgaben*",
            choose: "Wählen Sie eine Option",
        },
    },
    billingInformation: {
        title: "Informationen zur Rechnungsstellung",
        tips: 'Wir speichern die von Ihnen angegebene Rechnungsadresse in Ihrem <a href="xxxx" target="_blank">Adressbuch</a> zur zukünftigen Verwendung. Um den steuerlichen Vorschriften zu entsprechen, geben Sie bitte rechtzeitig die MwSt.-/Steuernummer des Unternehmens an.',
        same_as: "Gleich wie Geschäftsinformationen",
        form: {
            company: "Firmenname*",
            address: "Adresse*",
            phone: "Telefon*",
            city: "Stadt*",
            state: "Bundesland/Provinz",
            country: "Land*",
            zip: "Postleitzahl*",
            reason: "Der Grund für die Abweichung von der Geschäftsadresse*",
        },
    },
    applicationType: {
        title: "Antragstyp",
        netTerm: "Zahlungsfrist* Zahlbar innerhalb von",
        amount: "Betrag*",
        bankReference: {
            title: "Bankreferenz*",
            text1: "Ein Bankreferenzschreiben oder eine Bankverbindung sind erforderlich.",
            upload: "Bankreferenzdatei hochladen",
            fill: "Füllen Sie die folgenden Informationen aus",
            form: {
                bankName: "Name der Bank*",
                contactName: "Name der Kontaktperson*",
                address: "Adresse*",
                phone: "Telefon*",
                city: "Stadt*",
                state: "Bundesland/Provinz",
                country: "Land/Region*",
                zip: "Postleitzahl*",
                account: "Kontonummer*",
            },
        },
        businessReference: {
            title: "Geschäftsreferenz*",
            text: "Bitte geben Sie uns mindestens drei (3) andere Unternehmen an, mit denen Ihr Unternehmen einen Kredit abgeschlossen hat und bei denen Sie in den letzten drei (3) Monaten Einkäufe getätigt haben.",
            text1: `Wenn Sie ein Unternehmen, ein Einzelunternehmer, ein Verein, eine nichtstaatliche Schule oder eine Kantine sind, müssen Sie die "Business Refference" ausfüllen.`,
            text2: 'Wenn Sie eine Wohltätigkeitsorganisation, eine gemeinnützige Organisation, eine staatliche Einrichtung oder eine staatliche Schule sind, ist "Business Reference" nicht erforderlich.',
            upload: "Handelsreferenzdatei hochladen",
            fill: "Füllen Sie die folgenden Informationen aus",
            email: "E-Mail*",
        },
        contactInformation: {
            title: "Ihre Kontaktinformationen",
            termsConditions: "Allgemeine Geschäftsbedingungen für Kauf auf Rechnung",
            companys: {
                title1: "Referenznummer #1",
                title2: "Referenznummer #2",
                title3: "Referenznummer #3",
            },
        },
        submitApplication: "Antrag unterschreiben und einreichen",
        from: {
            companyName: "Company Name*",
            address: "Address*",
            phone: "Phone*",
            city: "City*",
            state: "State/Province",
            country: "Country*",
            zipCode: "*Zip Code",
            yearStated: "Year Stated*",
            federalID: "Federal ID/Tax ID (for U.S. customers)",
            dunsNumber: "DUNS Number",
            website: "Website*",
            first_name: "Vorname*",
            last_name: "Nachname*",
            title: "Titel*",
            businessEmail: "Geschäftliche E-Mail*",
            phoneNumber: "Telefon Nummer*",
        },
        netList: {
            text1: "15 Tagen",
            text2: "30 Tagen",
            text3: "45 Tagen",
            text4: "60 Tagen",
        },
        amountList: {
            name1: "USD $",
            name2: "EUR €",
            name3: "GBP £",
            name4: "CAD C$",
            name5: "AUD $",
            name6: "CNY ￥",
            name7: "CHF Fr.",
            name8: "HKD $",
            name9: "JPY ¥",
            name10: "BRL R$",
            name11: "NOK kr.",
            name12: "DKK kr.",
            name13: "SEK Kr.",
            name14: "MXN $",
            name15: "NZD $",
            name16: "SGD S$",
            name17: "RUB ₽",
            name18: "RUB $",
            name19: "MYR RM",
            name20: "THB ฿",
            name21: "PHP ₱",
        },
        attentionList: {
            text1: "1. Alle Bestellungen, die bei FS auf Rechnung erteilt werden, sind innerhalb der jeweiligen Zahlungsfrist ab Versanddatum fällig und zu zahlen. ",
            text2: `2. Ist der Käufer (bezieht sich auf das Unternehmen, das im Abschnitt „Geschäftsinformationen“ aufgeführt ist; dasslbe gilt unten) Opfer höherer Gewalt und hat FS vor Fälligkeit schriftlich informiert, kann die Zahlung auf 15 Kalendertage nach Fälligkeit verschoben werden. `,
            text3: `3. Wenn der Käufer eine Zahlung nicht innerhalb von 15 Kalendertagen nach Fälligkeitsdatum leistet, wird FS unbeschadet aller anderen Rechte oder Rechtsmittel, die FS zur Verfügung stehen, den Kredit des Käufers beschränken und ist berechtigt, dem Käufer Zinsen in Höhe von einem Prozent (1%) der überfälligen Zahlung pro Monat bis zur vollständigen Zahlung zu berechnen (jeder Teilmonat wird für die Berechnung der Zinsen als voller Monat behandelt). `,
            text4: `4. Wenn der Käufer die Ware erhält und ein After-Sales-Problem durch FS verursacht wird, ist der Käufer berechtigt, die Zahlung auszusetzen, bis er ein repariertes oder ausgetauschtes Produkt erhält. Wenn das Problem nach dem Verkauf durch den Käufer verursacht wird, kann der Käufer die Bezahlung der Bestellung nicht verweigern.  `,
            text5: `5. Wenn ein Kreditauftrag storniert werden soll, informieren Sie bitte Ihren Account Manager vorab schriftlich darüber. FS wird den Vorgang prüfen, Ihnen Feedback geben und Sie über den Stornierungsprozess aufklären. Wenn die Stornierung der Bestellung durch FS verursacht wird (z.B. fehlerhafte oder falsche Produkte), ist der Käufer berechtigt, die Bestellung zu annullieren. Wenn die Stornierung der Bestellung durch den Käufer verursacht wird und der Käufer dies nicht im Vorfeld formell erklärt und mit FS ausgehandelt hat, kann die Bestellung nicht storniert werden.`,
            // text6: `6. Mit der Einreichung dieses Antrags ermächtigen Sie FS, die von Ihnen angegebenen Bank- und Geschäftsreferenzen zu überprüfen.`,
            text7: `6. Alle vom Käufer im Rahmen dieser Vereinbarung bereitgestellten Informationen sind vertraulich und Eigentum des Käufers. FS wird keine Informationen an Dritte weitergeben oder veröffentlichen, es sei denn, die Weitergabe, Veröffentlichung und Anwendung der vertraulichen Informationen wird vom Käufer schriftlich per E-Mail genehmigt.`,
            text8: `7. Sollten Sie Unstimmigkeiten oder Konflikte feststellen, rufen Sie bitte xxxx an oder senden eine E-Mail an Ihren Account Manager bei FS.`,
            text9: `8. Der Käufer hat das Recht, die relevanten Informationen über Net Terms Management zu ändern. Der Käufer ist dafür verantwortlich, die Authentizität, Richtigkeit und Vollständigkeit der geänderten Informationen zu bestätigen. Außerdem ist der Käufer für die vorgenommenen Änderungen verantwortlich und ist nicht verpflichtet, ein weiteres Dokument aufgrund der geänderten Informationen zu unterzeichnen.`,
            text10: `9. Die vom Erwerber zur Verfügung gestellten Daten haben die erforderliche Genehmigung des Betroffenen zur Weitergabe und Nutzung erhalten, und FS.com kann sie wie üblich erhalten und nutzen. Der Erwerber muss vermeiden, dass FS.com durch den Betroffenen in Anspruch genommen wird.`,
        },
        policys: {
            text1: `Ich stimme den <a href="javascript:;" class="policy">Datenschutzrichtlinien</a> und <a href="javascript:;" class="trems">AGB</a> von FS zu.`,
            text2: `Ich bin befugt, diesen Antrag im Namen der oben genannten Firma/Organisation/Institution zu unterzeichnen. `,
            text3: `Ich bestätige hiermit, dass alle oben genannten Informationen vollständig und korrekt sind. Alle Angaben wurden in dem Wissen gemacht, dass sie zur Bestimmung des Betrags und der Bedingungen des zu gewährenden Kredits verwendet werden sollen.`,
        },
    },
    apply: {
        errtext: "Kauf auf Rechnung ist nur für Geschäftskonten verfügbar. Wenn Sie Fragen haben, <a href='xxxx' target='_blank'>wenden Sie sich bitte an Ihren Account Manager.</a>",
        errtext2:
            "Bitte beachten Sie, dass Sie den Online-Kreditantrag nur einmal einreichen können. Wenn der Antrag zum ersten Mal fehlgeschlagen ist, können Sie das PDF-Antragsformular ausfüllen und Ihren Account Manager kontaktieren.",
        errsubmit:
            "Bitte beachten Sie, dass Sie den Antrag für Offline- Net Terms nur einmal stellen können. Wenn der Antrag zum ersten Mal fehlschlägt, können Sie das PDF-Antragsformular ausfüllen und Ihren Kundenbetreuer kontaktieren.",
    },
    inlineTips: {
        business: `Die registrierte Adresse Ihres Unternehmens wird hier automatisch eingetragen und kann nicht bearbeitet werden. Wenn Sie die Adresse aktualisieren müssen,wenden <a href="xxxx" target='_blank'>Sie sich bitte an Ihren Kundenbetreuer.</a>`,
        billing: `Gemäß den Anforderungen unseres Unternehmens an das Kreditmanagement für EU-Länder/Regionen muss Ihre Rechnungsadresse für Net Terms mit der registrierten Adresse Ihres Unternehmens übereinstimmen, so dass bei zukünftigen Bestellungen mit Net Terms nur noch diese Adresse als Rechnungsadresse verwendet werden kann.`,
        billing2: `Gemäß den Anforderungen unseres Unternehmens an die Kreditverwaltung müssen Ihr Firmenname und Ihr Land für Net Terms mit der registrierten Adresse Ihres Unternehmens übereinstimmen.`,
        vat_number: `Sie können von der Mehrwertsteuer befreit werden, wenn Sie eine gültige Steueridentifikationsnummer haben.`,
    },
    validate: {
        first_name: {
            first_name_required: "Dies ist ein Pflichtfeld.",
            first_name_max: "Der Vorname darf maximal 40 Zeichen lang sein.",
            first_entry_name_max: "Der Vorname darf maximal 35 Zeichen lang sein.",
            first_name_min: "Der Vorname muss mindestens 2 Zeichen lang sein.",
        },
        last_name: {
            last_name_required: "Dies ist ein Pflichtfeld.",
            last_name_max: "Der Nachname darf maximal 40 Zeichen lang sein.",
            last_entry_name_max: "Der Nachname darf maximal 35 Zeichen lang sein.",
            last_name_min: "Der Nachname muss mindestens 2 Zeichen lang sein.",
        },
        email: {
            email_required: "Dies ist ein Pflichtfeld.",
            email_valid: "Bitte geben Sie eine gültige E-Mail-Adresse ein.",
            email_validate2: "Bitte geben Sie eine gültige E-Mail-Adresse ein. (z.B. <EMAIL>)",
        },
        phone: {
            phone_required: "Dies ist ein Pflichtfeld.",
            phone_min: "Die Telefonnummer muss mindestens 6-stellig sein.",
            phone_validate: "Die Telefonnummer darf maximal 40 Zeichen lang sein.",
        },
        company: {
            company_required: "Dies ist ein Pflichtfeld.",
            company_validate: "cDer Firmenname darf maximal 120 Zeichen lang sein.",
        },
        address: {
            address_required: "Dies ist ein Pflichtfeld.",
            address_validate: "Die Adresse darf maximal 100 Zeichen lang sein.",
            address_validate1: "Die Adresse muss zwischen 4 und 35 Zeichen lang sein.",
        },
        city: {
            city_required: "Dies ist ein Pflichtfeld.",
            city_validate: "Die Stadt muss zwischen 2 und 40 Zeichen lang sein.",
        },
        state: {
            state_required: "Dies ist ein Pflichtfeld.",
            state_validate: "Das Bundesland/die Provinz muss zwischen 1 und 100 Zeichen lang sein.",
        },
        country: {
            country_required: "Dies ist ein Pflichtfeld.",
        },
        agreement: {
            agreePolicy: "Please make sure you agree to our Privacy Policy and Notice at Collection and Terms of Use.",
        },
        zip_code: {
            zip_code_required: " Dies ist ein Pflichtfeld.",
            zip_code_validate: "Die Postleitzahl muss zwischen 5 und 9 Zeichen lang sein.",
        },
        yearStated: {
            yearStated_required: "Dies ist ein Pflichtfeld.",
            yearStated_validate: "Das Geschäftsjahr muss zwischen 1 und 100 Zeichen lang sein.",
        },
        federal: {
            federal_required: "Dies ist ein Pflichtfeld.",
            federal_validate: "Die VAT-Nummer muss neun Ziffern enthalten.",
        },
        DUNS: {
            DUNS_required: "Dies ist ein Pflichtfeld.",
            DUNS_validate: "Die DUNS-Nummer muss neun Ziffern enthalten.",
        },
        website: {
            website_required: "Dies ist ein Pflichtfeld.",
            website_validate: "Die Website darf maximal 1280 Zeichen lang sein.",
        },
        reason: {
            reason_required: "Dies ist ein Pflichtfeld.",
            reason_validate: "reason must be between 1 and 200 characters long.",
        },
        amount: {
            amount_required: "Dies ist ein Pflichtfeld.",
            amount_validate: "Die Kontonummer darf maximal 100 Zeichen lang sein.",
        },
        file: {
            file_required: "Dies ist ein Pflichtfeld.",
        },
        title: {
            title_require: "Dies ist ein Pflichtfeld.",
            title_validate: "Der Titel darf maximal 100 Zeichen lang sein.",
        },
        bankName: {
            bankName_required: "Dies ist ein Pflichtfeld.",
            bankName_validate: "Der Bankname darf maximal 100 Zeichen lang sein.",
        },
        contactName: {
            contactName_required: "Dies ist ein Pflichtfeld.",
            contactName_validate: "Der Kontaktname darf maximal 100 Zeichen lang sein.",
        },
        accountNumber: {
            accountNumber_required: "Dies ist ein Pflichtfeld.",
            contactName_validate: "Die Kontonummer darf maximal 100 Zeichen lang sein.",
        },
        policys: {
            agreePolicy: "Bitte stimmen Sie unsere Datenschutzerklärung und AGB zu.",
            policys_required: "Dies ist ein Pflichtfeld.",
        },
        abn: {
            validate: "Die ABN muss elf Ziffern enthalten.",
        },
        payMumber: {
            require: "Der Betrag ist erforderlich",
        },
    },
    noSelect: "Nicht anwendbar für dieses Land.",
    errorTsips: `Sorry, we're experiencing a delay in generating the signature file. Please try again later.`,
    titleList: {
        choose: "Auswählen",
        management: "Management",
        procurement: "Beschaffung",
        sales: "Verkauf",
        administration: "Verwaltungsabteilung",
        accounting: "Buchhaltung",
        engineering: "Engineering",
        logisticsandstorage: "Logistik und Lagerung",
        marketing: "Marketing",
        operation: "Betrieb",
        owner: "Eigentümer",
        other: "Andere",
        inpPlaceholder: "Suchen Sie Ihren Titel",
    },
    headOffice: {
        location: "Standort der Hauptniederlassung",
        postalCode: "Postleitzahl*",
        PostalSearch: "PLZ-Suche",
        stateProvince: "Bundesland/Provinz*",
        city: "Stadt*",
        address: "Anschrift*",
        phoneNumber: "Telefon Nummer*",
    },
    officeLocation: {
        office: "Bürostandort",
        deliveryAddress: "Lieferadresse",
        sameLocation: "Gleiche Adresse wie der Standort der Hauptniederlassung",
        sameOffice: "Gleiche Adresse wie der Standort des Büros",
    },
    billingAddress: {
        bill: "Rechnungsadresse",
        payeeBank: "Bank des Zahlungsempfängers",
        bankName: "Name der Bank",
        bankCode: "Bankleitzahl",
        branchName: "Name der Filiale",
        branchCode: "Bankleitzahl",
        typeDeposit: "Art der Einlage",
        ordinaryDeposit: "Gewöhnliche Einlage",
        checkingAccount: "Girokonto",
        accountNo: "Konto-Nr.",
        accountName: "Name des Kontos",
        accountKatakana: "Name des Kontos (Katakana)",
    },
    paymentTerms: {
        title: "Zahlungsbedingungen",
        tips1: "Die Zahlung für Produkte sollte am Ende eines jeden Monats erfolgen (Banküberweisung, Paypal, Kreditkarte).",
        NetTerm: "Zahlungsfrist*",
        content1: "Monatsende, Zahlung am nächsten Monatsende",
        amount: "Betrag*",
        tips2: "Wenn Sie andere als die oben genannten Zahlungsbedingungen wünschen, füllen Sie bitte das untenstehende Formular aus oder wenden Sie sich an den zuständigen Kundenbetreuer.",
    },
    contactInformation: {
        title: "Kontaktinformationen",
        tips: "Wir werden die von Ihnen angegebenen Informationen verwenden, wenn wir Sie bezüglich Ihres Kreditantrags kontaktieren müssen.",
        lastName: "Nachname*",
        firstName: "Vorname*",
        jobTitle: "Titel*",
        businessEmail: "Geschäftliche E-Mail*",
        phoneNumber: "Telefon Nummer*",
    },
    applyInformation: {
        title: "Antragsinformationen",
        organizationName: "Organisationsname",
        des: "Beschreibung, wie Sie die API(s) nutzen werden",
    },
    accountsPayable: {
        title: "Kontaktinformationen des zahlungspflichtigen Kontos",
        tips: `Nachdem die Nettokonditionen Ihres Geschäftskontos genehmigt wurden, werden diese Daten verwendet, wenn wir Sie wegen Ihres Kontos kontaktieren müssen.`,
        isSame: "Gleich wie meine Kontaktinformationen",
        lastName: "Nachname*",
        firstName: "Vorname*",
        jobTitle: "Titel*",
        businessEmail: "Geschäftliche E-Mail*",
        phoneNumber: "Telefon Nummer*",
    },
    remarks: {
        remarks: "Bemerkungen",
        content: `Bitte legen Sie eines der folgenden Dokumente zum Nachweis Ihrer Identität vor: Geschäftskarte, Mitarbeiterausweis, Beschäftigungsnachweis o. Ä.`,
        list: "Um die Prüfung zu beschleunigen, bitten wir Sie, zusammen mit dem Kreditantrag eine beglaubigte Kopie des Handelsregisterauszugs, der Bescheinigung über die laufenden Geschäfte, der Jahresabschlüsse oder der Finanzberichte einzureichen.",
        plan: "Liste der Produkte, die Sie kaufen möchten / Jährlicher Einkaufsplan",
    },
    termsConditions: "Allgemeine Geschäftsbedingungen",
    unsameResaon: "Der Grund für die Abweichung vom Standort der Hauptniederlassung oder des Büros: ",
    employeeList: {
        option0: "Öffentliche Einrichtungen (z. B. Bildungswesen, Gesundheitswesen, Regierungsstellen usw.)",
        option1: "Mehr als 1000 Beschäftigte",
        option2: "200~1000 Beschäftigte",
        option3: "50~200 Beschäftigte",
        option4: "Weniger als 50 Beschäftigte",
    },
    upload: `ファイルアップロード`,
    placeholders: {
        eg: "z. B. Fertigung",
        eg1: "z. B. Fertigung",
        eg2: "z. B. 10 Millionen Yen",
        eg3: "Bitte auswählen",
        eg4: "z.B. 2020",
        eg5: "z. B. 100 Millionen Yen",
        eg6: "z. B. Bank of Mitsubishi UFJ",
        eg7: "z. B. 0005",
        eg8: "z. B. Niederlassung Tokio",
        eg9: "z. B. 123",
        eg10: "z. B. ********",
        eg11: "z. B. Taro Yamada",
        eg12: "z. B. Yamada Taro",
    },
    pdfFile: {
        title: "与信限度申請書（機密）",
    },
    accountPay: {
        title: "Kontaktinformationen der zu zahlenden Konten",
        tips: `Nachdem die Nettokonditionen Ihres Geschäftskontos genehmigt wurden, werden diese Daten verwendet, wenn wir Sie bezüglich Ihres Kontos kontaktieren müssen.`,
        isSame: "Wie meine Kontaktinformationen",
        form: {
            firstName: "Vorname*",
            lastName: "Nachname*",
            title: "Titel*",
            email: "Geschäftliche E-Mail*",
            phone: "Telefon Nummer*",
        },
    },
    success: {
        title: "Wir haben Ihre Antrag auf Nettokonditionen erhalten.",
        content: 'Wir werden Sie bald kontaktieren. Sie können den Fortschritt Ihrer Bewerbung in Ihren <a href="xxxx">Fällen</a> verfolgen.',
    },
    jpPolicy: {
        text1: `1. Die oben angegebenen Informationen werden für die Kreditwürdigkeitsprüfung verwendet.  Auf der Grundlage der Ergebnisse der Prüfung und Ihres jährlichen Einkaufsplans wird ein Kreditlimit festgelegt.`,
        text2: `2. Wenn der Käufer (hiermit ist das in den „Geschäftsinformationen“ aufgeführte Unternehmen gemeint) einen Grund höherer Gewalt hat und FS in der schriftlichen Mitteilung vor dem Fälligkeitsdatum informiert hat, kann die Zahlung auf 15 Kalendertage nach dem Fälligkeitsdatum verschoben werden.`,
        text3: `3. Wenn der Käufer eine Zahlung nicht innerhalb von 15 Kalendertagen nach dem Fälligkeitsdatum leistet, ist FS unbeschadet aller anderen Rechte oder Rechtsmittel, die FS zur Verfügung stehen, berechtigt, den Kredit des Käufers zu beschränken und dem Käufer Zinsen in Höhe von einem Prozent (1 %) der überfälligen Zahlung pro Monat zu berechnen, bis die Zahlung vollständig erfolgt ist (jeder Teilmonat wird bei der Berechnung der Zinsen als voller Monat behandelt).`,
        text4: `4. Wenn der Käufer nach Erhalt der Ware ein Problem mit dem Kundendienst hat, das von FS verursacht wurde, kann der Käufer die Zahlung aussetzen, bis er das reparierte oder ersetzte Produkt erhält. Wenn das Problem mit dem Kundendienst jedoch durch den Käufer verursacht wurde, kann der Käufer die Zahlung für die Bestellung nicht verweigern.`,
        text5: `5. Wenn eine Bestellung auf Kredit storniert werden soll, informieren Sie bitte Ihren Kundenbetreuer im Voraus über die schriftliche Mitteilung. FS wird dies prüfen, Ihnen eine Rückmeldung geben und Sie über den Stornierungsprozess informieren. Wenn die Stornierung der Bestellung durch FS verursacht wurde (z. B. durch einen Defekt oder falsch gelieferte Produkte), kann der Käufer die Bestellung stornieren. Wenn die Stornierung der Bestellung durch den Käufer verursacht wird und der Käufer nicht im Voraus mit FS formell erklärt und verhandelt hat, kann die Bestellung nicht storniert werden.`,
        text6: `6. Alle vom Käufer im Rahmen dieser Vereinbarung zur Verfügung gestellten Informationen sind vertraulich und Eigentum des Käufers. FS wird keine Informationen an Dritte weitergeben oder veröffentlichen, es sei denn, die Weitergabe, Veröffentlichung und Verwendung der vertraulichen Informationen wurde vom Käufer schriftlich genehmigt.`,
        text7: `7. Im Falle von Problemen oder Streitigkeiten wenden Sie sich bitte telefonisch an xxxx oder per E-Mail an Ihren Kundenbetreuer bei FS.`,
        text8: `8. Der Käufer hat das Recht, die relevanten Informationen über Net Terms Management zu ändern. Der Käufer ist dafür verantwortlich, die Authentizität, Richtigkeit und Vollständigkeit der geänderten Informationen zu bestätigen. Außerdem ist der Käufer für die vorgenommenen Änderungen verantwortlich und ist nicht verpflichtet, ein weiteres Dokument aufgrund der geänderten Informationen zu unterzeichnen.`,
        text9: `9. Die vom Erwerber zur Verfügung gestellten Daten haben die erforderliche Genehmigung des Betroffenen zur Weitergabe und Nutzung erhalten, und FS.com kann sie wie üblich erhalten und nutzen. Der Erwerber muss vermeiden, dass FS.com durch den Betroffenen in Anspruch genommen wird.`,
    },
    topSwitch: {
        basic: `Grundinformation`,
        payment: `Zahlungsinformationen`,
        contact: `Kontaktinformationen`,
    },
}
