export default {
    banner: {
        title: "PicOS® para la solución H100 InfiniBand",
        desc: "Alto rendimiento, escalabilidad y seguridad para HPC",
        crumbList: [
            {
                name: "Inicio",
                url: "/",
            },
            {
                name: "Redes HPC",
                url: "/solutions/hpc-networking-2000.html",
            },
            {
                name: "ID de solución: S3000",
                url: "/solutions/picos-for-h100-infiniband-solution-S3000.html",
            },
        ],
    },
    nav: {
        list: ["Introducción", "Productos", "PicOS®", "AmpCon-DC", "Beneficios", "Recursos"],
        btn: "Contáctanos",
    },
    introduction: {
        title: "PicOS® y plataforma de gestión AmpCon-DC  para la red NVIDIA® InfiniBand H100",
        desc: "Los switches FS pueden utilizar la plataforma de administración avanzada PicOS® y AmpCon-DC para permitir a los operadores de centros de datos aprovisionar, monitorear, administrar y mantener de manera eficiente la estructura moderna del centro de datos, logrando una mayor utilización y reduciendo los gastos operativos generales.",
        tagTittle: ["Descripción general", "Red InfiniBand", "Red de gestión", "Red de almacenamiento"],
        tagSubTitle: [
            `Basada en la GPU NVIDIA® H100, junto con el software <a href="xxxx" target="_blank">PicOS®</a> y la <a href="nnnn" target="_blank">plataforma de gestión AmpCon-DC </a>, la solución FS H100 Infiniband se adapta según la topología de red de la arquitectura de HPC, incluyendo la red Infiniband, la red de gestión y la red de almacenamiento, para satisfacer diversas necesidades empresariales.`,
            "Equipada con la GPU NVIDIA® H100 y switches InfiniBand, la red Infiniband presenta latencia ultrabaja y gran ancho de banda, lo que garantiza una transmisión sin pérdidas con control de flujo y comprobaciones de redundancia CRC.",
            "Los switches FS pueden utilizar el software avanzado PicOS® y los conjuntos de funciones de la plataforma de gestión AmpCon-DC para que los clientes puedan aprovisionar, supervisar, gestionar, solucionar problemas de forma preventiva y mantener la infraestructura HPC de forma eficiente, logrando una mayor utilización y reduciendo el opex general.",
            "Los switches FS PicOS® soportan el protocolo BGP con potentes capacidades de control de enrutamiento, al tiempo que garantizan la ruta de reenvío óptima y el estado de reenvío de baja latencia de la red de almacenamiento. Además, es flexible y puede escalarse para satisfacer requisitos específicos de capacidad y ancho de banda.",
        ],
        tagDesc: [
            {
                title: "Transmisión InfiniBand sin pérdidas",
                desc: "La arquitectura H100, impulsada por conmutadores InfiniBand, ofrece una latencia ultrabaja y un alto ancho de banda de 800Gbps, lo que garantiza una transmisión sin pérdidas con control de flujo y comprobaciones CRC.",
            },
            {
                title: "Sistema operativo PicOS®",
                desc: "PicOS® elimina la dependencia de un único proveedor en la infraestructura de red crítica para ofrecer un sistema operativo de red (NOS) más resistente, programable y escalable con un costo total de propiedad más bajo.",
            },
            {
                title: "plataforma de gestión AmpCon-DC ",
                desc: "AmpCon-DC permite a los operadores de centros de datos aprovisionar, monitorear, administrar, solucionar problemas y mantener de manera preventiva y mantener la estructura del centro de datos de manera eficiente.",
            },
        ],
    },
    products: {
        title: "Lista de productos",
        tabTitList: ["Red InfiniBand", "Red de gestión", "Red de almacenamiento"],
        list: [
            [
                {
                    title: "Switches InfiniBand QM9700/9790",
                    descTitle: ["MQM9700-NS2F", "MQM9790-NS2F"],
                },
                {
                    title: "Adaptadores InfiniBand ConnectX®-6/7",
                    descTitle: ["MCX75510AAS-NEAT", "MCX75310AAC-NEAT", "MCX75310AAS-NEAT"],
                },
                {
                    title: "Transceptores InfiniBand 800G/400G",
                    descTitle: ["OSFP-SR8-800G", "OSFP-SR4-400G-FL"],
                },
                {
                    title: "Cables de fibra óptica",
                    descTitle: ["12FMTPOM4"],
                },
            ],
            [
                {
                    title: "Plataforma de gestión AmpCon-DC ",
                    descTitle: [" P-AC-EE-B1"],
                },
                {
                    title: "Switches PicOS®",
                    descTitle: ["N8550-64C", "N8550-48B8C", "N5850-48S6Q", "S5810-48TS"],
                },
                {
                    title: "Módulo transceptor óptico",
                    descTitle: ["QSFP28-SR4-100G", "QSFP28-SFP28-CVR", "SFP28-25GSR-85", "SFP-10GSR-85"],
                },
                {
                    title: "Cables de fibra óptica",
                    descTitle: ["12FMTPOM4", "OM4LCDX", "C6ASFTPSGPVC"],
                },
            ],
            [
                {
                    title: "Switches PicOS®",
                    descTitle: ["N8550-64C"],
                },
                {
                    title: "Adaptadores Ethernet ConnectX®-6",
                    descTitle: ["MCX623106AN-CDAT"],
                },
                {
                    title: "Módulo transceptor óptico",
                    descTitle: ["QSFP28-SR4-100G"],
                },
                {
                    title: "Cables de fibra óptica",
                    descTitle: ["12FMTPOM4"],
                },
            ],
        ],
    },
    picos: {
        title: "Operaciones de red más resilientes y eficientes con un coste total de propiedad más bajo",
        desc: `Con <a href="xxxx" target="_blank">PicOS®</a>, proporciona redes programables, altamente confiables y resistentes que sean más eficientes y escalables que los predecesores monolíticos.`,
        list: [
            {
                title: "Interoperabilidad total",
                subTitle:
                    "Soporta los protocolos OpenFlow, SNMP y gNMI, proporciona interfaces de programación de aplicaciones API, ofrece interoperabilidad con Cisco y otras infraestructuras de terceros para una migración y actualización progresivas.",
            },
            {
                title: "Virtualización de red",
                subTitle: "Utiliza una solución abierta con matrices spine-leaf para soportar arquitecturas de virtualización flexibles y escalables sin soluciones de chasis Cisco.",
            },
            {
                title: "Seguridad reforzada",
                subTitle: "Permite ACL complejas sin agotar los recursos TCAM para controlar con flexibilidad el tráfico de red, garantizando la seguridad de los datos sin sacrificar el rendimiento del hardware.",
            },
            {
                title: "Flexibilidad de la red",
                subTitle:
                    "La tecnología CrossFlow™ permite el tráfico SDN/OpenFlow con tráfico de producción de capa 2/capa 3 en los mismos puertos de switches, formando Open Intent-based Networking (OIBN) para mejorar la flexibilidad de los servicios de red.",
            },
        ],
    },
    ampcon: {
        title: "Automatiza la gestión del ciclo de vida de la red de un extremo a otro",
        desc: "Evita configuraciones erróneas y tiempos de inactividad con una gestión del ciclo de vida de la red de extremo a extremo, completa con aprovisionamiento, mantenimiento, verificación de cumplimiento y actualizaciones automatizados.",
        list: [
            {
                title: "Aprovisionamiento sin intervención",
                subTitle: "Escale de forma fácil y segura desde cualquier lugar con aprovisionamiento automatizado y aplicación de políticas.",
            },
            {
                title: "Gestión de configuración",
                subTitle: "Envía actualizaciones, parches y correcciones de errores a uno o a un grupo de conmutadores para ahorrar mano de obra y evitar el tiempo de inactividad.",
            },
            {
                title: "Copias de seguridad y cumplimiento",
                subTitle: `Automatiza las copias de seguridad de la configuración, los registros de operaciones y las comprobaciones de cumplimiento con una "configuración dorada" con reversión inteligente para minimizar los errores.`,
            },
            {
                title: "Cambiar visibilidad",
                subTitle: "Proporciona un inventario detallado de todos los conmutadores, incluidos detalles de hardware, versión de software, configuración y más.",
            },
            {
                title: "Automatización sin agentes",
                subTitle: "Escribe Ansible Playbooks para crear y programar flujos de trabajo personalizados con informes fáciles de leer.",
            },
            {
                title: "Preconfiguración de PicOS-V",
                subTitle: `<a href="xxxx" target="_blank">La plataforma de gestión AmpCon-DC  </a> se puede preconfigurar con <a href="nnnn" target="_blank">PicOS-V</a> en un escenario de virtualización y luego trasladarse al centro de datos.`,
            },
        ],
    },
    benefits: {
        title: "Explora los beneficios de la red H100 InfiniBand",
        list: [
            {
                title: "Plataforma de gestión unificada",
                desc: `<a href="xxxx" target="_blank">PicOS®</a> con <a href="nnnn" target="_blank">plataforma de gestión AmpCon-DC </a> permite la configuración, el monitoreo y el mantenimiento unificados de las redes de administración y almacenamiento, eliminando costosos tiempos de inactividad y tareas manuales que consumen mucho tiempo.`,
            },
            {
                title: "Potente arquitectura InfiniBand",
                desc: "FS proporciona arquitecturas de red InfiniBand altamente confiables y servicios técnicos profesionales, entregando diseños de red personalizados y listas de productos en 48 horas.",
            },
            {
                title: "Solución rentable",
                desc: "Al asociarte con NVIDIA®, FS puede ofrecer productos originales como conmutadores y adaptadores InfiniBand, reduciendo costos en un 30% a través de su rico ecosistema de productos.",
            },
            {
                title: "I+D y pruebas de primer nivel",
                desc: "FS tiene un centro de investigación y desarrollo de primera clase y utiliza conmutadores NVIDIA® MQM9790, tarjetas de red ConnectX®-7 y otros productos para construir una plataforma de prueba para pruebas de transmisión de carga completa para garantizar la estabilidad.",
            },
            {
                title: "Diseño de partición",
                desc: "El diseño de la partición de la red (informática, almacenamiento, administración dentro de banda y administración fuera de banda) ayuda a aislar diferentes áreas comerciales y reducir la complejidad del tráfico.",
            },
            {
                title: "Escalabilidad flexible",
                desc: "La arquitectura de red de capa 2 de Spine-Leaf cumple con los requisitos actuales de operación de la red y al mismo tiempo proporciona flexibilidad y confiabilidad para futuras expansiones comerciales.",
            },
        ],
    },
    resources: {
        title: "Recursos",
        list: [
            {
                title: "Soluciones de funciones",
                linkText: ["Solución PicOS® para conducción automatizada", "Potencia la HPC con la red RoCE", "Impulsa la HPC con la solución de cables y transceptores FS InfiniBand"],
            },
            {
                title: "Blogs",
                linkText: [
                    "Descripción general de la red y la arquitectura de InfiniBand",
                    "Pica8 AmpCon-DC: Tu socio de automatización de redes abiertas",
                    "Consejos para elegir productos InfiniBand para informática HPC",
                ],
            },
            {
                title: "Noticias",
                linkText: [
                    "Los switches FS ahora son compatibles con PicOS® para una experiencia de red unificada",
                    "El auge de los centros de datos de HPC: FS potenciando los centros de datos de próxima generación",
                    "FS presenta una solución de conectividad de red de alta velocidad de próxima generación para acelerar la era de HPC",
                ],
            },
            {
                title: "Documentos",
                linkText: ["Informe de pruebas de la solución InfiniBand PicOS® para H100", "Manual de entrega de la solución InfiniBand H100", "Guía de pruebas de aceptación de transceptores 800G y 400G"],
            },
        ],
    },
    contactUs: {
        title: "¿Listo para empezar?",
        desc: "¿Tienes alguna pregunta sobre la compra de una solución que te interesa? Rellena el formulario y te responderemos en 1 día laborable.",
    },
    headTop: {
        href: "/solutions/picos-for-h100-infiniband-solution-S3000.html",
        name: "Solución InfiniBand H100",
    },
    meta: {
        title: "PicOS® y AmpCon-DC para la red NVIDIA® InfiniBand H100",
        description:
            "Descubre cómo las soluciones FS H100 InfiniBand revolucionan las redes informáticas de alto rendimiento, aumentando la eficiencia en un 20%. Con el software PicOS® y el controlador de red AmpCon-DC, los usuarios disfrutan de una configuración y mantenimiento perfectos, mejorando la seguridad y acelerando los flujos de trabajo de HPC.",
    },
}
