export default {
    first_name: {
        first_name_required: "Por favor, introduce tu nombre.",
        first_name_max: "Límite máximo de 40 caracteres.",
        first_entry_name_max: "Límite máximo de 35 caracteres.",
        first_name_min: "Límite mínimo de 2 caracteres.",
    },
    last_name: {
        last_name_required: "Por favor, introduce tu apellido.",
        last_name_max: "Límite máximo de 40 caracteres.",
        last_entry_name_max: "Límite máximo de 35 caracteres.",
        last_name_min: "Límite mínimo de 2 caracteres.",
    },
    email: {
        email_required: "Por favor, introduce tu dirección de correo electrónico.",
        business_email_required: "Por favor, ingresa tu correo electrónico comercial.",
        email_valid: "Por favor, introduce un correo electrónico válido.",
        email_validate2: "Por favor, introduce un correo electrónico válido. (ej: <EMAIL>)",
        email_validate3: `Por favor, no rellena una dirección de correo electrónico que incluya "admin/support/postmaster/abuse", ya que puede causar errores al utilizar la cuenta.`,
    },
    phone: {
        phone_required: "Por favor, introduce tu número de teléfono.",
        phone_min: "El número de teléfono debe tener al menos 6 dígitos.",
        phone_validate: "Solo se permiten dígitos y al menos 7 dígitos.",
        phone_validate_us: `Tu número de teléfono debe ser un número de 10 dígitos.`,
    },
    password: {
        password_required: "Por favor, introduce tu contraseña.",
        password_validate: "Mínimo 6 caracteres y máximo 32 caracteres; Debe incluir al menos una letra y un número. Caracteres especiales (_ ? @ ! # $ % & * .) están permitidos.",
        password_validate2: "La contraseña debe tener un máximo de 32 caracteres.",
        password_confirm: `Por favor, introduce tu contraseña.`,
        password_match: `Las contraseñas no coinciden. Inténtalo de nuevo.`,
    },
    register_password: {
        valid: "Por favor, introduce una contraseña válid.",
        condition_list: ["Al menos una letra", "Al menos un número", "6-32 caracteres", "Sin espacio"],
        tips: "Caracteres especiales (_ ? @ ! # $ % & * .) permitidos.",
    },
    ret_code: {
        ret_code_required: `Se requiere el código de restablecimiento.`, //新增 2021-08-03
    },
    company: {
        company_required: "Se requiere el nombre de tu empresa.",
        company_validate: "El nombre de la empresa debe tener entre 1 y 120 caracteres.",
        company_size: "Por favor, elige el tamaño de tu empresa.",
    },
    address_type: {
        address_type_required: "Se requiere el tipo de dirección.",
    },
    address: {
        address_required: "Se requiere tu dirección.",
        address_validate: "La dirección línea 1 debe tener entre 4 y 35 caracteres.",
    },
    address2: {
        address2_required: "Se requiere tu dirección línea 2.",
        address2_validate: "La dirección línea 2 debe tener entre 4 y 35 caracteres.",
    },
    city: {
        city_required: "Se requiere tu ciudad.",
        city_validate: "La longitud de la ciudad debe estar entre 2 y 40 caracteres.",
    },
    state: {
        state_required: "Se requiere tu estado/provincia/región.",
    },
    agreement: {
        agree: "Asegúrate de que aceptas nuestra Política de privacidad y los Términos y condiciones.",
    },
    zip_code: {
        zip_code_required: "Se requiere el código postal..",
        zip_code_validate: "El código postal debe tener al menos 3 caracteres.",
    },
    industry: {
        industry_required: "Por favor, elige un sector.",
    },
    function: {
        function_required: "Por favor, elige al menos una función.",
    },
    time: {
        time_required: "Por favor, elige la hora.",
    },
    date: {
        date_required: "Por favor, elige la fecha.",
    },
    comments_questions: {
        comments_questions_required: "El campo de observaciones no puede estar vacío.",
    },
    half_validate: `Por favor, introduce números en inglés de medio ángulo.`,
    zip_code_match: `El código postal y la dirección no coinciden.`,
    tel_error: `El número de teléfono introducido es incorrecto.`,
    valid_phone_number: `Por favor, introduce un número de teléfono válido.`,
    valid_post_number: `Please enter a valid postcode.`,
    aggree_policy_new:
        "He leído y acepto los FS.COM <a href='BBBB' target='_blank'>Términos y Condiciones</a> y reconozco la Política de <a href='AAAA' target='_blank'>Privacidad y el Aviso de Recolección de Datos</a>.",
}
