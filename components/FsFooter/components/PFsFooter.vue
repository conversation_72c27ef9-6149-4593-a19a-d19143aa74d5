<template>
    <div class="fs-footer-box fs-footer-box-new">
        <div class="fs-footer">
            <div class="footer-main" @click="resourcePage">
                <nav class="footer-box-nav" :class="{ ru_grid: website == 'ru' && screenWidth > 768 }">
                    <!-- <div></div> -->
                    <div
                        class="footer-box"
                        :class="{ deClass: userInfo && userInfo.isCompanyOrganizationUser && website == 'de' }"
                        :style="footerColStyle[i]"
                        :ref="`footer-col-${i}`"
                        v-for="(item, i) in footer_data"
                        :key="item.title">
                        <h3 class="title detitle">{{ item.title }}</h3>
                        <div class="list" v-if="item.children && item.children.length">
                            <p class="list-item" @click.stop="pointLink(c.title)" v-for="(c, i) in item.children" :key="i">
                                <!-- <a tabindex="0" class="fs-hover-underline" href="javascript:;" v-if="['chat', 'bug'].includes(c.url)" v-html="c.title" @click="urlClick(c.url)"></a> -->
                                <a tabindex="0" :target="c.tag === 2 ? '_blank' : '_self'" :href="c.url.includes('solution_design.html') ? `${$localeLink(c.url)}?resource=1` : `${$localeLink(c.url)}`">
                                    <span class="txt fs-hover-underline" v-html="c.title"></span>
                                    <span class="iconfont iconfont_com" v-show="c.tag === 2">&#xe60b;</span>
                                </a>
                            </p>
                        </div>
                    </div>
                    <div></div>
                </nav>
                <div class="footer-box footer-box-left">
                    <FooterSubscribe />
                    <FooterSocial />
                </div>
            </div>

            <section>
                <div
                    class="footer_country_box"
                    :class="{ new_country_box: website !== 'uk' && (country_name === 'France' || countrysIcon[website]), au_country_box: website === 'au' || website === 'sg' || website === 'mx' }">
                    <div
                        v-show="screenWidth > 768"
                        id="MyCustomTrustbadge"
                        :class="[
                            `MyCustomTrustbadge-${website}`,
                            { myCustomTrustbadge_other: ['de', 'de-en', 'nl', 'fr', 'es', 'it'].includes(website) },
                            { myCustomTrustbadge_none: !['de', 'de-en', 'nl', 'fr', 'es', 'it'].includes(website) },
                        ]"></div>
                    <div class="certification-box" v-if="!(country_name === 'France' || countrysIcon[website] || ['cn'].includes(website))">
                        <CertificationCtn :data="certifications[warehouse]" />
                    </div>
                    <div class="certification-box" v-if="website === 'uk'">
                        <CertificationCtn :data="certifications[warehouse]" />
                    </div>
                    <div class="certification-cn-box" v-if="['cn'].includes(website)">
                        <a href="https://ss.knet.cn/verifyseal.dll?sn=e15101544030060902l1bf000000&ct=df&a=1&pa=0.05508846207521856" target="_blank"></a>
                        <a href="https://credit.szfw.org/CX20150924011838250320.html" target="_blank"></a>
                        <a href="http://www.beian.gov.cn/portal/registerSystemInfo?recordcode=**************" target="_blank">
                            <img src="https://img-en.fs.com/zh/includes/templates/fiberstore/images/beian_icon.png" alt="" />
                            <p>粤公网安备 **************号</p>
                        </a>
                    </div>
                    <div class="country_box_right" ref="rightCountryBox">
                        <m-change-website :onlyName="true" :top="48" v-show="screenWidth <= 1024"></m-change-website>
                        <div v-show="screenWidth > 1024" class="right-country" ref="rightCountry" @keyup.esc="countryTopMouseleave" @mouseenter="countryTopMouseenter" @mouseleave="countryTopMouseleave">
                            <div class="current-country-box" @keyup.enter="countryTopMouseenter" tabindex="0">
                                <span class="country-info">{{ `${country_name}  / ${symbol} ${currency}` }}</span>
                                <span class="iconfont iconfont-down" :class="{ 'iconfont-down-up': show_country }">&#xe704;</span>
                            </div>
                            <div class="country-currency-wrap" v-show="show_country">
                                <div class="triangle-up" :style="triangleUpStyle"></div>
                                <div class="triangle-up-line"></div>
                                <div class="country-currency-box">
                                    <pc-change-website @focus="changeWebsiteFocus" @change="changeWebsiteChange" @toogleClick="changeWebsiteClick" @input="changeWebsiteInput"></pc-change-website>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="footer-bottom">
                    <div class="policy-box" v-if="top_data && top_data.footerLinks && top_data.footerLinks.length">
                        <template v-for="(item, index) in top_data.footerLinks">
                            <a v-if="item.link.includes('report-vulnerability')" tabindex="0" :key="item.text" @click="pointLink(item.text)" :href="$localeLink(item.link)"> {{ item.text }} </a>
                            <template v-else>
                                <a tabindex="0" :key="item.text" @click.stop="pointLink(item.text)" :href="$localeLink(item.link)"> {{ item.text }} </a>
                            </template>
                            <span class="line" v-if="index !== top_data.footerLinks.length - 1" :key="'line' + item.text"></span>
                        </template>
                        <!-- <template v-if="isShowCookieSetting"> -->
                        <span class="line"></span>
                        <a @click="handleCookiePopup">
                            <span>{{ $c("pages.home.cookie.cookieSetting") }}</span>
                        </a>
                        <!-- </template> -->
                    </div>
                    <client-only>
                        <div class="left-cr-box">
                            <span class="copyright" v-html="top_data.right"></span>
                            <template v-if="isCn">
                                <a :href="localePath({ path: '/policies/report.html' })" target="_blank">廉洁举报</a>
                                <a href="https://beian.miit.gov.cn" target="_blank">粤ICP备12032516号</a>
                                <a href="https://mall.jd.com/index-11817598.html?from=pc" id="bdtj_jdgm" target="_blank" @click.stop="bdRequest(56)">飞速（FS）京东旗舰店</a>
                            </template>
                        </div>
                    </client-only>
                </div>
            </section>
            <div class="footer-certification-box footer-certification-box_France" v-if="country_name === 'France' || countrysIcon[website]">
                <div class="certification-box" :class="{ 'certification-box-uk': website === 'uk' }">
                    <template v-if="country_name === 'France'">
                        <CertificationCtn :data="certifications[warehouse]['other']" />
                    </template>
                    <template v-else-if="website === 'uk'">
                        <div class="uk_local_txt">
                            <p>Company Registration Number: 10876330</p>
                            <p>VAT Registration Number: GB277384560</p>
                        </div>
                    </template>
                    <template v-else>
                        <template v-if="warehouse === 'DE' && ['uk', 'de-en', 'nl', 'de'].includes(website)">
                            <CertificationCtn :data="certifications[warehouse][website]" />
                        </template>
                        <template v-else-if="warehouse === 'DE' && !['uk', 'de-en', 'nl', 'de'].includes(website)">
                            <CertificationCtn :data="certifications[warehouse]['other']" />
                        </template>
                        <template v-else>
                            <CertificationCtn :data="certifications[warehouse]" />
                        </template>
                    </template>
                </div>

                <div class="pay_box pay-icon" v-if="countrysIcon[website]">
                    <a v-for="(img, i) in getPayIcons(website)" :key="i" tabindex="0" :href="img.href" :target="img.href === 'javascript:;' ? '_self' : '_blank'" :class="{ uk_a: website == 'uk' }">
                        <img :src="img.imgur" alt="" />
                    </a>
                </div>
            </div>
        </div>
        <CookiePopup :show="showCookiePopup" :website="website" :cookieType="cookieType" @close="showCookiePopup = false" @agree="handleAgree" />
    </div>
</template>
<script>
import PcChangeWebsite from "@/components/ChangeWebsite/PcChangeWebsite"
import MChangeWebsite from "@/components/ChangeWebsite/MChangeWebsite"
import { mapState, mapMutations, mapGetters, mapActions } from "vuex"
import FsButton from "@/components/FsButton/FsButton"
import FsSelect from "@/components/FsSelect/FsSelect.vue"
import ValidateError from "@/components/ValidateError/ValidateError.vue"
import { trust_badge } from "@/util/meta.js"
import socialLink from "@/constants/socialLink.js"
import FsPhoneLabel from "./FsPhoneLabel.vue"
import FsWorkingHoursLabel from "./FsWorkingHoursLabel.vue"
import CertificationCtn from "./CertificationCtn"
import { certifications, countrysIcon, payIconList } from "@/constants/common.js"
import FooterSubscribe from "./FooterSubscribe.vue"
import FooterSocial from "./FooterSocial.vue"
import CookiePopup from "../../../popup/CookiePopup/CookiePopup.vue"
import { setCookieOptions } from "@/util/util"
export default {
    name: "PFsFooter",
    components: {
        FsButton,
        ValidateError,
        FsSelect,
        PcChangeWebsite,
        MChangeWebsite,
        FsPhoneLabel,
        FsWorkingHoursLabel,
        CertificationCtn,
        FooterSubscribe,
        FooterSocial,
        CookiePopup,
    },
    data() {
        return {
            // xe623: require("@/assets/svg/xe623.svg"),
            // xf061: require("@/assets/svg/xf061.svg"),
            leavefb_show: false,
            show_country: false,
            // select_language_currency: "",
            select_country_loading: false,
            triangleUpStyle: {},
            email_form: {
                email: "",
                placeholder: this.$c("components.smallComponents.fsFooter.email"),
                loading: false,
                error: "",
                success_info: "",
            },
            socialLink,
            policy_data: [
                {
                    url: "site-map",
                    title: this.$c("components.smallComponents.fsFooter.maps"),
                },
                {
                    url: "privacy-policy",
                    title: this.$c("components.smallComponents.singleFooter.policy"),
                },
                {
                    url: "terms-of-use",
                    title: this.$c("components.smallComponents.singleFooter.terms"),
                },
            ],
            certifications: certifications,
            footerColStyle: [],

            countrysIcon: countrysIcon,
            payIconList: payIconList,
            showCookiePopup: false,
        }
    },
    computed: {
        ...mapState({
            isLogin: (state) => state.userInfo.isLogin,
            userInfo: (state) => state.userInfo.userInfo,
            cartList: (state) => state.cart.list,
            cartData: (state) => state.cart.cartData,
            savedCarts: (state) => state.userInfo.savedCarts,
            activeQuotes: (state) => state.userInfo.activeQuotes,
            menu_data: (state) => state.category.menu_data,
            classify_data: (state) => state.category.classify_data,
            top_data: (state) => state.category.top_data,
            // footer_data: (state) => state.category.footer_data,
            iso_code: (state) => (state.webSiteInfo.iso_code ? state.webSiteInfo.iso_code.toLowerCase() : ""),
            country_name: (state) => state.webSiteInfo.country_name,
            symbol: (state) => state.webSiteInfo.symbol,
            currency: (state) => state.webSiteInfo.currency,
            qty: (state) => state.cart.qty,
            website: (state) => state.webSiteInfo.website,
            showCookieTip: (state) => state.webSiteInfo.showCookieTip,
            showCookieTipOld: (state) => state.webSiteInfo.showCookieTipOld,
            warehouse: (state) => state.webSiteInfo.warehouse,
            pageGroup: (state) => state.ga.pageGroup,
            isMobile: (state) => state.device.isMobile,
            language: (state) => state.webSiteInfo.language,
            screenWidth: (state) => state.device.screenWidth,
            state1: (state) => state,
        }),
        ...mapGetters({
            isRussia: "webSiteInfo/isRussia",
            isCn: "webSiteInfo/isCn",
            isJp: "webSiteInfo/isJp",
            isJpEn: "webSiteInfo/isJpEn",
            footer_data: "category/max_footer_data",
        }),
        isShowCookieSetting() {
            const EuropeSiteMap = ["de-en", "de", "nl", "fr", "es", "it"]
            return this.website && EuropeSiteMap.includes(this.website)
        },
        cookieType() {
            // 基于网站类型来判断cookie类型，而不是依赖显示状态
            const EuropeSiteMap = ["de-en", "de", "nl", "fr", "es", "it"]

            if (this.website && EuropeSiteMap.includes(this.website)) {
                return 1 // 欧洲站点使用新版cookie提示
            } else {
                return 0 // 非欧洲站点使用旧版cookie提示
            }
        },
    },
    mounted() {
        document.addEventListener("click", this.handleOtherClick)
        let currentPath = this.$route.name
        if (!(!/^home/g.test(currentPath) && ["de", "de-en", "nl", "es", "mx"].includes(this.website) && this.isMobile)) {
            trust_badge(this.website, this.isMobile)
        }
        this.initFooterStyle()
        console.log(this.state1, "state1")
    },
    destroyed() {
        document.removeEventListener("click", this.handleOtherClick)
    },

    methods: {
        ...mapMutations({
            setResourcePage: "device/setResourcePage",
            setShowCookieTip: "webSiteInfo/setShowCookieTip",
            setShowCookieTipOld: "webSiteInfo/setShowCookieTipOld",
        }),
        ...mapActions({
            updateSiteInfo: "selectCountry/updateSiteInfo",
        }),
        replaceURL04(url) {
            return this.localePath({ path: "/" }) === "/" ? url : `${this.localePath({ path: "/" })}${url}`
        },
        bdRequest(newType) {
            this.$bdRequest({
                conversionTypes: [
                    {
                        logidUrl: location.href,
                        newType: newType,
                    },
                ],
            })
        },
        // 获取支付支付图标
        getPayIcons(country) {
            const result = []
            const arr = this.countrysIcon[country]
            if (arr) {
                arr.forEach((item) => {
                    if (item) {
                        result.push(this.payIconList[item])
                    }
                })
            }
            return result
        },
        initFooterStyle() {
            this.footer_data.map((item, index) => {
                const colWidth = this.$refs[`footer-col-${index}`][0].clientWidth
                // this.footerColStyle[index] = { width: (colWidth > 128 ? 180 : 128) + "px" }
                this.footerColStyle[index] = { width: "auto" }
                if (this.screenWidth > 890) {
                    this.footerColStyle[index] = { width: "auto" }
                } else {
                    this.footerColStyle[index] = { width: colWidth > 124 ? "148px" : "106px" }
                }
            })

            // this.triangleUpStyle = {
            //     left: 300 - Math.floor(this.$refs["rightCountryBox"].clientWidth / 2) + Math.floor(this.$refs["rightCountry"].clientWidth / 2) + "px",
            // }
        },
        changeWebsiteFocus(areaName) {
            if (window.dataLayer) {
                window.dataLayer.push({
                    event: "uaEvent",
                    eventCategory: this.pageGroup,
                    eventAction: "bottom_function",
                    eventLabel: `Country Input_${areaName}`,
                    nonInteraction: false,
                })
            }
        },
        changeWebsiteClick() {
            if (window.dataLayer) {
                window.dataLayer.push({
                    event: "uaEvent",
                    eventCategory: this.pageGroup,
                    eventAction: "bottom_function",
                    eventLabel: "Continents_select",
                    nonInteraction: false,
                })
            }
        },
        changeWebsiteChange(countryLanguage) {
            if (window.dataLayer) {
                window.dataLayer.push({
                    event: "uaEvent",
                    eventCategory: this.pageGroup,
                    eventAction: "bottom_function",
                    eventLabel: `Switch_${countryLanguage}`,
                    nonInteraction: false,
                })
            }
        },
        changeWebsiteInput(areaName, value) {
            if (window.dataLayer) {
                window.dataLayer.push({
                    event: "uaEvent",
                    eventCategory: this.pageGroup,
                    eventAction: "bottom_function",
                    eventLabel: `Search_${areaName}_${value}`,
                    nonInteraction: false,
                })
            }
        },
        urlClick(url) {
            if (url === "bug") {
                this.$emit("feedbackShow")
            }
        },
        countryTopMouseenter() {
            this.show_country = true
        },
        countryTopMouseleave() {
            this.show_country = false
        },
        handleOtherClick(e) {
            e.stopPropagation()
            if (!this.$refs.rightCountry.contains(e.target)) {
                this.show_country = false
            }
        },
        topClick(url) {
            let str = ""
            if (url.includes("about_us")) {
                str = `About FS`
            } else if (url.includes("shipping_delivery")) {
                str = `Shipping Policy`
            } else if (url === "Logo") {
                str = `Logo`
            } else if (url === "Sign In" || url === "Sign In Top") {
                str = `Sign In`
            } else if (url === "Account" || url === "Account-top") {
                str = `Account`
            } else if (url === "Need Help") {
                str = "Need Help"
            } else if (url === "Cart") {
                str = "Cart"
            } else if (url === "Language_Unfold") {
                str = "Language_Unfold"
            } else if (url === "Save Country Setting") {
                str = "Save Country Setting"
            } else if (url.includes("Switch Language_")) {
                str = url
            }
            if (str && window.dataLayer) {
                window.dataLayer.push({
                    event: "uaEvent",
                    eventCategory: this.pageGroup,
                    eventAction: url === "Account" || url === "Sign In" ? "login_function" : "bottom_function",
                    eventLabel: str,
                    nonInteraction: false,
                })
            }
        },

        pointLink(c) {
            if (window.dataLayer) {
                window.dataLayer.push({
                    event: "uaEvent",
                    eventCategory: this.pageGroup,
                    eventAction: "bottom_navigation",
                    eventLabel: c,
                    nonInteraction: false,
                })
            }
        },
        logoPoint() {
            if (window.dataLayer) {
                window.dataLayer.push({
                    event: "uaEvent",
                    eventCategory: this.pageGroup,
                    eventAction: "bottom_function",
                    eventLabel: "Logo",
                    nonInteraction: false,
                })
            }
        },
        appDownloadPoint() {
            if (window.dataLayer) {
                window.dataLayer.push({
                    event: "uaEvent",
                    eventCategory: this.pageGroup,
                    eventAction: "bottom_function",
                    eventLabel: "Download App",
                    nonInteraction: false,
                })
            }
        },
        socialPoint(t) {
            if (window.dataLayer) {
                window.dataLayer.push({
                    event: "uaEvent",
                    eventCategory: this.pageGroup,
                    eventAction: "bottom_function",
                    eventLabel: t,
                    nonInteraction: false,
                })
            }
        },
        // 设置来源页面类型
        resourcePage() {
            this.setResourcePage(1)
        },
        handleCookiePopup() {
            this.showCookiePopup = !this.showCookiePopup
        },
        // 统一处理同意cookie的方法
        handleAgree() {
            this.agreeCookie()
        },
        // 统一的同意cookie方法，合并原agreeCookie和agreeCookieNew的逻辑
        agreeCookie() {
            // 设置统一的cookie控制标识
            this.$cookies.set("cookieconsent_dismissed", "yes")
            // 设置Google Analytics cookie
            this.$cookies.set("fs_google_analytics", "yes")
            // 设置营销SDK cookie
            this.$cookies.set("fs_marketing_sdk", "yes")

            // 关闭所有cookie提示
            this.setShowCookieTip(false)
            this.setShowCookieTipOld(false)

            // 更新GTM同意状态
            if (window.gtag) {
                window.gtag("consent", "update", {
                    region: ["AT", "BE", "BG", "CY", "CZ", "DE", "DK", "EE", "ES", "FI", "FR", "GR", "HR", "HU", "IE", "IS", "IT", "LI", "LT", "LU", "LV", "MT", "NL", "NO", "PL", "PT", "RO", "SE", "SI", "SK"],
                    ad_storage: "granted",
                    ad_user_data: "granted",
                    ad_personalization: "granted",
                    analytics_storage: "granted",
                })
            }

            // 刷新页面以应用新的cookie设置
            window && window.location.reload()
        },
    },
    watch: {
        screenWidth() {
            this.initFooterStyle()
        },
    },
}
</script>
<style lang="scss">
#MyCustomTrustbadge {
    position: relative;
    &.myCustomTrustbadge_none {
        display: none;
    }
    &.myCustomTrustbadge_other {
        display: block;
    }
    ._t53mel {
        width: 112px !important;
        height: 56px !important;
        border: 1px solid rgb(229, 229, 229) !important;
        border-radius: 8px !important;
        ._6ql45s {
            width: 100% !important;
            height: 100% !important;
            padding: 0 !important;
            margin: 0 !important;
            border-width: 0px !important;
            ._1748qll {
                width: 90px !important;
                height: 40px !important;
                position: relative !important;
                ._ihmjp9 {
                    position: absolute !important;
                    width: 28px !important;
                    height: 28px !important;
                    left: 8px !important;
                    top: 8px !important;
                    padding: 0 !important;
                    margin: 0 !important;
                    img {
                        width: 100% !important;
                        height: 100% !important;
                    }
                }
                ._1koxq8s {
                    position: absolute !important;
                    left: 42px !important;
                    top: 6px !important;
                    padding: 0 !important;
                    ._jikro2 {
                        width: 48px !important;
                        height: 8px !important;
                        img {
                            width: 8px !important;
                            height: 8px !important;
                        }
                        ._9m9ntk,
                        ._gy75xr,
                        ._1cghd0k {
                            width: 100% !important;
                            height: 100% !important;
                            img {
                                width: 8px !important;
                                height: 8px !important;
                            }
                        }
                    }
                    ._trzuc6 {
                        font-size: 10px !important;
                        margin-top: 2px !important;
                        line-height: 10px !important;
                    }
                    ._1o5ol2b {
                        font-size: 10px !important;
                        line-height: 10px !important;
                        color: #19191a !important;
                    }
                }
            }
        }
        ._1bjjocx {
            position: absolute !important;
            width: 10px !important;
            height: 3px !important;
            top: 6px !important;
            right: 6px !important;
            ._fhufc4 {
                position: absolute !important;
                width: 10px !important;
                height: 3px !important;
            }
        }
        ._ue0ri9 {
            position: absolute !important;
            left: 8px !important;
            width: 96px !important;
        }
        ._1qgn7s9 {
            margin-top: 4px !important;
            font-size: 9px !important;
            color: #19191a !important;
            height: 15px !important;
            line-height: 9px !important;
        }
    }
    ._1gwv20v {
        ._lk7o4h {
            position: relative !important;
            border: 1px solid #ccc !important;
            border-radius: 3px !important;
            padding: 11px 16px !important;
            box-sizing: border-box !important;

            ._thsmae {
                width: 100% !important;
                align-items: center !important;

                ._1qiwh36 {
                    width: 58px !important;
                    height: 58px !important;
                    padding: 0 !important;
                    margin: 0 !important;
                    margin-right: 12px !important;

                    ._upwhbk {
                        display: block !important;
                        margin: auto !important;
                        width: 58px !important;
                        height: 58px !important;
                    }
                }

                ._argvb9 {
                    flex: 1 !important;
                    display: flex !important;
                    flex-direction: column !important;
                    justify-content: center !important;
                    padding: 12px 0 0 0 !important;
                    align-items: center !important;
                    height: 58px !important;

                    ._s7xc8z {
                        color: #707070 !important;
                        font-size: 12px !important;
                        line-height: 18px !important;
                        font-display: swap !important;
                        padding: 0 !important;
                        margin-top: 2px !important;
                    }

                    ._8pqgf9 {
                        color: #707070 !important;
                        font-display: swap !important;
                        font-size: 13px !important;
                        margin-top: 4px !important;
                        height: 13px !important;
                        line-height: 13px !important;
                        margin-bottom: -2px !important;

                        > span {
                            display: inline-block !important;
                            height: 13px !important;
                            color: #707070 !important;
                            font-display: swap !important;
                            line-height: 13px !important;
                            font-size: 13px !important;
                        }
                    }
                }
            }

            ._zbxp0s {
                display: none !important;
            }

            ._1iu1jow {
                display: none !important;
            }

            ._qcra45 {
                padding: 0 !important;
                top: 6px !important;
                font-size: 13px !important;
                color: #19191a !important;
                white-space: nowrap !important;
                margin: 0 !important;
                position: absolute !important;
                text-align: center !important;
                width: calc(100% - 32px) !important;
                padding-left: 70px !important;
                left: 16px !important;
                text-align: center !important;
                font-display: swap !important;
            }
        }
    }

    &.MyCustomTrustbadge-de {
        ._1gwv20v {
            ._lk7o4h {
                width: 182px !important;
            }
        }
    }

    &.MyCustomTrustbadge-de-en {
        ._1gwv20v {
            ._lk7o4h {
                width: 204px !important;
            }
        }
    }

    &.MyCustomTrustbadge-fr {
        ._1gwv20v {
            ._lk7o4h {
                width: 223px !important;
            }
        }
    }

    &.MyCustomTrustbadge-es {
        ._1gwv20v {
            ._lk7o4h {
                width: 254px !important;
            }
        }
    }

    &.MyCustomTrustbadge-mx {
        ._1gwv20v {
            ._lk7o4h {
                width: 254px !important;
            }
        }
    }

    &.MyCustomTrustbadge-it {
        ._1gwv20v {
            ._lk7o4h {
                width: 233px !important;
            }
        }
    }
}
</style>

<style lang="scss" scoped>
.ru_grid {
    gap: 60px;
    @media screen and (min-width: 768px) and (max-width: 1200px) {
        gap: 36px;
    }
    .footer-box {
        width: 170px !important;
        @media screen and (min-width: 1024px) and (max-width: 1200px) {
            width: 138px !important;
        }
        @media screen and (min-width: 768px) and (max-width: 1024px) {
            width: calc((100% - 144px) / 4) !important;
        }
    }
}
.fs-footer-box {
    width: 100%;
    background: #fff;
    border-top: 1px solid $borderColor2;
    padding: 32px 0;

    .fs-footer {
        @include newPcHeaderWidthBox();
        // padding-right: 80px;
        @media (max-width: 1360px) {
            padding-right: 80px;
        }
        @media (max-width: 1300px) {
            padding-right: 80px;
        }
        @media (max-width: 1200px) {
            width: 100%;
            padding: 0 32px;
            padding-right: 80px;
        }
        @include pad {
            padding: 0 24px;
            padding-right: 80px;
        }
        @include mobile {
            width: 100%;
            padding: 32px 16px 24px 16px;
        }
    }

    .footer-main {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        // flex-direction: row;
        .footer-box-nav {
            display: flex;
            justify-content: space-between;
            flex-grow: 1;
        }
    }

    .footer-box {
        flex-shrink: 0;
        &.footer-box-left {
            width: 280px;
        }

        .logo_box {
            padding: 16px 0 32px 0;

            .logo {
                display: block;
                width: 85px;

                &.logo-cn {
                    width: 135px;
                }

                > img {
                    display: block;
                    margin: 0 auto;
                    max-width: 100%;
                }
            }
        }

        .title {
            @include font14;
            color: $textColor1;
            margin-bottom: 12px;
            font-weight: 600;
        }

        .list {
            .list-item {
                margin-bottom: 8px;
                @include font12;
                > a {
                    color: $textColor3;
                    text-decoration: none;
                }
                .fs-hover-underline {
                    padding-bottom: 3.5px;
                    // transition: background .2s;
                    background-size: 100% 0;
                    background-position: left bottom;
                    &:hover {
                        color: $textColor1;
                        text-decoration: underline;
                        // text-decoration: none;
                        // background: linear-gradient($bgColor5, $bgColor5) repeat-x left bottom;
                        // background-size: 100% 1.5px;
                    }
                }

                .iconfont_com {
                    cursor: pointer;
                    display: inline-block;
                    width: 13px;
                    height: 13px;
                    margin-left: 4px;
                    // margin-top: 4px;
                    font-size: 13px;
                    color: $textColor3;
                    display: none !important;
                }

                &:last-child {
                    margin-bottom: 0;
                }
            }
        }

        // .email_form {
        //     margin-top: 16px;
        //     .form-item {
        //         display: flex;
        //         align-items: center;
        //         width: 100%;
        //         .email {
        //             flex: 1 1 auto;
        //             height: 40px;
        //             border-radius: 2px 0 0 2px;
        //             border: solid 1px $borderColor2;
        //             padding: 10px;
        //             line-height: 20px;
        //             height: 40px;
        //             font-size: 13px;
        //             &:focus {
        //                 border: solid 1px #8d8d8f;
        //             }
        //         }
        //         .sbtn {
        //             flex: 0 0 38px;
        //             height: 40px;
        //             display: flex;
        //             align-items: center;
        //             justify-content: center;
        //             background: #8d8d8f;
        //             transition: all 0.3s;
        //             cursor: pointer;
        //             border-radius: 0;
        //             &:hover {
        //                 background: #4c4948;
        //             }
        //             .iconfont-right {
        //                 font-size: 13px;
        //                 color: #fff;
        //             }
        //         }
        //     }
        //     .success_info {
        //         @include font13;
        //         color: $textColor1;
        //     }
        // }
        .share_box {
            display: flex;
            align-items: center;
            // justify-content: space-between;
            .iconfont-share {
                display: flex;
                font-size: 26px;
                color: #707070;
                cursor: pointer;
                width: 26px;
                height: 26px;
                align-items: center;
                justify-content: center;
                margin-right: 13px;

                &:last-child {
                    margin-right: 0;
                }

                &:hover {
                    color: #4b4b4d;
                    background-color: $btnBgColor4;
                }
            }
        }

        .jp_locale {
            div {
                color: #707070;
                &:first-of-type {
                    @include font13;
                    font-weight: 600;
                }
                &:last-of-type {
                    @include font12;
                }
            }
            margin: 16px 0px -8px;
        }

        .tel_box {
            margin-top: 16px;
            margin-bottom: 4px;
            @include font12;
            font-weight: 400;
            color: #707070;

            &.cn {
                margin-top: 0;
            }
            &.uk {
                margin-bottom: 0;
            }
        }

        .jp_txt {
            @include font12;
            font-weight: 400;
            color: #707070;
        }

        .about_fs_cn {
            display: flex;
            align-items: center;
            @include font12;
            font-weight: 400;
            color: #707070;
            > span {
                margin-right: 8px;
            }
            .wx-pop {
                width: 26px;
                height: 26px;
                position: relative;
                &:hover {
                    .fs-wx-wrap {
                        display: block;
                    }
                }
                img {
                    display: block;
                    widows: 100%;
                    height: auto;
                    cursor: pointer;
                }
                .fs-wx-wrap {
                    display: none;
                    position: absolute;
                    left: -50px;
                    top: 36px;
                    box-shadow: 0 1px 8px 0 rgb(120 102 102 / 30%);
                    background-color: #ffffff;
                    z-index: 10;
                    .triangle-up {
                        display: inline-block;
                        width: 16px;
                        height: 16px;
                        background-color: #fff;
                        box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.1);
                        transform: rotate(-135deg);
                        position: absolute;
                        top: -8px;
                        left: 55px;
                        z-index: 10;
                    }
                    .triangle-up-line {
                        position: absolute;
                        top: 0;
                        left: 0;
                        background-color: #fff;
                        width: 100%;
                        height: 15px;
                        z-index: 100;
                    }
                    .cont {
                        padding-top: 10px;
                    }
                }
            }
        }
        &.deClass {
            &:not(:last-child) {
                margin-right: 10px;
            }
            @media (max-width: 1024px) {
                .detitle {
                    word-wrap: break-word;
                }
            }
        }
        &.deClassL {
            @media (max-width: 1024px) {
                margin-right: 16px;
            }
        }
    }

    .footer_country_box {
        display: flex;
        justify-content: space-between;
        border-bottom: 1px solid $borderColor2;
        padding-bottom: 24px;
        margin-top: 24px;
        align-items: center;

        &.new_country_box {
            // justify-content: flex-end;
        }
        &.au_country_box {
            justify-content: flex-end;
        }
        .certification-box {
            display: flex;
            &.certification-box-uk {
                height: auto;
            }
            .uk_local_txt {
                @include font12;
                color: $textColor3;
            }
            // a {
            //     margin-right: 16px;

            //     &:last-of-type {
            //         margin-right: 0;
            //     }

            //     img {
            //         width: auto;
            //         max-height: 28px;
            //     }
            // }
        }

        .certification-cn-box {
            display: flex;
            a {
                margin-right: 20px;
                width: 80px;
                height: 40px;
                background: url("https://img-en.fs.com/zh/includes/templates/fiberstore/images/footer/customer_service_icon.png") no-repeat;
                text-decoration: none;
                &:nth-of-type(1) {
                    background-position: -61px -28px;
                }
                &:nth-of-type(2) {
                    background-position: -61px -126px;
                }
                &:nth-of-type(3) {
                    background-position: -61px -222px;
                }
                &:last-child {
                    width: auto;
                    display: flex;
                    align-items: center;
                    background: none;
                    img {
                        display: block;
                        width: 20px;
                        height: 20px;
                        margin-right: 4px;
                    }
                    p {
                        @include font12;
                        color: $textColor3;
                    }
                }
            }
        }

        .country_box_right {
            flex-shrink: 0;
            display: flex;
            align-items: center;
            position: relative;
        }

        .right-country {
            position: relative;
            .triangle-up {
                display: inline-block;
                width: 16px;
                height: 16px;
                background-color: #fff;
                box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.1);
                transform: rotate(-135deg);
                position: relative;
                z-index: 10;
            }
            .triangle-up-line {
                position: absolute;
                bottom: 0;
                right: 0;
                background-color: #fff;
                width: 100%;
                height: 16px;
                z-index: 100;
            }

            .current-country-box {
                display: flex;
                align-items: center;
                font-size: 13px;
                color: $textColor3;
                cursor: pointer;
                transition: all 0.3s;
                padding: 5px 0;
                .iconfont-global {
                    font-size: 18px;
                    display: block;
                    width: 18px;
                    height: 18px;
                    line-height: 18px;
                }
                img {
                    width: 18px;
                    height: 18px;
                }

                .country-info {
                    @include font12;
                    margin: 0 4px;
                    white-space: nowrap;
                }

                .iconfont-down {
                    font-size: 16px;
                    transition: all 0.2s;
                    // padding-top: 3px;
                    &.iconfont-down-up {
                        transform: rotateX(-180deg);
                    }
                }

                &:hover {
                    color: $textColor1;
                    .iconfont-global {
                        color: $textColor3;
                    }
                }
            }

            .country-currency-wrap {
                width: 300px;
                position: absolute;
                left: 50%;
                transform: translateX(-50%);
                bottom: 34px;
                z-index: 10;
                // transform: translate3d(-50%, 0, 0);
                .triangle-up {
                    position: absolute;
                    bottom: -8px;
                    left: 50%;
                    transform: translate3d(-50%, 0, 0) rotate(-135deg);
                }

                .country-currency-box {
                    .title {
                        font-size: 13px;
                        color: $textColor3;
                        font-weight: 400;
                        margin: 15px 0 10px 0;
                        line-height: initial;
                    }

                    .fs-button {
                        margin-top: 15px;
                        // border: 2px solid #19191a;
                        // height: 42px;
                        // font-weight: 600;
                    }
                }
            }
        }

        .line {
            display: inline-block;
            width: 1px;
            height: 8px;
            background: #ccc;
            margin: 0 20px;
        }
    }

    .app_box {
        font-size: 12px;
        color: $textColor3;
        font-weight: 400;
        align-items: center;
        display: flex;

        .iconfont-mobile {
            font-size: 18px;
            display: block;
            width: 18px;
            height: 18px;
            line-height: 18px;
            margin-right: 4px;
        }

        > a {
            color: $textColor3;
            @include font12;

            &:hover {
                text-decoration: underline;
                color: $textColor1;
            }
        }

        // &:hover {
        //     color: $textColor1;
        // }
    }

    .app_cn_box {
        font-size: 12px;
        color: $textColor3;
        font-weight: 400;
        align-items: center;
        display: flex;
        padding: 5px 0;
        position: relative;
        cursor: pointer;

        &:hover {
            .app-qr-wrap {
                display: block;
            }
        }

        .iconfont-mobile {
            font-size: 18px;
            margin-right: 4px;
        }

        > span {
            color: $textColor3;
            @include font12;
        }

        .app-qr-wrap {
            display: none;
            position: absolute;
            left: -20px;
            bottom: 34px;
            box-shadow: 0 1px 8px 0 rgb(120 102 102 / 30%);
            background-color: #ffffff;
            z-index: 10;
            .triangle-up {
                display: inline-block;
                width: 16px;
                height: 16px;
                background-color: #fff;
                box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.1);
                transform: rotate(-135deg);
                position: absolute;
                bottom: -8px;
                left: 50px;
                z-index: 10;
            }
            .triangle-up-line {
                position: absolute;
                bottom: 0;
                left: 0;
                background-color: #fff;
                width: 100%;
                height: 15px;
                z-index: 100;
            }
            .app-qr-box {
                padding: 10px 10px 15px 10px;
                img {
                    display: block;
                    width: 100px;
                    height: auto;
                }
            }
        }
    }

    .footer-bottom {
        padding: 24px 0 0 0;
        display: flex;
        align-items: flex-start;
        justify-content: space-between;
        gap: 20px;

        .left-cr-box {
            display: flex;
            align-items: center;
            justify-content: flex-end;
            flex-shrink: 0;
            white-space: nowrap;
            // padding-right: 30px;
            // @media screen and (min-width: 1024px) and (max-width: 1200px) {
            //     width: calc(50% - 10px);
            // }
            a {
                @include font12;
                color: $textColor3;
                text-decoration: none;
                margin-left: 8px;
            }
        }

        .copyright {
            font-size: 12px;
            color: $textColor3;
            text-align: right;
            line-height: 20px;
        }

        .policy-box {
            display: flex;
            align-items: center;
            flex: 1;
            flex-wrap: wrap;
            .line {
                display: inline-block;
                width: 1px;
                height: 8px;
                background: #ccc;
                margin: 0 8px;
            }
            > a {
                display: flex;
                color: $textColor3;
                align-items: center;
                position: relative;
                @include font12;
                cursor: pointer;
                &:last-child {
                    .line {
                        display: none;
                    }
                }

                &:hover {
                    color: $textColor1;
                }
            }
        }
    }

    .footer-certification-box {
        margin-top: 24px;
        display: flex;
        justify-content: flex-start;
        align-items: center;
        &.footer-certification-box_France {
            justify-content: space-between;
        }
        &.pay-icon {
            justify-content: space-between;
        }

        .certification-box {
            height: 28px;
            display: flex;
            &.certification-box-uk {
                height: auto;
            }
            .uk_local_txt {
                @include font12;
                color: $textColor3;
            }
            a {
                margin-right: 16px;

                &:last-of-type {
                    margin-right: 0;
                }

                img {
                    width: auto;
                    max-height: 28px;
                }
            }
        }

        .pay_box {
            display: flex;
            align-items: flex-end;

            img {
                margin-right: 16px;

                &:last-of-type {
                    margin-right: 0;
                }
            }
            &.pay-icon {
                align-items: center;
                > a {
                    height: 28px;
                    margin-left: 16px;
                    display: flex;
                    align-items: center;
                    &.uk_a {
                        padding-top: 4px;
                        display: block;
                        img {
                            display: block;
                        }
                    }
                }
            }
        }
    }

    @media (max-width: 820px) {
        display: none;
    }
}
</style>
