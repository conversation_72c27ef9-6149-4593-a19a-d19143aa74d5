<template>
    <div class="m-fs-footer m-fs-footer-new">
        <FooterSubscribe />
        <div class="mt0" v-if="footer_data && footer_data.length">
            <div :class="['mt0-box', { hide: !item.title && !(item.children && item.children.length) }]" v-for="item in footer_data" :key="item.id">
                <div class="mt0-title" @click.stop="handleClick(item)">
                    <div class="left">{{ item.title }}</div>
                    <div class="iconfont iconfont_down" :class="{ iconfont_down_up: item.title === list_active }">&#xe704;</div>
                </div>
                <slide-down>
                    <div class="mt0-ctn" v-if="item.children && item.children.length && item.title === list_active">
                        <p v-for="c in item.children" :key="c.title">
                            <!-- <a class="mt0-item" href="javascript:;" v-if="['chat', 'bug'].includes(c.url)" v-html="c.title" @click="feedbackShow"></a> -->
                            <a
                                :target="c.tag === 2 ? '_blank' : '_self'"
                                class="mt0-item"
                                @click.native="closeSlide"
                                :href="c.url.includes('solution_design.html') ? `${$localeLink(c.url)}?resource=1` : `${$localeLink(c.url)}`">
                                {{ c.title }}
                            </a>
                            <!-- <a
                                :target="c.tag === 2 ? '_blank' : '_self'"
                                class="mt0-item"
                                v-if="!['chat', 'bug'].includes(c.url) && $handleLink(c.url).type === 2"
                                :href="c.url.includes('solution_design.html') ? `/${c.url}?resource=1` : `/${c.url}`"
                                >>
                                {{ c.title }}
                            </a> -->
                        </p>
                    </div>
                </slide-down>
            </div>
            <!-- extra -->
            <div v-if="false" class="mt0-box">
                <div
                    class="mt0-title"
                    @click.stop="
                        showDownload = !showDownload
                        list_active = ''
                    ">
                    <div class="left">{{ $c("components.smallComponents.MFsFooter.mobile") }}</div>

                    <div class="iconfont iconfont_down" :class="{ iconfont_down_up: showDownload }">&#xe704;</div>
                </div>
                <slide-down>
                    <div class="mt0-ctn extra" v-if="showDownload">
                        <p>
                            <i class="iconfont" alt=""> &#xf270; </i>
                            <a @click="downloadIos" href="https://apps.apple.com/us/app/fs-com/id1441371183?l=zh&ls=1" class="mt0-item"> {{ $c("components.smallComponents.MFsFooter.ios") }}</a>
                        </p>
                        <p>
                            <i class="iconfont" alt=""> &#xf271; </i>
                            <a
                                target="_blank"
                                @click="downloadAndroid"
                                :href="
                                    ['cn', 'hk', 'tw', 'mo'].includes(website)
                                        ? 'https://airplan.fs.com/YX_0evWtMz4373v/app_version_management_download.php?action=downloadFile&file=701'
                                        : 'https://play.google.com/store/apps/details?id=cn.com.sf.fiberstore'
                                "
                                class="mt0-item">
                                {{ $c("components.smallComponents.MFsFooter.android") }}</a
                            >
                        </p>
                    </div>
                </slide-down>
            </div>
        </div>

        <!-- <div class="mt1">
            <m-change-website :onlyName="true" @showWebsite="showWebsite" @hideWebsite="hideWebsite"></m-change-website>
        </div> -->
        <div class="app_box">
            <template v-if="!isCn">
                <a class="app_item" :href="$c('components.smallComponents.footerSubscribe.android_app_link')" target="_blank">
                    <img src="https://resource.fs.com/mall/generalImg/202403271936115liwdb.png" alt="" />
                </a>
                <a class="app_item" :href="$c('components.smallComponents.footerSubscribe.ios_app_link')" target="_blank">
                    <img src="https://resource.fs.com/mall/generalImg/20240327193611q03isg.png" alt="" />
                </a>
            </template>
            <template v-else>
                <a class="app_item" href="https://cn-airplan.fs.com/YX_0evWtMz4373v/app_version_management_download.php?action=downloadFile&file=674" target="_blank">
                    <img src="https://resource.fs.com/mall/generalImg/2024053112070640o3cs.png" alt="" />
                </a>
                <a class="app_item" href="https://apps.apple.com/cn/app/%E9%A3%9E%E9%80%9F-fs-%E9%AB%98%E9%80%9F%E9%80%9A%E4%BF%A1%E4%B8%93%E5%AE%B6/id1551439855" target="_blank">
                    <img src="https://resource.fs.com/mall/generalImg/20240327193611q03isg.png" alt="" />
                </a>
            </template>
        </div>
        <div class="mt2" v-if="!isCn && socialLink && socialLink.length">
            <a class="iconfont iconfont-share" target="_blank" v-for="i in socialLink" :key="i.title" v-html="i.icon" :href="i.link[website] || i.link.en" :title="i.title"></a>
        </div>

        <!-- <div class="share_box" v-if="!isCn && socialLink && socialLink.length">
			<a class="iconfont iconfont-share" target="_blank" @click="socialPoint(i.title)" v-for="i in socialLink" :key="i.title" v-html="i.icon" :href="i.link[website] || i.link.en" :title="i.title"></a>
		</div> -->

        <!-- <template v-if="iso_code === 'jp'">
            <div class="jp_locale">
                <template v-if="website === 'en'">
                    <div>FS JAPAN CO., LTD.</div>
                    <div>JS Progress Building 5F, 4-1-23, Heiwajima, Ota Ku, Tokyo, 143-0006, Japan</div>
                </template>
                <template v-if="website === 'jp'">
                    <div>FS JAPAN株式会社</div>
                    <div>143-0006 東京都大田区平和島4-1-23 JSプログレビル 5階</div>
                </template>
            </div>
            <div :class="['tel_box', { cn: isCn }]" v-if="!isRussia">
                <div>
                    <template v-if="iso_code !== 'jp'">
                        <span v-show="website !== 'sg'">{{ website === "de" ? "" : $c("components.smallComponents.fsFooter.Call_us_at") + (website == "jp" ? "" : " ") }}</span>
                        <span v-show="website === 'sg'"><i class="iconfont">&#xe66c;</i>Call</span>
                    </template>
                    <template v-else>
                        <span> {{ website === "en" ? "TEL:" : website === "jp" ? "電話番号:" : $c("components.smallComponents.fsFooter.Call_us_at") }} </span>
                    </template>
                    <span>{{ top_data.phone || "" }}</span>
                </div>
                <div v-if="isJp || isJpEn">{{ $c("components.smallComponents.jpFax") }}</div>
            </div>
            <div class="jp_txt">
                <span v-if="website !== 'sg'">{{ website === "en" && iso_code === "jp" ? "Monday to Friday 10:00 – 17:00" : $c("components.FsContactUs.time1") }}</span>
                <span v-else> <i class="iconfont">&#xe632;</i>Mon - Fri｜24h Phone Support </span>
            </div>
        </template> -->
        <div class="certification-box-only" v-if="!(country_name === 'France' || countrysIcon[website] || isCn)">
            <!-- <a v-for="img in certifications[warehouse]" :key="img.imgUrl" :href="img.toUrl" :target="img.toUrl === 'javascript:;' ? '_self' : '_blank'" :aria-label="img.label">
                <img :src="img.imgUrl" :alt="img.label" :aria-label="img.label" />
            </a> -->

            <CertificationCtn :data="certifications[warehouse]" />
        </div>
        <div class="wx-pop" v-if="isCn">
            <fs-popover class="ctn_tip" position="bottom" :icon="false" :transfer="false" :mobileStyle="{ padding: '16px' }">
                <img slot="trigger" src="https://resource.fs.com/mall/generalImg/20230221160741dsqv2k.svg" width="32" height="32" alt="" />
                <div class="ctn_tip_tip">
                    <img src="https://resource.fs.com/mall/generalImg/20240704151602um8uhe.jpeg" width="190" height="190" alt="" />
                </div>
            </fs-popover>
        </div>
        <div class="certification-cn-box" v-if="isCn">
            <a href="https://ss.knet.cn/verifyseal.dll?sn=e15101544030060902l1bf000000&ct=df&a=1&pa=0.05508846207521856" target="_blank"></a>
            <a href="https://credit.szfw.org/CX20150924011838250320.html" target="_blank"></a>
            <!-- 去掉实名认证 -->
            <!-- <a href="https://v.yunaq.com/certificate?domain=www.fs.com&from=label&code=90020" target="_blank"></a> -->
            <a href="http://www.beian.gov.cn/portal/registerSystemInfo?recordcode=**************" target="_blank">
                <img src="https://img-en.fs.com/zh/includes/templates/fiberstore/images/beian_icon.png" alt="" />
                <p>粤公网安备 **************号</p>
            </a>
        </div>
        <ClientOnly>
            <div v-if="['fr', 'de', 'es', 'mx', 'ru', 'jp', 'it', 'au', 'sg'].includes(iso_code) || website === 'uk'">
                <div class="footer-certification-box" v-if="country_name === 'France' || countrysIcon[website]">
                    <div class="certification-box">
                        <template v-if="country_name === 'France'">
                            <!-- <a :href="img.toUrl" v-for="img in certifications[warehouse]['other']" :key="img.imgUrl" :target="img.toUrl === 'javascript:;' ? '_self' : '_blank'" :aria-label="img.label">
                        <img :src="img.imgUrl" :alt="img.label" :aria-label="img.label" />
                    </a> -->

                            <CertificationCtn :data="certifications[warehouse]['other']" />
                        </template>
                        <template v-else>
                            <template v-if="warehouse === 'DE' && ['uk', 'de-en', 'de'].includes(website)">
                                <!-- <a v-for="img in certifications[warehouse][website]" :key="img.imgUrl" :href="img.toUrl" :target="img.toUrl === 'javascript:;' ? '_self' : '_blank'" :aria-label="img.label">
                            <img :src="img.imgUrl" :alt="img.label" :aria-label="img.label" />
                        </a> -->
                                <CertificationCtn :data="certifications[warehouse][website]" />
                            </template>
                            <template v-else-if="warehouse === 'DE' && !['uk', 'de-en', 'de'].includes(website)">
                                <!-- <a v-for="img in certifications[warehouse]['other']" :key="img.imgUrl" :href="img.toUrl" :target="img.toUrl === 'javascript:;' ? '_self' : '_blank'" :aria-label="img.label">
                            <img :src="img.imgUrl" :alt="img.label" :aria-label="img.label" />
                        </a> -->
                                <CertificationCtn :data="certifications[warehouse]['other']" />
                            </template>
                            <template v-else>
                                <!-- <a v-for="img in certifications[warehouse]" :key="img.imgUrl" :href="img.toUrl" :target="img.toUrl === 'javascript:;' ? '_self' : '_blank'" :aria-label="img.label">
                            <img :src="img.imgUrl" :alt="img.label" :aria-label="img.label" />
                        </a> -->
                                <CertificationCtn :data="certifications[warehouse]" />
                            </template>
                        </template>
                    </div>

                    <div class="pay_box pay-icon" v-if="countrysIcon[website]">
                        <a v-for="(img, i) in getPayIcons(website)" :key="i" :href="img.href" :target="img.href === 'javascript:;' ? '_self' : '_blank'">
                            <img :src="img.imgur" alt="" />
                        </a>
                    </div>
                </div>
                <div class="mt3" :class="{ au_bottom: (website == 'au' || website == 'es') && screenWidth < 768, sg_bottom: website == 'sg' && screenWidth < 768 }">
                    <p class="copyright" v-html="top_data.right"></p>

                    <div class="policy-box">
                        <a :href="$localeLink(item.url)" v-for="item in policy_data" :key="item.title">{{ item.title }} <span class="line"></span></a>
                    </div>
                </div>
                <div v-if="(website === 'uk' || website === 'sg') && screenWidth < 768" class="m_uk_local_txt">
                    <p>Company Registration Number: 10876330</p>
                    <p>VAT Registration Number: GB277384560</p>
                </div>
            </div>
            <div v-else>
                <div class="mt3">
                    <p class="copyright" v-html="top_data.right"></p>
                    <div class="policy-box">
                        <a :href="$localeLink(item.url)" v-for="item in policyArray" :key="item.title">{{ item.title }} <span class="line"></span></a>
                    </div>
                </div>
                <div class="footer-certification-box" v-if="country_name === 'France' || countrysIcon[website]">
                    <div class="certification-box">
                        <template v-if="country_name === 'France'">
                            <!-- <a :href="img.toUrl" v-for="img in certifications[warehouse]['other']" :key="img.imgUrl" :target="img.toUrl === 'javascript:;' ? '_self' : '_blank'" :aria-label="img.label">
                        <img :src="img.imgUrl" :alt="img.label" :aria-label="img.label" />
                    </a> -->

                            <CertificationCtn :data="certifications[warehouse]['other']" />
                        </template>
                        <template v-else>
                            <template v-if="warehouse === 'DE' && ['uk', 'de-en', 'de'].includes(website)">
                                <!-- <a v-for="img in certifications[warehouse][website]" :key="img.imgUrl" :href="img.toUrl" :target="img.toUrl === 'javascript:;' ? '_self' : '_blank'" :aria-label="img.label">
                            <img :src="img.imgUrl" :alt="img.label" :aria-label="img.label" />
                        </a> -->
                                <CertificationCtn :data="certifications[warehouse][website]" />
                            </template>
                            <template v-else-if="warehouse === 'DE' && !['uk', 'de-en', 'de'].includes(website)">
                                <!-- <a v-for="img in certifications[warehouse]['other']" :key="img.imgUrl" :href="img.toUrl" :target="img.toUrl === 'javascript:;' ? '_self' : '_blank'" :aria-label="img.label">
                            <img :src="img.imgUrl" :alt="img.label" :aria-label="img.label" />
                        </a> -->
                                <CertificationCtn :data="certifications[warehouse]['other']" />
                            </template>
                            <template v-else>
                                <!-- <a v-for="img in certifications[warehouse]" :key="img.imgUrl" :href="img.toUrl" :target="img.toUrl === 'javascript:;' ? '_self' : '_blank'" :aria-label="img.label">
                            <img :src="img.imgUrl" :alt="img.label" :aria-label="img.label" />
                        </a> -->
                                <CertificationCtn :data="certifications[warehouse]" />
                            </template>
                        </template>
                    </div>

                    <div class="pay_box pay-icon" v-if="countrysIcon[website]">
                        <a v-for="(img, i) in getPayIcons(website)" :key="i" :href="img.href" :target="img.href === 'javascript:;' ? '_self' : '_blank'">
                            <img :src="img.imgur" alt="" />
                        </a>
                    </div>
                </div>
            </div>
        </ClientOnly>
    </div>
</template>
<script>
import SlideDown from "@/components/SlideDown/SlideDown.vue"
import MChangeWebsite from "@/components/ChangeWebsite/MChangeWebsite"
import FsPopover from "@/components/FsPopover"
import socialLink from "@/constants/socialLink.js"
import { mapState, mapMutations, mapGetters, mapActions } from "vuex"
import { certifications, countrysIcon, payIconList } from "@/constants/common.js"
import CertificationCtn from "./CertificationCtn"
import FooterSubscribe from "./FooterSubscribe.vue"

export default {
    name: "MFsFooter",
    components: {
        SlideDown,
        // MSelectCountry,
        MChangeWebsite,
        FsPopover,
        CertificationCtn,
        FooterSubscribe,
    },
    data() {
        return {
            //footer_data,
            showDownload: false,
            list_active: "",
            leavefb_show: false,
            socialLink,

            policy_data: [
                {
                    url: "privacy-policy",
                    title: this.$c("components.smallComponents.singleFooter.policy"),
                },
                {
                    url: "terms-of-use",
                    title: this.$c("components.smallComponents.singleFooter.terms"),
                },
            ],
            policy_data_en: [
                {
                    url: "privacy-notice",
                    title: this.$c("components.smallComponents.singleFooter.privacyNotice"),
                },
                {
                    url: "cookie-notice",
                    title: this.$c("components.smallComponents.singleFooter.cookiesNotice"),
                },
                {
                    url: "terms-of-use",
                    title: this.$c("components.smallComponents.singleFooter.terms"),
                },
            ],
            certifications: certifications,
            countrysIcon: countrysIcon,
            payIconList: payIconList,
        }
    },

    computed: {
        ...mapState({
            // footer_data: (state) => state.category.footer_data,
            top_data: (state) => state.category.top_data,
            website: (state) => state.webSiteInfo.website,
            warehouse: (state) => state.webSiteInfo.warehouse,
            country_name: (state) => state.webSiteInfo.country_name,
            pageGroup: (state) => state.ga.pageGroup,
            show_header_right: (state) => state.category.show_header_right,
            iso_code: (state) => (state.webSiteInfo.iso_code ? state.webSiteInfo.iso_code.toLowerCase() : ""),
            pageGroup: (state) => state.ga.pageGroup,
            screenWidth: (state) => state.device.screenWidth,
        }),
        ...mapGetters({
            isRussia: "webSiteInfo/isRussia",
            isCn: "webSiteInfo/isCn",
            isJp: "webSiteInfo/isJp",
            isJpEn: "webSiteInfo/isJpEn",
            footer_data: "category/max_footer_data",
        }),
        policyArray() {
            return this.website === "en" ? this.policy_data_en : this.policy_data
        },
    },
    methods: {
        ...mapMutations({
            setShowHeaderRight: "category/setShowHeaderRight",
        }),
        feedbackShow() {
            this.$emit("feedbackShow")
        },
        handleClick(item) {
            this.showDownload = false
            this.list_active = this.list_active === item.title ? "" : item.title
        },
        closeSlide() {
            console.log(123)
            this.list_active = ""
        },
        //  获取对应网站支付图标
        getPayIcons(country) {
            const result = []
            const arr = this.countrysIcon[country]
            if (arr) {
                arr.forEach((item) => {
                    if (item) {
                        result.push(this.payIconList[item])
                    }
                })
            }
            return result
        },
        downloadIos() {
            console.log("ios")
            if (window.dataLayer) {
                window.dataLayer.push({
                    event: "uaEvent",
                    eventCategory: this.pageGroup,
                    eventAction: "bottom_navigation",
                    eventLabel: "Download App_IOS",
                    nonInteraction: false,
                })
            }
        },
        downloadAndroid() {
            console.log("Android")
            if (window.dataLayer) {
                window.dataLayer.push({
                    event: "uaEvent",
                    eventCategory: this.pageGroup,
                    eventAction: "bottom_navigation",
                    eventLabel: "Download App_Android",
                    nonInteraction: false,
                })
            }
        },
        hideWebsite() {
            this.setShowHeaderRight(true)
        },
        showWebsite() {
            this.setShowHeaderRight(false)
        },
    },
}
</script>

<style lang="scss" scoped>
.m-fs-footer {
    width: 100%;
    // padding: 0 3% 1px 3%;
    padding: 0 0 1px 0;
    background: $bgColor1;
    border-top: 1px solid $borderColor2;
    display: none;
    .mt0-box {
        border-bottom: 1px solid $borderColor2;
        // padding: 0 5px;
        overflow: hidden;
        &.hide {
            display: none;
        }
    }
    .mt0 {
        padding: 0 16px;
    }
    .mt0-title {
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 46px;
        padding: 0;
        cursor: pointer;
        .left {
            @include font14;
            color: $textColor1;
        }
        .right {
            display: inline-block;
            width: 13px;
            height: 13px;
            position: relative;
            .line {
                width: 13px;
                height: 1px;
                position: absolute;
                background: #232323;
                left: 50%;
                top: 50%;
                transform: translate3d(-50%, -50%, 0);
                &.line2 {
                    transform: translate3d(-50%, -50%, 0) rotate(-90deg);
                    transition: all 0.3s;
                }
                &.line2_active {
                    transform: translate3d(-50%, -50%, 0) rotate(0deg);
                }
            }
        }
        .iconfont_down {
            display: inline-block;
            font-size: 16px;
            line-height: 1;
            padding: 4px;
            color: $textColor1;
            transition: all 0.3s;
            &.iconfont_down_up {
                transform: rotateX(-180deg);
            }
        }
    }
    .mt0-ctn {
        display: flex;
        flex-direction: column;

        .mt0-item {
            display: block;
            padding: 10px calc(3% + 15px - 16px);
            color: $textColor3;
            font-size: 13px;
            cursor: pointer;
            text-decoration: none;
            /* &:last-child {
                padding-bottom: 15px;
            } */
        }
    }
    .extra {
        > p {
            // padding: 5px 15px;
            padding: 10px calc(3% + 15px);
            // padding-bottom: 15px;
            display: flex;
            align-items: center;

            i {
                margin-right: 10px;
                color: #707070;
                //    vertical-align: middle;
            }
            .mt0-item {
                flex: 1;
                display: inline-block;
                padding: 0 !important;
            }
        }
    }
    .mt1 {
        display: flex;
        justify-content: center;
        align-items: center;
        margin: 20px 0;
    }

    .mt2 {
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 20px;
        .iconfont-share {
            display: flex;
            font-size: 16px;
            color: #707070;
            cursor: pointer;
            margin: 0 2px;
            width: 32px;
            height: 32px;
            align-items: center;
            justify-content: center;
            text-decoration: none;
            // &:hover{
            // 	color: #000;
            // }
        }
    }
    .jp_locale {
        text-align: center;
        margin: 30px 0px -8px;
        div {
            color: #707070;
            &:first-of-type {
                @include font13;
                font-weight: 600;
            }
            &:last-of-type {
                max-width: 280px;
                margin: 0 auto;
                @include font12;
            }
        }
    }
    .tel_box {
        margin-top: 16px;
        margin-bottom: 4px;
        @include font12;
        font-weight: 400;
        color: #707070;
        text-align: center;
        display: flex;
        justify-content: center;
        &.cn {
            margin-top: 0;
        }
        > div {
            margin-right: 16px;
            &:last-child {
                margin-right: 0;
            }
            > span {
                white-space: nowrap;
                .iconfont {
                    font-size: 12px;
                    margin-right: 8px;
                }
            }
        }
    }

    .jp_txt {
        @include font12;
        font-weight: 400;
        color: #707070;
        text-align: center;
        margin-bottom: 30px;
        .iconfont {
            font-size: 12px;
            margin-right: 8px;
        }
    }
    .mt3 {
        padding: 0 16px 24px 16px;
        // @media (max-width: 768px) {
        //     padding-bottom: 0;
        // }
        .copyright {
            @include font12;
            color: $textColor3;
            text-align: center;
        }
        .policy-box {
            display: flex;
            align-items: center;
            justify-content: center;
            @include font12;
            padding-top: 4px;
            flex-shrink: 0;
            > a {
                display: flex;
                color: $textColor3;
                align-items: center;
                position: relative;
                display: flex;
                align-items: center;
                .line {
                    display: inline-block;
                    width: 1px;
                    height: 10px;
                    border-left: 1px solid #707070;
                    font-size: 12px;
                    margin: 0 6px;
                }
                &:last-child {
                    .line {
                        display: none;
                    }
                }
                &:hover {
                    color: $textColor1;
                    text-decoration: none;
                }
            }
        }
    }
    .au_bottom {
        padding-bottom: 24px;
    }
    .sg_bottom {
        padding-bottom: 0;
    }
    .m_uk_local_txt {
        @include font12;
        color: $textColor3;
        text-align: center;
        padding-top: 4px;
        padding-bottom: 24px;
    }
    .wx-pop {
        margin-bottom: 20px;
        .ctn_tip {
            margin-left: 0;
            display: flex;
            justify-content: center;
        }
        ::v-deep .popper-mobile {
            .popper-mobile-content {
                width: auto;
                .ctn_tip_tip {
                    display: flex;
                    justify-content: center;
                    align-items: center;
                }
            }
        }
        @media (max-width: 820px) {
            display: none;
        }
    }
    .certification-cn-box {
        display: flex;
        gap: 12px;
        justify-content: center;
        padding: 4px 16px 20px;
        a {
            width: 80px;
            height: 40px;
            background: url("https://img-en.fs.com/zh/includes/templates/fiberstore/images/footer/customer_service_icon.png") no-repeat;
            text-decoration: none;
            // transform: scale(0.75);
            flex-shrink: 0;
            &:nth-of-type(1) {
                background-position: -61px -28px;
            }
            &:nth-of-type(2) {
                background-position: -61px -126px;
            }
            &:nth-of-type(3) {
                background-position: -61px -222px;
            }
            &:last-child {
                flex: 1;
                flex-shrink: initial;
                width: auto;
                display: flex;
                align-items: center;
                background: none;
                transform: scale(1);
                img {
                    display: block;
                    width: 20px;
                    height: 20px;
                    margin-right: 4px;
                }
                p {
                    @include font12;
                    color: $textColor3;
                }
            }
        }
    }
    .certification-box-only {
        height: 30px;
        margin: 20px 0;
        display: flex;
        justify-content: center;
        a {
            margin-right: 12px;
            user-select: none;
            -webkit-tap-highlight-color: rgba(255, 255, 255, 0);
            &:last-of-type {
                margin-right: 0;
            }
            img {
                width: auto;
                max-height: 24px;
            }
        }
    }
    .footer-certification-box {
        margin-top: 5px;
        @media (min-width: 821px) {
            margin-top: 24px;
        }
        display: flex;
        flex-direction: column;
        .certification-box {
            height: 24px;
            margin-bottom: 16px;
            display: flex;
            justify-content: center;
            a {
                margin-right: 12px;
                user-select: none;
                -webkit-tap-highlight-color: rgba(255, 255, 255, 0);
                &:last-of-type {
                    margin-right: 0;
                }
                img {
                    width: auto;
                    max-height: 24px;
                }
            }
        }
        .pay_box {
            display: flex;
            justify-content: center;
            flex-wrap: wrap;
            img {
                height: 20px;
                margin-right: 12px;
                margin-bottom: 16px;
                &:last-of-type {
                    margin-right: 0;
                }
            }
            @media (max-width: 414px) {
                img:nth-of-type(4) {
                    margin-right: 0;
                }
            }
        }
        .pay-icon {
            > a {
                height: 28px;
                margin-right: 12px;
                margin-bottom: 16px;
                @media (max-width: 820px) {
                    height: 20px !important;
                    margin-bottom: 20px !important;
                }
                img {
                    margin: 0 0;
                }
                &:last-child {
                    @media (max-width: 820px) {
                        margin-right: 0 !important;
                    }
                }
            }
        }
    }
    @media (max-width: 820px) {
        display: block;
    }
}
.app_box {
    display: flex;
    margin-bottom: 40px;
    @media (max-width: 820px) {
        justify-content: center;
        margin: 32px 0 16px;
    }
    .app_item {
        display: inline-block;
        margin-right: 12px;
        height: 28px;
        img {
            display: block;
            max-width: 100%;
            max-height: 100%;
        }

        &:last-child {
            margin-right: 0;
        }
    }
}
</style>
