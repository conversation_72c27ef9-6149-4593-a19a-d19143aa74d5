<template>
    <section class="subscribe_box">
        <!-- <div class="logo_box">
            <a tabindex="0" :class="['logo', { logo_cn: isCn }]" :href="localePath({ path: '/' })" aria-label="FS">
                <img :src="isCn ? 'https://resource.fs.com/mall/generalImg/20230703110143tw3bnh.png' : 'https://img-en.fs.com/includes/templates/fiberstore/images/fs-new/common/logo_footer.svg'" alt="FS LOGO" />
            </a>
        </div> -->
        <h3 class="subscribe_info">{{ $c("components.smallComponents.footerSubscribe.Stay_in_touch") }}</h3>
        <form class="form" @submit.prevent="submit">
            <div class="form_item">
                <input v-model="email" :placeholder="$c('components.smallComponents.footerSubscribe.Enter_your_email')" @input="input" />
                <FsButton type="red" htmlType="submit" :disabled="loading" :loading="loading">{{ $c("components.smallComponents.footerSubscribe.Subscribe") }}</FsButton>
            </div>
            <ValidateError :error="error_info" />
            <div v-if="success" class="error_info" :class="{ error_info_success: success }">{{ success_info }}</div>
        </form>
        <!-- <div class="agree_box">
            <input type="checkbox" v-model="check" />
            <p>I agree to FS's <a href="https://www.fs.com/policies/privacy_policy.html">Privacy Policy and Notice at Collection</a> and <a href="https://www.fs.com/policies/terms_of_use.html">Term of Use</a>.</p>
        </div> 
        <ValidateError v-if="!check" error="Please make sure you agree to our Privacy Policy and Notice at Collection and Terms of Use." />
		-->
    </section>
</template>

<script>
import FsButton from "@/components/FsButton/FsButton.vue"
import ValidateError from "@/components/ValidateError/ValidateError.vue"
import { mapState, mapMutations, mapGetters, mapActions } from "vuex"
import { getRecaptchaToken } from "@/util/grecaptchaHost"

export default {
    name: "FooterSubscribe",
    components: {
        FsButton,
        ValidateError,
    },
    data() {
        return {
            email: "",
            loading: false,
            check: true,
            success: "",
            error_info: "",
            success_info: "",
            recaptchaTp: false,
            recaptchaVal: "",
        }
    },
    computed: {
        ...mapState({
            isLogin: (state) => state.userInfo.isLogin,
            userInfo: (state) => state.userInfo.userInfo,
            cartList: (state) => state.cart.list,
            cartData: (state) => state.cart.cartData,
            savedCarts: (state) => state.userInfo.savedCarts,
            activeQuotes: (state) => state.userInfo.activeQuotes,
            menu_data: (state) => state.category.menu_data,
            classify_data: (state) => state.category.classify_data,
            top_data: (state) => state.category.top_data,
            // footer_data: (state) => state.category.footer_data,
            iso_code: (state) => (state.webSiteInfo.iso_code ? state.webSiteInfo.iso_code.toLowerCase() : ""),
            country_name: (state) => state.webSiteInfo.country_name,
            symbol: (state) => state.webSiteInfo.symbol,
            currency: (state) => state.webSiteInfo.currency,
            qty: (state) => state.cart.qty,
            website: (state) => state.webSiteInfo.website,
            warehouse: (state) => state.webSiteInfo.warehouse,
            pageGroup: (state) => state.ga.pageGroup,
            isMobile: (state) => state.device.isMobile,
            language: (state) => state.webSiteInfo.language,
            screenWidth: (state) => state.device.screenWidth,
            state1: (state) => state,
        }),
        ...mapGetters({
            isRussia: "webSiteInfo/isRussia",
            isCn: "webSiteInfo/isCn",
            isJp: "webSiteInfo/isJp",
            isJpEn: "webSiteInfo/isJpEn",
            footer_data: "category/max_footer_data",
        }),
    },
    methods: {
        input() {
            this.success = false
            if (/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(this.email.replace(/\s+/g, ""))) {
                this.error_info = ""
            }
        },
        // 谷歌人机校验
        getValidateCode(val) {
            this.recaptchaTp = val.recaptchaTp
            console.log(val, "valsss")
            if (val.recaptchaTp) {
                this.recaptchaVal = val.recaptchaVal
                this.submit()
            }
        },
        // 初始化谷歌验证
        initGrecaptcha() {
            this.recaptchaTp = false
            this.recaptchaVal = ""
        },
        async submit() {
            // if (this.loading) {
            //     return
            // }
            if (!this.email.replace(/\s+/g, "")) {
                this.success = false
                this.error_info = this.$c("components.smallComponents.footerSubscribe.Please_enter_your_email_address")
                return
            }
            if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(this.email.replace(/\s+/g, ""))) {
                this.success = false
                this.error_info = this.$c("components.smallComponents.footerSubscribe.Please_enter_a_valid_email_address")
                return
            }
            if (!this.check) {
                return
            }
            this.loading = true
            const { recaptchaTp, headers = {}, errorMsg = "grecaptcha_error" } = await getRecaptchaToken()
            if (!recaptchaTp) {
                this.loading = false
                this.$message.error(this.$c(`pages.Login.regist.${errorMsg}`))
                return
            }
            this.$axios
                .post("/api/subscribe", { email: this.email }, { headers })
                .then((res) => {
                    this.initGrecaptcha()
                    this.loading = false
                    if (res && res.data) {
                        this.success = true
                        this.success_info = this.$c("components.smallComponents.footerSubscribe.Welcome_to_FS_You_be_sent")
                    }
                })
                .catch((error) => {
                    this.initGrecaptcha()
                    this.loading = false
                    if (error && error.data && error.data.errors && error.data.errors.email) {
                        this.error_info = error.data.errors.email
                    }
                })
        },
    },
}
</script>

<style lang="scss" scoped>
.subscribe_box {
    width: 100%;
    @media (max-width: 820px) {
        padding: 20px 16px 21px;
        margin: 0;
        width: 100%;
    }
}

.logo_box {
    margin-bottom: 40px;
    @media (max-width: 820px) {
        margin-bottom: 24px;
    }

    .logo {
        display: block;
        width: 85px;
        @media (max-width: 820px) {
            width: 76px;
        }
        &.logo_cn {
            width: 135px;
        }
        > img {
            display: block;
            margin: 0 auto;
            max-width: 100%;
        }
    }
}
.form {
}
.form_item {
    display: flex;
    gap: 0;
    ::v-deep {
        input {
            width: auto;
            height: 32px;
            @include font12;
            border-radius: 4px 0 0 4px;
            background: #f6f6f8;
            border-width: 0;
            flex: 1 1 auto;
            transition: none;
            &:focus {
                background: #f6f6f8;
                border: 1px solid #eeeeee;
                &::placeholder {
                    opacity: 0;
                }
                &:hover {
                    background: #f6f6f8;
                }
            }
            &::placeholder {
                color: #707070;
            }
            &:hover {
                background: linear-gradient(0deg, rgba(25, 25, 26, 0.04), rgba(25, 25, 26, 0.04)), #f6f6f8;
                @media (max-width: 819px) {
                    background: #f6f6f8;
                }
            }
        }
        .fs-button {
            margin-left: -1px;
            border-radius: 0 4px 4px 0;
            flex-shrink: 0;
            height: 32px;
            @include font12;
            padding: 0 12px;
            // padding: 9px 20px;
        }
    }
}
.subscribe_info {
    @include font14;
    font-weight: 600;
    color: #19191a;
    margin-bottom: 12px;
    @media (max-width: 768px) {
        font-weight: normal;
    }
}

.agree_box {
    display: flex;
    align-items: flex-start;
    ::v-deep {
        input {
            margin-right: 8px;
            height: auto;
        }
    }
    p {
        padding-top: 1px;
        @include font12;
        color: $textColor2;

        a {
            color: $textColor1;
            text-decoration: underline;

            &:hover {
                color: $textColor2;
            }
        }
    }
}

.error_info {
    @include font13;
    color: #c00000;

    &.error_info_success {
        color: #10a300;
        padding-top: 4px;
    }
}
@media (max-width: 820px) {
    .subscribe_box {
        padding-top: 32px;
        // padding-bottom: 8px;
        .logo_box {
            display: none;
        }
        .form_item {
            ::v-deep {
                input {
                    height: 42px;
                    background: #fff;
                    font-size: 13px;
                    &:focus {
                        background: #fff;
                        &:hover {
                            background: #fff;
                        }
                    }
                    &:hover {
                        background: #fff;
                    }
                }
                .fs-button {
                    height: 42px;
                    padding: 10px 24px;
                    font-size: 14px;
                }
            }
        }
    }
    .error_info {
        @include font14;
        color: #c00000;
        &.error_info_success {
            color: #10a300;
            padding-top: 12px;
        }
    }
}
</style>
