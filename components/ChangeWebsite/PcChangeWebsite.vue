<template>
    <div class="select-country" ref="selectCountry" v-if="countryArealist && countryArealist.length" v-loading.fullscreen="loading">
        <div class="gray-bg-mask"></div>
        <!-- <ul class="province-list">
            <li class="province-item" :class="{ active: activeIndex === index }" v-for="(item, index) in countryArealist" :key="item.areaName" @click="areaNavClick(index)">
                <span>{{ item.areaName }}</span>
            </li>
            <li class="list_active_block" :style="{ top: activeIndex * 46 + 'px' }"></li>
        </ul> -->
        <div class="right-country-wrap">
            <div class="country-active-box">
                <span class="iconfont">&#xf060;</span>
                <span class="country-name" :aria-label="country_name">{{ country_name }}</span>
                <span class="country-lang-symbol" :aria-label="`${language} / ${symbol}${currency}`" v-if="currency === 'USD'">{{ " - " + `${language} / ${symbol} ${currency}` }}</span>
                <span class="country-lang-symbol" :aria-label="`${language} / ${symbol} ${currency}`" v-else>{{ " - " + `${language} / ${symbol} ${currency}` }}</span>
            </div>
            <div class="current_line"></div>
            <div class="search-box">
                <span v-if="!search && !focusValue" class="iconfont iconfont_search">&#xe694;</span>
                <input type="text" tabindex="0" class="inp" @focus.stop="focus" @blur="blur" @keyup="keyup" v-model.trim="search" @input="getListCountry()" />
                <div class="iconfont-clear" tabindex="0" @keyup.enter="clearSearch()" v-if="search" @click.stop="clearSearch()">
                    <span class="iconfont iconfont-close">&#xf30a;</span>
                    <!-- <img class="default" src="https://resource.fs.com/mall/generalImg/20230324174333lxr3fb.svg" alt="" />
                    <img class="hover" src="https://resource.fs.com/mall/generalImg/20230413155330uijiwk.svg" alt="" /> -->
                </div>
            </div>
            <div class="country-list-box">
                <!-- <ul class="country-box" v-if="countryArealist[activeIndex].areaList && countryArealist[activeIndex].areaList.length">
                    <template v-for="(item, index) in countryArealist[activeIndex].areaList">
                        <li
                            class="item"
                            :key="index"
                            :class="{ item_line: !search && countryArealist[activeIndex].linePosition && index + 1 === countryArealist[activeIndex].linePosition }"
                            v-if="isCn ? isShow(item.countries_chinese_name) : isShow(item.countries_name)">
                            <div :key="activeIndex">
                                <span tabindex="0" @keyup.enter="click(item)" @click.stop="click(item)">
                                    <span class="country-name">{{ (isCn ? item.countries_chinese_name : item.countries_name) + " - " }}</span>
                                    <span class="country-lang-symbol" v-if="item.currency === 'USD'">{{ `${item.language} / ${item.symbol}${item.currency}` }}</span>
                                    <span class="country-lang-symbol" v-else>{{ `${item.language} / ${item.symbol} ${item.currency}` }}</span>
                                </span>
                            </div>
                        </li>
                    </template>
                </ul> -->
                <ul class="country-box" v-if="areaListCountry && areaListCountry.length">
                    <li class="item" v-for="(item, index) in areaListCountry" :key="index" :class="{ item_line: !search && areaCountry.linePosition && index + 1 === areaCountry.linePosition }">
                        <div>
                            <span tabindex="0" @keyup.enter="click(item)" @click.stop="click(item)">
                                <span class="country-name">{{ (isCn ? item.countries_chinese_name : item.countries_name) + " - " }}</span>
                                <span class="country-lang-symbol" v-if="item.currency === 'USD'">{{ `${item.language} / ${item.symbol} ${item.currency}` }}</span>
                                <span class="country-lang-symbol" v-else>{{ `${item.language} / ${item.symbol} ${item.currency}` }}</span>
                            </span>
                        </div>
                    </li>
                </ul>
                <p v-else>
                    {{ $c("components.smallComponents.noCountryResult") }}
                </p>
            </div>
        </div>
    </div>
</template>
<script>
/*
    切换站点组件(新版 根据州来对国家进行分类选择)
    如果在选择国家时，需要触发其他操作，则需要传入change事件；

    国家信息和区号保存保存在store中，/store/selectCountry.js
    @parmas isRequest [Boolean] 是否进行接口请求  默认false  只需要请求一次，数据保存在/store/selectCountry.js 
*/

import { mapState, mapGetters, mapActions } from "vuex"

export default {
    name: "PcChangeWebsite",
    components: {},
    props: {
        isRequest: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {
            activeIndex: 0,
            country_loading: false,
            search: "",
            placeholder: this.$c("components.smallComponents.searchCountry"),
            loading: false,
            areaListCountry: [],
            areaCountry: {},
            focusValue: false,
        }
    },
    computed: {
        ...mapState({
            pageGroup: (state) => state.ga.pageGroup,
            countryArealist: (state) => state.selectCountry.countryArealist,
            country_name: (state) => state.webSiteInfo.country_name,
            iso_code: (state) => state.webSiteInfo.iso_code,
            language: (state) => state.webSiteInfo.language,
            symbol: (state) => state.webSiteInfo.symbol,
            currency: (state) => state.webSiteInfo.currency,
        }),
        ...mapGetters({
            isRussia: "webSiteInfo/isRussia",
            isCn: "webSiteInfo/isCn",
        }),
    },
    created() {
        if (process.client && this.isRequest) {
            this.getCountryArea()
        }
    },
    watch: {
        countryArealist: {
            handler(val) {
                if (val) {
                    this.getListCountry()
                }
            },
            deep: true,
            immediate: true,
        },
    },
    mounted() {},
    methods: {
        ...mapActions({
            updateSiteInfo: "selectCountry/updateSiteInfo",
            getCountryArea: "selectCountry/getCountryArea",
        }),
        focus() {
            this.focusValue = true
            // this.$emit("focus", this.countryArealist[this.activeIndex].areaName)
        },
        blur() {
            this.focusValue = false
        },
        keyup(e) {
            if (this.search && e.keyCode !== 8) {
                // this.$emit("input", this.countryArealist[this.activeIndex].areaName, this.search)
            }
        },
        areaNavClick(i) {
            this.activeIndex = i
            this.$emit("toogleClick")
        },
        click({ iso_code, language, currency, symbol, countries_chinese_name, countries_name }) {
            this.loading = true
            this.updateSiteInfo({
                iso_code: iso_code,
                language: language,
                currency: currency,
            })
            this.$emit("change", `${this.isCn ? countries_chinese_name : countries_name} - ${language} / ${symbol} ${currency}`)
        },
        gaUE(eventAction, eventLabel) {
            let obj = { event: "uaEvent", eventCategory: "Customer Service Page", eventAction: eventAction, eventLabel: eventLabel, nonInteraction: false }
            window.dataLayer && window.dataLayer.push(obj)
        },
        clearSearch() {
            this.search = ""
            this.getListCountry()
        },
        isShow(name) {
            if (this.search === "") return true
            let status = true
            if (this.isCn) {
                status = !!~name.indexOf(this.search)
            } else {
                if ((this.search === "中国" || this.search === "中" || this.search === "国") && name && name.toLowerCase() === "china") {
                    //国家/语言切换问题，要求从其他站点切换到中文站时，支持搜索“china”和“中国”，当前只支持搜“china”。（boss反馈）
                    status = true
                } else {
                    let reg = new RegExp("^" + this.search, "gi")
                    status = reg.test(name)
                }
            }
            return status
        },
        getListCountry() {
            let value = this.search
            // this.countryArealist && this.countryArealist.length ? (this.areaCountry = this.countryArealist[this.activeIndex]) : ""
            this.areaListCountry = this.countryArealist || []
            if (value) {
                this.areaListCountry.length
                    ? (this.areaListCountry = this.countryArealist.filter((item) => {
                          if (this.isCn) {
                              return !!~item.countries_chinese_name.indexOf(value)
                          } else {
                              if ((value === "中国" || value === "中" || value === "国") && item.countries_name && item.countries_name.toLowerCase() === "china") {
                                  //国家/语言切换问题，要求从其他站点切换到中文站时，支持搜索“china”和“中国”，当前只支持搜“china”。（boss反馈）
                                  return item
                              } else {
                                  let reg = new RegExp("^" + this.search, "gi")
                                  return reg.test(item.countries_name)
                              }
                          }
                      }))
                    : ""
            }
        },
    },
}
</script>
<style lang="scss" scoped>
.select-country {
    width: 100%;
    height: 440px;
    border: none;
    border-radius: 4px;
    box-shadow: 0 1px 8px 0 rgba(120, 102, 102, 0.3);
    padding: 20px;
    display: flex;
    color: $textColor1;
    .gray-bg-mask {
        width: 100%;
        height: 100%;
        border-radius: 3px;
        position: absolute;
        left: 0;
        top: 0;
        z-index: -1;
        background: #fff;
    }
    .province-list {
        display: none;
        width: 170px;
        position: relative;
        li {
            height: 46px;
            padding: 0 0 0 16px;
            background-color: transparent;
            @include font13;
            cursor: pointer;
            position: relative;
            display: flex;
            align-items: center;
            overflow: hidden;
            border-radius: 3px 0 0 3px;
            span {
                padding-bottom: 4px;
                background-size: 100% 0;
                background-position: left bottom;
            }
            &:hover {
                background-color: $bgColor1;
                span {
                    @include fsUnderline;
                }
            }
            &.active {
                span {
                    @include fsUnderline;
                }
                &:hover {
                    background-color: transparent;
                }
            }
            &.list_active_block {
                width: 100%;
                height: 46px;
                position: absolute;
                left: 0;
                top: 0;
                // transition: all .3s ease-out;
                border-radius: 3px 0 0 3px;
                background: #fff;
                z-index: -1;
            }
        }
    }
    .right-country-wrap {
        flex: 1;
        padding: 0;
        background-color: $bgColor3;
        display: flex;
        flex-direction: column;
        border-radius: 0 0 3px 0;
        .country-active-box {
            padding: 0;
            display: flex;
            align-items: center;
            .iconfont {
                font-size: 14px;
                margin-right: 8px;
                color: #10a300;
            }
            .country-name {
                max-width: 124px;
                margin-right: 3px;
                @include txt-hid;
            }
            .country-name,
            .country-lang-symbol {
                display: inline-block;
                @include font14;
                font-weight: 600;
            }
        }
        .search-box {
            position: relative;
            margin-bottom: 8px;
            .iconfont_search {
                display: inline-block;
                width: 16px;
                height: 16px;
                font-size: 12px;
                position: absolute;
                left: 12px;
                top: 50%;
                transform: translate3d(0, -50%, 0);
                cursor: pointer;
                text-align: center;
                line-height: 16px;
                color: $textColor2;

                &:hover {
                    color: $textColor1;
                    transition: all 0.3s;
                }
            }
            .iconfont-clear {
                width: 28px;
                height: 28px;
                position: absolute;
                right: 7px;
                top: 50%;
                transform: translateY(-50%);
                display: flex;
                justify-content: center;
                align-items: center;
                cursor: pointer;
                .iconfont-close {
                    display: block;
                    width: 16px;
                    height: 16px;
                    line-height: 16px;
                    font-size: 16px;
                    text-align: center;
                    color: $textColor3;
                }
                &:hover {
                    background: rgba(25, 25, 26, 0.04);
                    border-radius: 3px;

                    .iconfont {
                        color: $textColor3;
                    }
                }
            }
        }
        .inp {
            width: 100%;
            height: 34px;
            padding: 8px 36px 8px 12px;
            // background-color: #fafafb;
            @include font12;
            &::-webkit-input-placeholder {
                @include font12;
                color: #cccccc;
            }
            &:focus {
                border: 1px solid $textColor1;
                outline: none;
                border-radius: 3px;
            }
            &:focus::placeholder {
                color: transparent;
            }
        }
        .country-list-box {
            flex: 1;
            overflow-y: auto;
            margin-right: -20px;
            padding-right: 20px;
            .country-box {
                li {
                    padding: 0;
                    @include font12;
                    > div {
                        padding: 8px 12px;
                        > span {
                            padding-bottom: 4px;
                            cursor: pointer;
                            &:hover {
                                text-decoration: underline;
                                color: #0060bf;
                                .country-name {
                                    color: #0060bf;
                                }
                            }
                        }
                    }
                    .country-lang-symbol {
                        color: $textColor6;
                        cursor: pointer;
                    }
                    &.item_line {
                        border-bottom: 1px solid #ccc;
                    }
                    &:focus-visible {
                        outline-offset: -2px;
                        border-radius: 3px;
                    }
                }
            }
            > p {
                color: $textColor3;
                @include font12;
                padding: 8px 12px;
            }
        }
    }
}
.current_line {
    margin: 12px 0;
    border-bottom: 1px solid #e5e5e5;
}
</style>
