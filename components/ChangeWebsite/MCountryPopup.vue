<template>
    <div class="m-country-popup" :class="{ m_country_popup_header: isHeader }" v-loading.fullscreen="save_loading">
        <transition name="fade">
            <div class="mask" v-if="show" @click.stop="maskClick"></div>
        </transition>
        <transition :name="isHeader ? 'slide-right' : 'slide-down'">
            <div class="m-country-main" v-if="show && activeIndex === -1">
                <div class="m-country-header">
                    <span class="text">{{ $c("components.smallComponents.selectCountry") }}</span>
                    <span class="iconfont iconfont-close" @click.stop="close">&#xf30a;</span>
                </div>

                <div class="m-country-ctn">
                    <div class="country-active-box">
                        <span class="iconfont">&#xf060;</span>
                        <span class="country-name" :aria-label="country_name">{{ country_name + " - " }}</span>
                        <span class="country-lang-symbol" :aria-label="`${language} / ${symbol}${currency}`" v-if="currency === 'USD'">{{ ` ${language} / ${symbol} ${currency}` }}</span>
                        <span class="country-lang-symbol" :aria-label="`${language} / ${symbol} ${currency}`" v-else>{{ ` ${language} / ${symbol} ${currency}` }}</span>
                    </div>
                    <div class="province-list" v-if="countryArealist && countryArealist.length">
                        <a class="ctn-item" href="javascript:;" @click.stop="activeIndex = index" v-for="(item, index) in countryArealist" :key="item.areaName">
                            <div class="left">
                                <span class="province-name">{{ item.areaName }}</span>
                            </div>
                            <span class="iconfont iconfont-right">&#xe703;</span>
                        </a>
                    </div>
                </div>
            </div>
        </transition>

        <transition :name="isHeader ? 'slide-right' : 'fade'">
            <div ref="m-country-main-more" class="m-country-main m-country-main-more" :class="{ 'm-country-main-more-top': isHeader }" :style="top ? { top: `${top}px` } : {}" v-if="show && activeIndex !== -1">
                <div class="m-country-header" @click.stop="isHeader ? closeCountry(true) : ''">
                    <span v-show="isHeader" class="iconfont iconfont-back">&#xe702;</span>
                    <!-- <span class="text">{{ $c("components.smallComponents.selectCountry") }}</span> -->
                    <div class="country-active-box">
                        <span class="iconfont">&#xf060;</span>
                        <span class="country-name">{{ `${country_name} - ${language} / ${symbol} ${currency}` }}</span>
                        <!-- <span class="country-lang-symbol">{{ `${language} / ${symbol} ${currency}` }}</span> -->
                    </div>
                    <!-- <span class="iconfont iconfont-left" @click.stop="hideCountryList">&#xf048;</span> -->
                    <span v-show="!isHeader" class="iconfont iconfont-close" @click.stop="isHeader ? '' : closeCountry()">&#xf30a;</span>
                </div>
                <div class="m-country-header-line"></div>
                <div class="m-country-ctn">
                    <div class="search-box">
                        <span class="iconfont iconfont-search" v-if="!search && showSearchIcon">&#xe694;</span>
                        <input class="inp" type="text" v-model.trim="search" @input="getListCountry()" @focus="showSearchIcon = false" @blur="showSearchIcon = true" />
                        <!-- <img v-if="search" @click.stop="search = ''" class="iconfont-clear" src="https://resource.fs.com/mall/generalImg/20230324174333lxr3fb.svg" alt="" /> -->

                        <span v-if="search" @click.stop="clearSearch()" class="iconfont iconfont-clear">&#xf30a;</span>
                    </div>
                    <div class="mt1">
                        <!-- <div class="country_list" v-if="countryArealist[activeIndex].areaList && countryArealist[activeIndex].areaList.length">
                            <template v-for="item in countryArealist[activeIndex].areaList">
                                <a class="ctn-item" href="javascript:;" @click.stop="websiteChange(item)" :key="item.id" v-show="isCn ? isShow(item.countries_chinese_name) : isShow(item.countries_name)">
                                    <div class="left">
                                        <span class="country-name">{{ (isCn ? item.countries_chinese_name : item.countries_name) + " - " }}</span>
                                        <span class="country-lang-symbol" v-if="item.currency === 'USD'">{{ `${item.language} / ${item.symbol}${item.currency}` }}</span>
                                        <span class="country-lang-symbol" v-else>{{ `${item.language} / ${item.symbol} ${item.currency}` }}</span>
                                    </div>
                                </a>
                            </template>
                        </div> -->
                        <div class="country_list" v-if="areaListCountry && areaListCountry.length">
                            <a class="ctn-item" href="javascript:;" @click.stop="websiteChange(item)" v-for="item in areaListCountry" :key="item.id">
                                <div class="left">
                                    <span class="country-name">{{ (isCn ? item.countries_chinese_name : item.countries_name) + " - " }}</span>
                                    <span class="country-lang-symbol" v-if="item.currency === 'USD'">{{ `${item.language} / ${item.symbol} ${item.currency}` }}</span>
                                    <span class="country-lang-symbol" v-else>{{ `${item.language} / ${item.symbol} ${item.currency}` }}</span>
                                </div>
                            </a>
                        </div>
                        <p v-else>
                            {{ $c("components.smallComponents.noCountryResult") }}
                        </p>
                    </div>
                </div>
            </div>
        </transition>
    </div>
</template>

<script>
/*
	M端切换站点组件(新版 根据州来对国家进行分类选择)
	save保存后触发close事件
*/
import SlideDown from "@/components/SlideDown/SlideDown.vue"
import FsButton from "@/components/FsButton/FsButton.vue"
import { mapState, mapGetters, mapActions, mapMutations } from "vuex"
export default {
    name: "MCountryPopup",
    components: {
        SlideDown,
        FsButton,
    },
    props: {
        show: {
            type: Boolean,
            default: false,
        },
        mask_click: {
            type: Boolean,
            default: true,
        },
        top: {
            type: Number,
            default: 0,
        },
        isHeader: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {
            activeIndex: 0,
            search: "",
            save_loading: false,
            placeholder: this.$c("components.smallComponents.searchCountry"),
            areaListCountry: [],
            areaCountry: {},
            showSearchIcon: true,
        }
    },
    computed: {
        ...mapState({
            countryArealist: (state) => state.selectCountry.countryArealist,
            country_name: (state) => state.webSiteInfo.country_name,
            iso_code: (state) => state.webSiteInfo.iso_code,
            language: (state) => state.webSiteInfo.language,
            symbol: (state) => state.webSiteInfo.symbol,
            currency: (state) => state.webSiteInfo.currency,
        }),
        ...mapGetters({
            isRussia: "webSiteInfo/isRussia",
            isCn: "webSiteInfo/isCn",
        }),
    },
    mounted() {
        document.addEventListener("click", this.otherClickHide, true)
    },
    destroyed() {
        document.removeEventListener("click", this.otherClickHide, true)
    },
    watch: {
        countryArealist: {
            handler(val) {
                if (val) {
                    this.getListCountry()
                }
            },
            deep: true,
            immediate: true,
        },
    },
    methods: {
        ...mapMutations({}),
        ...mapActions({
            updateSiteInfo: "selectCountry/updateSiteInfo",
        }),
        hideCountryList() {
            this.activeIndex = 0
        },
        closeCountry(flag = false) {
            this.close(flag)
            this.hideCountryList()
        },
        close(flag = false) {
            this.$emit("close", flag)
        },
        websiteChange({ iso_code, language, currency, symbol }) {
            if (this.save_loading) {
                return
            }
            this.save_loading = true
            window.localStorage.removeItem("showEntry")
            this.closeCountry(true)
            this.updateSiteInfo({
                iso_code: iso_code,
                language: language,
                currency: currency,
            })
        },
        maskClick() {
            if (this.mask_click) {
                this.closeCountry()
            }
        },
        clearSearch() {
            this.search ? (this.search = "") : ""
            this.getListCountry()
        },
        isShow(name) {
            if (this.search === "") {
                return true
            }
            if ((this.search === "中国" || this.search === "中" || this.search === "国") && name && name.toLowerCase() === "china") {
                return true
            } else {
                let reg = new RegExp("^" + this.search, "gi")
                return reg.test(name)
            }
        },
        otherClickHide(e) {
            if (this.show) {
                let _el = this.$refs["m-country-main-more"]
                let _btnEl = document.querySelector(".m-change-website .current-country")
                if (_el && _btnEl) {
                    if (!_el.contains(e.target) && !_btnEl.contains(e.target)) {
                        this.isHeader ? "" : this.$emit("close")
                    }
                }
            }
        },
        getListCountry() {
            let value = this.search
            // this.countryArealist && this.countryArealist.length ? (this.areaCountry = this.countryArealist[this.activeIndex]) : ""
            this.areaListCountry = this.countryArealist || []
            if (value) {
                this.areaListCountry.length
                    ? (this.areaListCountry = this.areaListCountry.filter((item) => {
                          if (this.isCn) {
                              return !!~item.countries_chinese_name.indexOf(value)
                          } else {
                              if ((value === "中国" || value === "中" || value === "国") && item.countries_name && item.countries_name.toLowerCase() === "china") {
                                  //国家/语言切换问题，要求从其他站点切换到中文站时，支持搜索“china”和“中国”，当前只支持搜“china”。（boss反馈）
                                  return item
                              } else {
                                  let reg = new RegExp("^" + this.search, "gi")
                                  return reg.test(item.countries_name)
                              }
                          }
                      }))
                    : ""
            }
        },
    },
}
</script>

<style lang="scss" scoped>
.m-country-popup {
    color: $textColor1;
    .mask {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.3);
        z-index: 101;
        transition: opacity 0.3s;
        display: none;
    }
    .m-country-main {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        z-index: 200;
        background: #fff;
        display: flex;
        flex-direction: column;
        transition: all 0.3s ease-in-out;
        .m-country-header {
            // border-bottom: 1px solid $borderColor2;
            @include font16;
            font-weight: 400;
            display: flex;
            justify-content: space-between;
            align-items: center;
            color: $textColor1;
            padding: 16px;
            position: relative;
            .iconfont-left {
                color: $textColor3;
                font-size: 16px;
                cursor: pointer;
            }
            .iconfont-close {
                position: absolute;
                top: 0;
                right: 0;
                display: flex;
                justify-content: center;
                align-items: center;
                height: 100%;
                padding: 0 16px;
                color: $textColor3;
                font-size: 16px;
                cursor: pointer;
            }
        }
        .m-country-header-line {
            margin: 0 auto 12px;
            width: calc(100% - 32px);
            border-bottom: 1px solid $borderColor2;
        }
        .m-country-ctn {
            padding: 0 16px;
            .country-active-box {
                padding: 12px 0;
                @include font14;
                // border-bottom: 1px solid #f7f7f7;
                display: flex;
                align-items: center;
                .iconfont {
                    color: #10a300;
                    font-size: 12px;
                    margin-right: 8px;
                }
                .country-lang-symbol {
                    color: $textColor1;
                }
            }
            .ctn-item {
                display: flex;
                align-items: center;
                justify-content: space-between;
                padding: 12px 0;
                position: relative;
                border-bottom: 1px solid #f7f7f7;
                cursor: pointer;
                @include font14;
                .country-icon {
                    display: inline-block;
                    width: 18px;
                    height: 18px;
                    background-repeat: no-repeat;
                    background-size: cover;
                    background-position: center;
                    border-radius: 18px;
                    margin-right: 8px;
                }
                .iconfont-right {
                    font-size: 12px;
                    color: $textColor1;
                }
            }
            .btn-box {
                background: #fff;
                padding: 20px 0;
                position: relative;
                z-index: 20;
                > button {
                    width: 100%;
                }
            }
        }
        .country-lang-symbol {
            color: $textColor6;
        }
        &.m-country-main-more {
            z-index: 250;
            // height: 80%;
            // max-height: 586px;
            box-shadow: inset 0 2px 6px 0 #ededed;
            top: 82px;
            @media (max-width: 960px) {
                top: 48px;
            }
            .m-country-header {
                // border-bottom: none;
            }
            .country-active-box {
                // padding: 12px 0;
                @include font14;
                font-weight: 600;
                display: flex;
                align-items: center;
                .iconfont {
                    color: #10a300;
                    font-size: 12px;
                    margin-right: 8px;
                }
                .country-lang-symbol {
                    color: $textColor1;
                }
            }
            .m-country-ctn {
                height: calc(100% - 56px);
                display: flex;
                flex-direction: column;
                padding: 0 16px;

                .search-box {
                    width: 100%;
                    position: relative;
                    margin-bottom: 12px;
                    .inp {
                        height: 34px;
                        border-radius: 3px;
                        background-color: #fff;
                        @include font12;
                        line-height: 20px;
                        color: $textColor1;
                        padding: 8px 36px 8px 12px;
                        &::-webkit-input-placeholder {
                            @include font14;
                            color: #cccccc;
                        }
                        &:focus {
                            border: 1px solid $textColor1;
                        }
                        &:focus::placeholder {
                            color: transparent;
                        }
                    }

                    .iconfont-search {
                        position: absolute;
                        left: 12px;
                        top: 50%;
                        transform: translateY(-50%);
                        color: $textColor3;
                        font-size: 12px;
                        width: 16px;
                        height: 16px;
                        text-align: center;
                        line-height: 16px;
                    }
                    .iconfont-clear {
                        width: 16px;
                        height: 16px;
                        line-height: 16px;
                        font-size: 12px;
                        position: absolute;
                        right: 12px;
                        top: 50%;
                        transform: translateY(-50%);
                        color: $textColor3;
                        &:hover {
                            cursor: pointer;
                            color: $textColor1;
                        }
                    }
                }
                .mt1 {
                    flex: 1;
                    overflow-y: auto;
                    margin-right: -16px;
                    padding-right: 16px;
                    .country_list {
                        .ctn-item {
                            padding: 12px;
                            border-bottom: none;
                        }
                    }
                    > p {
                        color: $textColor3;
                        @include font14;
                        padding: 12px;
                    }
                }
            }
            &.m-country-main-more-top {
                .m-country-header {
                    justify-content: flex-start;
                    border-bottom: none;
                    cursor: pointer;
                    .text {
                        font-weight: 600;
                    }
                    .iconfont-back {
                        font-size: 12px;
                        margin-right: 4px;
                    }
                }
                .country_list {
                    .country-name {
                        color: $textColor1;
                    }
                }
            }
        }
    }
    &.m_country_popup_header {
        .m-country-main {
            position: absolute;
        }
        .m-country-main-more {
            top: 0;
        }
        .m-country-popup {
            transition: slide-right;
        }
    }
}
</style>
