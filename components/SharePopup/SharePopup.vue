<template>
    <fs-popup-new :show="show" @close="handleClose" transition="slide-up" width="680" :title="title" :loading="loading || submitLoading" mobileWidth="calc(100% - 64px)">
        <div class="contain">
            <div class="header between">
                <p class="tit">{{ $c("common.module.addATeam") }}</p>
                <span class="handle" @click="handleShareAll">{{ $c("common.module.shareToAllMyTeams") }}</span>
            </div>
            <FsDropdown v-model="visible">
                <template v-slot:label>
                    <div class="select-box" :class="{ active: visible }">
                        <div class="select-label" :class="{ placeholder: checkedList.length === 0 }">
                            {{ formTypeLabel }}
                        </div>
                        <span class="iconfont">&#xe703;</span>
                    </div>
                </template>
                <template v-slot:menu>
                    <div class="select-menu" ref="selectMenu">
                        <div class="select-item" v-for="item in teamList" :key="item.id">
                            <label class="select-item-label">
                                <input type="checkbox" :checked="checkedList.includes(item.id)" @change="handleCheckboxChange($event, item)" />
                                <p>{{ item.name }}</p>
                            </label>
                            <span class="small" v-if="currentList.some((i) => i.id === item.id)">{{ $c("common.module.currentlyShared") }}</span>
                        </div>
                    </div>
                </template>
            </FsDropdown>
            <div class="cur-share" v-if="checkedListData.length">
                <div class="header between">
                    <p class="tit">{{ $c("common.module.newlyAdded") }}</p>
                    <span class="handle" @click="handleDisableNewAll">{{ $c("common.basic.disableAll") }}</span>
                </div>

                <ul class="content">
                    <li v-for="(item, index) in checkedListData" :key="index" class="item_team between">
                        <p class="tit">{{ item.name }}</p>
                        <span class="handle" @click="handleDisableNew(index)">{{ $c("common.basic.disable") }}</span>
                    </li>
                </ul>
            </div>

            <div class="cur-share" v-if="currentList.length">
                <div class="header between">
                    <p class="tit">{{ $c("common.module.currentlySharedWith") }}</p>
                    <span class="handle" @click="handleDisableCurAll">{{ $c("common.basic.disableAll") }}</span>
                </div>

                <ul class="content">
                    <li v-for="(item, index) in currentList" :key="index" class="item_team between">
                        <p class="tit">{{ item.name }}</p>
                        <span class="handle" @click="handleDisableCur(item, index)">{{ $c("common.basic.disable") }}</span>
                    </li>
                </ul>
            </div>
        </div>
        <template slot="footer">
            <div class="btn">
                <div class="btn_dobue">
                    <fs-button type="blackline" :text="$c('common.basic.Cancel')" @click="close"></fs-button>
                    <fs-button type="red" :disabled="checkedList.length === 0 && keepCurrentList.length === currentList.length" :text="$c('common.basic.save')" @click="save"></fs-button>
                </div>
            </div>
        </template>
    </fs-popup-new>
</template>

<script>
import FsPopupNew from "@/components/FsPopupNew/FsPopupNew.vue"
import FsButton from "@/components/FsButton/FsButton.vue"
import FsSelect from "@/components/FsSelect/FsSelect.vue"
import FsDropdown from "@/components/FsDropdown/FsDropdown.vue"
import { mapState } from "vuex"
import { deepClone } from "@/util/util.js"
export default {
    name: "SharePopup",
    components: {
        FsButton,
        FsPopupNew,
        FsSelect,
        FsDropdown,
    },
    model: {
        prop: "show",
        event: "change",
    },
    props: {
        pageType: {
            type: String,
            default: "tax", // tax || address ||account
        },
        show: {
            type: Boolean,
            default: false,
        },
        dataItem: {
            type: Object,
            default: () => {},
        },
        addressType: {
            //分享地址时区分收获地址和账单地址字段，0:账单地址，1:运输地址
            type: Number,
            default: 0,
        },
        popupTitle: {
            type: String,
            default: "",
        },
    },
    data() {
        return {
            // title: this.pageType == "tax" ? this.$c("common.module.taxShareTitle") : this.$c("pages.AddressBook.whoCanView"),
            loading: false,
            teamList: [],
            currentList: [],
            keepCurrentList: [],
            checkedList: [],
            visible: false,
            submitLoading: false,
        }
    },
    computed: {
        ...mapState({
            userInfo: (state) => state.userInfo.userInfo,
        }),
        checkedListData() {
            return this.teamList.filter((i) => this.checkedList.includes(i.id)) || []
        },
        formTypeLabel() {
            const { checkedList, checkedListData } = this
            console.log("checkedList", checkedList)
            return checkedList.length ? checkedListData.map((i) => i.name).join(";") : this.$c("form.form.please_select")
        },
        title() {
            if (this.popupTitle) {
                return this.popupTitle
            } else {
                return this.pageType == "tax" ? this.$c("common.module.taxShareTitle") : this.$c("pages.AddressBook.whoCanView")
            }
        },
    },
    watch: {
        show(val) {
            val && this.getInitList()
        },
    },
    mounted() {},
    methods: {
        handleClose() {
            this.$emit("change", false)
        },
        async getInitList() {
            try {
                if (this.loading) return
                this.loading = true
                this.checkedList = []
                const map = {
                    address: () => [
                        this.$axios.get("/api/companyAccount/getMyCanShareTeam"),
                        this.$axios.get("/api/getCurrentShareTeam", {
                            params: {
                                address_book_id: this.dataItem?.addressBookId,
                                address_type: this.addressType,
                            },
                        }),
                    ],
                    tax: () => [
                        this.$axios.get("/api/companyAccount/getMyCanShareTeam"),
                        this.$axios.get("/api/free_tax/getCurrentShareTeam", {
                            params: {
                                certificates_id: this.dataItem?.id,
                            },
                        }),
                    ],
                    account: () => [
                        this.$axios.get("/api/companyAccount/getMyCanShareTeam"),
                        this.$axios.get("/api/shipping_account/getCurrentShareTeam", {
                            params: {
                                account_id: this.dataItem?.id,
                            },
                        }),
                    ],
                }
                const promiseList = map[this.pageType]
                const [teamRes, curRes] = await Promise.all(promiseList())
                this.teamList = teamRes.data
                this.currentList = curRes.data
                this.keepCurrentList = deepClone(curRes.data)
            } catch (error) {
                console.error(error.message)
            }
            this.loading = false
        },
        handleShareAll() {
            this.checkedList = this.teamList.map((i) => i.id)
        },
        handleCheckboxChange(event, item) {
            const bol = event.target.checked
            if (bol) {
                this.checkedList.push(item.id)
            } else {
                const index = this.checkedList.findIndex((i) => i === item.id)
                if (index > -1) {
                    this.checkedList.splice(index, 1)
                }
            }
        },
        handleDisableNewAll() {
            this.checkedList = []
        },
        handleDisableNew(index) {
            this.checkedList.splice(index, 1)
        },
        handleDisableCurAll() {
            this.currentList = []
            // const callback = () => {
            //     this.currentList = []
            // }
            // const bizTeamIds = this.currentList.map((i) => i.id)
            // const map = {
            //     address: () => this.fetchAddressDelShare(bizTeamIds, callback),
            //     tax: () => this.fetchTaxDelShare(bizTeamIds, callback),
            // }
            // map[this.pageType] && map[this.pageType]()
        },
        handleDisableCur(item, index) {
            this.currentList.splice(index, 1)
            // const callback = () => {
            //     this.currentList.splice(index, 1)
            // }
            // const bizTeamIds = [item.id]
            // const map = {
            //     address: () => this.fetchAddressDelShare(bizTeamIds, callback),
            //     tax: () => this.fetchTaxDelShare(bizTeamIds, callback),
            // }
            // map[this.pageType] && map[this.pageType]()
        },
        // async fetchTaxDelShare(bizTeamIds, callback) {
        //     try {
        //         this.loading = true
        //         const params = {
        //             certificatesId: this.dataItem?.id,
        //             bizTeamIds,
        //         }
        //         await this.$axios.post("/api/free_tax/del_share", params)
        //         callback()
        //     } catch (error) {
        //         console.error(error.message)
        //     }
        //     this.loading = false
        // },
        // async fetchAddressDelShare(bizTeamIds, callback) {
        //     try {
        //         this.loading = true
        //         const params = {
        //             bizTeamIds,
        //             bizAccountId: this.userInfo.companyInfo.bizAccountId,
        //             addressBookId: this.dataItem.address_book_id,
        //         }
        //         await this.$axios.post("/api/del_share_address", params)
        //         callback()
        //     } catch (error) {
        //         console.error(error.message)
        //     }
        //     this.loading = false
        // },
        /**
         * 获取添加和删除的id集合，并过滤添加和删除的交集
         */
        getAddAndDelIds() {
            const curIds = this.currentList.map((i) => i.id) || []
            const keepCurIds = this.keepCurrentList.map((i) => i.id) || []
            const delIds = keepCurIds.filter((i) => !curIds.includes(i))
            //获取交集
            const filterIds = this.checkedList.filter((i) => delIds.includes(i))
            const maxCheckIds = this.checkedList.filter((i) => !filterIds.includes(i))
            const maxDelIds = delIds.filter((i) => !filterIds.includes(i))
            return { maxCheckIds, maxDelIds }
        },
        async fetchTaxShare() {
            try {
                this.submitLoading = true
                const { maxCheckIds, maxDelIds } = this.getAddAndDelIds()
                const params = {
                    certificatesId: this.dataItem?.id,
                    addBizTeamIds: maxCheckIds,
                    delBizTeamIds: maxDelIds,
                }
                await this.$axios.post("/api/free_tax/addAndDelList", params)
                this.close()
                this.$emit("success")
            } catch (error) {
                console.error(error.message)
            }
            this.submitLoading = false
        },
        async fetchAddressShare() {
            console.log("bizTeamIds:", this.checkedList, "bizAccountId: ", this.userInfo.companyInfo.bizAccountId, " addressBookId: ", this.dataItem?.addressBookId)
            const { maxCheckIds, maxDelIds } = this.getAddAndDelIds()
            try {
                this.submitLoading = true
                const params = {
                    addBizTeamIds: maxCheckIds,
                    delBizTeamIds: maxDelIds,
                    bizAccountId: this.userInfo.companyInfo.bizAccountId,
                    addressBookId: this.dataItem.addressBookId,
                    // addressType: [1, 0][this.addressType || 0],
                    addressType: this.addressType,
                }
                await this.$axios.post("/api/addAndDelListAddress", params)
                this.close()
                this.$emit("success")
            } catch (error) {
                console.error(error.message)
            }
            this.submitLoading = false
        },
        async fetchShippingAccountShare() {
            try {
                this.submitLoading = true
                const { maxCheckIds, maxDelIds } = this.getAddAndDelIds()
                const params = {
                    customersShippingAccountId: this.dataItem?.id,
                    bizAccountId: this.userInfo.companyInfo.bizAccountId,
                    addBizTeamIds: maxCheckIds,
                    delBizTeamIds: maxDelIds,
                }
                await this.$axios.post("/api/shipping_account/addAndDelListAccount", params)
                this.close()
                this.$emit("success")
            } catch (error) {
                console.error(error.message)
            }
            this.submitLoading = false
        },
        async save() {
            const map = {
                address: () => this.fetchAddressShare(),
                tax: () => this.fetchTaxShare(),
                account: () => this.fetchShippingAccountShare(),
            }
            map[this.pageType] && map[this.pageType]()
        },

        close() {
            this.$emit("change")
        },
    },
}
</script>

<style lang="scss" scoped>
::v-deep .loading-wrap {
    height: 100% !important;
}
.contain {
    padding: 16px 24px 24px;
    min-height: 200px;
    color: #19191a;
    @include font14();
    .header {
        margin-bottom: 4px;
    }
    .between {
        display: flex;
        justify-content: space-between;
        align-items: center;
        @include font12();
        .handle {
            @include blueLink();
        }
    }
    .cur-share {
        margin-top: 20px;
    }
    .content {
        padding: 0 24px;
        background: #f7f7f7;
        border-radius: 3px;
        max-height: 234px;
        overflow-y: auto;
        > li {
            padding: 20px 0;
            border-bottom: 1px solid #eeeeee;
            &:last-child {
                border-bottom: 0;
            }
        }
    }
}
.btn {
    padding: 0 24px 24px;
    display: flex;
    justify-content: flex-end;
    .btn_dobue {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        grid-gap: 16px;
    }
}

.select-box {
    width: 100%;
    display: flex;
    align-items: center;
    border: 1px solid #e5e5e5;
    border-radius: 3px;
    overflow: hidden;
    padding: 10px 12px;
    &.active {
        border-color: #19191a;
        .iconfont {
            color: #19191a;
            transform: rotate(-90deg);
        }
    }
    .select-label {
        flex: 1;
        min-height: 22px;
        @include font13();
        cursor: pointer;
        &.placeholder {
            color: #707070;
            @include font13();
        }
        @include txt-hid;
    }
    .iconfont {
        font-size: 12px;
        margin-left: 6px;
        color: #707070;
        transform: rotate(90deg);
        transition: transform 0.2s;
    }
}
.select-menu {
    max-height: 336px;
    border-radius: 3px;
    border: 1px solid #e5e5e5;
    background-color: #fff;
    overflow: auto;
    padding: 5px 0;

    .select-item {
        display: flex;
        align-items: center;
        padding: 8px 12px;
        cursor: pointer;
        transition: all 0.2s;
        &:hover {
            background: #f7f7f7;
        }

        .select-item-label {
            display: flex;
            align-items: center;
            flex: 1;
            cursor: pointer;
            > p {
                @include font14();
                margin-left: 8px;
            }
        }
        .small {
            color: #707070;
        }
        input[type="checkbox"]:hover:before {
            color: #cccccc;
        }
        input[type="checkbox"]:checked:before {
            color: #707070;
        }
    }
}
@include mediaM {
    .contain {
        padding: 20px 16px;
    }
    .btn {
        padding: 0 16px 16px;
        .btn_dobue {
            width: 100%;
            grid-template-columns: repeat(1, 1fr);
            ::v-deep .fs-button-red {
                order: -1;
            }
        }
    }
}
</style>
