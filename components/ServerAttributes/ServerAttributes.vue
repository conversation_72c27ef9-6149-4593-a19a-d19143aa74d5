<template>
    <div class="server_attributes">
        <section v-for="(v, i) in list" :key="i">
            <h6>{{ v.server_option_id_name || v.option_name || v.optionName }}</h6>
            <p v-if="v.value_name || v.valueName">
                {{ v.value_name || v.valueName }}
            </p>
            <template v-else>
                <template v-if="isNewStyle">
                    <div v-for="(c, k) in v.server_option_values || v.option_values || v.options" :key="k" class="server_value">
                        <p>{{ c.server_value_id_name || c.value_name || c.valueName }}</p>
                        <p>
                            <span v-if="c.server_value_products_qty || c.valueProductsQty">x{{ setAttribute(c.server_value_products_qty || c.valueProductsQty) }}</span
                            ><span v-else-if="c.accessories_product_qty">{{ setAttribute(c.accessories_product_qty) }}</span>
                        </p>
                    </div>
                </template>
                <template v-else>
                    <p v-for="(c, k) in v.server_option_values || v.option_values || v.options" :key="k">
                        <span v-if="c.server_value_products_qty || c.valueProductsQty">{{ setAttribute(c.server_value_products_qty || c.valueProductsQty) }}</span
                        ><span v-else-if="c.accessories_product_qty">{{ setAttribute(c.accessories_product_qty) }}</span
                        >{{ c.server_value_id_name || c.value_name || c.valueName }}
                    </p>
                </template>
            </template>
        </section>
    </div>
</template>

<script>
/*
    服务器定制属性组件
    @parmas list [Object]                
	
*/
import { mapState } from "vuex"
export default {
    name: "ServerAttributes",
    components: {},
    props: {
        list: {
            type: [Object, Array],
            default: {},
        },
        isNewStyle: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {}
    },
    computed: {
        ...mapState({
            language: (state) => state.webSiteInfo.language,
        }),
    },
    mounted() {},
    methods: {
        // 格式化定制服器属性个数显示格式
        setAttribute(data) {
            if (this.language === "English") {
                return this.isNewStyle ? `${data} ` : `(${data}) `
            } else if (this.language === "Deutsch" || this.language === "Français" || this.language === "Español") {
                return `${data} x `
            } else if (this.language === "Pусский" || this.language === "日本語" || this.language === "Italiano") {
                return `${data}x `
            } else {
                return `${data}x `
            }
        },
    },
}
</script>

<style lang="scss" scoped>
.server_attributes {
    @include font12;
    color: $textColor1;
    word-break: break-word;
    h6 {
        padding: 8px 0 4px 0;
        @include font12;
        color: $textColor5;
    }
    p {
        color: $textColor3;
    }
    .server_value {
        display: flex;
        align-items: center;
        &:not(:last-child) {
            margin-bottom: 4px;
        }
        p {
            &:first-child {
                margin-right: 12px;
                width: 321px;
                max-width: 70%;
            }
        }
    }
}
@include mediaM {
    .server_attributes {
        width: 100%;
        .server_value {
            justify-content: space-between;
            p {
                &:first-child {
                    max-width: 80%;
                }
            }
        }
    }
}
</style>
