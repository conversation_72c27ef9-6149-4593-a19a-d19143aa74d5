<template>
    <div>
        <div class="fs-global-float-wrap" :class="{ opacity: isScroll && !isPC, 'is-pc': isPC }" v-if="!isDoNotShow && isRender" :style="pageStyle">
            <transition name="fade">
                <div class="box" v-show="showBackBtn">
                    <div class="btn-box" tabindex="0" @keyup.enter.stop="backTop" @click.stop="backTop">
                        <span class="iconfont iconfont_backtop">&#xe630;</span>
                    </div>
                </div>
            </transition>

            <div class="box">
                <SlideDown>
                    <div v-show="!isFold || isPC">
                        <div class="chat-box btn-box" :class="{ line: !isPC }" tabindex="0" @keyup.enter="chat" @click.stop="chat" @mouseenter="enterFn('chat')" @mouseleave="leaveFn" id="bdtj_lxwm">
                            <template v-if="!unreadMsgStatus">
                                <span class="iconfont iconfont_chat">&#xe6a0;</span>
                            </template>
                            <img v-else src="https://resource.fs.com/mall/generalImg/202306211654294kt8p6.svg" alt="" />
                        </div>
                        <div
                            class="form-box btn-box"
                            :class="{ line: !isPC }"
                            tabindex="0"
                            @keyup.enter="openProblemConsultationPop"
                            @keyup.esc="closeProblemConsultationPop"
                            @click.stop="openProblemConsultationPop"
                            @mouseenter="enterFn('form')"
                            @mouseleave="leaveFn">
                            <span class="iconfont iconfont_form">&#xe728;</span>
                        </div>
                    </div>
                </SlideDown>

                <div v-if="!isPC" class="fold-box btn-box" :class="{ folded: isFold }" tabindex="0" @keyup.enter.stop="changeFold" @click="changeFold">
                    <span class="iconfont iconfont_fold">&#xe708;</span>
                </div>
            </div>
            <!-- <problem-consultation-popup :show="showFormPop" @closeProblemConsultationPop="closeProblemConsultationPop"></problem-consultation-popup> -->
        </div>
        <div class="fs_global_btn_box_padMobile" v-if="!isDoNotShow && isRender" :style="pageStyle">
            <transition name="fade">
                <div class="padMobile_btn backTop_btn" v-show="showBackBtn">
                    <div class="btn-box" tabindex="0" @keyup.enter.stop="backTop" @click.stop="backTop">
                        <span class="iconfont iconfont_backtop">&#xe630;</span>
                    </div>
                </div>
            </transition>
            <div id="bdtj_lxwm" class="padMobile_btn livechat_btn" tabindex="0" @keyup.enter="chat" @click.stop="chat" @mouseenter="enterFn('chat')" @mouseleave="leaveFn">
                <template v-if="!unreadMsgStatus">
                    <span class="iconfont iconfont_chat">&#xe6a0;</span>
                </template>
                <img v-else src="https://resource.fs.com/mall/generalImg/202306211654294kt8p6.svg" alt="" />
            </div>
            <div
                class="padMobile_btn consult_btn"
                tabindex="0"
                @keyup.enter="openProblemConsultationPop"
                @keyup.esc="closeProblemConsultationPop"
                @click.stop="openProblemConsultationPop"
                @mouseenter="enterFn('form')"
                @mouseleave="leaveFn">
                <span class="iconfont iconfont_form">&#xe728;</span>
            </div>
        </div>
        <problem-consultation-popup :show="showFormPop" @closeProblemConsultationPop="closeProblemConsultationPop" :customStyle="pageStyle"></problem-consultation-popup>
    </div>
</template>

<script>
import ProblemConsultationPopup from "@/popup/ProblemConsultationPopup/ProblemConsultationPopup"
import { scrollTo, throttle } from "@/util/util.js"
import { mapState, mapMutations } from "vuex"
import { hideLiveChat } from "@/util/util.js"
import SlideDown from "@/components/SlideDown/SlideDown.vue"
export default {
    name: "FsGlobalFloatBtn",
    components: { ProblemConsultationPopup, SlideDown },
    props: {},
    data() {
        return {
            showBackBtn: false,
            showFormPop: false,
            pageStyle: {},
            activeIndex: -1,
            timer: null,
            isScroll: false,
            scrollTimer: null,
            isFold: false,
            isRender: false,
        }
    },
    inject: ["fsLiveChat"],
    computed: {
        ...mapState({
            pageGroup: (state) => state.ga.pageGroup,
            showCookieTip: (state) => state.webSiteInfo.showCookieTip,
            noReplyNum: (state) => state.liveChat.noReplyNum,
            isMobile: (state) => state.device.isMobile,
            screenWidth: (state) => state.device.screenWidth,
            screenHeight: (state) => state.device.screenHeight,
            unreadMsgStatus: (state) => state.liveChat.unreadMsgStatus,
        }),
        isDoNotShow() {
            return this.isMobile && /^home/g.test(this.$route.name) && this.showCookieTip
        },
        isPC() {
            return this.screenWidth > 1024
        },
    },

    mounted() {
        if (typeof window !== "undefined") {
            this.isFold = localStorage.getItem("suspendFold") === "true"
        }
        this.isRender = true
        this.onScroll("init")
        if (typeof window !== "undefined") {
            window.addEventListener("scroll", this.onScroll)
            if (window.localStorage && window.localStorage.getItem("noReplyNum") > 0) {
                let num = window.localStorage.getItem("noReplyNum")
                this.$store.commit("liveChat/setNoReplyNum", num)
            }
            window.addEventListener("resize", this.handleResize)
        }
        this.initPosition()
    },

    methods: {
        ...mapMutations({
            hideLiveChat: "liveChat/hideLiveChat",
        }),
        gaEvent(eventAction, eventLabel) {
            if (typeof window !== "undefined") {
                window.dataLayer &&
                    window.dataLayer.push({
                        event: "uaEvent",
                        eventCategory: this.pageGroup,
                        eventAction: eventAction,
                        eventLabel: eventLabel,
                        nonInteraction: false,
                    })
            }
        },
        handleResize() {
            this.initPosition()
        },
        initPosition() {
            if (typeof document === "undefined") return

            let isVisible = null
            const el =
                document.querySelector(".cookie") ||
                document.querySelector(".product_wrap .m_add_cart_box") ||
                document.querySelector(".order_detail .total-float") ||
                document.querySelector(".products_fixed_box") ||
                document.querySelector(".cart-box .postfix")

            if (el && typeof window !== "undefined") {
                const computedStyle = window.getComputedStyle(el)
                isVisible = computedStyle.visibility !== "hidden" && computedStyle.display !== "none"
            }
            if (isVisible) {
                let bottomValue
                if (document.querySelector(".cookie")) {
                    console.log("this.screenWidth", this.screenWidth)
                    // if (this.screenWidth > 1024) {
                    //     bottomValue = el.offsetHeight + 48 + "px"
                    // } else {
                    //     bottomValue = el.offsetHeight + 20 + "px"
                    // }
                    // 只有有cookieTip出现，所有端间距都是20px
                    bottomValue = el.offsetHeight + 20 + "px"
                } else {
                    bottomValue = el.offsetHeight + 16 + "px"
                }
                this.pageStyle = {
                    bottom: bottomValue,
                }
                if (typeof window !== "undefined" && typeof MutationObserver !== "undefined") {
                    const observer = new MutationObserver(() => {
                        const chatEl = document.querySelector("iframe.fsLiveChat")
                        const cookieEl = document.querySelector(".cookie")
                        console.log("chatEl==", chatEl.style.bottom, el.offsetHeight)

                        if (chatEl) {
                            if (this.screenWidth > 1024) {
                                chatEl.style.bottom = el.offsetHeight + 32 + "px"
                                if (cookieEl) {
                                    chatEl.style.bottom = el.offsetHeight + 4 + "px"
                                }
                            } else if (this.screenWidth >= 768) {
                                chatEl.style.bottom = el.offsetHeight + "px"
                                if (cookieEl) {
                                    chatEl.style.bottom = el.offsetHeight + 4 + "px"
                                }
                            } else {
                                chatEl.style.bottom = 0 + "px"
                            }
                            console.log("chatElAAAAAA==", chatEl.style.bottom, el.offsetHeight)
                            observer.disconnect()
                        }
                    })
                    observer.observe(document.body, { childList: true, subtree: true })
                }

                const chatEl = document.querySelector("iframe.fsLiveChat")
                if (chatEl) {
                    if (chatEl.offsetTop <= 56) {
                        chatEl.style.height = this.screenHeight - el.offsetHeight - 56 + "px"
                    } else {
                        chatEl.style.height = "632px"
                    }
                }
            } else {
                // 页面高度变化，极限距离
                console.log("screenHeight", this.screenHeight, this.screenHeight - 80)
                const chatEl = document.querySelector("iframe.fsLiveChat")
                if (chatEl && this.screenWidth > 760) {
                    if (this.screenHeight - 80 < 632) {
                        chatEl.style.height = this.screenHeight - 80 + 32 + "px"
                        chatEl.style.bottom = 24 + "px"
                        this.pageStyle = {
                            bottom: "40px",
                        }
                    } else {
                        chatEl.style.height = "632px"
                        chatEl.style.bottom = "24px"
                        this.pageStyle = {
                            bottom: "40px",
                        }
                    }
                }
            }
        },
        onScroll(val) {
            this.clearScrollTimer()
            if (val !== "init") {
                this.isScroll = true
            }

            if (typeof document !== "undefined" && typeof window !== "undefined") {
                let scrollTop = document.documentElement.scrollTop || document.body.scrollTop
                let clientHeight = window.screen.availHeight
                let fix = /^products/g.test(this.$route.name) ? 1 : 2
                this.showBackBtn = scrollTop >= clientHeight * fix
            }

            this.scrollTimer = setTimeout(() => {
                this.isScroll = false
            }, 200)
            this.initPosition()
        },
        chat() {
            this.showFormPop = false
            this.fsLiveChat()
            this.gaEvent("floating_button", "Live Chat")
            this.$bdRequest({
                conversionTypes: [
                    {
                        logidUrl: "https://cn.fs.com/?bd_vid=uANBIyIxUhNLgvw-I-tzPHf1PWT1g17xnHTzPj64PWRsrjmLP6",
                        newType: 1,
                    },
                ],
            })
        },
        closeChat() {
            hideLiveChat()
        },
        enterFn(type) {
            this.timer = setTimeout(() => {
                if (type === "chat" && !this.$store.state.liveChat.showLiveChat) {
                    this.chat()
                } else if (type === "form") {
                    this.openProblemConsultationPop()
                }
            }, 3000)
        },
        leaveFn() {
            this.timer && clearInterval(this.timer)
            this.timer = null
        },
        clearScrollTimer() {
            this.scrollTimer && clearTimeout(this.scrollTimer)
            this.scrollTimer = null
        },
        backTop() {
            if (!this.showBackBtn) return
            if (typeof document !== "undefined") {
                scrollTo(document.body, 9)
            }
        },
        openProblemConsultationPop() {
            this.showFormPop = true
            this.closeChat()
            this.gaEvent("floating_button", "Contact Sales")
            this.$bdRequest({
                conversionTypes: [
                    {
                        logidUrl: typeof location !== "undefined" ? location.href : "",
                        newType: 5,
                    },
                ],
            })
        },
        closeProblemConsultationPop() {
            this.showFormPop = false
        },
        changeFold() {
            this.isFold = !this.isFold
            if (typeof localStorage !== "undefined") {
                localStorage.setItem("suspendFold", this.isFold)
            }
        },
    },
    beforeDestroy() {
        if (typeof window !== "undefined") {
            window.removeEventListener("scroll", this.onScroll)
            window.removeEventListener("resize", this.handleResize)
        }
        this.leaveFn()
        this.clearScrollTimer()
    },
}
</script>

<style lang="scss" scoped>
.fs-global-float-wrap {
    width: 48px;
    // height: 160px;
    position: fixed;
    right: 20px;
    bottom: 40px;
    z-index: 10;
    @include padMobile() {
        display: none;
    }
    &.opacity .box {
        opacity: 0.5;
    }
    &.is-pc {
        right: 20px;
        width: 48px;
        .box .btn-box {
            width: 48px;
            height: 48px;

            &:hover {
                color: #19191a;
            }

            &.chat-box {
                margin-bottom: 8px;
                &:hover {
                    color: #19191a;
                    // background-color: #f6f6f8;
                }
            }
            &.form-box {
                background-color: #707070;
                color: #fff;

                &:hover {
                    color: #fff;
                    background-color: #4b4b4d;
                }
            }
            img {
                width: 24px;
            }
            .iconfont {
                font-size: 24px;
                width: 24px;
                height: 24px;
                line-height: 24px;
            }
        }
    }
    .box {
        // box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.1);
        // border-radius: 4px;
        // overflow: hidden;
        &:not(:first-child) {
            margin-top: 16px;
        }
    }
    .btn-box {
        width: 48px;
        height: 48px;
        display: flex;
        justify-content: center;
        align-items: center;
        background-color: #ffffff;
        transition: all 0.3s;
        cursor: pointer;
        color: #707070;
        border-radius: 50%;
        box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.1);

        // &:hover {
        //     color: #19191a;
        // }
        // @include mediaIpad {
        //     &:hover {
        //         color: #707070;
        //     }
        // }
        &.folded {
            .iconfont_fold {
                transform: rotateZ(180deg);
            }
        }
        &.line {
            position: relative;
            &::after {
                content: "";
                position: absolute;
                bottom: 0;
                left: 7px;
                right: 7px;
                background-color: #e5e5e5;
                height: 1px;
                z-index: 2;
            }
        }
        img {
            display: block;
            width: 21px;
            height: auto;
        }

        .iconfont {
            font-size: 21px;
            display: block;
            width: 21px;
            height: 21px;
            line-height: 21px;
        }
        .iconfont_fold {
            width: 16px;
            height: 16px;
            font-size: 16px;
            line-height: 1;
            transition: transform 0.3s ease-in-out;
        }
    }
}

@media (max-width: 768px) {
    .fs-global-float-wrap {
        z-index: 50;
    }
}

.fs_global_btn_box_padMobile {
    position: fixed;
    display: none;
    flex-direction: column;
    right: 10px;
    bottom: 40px;
    z-index: 1;
    @include padMobile() {
        display: flex;
        right: 20px;
        z-index: 50;
    }
    // @media (max-width: 768px) {
    //     z-index: 50;
    // }
    .padMobile_btn {
        width: 48px;
        height: 48px;
        border-radius: 30px;
        display: flex;
        justify-content: center;
        align-items: center;
        color: #707070;
        .iconfont {
            font-size: 24px;
            width: 24px;
            height: 24px;
            line-height: 24px;
        }
        &.backTop_btn {
            background: #ffffff;
            box-shadow: 0px 4px 12px 0px rgba(0, 0, 0, 0.15);
            margin-bottom: 16px;
        }
        &.consult_btn {
            background-color: #707070;
            color: #fff;
            margin-top: 8px;
            &:hover {
                color: #fff;
                background-color: #4b4b4d;
            }
        }
        &.livechat_btn {
            background: #ffffff;
            box-shadow: 0px 4px 12px 0px rgba(0, 0, 0, 0.15);
            position: relative;
            img {
                width: 24px;
            }
            .circle_div {
                display: flex;
                justify-content: center;
                align-items: center;
                position: absolute;
                background: #ffffff;
                width: 18px;
                height: 18px;
                border-radius: 9px;
                top: 3px;
                left: 44px;
                .red_div {
                    width: 12px;
                    height: 12px;
                    border-radius: 6px;
                    background: #c00000;
                }
            }
        }
    }
}
</style>
