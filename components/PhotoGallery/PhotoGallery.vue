<template>
    <div class="photo_gallery_box">
        <div class="photo_gallery_ctn_pc">
            <div v-for="(image, key) in imageList" class="gallery_item" :key="key" :data-pswp-width="image.width" :data-pswp-height="image.height" target="_blank" rel="noreferrer">
                <img :src="image.thumbnailURL" alt="" />
            </div>
        </div>
        <div :id="galleryID" class="photo_gallery_ctn_m">
            <a v-for="(image, key) in imageList" :key="key" :href="image.largeURL" :data-pswp-width="getImageWidth(image)" :data-pswp-height="getImageHeight(image)" target="_blank" rel="noreferrer">
                <img :src="image.thumbnailURL" alt="" />
            </a>
        </div>
    </div>
</template>
<script>
import PhotoSwipeLightbox from "./photoswipe/photoswipe-lightbox.esm.js"
import "./photoswipe/photoswipe.css"

export default {
    name: "PhotoGallery",
    props: {
        galleryID: {
            type: String,
            default: "photo_gallery_box",
        },
        images: {
            type: Array,
            default: () => [],
        },
        fullScreen: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {
            imageList: [],
        }
    },
    mounted() {
        this.initImages()
        console.log("==========================================")
        console.log(this.imageList)

        if (!this.lightbox) {
            this.lightbox = new PhotoSwipeLightbox({
                gallery: "#" + this.galleryID,
                children: "a",
                pswpModule: () => import("./photoswipe/photoswipe.esm.js"),
                arrowKeys: false,
                bgOpacity: 0.8,
                // preloaderDelay: 0,
                bgClickAction: "close",
                imageClickAction: "close",
                tapAction: "close",
                pinchToClose: false,
                closeOnVerticalDrag: false,
            })
            this.lightbox.init()
        }
    },
    methods: {
        async initImages() {
            // var widths = await Promise.all(this.images.map(loadImage))
            try {
                // 使用 Promise.all 和 await 等待所有图片加载完成
                await Promise.all(this.images.map(this.loadImage))

                // 所有图片加载完成后的操作
                // console.log("所有图片宽度:", widths)
                // 在这里执行其他操作，比如计算宽度的平均值等等
            } catch (error) {
                // 处理加载错误
                console.error(error)
            }
        },
        loadImage(url) {
            let _this = this
            return new Promise(function (resolve, reject) {
                var img = new Image()
                img.onload = function () {
                    _this.imageList.push({
                        largeURL: url,
                        thumbnailURL: url,
                        width: img.width,
                        height: img.height,
                    })
                    resolve()
                }
                img.onerror = function () {
                    reject(new Error("Failed to load image at " + url))
                }
                img.src = url
            })
        },
        getImageWidth(image) {
            // 只在移动端且fullScreen为true时返回800，否则返回原始宽度
            if (this.isMobile() && this.fullScreen) {
                return 800
            }
            return image.width
        },
        getImageHeight(image) {
            // 只在移动端且fullScreen为true时返回800，否则返回原始高度
            if (this.isMobile() && this.fullScreen) {
                return 800
            }
            return image.height
        },
        isMobile() {
            // 检测是否为移动端
            return window.innerWidth <= 768
        },
    },
    unmounted() {
        if (this.lightbox) {
            this.lightbox.destroy()
            this.lightbox = null
        }
    },
}
</script>

<style lang="scss">
.pswp {
    .pswp__counter {
        display: none;
    }

    .pswp__button--zoom {
        display: none !important;
    }
    .pswp__preloader {
        display: none !important;
    }

    .pswp__button--arrow {
        display: none;
    }

    .pswp__button--close {
        width: 32px;
        height: 32px;
        opacity: 1;
        top: 18px;
        right: 18px;
        margin-right: 0;

        svg {
            display: none;
        }

        &:before {
            // content: "\e6a0";
            content: "\f30a";
            color: #ffffff;
            font-family: "iconfont" !important;
            -webkit-font-smoothing: antialiased;
            cursor: pointer;
            transition: all 0.3s;
            font-size: 20px;
        }
    }
}
</style>

<style lang="scss" scoped>
.photo_gallery_box {
    width: 100%;

    .gallery_item,
    a {
        display: block;
        width: 100%;

        img {
            display: block;
            max-width: 100%;
        }
    }
}

.photo_gallery_ctn_pc {
    display: block;

    @media screen and (max-width: 768px) {
        display: none;
    }
}

.photo_gallery_ctn_m {
    display: none;

    @media screen and (max-width: 768px) {
        display: block;
    }
}
</style>
