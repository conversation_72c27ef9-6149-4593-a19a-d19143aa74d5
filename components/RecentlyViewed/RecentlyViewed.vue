<template>
    <div class="recently_viewed" :class="`recently_viewed_${type}`" v-if="list && list.length">
        <div class="title">{{ $c("components.smallComponents.recentlyViewed") }}</div>
        <div class="ctn" v-show="!isMobile">
            <swiper ref="cartRecently" :options="swiperOption" v-if="list && list.length">
                <swiper-slide v-for="(item, index) in list" :key="item.product_id">
                    <nuxt-link target="_blank" @click.native="gaEvent('img', index)" class="product-img" :to="localePath({ name: 'products', params: { id: item.products_id } })">
                        <img :src="item.image" alt="" />
                    </nuxt-link>
                    <nuxt-link target="_blank" @click.native="gaEvent('text', index)" class="product-title" :to="localePath({ name: 'products', params: { id: item.products_id } })">{{ item.products_name }}</nuxt-link>
                    <p class="product-price" v-html="item.products_price_str"></p>
                </swiper-slide>
            </swiper>
            <div class="swiper-button-prev" v-show="show_btn" slot="button-prev" @click.stop="gaEventSwitch('left')">
                <span class="iconfont iconfont-left">&#xe702;</span>
            </div>
            <div class="swiper-button-next" v-show="show_btn" slot="button-next" @click.stop="gaEventSwitch('right')">
                <span class="iconfont iconfont-right">&#xe703;</span>
            </div>
        </div>
        <!-- <div class="m_ctn" v-if="list && list.length && isMobile">
            <div class="mt1">
                <nuxt-link target="_blank" :to="localePath({name:'products',params:{id:list[n-1].products_id}})" class="mt1_item" v-for="n in (list.length>1?2:1)" :key="n">
                    <img class="p_img" :src="list[n-1].image" alt="">
                    <h2 class="p_title" v-html="list[n-1].products_name"></h2>
                    <p class="p_price" v-html="list[n-1].products_price_str"></p>
                </nuxt-link>
            </div>
            <slide-down>
                <div class="mt1" v-if="list.length>2 && show_more">
                    <nuxt-link target="_blank" :to="localePath({name:'products',params:{id:list[n].products_id}})" class="mt1_item" v-for="n in list.length-2" :key="n">
                        <img class="p_img" :src="list[n+1].image" alt="">
                        <h2 class="p_title" v-html="list[n+1].products_name"></h2>
                        <p class="p_price" v-html="list[n+1].products_price_str"></p>
                    </nuxt-link>
                </div>
            </slide-down>
            <p class="m_btn" v-if="list.length>2 && isMobile">
                <span class="iconfont iconfont_down" :class="{iconfont_down_up:show_more}" @click.stop="toogleShow">&#xe704;</span>
            </p>
        </div> -->
        <div class="m_ctn" v-show="list && list.length && isMobile">
            <overflow-container class="overflow_container" :class="{ overflow_container_less: list.length < 3 }">
                <div class="products_box" :class="{ products_box_less: list.length < 3 }">
                    <nuxt-link class="products_item" target="_blank" :to="localePath({ name: 'products', params: { id: list[n - 1].products_id } })" v-for="n in list.length" :key="n">
                        <img class="p_img" :src="list[n - 1].image" alt="" />
                        <h2 class="p_title" v-html="list[n - 1].products_name"></h2>
                        <p class="p_price" v-html="list[n - 1].products_price_str"></p>
                    </nuxt-link>
                </div>
            </overflow-container>
        </div>
    </div>
</template>

<script>
import SlideDown from "@/components/SlideDown/SlideDown.vue"
import { Swiper, SwiperSlide } from "vue-awesome-swiper"
import { mapState } from "vuex"
import OverflowContainer from "@/pages/Products/components/OverflowContainer.vue"
import { setCookieOptions } from "@/util/util"

export default {
    name: "RecentlyViewed",
    components: {
        Swiper,
        SwiperSlide,
        SlideDown,
        OverflowContainer,
    },
    props: {
        type: {
            type: String,
            default: "cart",
        },
    },
    data() {
        return {
            list: [],
            show_btn: false,
            show_more: false,
            swiperOption: {
                slidesPerView: 4,
                spaceBetween: 10,
                slidesPerGroup: 4,
                loop: false,
                loopFillGroupWithBlank: true,
                navigation: {
                    nextEl: ".swiper-button-next",
                    prevEl: ".swiper-button-prev",
                },
                breakpoints: {
                    300: {
                        slidesPerView: 2,
                        spaceBetween: 10,
                        slidesPerGroup: 2,
                    },
                    480: {
                        slidesPerView: 2,
                        spaceBetween: 10,
                        slidesPerGroup: 2,
                    },
                    768: {
                        slidesPerView: 3,
                        spaceBetween: 10,
                        slidesPerGroup: 3,
                    },
                    960: {
                        slidesPerView: 4,
                        spaceBetween: 10,
                        slidesPerGroup: 4,
                    },
                    1200: {
                        slidesPerView: 4,
                        spaceBetween: 10,
                        slidesPerGroup: 4,
                    },
                    1420: {
                        slidesPerView: 4,
                        spaceBetween: 20,
                        slidesPerGroup: 4,
                    },
                },
            },
        }
    },
    computed: {
        ...mapState({
            isMobile: (state) => state.device.isMobile,
            screenWidth: (state) => state.device.screenWidth,
            currency: (state) => state.webSiteInfo.currency,
        }),
        swiper() {
            return this.$refs.cartRecently.$swiper
        },
    },
    created() {
        if (process.client) {
            let recentlyProducts = JSON.parse(localStorage.getItem("recentlyProducts"))
            if (recentlyProducts && recentlyProducts.length) {
                this.$axios.post("/api/special_index", { products_ids: recentlyProducts, products_status: 1, page: this.type !== "cart" ? "product_info" : undefined }).then((res) => {
                    if (res.data && res.data.length) {
                        this.list.splice(0, this.list.length, ...res.data)
                        let arr = []
                        res.data.map((item) => {
                            arr.push(item.products_id)
                        })
                        this.$cookies.set("recentlyProducts", arr)
                        if (this.screenWidth >= 1420 && this.list.length >= 6) {
                            this.show_btn = true
                        }
                        if (this.screenWidth < 1420 && this.screenWidth >= 1200 && this.list.length >= 5) {
                            this.show_btn = true
                        }
                        if (this.screenWidth < 1200 && this.screenWidth >= 960 && this.list.length >= 4) {
                            this.show_btn = true
                        }
                    }
                })
            }
        }
    },
    mounted() {},
    methods: {
        toogleShow() {
            this.show_more = !this.show_more
        },
        gaEvent(s, index) {
            if (window.dataLayer) {
                window.dataLayer.push({
                    event: "eeEvent",
                    eventCategory: this.type === "cart" ? `Cart Page` : `productDetailPage_${this.$route.params.id}`,
                    eventAction: "select_item",
                    eventLabel: `${s}_Recently Viewed_${this.list[index].products_id}`,
                    proPosition: this.type === "cart" ? `${index + 1}` : `1`,
                    proId: `${this.list[index].products_id}`,
                    proName: `${this.list[index].products_name.substr(0, 100)}`,
                    nonInteraction: false,
                    ecommerce: {
                        currencyCode: `${this.currency}`,
                        click: {
                            actionField: {
                                list: "Recently Viewed",
                            },
                            products: [
                                {
                                    id: `${this.list[index].products_id}`,
                                    name: `${this.list[index].products_name.substr(0, 100)}`,
                                    price: `${this.list[index].products_price}`,
                                    position: index + 1,
                                },
                            ],
                        },
                    },
                })
            }
        },
        gaEventSwitch(dir) {
            if (window.dataLayer) {
                if (/^products/.test(this.$route.name)) {
                    window.dataLayer.push({
                        event: "uaEvent",
                        eventCategory: `productDetailPage_${this.$route.params.id}`,
                        eventAction: "switch_Product",
                        eventLabel: dir,
                        nonInteraction: false,
                    })
                } else {
                    window.dataLayer.push({
                        event: "uaEvent",
                        eventCategory: "Cart Page",
                        eventAction: "switch_Product",
                        eventLabel: `Recently Viewed_${dir}`,
                        nonInteraction: false,
                    })
                }
            }
        },
    },
    watch: {
        screenWidth: {
            handler(n, o) {
                if (n >= 1420 && this.list.length >= 6) {
                    this.show_btn = true
                }
                if (n < 1420 && n >= 1200 && this.list.length >= 5) {
                    this.show_btn = true
                }
                if (n < 1200 && n >= 960 && this.list.length >= 4) {
                    this.show_btn = true
                }
            },
            immediate: true,
        },
    },
}
</script>

<style lang="scss" scoped>
.cart-recently {
    width: 100%;
    position: relative;
    @media (max-width: 960px) {
        display: none;
    }
}

.title {
    color: $textColor1;
    @include font20;
    line-height: 40px;
    margin: 0px 0 16px 0;
    font-weight: 600;
    @media (max-width: 960px) {
        font-size: 16px;
        line-height: 30px;
        padding: 12px 3% 16px 3%;
        background: #fff;
        margin: 0;
        border-bottom: 1px solid $borderColor2;
    }
}
.ctn {
    width: 100%;
    padding: 10px 20px 30px 20px;
    background: #fff;
    height: 230px;
    position: relative;
    .swiper-container {
        width: 100%;
        z-index: 0;
    }
    .swiper-slide {
        height: 190px;
        padding: 0 10px;
        .product-img {
            display: block;
            > img {
                display: block;
                width: 120px;
                height: 120px;
                margin: 0 auto;
            }
        }
        .product-title {
            color: $textColor1;
            @include font14;
            height: 44px;
            display: -webkit-box;
            overflow: hidden;
            text-overflow: ellipsis;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
        }
        .product-price {
            color: $textColor1;
            margin-top: 16px;
            @include font14;
            width: 100%;
            font-weight: 600;
        }
    }
    .swiper-button-prev {
        left: 20px;
        cursor: pointer;
        top: 40%;
        display: inline-block;
        z-index: 10;
        pointer-events: auto;
        > span {
            line-height: 1;
            cursor: pointer;
            display: inline-block;
            width: 36px;
            height: 36px;
            text-align: center;
            line-height: 36px;
            background-color: rgba(0, 0, 0, 0.2);
            border-radius: 36px;
            font-size: 20px;
            color: #fff;
            user-select: none;
        }
        &:after {
            content: "";
        }
    }
    .swiper-button-next {
        right: 20px;
        cursor: pointer;
        display: inline-block;
        top: 40%;
        z-index: 10;
        pointer-events: auto;
        > span {
            line-height: 1;
            cursor: pointer;
            display: inline-block;
            width: 36px;
            height: 36px;
            text-align: center;
            line-height: 36px;
            background-color: rgba(0, 0, 0, 0.2);
            border-radius: 36px;
            font-size: 20px;
            color: #fff;
            user-select: none;
        }
        &:after {
            content: "";
        }
    }
    &:hover {
        .swiper-button-prev {
            display: inline-block;
        }
        .swiper-button-next {
            display: inline-block;
        }
    }
}
.recently_viewed {
    width: 100%;
}
.recently_viewed_products {
    background: #fff;
    padding: 0 32px;
    @media (max-width: 960px) {
        padding: 0;
    }
    .title {
        padding: 14px 0 15px 0;
        border-bottom: 1px solid $borderColor2;
        @media (max-width: 960px) {
            padding: 12px 3%;
        }
    }
    .ctn {
        .swiper-button-prev {
            left: 0;
        }
        .swiper-button-next {
            right: 10px;
        }
    }
}
// .m_ctn {
//     padding: 0 3% 8px 3%;
//     background: #fff;
// }
.m_ctn {
    padding: 12px 3% 32px 3%;
    background: #fff;
    .overflow_container {
        &.overflow_container_less {
            &::v-deep {
                .overflow_bg {
                    display: none;
                }
                .overflow_box {
                    width: 100%;
                    .overflow_ctn {
                        width: 100%;
                    }
                }
                .overflow_slide_box {
                    display: none;
                }
            }
        }
    }
    .products_box {
        display: flex;
        align-items: flex-start;
        &.products_box_less {
            width: 100%;
            .products_item {
                width: 50%;
            }
        }
        .products_item {
            display: inline-block;
            width: calc(96vw / 2.5);
            padding: 0 6px;
            &:hover {
                text-decoration: none;
            }
            &:first-child {
                padding-left: 0;
            }
            &:last-child {
                padding-right: 0;
            }

            .p_img {
                display: block;
                max-width: 120px;
                margin: 0 auto;
            }
            .p_title {
                @include font13;
                color: $textColor1;
                overflow: hidden;
                text-overflow: ellipsis;
                display: -webkit-box;
                -webkit-box-orient: vertical;
                -webkit-line-clamp: 2;
                margin: 12px 0;
            }
            .p_price {
                @include font14;
                color: $textColor1;
                font-weight: 600;
            }
        }
    }
}
.m_btn {
    border-top: 1px solid $borderColor2;
    padding: 13px 0 5px;
    text-align: center;
    .iconfont_down {
        display: inline-block;
        font-size: 14px;
        color: #0070bc;
        transition: all 0.3s;
        cursor: pointer;
        &.iconfont_down_up {
            transform: rotate(-180deg);
        }
    }
}
.mt1 {
    display: flex;
    flex-wrap: wrap;
    position: relative;
    overflow: hidden;
    .mt1_item {
        width: 50%;
        flex-shrink: 0;
        margin-bottom: 10px;
        min-height: 240px;
        text-decoration: none;
        &:nth-child(odd) {
            padding-right: 10px;
        }
        &:nth-child(even) {
            padding-left: 10px;
        }
        .p_img {
            display: block;
            margin: 0 auto;
            width: 120px;
        }
        .p_title {
            @include font13;
            color: $textColor1;
            font-weight: 400;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
            margin: 10px 0;
        }
        .p_price {
            @include font14;
            margin-top: 5px;
            font-weight: 600;
            color: $textColor1;
        }
    }
}
</style>
