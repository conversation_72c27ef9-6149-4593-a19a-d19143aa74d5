<template>
    <div>
        <!-- 非欧洲的 -->
        <div class="cookie" v-if="showCookieTipOld">
            <div class="m_close">
                <i class="iconfont icon-close" @click="closeCookieTipOld">&#xf30a;</i>
            </div>
            <div class="main-EU main">
                <template v-if="isLogin">
                    <template v-if="iso_code === 'US' && userInfo.cookie_authorization === 0">
                        <p class="p_us">
                            <span class="policy" v-html="validateFont(updata_us_text)"></span>
                        </p>
                        <div class="btn_box">
                            <fs-button type="gray" class="accept_btn" @click="acknowledgeAndContinue">Acknowledge and Continue</fs-button>
                        </div>
                    </template>
                    <template v-else>
                        <p v-html="validateFont(getTextContent('txt01'))"></p>
                        <div class="btn_box">
                            <fs-button class="cookie_setting" type="whiteline" tabindex="0" @click="manageSetting($event)">{{ getTextContent("cookieSetting") }}</fs-button>
                            <fs-button v-if="iso_code === 'US'" type="whiteline" @click="handleCookiesDisagree">{{ getTextContent("rejectAll") }}</fs-button>
                            <fs-button class="accept accept_btn" type="gray" tabindex="0" @click="agreeCookie">{{ getTextContent("acceptCookies") }}</fs-button>
                        </div>
                    </template>
                </template>
                <template v-else>
                    <p v-html="validateFont(getTextContent('txt01'))"></p>
                    <div class="btn_box">
                        <fs-button class="cookie_setting" type="whiteline" tabindex="0" @click="manageSetting($event)">{{ getTextContent("cookieSetting") }}</fs-button>
                        <fs-button v-if="iso_code === 'US'" type="whiteline" @click="handleCookiesDisagree">{{ getTextContent("rejectAll") }}</fs-button>
                        <fs-button class="accept accept_btn" type="gray" tabindex="0" @click="agreeCookie">{{ getTextContent("acceptCookies") }}</fs-button>
                    </div>
                </template>
            </div>
        </div>
        <!-- 欧洲的 -->
        <div class="cookie" v-if="showCookieTip">
            <div class="m_close">
                <i class="iconfont icon-close" @click="closeCookieTip">&#xf30a;</i>
            </div>
            <div class="main-EU main">
                <p>
                    <span class="policy" v-html="validateFont(getTextContent('txt01'))"></span>
                </p>
                <div class="btn_box">
                    <fs-button type="whiteline" tabindex="0" @click="manageSetting($event)">{{ getTextContent("cookieSetting") }}</fs-button>
                    <fs-button type="whiteline" @click="handleCookiesDisagree">{{ getTextContent("rejectAll") }}</fs-button>
                    <fs-button type="gray" class="accept_btn" @click="agreeCookie">{{ getTextContent("acceptCookies") }}</fs-button>
                </div>
            </div>
        </div>
        <CookiePopup :show="showPopup" :cookieType="cookieType" @close="showPopup = false" @agree="handleAgree" />
    </div>
</template>

<script>
import FsButton from "@/components/FsButton/FsButton.vue"
import { mapMutations, mapState } from "vuex"
import { setCookieOptions } from "@/util/util"
import CookiePopup from "@/popup/CookiePopup/CookiePopup.vue"

export default {
    name: "cookie-tip",
    data() {
        return {
            showPopup: false,
            cookieTipNew: {
                txt01: `By clicking "Accept All Cookies", you agree to the storing of cookies on your device to enhance site navigation, analyse site usage, and assist in our marketing efforts. <a href="XXXX">Cookie Policy</a>`,
                cookieSetting: `Cookie Settings`,
                acceptCookies: `Accept All Cookies`,
                rejectAll: `Reject All`,
            },
            cookieTipNew_en: {
                txt01: `FS uses cookies and other technologies to make our website function and to make advertising and content more relevant to you.  To learn more view our <a href="XXXX">Privacy and Cookies</a>. You can manage your cookie preferences at any time by selecting <a href="XXXX">Manage Cookies</a>.`,
                cookieSetting: `Cookie Settings`,
                acceptCookies: `Accept All Cookies`,
                rejectAll: `Reject All`,
            },
            cookieTipNew_mx: {
                txt01: `FS utiliza cookies y otras tecnologías para que nuestro sitio web funcione correctamente y para que la publicidad y el contenido sean más relevantes para ti. Para obtener más información, consulta nuestra <a href="XXXX">Política de Privacidad y Cookies</a>. Puedes gestionar tus preferencias de cookies en cualquier momento seleccionando "<a href="XXXX">Administrar cookies</a>".`,
                cookieSetting: `Configuración de cookies`,
                acceptCookies: `Aceptar todas las cookies`,
                rejectAll: `Reject All`,
            },
            updata_us_text: `We've made important updates to our <a href="YYYY">Cookie Notice</a> effective [July 19,2025], to clarify how we collect, use, and share personal information. Please take a moment to review the updated <a href="YYYY">Cookie Notice</a>. By selecting "Acknowledge and Continue", you acknowledge these changes.`,
        }
    },
    components: {
        FsButton,
        CookiePopup,
    },
    mounted() {
        // 检查cookie授权状态并处理相关cookie
        this.checkCookieAuthorizationStatus()
    },
    methods: {
        ...mapMutations({
            setShowCookieTip: "webSiteInfo/setShowCookieTip",
            setShowCookieTipOld: "webSiteInfo/setShowCookieTipOld",
        }),
        // 新增：检查cookie授权状态的方法
        checkCookieAuthorizationStatus() {
            // 如果用户已登录且cookie_authorization为0，需要清除相关cookie
            if (this.isLogin && this.userInfo && this.userInfo.cookie_authorization === 0) {
                this.clearCookieConsents()
            }
        },
        // 新增：清除cookie同意相关的cookie
        clearCookieConsents() {
            // 清除欧洲站点相关的cookie
            this.$cookies.remove("cookieconsent_dismissed")
            this.$cookies.remove("fs_marketing_sdk")

            // 清除非欧洲站点相关的cookie - 统一使用 cookieconsent_dismissed
            this.$cookies.remove("fs_google_analytics")

            console.log("Cookie consents cleared due to cookie_authorization = 0")
        },
        // 新增的统一处理同意cookie的方法
        handleAgree() {
            this.agreeCookie()
        },
        // 同意cookie的方法 - 保持不变
        agreeCookie() {
            // 设置为"yes"表示用户同意
            this.$cookies.set("cookieconsent_dismissed", "yes")

            // 设置所有相关的cookies（不再区分站点类型）
            this.$cookies.set("fs_google_analytics", "yes")
            this.$cookies.set("fs_marketing_sdk", "yes")
            if (this.iso_code === "US") {
                this.$cookies.set("fs_function_cookie", "yes")
            }

            // 关闭所有cookie提示
            this.setShowCookieTipOld(false)
            this.setShowCookieTip(false)

            // GTM consent更新
            if (window.gtag) {
                window.gtag("consent", "update", {
                    region: ["AT", "BE", "BG", "CY", "CZ", "DE", "DK", "EE", "ES", "FI", "FR", "GR", "HR", "HU", "IE", "IS", "IT", "LI", "LT", "LU", "LV", "MT", "NL", "NO", "PL", "PT", "RO", "SE", "SI", "SK"],
                    ad_storage: "granted",
                    ad_user_data: "granted",
                    ad_personalization: "granted",
                    analytics_storage: "granted",
                })
            }

            // 刷新页面
            window && window.location.reload()
        },
        // 拒绝cookie的方法 - 需要调整
        handleCookiesDisagree() {
            // 设置为"rejected"表示用户拒绝
            this.$cookies.set("cookieconsent_dismissed", "rejected")

            // 清除所有同意时设置的cookies
            this.$cookies.remove("fs_marketing_sdk")

            if (this.iso_code === "US") {
                this.$cookies.remove("fs_function_cookie")
            }

            this.$cookies.remove("fs_google_analytics")

            // 清除相关的分析cookies
            this.$cookies.remove("_ga")
            this.$cookies.remove("_gid")
            this.$cookies.remove("AMP_TOKEN")
            this.$cookies.remove("_ym_isad")
            this.$cookies.remove("_ym_uid")
            this.$cookies.remove("_ym_visorc_48770636")

            // 关闭cookie提示
            if (this.showCookieTip) {
                this.setShowCookieTip(false)
            }
            if (this.showCookieTipOld) {
                this.setShowCookieTipOld(false)
            }

            // GTM consent拒绝更新
            if (window.gtag) {
                window.gtag("consent", "update", {
                    region: ["AT", "BE", "BG", "CY", "CZ", "DE", "DK", "EE", "ES", "FI", "FR", "GR", "HR", "HU", "IE", "IS", "IT", "LI", "LT", "LU", "LV", "MT", "NL", "NO", "PL", "PT", "RO", "SE", "SI", "SK"],
                    ad_storage: "denied",
                    ad_user_data: "denied",
                    ad_personalization: "denied",
                    analytics_storage: "denied",
                })
            }

            // 刷新页面
            window && window.location.reload()
        },
        disAgreeGoogle(e) {
            if (e.target.className === "disAgreeGoogle") {
                let googleAnalytics = this.$cookies.get("_ga")
                if (googleAnalytics) {
                    this.$cookies.remove("_ga")
                    this.$cookies.remove("_gid")
                    this.$cookies.remove("AMP_TOKEN")
                    this.$cookies.remove("_ym_isad")
                    this.$cookies.remove("_ym_uid")
                    this.$cookies.remove("_ym_visorc_48770636")
                }
            }
            this.setShowCookieTipOld(false)
            if (window.gtag) {
                window.gtag("consent", "update", {
                    region: ["AT", "BE", "BG", "CY", "CZ", "DE", "DK", "EE", "ES", "FI", "FR", "GR", "HR", "HU", "IE", "IS", "IT", "LI", "LT", "LU", "LV", "MT", "NL", "NO", "PL", "PT", "RO", "SE", "SI", "SK"],
                    ad_storage: "denied",
                    ad_user_data: "denied",
                    ad_personalization: "denied",
                    analytics_storage: "denied",
                })
            }
        },
        validateFont(params) {
            let str = ""
            str = params.replace(/XXXX/g, this.localePath({ path: "/policies/privacy_policy.html" }))
            str = str.replace(/YYYY/g, this.$localeLink("/policies/cookie_notice.html"))
            return str
        },
        manageSetting(e) {
            this.showPopup = true
        },
        // 关闭非欧洲版本的cookie提示
        closeCookieTipOld() {
            this.setShowCookieTipOld(false)
        },
        // 关闭欧洲版本的cookie提示
        closeCookieTip() {
            this.setShowCookieTip(false)
        },
        // 根据站点类型获取文本内容
        getTextContent(key) {
            // 添加美国地区 txt01 的特殊处理
            if (this.iso_code === "US" && key === "txt01") {
                return `This website uses cookies, pixels, and similar technologies (collectively "Cookies") to improve your browsing experience. By clicking "Accept All Cookies", you agree to the storing of Cookies on your device and that we may share, intercept, track, store, and analyze your interactions with the website to enhance site navigation, analyze site usage, and assist in our marketing efforts. For more information on our use of cookies, please review our <a href="YYYY">Cookies Notice</a>.`
            }

            if (this.website === "de-en") {
                return this.cookieTipNew[key]
            } else if (this.website === "au" || this.website === "sg") {
                return this.cookieTipNew_en[key]
            } else if (this.website === "mx") {
                return this.cookieTipNew_mx[key]
            } else {
                return this.$c(`pages.home.cookieTipNew.${key}`)
            }
        },
        // 新增acknowledgeAndContinue方法
        async acknowledgeAndContinue() {
            try {
                // 调用cookie授权接口
                await this.$axios.post("/api/cookie/authorization")
                // 请求成功后关闭弹窗
                this.setShowCookieTipOld(false)
            } catch (error) {
                console.error("Cookie authorization failed:", error)
                // 即使请求失败也关闭弹窗（根据业务需求可调整）
                this.setShowCookieTipOld(false)
            }
        },
    },
    computed: {
        ...mapState({
            website: (state) => state.webSiteInfo.website,
            showCookieTip: (state) => state.webSiteInfo.showCookieTip,
            showCookieTipOld: (state) => state.webSiteInfo.showCookieTipOld,
            iso_code: (state) => state.webSiteInfo.iso_code,
            isLogin: (state) => state.userInfo.isLogin,
            userInfo: (state) => state.userInfo.userInfo,
        }),
        // 判断是否应该使用英文数据（de-en、au、sg站点）
        shouldUseEnglishData() {
            return this.website === "de-en" || this.website === "au" || this.website === "sg"
        },
        cookieType() {
            // 基于网站类型来判断cookie类型，而不是依赖显示状态
            const EuropeSiteMap = ["de-en", "de", "nl", "fr", "es", "it"]

            if (this.website && EuropeSiteMap.includes(this.website)) {
                return 1 // 欧洲站点使用新版cookie提示
            } else {
                return 0 // 非欧洲站点使用旧版cookie提示
            }
        },
    },
    created() {
        console.log(this.website)
    },
    watch: {
        // 监听userInfo变化，当cookie_authorization变为0时清除相关cookie
        "userInfo.cookie_authorization": {
            handler(newVal, oldVal) {
                if (newVal === 0 && oldVal !== 0) {
                    this.clearCookieConsents()
                }
            },
            immediate: false,
        },
    },
}
</script>

<style lang="scss" scoped>
.cookie {
    width: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    position: fixed;
    left: 0;
    bottom: 0;
    z-index: 51; //现在先改成51，改之前是10，如果后面发现有问题，再还原，因为设计说要将右下角的FsGlobalBtn(z-index为50)放在Cookie的下面
    padding: 20px 0;
    color: $textColor3;
    box-shadow: 0px -8px 20px 0px rgba(0, 0, 0, 0.1);
    .m_close {
        display: flex;
        justify-content: flex-end;
        align-items: center;
        padding: 0 20px;
        position: relative;
        .iconfont {
            position: absolute;
            top: 0;
            right: 20px;
            color: $textColor7;
            font-size: 20px;
            line-height: 1;
            display: block;
            padding: 4px;
            cursor: pointer;
            &::before {
                display: block;
            }
        }
    }
    .main {
        // width: 100%;
        // max-width: 1200px;
        // margin: 0 auto;
        position: relative;
        display: flex;
        align-items: center;
        height: 100%;
        justify-content: space-between;
        column-gap: 24px;

        max-width: 1264px;
        padding-left: 32px;
        padding-right: 32px;
        margin: 0 auto;

        @media (max-width: 1264px) {
            padding-left: 32px;
            padding-right: 32px;
        }

        @media (max-width: 1024px) {
            padding-left: 24px;
            padding-right: 24px;
        }
        @media (max-width: 819px) {
            padding-left: 16px;
            padding-right: 16px;
        }
        @media (max-width: 768px) {
            padding-left: 16px;
            padding-right: 16px;
        }
    }

    .main-notEU {
        p {
            @include font14;
            color: $textColor7;
            padding-right: 20px;
            width: 1040px;
            ::v-deep a {
                text-decoration: underline;
                color: $textColor7;
            }
        }

        .icon {
            color: $textColor7;
            font-size: 12px;
            position: absolute;
            right: 0;
            top: 50%;
            transform: translateY(-50%);
        }
    }

    .main-EU {
        justify-content: space-between;
        :deep(.fs-button) {
            @media (max-width: 768px) {
                width: 100%;
            }
        }
        p {
            max-width: 840px;
            @include font14;
            color: $textColor7;
            .policy {
                display: block;
            }
            ::v-deep {
                a {
                    color: $textColor7;
                    text-decoration: underline;
                }
            }
            // .manage {
            //     a {
            //         color: #4080ff;
            //         cursor: pointer;
            //         :hover {
            //             text-decoration: underline;
            //         }
            //     }
            //     .iconfont {
            //         @include font12;
            //         margin-left: 4px;
            //     }
            // }
            &.p_us {
                max-width: 938px;
            }
        }

        @media (max-width: 768px) {
            flex-direction: column;

            .accept {
                margin-left: 0;
            }
        }
    }
    .btn_box {
        display: flex;
        column-gap: 12px;
        .fs-button {
            height: 36px;
            padding: 0px 12px;
            &.accept_btn {
                color: $textColor1;
            }
        }
        @media (max-width: 768px) {
            margin-top: 24px;
            flex-direction: column-reverse;
            width: 100%;
            margin-left: 0;
            row-gap: 12px;
            .fs-button {
                height: 42px;
                padding: 0px 12px;
            }
        }
    }
    @media (max-width: 1024px) {
        border-radius: 8px 8px 0 0;
        padding: 16px 0 24px;
        .m_close {
            padding: 0 24px 16px;
            .iconfont {
                position: static !important;
            }
        }
        @media (max-width: 768px) {
            .m_close {
                padding: 0 16px 16px;
            }
        }
    }
}
</style>
