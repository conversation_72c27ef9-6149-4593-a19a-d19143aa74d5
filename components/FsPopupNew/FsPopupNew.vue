<template>
    <div @keyup.esc="close" :data-transfer="appendBody" v-transfer-dom>
        <transition :name="transition">
            <div class="fs-popup" v-if="show" :class="fsPopupClass" :style="customStyle">
                <div class="fs-popup-ctn" :class="mobileWidth ? 'fs-popup-ctn_height' : ''" ref="fsPopupCtn" :style="{ height: height, width: popupWidth }" v-loading="loading">
                    <template v-if="!autoContent">
                        <div class="fs-popup-header" v-if="!hideHeader">
                            <div class="title_box">
                                <div class="title" v-if="title" v-html="title"></div>
                                <div class="header_slot" v-if="!title">
                                    <slot name="header"></slot>
                                </div>
                            </div>
                            <div class="iconfont_close_box" tabindex="0" @keyup.enter.stop="close" @click.stop="close">
                                <span class="iconfont iconfont_close">&#xf30a;</span>
                            </div>
                        </div>
                        <div class="fs-popup-close" v-if="hideHeader">
                            <div class="iconfont_close_box" v-if="showClose" @click.stop="close">
                                <span class="iconfont iconfont_close">&#xf30a;</span>
                            </div>
                        </div>
                        <div class="fs-popup-body" :class="{ 'no-padding': !hasFooterContent }">
                            <slot></slot>
                        </div>
                        <div class="fs-popup-footer" :class="{ 'no-padding': !hasFooterContent }">
                            <slot name="footer"></slot>
                        </div>
                    </template>
                    <template v-else>
                        <slot></slot>
                    </template>
                </div>
            </div>
        </transition>
        <div class="mask" v-if="show"></div>
    </div>
</template>
<script>
/*
  弹窗组件设计稿规范  https://mastergo.com/file/68152983788808?page_id=1209%3A9865
2025年 https://mastergo.com/file/130922266222581?fileOpenFrom=project&page_id=143%3A5702&devMode=true&layer_id=202%3A07293%2F192%3A03553
  弹窗样式分为3种
  pc端居中
  m端全屏
  m端居中

  @params show [Boolean] 弹窗显隐状态
  @params title [String] 标题
  @params loading [Boolean] 是否显示加载动画
  @params width [String] 弹窗宽度  default ''
  @params height [String] 弹窗高度 目前规范高度由内容撑开 可根据需求调整 default ''
  @params hideHeader [Boolean] default false
  @params showClose [showClose] 自定义弹窗头部时 是否显示关闭按钮 default true
  @params clickHide [Boolean] 是否点击弹窗遮罩层关闭弹窗 default true
  @params transition [String] 动画名   目前有5中动画效果
  @params isMDrawer [Boolean] M端是否展示距离顶部64px的样式
  @params style  [Object] 传入的style样式
  fade: 渐隐渐现
  slide-right:右侧划入
  slide-left:左侧滑入
  slide-down:底部划入
  slide-up: 顶部划入
  slot  插槽
  若需要自定义头部，可以写具名插槽 name="header"
  默认插槽为body内容
  底部按钮插槽 footer
  @params autoContent [Boolean] 若需要自定义整个弹窗内容 设置为true default false
  @params mobileWidth [String] 在m端为居中删除按钮样式时 可根据需求调整 设置宽度 default " "
  @params type [String] 用于区分弹窗类型 使用不同样式 all 全屏展示 float 浮层展示 drawer 抽屉展示
  弹窗整体布局 header body footer 布局样式结构
*/
import fixScroll from "@/util/fixScroll"
import FsCircle from "../FsCircle/FsCircle.vue"
import TransferDom from "@/components/FsPopover/directives/transfer-dom"
export default {
    name: "FsPopup",
    components: {
        FsCircle,
    },
    directives: { TransferDom },
    props: {
        show: {
            type: Boolean,
            default: false,
        },
        title: {
            type: String,
            default: "",
        },
        loading: {
            type: Boolean,
            default: false,
        },
        width: {
            type: String,
            required: false,
            default: "",
        },
        height: {
            type: String,
            required: false,
            default: "",
        },
        hideHeader: {
            type: Boolean,
            default: false,
        },
        clickHide: {
            type: Boolean,
            default: true,
        },
        transition: {
            type: String,
            default: "",
        },
        showClose: {
            type: Boolean,
            default: true,
        },
        autoContent: {
            type: Boolean,
            default: false,
        },
        mobileWidth: {
            type: String,
            default: "",
        },
        isFixScroll: {
            type: Boolean,
            default: true,
        },
        isMDrawer: {
            type: Boolean,
            default: false,
        },
        appendBody: {
            type: Boolean,
            default: false,
        },
        isNew: {
            type: Boolean,
            default: false,
        },
        type: {
            type: String,
            default: "",
        },
        customStyle: {
            type: Object,
            default: () => {},
        },
    },
    data() {
        return {
            popupWidth: this.width,
            hasFooterContent: false,
        }
    },
    computed: {
        fsPopupClass() {
            return `${this.isMDrawer ? "isMDrawer" : ""} ${this.isNew ? "is_new" : ""} ${this.type} `
        },
    },
    mounted() {
        this.reSizeT()
        window.addEventListener("resize", this.reSizeT)
        document.addEventListener("click", this.handleOtherClick)
        this.checkFooterContent()
    },
    updated() {
        this.checkFooterContent()
    },
    destroyed() {
        window.removeEventListener("resize", this.reSizeT)
        document.removeEventListener("click", this.handleOtherClick)
    },
    methods: {
        checkFooterContent() {
            // 检查footer内容
            this.hasFooterContent = !!this.$slots.footer
        },
        close() {
            this.$emit("close")
        },
        handleOtherClick(e) {
            // if (this.clickHide && e.target.className === "fs-popup") {
            console.log("e.target.className ", e.target.className)
            if (this.clickHide && e.target.className.includes("fs-popup")) {
                console.log(666666)
                this.$emit("close")
            }
            if (this.clickHide && this.isMDrawer && e.target.className === "mask") {
                this.$emit("close")
            }
        },
        reSizeT() {
            if (document.body.clientWidth <= 768) {
                this.popupWidth = this.mobileWidth ? this.mobileWidth : "100%"
            } else {
                this.popupWidth = this.width + "px"
            }
        },
        fixScreen() {
            fixScroll.fixed()
        },
        noFixScreen() {
            fixScroll.unfixed()
        },
    },
    watch: {
        show: {
            handler(n, o) {
                this.$emit("change", n)
                if (process.client) {
                    if (n && this.isFixScroll) {
                        fixScroll.fixed()
                    } else if (n && document.body.clientWidth <= 768 && !this.isFixScroll) {
                        fixScroll.fixed()
                    } else {
                        fixScroll.unfixed()
                    }
                }
            },
        },
        width: {
            handler(n, o) {
                this.popupWidth = this.width + "px"
            },
        },
    },
}
</script>
<style lang="scss" scoped>
.fs-popup {
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    width: 100%;
    height: 100%;
    z-index: 111;
    display: flex;
    align-items: center;
    justify-content: center;

    &.is_new {
        &.all {
            border-radius: 0;
        }
        &.float .fs-popup-body {
            &.no-padding {
                padding-bottom: 24px;
            }
        }
        .fs-popup-header {
            padding: 16px 24px 0 24px;
            // border-bottom: 0;
        }
        .title_box {
            padding-top: 8px;
            width: 100%;
            border-bottom: 1px solid #dee0e3;
            .title {
                @include font18();
                font-weight: 600;
            }
        }
        .iconfont_close_box {
            position: absolute;
            top: 16px;
            right: 16px;
            width: auto;
            min-height: auto;
            height: auto;
            padding: 4px;
        }
        .iconfont_close {
            width: 20px;
            height: 20px;
            line-height: 20px;
            font-size: 20px;
        }
        .fs-popup-body {
            padding: 16px 24px 0px 24px;
        }
        .fs-popup-footer {
            // margin-top: -8px;
            padding: 16px 24px 24px 24px;
            &.no-padding {
                padding: 0;
            }
        }
        @include mobile {
            &.float .fs-popup-body {
                padding: 16px 16px 0 16px;
                &.no-padding {
                    padding-bottom: 24px;
                }
            }
            .fs-popup-header {
                padding: 16px 16px 0;
            }

            .fs-popup-body {
                padding: 20px 16px 0px 16px;
            }
            .fs-popup-footer {
                padding: 16px 16px 20px 16px;
                &.no-padding {
                    padding: 0;
                }
            }
        }
    }
}

.fs-popup-ctn {
    background-color: #fff;
    border-radius: 8px;
    max-height: calc(100% - 128px);
    z-index: 101;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    position: relative;
    @media (max-width: 960px) {
        background-color: #fff;
    }
}

.fs-popup-loading {
    width: 750px;
    height: 290px;
    &:hover {
        color: $textColor1;
    }
}
.iconfont_close_box {
    position: absolute;
    top: 16px;
    right: 16px;
    // transform: translateY(-50%);
    transition: all 0.3s;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    &:hover {
        .iconfont_close {
            color: $textColor1;
        }
        background-color: rgba(25, 25, 26, 0.04);
    }
}
.iconfont_close {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 20px;
    height: 20px;
    font-size: 20px;
    font-weight: 400;
    color: #707070;
}

.fs-popup-header {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 20px;
    align-self: stretch;
    justify-content: space-between;
    padding: 16px 16px 0 24px;
    @media (max-width: 768px) {
        padding: 16px 16px 0;
    }
}

.title_box {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 8px;
    width: 100%;
    padding-bottom: 16px;
    padding-right: 49px;
    border-bottom: 1px solid #dee0e3;
    .title {
        flex: 1 1 auto;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        color: $textColor1;
        @include font18;
        padding-right: 32px;
        font-weight: 600;
    }
    .iconfont_success {
        margin-right: 10px;
        @include font20;
        color: #339933;
        flex: 0 0 20px;
    }
}
.header_slot {
    width: 100%;
}

.fs-popup-close {
    flex: 0 0 40px;
    position: relative;
    width: 100%;
    .iconfont_close_box {
        top: 16px;
        right: 16px;
        transform: translateY(0);
    }
}

.fs-popup-body {
    position: relative;
    flex: 1 1 auto;
    overflow-y: auto;
}
.fs-popup-footer {
    flex-shrink: 0;
}

.mask {
    position: fixed;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    z-index: 108;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.3);
}

@media (max-width: 960px) {
    .iconfont_close {
        // width: 16px;
        // height: 16px;
        // line-height: 16px;
        // font-size: 16px;
    }
    .fs-popup {
        .fs-popup-ctn {
            // width: 100%;
            // max-height: 100%;
            // border-radius: 8px;
        }
        .fs-popup-ctn_height {
            height: auto;
        }
        // .iconfont_close_box {
        //     top: 0;
        //     right: 0;
        //     transform: translateY(0);
        //     padding: 0;
        //     width: 52px;
        //     min-height: 48px;
        //     height: 100%;
        //     display: flex;
        //     justify-content: center;
        //     align-items: center;
        // }
        // .fs-popup-header {
        //     flex-shrink: 0;
        //     padding: 12px 16px;
        //     position: relative;
        //     min-height: 48px;
        //     border-bottom: 1px solid $borderColor2;
        // }
        // .title_box {
        //     display: flex;
        //     align-items: center;
        //     width: 100%;
        //     .title {
        //         flex: 1 1 auto;
        //         overflow: hidden;
        //         text-overflow: ellipsis;
        //         white-space: nowrap;
        //         color: $textColor1;
        //         @include font16;
        //     }
        //     .iconfont_success {
        //         margin-right: 10px;
        //         @include font20;
        //         color: #339933;
        //         flex: 0 0 20px;
        //     }
        // }
        &.isMDrawer {
            @media (max-width: 768px) {
                height: auto;
                top: auto;
                border-radius: 8px 8px 0 0;
                overflow: hidden;
                .fs-popup-ctn {
                    max-height: 100vh;
                    width: 100% !important;
                    overflow: auto;
                    height: auto;
                    border-radius: 0;
                }
            }
        }
    }
}
</style>
