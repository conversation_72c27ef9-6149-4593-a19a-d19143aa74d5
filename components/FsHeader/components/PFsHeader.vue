<template>
    <div class="fs-header" v-loading.fullscreen="loading">
        <HeaderHoliday v-if="!isMobile"></HeaderHoliday>
        <div class="header-top-wrap">
            <div class="header-top">
                <div class="left-box">
                    <template v-if="website === 'cn'">
                        <i class="iconfont">&#xe66c;&nbsp;</i>
                    </template>
                    <nuxt-link v-if="top_data && top_data.fsSite && top_data.fsSite.title" class="left-link" :to="localePath({ path: top_data.fsSite.url })" tabindex="0" @click.native="topClick(top_data.fsSite.url)">{{
                        top_data && top_data.fsSite && top_data.fsSite.title ? top_data.fsSite.title : ""
                    }}</nuxt-link>
                    <span
                        class="left-link"
                        tabindex="0"
                        :title="top_data && top_data.notice && top_data.notice.title ? top_data.notice.title.replace(/&nbsp;/g, ' ') : ''"
                        v-html="top_data && top_data.notice && top_data.notice.title ? top_data.notice.title : ''"
                        @click.stop="
                            topClick(top_data.notice.url)
                            noticeClick(top_data.notice.url)
                        "></span>
                </div>
                <div class="right-box" ref="rightBox">
                    <template v-if="website === 'de'">
                        <div class="thruted-box" ref="thrutedBox">
                            <a class="thruted" tabindex="0" href="https://www.trustedshops.de/bewertung/info_X6F10DAB6FE00F8D75CD228AC106DD37B.html" target="_blank">
                                <img src="https://resource.fs.com/mall/generalImg/20221118180655o1xr3i.png" alt />
                                <span>Käuferschutz</span>
                            </a>
                        </div>
                        <span class="right-line"></span>
                    </template>

                    <div
                        class="right-service-wrap"
                        ref="rightContact"
                        @keyup.esc.stop="serviceTopMouseleave"
                        @keydown.tab.stop="handleServiceFocusout"
                        @mouseenter="serviceTopMouseenter"
                        @mouseleave="serviceTopMouseleave">
                        <div class="contact-us-box" id="contact-us-box">
                            <div class="tabindex-box">
                                <!-- <div class="tabindex-box" tabindex="0" @keyup.enter="serviceTopMouseenter"> -->
                                <!-- <span class="iconfont iconfont-contact-us" v-show="noReplyNum == 0">&#xe729;</span> -->
                                <img class="iconfont-contact-us" v-show="noReplyNum > 0" src="https://resource.fs.com/mall/generalImg/20230509115819fdc9bj.svg" />
                                <!-- localePath({ path: solutionsNvaListNew[solutions_active_index].url }) -->
                                <a class="txt" :href="$localeLink('/contact-us.html')">{{ $c("components.FsContactUs.btn") }}</a>
                                <!-- <span class="iconfont iconfont-down" :class="{ 'iconfont-down-up': show_contact }">&#xe704;</span> -->
                            </div>
                        </div>
                        <div class="contact-currency-wrap" v-if="false">
                            <!-- <div class="contact-currency-wrap" v-show="show_contact"> -->
                            <div class="triangle-up" :style="triangleUpContactStyle"></div>
                            <div class="triangle-up-line"></div>
                            <div class="content-pop">
                                <div class="sales-support">
                                    <div class="tit">{{ $c("components.FsContactUs.list_tit3") }}</div>
                                    <div class="des">{{ $c("components.FsContactUs.time") }}</div>
                                    <div class="btn-box">
                                        <fs-button
                                            tabindex="0"
                                            @click="
                                                chat()
                                                gaEventService('Live Chat')
                                                show_contact = false
                                            "
                                            id="online_chat"
                                            type="blackline">
                                            <client-only>
                                                <div class="live_chat_dot" v-show="noReplyNum > 0 && saleOnline" :style="{ width: noReplyNum > 9 ? '36px' : '20px' }">
                                                    <div class="wrap">
                                                        <div class="dot">
                                                            <span>{{ noReplyNum }}</span>
                                                        </div>
                                                        <img src="https://resource.fs.com/mall/generalImg/20230517100807icox03.svg" alt />
                                                    </div>
                                                </div>
                                                <img src="https://resource.fs.com/mall/generalImg/20231027095258o9ujus.png" alt="" v-show="noReplyNum == 0 && saleOnline" />
                                                <img src="https://resource.fs.com/mall/generalImg/202310270952582uct6d.png" alt="" v-show="!saleOnline" />
                                                <span style="margin-left: 4px">{{ $c("components.FsContactUs.live_btn") }}</span>
                                            </client-only>
                                        </fs-button>
                                    </div>
                                </div>
                                <div class="list-wrap" :class="`list-wrap-${website}`">
                                    <div class="list-item clearfix">
                                        <img src="https://resource.fs.com/mall/generalImg/20230517100807z22eap.svg" alt />
                                        <div class="txt-box">
                                            <span
                                                class="tit living"
                                                tabindex="0"
                                                @click="
                                                    chat()
                                                    gaEventService('Live Chat')
                                                    show_contact = false
                                                ">
                                                <span class="tit-txt">{{ $c("components.FsContactUs.list_tit3") }}</span>
                                                <span class="iconfont iconfont_right">&#xe726;</span>
                                            </span>
                                            <span class="des" :class="{ jp: website === 'jp' }">{{ $c("components.FsContactUs.time") }}</span>
                                        </div>
                                    </div>
                                    <div class="list-item clearfix">
                                        <span class="iconfont iconfont-contact">&#xe728;</span>
                                        <div class="txt-box">
                                            <nuxt-link
                                                class="tit"
                                                tabindex="0"
                                                @click.native="
                                                    gaEventService('Contact Sales')
                                                    show_contact = false
                                                "
                                                :to="localePath({ name: 'LivechatServicemail' })"
                                                >{{ $c("components.FsContactUs.top_btn") }}</nuxt-link
                                            >
                                        </div>
                                    </div>
                                    <div class="list-item clearfix">
                                        <span class="iconfont iconfont-technical">&#xe64d;</span>
                                        <div class="txt-box">
                                            <nuxt-link
                                                class="tit"
                                                tabindex="0"
                                                @click.native="
                                                    gaEventService('Tech Support')
                                                    show_contact = false
                                                "
                                                :to="localePath({ path: '/solution_support.html' })"
                                                >{{ $c("components.FsContactUs.list_tit1") }}</nuxt-link
                                            >
                                        </div>
                                    </div>
                                    <div class="list-item clearfix" v-if="false">
                                        <span class="iconfont iconfont-support">&#xe721;</span>
                                        <div class="txt-box">
                                            <nuxt-link
                                                class="tit"
                                                @click.native="
                                                    gaEventService('Support Center')
                                                    show_contact = false
                                                "
                                                :to="localePath({ path: '/support.html' })"
                                                >{{ $c("components.FsContactUs.list_tit2") }}</nuxt-link
                                            >
                                        </div>
                                    </div>
                                    <div class="list-item clearfix">
                                        <span class="iconfont iconfont-feedback">&#xe721;</span>
                                        <div class="txt-box">
                                            <span
                                                class="tit"
                                                tabindex="0"
                                                @click="
                                                    feedbackShow()
                                                    gaEventService('Feedback')
                                                    show_contact = false
                                                "
                                                >{{ $c("components.FsContactUs.list_tit5") }}</span
                                            >
                                        </div>
                                    </div>
                                    <div class="list-item clearfix" v-if="!isRussia">
                                        <span class="iconfont iconfont-tel">&#xe66c;</span>
                                        <div class="txt-box">
                                            <span class="tit noHoverLine" v-if="website !== 'sg'" v-html="`${$c('components.FsContactUs.list_tit4')}${website === 'de' ? '<br>' : ' '}${top_data.phone}`"></span>
                                            <span class="tit noHoverLine" v-else>
                                                Call
                                                <a tabindex="0" :href="`tel:${top_data.phone}`">{{ top_data.phone }}</a>
                                            </span>
                                            <span class="des" :class="{ jp: website === 'jp' }" v-if="website !== 'sg'">{{
                                                website === "en" && iso_code === "JP" ? "Monday to Friday 10:00 – 17:00" : $c("components.FsContactUs.time1")
                                            }}</span>
                                            <span class="des" v-else>Mon - Fri｜24h Phone Support</span>
                                        </div>
                                    </div>
                                    <div class="list-item clearfix" v-if="isJp || isJpEn">
                                        <span class="iconfont iconfont-fax">&#xe736;</span>
                                        <div class="txt-box">
                                            <span class="tit noHoverLine">{{ $c("components.smallComponents.jpFax") }}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <span class="right-line"></span>
                    <div class="right-country" ref="rightCountry" @keyup.esc="countryTopMouseleave" @mouseenter="countryTopMouseenter" @mouseleave="countryTopMouseleave">
                        <div class="current-country-box">
                            <div class="tabindex-box" tabindex="0" @keyup.enter="countryTopMouseenter">
                                <!-- <img src="https://resource.fs.com/mall/generalImg/20231027105249iq6yoe.svg" alt="" /> -->
                                <span class="country-info" v-if="currency === 'USD'">{{ `${country_name} / ${symbol} ${currency}` }}</span>
                                <span class="country-info" v-else>{{ `${country_name} / ${symbol} ${currency}` }}</span>
                                <span class="iconfont iconfont-down" :class="{ 'iconfont-down-up': show_country }">&#xe704;</span>
                            </div>
                        </div>
                        <div class="country-currency-wrap" v-show="show_country">
                            <div class="triangle-up" :style="triangleUpStyle"></div>
                            <div class="triangle-up-line"></div>
                            <div class="country-currency-box">
                                <pc-change-website :isRequest="true" @focus="changeWebsiteFocus" @toogleClick="changeWebsiteClick" @change="changeWebsiteChange" @input="changeWebsiteInput"></pc-change-website>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <nav class="header-ctn-wrap">
            <div
                class="header-ctn"
                :class="{
                    'header-ctn-company': this.isCompanyAndAccountPage,
                    'is-search': this.isCompanyAndAccountPage && this.isCompanyShowSearch,
                }">
                <nuxt-link :to="localePath({ name: 'home' })" tabindex="0" aria-label="FS" @click.native="topClick('Logo')" :class="logoClassName"> </nuxt-link>
                <div class="menu-list" ref="menuList" v-show="menu_data_new && menu_data_new.length" @keyup.esc="menuListMouseleave" @mouseleave="menuListMouseleave">
                    <template v-for="(item, index) in menu_data_new">
                        <p class="chunk-purple"></p>
                        <div class="menu-item" @keydown.tab="handleMenuTab" @mouseenter="menuMouseenter(index + 1)">
                            <div @keyup.enter="menuMouseenter(index + 1)" class="menu-label" :class="{ isActive: menu_active === index + 1 }" tabindex="0">
                                {{ item.nav ? item.nav : item.title }}
                            </div>
                            <client-only>
                                <div
                                    class="header-menu-more-wrap header-menu-more"
                                    :class="{
                                        'header-menu-more-category': index === 0,
                                        'header-menu-more-solution': index === 1,
                                        'header-menu-more-classify': index > 1,
                                    }"
                                    v-show="menu_active === index + 1"
                                    @click="resourcePage">
                                    <!--All products-->
                                    <template v-if="index === 0">
                                        <div class="more-left">
                                            <section class="left-list">
                                                <div
                                                    :ref="`products-left-nav-${index}`"
                                                    class="list-item"
                                                    v-for="(item, index) in menu_data_new[0].data"
                                                    :key="item.id"
                                                    :class="{ 'list-item-active': index === category_active_index }"
                                                    @mouseenter="menuleftMouseenter('category_active_index', index)">
                                                    <a tabindex="0" :href="$localeLink(item.url)" class="category-name" @keyup.enter="menuleftMouseenter('category_active_index', index)" @click.stop="hideMenumore(item)">
                                                        <span>{{ item.name ? item.name : "" }}</span>
                                                    </a>
                                                </div>
                                                <div class="list_active_block" :style="productLeftNavStyle"></div>
                                            </section>
                                        </div>
                                        <div class="more-right">
                                            <div
                                                class="category-box"
                                                v-show="
                                                    menu_data_new[0].data &&
                                                    menu_data_new[0].data.length &&
                                                    menu_data_new[0].data[category_active_index] &&
                                                    menu_data_new[0].data[category_active_index]?.children &&
                                                    menu_data_new[0].data[category_active_index]?.children.length
                                                ">
                                                <div class="category-item-box" v-for="(c, ci) in menu_data_new[0].data[category_active_index]?.children" :key="`${c.name}_${ci}`">
                                                    <div class="category-item">
                                                        <span class="category-img" :alt="c.name" :style="{ backgroundImage: 'url(' + c.image + ')' }"></span>
                                                        <div class="category-list">
                                                            <a :href="$handleLink(c.url).url" tabindex="0" class="category-list-title" @click="hideMenumore(c)">
                                                                <span>{{ c.name }}</span>
                                                            </a>
                                                            <div class="category-list-children category-list-children-nav" v-show="c?.children && c?.children.length">
                                                                <p class="category-list-children-item" v-for="p in c.children" :key="p.name">
                                                                    <a
                                                                        @click="hideMenumore(p)"
                                                                        tabindex="0"
                                                                        :class="{ item_info_hot: p.is_has_hot_products, item_info_new: p.is_has_new_products }"
                                                                        :href="$handleLink(p.url).url">
                                                                        {{ p.name }}
                                                                    </a>
                                                                    <FsTextTag :type="p.is_has_new_products && 1" :styles="{ fontSize: '12px' }" :CnHeaderTagShow="menu_active" />
                                                                    <FsTextTag :type="p.is_has_hot_products && 2" :styles="{ fontSize: '12px' }" :CnHeaderTagShow="menu_active" />
                                                                </p>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </template>
                                    <!--solution-->
                                    <template v-else-if="index === 1">
                                        <!-- <div class="more-left">
                                            <section class="left-list">
                                                <div
                                                    class="list-item"
                                                    v-for="(item, index) in solutionsNvaList"
                                                    :key="index"
                                                    :class="{ 'list-item-active': index === solutions_active_index }"
                                                    @mouseenter="menuleftMouseenter('solutions_active_index', index)">
                                                    <span class="category-name" tabindex="0" @keyup.enter="menuleftMouseenter('solutions_active_index', index)">{{ item.title }}</span>
                                                </div>
                                                <div class="list_active_block" :style="{ top: `${solutions_active_index * 48}px` }"></div>
                                            </section>
                                        </div> -->
                                        <div class="more-left">
                                            <section class="left-list">
                                                <div
                                                    class="list-item"
                                                    :ref="`solutions-left-nav-${index}`"
                                                    v-for="(item, index) in solutionsNvaListNew"
                                                    :key="index"
                                                    :class="{ 'list-item-active': index === solutions_active_index }"
                                                    @mouseenter="menuleftMouseenter('solutions_active_index', index)">
                                                    <a href="javascript:;">
                                                        <span class="category-name" tabindex="0" @keyup.enter="menuleftMouseenter('solutions_active_index', index)">{{ item.title }}</span>
                                                    </a>
                                                </div>
                                                <div class="list_active_block" :style="solutionsLeftNavStyle"></div>
                                            </section>
                                        </div>
                                        <div class="more-right">
                                            <div class="solution_list_head">
                                                <div class="solution_list_head_left">
                                                    <h3 class="category-box-new-tit">
                                                        <!-- <a :href="localePath({ path: solutionsNvaListNew[solutions_active_index].url })">{{ solutionsNvaListNew[solutions_active_index].title }}</a> -->
                                                        <a href="javascript:;">{{ solutionsNvaListNew[solutions_active_index].title }}</a>
                                                    </h3>
                                                    <p class="category-box-new-desc">
                                                        <span>{{ solutionsNvaListNew[solutions_active_index].desc }}</span>
                                                        <a :href="localePath({ path: solutionsNvaListNew[solutions_active_index].url })">{{
                                                            $c("components.smallComponents.See_all").replace("xxxx", solutionsNvaListNew[solutions_active_index].title)
                                                        }}</a>
                                                    </p>
                                                </div>
                                                <a class="solution_list_head_right" v-if="website === 'en' && env === 'release'" href="https://pre-design-center.whgxwl.com/">
                                                    <FsButton type="red">FS Design Center</FsButton>
                                                </a>
                                            </div>
                                            <div class="category-box category-box-new">
                                                <div class="category-item-box" v-for="(item, index) in solutionsNvaListNew[solutions_active_index].list" :key="index">
                                                    <div class="category-item">
                                                        <div class="solution_new_pic">
                                                            <a :href="localePath({ path: item.url })"> <img v-lazy="item.img" alt="" /></a>
                                                            <!-- <a :href="item.url">
                                                                <img :src="item.img" alt="" />
                                                            </a> -->
                                                        </div>
                                                        <div class="solution_new_txt">
                                                            <a :href="localePath({ path: item.url })">{{ item.title }}</a>
                                                            <!-- <a :href="item.url">{{ item.title }}</a> -->
                                                            <p>{{ item.desc }}</p>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </template>
                                    <!--other-->
                                    <template v-else-if="classify_new_data && classify_new_data.length && classify_new_data[index - 2]">
                                        <div class="classify_box classify_box_new" :class="`classify_box_${index}`" v-for="(item, i) in [classify_new_data[index - 2]]" :key="i">
                                            <div class="classify_box_lt">
                                                <h3 class="classify_title">{{ item.title }}</h3>
                                                <p class="classify_desc">{{ item.desc }}</p>
                                                <ul>
                                                    <li v-for="(service_item, service_index) in item.data" :key="service_index">
                                                        <h3 class="service_item_tit">{{ service_item.title }}</h3>
                                                        <template v-if="index === 4 && service_index == 2">
                                                            <div v-if="!isCn && socialLink && socialLink.length" class="share_box">
                                                                <a
                                                                    v-for="s_item in socialLink"
                                                                    :key="s_item.title"
                                                                    class="iconfont iconfont-share"
                                                                    target="_blank"
                                                                    tabindex="0"
                                                                    :href="s_item.link[website] || s_item.link.en"
                                                                    :title="s_item.title"
                                                                    :aria-label="s_item.title"
                                                                    v-html="s_item.icon"></a>
                                                            </div>
                                                            <div v-if="isCn" class="about_fs_cn">
                                                                <img src="https://resource.fs.com/mall/generalImg/20240912102456ce06ej.png" width="80" height="80" alt="" />
                                                                <!-- <fs-popover position="right" :icon="false" :transfer="false">
                                                                    <img src="https://resource.fs.com/mall/generalImg/20230221160741dsqv2k.svg" width="26" height="26" alt="" slot="trigger" />
                                                                    <img src="https://img-en.fs.com/zh/includes/templates/fiberstore/images/feisu-footer-wx.png" width="170" height="170" alt="" />
                                                                </fs-popover> -->
                                                            </div>
                                                        </template>
                                                        <template v-else>
                                                            <p v-for="(service_item_l, service_index_l) in service_item.list" :key="service_index_l">
                                                                <a v-if="index === 4 && service_index === 1 && service_index_l === 0" id="service_chat" :href="`mailto:${service_item_l.title}`" @click.stop="salesClick">{{
                                                                    service_item_l.title
                                                                }}</a>
                                                                <a v-else-if="index === 4 && service_index === 1 && service_index_l === 1" id="bdtj_lxdh" :href="`tel:${service_item_l.title}`" @click.stop="telClick">{{
                                                                    service_item_l.title
                                                                }}</a>
                                                                <a
                                                                    v-else
                                                                    :href="service_item_l.url"
                                                                    :target="getHostNameTagATarget(service_item_l.url)"
                                                                    @click="burryPoint(index, service_item.title, service_item_l.title)"
                                                                    >{{ service_item_l.title }}</a
                                                                >
                                                                <!-- <a :href="localePath({ path: service_item_l.url })">{{ service_item_l.title }}</a> -->
                                                            </p>
                                                        </template>
                                                    </li>
                                                </ul>
                                            </div>
                                            <div class="classify_box_rt">
                                                <template v-if="!item.image_data?.button">
                                                    <a :href="item.image_data?.url">
                                                        <img v-lazy="item.image" :alt="item.title" />
                                                    </a>
                                                </template>
                                                <template v-else>
                                                    <img v-lazy="item.image" :alt="item.title" />
                                                    <div class="classify_box_rt_content">
                                                        <h3 v-if="item.image_data?.title" v-html="item.image_data.title"></h3>
                                                        <p v-if="item.image_data?.desc" v-html="item.image_data?.desc"></p>
                                                        <a :href="item.image_data?.url" @click.stop="bdClick(item.image_data?.url)">
                                                            <fs-button type="red" class="banner_main_font_more">{{ item.image_data?.button }}</fs-button>
                                                        </a>
                                                    </div>
                                                </template>
                                            </div>
                                            <!-- <div class="classify_left" v-show="item.children && item.children.length">
                                                <div class="classify_item" v-for="(citem, cindex) in item.children" :key="cindex">
                                                    <div class="classify_item_title" v-html="citem.title"></div>
                                                    <div class="classify_item_info" :class="{ 'last-info': cindex === citem.children.length - 1 }" v-for="(cinfo, cindex) in citem.children" :key="cindex">
                                                        <div class="info_box" v-show="['chat', 'bug'].includes(cinfo.url)">
                                                            <span class="info" tabindex="0" @keyup.enter.stop="urlClick(cinfo.url)" @click.stop="urlClick(cinfo.url)" v-html="cinfo.title"></span>
                                                        </div>
                                                        <div class="info_box" v-show="!['chat', 'bug'].includes(cinfo.url)">
                                                            <a
                                                                class="info"
                                                                @click="gaEventFirst(cinfo)"
                                                                :target="cinfo.tag === 2 ? '_blank' : '_self'"
                                                                v-show="$handleLink(cinfo.url).type === 2"
                                                                :href="$handleLink(cinfo.url).url"
                                                                v-html="cinfo.title"
                                                                tabindex="0"
                                                                :title="i === 0 ? cinfo.title : ''"></a>
                                                            <nuxt-link
                                                                :target="cinfo.tag === 2 ? '_blank' : '_self'"
                                                                @click.native="hideMenumore(cinfo)"
                                                                v-show="$handleLink(cinfo.url).type === 1"
                                                                :to="localePath({ path: cinfo.url == 'solution_design.html' ? $handleLink(cinfo.url).url + '?resource=1' : $handleLink(cinfo.url).url })"
                                                                class="info"
                                                                tabindex="0"
                                                                v-html="cinfo.title"
                                                                :title="i === 0 ? cinfo.title : ''"></nuxt-link>
                                                            <FsTextTag v-if="cinfo.tag === 1 || cinfo.tag === 3" :type="cinfo.tag === 3 ? 2 : 1" :styles="{ fontSize: '12px' }" :CnHeaderTagShow="menu_active" />
                                                        </div>
                                                    </div>
                                                </div>
                                            </div> -->
                                        </div>
                                    </template>
                                    <!-- <a href="javascript:;" v-show="menu_active != 1" class="iconfont close-more-btn" tabindex="0" @click.stop="hideMenumore">&#xf30a;</a> -->
                                </div>
                            </client-only>
                        </div>
                    </template>
                    <!-- <p class="chunk-orange"></p> -->
                </div>
                <div class="search-box" v-if="false">
                    <div class="icon-box" v-if="isCompanyAndAccountPage">
                        <div class="icon-box-content" @click.stop="handleIconSearchClick">
                            <span class="iconfont iconfont-search">&#xe694;</span>
                        </div>
                    </div>
                    <div class="search-content" @keyup.esc="show_search_result = false" @keydown.tab="handleSearchTab">
                        <div class="search-form">
                            <!-- <input type="text" class="search-inp" ref="searchInput" :placeholder="$c('components.smallComponents.search.pcPlaceholder')" v-model.trim="search_inp" @focus="searchFocus" @input="change" />
                            <span class="iconfont iconfont-clear" tabindex="0" @keyup.enter.stop="clearSearch" v-if="search_inp" @click.stop="clearSearch">&#xf30a;</span>
                            <button type="submit" @click.stop class="sbtn" tabindex="0">
                                <span class="iconfont iconfont-search">&#xe694;</span>
                            </button> -->
                            <FsSelectSearch v-model="search_inp" :placeholder="$c('components.smallComponents.search.pcPlaceholder')" @focus="searchFocus" @clear="clearSearch" @input="change" @search="submitSearch" />
                        </div>
                        <template v-if="is_en">
                            <div class="search-result" v-if="search_inp && search_list && search_list.length && show_search_result">
                                <ul>
                                    <li v-for="(v, i) in search_list" :key="i" tabindex="0" @keyup.enter="clickOption(v.name, 1)" @click.stop="clickOption(v.name, 1)">
                                        <a v-html="highLight(v.name)"></a>
                                    </li>
                                </ul>
                            </div>
                            <div class="search-result" v-else-if="(!search_inp && show_search_result) || (!search_list.length && show_search_result)">
                                <div class="search-result-before">
                                    <div class="recent" v-if="history_list && history_list.length > 0">
                                        <div class="title">{{ $c("components.smallComponents.search.recentSearch") }}</div>
                                        <div class="recent-list">
                                            <div v-for="(v, i) in history_list" tabindex="0" @keyup.enter="clickOption(v, 2)" :title="v" :key="i" @click.stop="clickOption(v, 2)">
                                                {{ v }}
                                                <i class="iconfont icon" tabindex="0" @keyup.enter.stop="deleteSearch(v)" @click.stop="deleteSearch(v)">&#xf30a;</i>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="hot" v-if="hotList && hotList.length > 0">
                                        <div class="title">
                                            <p>{{ $c("components.smallComponents.search.hotSearch") }}</p>
                                            <p class="change" tabindex="0" @keyup.enter="rotate" @click.stop="rotate">
                                                <i class="iconfont icon" :class="{ active: rotateType }">&#xe705;</i>
                                                <span>{{ $c("components.smallComponents.search.change") }}</span>
                                            </p>
                                        </div>
                                        <div class="hot-list">
                                            <div v-for="(v, i) in hotList" :title="v.name" tabindex="0" @keyup.enter="clickOption(v.name, 3)" :key="i" @click.stop="clickOption(v.name, 3)">{{ v.name }}</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </template>
                        <template>
                            <template v-if="search_page_state == 1">
                                <div class="search-result_not_en" v-if="search_inp && search_list && search_list.length && show_search_result">
                                    <div class="search-result_not_en-item" v-for="(v, i) in search_list" :key="i" @click="clickToDetail(v.products_id)">
                                        <img v-lazy="v.img" class="product-img" />
                                        <div class="product">
                                            <div class="product-info" v-html="highLight(v.products_name)"></div>
                                            <div class="product-ids" v-if="v.products_id == search_inp">
                                                #
                                                <span>{{ v.products_id }}</span>
                                            </div>
                                            <div class="product-ids" v-else-if="v.products_model">
                                                <span>{{ v.products_model }}</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="search-result_not_en-more" v-if="search_list.length > 1" @click="clickOption(search_inp, 1)">
                                        <a>{{ $c("components.smallComponents.search.isntEn") }}</a>
                                    </div>
                                </div>
                            </template>
                            <template v-else-if="search_page_state == 2">
                                <div class="search-result_not_en" v-if="search_inp && search_list && search_list.length && show_search_result">
                                    <div class="search-result_not_en-item_other" v-for="(v, i) in search_list" :key="i" @click="clickToDetail(v.products_id)">
                                        <div class="search-result_not_en-item_tit" v-html="subs($c('components.smallComponents.search.isDB'), v, 1)"></div>
                                        <div class="search-result_not_en-item_con">
                                            <img v-lazy="v.img" class="product-img" />
                                            <div class="product">
                                                <div class="product-info" v-html="highLight(v.products_name)"></div>
                                                <div class="product-ids" v-if="v.products_id == search_inp">
                                                    #
                                                    <span>{{ v.products_id }}</span>
                                                </div>
                                                <div class="product-ids" v-else-if="v.products_model">
                                                    <span>{{ v.products_model }}</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </template>
                            <template v-else-if="search_page_state == 3">
                                <div class="search-result_not_en" v-if="search_inp && search_list && search_list.length && show_search_result">
                                    <div class="search-result_not_en-item_other" v-for="(v, i) in search_list" :key="i" @click="clickToDetail(v.products_id)">
                                        <div class="search-result_not_en-item_tit" v-html="subs($c('components.smallComponents.search.isDD'), v, 2)"></div>
                                        <div class="search-result_not_en-item_con">
                                            <img v-lazy="v.img" class="product-img" />
                                            <div class="product">
                                                <div class="product-info" v-html="highLight(v.products_name)"></div>
                                                <div class="product-ids" v-if="v.products_id == search_inp">
                                                    #
                                                    <span>{{ v.products_id }}</span>
                                                </div>
                                                <div class="product-ids" v-else-if="v.products_model">
                                                    <span>{{ v.products_model }}</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </template>
                        </template>
                    </div>
                </div>
                <div class="header-ctn-right">
                    <div class="search_box">
                        <span class="iconfont search" @click="clickSearchIcon(false)">&#xe694;</span>
                    </div>
                    <!-- <notifications class="icon-box"></notifications> -->
                    <div class="cart-box icon-box">
                        <nuxt-link v-if="qty > 0" tabindex="0" tag="span" :to="localePath({ name: 'shopping-cart' })" class="cart-top" @click.native="cartClick">
                            <div class="cart-icon-box">
                                <span class="iconfont iconfont-cart">&#xe693;</span>
                                <span class="cart-num" :class="{ more: qty > 99 }" v-if="qty">{{ qty > 99 ? "99+" : qty }}</span>
                            </div>
                        </nuxt-link>
                        <fs-popover v-else position="bottom" :icon="false" :offset="14" :closeIcon="false" :popperStyle="{ borderRadius: '8px' }">
                            <span class="cart-top empty" slot="trigger">
                                <div class="cart-icon-box">
                                    <span class="iconfont iconfont-cart">&#xe693;</span>
                                </div>
                            </span>
                            {{ $c("pages.ShoppingCart.newCart.cartEmptyTips") }}
                        </fs-popover>
                        <!-- <Transition>
                            <div class="cart-main" v-if="show_cart_list">
                                <div class="cart-ctn">
                                    <div class="cart_loading_box" v-if="qty && !cart_request && cart_loading">
                                        <div class="cart_loading" v-loading:30="cart_loading"></div>
                                        <p class="cart_loading_info">{{ $c("components.smallComponents.pHeader.cartLoading") }}</p>
                                    </div>
                                    <div class="cart-product-box" v-if="cart_request && qty">
                                        <div class="product-head">{{ isRuNum(qty) }}</div>
                                        <div class="product-list">
                                            <div class="product-item" v-for="(item, index) in cartList" :key="index">
                                                <img class="product-img" :src="item.products_info.image" />
                                                <div class="product-content">
                                                    <div class="product-title" :title="item.products_info.productName" v-html="item.products_info.productName"></div>
                                                    <p class="product-num">{{ $c("pages.ShoppingCart.newCart.qty") + (website == "fr" ? " " : "") + ": " + item.qty }}</p>
                                                </div>
                                                <span class="product-price" v-html="website != 'en' ? item.products_info.taxPriceExchangeRateFormat : item.products_info.finalPriceExchangeRateFormat"></span>
                                            </div>
                                        </div>

                                        <div class="checkout-box">
                                            <div class="checkout-box-text">
                                                <span class="checkout-box-left">{{ $c("pages.ShoppingCart.Subtotal") }}{{ website == "fr" ? " " : "" }}:</span>
                                                <span class="checkout-box-right" v-html="website != 'en' ? cartData.checkedPriceInfo.total.totalFormat : cartData.totalPriceInfo.subTotal.subTotalFormat"></span>
                                            </div>
                                            <div class="checkout-box-btn">
                                                <fs-button class="cart_btn1 toCheck" tabindex="0" @click="toCheck" type="red">{{ $c("pages.ShoppingCart.newCart.Secure_Checkout") }}</fs-button>
                                                <nuxt-link :to="localePath({ name: 'shopping-cart' })" tabindex="-1" :style="{ display: 'block', width: `100%` }">
                                                    <fs-button type="gray" tabindex="0" @click="toShopCart" :style="{ width: `100%` }">{{ $c("pages.ShoppingCart.newCart.viewCart") }}</fs-button>
                                                </nuxt-link>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="cart-empty" v-if="!qty">
                                        <div class="cart-empty-title">{{ $c("pages.ShoppingCart.newCart.headerEmpty") }}</div>
                                        <div class="cart-empty-cont">
                                            <div class="cart-empty-text">
                                                <span class="cart-empty-left">{{ $c("pages.ShoppingCart.Subtotal") }}{{ website == "fr" ? " " : "" }}:</span>
                                                <span class="cart-empty-right" v-html="cartData.totalPriceInfo && cartData.totalPriceInfo.subTotal.subTotalFormat"></span>
                                            </div>
                                            <div class="cart-empty-btn">
                                                <fs-button class="cart_btn1" type="red" tabindex="0" :disabled="true">{{ $c("pages.ShoppingCart.newCart.Secure_Checkout") }}</fs-button>
                                                <nuxt-link :to="localePath({ name: 'shopping-cart' })" tabindex="-1" :style="{ display: 'block', width: `100%` }">
                                                    <fs-button type="gray" tabindex="0" @click="toShopCart" :style="{ width: `100%` }">{{ $c("pages.ShoppingCart.newCart.viewCart") }}</fs-button>
                                                </nuxt-link>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="triangleTip"></div>
                                <div class="triangle-cover"></div>
                            </div>
                        </Transition> -->
                    </div>
                    <div class="account-box icon-box" @keyup.esc.stop="accountMouseleave" @keydown.tab.stop="handleAccountFocusout" @mouseenter="accountMouseenter" @mouseleave="accountMouseleave">
                        <nuxt-link
                            :to="isLogin ? localePath({ name: 'my-account' }) : localePath({ name: 'login', query: { redirect: $route.fullPath } })"
                            class="account-top"
                            tag="span"
                            tabindex="0"
                            @keyup.enter.native="accountMouseenter"
                            @click.native="topClick(isLogin ? 'Account-top' : 'Sign In Top')">
                            <template v-if="isLogin">
                                <!-- <img class="account_svg" src="https://resource.fs.com/mall/generalImg/20240521161918v8h8as.svg" /> -->
                                <img class="account_svg" src="https://resource.fs.com/mall/generalImg/202502251426447a3wek.svg" />
                            </template>
                            <template v-else>
                                <span class="iconfont iconfont-account">&#xe679;</span>
                            </template>

                            <!-- <span class="top-info extra-label" v-if="!isLogin">
                                <span class="top-info-login" v-if="website !== 'cn'">{{ $c("components.smallComponents.pHeader.sign_short") }}</span>
                            </span>
                            <span class="top-info extra-label" v-if="isLogin">
                                <span class="top-info-login" v-if="website !== 'cn'">{{ $c("components.smallComponents.newPHeader.account") }}</span>
                            </span> -->
                            <!-- <Transition>
                                <span class="triangle-up" v-if="show_account_box"></span>
                                <span class="triangle-up-line" v-if="show_account_box"></span>
                            </Transition> -->
                        </nuxt-link>
                        <Transition>
                            <div class="account-main" v-show="show_account_box">
                                <div class="account-ctn" v-if="false">
                                    <div class="welcome_wrap" v-if="isLogin">
                                        <dl class="welcome-box">
                                            <dt v-if="!['cn', 'hk', 'tw', 'mo'].includes(website)">
                                                {{ $c("components.smallComponents.newPHeader.welcome").replace("xxx", userInfo ? userInfo.customers_firstname : "") }}
                                            </dt>
                                            <dt v-else>{{ $c("components.smallComponents.newPHeader.welcome").replace("xxx", userInfo ? userInfo.customers_lastname + userInfo.customers_firstname : "") }}</dt>
                                            <!-- <dd v-if="companyInfo && companyInfo.companyName">{{ companyInfo.companyName }}</dd> -->
                                        </dl>
                                        <div v-if="userInfo.isCompanyOrganizationUser" class="apply_busy apply_busy_font">
                                            {{ $c("components.smallComponents.newPHeader.businessAccountFont").replace("XXX", companyInfo && companyInfo.companyName ? companyInfo.companyName : "") }}
                                        </div>

                                        <div v-if="userInfo.isCompanyOrganizationUser" class="apply_busy add_team_font" @click="showPopupLink">{{ $c("components.smallComponents.newPHeader.addTeamMember") }}</div>
                                        <div v-else class="apply_busy">
                                            <p>{{ $c("components.smallComponents.newPHeader.shoppBusiness") }}</p>
                                            <a href="javascript:;" tabindex="0" class="apply_busy_link" @click="gaEventAccount('Business Account Entrance', 1)">
                                                <span>{{ $c("components.smallComponents.newPHeader.certifyBusiness") }}</span> <i class="icon iconfont">&#xe703;</i>
                                            </a>
                                        </div>
                                    </div>
                                    <div class="account-service-box">
                                        <div class="service-item" v-for="(item, index) in menuMixin" :key="index">
                                            <nuxt-link v-if="item.path" tabindex="0" :to="localePath({ path: item.path })" class="service-info-box" @click.native="gaEventAccount(item.eventLabel)">
                                                <!-- <template v-if="isLogin">
                                                    <img class="noti_svg" v-if="item.name === 'notifications'" src="https://resource.fs.com/mall/generalImg/20240321145423o49bw3.svg" />
                                                    <i v-else class="iconfont" v-html="item.icon"></i>
                                                </template> -->
                                                <div class="service-info">{{ item.label }}</div>
                                            </nuxt-link>
                                        </div>
                                    </div>
                                    <div class="logout-box" v-if="isLogin">
                                        <fs-button type="gray" tabindex="0" @click="logout" :loading="logout_loading" :style="{ width: `100%` }">{{ $c("components.smallComponents.newPHeader.signOut") }}</fs-button>
                                    </div>
                                    <div class="login-box" v-else>
                                        <fs-button type="red" tabindex="0" @click="topClick('Sign In'), toLogin()" :style="{ width: `100%` }"> {{ $c("components.smallComponents.newPHeader.sign") }} </fs-button>
                                        <fs-button type="gray" tabindex="0" class="create_btn" :style="{ width: `100%` }" @click="gaEventRegist">{{ $c("components.smallComponents.newPHeader.createAccount") }}</fs-button>
                                    </div>
                                </div>
                                <div class="new-account-wrap">
                                    <div class="menu-box" v-if="isLogin">
                                        <div class="welcome_wrap">
                                            <p v-if="!['cn', 'hk', 'tw', 'mo'].includes(website)">
                                                {{ $c("pages.UserInvitation.userInvitation.title1").replace("xxxx", userInfo ? userInfo.customers_firstname : "") }}
                                            </p>
                                            <p v-else>{{ $c("pages.UserInvitation.userInvitation.title1").replace("xxxx", userInfo ? userInfo.customers_lastname + userInfo.customers_firstname : "") }}</p>
                                        </div>
                                        <div class="menu-list-box">
                                            <div class="menu-item" v-for="(item, index) in newMenuMixin" :key="index">
                                                <a v-if="item.path" tabindex="0" :href="localePath({ path: item.path })" class="service-info-box" @click.stop="gaEventAccount(item.eventLabel)">
                                                    <div class="menu-text">{{ item.label }}</div>
                                                </a>
                                            </div>
                                            <!-- 退出登录 -->
                                            <div class="menu-item-line"></div>
                                            <div class="menu-item sign-out">
                                                <a class="service-info-box">
                                                    <div class="menu-text" @click="logout">{{ $c("components.smallComponents.newPHeader.signOut") }}</div>
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="login-box" v-else>
                                        <fs-button type="red" tabindex="0" @click="topClick('Sign In'), toLogin()" :style="{ width: `100%` }"> {{ $c("components.smallComponents.newPHeader.sign") }} </fs-button>
                                        <fs-button type="gray" tabindex="0" class="create_btn" :style="{ width: `100%` }" @click="gaEventRegist">{{ $c("components.smallComponents.newPHeader.signUp") }}</fs-button>
                                    </div>
                                </div>
                                <div class="triangleTip"></div>
                                <div class="triangle-cover"></div>
                            </div>
                        </Transition>
                    </div>
                </div>
            </div>
        </nav>
        <transition name="fade">
            <div class="header-mask" :class="{ fix_top: fixTopStatus }" :style="{ top: `${mask_top}px` }" v-show="menu_active > 0 || searchClick" @click.stop="clickSearchIcon(true)"></div>
            <!-- <div class="header-mask" :style="{ top: `${mask_top}px` }" v-show="menu_active > 0 || searchClick" @click.stop="clickSearchIcon(true)"></div> -->
        </transition>
        <SearchRe :top="mask_top" :show="searchClick" @close="clickSearchIcon(false)" />
        <LinkUserPopup :show="isShowLink" appendBody @close="closePopup"></LinkUserPopup>
    </div>
</template>

<script>
import PcChangeWebsite from "@/components/ChangeWebsite/PcChangeWebsite"
import FsSelect from "@/components/FsSelect/FsSelect.vue"
import FsButton from "@/components/FsButton/FsButton.vue"
import FsTextTag from "@/components/FsTag/FsTextTag"
import FsPopover from "@/components/FsPopover/index"
import FsSelectSearch from "@/components/FsSelectSearch"
import HeaderHoliday from "./Holiday.vue"
import SearchRe from "./SearchRe.vue"
import { mapState, mapMutations, mapGetters, mapActions } from "vuex"
import { getElementTop, liveChat, removeLabel, debounce, searchBuried, setCookieOptions } from "@/util/util.js"
import { isObject } from "@/util/types"
import AES from "@/util/AES.js"
import dataMixin from "../dataMixin"
import menuDataMixin from "@/components/AccountMenus/menuDataMixin"
import Notifications from "./Notifications.vue"
import HandleLink from "@/components/HandleLink/HandleLink"
import { accessibleFocusout } from "@/util/accessible.js"
import fixScroll from "@/util/fixScroll.js"
import socialLink from "@/constants/socialLinkNewIcon.js"
import LinkUserPopup from "../../AccountMenus/LinkUser/LinkUserPopup.vue"
export default {
    name: "PFsHeader",
    components: {
        PcChangeWebsite,
        FsSelect,
        FsButton,
        FsPopover,
        FsTextTag,
        HeaderHoliday,
        Notifications,
        FsSelectSearch,
        HandleLink,
        SearchRe,
        LinkUserPopup,
    },
    mixins: [dataMixin, menuDataMixin],

    data() {
        return {
            xe623: require("@/assets/svg/xe623.svg"),
            xe621: require("@/assets/svg/xe621.svg"),
            select_country_loading: false,
            show_country: false,
            show_contact: false,
            checkout_loading: false,
            search_inp: "",
            menu_active: 0,
            category_active_index: 0,
            show_search_result: false,
            show_cart_list: false,
            cart_loading: false,
            cart_request: false,
            search_list: [],
            // select_language_currency: "",
            logout_loading: false,
            mask_top: 111,
            menu_slide: 1,
            menu_side_left: 0,
            // 新增内容
            rotateType: false,
            page: 1,
            hotList: [],
            history_list: [],
            showListenType: 0,
            loading: false,
            timeout: null,
            is_en: true,
            search_page_state: 1,
            // rotaWord: '',
            hotNum: 0,
            timer: null,
            allMenuLeft: [],
            mouseTime: null, //顶部导航时间函数
            //
            solutions_active_index: 0,
            solutionsNvaList: [
                {
                    title: this.$c("components.smallComponents.solutions.Industry.title"),
                    cate: [
                        {
                            title: this.$c("components.smallComponents.solutions.Industry.one.title"),
                            href: "/solutions_industries/Education-Industry-Solutions-61.html",
                            list: [
                                {
                                    title: this.$c("components.smallComponents.solutions.Industry.one.list.one"),
                                    href: "/solutions/Campus-VoIP-communication-solution-12.html",
                                    state: 0,
                                },
                                {
                                    title: this.$c("components.smallComponents.solutions.Industry.one.list.three"),
                                    href: "/solutions/University-data-center-network-solution-16.html",
                                    state: 0,
                                },
                                {
                                    title: this.$c("components.smallComponents.solutions.Industry.one.list.four"),
                                    href: "/solutions/Campus-all-optical-network-solution-38.html",
                                    state: 0,
                                },
                                {
                                    title: this.$c("solutions.CmsSolutions.headerPushEntry.digitalCampusConstructionSolution.title"),
                                    href: "/solutions/digital-campus-construction-solution-10003.html",
                                    state: 1,
                                },
                                {
                                    title: this.$c("components.smallComponents.solutions.Industry.one.list.two"),
                                    href: "/solutions/Video-Surveillance-Campus-solution-999.html",
                                    state: 1,
                                },
                            ],
                        },
                        {
                            title: this.$c("components.smallComponents.solutions.Industry.two.title"),
                            href: "/solutions_industries/Small-Business-Networking-Solutions-13.html",
                            list: [
                                {
                                    title: this.$c("components.smallComponents.solutions.Industry.two.list.one"),
                                    href: "/solutions/Private-Cloud-Solutions-for-Small-Businesses-Explore-OEM-Solutions-8.html",
                                    state: 0,
                                },
                                {
                                    title: this.$c("components.smallComponents.solutions.Industry.two.list.two"),
                                    href: "/solutions/Internet-Office-Network-Solution-47.html",
                                    state: 2,
                                },
                                {
                                    title: this.$c("components.smallComponents.solutions.Industry.two.list.three"),
                                    href: "/solutions/All-optical-network-solutions-for-high-rise-office-70.html",
                                    state: 0,
                                },
                                {
                                    title: this.$c("components.smallComponents.solutions.Industry.two.list.four"),
                                    href: "/solutions/VoIP-Solution-for-Branches-of-Business-97.html",
                                    state: 0,
                                },
                                {
                                    title: this.$c("components.smallComponents.solutions.Industry.two.list.five"),
                                    href: "/solutions/Network-Traffic-Monitoring-Solution-for-Medium-and-Large-Enterprises-95.html",
                                    state: 0,
                                },
                            ],
                        },
                        {
                            title: this.$c("components.smallComponents.solutions.Industry.three.title"),
                            href: "/solutions_industries/Hospitality-Networking-Solutions-133.html",
                            list: [
                                {
                                    title: this.$c("components.smallComponents.solutions.Industry.three.list.one"),
                                    href: "/solutions/VoIP-Solution-for-Hotel-96.html",
                                    state: 1,
                                },
                                {
                                    title: this.$c("components.smallComponents.solutions.Industry.three.list.two"),
                                    href: "/solutions/Smart-Hotel-Network-Solution-102.html",
                                    state: 0,
                                },
                                {
                                    title: this.$c("components.smallComponents.solutions.Industry.three.list.three"),
                                    href: "/solutions/Smart-Hotel-Wireless-Network-Solution-9.html",
                                    state: 0,
                                },
                                {
                                    title: this.$c("components.smallComponents.solutions.Industry.three.list.four"),
                                    href: "/solutions/Smart-Hotel-Video-Surveillance-Solution-18.html",
                                    state: 0,
                                },
                                {
                                    title: this.$c("components.smallComponents.solutions.Industry.three.list.five"),
                                    href: "/solutions/All-optical-network-coverage-solution-for-hotel-style-apartments-68.html",
                                    state: 0,
                                },
                            ],
                        },
                        {
                            title: this.$c("components.smallComponents.solutions.Industry.four.title"),
                            href: "/solutions_industries/Media-and-Entertainment-Solutions-85.html",
                            list: [
                                {
                                    title: this.$c("components.smallComponents.solutions.Industry.four.list.one"),
                                    href: "/solutions/Bar-Security-Video-Surveillance-Solution-6.html",
                                    state: 0,
                                },
                                {
                                    title: this.$c("components.smallComponents.solutions.Industry.four.list.two"),
                                    href: "/solutions/Amusement-Parks-Resumption-Video-Surveillance-Solution-100.html",
                                    state: 0,
                                },
                                // {
                                //     title: this.$c("components.smallComponents.solutions.Industry.six.list.one"),
                                //     href: "/solutions/Large-Shopping-Mall-Wireless-Network-Solution-13.html",
                                //     state: 0,
                                // },
                                // {
                                //     title: this.$c("components.smallComponents.solutions.Industry.six.list.two"),
                                //     href: "/solutions/Smart-Supermarket-and-Retailer-Video-Surveillance-Solution-5.html",
                                //     state: 0,
                                // },
                            ],
                        },
                        {
                            title: this.$c("components.smallComponents.solutions.Industry.five.title"),
                            href: "",
                            list: [
                                // {
                                //     title: this.$c("components.smallComponents.solutions.Industry.seven.list.one"),
                                //     href: "/solutions/VoIP-Solutions-for-Governments-14.html",
                                //     state: 0,
                                // },
                                {
                                    title: this.$c("components.smallComponents.solutions.Industry.five.list.one"),
                                    href: "/solutions/10g-dual-fiber-multi-point-flexible-expansion-solution-10135.html",
                                    state: 1,
                                },
                                {
                                    title: this.$c("components.smallComponents.solutions.Industry.five.list.two"),
                                    href: "/solutions/Metro-automated-transmission-platform-solution-56.html",
                                    state: 0,
                                },
                            ],
                        },
                        {
                            title: this.$c("components.smallComponents.solutions.Industry.six.title"),
                            href: "/solutions_industries/Retail-Industry-Solutions-49.html",
                            list: [
                                {
                                    title: this.$c("components.smallComponents.solutions.Industry.six.list.one"),
                                    href: "/solutions/Large-Shopping-Mall-Wireless-Network-Solution-13.html",
                                    state: 0,
                                },
                                {
                                    title: this.$c("components.smallComponents.solutions.Industry.six.list.two"),
                                    href: "/solutions/Smart-Supermarket-and-Retailer-Video-Surveillance-Solution-5.html",
                                    state: 0,
                                },
                            ],
                        },
                        {
                            title: this.$c("components.smallComponents.solutions.Industry.seven.title"),
                            href: "/solutions_industries/Government-Network-Solutions-73.html",
                            list: [
                                {
                                    title: this.$c("components.smallComponents.solutions.Industry.seven.list.one"),
                                    href: "/solutions/VoIP-Solutions-for-Governments-14.html",
                                    state: 0,
                                },
                            ],
                        },
                        {
                            title: this.$c("components.smallComponents.solutions.Industry.eight.title"),
                            href: "",
                            list: [
                                {
                                    title: this.$c("components.smallComponents.solutions.Industry.eight.list.one"),
                                    href: "/solutions/automation-system-network-solution-10001.html",
                                    state: 1,
                                },
                            ],
                        },
                    ],
                },
                {
                    title: this.$c("components.smallComponents.solutions.Scenario.title"),
                    cate: [
                        {
                            title: this.$c("components.smallComponents.solutions.Scenario.one.title"),
                            href: "/solutions_industries/IDC-EDC-Solutions-25.html",
                            list: [
                                {
                                    title: this.$c("components.smallComponents.solutions.Scenario.one.list.one"),
                                    href: "/solutions/CDN-Data-Center-Network-Solution---Reprint-94.html",
                                    state: 0,
                                },
                                {
                                    title: this.$c("components.smallComponents.solutions.Scenario.one.list.two"),
                                    href: "/solutions/Network-Visibility-Solution-in-Data-Centers-10.html",
                                    state: 0,
                                },
                                {
                                    title: this.$c("components.smallComponents.solutions.Scenario.one.list.three"),
                                    href: "/solutions/All-electric-access-data-center-network-solution-93.html",
                                    state: 0,
                                },
                                {
                                    title: this.$c("components.smallComponents.solutions.Scenario.one.list.four"),
                                    href: "/solutions/Data-Center-Structured-Cabling-Solution-75.html",
                                    state: 0,
                                },
                                {
                                    title: this.$c("solutions.CmsSolutions.headerPushEntry.fourOneNetworkUpgrading.title"),
                                    href: this.$c("solutions.CmsSolutions.headerPushEntry.fourOneNetworkUpgrading.href"),
                                    state: 1,
                                },
                                {
                                    title: this.$c("components.smallComponents.solutions.Scenario.one.list.five"),
                                    href: "/solutions/25G-100G-Data-Center-Network-Solution-11.html",
                                    state: 2,
                                },
                            ],
                        },
                        {
                            title: this.$c("components.smallComponents.solutions.Scenario.two.title"),
                            href: "",
                            list: [
                                {
                                    title: this.$c("components.smallComponents.solutions.Scenario.two.list.one"),
                                    href: "/solutions/High-Capacity-OTN-Solutions-for-Data-Center-40.html",
                                    state: 0,
                                },
                                {
                                    title: this.$c("components.smallComponents.solutions.Scenario.two.list.three"),
                                    href: "/solutions/Interconnect-Solution-for-Small-and-Medium-Sized-Data-Centers-46.html",
                                    state: 0,
                                },
                                {
                                    title: this.$c("components.smallComponents.solutions.Scenario.two.list.four"),
                                    href: "/solutions/Multi-service-hybrid-access-transport-solution-69.html",
                                    state: 0,
                                },
                                {
                                    title: this.$c("components.smallComponents.solutions.Scenario.two.list.five"),
                                    href: "/solutions/Long-Haul-and-Fully-Convergent-Solutions-for-Data-Centers-23.html",
                                    state: 0,
                                },
                            ],
                        },
                        {
                            title: this.$c("components.smallComponents.solutions.Scenario.three.title"),
                            href: "",
                            list: [
                                {
                                    title: this.$c("components.smallComponents.solutions.Scenario.three.list.one"),
                                    href: "/solutions/Enterprises-Security-Video-Surveillance-Solution-19.html",
                                    state: 0,
                                },
                                {
                                    title: this.$c("components.smallComponents.solutions.Scenario.three.list.two"),
                                    href: "/solutions/Family-Villa-Video-Surveillance-Solutions-4.html",
                                    state: 0,
                                },
                                {
                                    title: this.$c("components.smallComponents.solutions.Scenario.three.list.three"),
                                    href: "/solutions/Outdoor-smart-parking-solution-45.html",
                                    state: 0,
                                },
                                {
                                    title: this.$c("components.smallComponents.solutions.Scenario.three.list.four"),
                                    href: "/solutions/Industrial-Park-Security-Video-Surveillance-Solution-17.html",
                                    state: 0,
                                },
                                {
                                    title: this.$c("components.smallComponents.solutions.Scenario.three.list.five"),
                                    href: "/solutions/Internet-building-security-monitoring-solutions-74.html",
                                    state: 0,
                                },
                            ],
                        },
                        {
                            title: this.$c("components.smallComponents.solutions.Scenario.four.title"),
                            href: "",
                            list: [
                                {
                                    title: this.$c("components.smallComponents.solutions.Scenario.four.list.one"),
                                    href: "/solutions/Enterprise-Wi-Fi-6-Network-Solution-1.html",
                                    state: 2,
                                },
                                {
                                    title: this.$c("components.smallComponents.solutions.Scenario.four.list.two"),
                                    href: "/solutions/Campus-Wireless-Network-Solution-101.html",
                                    state: 0,
                                },
                                {
                                    title: this.$c("components.smallComponents.solutions.Scenario.four.list.three"),
                                    href: "/solutions/Wisdom-Villa-Wi-Fi-Coverage-Network-Solution-20.html",
                                    state: 0,
                                },
                                {
                                    title: this.$c("components.smallComponents.solutions.Scenario.four.list.four"),
                                    href: "/wireless_product_selector.html",
                                    state: 1,
                                },
                            ],
                        },
                    ],
                },
            ],
            triangleUpStyle: {},
            triangleUpContactStyle: {},
            productLeftNavStyle: {},
            solutionsLeftNavStyle: {},
            saleOnline: true,
            nav_timer: null,
            show_account_box: false,
            chunkPurpleStyle: {
                width: "60px",
            },
            isCompanyShowSearch: false,
            searchClick: false,
            solutionsNvaListNew: [{ title: "", list: [] }],
            solutionDataNew: {},
            menu_data_new: [],
            socialLink: socialLink,
            isShowLink: false,
            fixTopStatus: false,
            needHrefBlank: ["community.fs.com", "developer.fs.com"],
            env: process?.env?.ENV || "",
        }
    },
    inject: ["fsLiveChat"],
    computed: {
        ...mapState({
            isLogin: (state) => state.userInfo.isLogin,
            userInfo: (state) => state.userInfo.userInfo,
            cartList: (state) => state.cart.list,
            cartData: (state) => state.cart.cartData,
            savedCarts: (state) => state.userInfo.savedCarts,
            activeQuotes: (state) => state.userInfo.activeQuotes,
            menu_data: (state) => state.category.menu_data,
            classify_data: (state) => state.category.classify_data,
            classify_new_data: (state) => state.category.classify_new_data,
            top_data: (state) => state.category.top_data,
            iso_code: (state) => state.webSiteInfo.iso_code,
            country_name: (state) => state.webSiteInfo.country_name,
            qty: (state) => state.cart.qty,
            language: (state) => state.webSiteInfo.language,
            symbol: (state) => state.webSiteInfo.symbol,
            email: (state) => state.category.email,
            website: (state) => state.webSiteInfo.website,
            pageGroup: (state) => state.ga.pageGroup,
            currency: (state) => state.webSiteInfo.currency,
            domain: (state) => state.meta.domain,
            list: (state) => state.cart.list,
            noReplyNum: (state) => state.liveChat.noReplyNum,
            warehouse: (state) => state.webSiteInfo.warehouse,
            isMobile: (state) => state.device.isMobile,
        }),
        ...mapGetters({
            gaLoginString: "userInfo/gaLoginString",
            isRussia: "webSiteInfo/isRussia",
            isCn: "webSiteInfo/isCn",
            isJp: "webSiteInfo/isJp",
            isJpEn: "webSiteInfo/isJpEn",
        }),
        formattDay() {
            let nowDay = new Date().getDay()
            return [0, 6].includes(nowDay)
        },
        companyInfo() {
            return this.userInfo?.companyInfo
        },
        isCompanyAccount() {
            return this.userInfo?.isCompanyOrganizationUser
        },
        isAccountPage() {
            const accountPageList = this.filterMenuMixin.map((i) => [...i.activeList, ...i.boldList])?.flat() || []
            const path = this.$route.path
            return accountPageList?.some((i) => path.includes(i))
        },
        //  是企业账户且页面属于账户中心
        isCompanyAndAccountPage() {
            return this.isCompanyAccount && this.isAccountPage
        },
        logoClassName() {
            let className = ""
            //这里有一个需求点是只有账户中心的页面才展示企业的logo
            const isAccountPage = this.isAccountPage
            if (this.isCn) {
                className = this.isCompanyAccount && isAccountPage ? "logo-company-cn" : "logo-cn"
            } else {
                className = this.isCompanyAccount && isAccountPage ? "logo-company" : ""
            }
            return `logo ${className}`
        },
    },
    created() {
        // this.initSolutionNav()
        this.initSolutionNavNew()
        console.log(this.solutionsNvaList, "solutionsNvaList")
        if (this.menu_data && this.menu_data.length) {
            this.menu_data_new = [this.menu_data[0], this.menu_data[1], ...this.classify_new_data]
        }
        // 优先展示hot、new、再其他，最多8个
        // this.processSolutionNav()
    },
    mounted() {
        this.isFixTop()
        console.log(this.classify_new_data, "classify_new_data", this.menu_data_new)
        if (window.localStorage && window.localStorage.getItem("noReplyNum") > 0) {
            let num = window.localStorage.getItem("noReplyNum")
            this.$store.commit("liveChat/setNoReplyNum", num * 1)
        }
        this.isSearchParams()
        // this.getHotSearch()
        this.getHistory()
        document.addEventListener("click", this.handleOtherClick)
        console.log(this.classify_data, "classify_data")

        this.triangleUpStyle = {
            left: 300 + 100 - this.$refs["rightBox"].clientWidth + (this.website === "de" ? this.$refs["thrutedBox"].clientWidth + 41 : 0) + Math.floor(this.$refs["rightCountry"].clientWidth / 2) + "px",
        }
        this.triangleUpContactStyle = {
            right: Math.floor(this.$refs["rightContact"].clientWidth / 2) + "px",
        }

        this.setProductNavStyle("category_active_index", this.category_active_index)
        this.setSolutionsNavStyle("solutions_active_index", this.solutions_active_index)
        this.computedChunkPurpleStyle()
    },
    beforeDestroy() {
        document.removeEventListener("click", this.handleOtherClick)
        window.removeEventListener("resize", this.resize)
        clearInterval(this.timer)
        this.timer = null
    },
    methods: {
        bdClick(url) {
            if (url.includes("/product_and_project_inquiry.html")) {
                this.$bdRequest({
                    conversionTypes: [
                        {
                            logidUrl: location.href,
                            newType: 20,
                        },
                    ],
                })
            }
        },
        isFixTop() {
            const url = window.location.href
            const regex = /\/products\/|\/specials\/|\/solutions\/|\/case-study\/|\/products_support.html/
            if (!regex.test(url)) {
                this.fixTopStatus = true
            } else {
                const filenames = ["2000.html", "2001.html", "2002.html", "2003.html", "2004.html", "2005.html", "2006.html", "2007.html"]
                this.fixTopStatus = filenames.some((filename) => url.includes(filename))
            }
        },
        closePopup() {
            this.isShowLink = false
        },
        salesClick() {
            this.$bdRequest({
                conversionTypes: [
                    {
                        logidUrl: location.href,
                        newType: 27,
                    },
                ],
            })
        },
        telClick() {
            this.$bdRequest({
                conversionTypes: [
                    {
                        logidUrl: location.href,
                        newType: 2,
                    },
                ],
            })
        },
        showPopupLink() {
            this.gaFunctin("link_user", "Confirm Submit")
            this.isShowLink = true
        },
        computedChunkPurpleStyle() {
            const callBack = () => {
                const width = window.innerWidth
                let val = 60
                if (width <= 1264) {
                    val = (Math.pow(width, 2) * 0.0404) / 1264
                    if (val < 39.5) {
                        val = 39.5
                    }
                }
                this.chunkPurpleStyle = {
                    width: `${val}px`,
                }
            }
            callBack()
            window.addEventListener("resize", callBack)
            this.$once("hook:beforeDestroy", () => {
                window.removeEventListener("resize", callBack)
            })
        },

        initSolutionNav() {
            return
            if (this.warehouse === "US") {
                // 40
                this.solutionsNvaList[1].cate[1].list[0].title = this.$c("solutions.HighCapacityOTN.navData.children[0].tit")
                this.solutionsNvaList[1].cate[1].list[0].state = 1
            }

            // 1000
            this.solutionsNvaList[1].cate[0].list.unshift(
                {
                    title: this.$c("solutions.CloudDataCenterVxLAN.navData.children[0].tit"),
                    href: this.$c("solutions.CloudDataCenterVxLAN.navData.children[0].href"),
                    state: 1,
                },
                {
                    title: this.$c("solutions.TelecomRoomsCablingUpgradingSolution.navData.children[3].tit"),
                    href: this.$c("solutions.TelecomRoomsCablingUpgradingSolution.navData.children[3].href"),
                    state: 1,
                }
            )
            // 1012
            // this.solutionsNvaList[1].cate[0].list.push({
            //     title: this.$c("solutions.TelecomRoomsCablingUpgradingSolution.navData.children[1].tit"),
            //     href: this.$c("solutions.TelecomRoomsCablingUpgradingSolution.navData.children[1].href"),
            //     state: 1,
            // })

            if (this.website !== "cn") {
                this.solutionsNvaList[0].cate[1].list.push({
                    title: this.$c("solutions.CampusBranch.headTop.name"),
                    href: this.$c("solutions.CampusBranch.headTop.href"),
                    state: 1,
                })
            }
            // 1001
            this.solutionsNvaList[0].cate[1].list.push({
                title: this.$c("solutions.MediumAndLargeCampusWirelessNetwork.navData.children[5].tit"),
                href: this.$c("solutions.MediumAndLargeCampusWirelessNetwork.navData.children[5].href"),
                state: 1,
            })

            if (this.warehouse === "US") {
                // 1002
                this.solutionsNvaList[1].cate[0].list.unshift({
                    title: this.$c("solutions.DCI.navData.children[0].tit"),
                    href: this.$c("solutions.DCI.navData.children[0].href"),
                    state: 1,
                })
            }

            // 1003
            this.solutionsNvaList[1].cate[0].list.unshift({
                title: this.$c("solutions.TwoTierNetworkArchitecture.navData.children[0].tit"),
                href: this.$c("solutions.TwoTierNetworkArchitecture.navData.children[0].href"),
                state: 1,
            })

            // 1004
            if (this.website === "uk") {
                this.solutionsNvaList[1].cate[1].list.push({
                    title: this.$c("solutions.OpticalCommunicationNetworkForSmartRailways.navData.children[5].tit"),
                    href: this.$c("solutions.OpticalCommunicationNetworkForSmartRailways.navData.children[5].href"),
                    state: 1,
                })
            }

            // 1007 1014 1015
            this.solutionsNvaList[1].cate[3].list.unshift(
                {
                    title: this.$c("solutions.Wireless5G.navData.children[0].tit"),
                    href: this.$c("solutions.Wireless5G.navData.children[0].href"),
                    state: 1,
                },
                {
                    title: this.$c("solutions.WirelessFronthaulOpticalTransceiverModuleSolution4G.navData.children[1].tit"),
                    href: this.$c("solutions.WirelessFronthaulOpticalTransceiverModuleSolution4G.navData.children[1].href"),
                    state: 1,
                },
                {
                    title: this.$c("solutions.ConvergedWirelessFronthaulOpticalTransceiverModuleSolution4G5G.navData.children[2].tit"),
                    href: this.$c("solutions.ConvergedWirelessFronthaulOpticalTransceiverModuleSolution4G5G.navData.children[2].href"),
                    state: 1,
                }
            )

            // 10028
            this.solutionsNvaList[0].cate[1].list.push({
                title: this.$c("solutions.SOHOOffice.navData.children[6].tit"),
                href: this.$c("solutions.SOHOOffice.navData.children[6].href"),
                state: 1,
            })
            // 10042
            this.solutionsNvaList[0].cate[1].list.push({
                title: this.$c("solutions.SOHOOffice.navData.children[7].tit"),
                href: this.$c("solutions.SOHOOffice.navData.children[7].href"),
                state: 1,
            })

            // 1009
            this.solutionsNvaList[0].cate[1].list.push({
                title: this.$c("solutions.VideoSurveillanceSmallMidShoppingMall.navData.children[0].tit"),
                href: this.$c("solutions.VideoSurveillanceSmallMidShoppingMall.navData.children[0].href"),
                state: 1,
            })

            if (this.warehouse === "US") {
                // 1010
                this.solutionsNvaList[0].cate[4].list.push({
                    title: this.$c("solutions.PhysicalLayerFiber.navData.children[3].tit"),
                    href: this.$c("solutions.PhysicalLayerFiber.navData.children[3].href"),
                    state: 1,
                })
            }

            //1013
            this.solutionsNvaList[0].cate[1].list.push({
                title: this.$c("solutions.MediumLarge10GigabitCampusNetworkSolution.navData.children[6].tit"),
                href: this.$c("solutions.MediumLarge10GigabitCampusNetworkSolution.navData.children[6].href"),
                state: 0,
            })

            // 10021 10023
            this.solutionsNvaList[0].cate[1].list.push(...this.$c("solutions.CmsSolutions.headerPushEntry.enterpeiseType"))
            //10017 1016 1017
            this.$c("solutions.EnterpriseVideoSurveillance.navData.children").forEach((item, index) => {
                if (index >= 5) {
                    let obj = {
                        title: item.tit,
                        href: item.href,
                        state: 0,
                    }
                    ;[5, 8, 9, 10].includes(index) ? (obj.state = 1) : ""

                    if (index === 10) {
                        this.solutionsNvaList[1].cate[2].list.unshift(obj)
                    } else {
                        this.solutionsNvaList[1].cate[2].list.push(obj)
                    }
                }
            })
            // 10002
            this.solutionsNvaList[0].cate[4].list.push({
                title: this.$c("solutions.CmsSolutions.headerPushEntry.cloudDataCenterUpgrade.title"),
                href: this.$c("solutions.CmsSolutions.headerPushEntry.cloudDataCenterUpgrade.href"),
                state: 1,
            })
            // 10006
            this.solutionsNvaList[1].cate[0].list.unshift({
                title: this.$c("solutions.CmsSolutions.headerPushEntry.HighComputingNetworkSolution.title"),
                href: this.$c("solutions.CmsSolutions.headerPushEntry.HighComputingNetworkSolution.href"),
                state: 1,
            })
            // 10011
            this.solutionsNvaList[1].cate[0].list.unshift({
                title: this.$c("solutions.CmsSolutions.headerPushEntry.iPSANStorageNetworkingSolution.title"),
                href: this.$c("solutions.CmsSolutions.headerPushEntry.iPSANStorageNetworkingSolution.href"),
                state: 1,
            })
            // 10013
            this.solutionsNvaList[1].cate[0].list.unshift({
                title: this.$c("solutions.CmsSolutions.headerPushEntry.securityResourcePoolSolution.title"),
                href: this.$c("solutions.CmsSolutions.headerPushEntry.securityResourcePoolSolution.href"),
                state: 1,
            })
            // 10014
            this.solutionsNvaList[1].cate[0].list.unshift({
                title: this.$c("solutions.CmsSolutions.headerPushEntry.dataCenterCDNSolution.title"),
                href: this.$c("solutions.CmsSolutions.headerPushEntry.dataCenterCDNSolution.href"),
                state: 1,
            })
            // 10004
            this.solutionsNvaList[1].cate[0].list.push({
                title: this.$c("solutions.CmsSolutions.headerPushEntry.eightDataCenter.title"),
                href: this.$c("solutions.CmsSolutions.headerPushEntry.eightDataCenter.href"),
                state: 1,
            })
            // 1011 修改为 10012
            this.solutionsNvaList[1].cate[0].list.push({
                title: this.$c("solutions.CmsSolutions.headerPushEntry.fourDaterCenter.title"),
                href: this.$c("solutions.CmsSolutions.headerPushEntry.fourDaterCenter.href"),
                state: 1,
            })
            // 1006
            this.solutionsNvaList[1].cate[0].list.push({
                title: this.$c("solutions.Transmission400G.navData.children[0].tit"),
                href: this.$c("solutions.Transmission400G.navData.children[0].href"),
                state: 1,
            })
            // 10005
            this.solutionsNvaList[1].cate[0].list.push({
                title: this.$c("solutions.CmsSolutions.headerPushEntry.oneDataCenter.title"),
                href: this.$c("solutions.CmsSolutions.headerPushEntry.oneDataCenter.href"),
                state: 1,
            })
            // 1005
            this.solutionsNvaList[1].cate[0].list.push({
                title: this.$c("solutions.Transmission100G.navData.children[0].tit"),
                href: this.$c("solutions.Transmission100G.navData.children[0].href"),
                state: 1,
            })
            // 10020 10019 10016 10027
            this.solutionsNvaList[1].cate[1].list.unshift(...this.$c("solutions.CmsSolutions.headerPushEntry.otnType.head"))
            // 10022
            this.solutionsNvaList[1].cate[1].list.push(...this.$c("solutions.CmsSolutions.headerPushEntry.otnType.back"))
            // 10032
            this.solutionsNvaList[0].cate[2].list.push(...this.$c("solutions.CmsSolutions.headerPushEntry.hospitalityType.back"))
            // 游戏数据中心专题页
            this.solutionsNvaList[0].cate[3].list.push({
                title: this.$c("solutions.GamingDataCenter.headTop.name"),
                href: this.$c("solutions.GamingDataCenter.headTop.href"),
                state: 1,
            })
            // 视频放送专题页
            this.solutionsNvaList[0].cate[3].list.push({
                title: this.$c("solutions.VideoStreaming.headTop.name"),
                href: this.$c("solutions.VideoStreaming.headTop.href"),
                state: 1,
            })
            //10010
            this.solutionsNvaList[0].cate[3].list.push({
                title: this.$c("solutions.CmsSolutions.headerPushEntry.outdoorWirelessNetwork.title"),
                href: "/solutions/outdoor-wireless-network-connectivity-solutions-10010.html",
                state: 1,
            })
            // 10033
            this.solutionsNvaList[0].cate[5].list.push(...this.$c("solutions.CmsSolutions.headerPushEntry.retailType.back"))
            // 10035 10036
            this.solutionsNvaList[1].cate[0].list.unshift(...this.$c("solutions.CmsSolutions.headerPushEntry.dataCenterType.head"))
            this.solutionsNvaList[1].cate[0].list.unshift({
                title: this.$c("solutions.AutomatedDrivingSolution.headTop.name"),
                href: this.$c("solutions.AutomatedDrivingSolution.headTop.href"),
                state: 1,
            })
            this.solutionsNvaList[1].cate[0].list.unshift({
                title: this.$c("solutions.CablingSystem.headTop.name"),
                href: this.$c("solutions.CablingSystem.headTop.href"),
                state: 1,
            })
            this.solutionsNvaList[1].cate[0].list.unshift({
                title: this.$c("solutions.CablingSystem.headTop.name"),
                href: this.$c("solutions.CablingSystem.headTop.href"),
                state: 1,
            })
            if (this.website !== "cn") {
                this.solutionsNvaList[1].cate[0].list.unshift({
                    title: this.$c("solutions.InfiniBandSolutions.headTop.name"),
                    href: this.$c("solutions.InfiniBandSolutions.headTop.href"),
                    state: 1,
                })
            }
            this.solutionsNvaList[1].cate[0].list.push(...this.$c("solutions.CmsSolutions.headerPushEntry.dataCenterType.back"))
            // 10041
            this.solutionsNvaList[0].cate[0].list.unshift(...this.$c("solutions.CmsSolutions.headerPushEntry.highEducation.head"))
        },

        initSolutionNavNew() {
            if (this.classify_data && this.classify_data.length > 0) {
                const solutionData = this.classify_data[0]
                if (solutionData.hasOwnProperty("solution_data_new") || solutionData.hasOwnProperty("solutionDataNew")) {
                    this.solutionDataNew = solutionData.solution_data_new || solutionData.solutionDataNew
                    this.solutionsNvaListNew = (solutionData.solution_data_new || solutionData.solutionDataNew).list
                }
            }
            console.log(this.solutionsNvaListNew, "solutionsNvaListNew")
        },
        processSolutionNav() {
            this.solutionsNvaList.forEach((item) => {
                item.cate.forEach((val) => {
                    let len = val.list.length
                    for (let i = 0; i < len - 1; i++) {
                        for (let j = 0; j < len - 1 - i; j++) {
                            if (val.list[j].state < val.list[j + 1].state) {
                                let temp = val.list[j]
                                val.list[j] = val.list[j + 1]
                                val.list[j + 1] = temp
                            }
                        }
                    }
                    val.list = val.list.slice(0, 8)
                })
            })
        },
        ...mapMutations({
            showLoading: "cart/showLoading",
            hideLoading: "cart/hideLoading",
            setCartData: "cart/setCartData",
            setResourcePage: "device/setResourcePage",
        }),
        ...mapActions({
            getUserInfo: "userInfo/getUserInfo",
            getCart: "cart/getCart",
            // updateSiteInfo: "selectCountry/updateSiteInfo",
        }),
        noticeClick(url) {
            this.$bdRequest({
                conversionTypes: [
                    {
                        logidUrl: location.href,
                        newType: 2,
                    },
                ],
            })
            this.$router.push(this.localePath({ path: url }))
        },
        clearNavTimer() {
            clearTimeout(this.nav_timer)
            this.nav_timer = null
        },
        accountMouseenter(e) {
            // this.clearNavTimer()
            // this.nav_timer = setTimeout(() => {
            //     // this.searchClick = false
            //     this.show_account_box = true
            //     fixScroll.unfixed()
            // }, 0)
            this.show_account_box = true
            this.clearNavTimer()
            this.nav_timer = setTimeout(() => {
                // this.searchClick = false
                this.show_account_box = true
                // fixScroll.unfixed()
            }, 300)
        },
        accountMouseleave() {
            // this.clearNavTimer()
            // this.show_account_box = false
            this.show_account_box = false
            this.clearNavTimer()
            this.nav_timer = setTimeout(() => {
                this.show_account_box = false
            }, 300)
        },
        cartMouseenter() {
            this.show_cart_list = true
            this.clearNavTimer()
            this.nav_timer = setTimeout(() => {
                this.show_cart_list = true
                if (!this.cart_request && !this.cart_loading) {
                    this.cart_loading = true
                    this.getCart({
                        isOnlyFetchCount: 0,
                        cb: (p) => {
                            this.cart_loading = false
                            this.cart_request = true
                            if (p && p.data) {
                                let l = p.data.isLogin ? 1 : 0
                                if (this.isLogin !== l) {
                                    this.getUserInfo()
                                }
                            }
                        },
                    })
                }
                // this.searchClick = false
                // fixScroll.unfixed()
            }, 300)
        },
        cartMouseleave() {
            // this.clearNavTimer()
            // this.show_cart_list = false
            this.show_cart_list = false
            this.clearNavTimer()
            this.nav_timer = setTimeout(() => {
                this.show_cart_list = false
            }, 300)
        },
        cartClick() {
            // this.cartMouseleave()
            this.topClick("Cart")
        },
        urlClick(url) {
            if (url === "chat") {
                this.chat()
                if (window.dataLayer) {
                    window.dataLayer.push({
                        event: "uaEvent",
                        eventCategory: `${this.pageGroup}`,
                        eventAction: "top_navigation",
                        eventLabel: "Chat With Customer Service",
                        nonInteraction: false,
                    })
                }
            }
        },
        chat() {
            this.fsLiveChat()
        },
        topClick(url) {
            let str = ""
            if (url.includes("about_us")) {
                str = `About FS`
            } else if (url.includes("shipping_delivery")) {
                str = `Shipping Policy`
            } else if (url === "Logo") {
                str = `Logo`
            } else if (url === "Sign In" || url === "Sign In Top") {
                str = `Sign In`
            } else if (url === "Account" || url === "Account-top") {
                str = `Account`
            } else if (url === "Contact Sales") {
                str = "Contact Sales"
            } else if (url === "Cart") {
                str = "Cart"
            } else if (url === "Language_Unfold") {
                str = "Language_Unfold"
            } else if (url === "Save Country Setting") {
                str = "Save Country Setting"
            } else if (url.includes("Switch Language_")) {
                str = url
            }
            if (str && window.dataLayer) {
                window.dataLayer.push({
                    event: "uaEvent",
                    eventCategory: this.pageGroup,
                    eventAction: url === "Account" || url === "Sign In" ? "login_function" : "top_function",
                    eventLabel: str,
                    nonInteraction: false,
                })
            }
        },
        burryPoint(index, title, lTitle) {
            if (index === 4 && window.dataLayer) {
                window.dataLayer.push({
                    event: "uaEvent",
                    eventCategory: "Navigation Module",
                    eventAction: "AboutUs_Module",
                    eventLabel: `${title}_${lTitle}`,
                    nonInteraction: false,
                })
            }
        },
        gaEventRegist() {
            if (window.dataLayer) {
                window.dataLayer.push({
                    event: "uaEvent",
                    eventCategory: `${this.pageGroup}_Top Login`,
                    eventAction: "login_function",
                    eventLabel: "Create an account",
                    nonInteraction: false,
                })
            }
            this.$router.push(this.localePath({ path: "/register.html" }))
        },
        toLogin() {
            this.$router.push(this.localePath({ name: "login", query: { redirect: this.$route.fullPath } }))
        },
        gaFunctin(eventAction, eventLabel) {
            window.dataLayer &&
                window.dataLayer.push({
                    event: "uaEvent",
                    eventCategory: this.pageGroup,
                    eventAction,
                    eventLabel,
                    nonInteraction: false,
                })
        },
        gaEventAccount(s, flag = false) {
            if (window.dataLayer) {
                window.dataLayer.push({
                    event: "uaEvent",
                    eventCategory: `${this.pageGroup}_Top Login`,
                    eventAction: "login_function",
                    eventLabel: s,
                    nonInteraction: false,
                })
            }
            if (flag) {
                this.$router.push(this.localePath({ path: "/business_account.html" }))
            }
        },
        gaEventCart(t, item, i) {
            if (window.dataLayer) {
                window.dataLayer.push({
                    event: "uaEvent",
                    eventCategory: `${this.pageGroup}_Top Cart`,
                    eventAction: "product_detail_entrance",
                    eventLabel: `${t}_${item.products_id}`,
                    proPosition: i + 1,
                    proId: item.products_id,
                    proName: `${item.products_info ? item.products_info.productName.substr(0, 100) : ""}`,
                    proCategoryFirstlevel: `${
                        item.products_info && item.products_info.categories_info && item.products_info.categories_info.length && item.products_info.categories_info[0] && item.products_info.categories_info[0].name
                            ? item.products_info.categories_info[0] && item.products_info.categories_info[0].name
                            : ""
                    }`,
                    proCategorySecondlevel: `${
                        item.products_info && item.products_info.categories_info && item.products_info.categories_info.length && item.products_info.categories_info[1] && item.products_info.categories_info[1].name
                            ? item.products_info.categories_info[1] && item.products_info.categories_info[1].name
                            : ""
                    }`,
                    proCategoryThirdlevel: `${
                        item.products_info && item.products_info.categories_info && item.products_info.categories_info.length && item.products_info.categories_info[2] && item.products_info.categories_info[2].name
                            ? item.products_info.categories_info[2] && item.products_info.categories_info[2].name
                            : ""
                    }`,
                    nonInteraction: false,
                })
            }
        },
        gaEventToCart() {
            if (window.dataLayer) {
                window.dataLayer.push({
                    event: "uaEvent",
                    eventCategory: `${this.pageGroup}_Top Cart`,
                    eventAction: "view_cart",
                    eventLabel: "Top Cart_View Cart",
                    nonInteraction: false,
                })
            }
            this.cartMouseleave()
        },
        gaEventDeleteCart(i) {
            console.log(this.cartList[i].products_info.categories_info)
            if (window.dataLayer) {
                let cates = []
                let attrs = []
                if (this.cartList[i].products_info && this.cartList[i].products_info.categories_info && this.cartList[i].products_info.categories_info.length) {
                    this.cartList[i].products_info.categories_info.map((item, index) => {
                        if (index < 3) {
                            cates.push(item.name)
                        }
                    })
                }
                if (this.cartList[i].attributes && isObject(this.cartList[i].attributes)) {
                    for (let attr in this.cartList[i].attributes) {
                        if (attr === "length") {
                            if (this.cartList[i].attributes[attr].product_length_name) {
                                if (this.cartList[i].attributes[attr].product_length_name) {
                                    attrs.push(`${this.$c("pages.ShoppingCart.Length")}:${this.cartList[i].attributes[attr].product_length_ft_name}`)
                                }
                            }
                        } else {
                            if (this.cartList[i].attributes[attr].option_values && this.cartList[i].attributes[attr].option_values.length) {
                                let vals = []
                                for (let k = 0; k < this.cartList[i].attributes[attr].option_values.length; k++) {
                                    if (this.cartList[i].attributes[attr].option_values[k].attrType === "standard") {
                                        // attrs.push(`${this.cartList[i].attributes[attr].option_id_name}:${this.cartList[i].attributes[attr].option_values[k].products_options_values_name}`)
                                        vals.push(this.cartList[i].attributes[attr].option_values[k].products_options_values_name)
                                    } else if (this.cartList[i].attributes[attr].option_values[k].attrType === "text" || this.cartList[i].attributes[attr].option_values[k].attrType === "file") {
                                        vals.push(this.cartList[i].attributes[attr].option_values[k].products_options_value_text)

                                        // attrs.push(`${this.cartList[i].attributes[attr].option_id_name}:${this.cartList[i].attributes[attr].option_values[k].products_options_value_text}`)
                                    }
                                }
                                attrs.push(`${this.cartList[i].attributes[attr].option_id_name}:${vals.join(",")}`)
                            }
                        }
                    }
                }
                console.log(cates)
                let obj = {
                    event: "eeEvent",
                    eventCategory: `${this.pageGroup}_Top Cart`,
                    eventAction: "remove_from_cart",
                    eventLabel: `Remove_${this.cartList[i].qty}`,
                    nonInteraction: false,
                    ecommerce: {
                        currencyCode: `${this.currency}`,
                        remove: {
                            products: [
                                {
                                    id: `${this.cartList[i].products_id}`,
                                    name: `${this.cartList[i].products_info ? this.cartList[i].products_info.productName.substr(0, 100) : ""}`,
                                    price: `${this.cartList[i].products_info ? parseFloat(this.cartList[i].products_info.finalPriceExchangeRate).toFixed(2) : ""}`,
                                    position: i + 1,
                                    category: cates.join("_"),
                                    quantity: `${this.cartList[i].qty}`,
                                },
                            ],
                        },
                    },
                }
                if (attrs && attrs.length) {
                    obj["ecommerce"]["remove"]["products"][0]["variant"] = attrs.join("|")
                }
                window.dataLayer.push(obj)
            }
        },
        menuMouseenter(index) {
            const rect = this.$refs.menuList.getBoundingClientRect()
            const distanceToTop = rect.top
            console.log("this.$refs.menuList", this.$refs.menuList, distanceToTop)
            this.clearNavTimer()
            this.nav_timer = setTimeout(() => {
                this.menu_active = index
                this.mask_top = this.$refs.menuList ? (this.fixTopStatus ? distanceToTop + 68 + 1 : getElementTop(this.$refs.menuList) + 68 + 1) : 0
                this.searchClick = false
                fixScroll.unfixed()
            }, 0)
        },
        menuListMouseleave() {
            this.clearNavTimer()
            setTimeout(() => {
                this.menu_active = 0
            }, 30)
        },
        menuleftMouseenter(attr, index) {
            this[attr] = index
            this.setProductNavStyle(attr, index)
            this.setSolutionsNavStyle(attr, index)
        },
        setSolutionsNavStyle(attr, index) {
            if (attr === "solutions_active_index") {
                let El = this.$refs[`solutions-left-nav-${index}`]
                let clientHeight = El ? El[0].clientHeight : 48
                let offsetTop = El ? El[0].offsetTop : 0
                this.solutionsLeftNavStyle = {
                    top: `${offsetTop ? offsetTop : index * 48}px`,
                    height: `${clientHeight ? clientHeight : 48}px`,
                }
            }
        },
        setProductNavStyle(attr, index) {
            if (attr === "category_active_index") {
                let El = this.$refs[`products-left-nav-${index}`]
                let clientHeight = El ? El[0].clientHeight : 48
                let offsetTop = El ? El[0].offsetTop : 0
                this.productLeftNavStyle = {
                    top: `${offsetTop ? offsetTop : index * 48}px`,
                    height: `${clientHeight ? clientHeight : 48}px`,
                }
            }
        },
        hideMenumore(item) {
            console.log(item)
            this.menu_active = 0
            if (item && item.url && window.dataLayer) {
                let t = ""
                if (!item.children) {
                    if (item.is_has_hot_products || item.is_has_new_products || item.tag === 1 || item.tag === 3) {
                        if (item.is_has_hot_products || item.tag === 3) {
                            t = "_Hot"
                        } else if (item.is_has_new_products || item.tag === 1) {
                            t = "_NEW"
                        }
                    } else {
                        t = "_Normal"
                    }
                }

                window.dataLayer.push({
                    event: "uaEvent",
                    eventCategory: this.pageGroup,
                    eventAction: "top_navigation",
                    eventLabel: `${this.domain}${this.$handleLink(item.url).url}${t}`,
                    nonInteraction: false,
                })
            }
        },
        gaEventFirst(item) {
            if (item && item.url && window.dataLayer) {
                window.dataLayer.push({
                    event: "uaEvent",
                    eventCategory: this.pageGroup,
                    eventAction: "top_navigation",
                    eventLabel: `${this.$handleLink(item.url).url}`,
                    nonInteraction: false,
                })
            }
        },
        clearSearch() {
            // this.search_inp = ""
            this.search_list = []
            // this.$refs.searchInput.focus()
            this.search_page_state = 1
        },

        countryTopMouseenter() {
            this.clearNavTimer()
            this.nav_timer = setTimeout(() => {
                this.show_country = true
                this.show_contact = false
                // this.searchClick = false
            }, 0)
        },
        countryTopMouseleave() {
            this.show_country = false
            this.clearNavTimer()
        },
        changeWebsiteFocus(areaName) {
            if (window.dataLayer) {
                window.dataLayer.push({
                    event: "uaEvent",
                    eventCategory: this.pageGroup,
                    eventAction: "top_function",
                    eventLabel: `Country Input_${areaName}`,
                    nonInteraction: false,
                })
            }
        },
        changeWebsiteClick() {
            if (window.dataLayer) {
                window.dataLayer.push({
                    event: "uaEvent",
                    eventCategory: this.pageGroup,
                    eventAction: "top_function",
                    eventLabel: "Continents_select",
                    nonInteraction: false,
                })
            }
        },
        changeWebsiteChange(countryLanguage) {
            if (window.dataLayer) {
                window.dataLayer.push({
                    event: "uaEvent",
                    eventCategory: this.pageGroup,
                    eventAction: "top_function",
                    eventLabel: `Switch_${countryLanguage}`,
                    nonInteraction: false,
                })
            }
        },
        changeWebsiteInput(areaName, value) {
            if (window.dataLayer) {
                window.dataLayer.push({
                    event: "uaEvent",
                    eventCategory: this.pageGroup,
                    eventAction: "top_function",
                    eventLabel: `Search_${areaName}_${value}`,
                    nonInteraction: false,
                })
            }
        },
        deleteCart(i) {
            let arr = []
            let o = {
                products_id: this.cartList[i].products_id,
                qty: this.cartList[i].qty,
                isChecked: this.cartList[i].isChecked,
            }
            arr.push(o)
            this.showLoading({ type: "head", index: i })
            this.$axios
                .post("/api/cart/delete", { products: arr })
                .then((res) => {
                    this.gaEventDeleteCart(i)
                    this.setCartData(res.data)
                    if (this.isLogin !== res.data.isLogin) {
                        this.getUserInfo()
                    }
                })
                .catch((err) => {
                    this.hideLoading({ type: "head", index: i })
                })
        },
        serviceTopMouseenter() {
            this.clearNavTimer()
            this.nav_timer = setTimeout(() => {
                this.show_country = false
                this.show_contact = true
                this.gaEventService("hover_Contact Us")
            }, 0)
        },
        serviceTopMouseleave() {
            this.clearNavTimer()
            this.show_contact = false
        },
        searchBlur() {
            // this.rotationWords()
            this.show_search_result = false
        },
        searchFocus(event) {
            this.show_search_result = true
            this.show_country = false
            searchBuried("focus", this.pageGroup)
        },
        handleOtherClick(e) {
            e.stopPropagation()
            if (!this.$refs.rightCountry.contains(e.target)) {
                this.show_country = false
            }
            if (e.target.className != "search-box" && !e.target.className.includes("fsSelectSearch__input")) {
                this.show_search_result = false
                this.handleCompanySearchClose()
            }
        },
        logout() {
            if (this.logout_loading) {
                return
            }
            this.logout_loading = true
            if (window.dataLayer) {
                window.dataLayer.push({
                    event: "uaEvent",
                    eventCategory: `${this.pageGroup}_Top Login`,
                    eventAction: "logout_button",
                    eventLabel: "Sign Out",
                    nonInteraction: false,
                    loginStatus: `Login_${this.gaLoginString}`,
                    userId: `${this.userInfo ? AES.encrypt(`${this.userInfo.customers_level}${this.userInfo.customers_id}`, `_-yu_xuan_3507-_`, `fs_com_phone2016`) : ""}`,
                })
            }

            this.$axios
                .post("/api/user/logout")
                .then((res) => {
                    this.logout_loading = false
                    this.$cookies.remove("token_new")
                    this.$cookies.remove("cartId")
                    this.getUserInfo()
                    this.getCart({ isOnlyFetchCount: 0 })
                    if (!/home/<USER>
                        this.$router.replace(this.localePath({ name: "home" }))
                    }
                })
                .catch((err) => {
                    this.logout_loading = false
                })
        },
        // 切换icon旋转效果
        rotate() {
            searchBuried("change", this.pageGroup)
            this.page += 1
            this.getHotSearch()
            this.rotateType = true
            setTimeout(() => {
                this.rotateType = false
            }, 1000)
        },
        // 请求热门关键词
        async getHotSearch() {
            const res = await this.$axios.get("/api/search/hotSearch", { params: { page: this.page } })
            this.hotList = res.data.hot_data
            this.is_en = res.data.is_en
            if (res.data.total_page == this.page) {
                this.page = 0
            }
            // this.rotationWords()
        },
        // 输入框改变请求
        change: debounce(
            async function () {
                if (!this.search_inp) return
                const res = await this.$axios.get("api/search/searchWords", { params: { keyword: removeLabel(this.search_inp) } })

                this.search_list = res.data.info
                this.show_search_result = this.search_list.length > 0
                this.search_page_state = res.data.page_state
            },
            100,
            false
        ),
        // 点击回车提交搜索
        submitSearch() {
            if (this.search_inp.replace(/\s+/g, "")) {
                searchBuried("button", this.pageGroup, this.search_inp)
                if (this.timeout) {
                    clearTimeout(this.timeout)
                }
                this.timeout = setTimeout(async () => {
                    // if (!this.search_inp) return
                    if (!this.search_inp && this.rotaWord) {
                        this.search_inp = this.rotaWord
                    }
                    if (removeLabel(this.search_inp) != this.$route.query.keyword) {
                        this.loading = true
                        this.getHistory()
                        this.show_search_result = false
                        this.$router.push(this.localePath({ name: "search_result", query: { keyword: removeLabel(this.search_inp) } }))
                    } else {
                        this.loading = true
                        // setTimeout(()=>{
                        //     this.loading = false
                        // },1000)
                        this.$router.go(0)
                    }
                }, 500)
            } else {
                this.$refs.searchInput.focus()
                // this.$router.go(0)
            }
        },
        // 新回车和点击搜索
        newSubmitSearch() {
            if (this.search_inp.replace(/\s+/g, "")) {
                searchBuried("button", this.pageGroup, this.search_inp)
                this.getHistory()
                if (this.search_inp != this.$route.query.keyword) {
                    let routeData = {}
                    routeData = this.$router.resolve(this.localePath({ name: "search_result", query: { keyword: removeLabel(this.search_inp) } }))
                    window.open(routeData.href, "_blank")
                } else {
                    this.loading = true
                    this.$router.go(0)
                }
            } else {
                this.$router.go(0)
            }
        },
        // 点击关键词搜索
        clickOption(n, t) {
            if (t == 1) {
                searchBuried("recommender", this.pageGroup, n)
            } else if (t == 2) {
                searchBuried("history", this.pageGroup, n)
            } else if (t == 3) {
                searchBuried("hot", this.pageGroup, n)
            }
            if (this.timeout) {
                clearTimeout(this.timeout)
            }
            this.loading = true
            if (n == this.$route.query.keyword) {
                this.$router.go(0)
            }
            this.timeout = setTimeout(async () => {
                this.search_inp = n
                this.getHistory(n)
                this.show_search_result = false
                // let routeData = {}
                // routeData = this.$router.resolve(this.localePath({ name: 'search_result', query: { keyword: removeLabel(n) } }));
                // window.open(routeData.href, '_blank');
                this.$router.push(this.localePath({ name: "search_result", query: { keyword: removeLabel(this.search_inp) } }))
            }, 500)
        },
        // 跳转到详情
        clickToDetail(id) {
            let routeData = {}
            routeData = this.$router.resolve(this.localePath({ name: "products", params: { id: id } }))
            window.open(routeData.href, "_blank")
        },
        // 获取历史记录
        getHistory(n) {
            if (localStorage.getItem("history") && JSON.parse(localStorage.getItem("history"))) {
                let local = JSON.parse(localStorage.getItem("history"))
                if (this.search_inp || n) {
                    if (local.indexOf(this.search_inp || n) == -1) {
                        if (local.length > 7) {
                            local.pop(local[0])
                            local.unshift(this.search_inp || n)
                        } else {
                            local.unshift(this.search_inp || n)
                        }
                    }
                }
                localStorage.setItem("history", JSON.stringify(local))
                this.history_list = local
            } else {
                localStorage.setItem("history", "[]")
                this.history_list = []
            }
        },
        // 删除搜索记录
        deleteSearch(n) {
            searchBuried("remove", this.pageGroup, n)
            if (JSON.parse(localStorage.getItem("history"))) {
                let local = JSON.parse(localStorage.getItem("history"))
                local = local.filter((item) => {
                    return item != n
                })
                localStorage.setItem("history", JSON.stringify(local))
                this.history_list = local
            }
        },
        // 关键字高亮
        highLight(txt) {
            if (!txt) {
                return ""
            }
            if (this.search_inp && this.search_inp.length > 0) {
                if (txt.indexOf(this.search_inp) != -1) {
                    // 匹配关键字正则
                    let replaceReg = new RegExp(this.search_inp, "g")
                    // 高亮替换v-html值
                    let replaceString = '<span style="background:#E5E5E5;">' + this.search_inp + "</span>"
                    // 开始替换
                    txt = txt.replace(replaceReg, replaceString)
                }
            }
            return txt
        },
        // 判断是否有搜索参数
        isSearchParams() {
            if (this.$route.path.substring(this.$route.path.lastIndexOf("/"), this.$route.path.length) == "/search_result") {
                this.search_inp = this.$route.query.keyword
            } else {
                this.search_inp = ""
            }
        },
        // 替换语言包中的变量
        subs(str, n, t) {
            if (t == 1) {
                return str.replace("xxxx", "<strong>" + n.products_id + "</strong>")
            } else if (t == 2) {
                return str.replace("xxxx", "<strong>" + this.search_inp + "</strong>").replace("yyyy", "<strong>" + n.products_id + "</strong>")
            }
        },
        // 热门搜索词轮播
        rotationWords() {
            let list = this.hotList.map((item) => {
                return item.name
            })
            if (this.hotNum > 0) {
                if (this.timer) {
                    clearInterval(this.timer)
                }
                this.timer = setInterval(() => {
                    if (this.hotNum < list.length) {
                        this.rotaWord = list[this.hotNum]
                        this.hotNum += 1
                        this.rotationWords()
                    } else {
                        this.hotNum = 0
                        this.rotationWords()
                    }
                }, 5000)
            } else {
                this.rotaWord = list[this.hotNum]
                this.hotNum += 1
                // this.rotationWords()
            }
        },
        // 跳转到购物车
        toShopCart() {
            this.gaEventToCart()
        },
        // 直接结算
        toCheck() {
            this.getUserInfo(() => {
                if (window.dataLayer) {
                    let arr = []
                    if (this.list && this.list.length) {
                        this.list.map((item, index) => {
                            if (item.isChecked) {
                                let variant = []
                                if (item.fs_cart_attr && item.fs_cart_attr.length) {
                                    item.fs_cart_attr.map((attr) => {
                                        variant.push(attr.replace(" - ", ":"))
                                    })
                                }
                                let cate = []
                                if (item.products_info.categories_info && item.products_info.categories_info.length) {
                                    item.products_info.categories_info.forEach((i) => {
                                        cate.push(i.name)
                                    })
                                    cate = cate.join("_")
                                }
                                arr.push({
                                    id: item.products_id,
                                    name: item.products_info.productName.substr(0, 100),
                                    price: item.products_info.finalPriceExchangeRate ? parseFloat(item.products_info.finalPriceExchangeRate).toFixed(2) : "",
                                    variant: variant.join("|"),
                                    position: index + 1,
                                    category: cate,
                                    quantity: item.qty,
                                })
                            }
                        })
                    }
                    console.log(arr, parseFloat(this.cartData.checkedPriceInfo.subTotalWithChecked.subTotalUsd).toFixed(2))
                    window.dataLayer.push({
                        event: "eeEvent",
                        eventCategory: `${this.pageGroup}_Top Cart`,
                        eventAction: "begin_checkout",
                        eventLabel: "Top Cart_Checkout",
                        eventValue: parseFloat(this.cartData.checkedPriceInfo.subTotalWithChecked.subTotalUsd).toFixed(2),
                        nonInteraction: false,
                        ecommerce: {
                            checkout: {
                                actionField: {
                                    step: 1,
                                    option: "",
                                },
                                products: arr,
                            },
                        },
                    })
                }
                if (window.yaCounter71412688) {
                    yaCounter71412688.reachGoal("checkout", function () {})
                }
                this.$bdRequest({
                    conversionTypes: [
                        {
                            logidUrl: location.href,
                            newType: 45,
                        },
                    ],
                })
                if (this.isLogin) {
                    if (this.cartData.loginType === "punchout") {
                        this.$axios
                            .get("/api/punchout/product")
                            .then((res) => {
                                let obj = {
                                    buyercookie: this.$cookies.get("punchout_buyer_cookie"),
                                    return_url: this.$cookies.get("punchout_return_url"),
                                    body: {
                                        items: res.data,
                                    },
                                }
                                let form = document.createElement("form")
                                form.action = this.$cookies.get("punchout_return_url")
                                form.method = "post"
                                form.style.display = "none"
                                let input = document.createElement("input")
                                input.type = "hidden"
                                input.name = "cartdata"
                                input.value = JSON.stringify(obj)
                                form.appendChild(input)
                                document.body.appendChild(form)
                                form.submit()
                            })
                            .catch((err) => {
                                alert(err.message)
                            })
                    } else {
                        // this.$router.push(this.localePath({ name: "confirm-order" }))
                        location.href = this.$localeLink("/confirm-order")
                    }
                } else {
                    if (this.cartData.loginType === "punchout") {
                        this.$router.push(this.localePath({ name: "home" }))
                    } else {
                        let fullpath = this.$route.fullPath
                        this.$router.push(this.localePath({ name: "login", query: { redirect: fullpath } }))
                    }
                }
            })
        },
        // 判断ru
        isRuNum(num) {
            if (this.website == "it") {
                if (num > 1) {
                    return num + " Articoli nel Carrello"
                } else {
                    return num + " Articolo nel Carrello"
                }
            } else if (this.website == "ru") {
                if (["11", "12", "13", "14"].indexOf(String(num)) != -1) {
                    return num + " товаров в корзине"
                } else if (num == 1 || String(num).slice(-1)[0] == 1) {
                    return num + " товар в корзине"
                } else if (["2", "3", "4"].indexOf(String(num)) != -1 || ["2", "3", "4"].indexOf(String(num).slice(-1)[0]) != -1) {
                    return num + " товара в корзине"
                } else if (["5", "6", "7", "8", "9"].indexOf(String(num)) != -1 || ["5", "6", "7", "8", "9"].indexOf(String(num).slice(-1)[0]) != -1) {
                    return num + " товаров в корзине"
                } else if (num == 0 || String(num).slice(-1)[0] == 0) {
                    return num + " товаров в корзине"
                }
            } else if (this.website == "fr") {
                if (num > 1) {
                    return num + " Articles dans le Panier"
                } else {
                    return num + " Article dans le Panier"
                }
            } else if (this.website == "es" || this.website == "mx") {
                if (num > 1) {
                    return num + " artículos en cesta"
                } else {
                    return num + " artículo en cesta"
                }
            } else {
                if (num > 1) {
                    return this.$c("pages.ShoppingCart.newCart.productsNum").replace("XXXX", num)
                } else {
                    return this.$c("pages.ShoppingCart.newCart.productNum").replace("XXXX", num)
                }
            }
        },
        // 设置来源页面类型
        resourcePage() {
            this.setResourcePage(1)
        },
        getNavGa(url, state = 0) {
            const stateList = ["Normal", "New", "Hot"]
            window.dataLayer &&
                window.dataLayer.push({
                    event: "uaEvent",
                    eventCategory: this.pageGroup,
                    eventAction: "top_navigation",
                    eventLabel: `${this.domain}${this.$handleLink(url).url}_${stateList[state]}`,
                    nonInteraction: false,
                })
        },
        // contact us
        gaEventService(label) {
            if (window.dataLayer) {
                window.dataLayer.push({
                    event: "uaEvent",
                    eventCategory: this.pageGroup,
                    eventAction: "top_function",
                    eventLabel: label,
                    nonInteraction: false,
                })
            }
        },
        feedbackShow() {
            this.$emit("feedbackShow")
        },
        handleServiceFocusout(event) {
            accessibleFocusout(event, this.show_contact, () => {
                this.serviceTopMouseleave()
            })
        },
        handleAccountFocusout(event) {
            accessibleFocusout(event, this.show_account_box, () => {
                this.accountMouseleave()
            })
        },
        handleCartFocusout(event) {
            accessibleFocusout(event, this.show_cart_list, () => {
                this.cartMouseleave()
            })
        },
        handleSearchTab(event) {
            accessibleFocusout(event, this.show_search_result, () => {
                this.show_search_result = false
            })
        },
        handleMenuTab(event) {
            accessibleFocusout(event, this.menu_active > 0, () => {
                this.menu_active = 0
            })
        },
        handleIconSearchClick() {
            console.log("handleIconSearchClick")
            this.isCompanyShowSearch = true
            setTimeout(() => {
                this.$refs.searchInput.focus()
            }, 300)
        },
        handleCompanySearchClose() {
            if (this.isCompanyAndAccountPage && this.isCompanyShowSearch) {
                this.isCompanyShowSearch = false
            }
        },
        // 点击搜索Icon
        clickSearchIcon(type) {
            const rect = this.$refs.menuList.getBoundingClientRect()
            const distanceToTop = rect.top
            this.mask_top = this.$refs.menuList ? (this.fixTopStatus ? distanceToTop + 68 + 1 : getElementTop(this.$refs.menuList) + 68 + 1) : 0
            console.log(type, "hide")
            if (type) {
                this.searchClick = false
            } else {
                this.searchClick = !this.searchClick
            }
            if (this.searchClick) {
                fixScroll.fixed()
            } else {
                fixScroll.unfixed()
            }
            console.log({
                event: "uaEvent",
                eventCategory: `${this.pageGroup}_Top Search`,
                eventAction: "click_search",
                eventLabel: "top search icon",
                nonInteraction: false,
            })
            if (window.dataLayer) {
                window.dataLayer.push({
                    event: "uaEvent",
                    eventCategory: `${this.pageGroup}_Top Search`,
                    eventAction: "click_search",
                    eventLabel: "top search icon",
                    nonInteraction: false,
                })
            }
        },
        // 定义一个函数，处理指定域名的a标签跳转打开新页面
        getHostNameTagATarget(urlString) {
            let target = "_self"
            try {
                const url = new URL(urlString)
                target = this.needHrefBlank.includes(url.hostname) ? "_blank" : "_self" // url.hostname; // 返回域名部分，例如 'www.example.com'
            } catch (e) {
                console.log("Invalid URL:", urlString)
            }
            return target
        },
    },
    watch: {
        $route(val) {
            this.loading = false
            if (val.path.substring(val.path.lastIndexOf("/"), val.path.length) == "/search_result") {
                this.search_inp = val.query.keyword
            } else {
                this.search_inp = ""
            }
        },
        search_inp(val) {
            if (val.length > 100) {
                this.search_inp = val.substr(0, 100)
            }
        },
        menu_active(val) {
            // console.log(val, this.menu_active === 0)
            if (val === 0) {
                // fixScroll.unfixed()
                // document.body.classList.remove("fixScroll")
                let element = document.querySelector(".more-right")
                if (element) {
                    element.scrollTop = 0
                }
            } else {
                // fixScroll.fixed()
                // document.body.classList.add("fixScroll")
                if (val === 1) {
                    this.category_active_index = 0
                    this.productLeftNavStyle = {
                        top: `0px`,
                        height: `48px`,
                    }
                } else {
                    // more-right
                    let element = document.querySelector(".more-right")
                    if (element) {
                        element.scrollTop = 0
                    }
                }
            }
            this.$forceUpdate()
        },
        category_active_index() {
            let element = document.querySelector(".more-right")
            if (element) {
                element.scrollTop = 0
            }
        },
    },
}
</script>

<style lang="scss" scoped>
@import "PFsHeaderNew";
</style>
