<template>
    <div class="m-account" :style="`top:${top}px`">
        <!-- <span @click="hideAccount" class="iconfont icon-close">&#xf30a;</span>
        <dl class="title">
            <dt>{{ $c("components.smallComponents.newPHeader.welcome").replace("xxx", userInfo ? userInfo.customers_firstname : "") }}</dt>
            <dd v-if="companyInfo && companyInfo.companyName">{{ companyInfo.companyName }}</dd>
            <div v-if="userInfo.isCompanyOrganizationUser" class="apply_busy apply_busy_font">
                {{ $c("components.smallComponents.newPHeader.businessAccountFont").replace("XXX", companyInfo && companyInfo.companyName ? companyInfo.companyName : "") }}
            </div>
            <div v-if="userInfo.isCompanyOrganizationUser" class="apply_busy add_team_font" @click="showPopupLink">{{ $c("components.smallComponents.newPHeader.addTeamMember") }}</div>
            <div v-else class="apply_busy">
                <p>{{ $c("components.smallComponents.newPHeader.shoppBusiness") }}</p>
                <a href="javascript:;" @click="gaEventAccount('Business Account Entrance', 1)"
                    ><span>{{ $c("components.smallComponents.newPHeader.certifyBusiness") }}</span
                    ><i class="icon iconfont">&#xe703;</i></a
                >
            </div>
        </dl>
        <div class="list">
            <template>
                <div v-for="(item, index) in menuMixin" :key="index">
                    <nuxt-link v-if="item.path" tag="div" @click.native="hideAccount(item)" :to="localePath({ path: item.path })" :class="['item', { hide: isCn && item.path === '/quote_history.html' }]">
                        <i v-if="isLogin" class="iconfont" v-html="item.icon"></i>
                        <span>{{ item.label }}</span>
                    </nuxt-link>
                    <div v-else class="item noti_item" @click="clickItem(item.name)">
                        <i v-if="isLogin" class="iconfont" v-html="item.icon"></i>
                        <span>{{ item.label }}</span>
                        <span class="dot" v-if="isLogin && isListUnRead"></span>
                    </div>
                </div>
            </template>
        </div>
        <div class="btn">
            <fs-button type="gray" @click="logout" :loading="logout_loading" :style="{ width: `100%` }">{{ $c("components.smallComponents.newPHeader.signOut") }}</fs-button>
        </div>
        <LinkUserPopup :show="optionType" @close="closePopup"></LinkUserPopup> -->
        <div class="menu-box" v-if="isLogin">
            <div class="welcome_wrap">
                <p v-if="!['cn', 'hk', 'tw', 'mo'].includes(website)">{{ $c("pages.UserInvitation.userInvitation.title").replace("xxxx ( aaaa)!", userInfo ? userInfo.customers_firstname : "") }}</p>
                <p v-else>{{ $c("pages.UserInvitation.userInvitation.title").replace("xxxx ( aaaa)!", userInfo ? userInfo.customers_lastname + userInfo.customers_firstname : "") }}</p>
            </div>
            <div class="menu-list-box">
                <div class="menu-item" v-for="(item, index) in newMenuMixin" :key="index">
                    <a v-if="item.path" tabindex="0" :href="localePath({ path: item.path })" class="service-info-box" @click.native="gaEventAccount(item.eventLabel)">
                        <div class="menu-text">{{ item.label }}</div>
                    </a>
                </div>
                <!-- 退出登录 -->
                <div class="menu-item">
                    <a class="service-info-box">
                        <div class="menu-text" @click="logout">{{ $c("components.smallComponents.newPHeader.signOut") }}</div>
                    </a>
                </div>
            </div>
        </div>
        <div class="login-box" v-else>
            <fs-button type="red" tabindex="0" @click="toLogin()" :style="{ width: `100%` }"> {{ $c("components.smallComponents.newPHeader.sign") }} </fs-button>
            <fs-button type="gray" tabindex="0" class="create_btn" :style="{ width: `100%` }" @click="gaEventRegist">{{ $c("components.smallComponents.newPHeader.signUp") }}</fs-button>
        </div>
    </div>
</template>

<script>
import AES from "@/util/AES.js"
import { mapState, mapMutations, mapGetters, mapActions } from "vuex"
import FsButton from "@/components/FsButton/FsButton.vue"
import LinkUserPopup from "../../AccountMenus/LinkUser/LinkUserPopup.vue"
import dataMixin from "../dataMixin"
import { setCookieOptions } from "@/util/util.js"

export default {
    name: "MAccount",
    data() {
        return {
            logout_loading: false,
            optionType: false,
        }
    },
    components: {
        FsButton,
        LinkUserPopup,
    },
    mixins: [dataMixin],
    props: {
        top: {
            type: Number,
            default: 82,
        },
    },
    computed: {
        ...mapState({
            userInfo: (state) => state.userInfo.userInfo,
            pageGroup: (state) => state.ga.pageGroup,
            isLogin: (state) => state.userInfo.isLogin,
            website: (state) => state.webSiteInfo.website,
            system_messages: (state) => state.userInfo.system_messages,
        }),
        ...mapGetters({
            // relation_list: "selectCountry/relation_list",
            gaLoginString: "userInfo/gaLoginString",
            isCn: "webSiteInfo/isCn",
        }),
        companyInfo() {
            return this.userInfo.companyInfo
        },
        isListUnRead() {
            return this.system_messages?.is_list_un_read === 1
        },
    },
    mounted() {},
    methods: {
        ...mapMutations({
            setCartData: "cart/setCartData",
        }),
        ...mapActions({
            getUserInfo: "userInfo/getUserInfo",
            getCart: "cart/getCart",
        }),
        closePopup() {
            this.optionType = false
        },
        showPopupLink() {
            this.gaFunctin("link_user", "Confirm Submit")
            this.optionType = true
        },
        toLogin() {
            if (window.dataLayer) {
                window.dataLayer.push({
                    event: "uaEvent",
                    eventCategory: this.pageGroup,
                    eventAction: "Sign In",
                    eventLabel: "Sign In",
                    nonInteraction: false,
                })
            }
            this.$router.push(this.localePath({ name: "login", query: { redirect: this.$route.fullPath } }))
        },
        gaEventRegist() {
            if (window.dataLayer) {
                window.dataLayer.push({
                    event: "uaEvent",
                    eventCategory: `${this.pageGroup}_Top Login`,
                    eventAction: "login_function",
                    eventLabel: "Create an account",
                    nonInteraction: false,
                })
            }
            this.$router.push(this.localePath({ path: "/register.html" }))
        },
        logout() {
            if (this.logout_loading) {
                return
            }
            this.logout_loading = true
            if (window.dataLayer) {
                window.dataLayer.push({
                    event: "uaEvent",
                    eventCategory: `${this.pageGroup}_Top Login`,
                    eventAction: "logout_button",
                    eventLabel: "Sign Out",
                    nonInteraction: false,
                    loginStatus: `Login_${this.gaLoginString}`,
                    userId: `${this.userInfo ? AES.encrypt(`${this.userInfo.customers_level}${this.userInfo.customers_id}`, `_-yu_xuan_3507-_`, `fs_com_phone2016`) : ""}`,
                })
            }
            this.$axios
                .post("/api/user/logout")
                .then((res) => {
                    this.hideAccount()
                    this.logout_loading = false
                    this.$cookies.remove("token_new")
                    this.getUserInfo()
                    this.getCart()
                    if (!/home/<USER>
                        this.$router.replace(this.localePath({ name: "home" }))
                    }
                })
                .catch((err) => {
                    this.hideAccount()
                    this.logout_loading = false
                })
        },
        hideAccount(item) {
            if (item) {
                this.gaEventAccount(item.eventLabel)
            }
            this.$emit("close")
        },
        gaEventAccount(s, flag = false) {
            if (window.dataLayer) {
                window.dataLayer.push({
                    event: "uaEvent",
                    eventCategory: `${this.pageGroup}_Top Login`,
                    eventAction: "login_function",
                    eventLabel: s,
                    nonInteraction: false,
                })
            }
            if (flag) {
                this.$emit("close")
                this.$router.push(this.localePath({ path: "/business_account.html" }))
            }
        },
        clickItem(type) {
            this.$emit("expand", type)
        },
        gaFunctin(eventAction, eventLabel) {
            window.dataLayer &&
                window.dataLayer.push({
                    event: "uaEvent",
                    eventCategory: this.pageGroup,
                    eventAction,
                    eventLabel,
                    nonInteraction: false,
                })
        },
    },
}
</script>

<style lang="scss" scoped>
.m-account {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    background: #fff;
    z-index: 101;
    box-shadow: 0px -2px 2px 0px rgba(0, 0, 0, 0.03);
    width: 288px;
    overflow: hidden;
    padding: 20px 16px 0;
    .menu-box {
        .welcome_wrap {
            text-align: center;
            padding-bottom: 16px;
            border-bottom: 1px solid #e5e5e5;
            p {
                @include font14;
                color: $textColor1;
                font-weight: 600;
            }
        }
        .menu-list-box {
            margin-top: 8px;
            .menu-item {
                display: flex;
                align-items: center;
                justify-content: center;
                padding: 7px;
                .service-info-box {
                    display: flex;
                    cursor: pointer;
                    @include font13;
                    align-items: center;
                    // font-weight: 600;
                    text-decoration: none;
                    color: #707070;
                }
            }
        }
    }
    .login-box {
        display: flex;
        flex-direction: column;
        gap: 8px;
        .fs-button {
            border-radius: 4px;
            height: 36px;
            @include font13;
            font-weight: 600;
        }
        .fs-button-gray {
            color: #707070;
            background: #f2f2f2;
            &:hover {
                color: #19191a;
                background: linear-gradient(0deg, rgba(25, 25, 26, 0.04), rgba(25, 25, 26, 0.04)), #f2f2f2;
            }
        }
    }
    .icon-close {
        position: absolute;
        top: 24px;
        right: 16px;
        font-size: 16px;
        width: 16px;
        height: 16px;
        cursor: pointer;
        color: $textColor1;
    }

    .title {
        word-break: break-word;
        margin-bottom: 12px;
        dt {
            @include font16;
            color: $textColor1;
            font-weight: 600;
        }
        dd {
            @include font12();
            color: #19191a;
            margin-top: 8px;
        }
    }
    .apply_busy {
        @include font12;
        color: $textColor3;
        margin-top: 8px;
        p {
            margin-bottom: 4px;
        }
        .iconfont {
            @include font12;
            margin-left: 4px;
        }
    }
    .apply_busy_font {
        color: $textColor1;
    }
    .add_team_font {
        color: $textColor6;
        &:hover {
            text-decoration: underline;
            cursor: pointer;

            > span {
                &::after {
                    display: block;
                }
            }
        }
    }
    .list {
        margin-bottom: 28px;
        padding-bottom: 12px;
        border-bottom: 1px solid #e5e5e5;
        .item {
            display: flex;
            align-items: center;
            @include font14();
            color: #19191a;
            padding: 12px 0;
            cursor: pointer;
            &.hide {
                display: none;
            }
            .iconfont {
                margin-right: 8px;
            }
            &.noti_item {
                position: relative;
            }
            .dot {
                position: absolute;
                top: 15px;
                left: 10px;
                width: 4px;
                height: 4px;
                border-radius: 8px;
                background-color: #c00000;
            }
            > span {
                &:hover {
                    text-decoration: underline;
                }
            }
        }
    }
    .btn {
        .fs-button {
            border-radius: 21px;
        }
    }
}
</style>
