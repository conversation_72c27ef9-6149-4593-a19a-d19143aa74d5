<template>
    <div class="header-holiday" v-if="isShowHoliday">
        <div class="main">
            <p class="pc-label label">
                <img src="https://resource.fs.com/mall/generalImg/20250421150241sdnzef.svg" class="notice-img" />
                <span>{{ holidayContent }}</span>
                <span @click.stop="closeHoliday" class="iconfont">&#xf30a;</span>
            </p>
            <div class="m-label label">
                <!-- <img src="https://img-en.fs.com/includes/templates/fiberstore/images/prompt_icon.svg" class="notice-img" /> -->
                <img src="https://resource.fs.com/mall/generalImg/20250421150241sdnzef.svg" class="notice-img" />
                <FsMarquee>{{ holidayContent }}</FsMarquee>
                <span @click.stop="closeHoliday" class="iconfont">&#xf30a;</span>
            </div>
        </div>
    </div>
</template>

<script>
import { mapState, mapMutations } from "vuex"
import FsMarquee from "@/components/FsMarquee/FsMarquee"
import { setCookieOptions } from "@/util/util"

export default {
    name: "holiday",
    components: { FsMarquee },
    data() {
        return {
            hasHoliday: true,
            holidayContent: "",
        }
    },
    computed: {
        ...mapState({
            website: (state) => state.webSiteInfo.website,
        }),
        isShowHoliday() {
            return this.hasHoliday && this.holidayContent
        },
    },
    watch: {
        isShowHoliday() {
            this.setIsShowHoliday(this.isShowHoliday)
        },
    },
    methods: {
        ...mapMutations({
            setIsShowHoliday: "holiday/setIsShowHoliday",
        }),
        closeHoliday() {
            const holidayCookieValue = {
                tips: this.holidayContent,
                show: "no",
            }
            this.$cookies.set("holiday_new" + this.website, holidayCookieValue)
            this.hasHoliday = false
        },
    },
    created() {
        if (process.client) {
            let currentPath = this.$route.name
            if (/^home/g.test(currentPath) || /^shopping-cart/g.test(currentPath) || /^products/g.test(currentPath) || /^confirm-order/g.test(currentPath)) {
                //只在首页展示holiday
                this.$axios
                    .get("/api/message/noticeMessageIndex")
                    .then((res) => {
                        if (res.data) {
                            this.holidayContent = res.data.content
                            const hasHolidayCookie = this.$cookies.get("holiday_new" + this.website)
                            console.log("hasHolidayCookie", hasHolidayCookie)
                            if (hasHolidayCookie) {
                                if (hasHolidayCookie.show == "yes") {
                                    this.holidayContent = res.data.content
                                } else {
                                    this.holidayContent = ""
                                }
                            } else {
                                const holidayCookieValue = {
                                    tips: res.data.content,
                                    show: "yes",
                                }
                                this.$cookies.set("holiday_new" + this.website, holidayCookieValue)
                                this.holidayContent = res.data.content
                            }
                        }
                    })
                    .catch((error) => {
                        console.log(error)
                    })
            }
        }
    },
}
</script>

<style lang="scss" scoped>
.header-holiday {
    // background-color: #444;
    background-color: #fff;
    min-height: 46px;
    display: flex;
    position: relative;
    z-index: 1;
    box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.1);
    .main {
        width: 84vw;
        max-width: 1420px;
        margin: 0 auto;
        position: relative;
        display: flex;
        align-items: center;
        @include newPcHeaderWidthBox();
    }
    .notice-img {
        width: 18px;
        height: 18px;
        margin-top: 2.5px;
        margin-right: 8px;
    }
    .label {
        flex: 1;
        overflow: hidden;
        @include font13;
        color: $textColor3;
        line-height: 46px;
        // margin: 0 8px;
    }
    .pc-label {
        line-height: 23px;
        display: flex;
        span {
            &:not(.iconfont) {
                flex: 1;
            }
        }
    }
    .m-label {
        display: none;
    }
    .iconfont {
        font-size: 20px;
        width: 20px;
        height: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-left: 8px;
        margin-top: 5px;
        color: $textColor3;
        cursor: pointer;
    }
    @media (max-width: 1200px) {
        .main {
            width: 94vw;
        }
    }
    @include mediaIpad {
        line-height: auto;
        height: auto;
        .main {
            width: 100%;
            padding: 8px 16px;
        }
        .m-label {
            display: flex;
            // display: block;
        }
        .pc-label {
            display: none;
        }
        .label {
            @include font13;
        }
        .notice-img {
            margin-top: 0;
        }
        .iconfont {
            width: 13px;
            height: 13px;
            font-size: 13px;
        }
    }
}
</style>
