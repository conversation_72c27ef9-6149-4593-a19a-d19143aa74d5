<template>
    <div class="m-side-bar" :style="{ top: `${top}px` }">
        <div class="content">
            <client-only>
                <div class="side-bar-box">
                    <div class="side-bar-list" v-if="category_data.data && category_data.data.length">
                        <div class="side-bar-item" v-for="item in category_data.data" :key="item.categories_id" @click.stop="levelClick1('category', item)">
                            <a href="javascript:;" class="next-btn">
                                <p class="label" v-html="item.name"></p>
                                <span class="iconfont icon-next">&#xe704;</span>
                            </a>
                        </div>
                    </div>
                    <p class="line"></p>
                    <div class="side-bar-list" v-if="classify_data && classify_data.length">
                        <div class="side-bar-item" v-for="(item, i) in classify_data" :key="item.title" @click.stop="helpShow(item, i)">
                            <a href="javascript:;" class="next-btn">
                                <p class="label" v-html="item.title || ''"></p>
                                <span class="iconfont icon-next">&#xe704;</span>
                            </a>
                        </div>
                    </div>
                    <p class="line"></p>
                    <div class="side-bar-list">
                        <nuxt-link
                            class="side-bar-item"
                            :to="localePath({ name: 'contact-us' })"
                            @click.native="
                                hideExpand()
                                gaEvent('top_navigation', 'Contact Us')
                            ">
                            <div class="link-btn">
                                <span class="iconfont icon-prefix">&#xe67e;</span>
                                <p class="label">{{ $c("components.FsContactUs.btn") }}</p>
                            </div>
                        </nuxt-link>
                        <!-- <div class="side-bar-item">
                            <m-change-website styleType="sidebar"></m-change-website>
                        </div> -->
                    </div>
                </div>
                <!-- 正常的classify_data -->
                <transition name="slide-right">
                    <div class="level-page" v-if="help_data">
                        <div class="side-bar-box">
                            <div class="back" @click.stop="helpHide">
                                <span class="iconfont">&#xe702;</span>
                                <p class="back-label">{{ help_data && help_data.title ? help_data.title : "" }}</p>
                            </div>
                            <div class="side-bar-box" v-if="help_data.children && help_data.children.length">
                                <template v-for="(item, itemIndex) in help_data.children">
                                    <div class="side-bar-list" :key="item.title">
                                        <p class="side-bar-title side-bar-item" v-if="item.title">{{ item.title }}</p>
                                        <template v-for="citem in item.children">
                                            <a :key="citem.title" href="javascript:;" class="side-bar-item next-btn" @click="urlClick(citem.url)" v-if="['chat', 'bug'].includes(citem.url)">
                                                <p class="label" v-html="citem.title"></p>
                                                <span class="iconfont icon-next">&#xe704;</span>
                                            </a>
                                            <nuxt-link
                                                :key="citem.title"
                                                @click.native="hideExpand($handleLink(citem.url).url)"
                                                class="side-bar-item link-btn"
                                                v-if="!['chat', 'bug'].includes(citem.url) && $handleLink(citem.url).type === 1"
                                                :target="citem.tag === 2 ? '_blank' : '_self'"
                                                :to="localePath({ path: citem.url == 'solution_design.html' ? $handleLink(citem.url).url + '?resource=1' : $handleLink(citem.url).url })">
                                                <p class="label" v-html="citem.title"></p>
                                                <!-- <span class="iconfont icon-next">&#xe703;</span> -->
                                            </nuxt-link>
                                            <a
                                                :key="citem.title"
                                                :target="citem.tag === 2 ? '_blank' : '_self'"
                                                @click.stop="hideExpand($handleLink(citem.url).url)"
                                                class="side-bar-item link-btn"
                                                v-if="!['chat', 'bug'].includes(citem.url) && $handleLink(citem.url).type === 2"
                                                :href="$handleLink(citem.url).url">
                                                <p class="label" v-html="citem.title"></p>
                                                <!-- <span class="iconfont icon-next">&#xe703;</span> -->
                                            </a>
                                        </template>
                                        <!-- <div class="side-bar-item" v-for="citem in item.children" :key="citem.title">
                                            <a href="javascript:;" class="next-btn" @click="urlClick(citem.url)" v-if="['chat', 'bug'].includes(citem.url)">
                                                <p class="label" v-html="citem.title"></p>
                                                <span class="iconfont icon-next">&#xe703;</span>
                                            </a>
                                            <nuxt-link
                                                @click.native="hideExpand($handleLink(citem.url).url)"
                                                class="link-btn"
                                                v-if="!['chat', 'bug'].includes(citem.url) && $handleLink(citem.url).type === 1"
                                                :target="citem.tag === 2 ? '_blank' : '_self'"
                                                :to="localePath({ path: citem.url == 'solution_design.html' ? $handleLink(citem.url).url + '?resource=1' : $handleLink(citem.url).url })">
                                                <p class="label" v-html="citem.title"></p>
                                            </nuxt-link>
                                            <a
                                                :target="citem.tag === 2 ? '_blank' : '_self'"
                                                @click.stop="hideExpand($handleLink(citem.url).url)"
                                                class="link-btn"
                                                v-if="!['chat', 'bug'].includes(citem.url) && $handleLink(citem.url).type === 2"
                                                :href="$handleLink(citem.url).url">
                                                <p class="label" v-html="citem.title"></p>
                                            </a>
                                        </div> -->
                                    </div>
                                    <div class="line" v-if="itemIndex !== help_data.children.length - 1" :key="'line' + itemIndex"></div>
                                </template>
                            </div>
                        </div>
                    </div>
                </transition>
                <!-- 非正常的classify_data  -Solutions -->
                <transition name="slide-right">
                    <div class="level-page" v-if="help_data_solution">
                        <div class="side-bar-box">
                            <div class="back" @click.stop="helpHideSolution">
                                <span class="iconfont">&#xe702;</span>
                                <p class="back-label">{{ help_data_solution && help_data_solution.name ? help_data_solution.name : "" }}</p>
                            </div>
                            <div class="side-bar-box" v-if="help_data_solution.children && help_data_solution.children.length">
                                <template v-for="(item, itemIndex) in help_data_solution.children">
                                    <div class="side-bar-list" :key="item.name">
                                        <p class="side-bar-title side-bar-item" v-if="item.name">{{ item.name }}</p>
                                        <div class="side-bar-item" v-for="citem in item.children" :key="citem.name" @click.stop="levelClick3(citem, item.name, help_data_solution.name)">
                                            <a class="link-btn" href="javascript:;">
                                                <p class="label" v-html="citem.name"></p>
                                                <span class="iconfont icon-next">&#xe704;</span>
                                            </a>
                                        </div>
                                    </div>
                                    <div class="line" v-if="itemIndex !== help_data_solution.children.length - 1" :key="'line' + itemIndex"></div>
                                </template>
                            </div>
                        </div>
                    </div>
                </transition>

                <!-- <transition name="slide-right">
                            <div class="level-page slide-page-user" v-if="slide_page_user">
                                <div class="side-bar-box level-box">
                                    <h2 class="side-bar-title" @click.stop="hideSlideUser"><a href="javascript:;" class="iconfont iconfont-left">&#xe702;</a>{{ $c("components.smallComponents.mSideBar.menu") }}</h2>
                                    <div class="side-bar-list side-bar-list-user">
                                        <nuxt-link @click.native="hideExpand(localePath({ name: 'my-account' }))" :to="localePath({ name: 'my-account' })" class="side-bar-item">
                                            <span class="iconfont iconfont-account">&#xe679;</span>
                                            <span class="info">{{ $c("components.smallComponents.mSideBar.myAccount") }}</span>
                                        </nuxt-link>
                                        <nuxt-link @click.native="hideExpand(localePath({ name: 'account_settings' }))" :to="localePath({ name: 'account_settings' })" class="side-bar-item">
                                            <span class="iconfont iconfont-setting">&#xf173;</span>
                                            <span class="info">{{ $c("components.smallComponents.newPHeader.manageProfile") }}</span>
                                        </nuxt-link>

                                        <nuxt-link @click.native="hideExpand(localePath({ name: 'order-history' }))" :to="localePath({ name: 'order-history' })" class="side-bar-item">
                                            <span class="iconfont iconfont-account">&#xf175;</span>
                                            <span class="info">{{ $c("components.smallComponents.newPHeader.myOrders") }}</span>
                                        </nuxt-link>
                                        <nuxt-link
                                            @click.native="hideExpand(localePath({ name: 'quote_history', query: { type: 1 } }))"
                                            :to="localePath({ name: 'quote_history', query: { type: 1 } })"
                                            class="side-bar-item">
                                            <span class="iconfont iconfont-account">&#xf178;</span>
                                            <span class="info">{{ $c("components.smallComponents.newPHeader.myQuotes") }}</span>
                                        </nuxt-link>
                                        <nuxt-link @click.native="hideExpand(localePath({ name: 'support_ticket' }))" :to="localePath({ name: 'support_ticket' })" class="side-bar-item">
                                            <span class="iconfont iconfont-account">&#xf176;</span>
                                            <span class="info">{{ $c("components.smallComponents.newPHeader.mySupport") }}</span>
                                        </nuxt-link>
                                        <nuxt-link @click.native="hideExpand(localePath({ name: 'request_rma' }))" :to="localePath({ name: 'request_rma' })" class="side-bar-item">
                                            <span class="iconfont iconfont-account">&#xf177;</span>
                                            <span class="info">{{ $c("components.smallComponents.newPHeader.returnRefund") }}</span>
                                        </nuxt-link>
                                        <div class="side-bar-item side-bar-item-logout" @click.stop="logout">
                                            <span class="iconfont iconfont-account">&#xf180;</span>
                                            <span class="info">{{ $c("components.smallComponents.mSideBar.signOut") }}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </transition> -->

                <transition name="slide-right">
                    <div class="level-page" v-if="level2_data">
                        <div class="side-bar-box">
                            <div class="back" @click.stop="levelBack2">
                                <span class="iconfont">&#xe702;</span>
                                <p class="back-label">{{ level2_data.name }}</p>
                            </div>
                            <div class="side-bar-list" v-if="level2_data.children && level2_data.children.length">
                                <div class="side-bar-item" v-for="item in level2_data.children" :key="item.name" @click.stop="levelClick2(item)">
                                    <a href="javascript:;" class="next-btn">
                                        <p class="label" v-html="item.name"></p>
                                        <span class="iconfont icon-next">&#xe704;</span>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </transition>

                <transition name="slide-right">
                    <div class="level-page" v-if="level3_data">
                        <div class="side-bar-box">
                            <div class="back" @click.stop="levelBack3">
                                <span class="iconfont">&#xe702;</span>
                                <p class="back-label">{{ level3_data.name }}</p>
                            </div>
                            <div class="side-bar-list" v-if="level3_data.children && level3_data.children.length">
                                <div class="side-bar-item" v-if="type === 'category'" @click.stop="viewAllClick">
                                    <a href="javascript:;" class="link-btn">
                                        <p class="label">{{ $c("components.smallComponents.mSideBar.viewAll") }}</p>
                                    </a>
                                </div>

                                <div class="side-bar-item" v-for="item in level3_data.children" :key="item.name" @click.stop="levelClick3(item)">
                                    <a href="javascript:;" class="next-btn">
                                        <p class="label">{{ item.name }}</p>
                                        <span class="iconfont icon-next" v-if="type !== 'category'">&#xe704;</span>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </transition>

                <transition name="slide-right">
                    <div class="level-page" v-if="level4_data">
                        <div class="side-bar-box">
                            <div class="back" @click.stop="levelBack4">
                                <span class="iconfont">&#xe702;</span>
                                <p class="back-label">{{ level4_data.name }}</p>
                            </div>

                            <div class="side-bar-list" v-if="level4_data.children && level4_data.children.length">
                                <div class="side-bar-item" v-if="type === 'solution' && level4_data.url" @click.stop="viewAllClick">
                                    <a href="javascript:;" class="link-btn">
                                        <p class="label">{{ $c("components.smallComponents.mSideBar.viewAll") }}</p>
                                    </a>
                                </div>

                                <div class="side-bar-item" v-for="item in level4_data.children" :key="item.name" @click.stop="levelClick4(item)">
                                    <a href="javascript:;" class="next-btn">
                                        <p class="label">{{ item.name }}</p>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </transition>

                <!-- My Account Menu-->
                <!-- <transition name="slide-right">
                            <div class="level-page" v-if="myAccountData">
                                <div class="side-bar-box level-box">
                                    <h2 class="side-bar-title" @click.stop="handleMyAccountBack"><a href="javascript:;" class="iconfont iconfont-left">&#xe702;</a>{{ $c("pages.MyAccount.myAccount") }}</h2>
                                    <div class="side-bar-list" v-if="myAccountData">
                                        <a href="javascript:;" @click.stop="handleMyAccountClick(item)" class="side-bar-item" v-for="item in myAccountData" :key="item.name">
                                            <div class="left">
                                                <span class="info" v-html="item.label"></span>
                                            </div>
                                            <span class="iconfont iconfont-right">&#xe703;</span>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </transition> -->

                <!-- My Account Menu Child-->
                <!-- <transition name="slide-right">
                            <div class="level-page" v-if="myAccountDataChild">
                                <div class="side-bar-box level-box">
                                    <h2 class="side-bar-title" @click.stop="handleMyAccountChildBack"><a href="javascript:;" class="iconfont iconfont-left">&#xe702;</a>{{ myAccountDataChild.label }}</h2>
                                    <ul class="need-help-list" v-if="myAccountDataChild.name === 'needHelp'">
                                        <template v-if="!isRussiaAndRuMixin">
                                            <li>{{ managerInfo.web_phone || "--" }}</li>
                                            <li>{{ $c("pages.MyAccount.menu.officeHours") }}</li>
                                        </template>
                                    </ul>

                                    <div class="side-bar-list" v-if="myAccountDataChild">
                                        <a href="javascript:;" @click.stop="handleMyAccountChildClick(item)" class="side-bar-item" v-for="item in myAccountDataChild.child" :key="item.name">
                                            <div class="left">
                                                <span class="info" v-html="item.label"></span>
                                            </div>
                                            <span class="iconfont iconfont-right">&#xe703;</span>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </transition> -->

                <leave-feedback :transition="isMobile ? 'slide-down' : 'fade'" :show="feedback.show" :title="feedback.title" @close="feedbackHide" @autoClose="feedbackHide"></leave-feedback>
            </client-only>
        </div>
    </div>
</template>

<script>
import { mapState, mapMutations, mapGetters, mapActions } from "vuex"
import { liveChat, setCookieOptions } from "@/util/util.js"
import LeaveFeedback from "@/popup/LeaveFeedback/LeaveFeedback.vue"
import menuDataMixin from "@/components/AccountMenus/menuDataMixin.js"
import MChangeWebsite from "@/components/ChangeWebsite/MChangeWebsite"
export default {
    name: "MSideBar",
    mixins: [menuDataMixin],
    components: {
        LeaveFeedback,
        MChangeWebsite,
    },
    props: {
        top: {
            type: Number,
            default: 0,
        },
    },
    data() {
        return {
            slide_page_user: false,
            help_data: null,
            help_data_solution: null,
            type: "",
            level2_data: null,
            level3_data: null,
            myAccountData: null,
            myAccountDataChild: null,
            logout_loading: false,
            feedback: {
                show: false,
                title: this.$c("components.smallComponents.fsFooter.feedBack"),
            },
            solutionNavData: [
                {
                    name: this.$c("components.smallComponents.solutions.Industry.title"),
                    children: [
                        {
                            name: this.$c("components.smallComponents.solutions.Industry.one.title"),
                            url: "/solutions_industries/Education-Industry-Solutions-61.html",
                            children: [
                                {
                                    name: this.$c("components.smallComponents.solutions.Industry.one.list.one"),
                                    url: "/solutions/Campus-VoIP-communication-solution-12.html",
                                    state: 0,
                                },
                                {
                                    name: this.$c("components.smallComponents.solutions.Industry.one.list.three"),
                                    url: "/solutions/Campus-all-optical-network-solution-38.html",
                                    state: 0,
                                },
                                {
                                    name: this.$c("components.smallComponents.solutions.Industry.one.list.four"),
                                    url: "/solutions/University-data-center-network-solution-16.html",
                                    state: 0,
                                },
                                {
                                    name: this.$c("solutions.CmsSolutions.headerPushEntry.digitalCampusConstructionSolution.title"),
                                    url: "/solutions/digital-campus-construction-solution-10003.html",
                                    state: 1,
                                },
                                {
                                    name: this.$c("components.smallComponents.solutions.Industry.one.list.two"),
                                    url: "/solutions/Video-Surveillance-Campus-solution-999.html",
                                    state: 1,
                                },
                            ],
                        },
                        {
                            name: this.$c("components.smallComponents.solutions.Industry.two.title"),
                            url: "/solutions_industries/Hospitality-Networking-Solutions-133.html",
                            children: [
                                {
                                    name: this.$c("components.smallComponents.solutions.Industry.two.list.one"),
                                    url: "/solutions/VoIP-Solution-for-Hotel-96.html",
                                    state: 1,
                                },
                                {
                                    name: this.$c("components.smallComponents.solutions.Industry.two.list.two"),
                                    url: "/solutions/Smart-Hotel-Wireless-Network-Solution-9.html",
                                    state: 0,
                                },
                                {
                                    name: this.$c("components.smallComponents.solutions.Industry.two.list.three"),
                                    url: "/solutions/All-optical-network-coverage-solution-for-hotel-style-apartments-68.html",
                                    state: 0,
                                },
                                {
                                    name: this.$c("components.smallComponents.solutions.Industry.two.list.four"),
                                    url: "/solutions/Smart-Hotel-Video-Surveillance-Solution-18.html",
                                    state: 0,
                                },
                                {
                                    name: this.$c("components.smallComponents.solutions.Industry.two.list.five"),
                                    url: "/solutions/Smart-Hotel-Network-Solution-102.html",
                                    state: 0,
                                },
                            ],
                        },
                        {
                            name: this.$c("components.smallComponents.solutions.Industry.three.title"),
                            url: "/solutions_industries/Small-Business-Networking-Solutions-13.html",
                            children: [
                                {
                                    name: this.$c("components.smallComponents.solutions.Industry.three.list.one"),
                                    url: "/solutions/Private-Cloud-Solutions-for-Small-Businesses-Explore-OEM-Solutions-8.html",
                                    state: 0,
                                },
                                {
                                    name: this.$c("components.smallComponents.solutions.Industry.three.list.two"),
                                    url: "/solutions/Internet-Office-Network-Solution-47.html",
                                    state: 2,
                                },
                                {
                                    name: this.$c("components.smallComponents.solutions.Industry.three.list.three"),
                                    url: "/solutions/All-optical-network-solutions-for-high-rise-office-70.html",
                                    state: 0,
                                },
                                {
                                    name: this.$c("components.smallComponents.solutions.Industry.three.list.four"),
                                    url: "/solutions/VoIP-Solution-for-Branches-of-Business-97.html",
                                    state: 0,
                                },
                                {
                                    name: this.$c("components.smallComponents.solutions.Industry.three.list.five"),
                                    url: "/solutions/Network-Traffic-Monitoring-Solution-for-Medium-and-Large-Enterprises-95.html",
                                    state: 0,
                                },
                            ],
                        },
                        {
                            name: this.$c("components.smallComponents.solutions.Industry.four.title"),
                            url: "/solutions_industries/Media-and-Entertainment-Solutions-85.html",
                            children: [
                                {
                                    name: this.$c("components.smallComponents.solutions.Industry.four.list.one"),
                                    url: "/solutions/Bar-Security-Video-Surveillance-Solution-6.html",
                                    state: 0,
                                },
                                {
                                    name: this.$c("components.smallComponents.solutions.Industry.four.list.two"),
                                    url: "/solutions/Amusement-Parks-Resumption-Video-Surveillance-Solution-100.html",
                                    state: 0,
                                },
                            ],
                        },
                        {
                            name: this.$c("components.smallComponents.solutions.Industry.five.title"),
                            url: "",
                            children: [
                                {
                                    name: this.$c("components.smallComponents.solutions.Industry.five.list.one"),
                                    url: "/solutions/10g-dual-fiber-multi-point-flexible-expansion-solution-10135.html",
                                    state: 1,
                                },
                                {
                                    name: this.$c("components.smallComponents.solutions.Industry.five.list.two"),
                                    url: "/solutions/Metro-automated-transmission-platform-solution-56.html",
                                    state: 0,
                                },
                            ],
                        },
                        {
                            name: this.$c("components.smallComponents.solutions.Industry.six.title"),
                            url: "/solutions_industries/Retail-Industry-Solutions-49.html",
                            children: [
                                {
                                    name: this.$c("components.smallComponents.solutions.Industry.six.list.one"),
                                    url: "/solutions/Large-Shopping-Mall-Wireless-Network-Solution-13.html",
                                    state: 0,
                                },
                                {
                                    name: this.$c("components.smallComponents.solutions.Industry.six.list.two"),
                                    url: "/solutions/Smart-Supermarket-and-Retailer-Video-Surveillance-Solution-5.html",
                                    state: 0,
                                },
                            ],
                        },
                        {
                            name: this.$c("components.smallComponents.solutions.Industry.seven.title"),
                            url: "/solutions_industries/Government-Network-Solutions-73.html",
                            children: [
                                {
                                    name: this.$c("components.smallComponents.solutions.Industry.seven.list.one"),
                                    url: "/solutions/VoIP-Solutions-for-Governments-14.html",
                                    state: 0,
                                },
                            ],
                        },
                        {
                            name: this.$c("components.smallComponents.solutions.Industry.eight.title"),
                            url: "",
                            children: [
                                {
                                    name: this.$c("components.smallComponents.solutions.Industry.eight.list.one"),
                                    url: "/solutions/automation-system-network-solution-10001.html",
                                    state: 1,
                                },
                            ],
                        },
                    ],
                },
                {
                    name: this.$c("components.smallComponents.solutions.Scenario.title"),
                    children: [
                        {
                            name: this.$c("components.smallComponents.solutions.Scenario.one.title"),
                            url: "/solutions_industries/IDC-EDC-Solutions-25.html",
                            children: [
                                {
                                    name: this.$c("components.smallComponents.solutions.Scenario.one.list.one"),
                                    url: "/solutions/CDN-Data-Center-Network-Solution---Reprint-94.html",
                                    state: 0,
                                },
                                {
                                    name: this.$c("components.smallComponents.solutions.Scenario.one.list.two"),
                                    url: "/solutions/Network-Visibility-Solution-in-Data-Centers-10.html",
                                    state: 0,
                                },
                                {
                                    name: this.$c("components.smallComponents.solutions.Scenario.one.list.three"),
                                    url: "/solutions/All-electric-access-data-center-network-solution-93.html",
                                    state: 0,
                                },
                                {
                                    name: this.$c("components.smallComponents.solutions.Scenario.one.list.four"),
                                    url: "/solutions/Data-Center-Structured-Cabling-Solution-75.html",
                                    state: 0,
                                },
                                {
                                    name: this.$c("solutions.CmsSolutions.headerPushEntry.fourOneNetworkUpgrading.title"),
                                    url: this.$c("solutions.CmsSolutions.headerPushEntry.fourOneNetworkUpgrading.href"),
                                    state: 1,
                                },
                                {
                                    name: this.$c("components.smallComponents.solutions.Scenario.one.list.five"),
                                    url: "/solutions/25G-100G-Data-Center-Network-Solution-11.html",
                                    state: 2,
                                },
                            ],
                        },
                        {
                            name: this.$c("components.smallComponents.solutions.Scenario.two.title"),
                            url: "",
                            children: [
                                {
                                    name: this.$c("components.smallComponents.solutions.Scenario.two.list.one"),
                                    url: "/solutions/High-Capacity-OTN-Solutions-for-Data-Center-40.html",
                                    state: 0,
                                },
                                {
                                    name: this.$c("components.smallComponents.solutions.Scenario.two.list.three"),
                                    url: "/solutions/Interconnect-Solution-for-Small-and-Medium-Sized-Data-Centers-46.html",
                                    state: 0,
                                },
                                {
                                    name: this.$c("components.smallComponents.solutions.Scenario.two.list.four"),
                                    url: "/solutions/Long-Haul-and-Fully-Convergent-Solutions-for-Data-Centers-23.html",
                                    state: 0,
                                },
                                {
                                    name: this.$c("components.smallComponents.solutions.Scenario.two.list.five"),
                                    url: "/solutions/Multi-service-hybrid-access-transport-solution-69.html",
                                    state: 0,
                                },
                            ],
                        },
                        {
                            name: this.$c("components.smallComponents.solutions.Scenario.three.title"),
                            url: "",
                            children: [
                                {
                                    name: this.$c("components.smallComponents.solutions.Scenario.three.list.one"),
                                    url: "/solutions/Family-Villa-Video-Surveillance-Solutions-4.html",
                                    state: 0,
                                },
                                {
                                    name: this.$c("components.smallComponents.solutions.Scenario.three.list.two"),
                                    url: "/solutions/Outdoor-smart-parking-solution-45.html",
                                    state: 0,
                                },
                                {
                                    name: this.$c("components.smallComponents.solutions.Scenario.three.list.three"),
                                    url: "/solutions/Industrial-Park-Security-Video-Surveillance-Solution-17.html",
                                    state: 0,
                                },
                                {
                                    name: this.$c("components.smallComponents.solutions.Scenario.three.list.four"),
                                    url: "/solutions/Internet-building-security-monitoring-solutions-74.html",
                                    state: 0,
                                },
                                {
                                    name: this.$c("components.smallComponents.solutions.Scenario.three.list.five"),
                                    url: "/solutions/Enterprises-Security-Video-Surveillance-Solution-19.html",
                                    state: 0,
                                },
                            ],
                        },
                        {
                            name: this.$c("components.smallComponents.solutions.Scenario.four.title"),
                            url: "",
                            children: [
                                {
                                    name: this.$c("components.smallComponents.solutions.Scenario.four.list.one"),
                                    url: "/solutions/Enterprise-Wi-Fi-6-Network-Solution-1.html",
                                    state: 2,
                                },
                                {
                                    name: this.$c("components.smallComponents.solutions.Scenario.four.list.two"),
                                    url: "/solutions/Campus-Wireless-Network-Solution-101.html",
                                    state: 0,
                                },
                                {
                                    name: this.$c("components.smallComponents.solutions.Scenario.four.list.three"),
                                    url: "/solutions/Wisdom-Villa-Wi-Fi-Coverage-Network-Solution-20.html",
                                    state: 0,
                                },
                                {
                                    name: this.$c("components.smallComponents.solutions.Scenario.four.list.four"),
                                    url: "/wireless_product_selector.html",
                                    state: 1,
                                },
                            ],
                        },
                    ],
                },
            ],
            solutionsNvaList: null,
            level4_data: null,
        }
    },
    inject: ["fsLiveChat"],
    computed: {
        ...mapState({
            isLogin: (state) => state.userInfo.isLogin,
            userInfo: (state) => state.userInfo.userInfo,
            cartList: (state) => state.cart.list,
            cartData: (state) => state.cart.cartData,
            savedCarts: (state) => state.userInfo.savedCarts,
            activeQuotes: (state) => state.userInfo.activeQuotes,
            top_data: (state) => state.category.top_data,
            menu_data: (state) => state.category.menu_data,
            category_data: (state) => state.category.category_data,
            classify_data: (state) => state.category.classify_data,
            isMobile: (state) => state.device.isMobile,
            managerInfo: (state) => state.userInfo.managerInfo,
            pageGroup: (state) => state.ga.pageGroup,
            domain: (state) => state.meta.domain,
            warehouse: (state) => state.webSiteInfo.warehouse,
            website: (state) => state.webSiteInfo.website,
        }),
        ...mapGetters({
            isCn: "webSiteInfo/isCn",
        }),
    },
    created() {
        this.initSolutionNav()
        // 优先展示hot、new、再其他，最多8个
        this.processSolutionNav()
    },
    mounted() {
        console.log(this.category_data)
    },
    methods: {
        initSolutionNav() {
            if (this.warehouse === "US") {
                // 40
                this.solutionNavData[1].children[1].children[0].name = this.$c("solutions.HighCapacityOTN.navData.children[0].tit")
                this.solutionNavData[1].children[1].children[0].state = 1
            }

            // 1000
            this.solutionNavData[1].children[0].children.unshift(
                {
                    name: this.$c("solutions.CloudDataCenterVxLAN.navData.children[0].tit"),
                    url: this.$c("solutions.CloudDataCenterVxLAN.navData.children[0].href"),
                    state: 1,
                },
                {
                    name: this.$c("solutions.TelecomRoomsCablingUpgradingSolution.navData.children[3].tit"),
                    url: this.$c("solutions.TelecomRoomsCablingUpgradingSolution.navData.children[3].href"),
                    state: 1,
                }
            )

            // 1001
            this.solutionNavData[0].children[1].children.push({
                name: this.$c("solutions.MediumAndLargeCampusWirelessNetwork.navData.children[5].tit"),
                url: this.$c("solutions.MediumAndLargeCampusWirelessNetwork.navData.children[5].href"),
                state: 1,
            })

            if (this.warehouse === "US") {
                // 1002
                this.solutionNavData[1].children[0].children.unshift({
                    name: this.$c("solutions.DCI.navData.children[0].tit"),
                    url: this.$c("solutions.DCI.navData.children[0].href"),
                    state: 1,
                })
            }

            // 1003
            this.solutionNavData[1].children[0].children.unshift({
                name: this.$c("solutions.TwoTierNetworkArchitecture.navData.children[0].tit"),
                url: this.$c("solutions.TwoTierNetworkArchitecture.navData.children[0].href"),
                state: 1,
            })

            // 1004
            if (this.website === "uk") {
                this.solutionNavData[1].children[1].children.push({
                    name: this.$c("solutions.OpticalCommunicationNetworkForSmartRailways.navData.children[5].tit"),
                    url: this.$c("solutions.OpticalCommunicationNetworkForSmartRailways.navData.children[5].href"),
                    state: 1,
                })
            }

            // 1007
            this.solutionNavData[1].children[3].children.unshift(
                {
                    name: this.$c("solutions.Wireless5G.navData.children[0].tit"),
                    url: this.$c("solutions.Wireless5G.navData.children[0].href"),
                    state: 1,
                },
                {
                    name: this.$c("solutions.WirelessFronthaulOpticalTransceiverModuleSolution4G.navData.children[1].tit"),
                    url: this.$c("solutions.WirelessFronthaulOpticalTransceiverModuleSolution4G.navData.children[1].href"),
                    state: 1,
                },
                {
                    name: this.$c("solutions.ConvergedWirelessFronthaulOpticalTransceiverModuleSolution4G5G.navData.children[2].tit"),
                    url: this.$c("solutions.ConvergedWirelessFronthaulOpticalTransceiverModuleSolution4G5G.navData.children[2].href"),
                    state: 1,
                }
            )

            // 10028
            this.solutionNavData[0].children[1].children.push({
                name: this.$c("solutions.SOHOOffice.navData.children[6].tit"),
                url: this.$c("solutions.SOHOOffice.navData.children[6].href"),
                state: 1,
            })
            // 10042
            this.solutionNavData[0].children[1].children.push({
                name: this.$c("solutions.SOHOOffice.navData.children[7].tit"),
                url: this.$c("solutions.SOHOOffice.navData.children[7].href"),
                state: 1,
            })

            // 1009
            this.solutionNavData[0].children[1].children.push({
                name: this.$c("solutions.VideoSurveillanceSmallMidShoppingMall.navData.children[0].tit"),
                url: this.$c("solutions.VideoSurveillanceSmallMidShoppingMall.navData.children[0].href"),
                state: 1,
            })

            //1013
            this.solutionNavData[0].children[1].children.push({
                name: this.$c("solutions.MediumLarge10GigabitCampusNetworkSolution.navData.children[6].tit"),
                url: this.$c("solutions.MediumLarge10GigabitCampusNetworkSolution.navData.children[6].href"),
                state: 0,
            })

            // 10021 10023
            this.solutionNavData[0].children[1].children.push(...this.$c("solutions.CmsSolutions.headerPushEntry.enterpeiseType"))

            if (this.warehouse === "US") {
                // 1010
                this.solutionNavData[0].children[4].children.push({
                    name: this.$c("solutions.PhysicalLayerFiber.navData.children[3].tit"),
                    url: this.$c("solutions.PhysicalLayerFiber.navData.children[3].href"),
                    state: 1,
                })
            }
            console.log(6666, this.solutionNavData[1].children[2].children)
            //10017 1016 1017
            this.$c("solutions.EnterpriseVideoSurveillance.navData.children").forEach((item, index) => {
                if (index >= 5) {
                    let obj = {
                        title: item.tit,
                        href: item.href,
                        state: 0,
                    }
                    ;[5, 8, 9, 10].includes(index) ? (obj.state = 1) : ""
                    if (index === 10) {
                        this.solutionNavData[1].children[2].children.unshift(obj)
                    } else {
                        this.solutionNavData[1].children[2].children.push(obj)
                    }
                }
            })

            // 10002
            this.solutionNavData[0].children[4].children.push({
                name: this.$c("solutions.CmsSolutions.headerPushEntry.cloudDataCenterUpgrade.title"),
                url: this.$c("solutions.CmsSolutions.headerPushEntry.cloudDataCenterUpgrade.href"),
                state: 1,
            })
            // 10006
            this.solutionNavData[1].children[0].children.unshift({
                name: this.$c("solutions.CmsSolutions.headerPushEntry.HighComputingNetworkSolution.title"),
                url: this.$c("solutions.CmsSolutions.headerPushEntry.HighComputingNetworkSolution.href"),
                state: 1,
            })
            // 10011
            this.solutionNavData[1].children[0].children.unshift({
                name: this.$c("solutions.CmsSolutions.headerPushEntry.iPSANStorageNetworkingSolution.title"),
                url: this.$c("solutions.CmsSolutions.headerPushEntry.iPSANStorageNetworkingSolution.href"),
                state: 1,
            })
            // 10013
            this.solutionNavData[1].children[0].children.unshift({
                name: this.$c("solutions.CmsSolutions.headerPushEntry.securityResourcePoolSolution.title"),
                url: this.$c("solutions.CmsSolutions.headerPushEntry.securityResourcePoolSolution.href"),
                state: 1,
            })
            // 10014
            this.solutionNavData[1].children[0].children.unshift({
                name: this.$c("solutions.CmsSolutions.headerPushEntry.dataCenterCDNSolution.title"),
                url: this.$c("solutions.CmsSolutions.headerPushEntry.dataCenterCDNSolution.href"),
                state: 1,
            })
            // 10004
            this.solutionNavData[1].children[0].children.push({
                name: this.$c("solutions.CmsSolutions.headerPushEntry.eightDataCenter.title"),
                url: this.$c("solutions.CmsSolutions.headerPushEntry.eightDataCenter.href"),
                state: 1,
            })
            // 1011 修改为 10012
            this.solutionNavData[1].children[0].children.push({
                name: this.$c("solutions.CmsSolutions.headerPushEntry.fourDaterCenter.title"),
                url: this.$c("solutions.CmsSolutions.headerPushEntry.fourDaterCenter.href"),
                state: 1,
            })
            // 1006
            this.solutionNavData[1].children[0].children.push({
                name: this.$c("solutions.Transmission400G.navData.children[0].tit"),
                url: this.$c("solutions.Transmission400G.navData.children[0].href"),
                state: 1,
            })
            // 10005
            this.solutionNavData[1].children[0].children.push({
                name: this.$c("solutions.CmsSolutions.headerPushEntry.oneDataCenter.title"),
                url: this.$c("solutions.CmsSolutions.headerPushEntry.oneDataCenter.href"),
                state: 1,
            })
            // 1005
            this.solutionNavData[1].children[0].children.push({
                name: this.$c("solutions.Transmission100G.navData.children[0].tit"),
                url: this.$c("solutions.Transmission100G.navData.children[0].href"),
                state: 1,
            })
            // 10020 10019 10016 10027
            this.solutionNavData[1].children[1].children.unshift(...this.$c("solutions.CmsSolutions.headerPushEntry.otnType.head"))
            // 10022
            this.solutionNavData[1].children[1].children.push(...this.$c("solutions.CmsSolutions.headerPushEntry.otnType.back"))
            // 10032
            this.solutionNavData[0].children[2].children.push(...this.$c("solutions.CmsSolutions.headerPushEntry.hospitalityType.back"))
            //10010
            this.solutionNavData[0].children[3].children.push({
                name: this.$c("solutions.CmsSolutions.headerPushEntry.outdoorWirelessNetwork.title"),
                url: "/solutions/outdoor-wireless-network-connectivity-solutions-10010.html",
                state: 1,
            })
            // 10033
            this.solutionNavData[0].children[5].children.push(...this.$c("solutions.CmsSolutions.headerPushEntry.retailType.back"))
            // 10035 10036
            this.solutionNavData[1].children[0].children.unshift(...this.$c("solutions.CmsSolutions.headerPushEntry.dataCenterType.head"))
            this.solutionNavData[1].children[0].children.push(...this.$c("solutions.CmsSolutions.headerPushEntry.dataCenterType.back"))
            // 10041
            this.solutionNavData[0].children[0].children.unshift(...this.$c("solutions.CmsSolutions.headerPushEntry.highEducation.head"))
        },
        processSolutionNav() {
            this.solutionNavData.forEach((item) => {
                item.children.forEach((val) => {
                    let len = val.children.length
                    for (let i = 0; i < len - 1; i++) {
                        for (let j = 0; j < len - 1 - i; j++) {
                            if (val.children[j].state < val.children[j + 1].state) {
                                let temp = val.children[j]
                                val.children[j] = val.children[j + 1]
                                val.children[j + 1] = temp
                            }
                        }
                    }
                    val.children = val.children.slice(0, 8)
                })
            })
        },
        ...mapMutations({
            setResourcePage: "device/setResourcePage",
            setShowHeaderRight: "category/setShowHeaderRight",
        }),
        ...mapActions({
            getUserInfo: "userInfo/getUserInfo",
            getCart: "cart/getCart",
        }),
        gaEvent(eventAction, eventLabel) {
            window.dataLayer &&
                window.dataLayer.push({
                    event: "uaEvent",
                    eventCategory: this.pageGroup,
                    eventAction: eventAction,
                    eventLabel: eventLabel,
                    nonInteraction: false,
                })
        },
        urlClick(url) {
            if (url === "chat") {
                this.fsLiveChat()
            }
            this.$emit("menuClick")
        },
        hideExpand(url) {
            this.$emit("menuClick")
            if (url) {
                this.gaEvent("top_navigation", `${this.domain}${url}_Normal`)
            }
        },
        accountClick() {
            if (!this.isLogin) {
                this.$router.push(this.localePath({ name: "login", query: { redirect: this.$route.fullPath } }))
            } else {
                this.slide_page_user = true
            }
        },
        hideSlideUser() {
            this.slide_page_user = false
        },
        helpShow(item, i) {
            if (i === 0 && item.location === "1") {
                this.type = "solution"
                this.help_data_solution = {
                    name: item.title,
                    children: this.solutionNavData,
                }
            } else {
                this.help_data = item
            }
            this.gaEvent("top_navigation", item.title)
        },
        helpHide() {
            this.help_data = null
        },
        helpHideSolution() {
            this.help_data_solution = null
        },
        levelClick1(type, item) {
            this.type = type
            if (type === "category") {
                if (item.children && item.children.length) {
                    this.level2_data = item
                } else {
                    this.$router.push(this.localePath({ name: "list", params: { id: item.url.replace(/\/?c\//, "") } }))
                }
                this.gaEvent("top_navigation", `${item.name}`)
            }
        },
        handleMyAccountShow() {
            if (!this.isLogin) {
                this.$router.push(this.localePath({ name: "login", query: { redirect: this.$route.fullPath } }))
            } else {
                this.myAccountData = [...JSON.parse(JSON.stringify(this.filterMenuMixin)), ...JSON.parse(JSON.stringify(this.MMenuMixin))]
            }
            this.gaEvent("top_navigation", "My Account")
        },
        handleMyAccountClick(item) {
            if (item.child && item.child.length) {
                this.myAccountDataChild = item
                this.gaEvent("login_function", item.label)
            } else {
                this.myAccountItemClick(item)
            }
        },
        handleMyAccountChildClick(item) {
            this.myAccountItemClick(item)
        },
        myAccountItemClick(item) {
            const { name, path, isOpen, eventLabel, label } = item

            if (name === "feedback") {
                this.feedbackShow()
                return
            }

            if (name === "chat") {
                this.callUp()
                return
            }

            if (name === "emailUs") {
                this.$emit("menuClick")
                this.handleResourcePageClickMixin()
                this.$router.push(this.localePath(path))
                return
            }

            if (name === "bills") {
                const { isPurchaseMixin: isPurchase } = this
                this.$emit("menuClick")
                this.toPageMixin({ name, query: { isPurchase } }, label, "login_function")
                return
            }

            if (isOpen) {
                this.openMixin(path, label, "login_function")
                return
            }

            if (path) {
                this.$emit("menuClick")
                this.toPageMixin(path, label, "login_function")
                return
            }
        },
        callUp() {
            const a = document.createElement("a")
            a.style.display = "none"
            a.href = "tel:+****************"
            document.body.appendChild(a)
            a.click()
            document.body.removeChild(a)
        },
        levelClick2(item) {
            if (this.type === "category" || this.type === "solution") {
                if (item.children && item.children.length) {
                    this.level3_data = item
                } else {
                    this.$router.push(this.$handleLink(item.url).url)
                }
                this.gaEvent("top_navigation", `${this.level2_data.name}_${item.name}`)
            }
        },
        levelClick3(item, pName, ppName) {
            if (this.type === "solution") {
                if (item.children && item.children.length) {
                    this.level4_data = item
                } else {
                    this.$router.push(this.$handleLink(item.url).url)
                }
                this.gaEvent("top_navigation", `${ppName}_${pName}_${item.name}`)
            } else {
                this.$emit("menuClick")
                this.$router.push(this.$handleLink(item.url).url)

                let t = ""
                if (item.is_has_hot_products) {
                    t = "_Hot"
                } else if (item.is_has_new_products) {
                    t = "_NEW"
                } else {
                    t = "_Normal"
                }
                this.gaEvent("top_navigation", `${this.domain}${this.$handleLink(item.url).url}_${t}`)
            }
        },
        levelClick4(item) {
            this.$emit("menuClick")
            this.$router.push(this.$handleLink(item.url).url)

            const stateList = ["Normal", "New", "Hot"]
            this.gaEvent("top_navigation", `${this.domain}${this.$handleLink(item.url).url}_${stateList[item.state]}`)
        },
        viewAllClick(name) {
            this.$emit("menuClick")
            if (this.type === "solution") {
                this.$router.push(this.$handleLink(this.level4_data.url).url)
                this.gaEvent("top_navigation", `${this.domain}${this.$handleLink(this.level4_data.url).url}_Normal`)
            } else {
                this.$router.push(this.$handleLink(this.level3_data.url).url)
                this.gaEvent("top_navigation", `${this.domain}${this.$handleLink(this.level3_data.url).url}_Normal`)
            }
        },
        handleMyAccountBack() {
            this.myAccountData = null
        },
        handleMyAccountChildBack() {
            this.myAccountDataChild = null
        },
        levelBack2() {
            this.level2_data = null
        },
        levelBack3() {
            this.level3_data = null
        },
        levelBack4() {
            this.level4_data = null
        },
        logout() {
            if (this.logout_loading) {
                return
            }
            this.logout_loading = true
            this.$axios
                .post("/api/user/logout")
                .then((res) => {
                    this.logout_loading = false
                    this.$cookies.remove("token_new")
                    this.getUserInfo()
                    this.getCart()
                    this.$router.replace(this.localePath({ name: "home" }))
                })
                .catch((err) => {
                    this.logout_loading = false
                })
        },
        feedbackShow() {
            this.feedback.show = true
        },
        feedbackHide() {
            this.feedback.show = false
        },
        hideWebsite() {
            this.hideExpand()
        },
    },
}
</script>

<style lang="scss" scoped>
.m-side-bar {
    position: fixed;
    top: 0;
    left: 0;
    bottom: 0;
    background: #fff;
    z-index: 101;
    box-shadow: 0px -2px 2px 0px rgba(0, 0, 0, 0.03);
    width: 350px;
    overflow: hidden;
    .content {
        color: #19191a;
        @include font14();
        height: 100%;
        overflow-x: hidden;
        overflow-y: auto;
    }
    .back {
        padding: 12px 16px;
        display: flex;
        @include font16();
        font-weight: 600;
        cursor: pointer;
        .iconfont {
            font-size: 12px;
            margin-right: 4px;
        }
        .back-label {
            word-break: break-word;
        }
    }
    .side-bar-box {
        padding-bottom: 12px;
    }
    .line {
        margin: 12px 16px;
        height: 1px;
        background-color: #e5e5e5;
    }
    .side-bar-list {
        .side-bar-title {
            font-weight: 600;
        }
        .side-bar-item {
            padding: 12px 16px;
            display: block;
            color: #19191a;
            cursor: pointer;
            .label {
                flex: 1;
                word-break: break-word;
            }
            .icon-prefix {
                font-size: 16px;
                display: block;
                width: 16px;
                height: 16px;
                line-height: 16px;
                text-align: center;
                margin-right: 8px;
            }
            .icon-next {
                font-size: 12px;
            }
            .next-btn,
            .link-btn {
                display: flex;
                align-items: center;
                color: #19191a;
                &:hover {
                    text-decoration: none;
                }
            }
        }
    }

    .level-page {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: #fff;
        overflow: hidden;
        overflow-y: auto;
    }
}
</style>
