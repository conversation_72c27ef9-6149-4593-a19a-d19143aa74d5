<template>
    <div class="policy_check_box">
        <div class="policy_check_policy_box">
            <input v-model="isAgreePolicy" type="checkbox" class="chk" />
            <div
                @click.stop="handleClickPolicy"
                class="policy_check_policy_box_text"
                v-html="
                    $c('form.validate.aggree_policy_new')
                        .replace('AAAA', localePath({ name: 'privacy-notice' }))
                        .replace('BBBB', localePath({ name: 'terms-of-use' }))
                "></div>
        </div>
        <validate-error :error="error"></validate-error>
    </div>
</template>

<script>
import ValidateError from "@/components/ValidateError/ValidateError.vue"
export default {
    model: {
        prop: "value",
        event: "check",
    },
    components: {
        ValidateError,
    },
    props: {
        error: {
            type: String,
            default: "",
        },
        value: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {}
    },
    computed: {
        isAgreePolicy: {
            get() {
                return this.value
            },
            set(val) {
                this.$emit("check", val)
                this.$emit("change", val)
            },
        },
    },
    methods: {
        handleClickPolicy(e) {
            this.$emit("clickPolicyText", e)
        },
    },
}
</script>

<style lang="scss" scoped>
.policy_check_box {
    @include font12;
    margin: 16px 0;
    color: $textColor3;
}
.policy_check_policy_box {
    display: flex;
    @include font12;
    color: $textColor3;
    .chk {
        margin-top: 4px;
        margin-right: 8px;
        width: 14px;
        height: 14px;
        font-size: 14px;
    }
}
</style>
