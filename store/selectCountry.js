import { Base64 } from "js-base64"
import { SELECT_STATE_COUNTRY_CODES } from "@/constants/common"
import { setCookieOptions, removeQueryParams } from "@/util/util"
export const state = () => ({
    current_country_code: "", //当前国家 iso_code
    current_country_id: "", //当前国家 id
    current_country_name: "", //当前国家名
    current_currency: "", //当前货币
    current_language: "", //当前语言
    current_symbol: "", //当前货币符号
    current_tel_prefix: "", //当前电话区号
    country_list: [], //所有国家列表
    province_list: [], //所有州列表
    site_relation: [], //所有货币语言
    state_list: [], //当前国家的州信息
    select_state_name: "", //当前选择的州 name
    select_state_code: "", //当前选择的州 code
    select_country_code: "", //选择后国家iso_code    初始时等于current_country_code
    select_country_id: "", //选择后国家id   初始时等于current_country_id
    select_country_name: "", //选择后国家名      初始时等于current_country_name
    select_language: "", //选择后语言    初始时等于current_language
    select_currency: "", //选择后货币   初始时等于current_currency
    select_tel_prefix: "", //选择后电话区号    初始时等于current_tel_prefix
    select_symbol: "", //选择后货币符号    初始时等于current_symbol
    sort_length: 0, //有些情况下 热门国家和普通国家之间会有分割线，分割线所在length,
    select_language_currency: "",
    exclude_current_country_code: [],
    countryArealist: [],
    siteMainRequest: 0,
    city_list: [], //当前选择的州下所有城市列表,
    countryNameList: {},
})

export const getters = {
    relation_list: (state) => {
        let arr = []
        if (state.site_relation && state.site_relation.length) {
            state.site_relation.map((item) => {
                arr.push(`${item.language} / ${item.symbol} ${item.currency}`)
            })
        }
        return arr
    },
    exclude_current_country_code: (state) => {
        let arr = []
        if (state.country_list && state.country_list.length) {
            for (let i = 0; i < state.country_list.length; i++) {
                if (state.country_list[i].iso_code !== state.current_country_code) {
                    arr.push(state.country_list[i].iso_code)
                }
            }
        }
        return arr
    },
    isUS: (state) => state.select_country_code === "US",
    // isShowState: (state) => SELECT_STATE_COUNTRY_CODES.includes(state.select_country_code),
    // 新需求，根据州列表是否为空，显示或隐藏州列表
    isShowState: (state) => {
        console.log("------州列表长度", state.state_list)
        return state.state_list.length > 0
    },
    isShowCitySelect: (state) => {
        return state.city_list.length > 0
    },
    country_label: (state, getters) => (getters.isShowState ? "pages.CaseDetail.detailsPage.emailUs.country" : "single.ContactSales.CountryRegion"),
    you_country_label: (state, getters) => (getters.isShowState ? "form.form.your_country" : "form.form.your_country_region"),
}

export const mutations = {
    setCurrentCountryCode(state, c) {
        state.current_country_code = c
    },
    setCurrentCountryId(state, c) {
        state.current_country_id = c
    },
    setCurrentCountryName(state, c) {
        state.current_country_name = c
    },
    setCurrentCureency(state, c) {
        state.current_currency = c
    },
    setCurrentLanguage(state, c) {
        state.current_language = c
    },
    setCurrentTelPrefix(state, c) {
        state.current_tel_prefix = c
    },
    setCurrentSymbol(state, c) {
        state.current_symbol = c
    },
    setSelectCountryCode(state, c) {
        state.select_country_code = c
    },
    setSelectCountryId(state, c) {
        state.select_country_id = c
    },
    setSelectCountryName(state, c) {
        state.select_country_name = c
    },
    setSelectTelprefix(state, t) {
        state.select_tel_prefix = t
    },
    setSelectLanguage(state, t) {
        state.select_language = t
    },
    setSelectCurrency(state, t) {
        state.select_currency = t
    },
    setCountryList(state, a) {
        state.country_list = a
    },
    setProvinceList(state, a) {
        state.province_list = a
    },
    setSiteRelation(state, a) {
        // state.site_relation = null;
        state.site_relation = a
    },
    setSelectSymbol(state, c) {
        state.select_symbol = c
    },
    setSortLength(state, a) {
        state.sort_length = a
    },
    setStateList(state, a) {
        state.state_list = a
    },
    setSelectStateName(state, a) {
        state.select_state_name = a
    },
    setSelectStateCode(state, a) {
        state.select_state_code = a
    },
    selectLanguageCurrency(state, a) {
        state.select_language_currency = a
    },
    setCountryArealist(state, arr) {
        // state.countryArealist.splice(0, state.countryArealist.length, ...arr)

        state.countryArealist = arr
    },
    setSiteMainRequest(state) {
        state.siteMainRequest = 1
    },
    setSiteMainRequestFailed(state) {
        state.siteMainRequest = 0
    },
    setCountryNameList(state, o) {
        state.countryNameList = o
    },
    setCityList(state, a) {
        if (a.length > 0) {
            state.city_list = a.map((item) => {
                return {
                    name: item.city,
                    value: item.city_code,
                }
            })
            return
        }
        state.city_list = a
    },
}

export const actions = {
    getCountryArea({ state, commit }) {
        if (state.countryArealist && !state.countryArealist.length) {
            return this.$axios
                .get("/api/website/siteRelationNew")
                .then((res) => {
                    commit("setCountryArealist", res.data.list)
                })
                .catch((err) => {
                    console.log(err)
                })
        }
    },
    getCountryNameList({ state, commit }) {
        return this.$axios
            .get("/api/website/getCountries")
            .then((res) => {
                commit("setCountryNameList", res.data)
            })
            .catch((err) => {
                console.log(err)
            })
    },
    getCountry({ state, commit }) {
        if (!state.siteMainRequest && state.country_list && !state.country_list.length) {
            commit("setSiteMainRequest")
            return this.$axios
                .get("/api/website/siteMain")
                .then((res) => {
                    commit("setCurrentCountryCode", res.data.currentCountryCode)
                    commit("setCurrentCountryId", res.data.currentCountryId)
                    commit("setCurrentCountryName", res.data.currentCountryName)
                    commit("setCurrentCureency", res.data.currentCurrency)
                    commit("setCurrentLanguage", res.data.currentLanguage)
                    commit("setCurrentTelPrefix", res.data.tel_prefix)
                    commit("setCurrentSymbol", res.data.currentSymbol)
                    commit("setSelectCountryCode", res.data.currentCountryCode)
                    commit("setSelectCountryId", res.data.currentCountryId)
                    commit("setSelectCountryName", res.data.currentCountryName)
                    commit("setSelectCurrency", res.data.currentCurrency)
                    commit("setSelectLanguage", res.data.currentLanguage)
                    commit("setSelectTelprefix", res.data.tel_prefix)
                    commit("setCountryList", res.data.countryList.source)
                    commit("setProvinceList", res.data.list)
                    commit("setSortLength", res.data.countryList.sortLength)
                    commit("setSiteRelation", res.data.siteRelation)
                    commit("setSelectSymbol", res.data.currentSymbol)
                    commit("selectLanguageCurrency", `${res.data.currentLanguage} / ${res.data.currentSymbol} ${res.data.currentCurrency}`)
                    if (res.data.countryList.source && res.data.countryList.source.length && res.data.currentCountryCode) {
                        let c = res.data.countryList.source,
                            d = res.data.currentCountryCode
                        for (let i = 0; i < c.length; i++) {
                            if (c[i].iso_code === d) {
                                commit("setStateList", c[i].states || [])
                                commit("setCityList", c[i].citys || [])
                                break
                            }
                        }
                    }
                })
                .catch((err) => {
                    console.log(err)
                    commit("setSiteMainRequestFailed")
                })
        }
    },
    async updateSiteInfo({ rootState, commit }, payload) {
        /**
         * payload 	必传三个参数   iso_code,language,currency
         * 			cllback  接口请求成功后回调函数，选传
         */
        let { iso_code, language, currency, isFetch = true } = payload
        const oldWebsite = rootState.webSiteInfo.website
        let responseWebsite = {}

        if (isFetch) {
            const obj = {
                iso_code,
                language,
                currency,
            }
            try {
                const data = await this.$axios.post("/api/website/updateSiteInfo", obj)
                console.log(data)
                responseWebsite = data.data
            } catch (error) {
                console.log("error:" + error)
            }
        } else {
            responseWebsite = payload
        }

        console.log("__++__")
        console.log(responseWebsite)

        if (responseWebsite) {
            if (payload.callback) {
                payload.callback()
            }

            this.$cookies.set("updateWebsiteInfoFlag", 1)

            let p = rootState.meta.domain

            if (["prod"].includes(process.env.ENV)) {
                if (["cn", "hk", "tw", "mo"].includes(responseWebsite.website)) {
                    p = `https://cn.fs.com${responseWebsite.website === "cn" ? "" : `/${responseWebsite.website}`}`
                }
            } else if (["prod_cn_local"].includes(process.env.ENV)) {
                if (!["cn", "hk", "tw", "mo"].includes(responseWebsite.website)) {
                    p = `https://www.fs.com/${responseWebsite.website === "en" ? "" : `/${responseWebsite.website}`}`
                }
            }

            // if (
            // 	(["prod_cn_local"].includes(process.env.ENV) &&
            // 		oldWebsite === "cn" &&
            // 		oldWebsite !== responseWebsite.website) ||
            // 	(!["prod_cn_local"].includes(process.env.ENV) &&
            // 		oldWebsite === "en" &&
            // 		oldWebsite !== responseWebsite.website)
            // ) {
            // 	p += this.$route.fullPath;
            // } else {
            // 	if (oldWebsite !== responseWebsite.website) {
            // 		p += this.$route.fullPath;
            // 	}
            // }else{

            // }

            p += location.pathname
            p = removeQueryParams(p, ["country", "currency", "languages"])

            commit("webSiteInfo/setWebSiteInfo", responseWebsite, { root: true })
            this.$cookies.set("fs_websiteinfo", responseWebsite)
            this.$cookies.set("preWebsiteInfoNew", responseWebsite)
            // this.$cookies.set("website", responseWebsite.website)
            // this.$cookies.set("iso_code", responseWebsite.iso_code)
            // this.$cookies.set("language", responseWebsite.language)
            // this.$cookies.set("currency", responseWebsite.currency)

            console.log("linklink_link")
            console.log(this.$localeLink)
            console.log(p)
            console.log(responseWebsite.website)
            console.log(this.$localeLink(p, responseWebsite.website))
            // if (process && process.env && process.env.ENV && ["prod", "prod_cn", "prod_cn_local", "release_hk"].includes(process.env.ENV)) {
            //     if (["prod"].includes(process.env.ENV)) {
            //         if (webSiteInfo.website === this.$i18n.locale) {
            //             p = location.href.replace(/[?&](country|currency|languages)=[^&]*/g, "")
            //         } else {
            //             if (["feisu.com", "cn", "hk", "tw", "mo"].includes(webSiteInfo.website)) {
            //                 // let p = process.env.ENV === "prod_cn" ? `https://cn.fs.com` : `https://cn.whgxwl.com`
            //                 p = `https://cn.fs.com`
            //                 if (webSiteInfo.website === "tw") {
            //                     p += "/tw"
            //                 } else if (webSiteInfo.website === "hk") {
            //                     p += "/hk"
            //                 } else if (webSiteInfo.website === "mo") {
            //                     p += "/mo"
            //                 }

            //                 p += this.switchLocalePath(this.$i18n.defaultLocale)
            //                 p = p.replace(/[?&](country|currency|languages)=[^&]*/g, "")
            //             } else {
            //                 p = location.origin + this.switchLocalePath(webSiteInfo.website)
            //                 p = p.replace(/[?&](country|currency|languages)=[^&]*/g, "")
            //             }
            //         }
            //     } else if (["prod_cn", "prod_cn_local", "release_hk"].includes(process.env.ENV)) {
            //         if (webSiteInfo.website === this.$i18n.locale) {
            //             p = location.href.replace(/[?&](country|currency|languages)=[^&]*/g, "")
            //         } else {
            //             if (["feisu.com", "cn", "hk", "tw", "mo"].includes(webSiteInfo.website)) {
            //                 p = location.origin + this.switchLocalePath(["feisu.com", "cn"].includes(webSiteInfo.website) ? "cn" : webSiteInfo.website)
            //             } else {
            //                 if (["release_hk"].includes(process.env.ENV)) {
            //                     p = `https://www.fs.com/?country=${webSiteInfo.iso_code}&currency=${webSiteInfo.currency}&languages=${webSiteInfo.language}`
            //                 } else {
            //                     p = `https://www.fs.com${webSiteInfo.website === "en" ? "" : `/${webSiteInfo.website}`}`
            //                     p += this.switchLocalePath("cn")
            //                     p = p.replace(/[?&](country|currency|languages)=[^&]*/g, "")
            //                 }
            //             }
            //         }
            //     }
            // } else {
            //     console.log("55555555555555555")
            //     p = location.origin + this.switchLocalePath(["feisu.com", "cn"].includes(webSiteInfo.website) ? "cn" : webSiteInfo.website)
            //     console.log(p)
            //     p = p.replace(/[?&](country|currency|languages)=[^&]*/g, "")
            // }
            setTimeout(() => {
                window.location.href = this.$localeLink(p, responseWebsite.website)
            }, 0)
        }
    },
}
