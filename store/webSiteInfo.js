import { setCookieOptions } from "@/util/util"

const languageEnum = {
    English: "en",
    Español: "es",
    Français: "fr",
    Pусский: "ru",
    Deutsch: "de",
    日本語: "jp",
    Italiano: "it",
    简体中文: "sc",
    繁体中文: "tc",
}

export const state = () => ({
    id: "",
    iso_code: "",
    currency: "",
    country_name: "",
    urlWebsite: "",
    website: "",
    language: "",
    countries_id: "",
    language_id: "",
    warehouse: "",
    isEuUnion: false,
    locale: "",
    tel_prefix: "",
    timezone: "",
    symbol: "",
    ip: "",
    pre_url: "",
    referer: "",
    showCookieTip: false,
    showCookieTipOld: false,
    seoLangCode: "en-US",
    cache_file_name: "",
    nuxt_server_init_time: "",
    //欧洲站点，用户是否同意营销类sdk加载
    // isLoadAdSdk: true,
    gpcEnabled: false,
    fsMarketingSdk: false,
    cookiesTipHidden: false,
})

// 俄罗斯的国家ID
const RUSSIA_COUNTRIES_ID = 176
// 印度国家id
const INDIA_COUNTRIES_ID = 99

export const getters = {
    community_website: (state) => {
        let str = ""
        if (state.website) {
            if (["en", "uk", "au", "sg", "de-en"].includes(state.website)) {
                str = ""
            } else if (state.website === "mx") {
                str = `/es`
            } else if (["tw", "mo"].includes(state.website)) {
                str = `/hk`
            } else {
                str = `/${state.website}`
            }
        }
        return str
    },
    communityWebsite: (state) => {
        let str = ""
        if (state.website) {
            if (["en", "uk", "au", "sg", "de-en"].includes(state.website)) {
                str = ""
            } else if (state.website === "mx") {
                str = `es`
            } else if (["tw", "mo"].includes(state.website)) {
                str = `hk`
            } else {
                str = `${state.website}`
            }
        }
        return str
    },
    website_prefix: (state) => {
        let w = ""

        console.log("111_wwww")
        console.log(w)
        console.log(process?.env?.ENV)
        if (state.website && process?.env?.ENV) {
            if (process.env.ENV === "prod" && state.website === "en") {
                w = ""
            } else if (process.env.ENV === "prod_cn_local" && state.website === "cn") {
                w = ""
            } else {
                w = `/${state.website}`
            }
        }
        return w
    },
    isRussia: (state) => state.countries_id === RUSSIA_COUNTRIES_ID,
    isIndiaCountry: (state) => state.countries_id === INDIA_COUNTRIES_ID,
    isSingaporeCountry: (state) => state.countries_id === 188, // 新加坡国家
    isAustraliaCountry: (state) => state.countries_id === 13, // 澳大利亚国家
    isJpCountry: (state) => state.countries_id === 107, // 日本国家
    isCn: (state) => state.website === "cn",
    isSingapore: (state) => state.website === "sg",
    isCnTr: (state) => ["hk", "tw", "mo"].includes(state.website),
    isJp: (state) => ["jp"].includes(state.website),
    isJpEn: (state) => !["jp"].includes(state.website) && state.countries_id === 107,
    isLoadAdSdk: (state) => {
        // 1. 如果是美国且开启了GPC，直接拒绝
        if (state.iso_code === "US" && state.gpcEnabled) {
            return false
        }

        // 2. 如果用户明确拒绝了营销SDK（无论哪个国家），都应该返回false
        if (!state.fsMarketingSdk) {
            return false
        }

        // 3. 处理欧洲站点cookies合规问题
        const EuropeSiteMap = ["de-en", "de", "fr", "es", "it"]
        if (EuropeSiteMap.includes(state.website)) {
            // 欧洲站点必须明确同意才能加载
            return state.fsMarketingSdk === "yes"
        }

        // 4. 其他地区，如果用户同意了就加载
        return state.fsMarketingSdk === "yes"
    },
}

export const mutations = {
    setWebSiteInfo(state, p = {}) {
        state.id = p.id || ""
        state.iso_code = p.iso_code || ""
        state.currency = p.currency || ""
        state.country_name = p.country_name || ""
        state.website = p.website || ""
        state.language = p.language || ""
        state.countries_id = p.countries_id || ""
        state.language_id = p.language_id || ""
        state.warehouse = p.warehouse || ""
        state.isEuUnion = p.isEuUnion || ""
        state.locale = p.locale || ""
        state.tel_prefix = p.tel_prefix || ""
        state.timezone = p.timezone || ""
        state.symbol = p.symbol || ""
        state.seoLangCode = p.seo_lang_code || "en_US"
        state.cache_file_name = p.language && p.iso_code && p.currency ? `${languageEnum[p.language]}_${p.iso_code}_${p.currency}` : ``
        // this.$cookies.set("fs_websiteinfo", p)
        // this.$cookies.set("website", p.website)
        // this.$cookies.set("iso_code", p.iso_code)
        // this.$cookies.set("language", p.language)
        // this.$cookies.set("currency", p.currency)
    },
    setNuxtServerInit(state, t) {
        state.nuxt_server_init_time = t
    },

    setUrlWebsite(state, t) {
        state.urlWebsite = t
    },
    setIp(state, s) {
        state.ip = s
        this.$cookies.set("ip", s)
    },
    setPreurl(state, s) {
        state.pre_url = s
    },
    setReferer(state, s) {
        state.referer = s
    },
    setShowCookieTip(state, s) {
        state.showCookieTip = s
    },
    setFsMarketingSdk(state, s) {
        state.fsMarketingSdk = s
    },
    setCookiesTipHidden(state, s) {
        state.cookiesTipHidden = s
    },
    setIsLoadAdSdk(state, s) {
        state.isLoadAdSdk = s
    },
    setShowCookieTipOld(state, s) {
        state.showCookieTipOld = s
    },
    setGpcEnabled(state, s) {
        state.gpcEnabled = s
    },
}
