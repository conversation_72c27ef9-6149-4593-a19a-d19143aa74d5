export const actions = {
    nuxtServerInit({ dispatch, commit }, { app, route, req, env, res, $c }) {
        let webp = req && req.headers && req.headers.accept ? req.headers.accept.includes("image/webp") : false
        let ua = req.headers["user-agent"] ? req.headers["user-agent"].toLowerCase() : ""
        console.log("store_index")
        const gpcEnabled = req.headers["sec-gpc"] === "1"
        if (req && req.headers) {
            let url = (env.NODE_ENV === "development" ? `http://` : `https://`) + req.headers.host
            commit("meta/setDomain", url)
        }
        if (env.NUXT_VERSION) {
            commit("device/setAppVersion", env.NUXT_VERSION)
        }
        // let time = new Date().getTime()
        // let fs_websiteinfo = app.$cookies.get("fs_websiteinfo")
        // let iso_code = app.$cookies.get("iso_code")
        let locale = app.i18n.locale === "eu-en" ? "de-en" : app.i18n.locale
        // let webSiteInfo = fs_websiteinfo || {
        //     website: locale
        // }

        //处理欧洲站点cookies合规问题
        const EuropeSiteMap = ["de-en", "de", "fr", "es", "it", "nl", "uk"]
        const website = locale //app.$cookies.get("website")
        // 获取用户是否已同意可设置的sdk
        const fs_marketing_sdk = app.$cookies.get("fs_marketing_sdk")
        const cookieconsent_dismissed = app.$cookies.get("cookieconsent_dismissed")

        // 如果用户已经做出选择但没有营销SDK cookie，说明用户拒绝了
        let fsMarketingSdkValue
        if (cookieconsent_dismissed === "yes" && !fs_marketing_sdk) {
            fsMarketingSdkValue = false // 明确设置为false表示用户拒绝
        } else if (fs_marketing_sdk === "yes") {
            fsMarketingSdkValue = true
        } else {
            fsMarketingSdkValue = undefined // 用户尚未做出选择
        }

        commit("webSiteInfo/setFsMarketingSdk", fsMarketingSdkValue)

        // 改进的弹窗显示逻辑：即使website cookie丢失也要显示弹窗
        // 只有当用户明确同意时才不显示弹窗
        // 优化后的弹窗显示逻辑：正确处理cookieconsent_dismissed的五种状态
        // undefined: 初始状态,Cookie未设置 - 显示弹窗
        // null：重置状态，重新显示Cookie提示 - 显示弹窗
        // "yes"：用户同意，设置相关Cookie - 不显示弹窗
        // "rejected"：用户拒绝，清除相关Cookie - 不显示弹窗
        // "dismissed"：用户关闭，已处理但未明确表态 - 不显示弹窗
        let showCookieTip = false
        let showCookieTipOld = false

        // 只有在 undefined 或 null 的情况下才显示弹窗
        if (cookieconsent_dismissed === undefined || cookieconsent_dismissed === null) {
            // 用户尚未做出任何选择，需要显示弹窗
            if (website && EuropeSiteMap.includes(website)) {
                // 已知是欧洲站点
                showCookieTip = true
            } else if (website && !EuropeSiteMap.includes(website)) {
                // 已知是非欧洲站点
                showCookieTipOld = true
            } else if (!website) {
                // website cookie丢失，为了安全起见，显示通用弹窗
                // 可以根据实际需求选择显示哪种弹窗，这里选择显示旧版弹窗作为默认
                showCookieTipOld = true
            }
        }
        // 对于 "yes"、"rejected"、"dismissed" 三种状态，showCookieTip 和 showCookieTipOld 都保持 false

        commit("device/setWebp", webp)
        commit("device/setDevice", /mobile|android|iphone|ipad|phone/i.test(ua))
        // commit("device/setIsPc", !(/mobile|android|iphone|ipad|phone/i.test(ua)));
        // commit("webSiteInfo/setWebSiteInfo", webSiteInfo)
        // commit("webSiteInfo/setNuxtServerInit", time)
        // commit("webSiteInfo/setIsLoadAdSdk", isLoadSdk)
        // commit("webSiteInfo/setWebSiteInfo", webSiteInfo)
        // commit("webSiteInfo/setNuxtServerInit", time)
        // commit("webSiteInfo/setIsLoadAdSdk", isLoadSdk)
        // 如果是欧洲的话，而且cookieconsent_dismissed没有指的时候，那么就展示tips
        commit("webSiteInfo/setFsMarketingSdk", fs_marketing_sdk)
        commit("webSiteInfo/setCookiesTipHidden", cookieconsent_dismissed)
        commit("webSiteInfo/setShowCookieTip", showCookieTip)
        //如果是非欧洲的话，处理展示默认的cookies提示
        commit("webSiteInfo/setShowCookieTipOld", showCookieTipOld)
        commit("webSiteInfo/setGpcEnabled", gpcEnabled)

        return Promise.all([dispatch("cart/getCart"), dispatch("userInfo/getUserInfo"), dispatch("category/getCategory"), dispatch("selectCountry/getCountryNameList")])
    },
}
