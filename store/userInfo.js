import { setCookieOptions } from "@/util/util"

export const state = () => ({
    isLogin: 0, //登录状态 0：未登录   1：已登录,
    isFirstLogin: 0,
    savedCarts: 0,
    userInfo: null, //用户信息
    activeQuotes: 0,
    isCNRegion: false,
    managerInfo: null,
    system_messages: null,
    isShowMore: false,
    readStatus: false,
})

export const getters = {
    isStaff: (state, getters, rootState) => {
        // 0为正常用户   1位公司员工
        let isStaff = 0
        let devideLang = rootState.device.deviceLang
        let mails = ["@fiberstore.com", "@fs.com", "@szyuxuan.com", "@feisu.com", "@ruijie.com.cn", "@centecnetworks.com", "@qq.com", "@163.com", "@126.com"]
        if (state.isLogin) {
            let customers_email_address = state.userInfo && state.userInfo.customers_email_address ? state.userInfo.customers_email_address : ""
            customers_email_address = customers_email_address.substring(customers_email_address.indexOf("@"), customers_email_address.length)
            if (mails.includes(customers_email_address)) {
                isStaff = 1
            } else {
                isStaff = 0
            }
        } else {
            if (devideLang === "zh-cn" || devideLang === "zh") {
                isStaff = 1
            } else {
                isStaff = 0
            }
        }
        let s = isStaff ? "internalTraffic" : "nonInternalTraffic"
        return isStaff
    },
    gaLoginString: (state, getters, rootState) => {
        // 0为正常用户   1位公司员工
        let isStaff = 0
        let devideLang = rootState.device.deviceLang
        let mails = ["@fiberstore.com", "@fs.com", "@szyuxuan.com", "@feisu.com", "@ruijie.com.cn", "@centecnetworks.com", "@qq.com", "@163.com", "@126.com"]
        if (state.isLogin) {
            let customers_email_address = state.userInfo && state.userInfo.customers_email_address ? state.userInfo.customers_email_address : ""
            customers_email_address = customers_email_address.substring(customers_email_address.indexOf("@"), customers_email_address.length)
            if (mails.includes(customers_email_address)) {
                isStaff = 1
            } else {
                isStaff = 0
            }
        } else {
            if (devideLang === "zh-cn" || devideLang === "zh") {
                isStaff = 1
            } else {
                isStaff = 0
            }
        }
        let s = isStaff ? "internalTraffic" : "nonInternalTraffic"
        return s
    },
    isShowPurchaseInlet: (state, getters, rootState) => state.userInfo?.isCompanyOrganizationUser && state.userInfo?.companyInfo?.is_show_purchase_inlet,
}

export const mutations = {
    setUserInfo(state, o) {
        state.isLogin = o.isLogin
        state.isFirstLogin = o.isFirstLogin
        state.savedCarts = o.savedCarts || 0
        state.userInfo = o.user ? o.user : null
        state.activeQuotes = o.activeQuotes || 0
        state.managerInfo = o.managerInfo || {}
        state.purchaseStatus = o.purchaseStatus
        state.isCNRegion = o.isCNRegion
        state.system_messages = o.system_messages
        state.isShowMore = o.isShowMore
        this.$cookies.set("customers_number_new", o.user ? o.user.customers_number_new : "")
        if (!o.isLogin) {
            this.$cookies.remove("token_new")
        }
    },
    resetUserInfo(svtate) {
        state.isLogin = 0
        state.isFirstLogin = 0
        state.savedCarts = 0
        state.savedCarts = null
        state.activeQuotes = 0
        state.managerInfo = {}
        state.isShowMore = false
        this.$cookies.set("customers_number_new", "")
        this.$cookies.remove("token_new")
    },
    setSystemMessages(state, o) {
        state.readStatus = o.status
    },
}

export const actions = {
    getUserInfo({ state, commit }, cb) {
        //获取用户信息
        return this.$axios
            .post("/api/user/info?getMore=1&get_system_messages=1")
            .then((res) => {
                commit("setUserInfo", res.data)
                if (cb) {
                    cb()
                }
            })
            .catch((err) => {
                if (cb) {
                    cb()
                }
            })
    },
    // 获取用户站内信状态
    getUserSystemMessages({ state, commit }) {
        return this.$axios
            .get("/api/message/expirationMessageRedDot")
            .then((res) => {
                commit("setSystemMessages", res.data)
            })
            .catch((err) => {
                console.log(err.code)
            })
    },
}
