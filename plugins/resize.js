import { isMobile, isWechat, generateUUID, setCookieOptions } from "@/util/util"

export default function ({ app, store, route, isHMR }) {
    store.commit("device/setDevice", isMobile())
    store.commit("device/setIsWechat", isWechat())
    store.commit("device/setScreenWidth", document.body.clientWidth)
    store.commit("device/setScreenHeight", document.documentElement.clientHeight)
    store.commit("device/setDeviceLang", window.navigator.language ? window.navigator.language.toLowerCase() : "")
    let _fs_pvid = generateUUID()

    console.log("resize_resize_111")
    console.log(store.getters["webSiteInfo/isLoadAdSdk"])
    if (store.getters["webSiteInfo/isLoadAdSdk"]) {
        app.$cookies.set("_fs_pvid", _fs_pvid)
    }
    app.$cookies.remove("token")
    app.$cookies.remove("token")
    app.$cookies.remove("webSiteInfo")
    app.$cookies.remove("webSiteInfo")
    if (!isHMR) {
        window.addEventListener("resize", () => {
            console.log(store.state.webSiteInfo)
            store.commit("device/setDevice", isMobile())
            store.commit("device/setScreenWidth", document.body.clientWidth)
            store.commit("device/setScreenHeight", document.documentElement.clientHeight)
        })
        window.addEventListener("beforeunload", (event) => {
            app.$cookies.set("preWebsiteInfoNew", app.$cookies.get("fs_websiteinfo"))
        })
        // let last = 0
        // window.addEventListener("scroll", debounce(function () {
        // 	let last = store.state.device.gaScroll || 0;
        // 	let h = document.body.clientHeight || document.documentElement.clientHeight;
        // 	let st = (document.documentElement.scrollTop || document.body.scrollTop) + window.innerHeight;
        // 	let pe = `${parseInt((st * 100 / h))}%`
        // 	if (st > last) {
        // 		// last = st;
        // 		store.commit("device/setGaScroll", st);
        // 		console.log(pe)
        // 		if (window.dataLayer) {
        // 			window.dataLayer.push({
        // 				"event": "uaEvent",
        // 				"eventCategory": store.state.ga.pageGroup,
        // 				"eventAction": "scroll",
        // 				"eventLabel": pe,
        // 				"nonInteraction": true
        // 			});
        // 		}
        // 	}
        // }, 500));
    }
}
