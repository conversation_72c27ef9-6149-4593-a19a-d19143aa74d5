import { Base64 } from "js-base64"
import createHmac from "create-hmac"
import { isObject } from "@/util/types"
import { api_config } from "@/config.js"
import { setCookieOptions, getGrecaptchaResponeString } from "@/util/util"
import { getCanvasFingerprinting } from "@/util/canvasFingerprinting.js"

function genNonce(num) {
    let chars = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz"
    let str = ""
    for (var i = 0; i < num; i++) {
        let pos = Math.round(Math.random() * (chars.length - 1))
        str += chars[pos]
    }
    return str
}

export default function ({ app, $axios, redirect, route, query, store, $winstonLog, env }) {
    // console.log("axiosaxiosaxiosaxios")
    // console.log(env)
    // console.log("axiosaxiosaxiosaxios")
    $axios.defaults.retry = 1 //请求重试次数
    // $axios.defaults.retryDelay=1  //请求重试间隔时间
    $axios.defaults.timeout = 180000
    $axios.interceptors.request.use(
        (config) => {
            config.headers.Accept = "application/json"
            let timestamps = new Date().getTime(),
                nonce = genNonce(16),
                apiKey = "yuxuanxuanpc",
                payload = "[]"
            let s = timestamps + nonce + payload
            let hmac = createHmac("sha256", "yuxuan3507")
            hmac.update(s)
            let signature = Base64.encode(hmac.digest("hex"))
            let token = app.$cookies.get("token_new") || query.token || ""
            let cartId = app.$cookies.get("cartId")
            let temporaryCartId = app.$cookies.get("temporaryCartId")
            const canvasFingerprinting = getCanvasFingerprinting()
            if (token) {
                config.headers.Authorization = token
            }
            if (cartId) {
                config.headers.cartId = cartId
            }
            if (canvasFingerprinting) {
                config.headers["fs-client-id"] = canvasFingerprinting
            }
            if (temporaryCartId) {
                config.headers.temporaryCartId = temporaryCartId
            }
            config.headers.timestamps = timestamps
            config.headers.nonce = nonce
            config.headers.apiKey = apiKey
            config.headers.clientSignature = signature
            config.headers.supportWebp = store.state.device.supportWebp
            if (query?.shareUrlId) {
                config.headers.shareUrlId = query.shareUrlId
            }
            let locale = app.i18n.locale === "eu-en" ? "de-en" : app.i18n.locale

            console.log("ddddddddddddddddddddd__111")
            console.log(locale)
            console.log("ddddddddddddddddddddd")

            let requestWebSiteInfo = {}

            const cookieWebsiteInfo = app.$cookies.get("fs_websiteinfo")
            // if (!["en", "au", "de", "de-en", "es", "fr", "it", "jp", "mx", "ru", "sg", "uk", "cn","hk","tw","mo"].includes(local)) {
            // 	if (env && env.ENV === "prod_cn") {
            // 		local = "cn"
            // 	}else{
            // 		local = "en"
            // 	}
            // }

            if (locale) {
                if (query?.country && query?.currency && query?.languages) {
                    requestWebSiteInfo = {
                        isCookie: true,
                        language: query.languages,
                        iso_code: query.country,
                        currency: query.currency,
                    }
                } else {
                    if (cookieWebsiteInfo?.website && cookieWebsiteInfo?.language && cookieWebsiteInfo?.iso_code && cookieWebsiteInfo?.currency) {
                        if (locale === "en" || cookieWebsiteInfo?.website === locale) {
                            requestWebSiteInfo = {
                                isCookie: true,
                                website: cookieWebsiteInfo?.website === "eu-en" ? "de-en" : cookieWebsiteInfo?.website,
                                iso_code: cookieWebsiteInfo?.iso_code,
                                currency: cookieWebsiteInfo?.currency,
                                language: cookieWebsiteInfo?.language,
                                language_id: cookieWebsiteInfo?.language_id,
                                locale: cookieWebsiteInfo?.locale,
                                countries_id: cookieWebsiteInfo?.countries_id,
                                warehouse: cookieWebsiteInfo?.warehouse,
                                country_name: cookieWebsiteInfo?.country_name,
                            }
                        } else {
                            requestWebSiteInfo = {
                                isCookie: false,
                                website: locale,
                            }
                        }
                    } else {
                        if (!((env?.ENV === "prod_cn_local" && locale === "cn") || (env?.ENV !== "prod_cn_local" && locale === "en"))) {
                            requestWebSiteInfo = {
                                isCookie: false,
                                website: locale,
                            }
                        }
                    }
                }
            }
            // }

            // if (env.ENV === "prod_cn") {
            //     webSiteInfo = {
            //         website: "cn",
            //         isCookie: true,
            //         language: query.languages || "",
            //         iso_code: query.country || "",
            //         currency: query.currency || "",
            //     }
            // }

            console.log("req_req_req")
            console.log(requestWebSiteInfo)
            config.headers.webSiteInfo = Base64.encode(JSON.stringify(requestWebSiteInfo))

            if (process.server) {
                config.headers.is_server = 1
            }
            if (config.url.includes("order-api")) {
                config.headers.prevTraceId = store.state?.prevTraceId?.prevTraceId
            }
            // console.log("112233")
            // console.log(config.url)
            // console.log("112233")
            // if (env && env.NODE_ENV === "production") {
            let geteway_api = api_config[env.ENV].gateway_api_client
            let fs_api = api_config[env.ENV].client

            if (locale === "cn") {
                geteway_api = api_config[env.ENV].cn_gateway_api || api_config[env.ENV].gateway_api_client
                fs_api = api_config[env.ENV]?.cn_fs_api || api_config[env.ENV].client
            }
            if (env.ENV === "prod_cn_local") {
                if (["hk", "tw", "mo"].includes(locale)) {
                    if (/^\/?order-api\//.test(config.url) || /^\/?cms\//.test(config.url)) {
                        config.baseURL = api_config[env.ENV].gateway_api_client_hk
                    } else if (/^\/?api\//.test(config.url)) {
                        config.baseURL = api_config[env.ENV].client_hk
                    }
                } else {
                    if (/^\/?order-api\//.test(config.url)) {
                        config.baseURL = geteway_api
                    } else if (/^\/?cms\//.test(config.url)) {
                        config.baseURL = api_config[env.ENV].gateway_api_client_hk
                    } else if (/^\/?api\//.test(config.url)) {
                        config.baseURL = fs_api
                    }
                }
            } else {
                if (/^\/?order-api\//.test(config.url) || /^\/?cms\//.test(config.url)) {
                    config.baseURL = geteway_api
                } else if (/^\/?api\//.test(config.url)) {
                    config.baseURL = fs_api
                }
            }
            if (config.url.includes("/api/pay/payment/status") || config.url.includes("/api/pay/payone/status") || config.url.includes("/api/pay/paypal/status")) {
                if (env.ENV === "prod" && env.NODE_ENV === "production") {
                    config.baseURL = "https://api.fs.com/"
                }
            }
            return config
        },
        (err) => {
            return Promise.reject(err)
        }
    )
    $axios.interceptors.response.use(
        (res) => {
            console.log("rr_rr_rr")
            console.log(res.config.url)
            console.log("params" + res.config.params)
            console.log("data" + res.config.data)
            let locale = app.i18n.locale === "eu-en" ? "de-en" : app.i18n.locale
            if (res.headers) {
                if (res.headers.authorization) {
                    app.$cookies.set("token_new", res.headers.authorization)
                }
                if (res.headers.cartid) {
                    app.$cookies.set("cartId", res.headers.cartid)
                }
                if (res.headers["content-disposition"]) {
                    app.$cookies.set("contentDisposition", res.headers["content-disposition"])
                }
                if (res.headers.clientip) {
                    store.commit("webSiteInfo/setIp", res.headers.clientip)
                }
                // console.log("res.headers", res.headers)
                if (res.headers.tlogtraceid) {
                    store.commit("prevTraceId/setPrevTraceId", res.headers.tlogtraceid)
                }
                if (res.headers["ip-website-info"] && JSON.parse(Base64.decode(res.headers["ip-website-info"]))) {
                    app.$cookies.set("ipWebsiteInfo", JSON.parse(Base64.decode(res.headers["ip-website-info"])))
                }

                if (res.headers.websiteinfo && JSON.parse(Base64.decode(res.headers.websiteinfo))) {
                    // const websiteCache = !!app.$cookies.get("fs_websiteinfo")
                    let responseWebsite = JSON.parse(Base64.decode(res.headers.websiteinfo))

                    console.log("res_res")
                    console.log(responseWebsite)
                    console.log("res_res")
                    let flag = false
                    let p = store.state.meta.domain
                    let websiteStore = store.state.webSiteInfo
                    console.log("res_store")
                    console.log(websiteStore)
                    if (["prod"].includes(env.ENV)) {
                        if (["cn", "hk", "tw", "mo"].includes(responseWebsite.website)) {
                            p = `https://cn.fs.com${responseWebsite.website === "cn" ? "" : `/${responseWebsite.website}`}`
                        }
                    } else if (["prod_cn_local"].includes(env.ENV)) {
                        if (!["cn", "hk", "tw", "mo"].includes(responseWebsite.website)) {
                            p = `https://www.fs.com/${responseWebsite.website === "en" ? "" : `/${responseWebsite.website}`}`
                        }
                    }

                    if ((["prod_cn_local"].includes(env.ENV) && locale === "cn" && locale !== responseWebsite.website) || (!["prod_cn_local"].includes(env.ENV) && locale === "en" && locale !== responseWebsite.website)) {
                        p += route.fullPath
                        flag = true
                    } else {
                        if (locale !== responseWebsite.website) {
                            p += route.fullPath
                            flag = true
                        }
                    }
                    store.commit("webSiteInfo/setWebSiteInfo", responseWebsite)
                    app.$cookies.set("fs_websiteinfo", responseWebsite)
                    // app.$cookies.set("website", responseWebsite.website)
                    // app.$cookies.set("iso_code", responseWebsite.iso_code)
                    // app.$cookies.set("language", responseWebsite.language)
                    // app.$cookies.set("currency", responseWebsite.currency)
                    console.log("11_22_11")
                    console.log(p)
                    console.log(flag)
                    console.log(app.$localeLink(p, responseWebsite.website))
                    if (flag) {
                        p = app.$localeLink(p, responseWebsite.website)

                        console.log("11_22_11")
                        console.log(p)
                        redirect(p)
                    }

                    // if (process && process.env && process.env.ENV && ["prod", "prod_cn", "prod_cn_local", "release_hk"].includes(process.env.ENV)) {
                    //     if (["prod"].includes(process.env.ENV)) {
                    //         if (webSiteInfo.website === app.i18n.locale) {
                    //             // let p = location.href
                    //             // 	.replace(/country=[^&]*&?/g, "")
                    //             // 	.replace(/currency=[^&]*&?/g, "")
                    //             // 	.replace(/languages=[^&]*&?/g, "")
                    //             // window.location.href = p
                    //         } else {
                    //             if (["feisu.com", "cn", "hk", "tw", "mo"].includes(webSiteInfo.website)) {
                    //                 let p = `https://cn.fs.com`
                    //                 if (webSiteInfo.website === "tw") {
                    //                     p += "/tw"
                    //                 } else if (webSiteInfo.website === "hk") {
                    //                     p += "/hk"
                    //                 } else if (webSiteInfo.website === "mo") {
                    //                     p += "/mo"
                    //                 }
                    //                 // console.log("===============")
                    //                 // console.log(p)
                    //                 p += app.switchLocalePath(app.i18n.defaultLocale)
                    //                 // console.log(p)
                    //                 redirect(p)
                    //             } else {
                    //                 let p = app.switchLocalePath(webSiteInfo.website)
                    //                 redirect(p)
                    //             }
                    //         }
                    //     } else if (["prod_cn", "prod_cn_local", "release_hk"].includes(process.env.ENV)) {
                    //         if (webSiteInfo.website === app.i18n.locale) {
                    //             // let p = location.href
                    //             // 	.replace(/country=[^&]*&?/g, "")
                    //             // 	.replace(/currency=[^&]*&?/g, "")
                    //             // 	.replace(/languages=[^&]*&?/g, "")
                    //             // 	redirect(p)
                    //         } else {
                    //             if (["feisu.com", "cn", "hk", "tw", "mo"].includes(webSiteInfo.website)) {
                    //                 let p = app.switchLocalePath(["feisu.com", "cn"].includes(webSiteInfo.website) ? "cn" : webSiteInfo.website)
                    //                 redirect(p)
                    //             } else {
                    //                 if (["release_hk"].includes(process.env.ENV)) {
                    //                     let p = `https://www.fs.com/?country=${webSiteInfo.iso_code}&currency=${webSiteInfo.currency}&languages=${webSiteInfo.language}`
                    //                     redirect(p)
                    //                 } else {
                    //                     let p = `https://www.fs.com/${webSiteInfo.website === "en" ? "" : webSiteInfo.website}`
                    //                     redirect(p)
                    //                 }
                    //             }
                    //         }
                    //     }
                    // } else {
                    //     if (webSiteInfo.website !== app.i18n.locale) {
                    //         if (process.server) {
                    //             let p = app.switchLocalePath(["feisu.com", "cn"].includes(webSiteInfo.website) ? "cn" : webSiteInfo.website)
                    //             // console.log(p)
                    //             redirect(p)
                    //         }
                    //     }
                    // }

                    // if (["feisu.com","cn"].includes(webSiteInfo.website)) {

                    //     let route_name = route.name
                    //     let url = "https://cn.fs.com/"

                    //     if (/^verify/g.test(route_name)) {
                    //         url += "verify"
                    //     }
                    //     if (/^home/g.test(route_name) || /^verify/g.test(route_name)) {
                    //         if (process.server && env && env.ENV === "prod") {
                    //             redirect(url)
                    //         }
                    //     } else {
                    //         let local = app.i18n.locale
                    //         if (!["en", "au", "de", "de-en", "es", "fr", "it", "jp", "mx", "ru", "sg", "uk", "cn", "hk", "tw", "mo"].includes(local)) {
                    //             local = "en"
                    //         }
                    //         store.commit("webSiteInfo/setWebSiteInfo", { website: local })
                    //     }
                    // } else {
                    //     store.commit("webSiteInfo/setWebSiteInfo", webSiteInfo)
                    //     if (query.country && query.currency && query.languages) {
                    //         if (process.server) {
                    //             if (webSiteInfo.website !== app.i18n.locale) {
                    //                 let url = app.switchLocalePath(webSiteInfo.website)
                    //                 if (url.indexOf("?") !== -1) {
                    //                     let p = url.substr(0, url.indexOf("?"))
                    //                     let q = url.substr(url.indexOf("?") + 1)
                    //                     let arr = q.split("&")
                    //                     let obj = {}
                    //                     arr.map((item) => {
                    //                         obj[item.split("=")[0]] = item.split("=")[1]
                    //                     })
                    //                     delete obj.country
                    //                     delete obj.currency
                    //                     delete obj.languages
                    //                     let str = ``
                    //                     for (let attr in obj) {
                    //                         if (attr) {
                    //                             str += `${attr}=${obj[attr]}&`
                    //                         }
                    //                     }
                    //                     if (str) {
                    //                         str = str.substr(0, str.length - 1)
                    //                         str = `${p}?${str}`
                    //                     } else {
                    //                         str = p
                    //                     }
                    //                     redirect(str)
                    //                 } else {
                    //                     redirect(app.switchLocalePath(webSiteInfo.website))
                    //                 }
                    //             }
                    //         }
                    //     } else {
                    //         if (webSiteInfo.website !== app.i18n.locale) {
                    //             if (process.server) {
                    //                 redirect(app.switchLocalePath(webSiteInfo.website))
                    //             }
                    //         }
                    //     }
                    // }
                }
            }

            if ($winstonLog) {
                $winstonLog.info(`[${res.status}] [${$axios.defaults.baseURL}] [${res.request.path}]`)
            }
            console.log(res.data)
            return Promise.resolve(res.data)
        },
        (err) => {
            console.log("err_rr_rrr")
            console.log("url:" + err?.config?.url)
            console.log(err?.config?.data)
            console.log("websiteinfo:" + err?.config?.headers["webSiteInfo"])
            console.log(err?.response?.data)
            console.log("err_rr_rrr")
            let locale = app.i18n.locale === "eu-en" ? "de-en" : app.i18n.locale
            if (err && err?.response && err?.response.headers) {
                if (err.response.headers.authorization) {
                    app.$cookies.set("token_new", err.response.headers.authorization)
                }
                if (err.response.headers.clientip) {
                    store.commit("webSiteInfo/setIp", err.response.headers.clientip)
                }
                if (err.response.headers?.tlogTraceId) {
                    store.commit("prevTraceId/prevTraceId", err.response.headers.tlogTraceId, setPrevTraceId())
                }
                if (err.response.headers["ip-website-info"] && JSON.parse(Base64.decode(err.response.headers["ip-website-info"]))) {
                    console.log("ip_ip")
                    console.log(err.response.headers["ip-website-info"])
                    app.$cookies.set("ipWebsiteInfo", JSON.parse(Base64.decode(err.response.headers["ip-website-info"])))
                }
                if (err.response.headers.websiteinfo && JSON.parse(Base64.decode(err.response.headers.websiteinfo))) {
                    // const websiteCache = !!app.$cookies.get("fs_websiteinfo")
                    let responseWebsite = JSON.parse(Base64.decode(err.response.headers.websiteinfo))
                    let flag = false
                    let p = store.state.meta.domain
                    let websiteStore = store.state.webSiteInfo

                    if (["prod"].includes(env.ENV) && env.NODE_ENV === "production") {
                        if (["cn", "hk", "tw", "mo"].includes(responseWebsite.website)) {
                            p = `https://cn.fs.com/${responseWebsite.website}`
                        }
                    } else if (["prod_cn_local"].includes(env.ENV) && env.NODE_ENV === "production") {
                        if (!["cn", "hk", "tw", "mo"].includes(responseWebsite.website)) {
                            p = `https://www.fs.com/${responseWebsite.website}`
                        }
                    }

                    if ((["prod_cn_local"].includes(env.ENV) && locale === "cn" && locale !== responseWebsite.website) || (!["prod_cn_local"].includes(env.ENV) && locale === "en" && locale !== responseWebsite.website)) {
                        p += route.fullPath
                        flag = true
                    } else {
                        if (locale !== responseWebsite.website) {
                            p += route.fullPath
                            flag = true
                        }
                    }
                    store.commit("webSiteInfo/setWebSiteInfo", responseWebsite)

                    app.$cookies.set("fs_websiteinfo", responseWebsite)
                    // app.$cookies.set("website", responseWebsite.website)
                    // app.$cookies.set("iso_code", responseWebsite.iso_code)
                    // app.$cookies.set("language", responseWebsite.language)
                    // app.$cookies.set("currency", responseWebsite.currency)

                    if (flag) {
                        p = app.$localeLink(p, responseWebsite.website)
                        console.log("11_22_11")
                        console.log(p)
                        redirect(p)
                    }
                }
                if (err.response.headers.cartid) {
                    app.$cookies.set("cartId", err.response.headers.cartid)
                }
            }
            if ($winstonLog) {
                $winstonLog.error(`[${err.status}] | [${$axios.defaults.baseURL}] |  [${err.request.path}] | ${err.message}`)
                $winstonLog.error(err.response && err.response.data)
            }
            if (process.client && app.$message) {
                // app.$message.error(err.response.data.message)
            }
            if (err.response && err.response.status) {
                switch (err.response.status) {
                    case 404:
                        if (err.config.url) {
                            redirect(app.localePath({ name: "404" }))
                        }
                        break
                    case 401:
                        console.log("112222333___")
                        console.log(err.config.url)
                        app.$cookies.remove("token_new")
                        store.commit("userInfo/resetUserInfo")
                        let q = JSON.parse(JSON.stringify(query)) || {}
                        if (q && q.redirect) {
                            delete q.redirect
                        }
                        q.redirect = route.fullPath
                        redirect(app.localePath({ name: "login", query: q }))
                        break
                    case 422:
                        if (err.response.data && err.response.data.errors && isObject(err.response.data.errors)) {
                            for (let attr in err.response.data.errors) {
                                if (Array.isArray(err.response.data.errors[attr])) {
                                    err.response.data.errors[attr] = err.response.data.errors[attr].join(",")
                                }
                            }
                        }
                        break
                    case 429:
                        if (err.config.url) {
                            redirect(app.localePath({ name: "429" }))
                        }
                        break
                    case 403:
                        if (process.client && err?.response?.data?.message) {
                            app.$message.error(err.response.data.message)
                        }
                        break
                    case 500:
                        if (err.config.url) {
                            // redirect(app.localePath({ name: "500" }))
                        }
                        break
                    case 400:
                        //if (process.server) {
                        return Promise.reject(err.response.data)
                        //}
                        break
                    case 409:
                        let config = err.config
                        if (!config || !config.retry) return Promise.reject(err)
                        config.__retryCount = config.__retryCount || 0
                        if (config.__retryCount >= config.retry) {
                            return Promise.reject(err.response.data)
                        }
                        config.__retryCount = config.__retryCount + 1
                        const backoff = new Promise(function (resolve) {
                            setTimeout(function () {
                                resolve()
                            }, config.retryDelay || 1)
                        })
                        return backoff.then(async () => {
                            const res = await getGrecaptchaResponeString()
                            config.headers["g-recaptcha-response"] = res || ""
                            return $axios(config)
                        })
                    default:
                        if (process.server && err.config.url) {
                            redirect(app.localePath({ name: "500" }))
                        }
                }
                return Promise.reject(err.response.data)
            } else {
                if (err.message.includes("timeout")) {
                    // console.log("网络超时", err)
                }
                return Promise.reject(err)
            }
        }
    )
}
