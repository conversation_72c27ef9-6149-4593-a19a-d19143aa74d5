export default ({ app }, inject) => {
    // 保存原始 $cookies
    const originalCookies = app.$cookies

    // 自定义 set 方法
    const customCookies = {
        ...originalCookies,
        set(key, value, opts = {}) {
            const defaults = {
                path: "/",
            }
            let ENV = process.env.ENV
            let NODE_ENV = process.env.NODE_ENV
            //let NODE_WEBSITE = process.env.NODE_WEBSITE
            if (NODE_ENV === "production") {
                if (["prod", "prod_cn", "prod_cn_local"].includes(ENV)) {
                    defaults.domain = "fs.com"
                } else {
                    defaults.domain = "whgxwl.com"
                }
            }

            if (["fs_marketing_sdk", "fs_google_analytics", "fs_function_cookie", "cookieconsent_dismissed", "cookies_tip_hidden"].includes(key)) {
                defaults.maxAge = 60 * 60 * 24 * 365
            } else if (
                [
                    "website",
                    "language",
                    "currency",
                    "iso_code",
                    "fs_websiteinfo",
                    "cartId",
                    "oldWebsite",
                    "_fs_pvid",
                    "ip",
                    "temporaryCartId",
                    "ipWebsiteInfo",
                    "preWebsiteInfoNew",
                    "updateWebsiteInfoFlag",
                    "recentlyProducts",
                ].includes(key)
            ) {
                defaults.maxAge = 60 * 60 * 24 * 30
            } else if (key.includes("holiday_new") || ["token_new", "customers_number_new"].includes(key)) {
                defaults.maxAge = 60 * 60 * 24 * 7
            }
            if (key === "_fs_pvid") {
                console.log("cookies____", key, value, defaults)
            }

            return originalCookies.set(key, value, { ...defaults, ...opts })
        },
        remove(key, opts = {}) {
            const defaults = { path: "/" }
            let ENV = process.env.ENV
            let NODE_ENV = process.env.NODE_ENV
            if (NODE_ENV === "production") {
                defaults.domain = ["prod", "prod_cn", "prod_cn_local"].includes(ENV) ? "fs.com" : "whgxwl.com"
            }
            return originalCookies.remove(key, { ...defaults, ...opts })
        },
    }

    // 覆盖原有 $cookies
    inject("cookies", customCookies)
}
