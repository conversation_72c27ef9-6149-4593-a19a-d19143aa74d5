const languageEnum = {
    en: 5,
    cn: 14,
    au: 1,
    sg: 2,
    "de-en": 3,
    uk: 4,
    it: 7,
    mx: 8,
    es: 9,
    jp: 10,
    ru: 5,
    fr: 12,
    de: 13,
    tw: 15,
    hk: 16,
    mo: 17,
    nl: 19,
}
const zendeskLocaleMap = {
    en: "en-US",
    au: "en-au",
    sg: "en-sg",
    "de-en": "en-DE",
    uk: "en-gb",
    it: "it",
    mx: "es-mx",
    es: "es",
    jp: "ja",
    ru: "en",
    fr: "fr",
    de: "de",
}
import fixScroll from "@/util/fixScroll"
export default ({ app, store, env, $axios }) => {
    const webSiteConfig = store.state.liveChat.webSiteConfig
    const webSite = (store.state.webSiteInfo.website === "ru" ? "en" : store.state.webSiteInfo.website) || ""
    // 控制站点不需要加载新livechat
    if (webSiteConfig.includes(webSite)) return
    const lcSdkUrl = ["prod", "prod_cn", "prod_cn_local"].includes(env.ENV) ? "https://livechat-front.fs.com/sdk.js" : "https://pre-livechat.whgxwl.com/sdk.js"
    if (process.client) {
        const isoCode = (store.state.webSiteInfo.website === "ru" ? "US" : store.state.webSiteInfo.iso_code) || ""
        const language = languageEnum[webSite] || ""
        const clientToken = app.$cookies.get("token_new") || ""
        const userInfo = store.state.userInfo.userInfo
        let clientUserInfo = ""
        if (clientToken && userInfo) {
            clientUserInfo = {
                email: userInfo?.customers_email_address || "",
                name: (userInfo?.customers_firstname || "") + " " + (userInfo?.customers_lastname || ""),
                mobile: userInfo?.customers_telephone || "",
            }
            clientUserInfo = JSON.stringify(clientUserInfo)
        }
        window.addEventListener("message", handleChatGa)
        function handleChatGa(event) {
            const postMsgData = event.data
            if (postMsgData?.type === "dataLayer") {
                const pageGroup = store.state.ga.pageGroup
                const params = postMsgData.data
                const gaParams = {
                    eventCategory: pageGroup,
                    event: "uaEvent",
                    eventAction: "Live Chat",
                    nonInteraction: false,
                    ...params,
                }
                console.log("------ga-------", gaParams)
                window.dataLayer && window.dataLayer.push(gaParams)
            }
            if (postMsgData?.type === "validate-video-click") {
                console.log("------video-click-------", postMsgData.data)
                const prod_id = postMsgData.data.prod_id
                const url = `${window.location.origin}/products/${prod_id}.html?videoNow=1`
                window.open(url, "_blank")
            }

            const isH5 = document.body.clientWidth < 768
            if (postMsgData === "hide_chat" && isH5) {
                fixScroll.unfixed()
            }
            if (postMsgData?.type === "open_chat" && isH5) {
                fixScroll.fixed()
            }
            if (postMsgData?.type === "unread_message") {
                store.commit("liveChat/setUnreadMsgStatus", postMsgData.data)
            }
        }
        app.router.afterEach((to, from) => {
            if (document.querySelector("iframe.fsLiveChat")) {
                const iframeEle = document.querySelector("iframe.fsLiveChat")
                iframeEle.contentWindow.postMessage(
                    {
                        type: "urlChange",
                        origin: window.location.href,
                    },
                    "*"
                )
            }
        })
        console.log("webSite", webSite)
        //全站切回自研liveChat
        ;(function (w, d) {
            var lc = d.createElement("script")
            lc.type = "text/javascript"
            lc.defer = true
            lc.src = lcSdkUrl
            var s = d.getElementsByTagName("script")[0]
            s.parentNode.insertBefore(lc, s)
            lc.onload = function () {
                if (w.fsLiveChatMount) {
                    fsLiveChatMount({
                        appId: 1, // 商城
                        isoCode, // 地区isoCode
                        language, // 语言id
                        webSite, // 站点
                        clientUserInfo, // 登录用户信息
                        fromPage: window.location.href, //可选
                    })
                }
            }
        })(window, document)
        // 新增it站点，测试自研liveChat
        // if (!["cn", "tw", "hk", "mo", "it"].includes(webSite)) {
        //     ;(function (w, d) {
        //         var lc = d.createElement("script")
        //         lc.type = "text/javascript"
        //         lc.defer = true
        //         lc.id = "ze-snippet"
        //         lc.src = "https://static.zdassets.com/ekr/snippet.js?key=a35bc69f-c6f6-4cde-b7f3-a3255ec4d9f9"
        //         var s = d.getElementsByTagName("script")[0]
        //         s.parentNode.insertBefore(lc, s)
        //         lc.onload = function () {
        //             if (w.zE) {
        //                 if (typeof webSite === "string" && zendeskLocaleMap.hasOwnProperty(webSite)) {
        //                     zE("messenger:set", "locale", zendeskLocaleMap[webSite])
        //                 }
        //                 if (clientToken) {
        //                     app.$axios
        //                         .post("/api/zendesk/generateJwtToken")
        //                         .then((res) => {
        //                             console.log("zendesk token", res.data.token)
        //                             const signedJwt = res.data.token
        //                             zE("messenger", "loginUser", function (callback) {
        //                                 callback(signedJwt)
        //                                 console.log("loginUser with token", signedJwt)
        //                             })
        //                         })
        //                         .catch((err) => {
        //                             console.log(err)
        //                         })
        //                 }
        //                 console.log("zendesk get customer_email_address:", userInfo?.customers_email_address)
        //                 zE("messenger:set", "conversationFields", [{ id: "28194227804441", value: userInfo?.customers_email_address || "" }])
        //                 // customer number field
        //                 console.log("zendesk get customers_number_new:", userInfo?.customers_number_new)
        //                 zE("messenger:set", "conversationFields", [{ id: "35684258978073", value: userInfo?.customers_number_new || "" }])
        //                 // webSite field
        //                 console.log("zendesk get webSite:", webSite)
        //                 zE("messenger:set", "conversationFields", [{ id: "35684241634073", value: webSite }])
        //                 // country field
        //                 console.log("zendesk get country_name: ", webSiteInfo.country_name)
        //                 zE("messenger:set", "conversationFields", [{ id: "28965480548121", value: webSiteInfo.country_name }])
        //                 zE("messenger:set", "conversationFields", [{ id: "36394089098009", value: webSiteInfo.countries_id }])
        //                 zE("messenger:on", "unreadMessages", function (count) {
        //                     console.log(`You have ${count} unread message(s).`)
        //                 })
        //                 // }
        //                 window.showFsLiveChat = function () {
        //                     zE("messenger", "open")
        //                 }
        //                 window.hideFsLiveChat = function () {
        //                     zE("messenger", "close")
        //                 }
        //             }
        //         }
        //     })(window, document)
        // } else {
        //     ;(function (w, d) {
        //         var lc = d.createElement("script")
        //         lc.type = "text/javascript"
        //         lc.defer = true
        //         lc.src = lcSdkUrl
        //         var s = d.getElementsByTagName("script")[0]
        //         s.parentNode.insertBefore(lc, s)
        //         lc.onload = function () {
        //             if (w.fsLiveChatMount) {
        //                 fsLiveChatMount({
        //                     appId: 1, // 商城
        //                     isoCode, // 地区isoCode
        //                     language, // 语言id
        //                     webSite, // 站点
        //                     clientUserInfo, // 登录用户信息
        //                     fromPage: window.location.href, //可选
        //                 })
        //             }
        //         }
        //     })(window, document)
        // }
    }
}
