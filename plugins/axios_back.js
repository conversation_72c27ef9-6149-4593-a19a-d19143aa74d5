import { Base64 } from "js-base64"
import createHmac from "create-hmac"
import { isObject } from "@/util/types"
import { api_config, live_chat_api } from "@/config.js"
import { setCookieOptions } from "@/util/util"

function genNonce(num) {
    let chars = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz"
    let str = ""
    for (var i = 0; i < num; i++) {
        let pos = Math.round(Math.random() * (chars.length - 1))
        str += chars[pos]
    }
    return str
}

// const websiteToLocal = {
// 	"au": "au",
// 	"de": "de",
// 	"de-en": "de-en",
// 	"en": "en",
// 	"es": "es",
// 	"fr": "fr",
// 	"it": "it",
// 	"jp": "jp",
// 	"mx": "mx",
// 	"ru": "ru",
// 	"sg": "sg",
// 	"uk": "uk",
// 	"feisu.com": "cn",
// }

export default function ({ app, $axios, redirect, route, query, store, $winstonLog, env }) {
    if (env && env.NODE_ENV === "production") {
        if (process.server) {
            if (env.ENV === "prod") {
                $axios.defaults.baseURL = api_config[env.ENV].server
            } else {
                $axios.defaults.baseURL = api_config[env.ENV].client
            }
        } else {
            $axios.defaults.baseURL = api_config[env.ENV].client
        }
    }
    console.log("axiosaxiosaxiosaxios")
    console.log(env)
    console.log("axiosaxiosaxiosaxios")

    $axios.defaults.timeout = 180000
    $axios.interceptors.request.use(
        (config) => {
            config.headers.Accept = "application/json"
            let timestamps = new Date().getTime(),
                nonce = genNonce(16),
                apiKey = "yuxuanxuanpc",
                payload = "[]"
            let s = timestamps + nonce + payload
            let hmac = createHmac("sha256", "yuxuan3507")
            hmac.update(s)
            let signature = Base64.encode(hmac.digest("hex"))
            let token = app.$cookies.get("token_new") || query.token || ""
            let cartId = app.$cookies.get("cartId")
            if (token) {
                config.headers.Authorization = token
            }
            if (cartId) {
                config.headers.cartId = cartId
            }

            config.headers.timestamps = timestamps
            config.headers.nonce = nonce
            config.headers.apiKey = apiKey
            config.headers.clientSignature = signature
            config.headers.supportWebp = store.state.device.supportWebp
            let local = app.i18n.locale

            // console.log("ddddddddddddddddddddd")
            // console.log(local)
            // console.log("ddddddddddddddddddddd")

            let webSiteInfo = store.state.webSiteInfo || {}
            // if (!["en", "au", "de", "de-en", "es", "fr", "it", "jp", "mx", "ru", "sg", "uk", "cn","hk","tw","mo"].includes(local)) {
            // 	if (env && env.ENV === "prod_cn") {
            // 		local = "cn"
            // 	}else{
            // 		local = "en"
            // 	}
            // }

            if (local) {
                if (query.country && query.currency && query.languages) {
                    webSiteInfo = {
                        isCookie: true,
                        language: query.languages,
                        iso_code: query.country,
                        currency: query.currency,
                    }
                } else {
                    if (webSiteInfo && webSiteInfo.website && webSiteInfo.iso_code) {
                        let website = webSiteInfo.website
                        if (webSiteInfo.language && webSiteInfo.iso_code && webSiteInfo.currency) {
                            if (website === local) {
                                webSiteInfo.isCookie = true
                            } else {
                                webSiteInfo = {
                                    website: local,
                                    isCookie: false,
                                }
                            }
                        } else {
                            // if (["feisu.com", "zh/tw", "zh/hk", "zh/mo"].includes(website)) {
                            if (["feisu.com", "cn", "zh/tw", "zh/hk", "zh/mo"].includes(website)) {
                                if (env && env.ENV === "prod") {
                                    webSiteInfo = {}
                                } else {
                                    webSiteInfo = {
                                        website: local,
                                        isCookie: false,
                                    }
                                }
                            } else {
                                webSiteInfo = {
                                    website: local,
                                    isCookie: false,
                                }
                            }
                        }
                    } else {
                        if (local === "en") {
                            webSiteInfo = {
                                language: query.languages || "",
                                iso_code: query.country || "",
                                currency: query.currency || "",
                            }
                        } else {
                            webSiteInfo = {
                                website: local,
                                isCookie: false,
                                language: query.languages || "",
                                iso_code: query.country || "",
                                currency: query.currency || "",
                            }
                        }
                    }
                }
            }

            // if (env.ENV === "prod_cn") {
            //     webSiteInfo = {
            //         website: "cn",
            //         isCookie: true,
            //         language: query.languages || "",
            //         iso_code: query.country || "",
            //         currency: query.currency || "",
            //     }
            // }

            config.headers.webSiteInfo = Base64.encode(JSON.stringify(webSiteInfo))

            if (env && env.NODE_ENV === "production") {
                if (live_chat_api.includes(config.url)) {
                    config.baseURL = api_config[env.ENV].live_chat_api
                }
            }

            if (process.server) {
                config.headers.is_server = 1
            }
            // console.log("qqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqq")
            // console.log(config.url)
            // console.log(webSiteInfo)
            // console.log(Base64.encode(JSON.stringify(webSiteInfo)))

            return config
        },
        (err) => {
            return Promise.reject(err)
        }
    )
    $axios.interceptors.response.use(
        (res) => {
            if (res.headers) {
                if (res.headers.authorization) {
                    app.$cookies.set("token_new", res.headers.authorization)
                }
                if (res.headers.cartid) {
                    app.$cookies.set("cartId", res.headers.cartid)
                }
                if (res.headers.clientip) {
                    store.commit("webSiteInfo/setIp", res.headers.clientip)
                }

                if (res.headers.websiteinfo && JSON.parse(Base64.decode(res.headers.websiteinfo))) {
                    let webSiteInfo = JSON.parse(Base64.decode(res.headers.websiteinfo))

                    // console.log("rrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrr")
                    // console.log(res.config.url)
                    // console.log(webSiteInfo)
                    // console.log(webSiteInfo.website)
                    // console.log("rrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrr")

                    if (["feisu.com", "cn"].includes(webSiteInfo.website)) {
                        // console.log("feisu.comfeisu.comfeisu.comfeisu.comfeisu.comfeisu.comfeisu.com")
                        // store.commit("webSiteInfo/setWebSiteInfo", webSiteInfo);
                        let route_name = route.name
                        let url = "https://cn.fs.com/"
                        // if (webSiteInfo.website === "zh/tw") {
                        //     url += "tw/"
                        // } else if (webSiteInfo.website === "zh/hk") {
                        //     url += "hk/"
                        // } else if (webSiteInfo.website === "zh/mo") {
                        //     url += "mo/"
                        // }
                        if (/^verify/g.test(route_name)) {
                            url += "verify"
                        }
                        if (/^home/g.test(route_name) || /^verify/g.test(route_name)) {
                            if (process.server && env && env.ENV === "prod") {
                                redirect(url)
                            }
                        } else {
                            let local = app.i18n.locale
                            if (!["en", "au", "de", "de-en", "es", "fr", "it", "jp", "mx", "ru", "sg", "nl", "uk", "cn", "hk", "tw", "mo"].includes(local)) {
                                local = "en"
                            }
                            store.commit("webSiteInfo/setWebSiteInfo", { website: local })
                        }
                    } else {
                        store.commit("webSiteInfo/setWebSiteInfo", webSiteInfo)
                        if (query.country && query.currency && query.languages) {
                            if (process.server) {
                                if (webSiteInfo.website !== app.i18n.locale) {
                                    let url = app.switchLocalePath(webSiteInfo.website)
                                    if (url.indexOf("?") !== -1) {
                                        let p = url.substr(0, url.indexOf("?"))
                                        let q = url.substr(url.indexOf("?") + 1)
                                        let arr = q.split("&")
                                        let obj = {}
                                        arr.map((item) => {
                                            obj[item.split("=")[0]] = item.split("=")[1]
                                        })
                                        delete obj.country
                                        delete obj.currency
                                        delete obj.languages
                                        let str = ``
                                        for (let attr in obj) {
                                            if (attr) {
                                                str += `${attr}=${obj[attr]}&`
                                            }
                                        }
                                        if (str) {
                                            str = str.substr(0, str.length - 1)
                                            str = `${p}?${str}`
                                        } else {
                                            str = p
                                        }
                                        redirect(str)
                                    } else {
                                        redirect(app.switchLocalePath(webSiteInfo.website))
                                    }
                                }
                            }
                        } else {
                            // console.log("cncncncncncncncncncncn")
                            // console.log(webSiteInfo.website)
                            // console.log(app.i18n.locale)
                            // console.log("cncncncncncncncncncncn")
                            if (webSiteInfo.website !== app.i18n.locale) {
                                if (process.server) {
                                    redirect(app.switchLocalePath(webSiteInfo.website))
                                }
                            }
                        }
                    }
                }
            }

            if ($winstonLog) {
                $winstonLog.info(`[${res.status}] [${$axios.defaults.baseURL}] [${res.request.path}]`)
            }
            return Promise.resolve(res.data)
        },
        (err) => {
            // console.log("errerrerrerrerrerrerrerrerrerrerrerrerrerrerr")
            // console.log(err.config.url)
            // console.log("errerrerrerrerrerrerrerrerrerrerrerrerrerrerr")
            if (err && err.response && err.response.headers) {
                if (err.response.headers.authorization) {
                    app.$cookies.set("token_new", err.response.headers.authorization)
                }
                if (err.response.headers.clientip) {
                    store.commit("webSiteInfo/setIp", err.response.headers.clientip)
                }
                if (err.response.headers.websiteinfo && JSON.parse(Base64.decode(err.response.headers.websiteinfo))) {
                    let webSiteInfo = JSON.parse(Base64.decode(err.response.headers.websiteinfo))
                    // if (["feisu.com", "zh/tw", "zh/hk", "zh/mo"].includes(webSiteInfo.website)) {
                    if (["feisu.com", "cn"].includes(webSiteInfo.website)) {
                        let route_name = route.name
                        let url = "https://cn.fs.com/"
                        // if (webSiteInfo.website === "zh/tw") {
                        //     url += "tw/"
                        // } else if (webSiteInfo.website === "zh/hk") {
                        //     url += "hk/"
                        // } else if (webSiteInfo.website === "zh/mo") {
                        //     url += "mo/"
                        // }
                        if (/^verify/g.test(route_name)) {
                            url += "verify"
                        }
                        if (/^home/g.test(route_name) || /^verify/g.test(route_name)) {
                            if (process.server && env && env.ENV === "prod") {
                                redirect(url)
                            }
                        } else {
                            let local = app.i18n.locale
                            if (!["en", "au", "de", "de-en", "es", "fr", "it", "jp", "mx", "ru", "sg", "uk", "cn", "hk", "tw", "mo"].includes(local)) {
                                local = "en"
                            }
                            store.commit("webSiteInfo/setWebSiteInfo", { website: local })
                        }
                    } else {
                        store.commit("webSiteInfo/setWebSiteInfo", webSiteInfo)
                        if (query.country && query.currency && query.languages) {
                            if (process.server) {
                                if (webSiteInfo.website !== app.i18n.locale) {
                                    let url = app.switchLocalePath(webSiteInfo.website)
                                    if (url.indexOf("?") !== -1) {
                                        let p = url.substr(0, 3)
                                        let q = url.substr(url.indexOf("?") + 1)
                                        let arr = q.split("&")
                                        let obj = {}
                                        arr.map((item) => {
                                            obj[item.split("=")[0]] = item.split("=")[1]
                                        })
                                        delete obj.country
                                        delete obj.currency
                                        delete obj.languages
                                        let str = ``
                                        for (let attr in obj) {
                                            if (attr) {
                                                str += `${attr}=${obj[attr]}&`
                                            }
                                        }
                                        if (str) {
                                            str = str.substr(0, str.length - 1)
                                            str = `${p}?${str}`
                                        } else {
                                            str = p
                                        }
                                        redirect(str)
                                    } else {
                                        redirect(app.switchLocalePath(webSiteInfo.website))
                                    }
                                }
                            }
                        } else {
                            if (webSiteInfo.website !== app.i18n.locale) {
                                if (process.server) {
                                    let p = app.switchLocalePath(webSiteInfo.website)
                                    redirect(p)
                                }
                            }
                        }
                    }
                }
                if (err.response.headers.cartid) {
                    app.$cookies.set("cartId", err.response.headers.cartid)
                }
            }
            if ($winstonLog) {
                $winstonLog.error(`[${err.status}] | [${$axios.defaults.baseURL}] |  [${err.request.path}] | ${err.message}`)
                $winstonLog.error(err.response && err.response.data)
            }
            if (process.client && app.$message) {
                //app.$message.error(err.response.data.message)
            }
            if (err.response && err.response.status) {
                switch (err.response.status) {
                    case 404:
                        if (err.config.url && !live_chat_api.includes(err.config.url)) {
                            redirect(app.localePath({ name: "404" }))
                        }
                        break
                    case 401:
                        app.$cookies.remove("token_new")
                        store.commit("userInfo/resetUserInfo")
                        let q = JSON.parse(JSON.stringify(query)) || {}
                        if (q && q.redirect) {
                            delete q.redirect
                        }
                        q.redirect = route.fullPath
                        redirect(app.localePath({ name: "login", query: q }))
                        break
                    case 422:
                        if (err.response.data && err.response.data.errors && isObject(err.response.data.errors)) {
                            for (let attr in err.response.data.errors) {
                                if (Array.isArray(err.response.data.errors[attr])) {
                                    err.response.data.errors[attr] = err.response.data.errors[attr].join(",")
                                }
                            }
                        }
                        break
                    case 429:
                        if (err.config.url && !live_chat_api.includes(err.config.url)) {
                            redirect(app.localePath({ name: "429" }))
                        }
                        break
                    case 500:
                        if (err.config.url && !live_chat_api.includes(err.config.url)) {
                            redirect(app.localePath({ name: "500" }))
                        }
                        break
                    case 400:
                        //if (process.server) {
                        return Promise.reject(err.response.data)
                        //}
                        break
                    default:
                        if (process.server && err.config.url && !live_chat_api.includes(err.config.url)) {
                            redirect(app.localePath({ name: "500" }))
                        }
                }
                return Promise.reject(err.response.data)
            } else {
                if (err.message.includes("timeout")) {
                    console.log("网络超时", err)
                }
                return Promise.reject(err)
            }
        }
    )
}
