export default function ({ isHMR, env, store }) {
    console.log("gtm_gtm_gtm")
    console.log(store)
    if (process.client && !isHMR) {
        const isLoadAdSdk = store.getters["webSiteInfo/isLoadAdSdk"]
        const href = window.location.href
        store.commit("ga/setPageLocation", href)

        if (!store.state.userInfo.isCNRegion) {
            function getCookie(name) {
                const cookies = document.cookie.split(";")
                for (let i = 0; i < cookies.length; i++) {
                    const cookie = cookies[i].trim()
                    if (cookie.startsWith(name + "=")) {
                        return cookie.substring(name.length + 1)
                    }
                }
                return null
            }
            window.dataLayer = window.dataLayer || []
            function gtag() {
                dataLayer.push(arguments)
            }
            window.gtag = gtag
            const cookievalue = getCookie("cookieconsent_dismissed")
            // 默认意见
            gtag("consent", "default", {
                region: ["AT", "BE", "BG", "CY", "CZ", "DE", "DK", "EE", "ES", "FI", "FR", "GR", "HR", "HU", "IE", "IS", "IT", "LI", "LT", "LU", "LV", "MT", "NL", "NO", "PL", "PT", "RO", "SE", "SI", "SK"],
                ad_storage: "denied",
                ad_user_data: "denied",
                ad_personalization: "denied",
                analytics_storage: "denied",
                wait_for_update: 500,
            })
            // 过往意见
            if (cookievalue && cookievalue.length != 0) {
                if (cookievalue == "yes") {
                    gtag("consent", "update", {
                        region: ["AT", "BE", "BG", "CY", "CZ", "DE", "DK", "EE", "ES", "FI", "FR", "GR", "HR", "HU", "IE", "IS", "IT", "LI", "LT", "LU", "LV", "MT", "NL", "NO", "PL", "PT", "RO", "SE", "SI", "SK"],
                        ad_storage: "granted",
                        ad_user_data: "granted",
                        ad_personalization: "granted",
                        analytics_storage: "granted",
                    })
                } else {
                    gtag("consent", "update", {
                        region: ["AT", "BE", "BG", "CY", "CZ", "DE", "DK", "EE", "ES", "FI", "FR", "GR", "HR", "HU", "IE", "IS", "IT", "LI", "LT", "LU", "LV", "MT", "NL", "NO", "PL", "PT", "RO", "SE", "SI", "SK"],
                        ad_storage: "denied",
                        ad_user_data: "denied",
                        ad_personalization: "denied",
                        analytics_storage: "denied",
                    })
                }
            }

            // 只有在isLoadAdSdk为true时才加载GTM相关内容
            if (isLoadAdSdk) {
                // GTM脚本加载
                ;(function (w, d, s, l, i) {
                    w[l] = w[l] || []
                    w[l].push({ "gtm.start": new Date().getTime(), event: "gtm.js" })
                    var f = d.getElementsByTagName(s)[0],
                        j = d.createElement(s),
                        dl = l != "dataLayer" ? "&l=" + l : ""
                    j.defer = true
                    j.src = "https://www.googletagmanager.com/gtm.js?id=" + i + dl
                    f.parentNode.insertBefore(j, f)
                })(window, document, "script", "dataLayer", "GTM-PBGKN3")

                // noscript标签也只在isLoadAdSdk为true时添加
                var noscript = document.createElement("noscript")
                var iframe = document.createElement("iframe")
                iframe.src = "https://www.googletagmanager.com/ns.html?id=GTM-PBGKN3"
                iframe.width = 0
                iframe.height = 0
                iframe.style.display = "none"
                iframe.style.visibility = "hidden"
                noscript.appendChild(iframe)
                document.body.appendChild(noscript)
            }
        }

        // reCAPTCHA保留现有逻辑，不受isLoadAdSdk控制
        ;(function (d) {
            const sitekey = "6Lf874IpAAAAACYfn7X2StpklJzoaN3JehO956Xc"
            const script = d.createElement("script")
            script.setAttribute("data-callType", "callScript")
            script.src = `https://www.google.com/recaptcha/api.js?render=${sitekey}`
            script.onerror = () => {
                const s = d.createElement("script")
                s.setAttribute("data-callType", "callScript")
                s.src = `https://www.recaptcha.net/recaptcha/api.js?render=${sitekey}`
                d.head.appendChild(s)
            }
            d.head.appendChild(script)
        })(document)
    }
}
