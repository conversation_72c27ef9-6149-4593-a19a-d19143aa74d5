import Vue from "vue"
import * as Sentry from "@sentry/vue"

export default function ({ app, store, route, isHMR, env }) {
    if (env && env.NODE_ENV === "production" && ["prod", "prod_cn_local"].includes(env.ENV)) {
        Sentry.init({
            Vue,
            dsn: "https://<EMAIL>/5",
            integrations: [Sentry.browserTracingIntegration({ router: app.router }), Sentry.replayIntegration()],
            // Set tracesSampleRate to 1.0 to capture 100%
            // of transactions for performance monitoring.
            // We recommend adjusting this value in production
            tracesSampleRate: 1.0,

            // Set `tracePropagationTargets` to control for which URLs distributed tracing should be enabled

            // Capture Replay for 10% of all sessions,
            // plus for 100% of sessions with an error
            replaysSessionSampleRate: 0.1,
            replaysOnErrorSampleRate: 1.0,
            beforeSend(event, hint) {
                if (event.exception) {
                    console.log("==================")
                    console.log(event)
                    console.log(hint)
                    console.error(`[Exeption handled by <PERSON><PERSON>]: (${hint.originalException})`, { event, hint })
                    console.log(Sentry)
                    console.log("==================")
                }
                return event
            },
        })
    }
}
