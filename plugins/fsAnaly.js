export default function ({ app, isHMR, app, env, store, route, req }) {
    if (process.server) {
        let referer = req.headers.referrer || req.headers.referer
        store.commit("webSiteInfo/setPreurl", referer || "")
        store.commit("webSiteInfo/setReferer", referer || "")
    }
    if (process.client && window) {
        const generateUUID = () => {
            let d = new Date().getTime()
            if (window.performance && typeof window.performance.now === "function") {
                d += performance.now()
            }
            let uuid = "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g, function (c) {
                let r = (d + Math.random() * 16) % 16 | 0
                d = Math.floor(d / 16)
                return (c == "x" ? r : (r & 0x3) | 0x8).toString(16)
            })

            uuid = uuid.replace(/-/g, "")
            return uuid
        }

        const getCookie = (cname) => {
            let name = cname + "="
            let ca = document.cookie.split(";")
            for (let i = 0; i < ca.length; i++) {
                let c = ca[i].trim()
                if (c.indexOf(name) == 0) {
                    return c.substring(name.length, c.length)
                }
            }
            return ""
        }

        const setCookie = (cname, cvalue, extime) => {
            let d = new Date()
            d.setTime(d.getTime() + extime)
            let expires = "expires=" + d.toGMTString()
            document.cookie = cname + "=" + cvalue + ";" + expires + ";path=/"
        }

        const deleteCookie = (name) => {
            let exp = new Date()
            exp.setTime(exp.getTime() - 1)
            let cval = getCookie(name)
            if (cval) {
                document.cookie = name + "=" + cval + ";expires=" + exp.toGMTString()
            }
        }

        const getResolution = () => {
            if (window.screen) {
                return `${window.screen.width}×${window.screen.height}`
            }
            return ""
        }

        const getUA = () => {
            if (window.navigator) {
                return window.navigator.userAgent
            }
            return ""
        }

        const createUUID = () => {
            let u = getCookie("_fs_uuid") || ""

            let n = 0
            if (!u) {
                u = generateUUID()
                n = 1
            }
            setCookie("_fs_uuid", u, 2 * 365 * 24 * 60 * 60 * 1000)
            let o = {
                uuid: u,
                is_new: n,
            }
            return o
        }

        const setTrackUrl = () => {
            if (window && window.location) {
                if (window.location.host === "front-platform.whgxwl.com") {
                    return "https://test-api-service.fs.com/analytics/dcm/collect"
                } else if (window.location.host === "newfs.whgxwl.com/") {
                    return "https://test-api-service.fs.com/analytics/dcm/collect"
                } else if (window.location.host === "www.fs.com") {
                    return "https://api-analytics.fs.com/dcm/collect"
                }
            }
            return "https://test-api-service.fs.com/analytics/dcm/collect"
        }

        const createSID = () => {
            let s = getCookie("_fs_sid") || ""
            let t = getCookie("_fs_timestamp") || ""
            let now = new Date()
            let nowTime = now.getTime()
            let nowDate = now.getFullYear + now.getMonth() + now.getDate()
            setCookie("_fs_timestamp", nowTime)
            if (s && t) {
                let old = new Date(parseInt(t))
                let oldDate = old.getFullYear + old.getMonth() + old.getDate()
                let oldTime = parseInt(t) + 30 * 60 * 1000
                if (oldTime >= nowTime && oldDate === nowDate) {
                    setCookie("_fs_sid", s)
                } else {
                    s = generateUUID()
                    setCookie("_fs_sid", s)
                }
            } else {
                s = generateUUID()
                setCookie("_fs_sid", s)
            }
            let o = {
                sid: s,
                timestamp: nowTime,
            }
            return o
        }

        const getIp = () => {
            return getCookie("ip") || ""
        }

        const getBrowserLanguage = () => {
            if (window.navigator) {
                return window.navigator.language || window.navigator.userLanguage
            }
            return ""
        }

        const isObject = (obj) => {
            return Object.prototype.toString.call(obj) === "[object Object]"
        }
        const FsAnalytics = {}
        FsAnalytics.push = (data) => {
            if (env && env.NODE_ENV === "production" && env.ENV === "prod") {
                if (data) {
                    if (!isObject(data)) {
                        console.error("[FsAnalytics Warn]: The params must be Object!")
                        return
                    }
                }
                let track_url = setTrackUrl()
                if (!track_url) {
                    console.error("[FsAnalytics Warn]: Track url is required!")
                    return
                }

                let u = createUUID(),
                    ip = getIp(),
                    country = getCookie("iso_code") || "",
                    site_code = getCookie("website") || "",
                    lang = app.$cookies.get("language") || "",
                    currency = getCookie("currency") || "",
                    customer_number = getCookie("customers_number_new") || "",
                    sid = createSID(),
                    user_agent = getUA(),
                    resolution = getResolution(),
                    fs_pvid = app.$cookies.get("_fs_pvid") || "",
                    app_id = 1,
                    agent_lang = getBrowserLanguage()
                const init_data = {
                    i: ip,
                    co: country,
                    sc: site_code,
                    l: lang,
                    cu: currency,
                    cn: customer_number,
                    v: u.uuid,
                    n: u.is_new,
                    s: sid.sid,
                    ts: parseInt(sid.timestamp / 1000),
                    ua: user_agent,
                    res: resolution,
                    fpvid: fs_pvid,
                    aid: app_id,
                    al: agent_lang,
                }
                // setCookie("_fs_pvid", fs_pvid);

                let new_data = Object.assign({}, init_data, data || {})
                let qs = []

                for (let attr in new_data) {
                    if (attr === "data") {
                        qs.push(`${encodeURIComponent(attr)}=${encodeURIComponent(JSON.stringify(new_data[attr]))}`)
                    } else {
                        qs.push(`${encodeURIComponent(attr)}=${encodeURIComponent(new_data[attr])}`)
                    }
                }
                let qa = "?" + qs.join("&")
                let img = new Image()
                img.src = track_url + qa
            }
        }
        window.FsAnalytics = FsAnalytics
        let route_name = route.name.substring(0, route.name.lastIndexOf("___"))
        if (route_name !== "list" && route_name !== "products") {
            let domain = store.state.meta.domain
            let url = `${domain}${route.fullPath}`
            FsAnalytics.push({
                u: url,
                ref_url: store.state.webSiteInfo.pre_url,
                page_name: route_name,
            })
        }
    }
}
