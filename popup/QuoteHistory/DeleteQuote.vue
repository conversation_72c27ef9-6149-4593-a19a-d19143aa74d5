<template>
    <fs-popup-new class="delete_popup" transition="fade" @close="close" :show="show" width="480" :mobileWidth="mobileWidth" type="float" isNew :title="$c('pages.Quote.Confirmation')">
        <div class="delete_popup_ctn">
            <p class="delete_info">{{ $c("pages.Quote.deleteThisQuote") }}</p>
        </div>
        <template slot="footer">
            <div class="delete_btn_box">
                <fs-button type="white" isNewStyle @click="close">{{ $c("pages.Quote.pop.cancel") }}</fs-button>
                <fs-button :loading="delete_btn_loading" isNewStyle @click="DeleteQuote">{{ $c("pages.Quote.delete") }}</fs-button>
            </div>
        </template>
    </fs-popup-new>
</template>

<script>
import FsPopupNew from "@/components/FsPopupNew/FsPopupNew.vue"
import FsButton from "@/components/FsButton/FsButton"
import { mapState } from "vuex"
export default {
    components: {
        FsButton,
        FsPopupNew,
    },
    props: {
        show: false,
        quote_id: 0,
        popStatu: {},
        quoteType: {
            type: [String, Number],
            default: 1,
        }, //1线上报价 2线下报价
    },
    data() {
        return {
            mobileWidth: "calc(100% - 64px)",
            delete_btn_loading: false,
            deleteFlag: false,
        }
    },
    mounted() {
        // console.log(`${this.$route.path.indexOf("my-account") ? "quote" : this.$route.path.indexOf("quote_history") ? "quote_history" : "quote_detail"}_operate`)
    },
    computed: {
        ...mapState({
            isMobile: (state) => state.device.isMobile,
            pageGroup: (state) => state.ga.pageGroup,
        }),
    },
    methods: {
        DeleteQuote() {
            this.delete_btn_loading = true
            window.dataLayer &&
                window.dataLayer.push({
                    event: "uaEvent",
                    eventCategory: this.pageGroup,
                    eventAction: `${this.$route.path.indexOf("my-account") ? "quote" : this.$route.path.indexOf("quote_history") ? "quote_history" : "quote_detail"}_operate`,
                    eventLabel: `Confirm Delete_${this.popStatu}_Delete this quote?`,
                    nonInteraction: false,
                })
            this.$axios
                .post("/api/quotes_delete", { type: this.quoteType, quotes_id: this.quote_id })
                .then((res) => {
                    console.log(res)
                    if (res.code == 200) {
                        this.deleteFlag = true
                        setTimeout(() => {
                            this.$emit("DeleteQuoteSuccess")
                            this.deleteFlag = false
                            this.delete_btn_loading = false
                        }, 2000)
                    }
                })
                .catch((err) => {
                    this.delete_btn_loading = false
                    this.$message.error(err.message)
                })
        },
        close() {
            this.$emit("close")
        },
    },
}
</script>

<style lang="scss" scoped>
.delete_popup {
    .delete_info {
        @include font14;
        color: $textColor1;
        // margin-bottom: 16px;
    }

    .delete_btn_box {
        display: flex;
        justify-content: flex-end;
        .fs-button {
            margin-left: 16px;
            height: 42px;
            @include mobile {
                margin-left: 12px;
            }
        }
    }
}
</style>
