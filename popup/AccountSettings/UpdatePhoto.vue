<template>
    <fs-popup-new v-bind="$attrs" v-on="$listeners" width="680" transition="slide-up" :type="type" :title="$c('pages.MyAccount.settings.updatePhoto')" :loading="loading" :isMDrawer="true">
        <div class="update_photo" ref="update_photo">
            <fs-warn v-if="err" type="red">
                {{ this.$c("pages.MyAccount.settings.errorTips") }}
            </fs-warn>
            <upload-file accept="image/jpeg,image/jpg,image/png,image/gif,image/bmp" @change="handleChange" :maxSize="300 * 1024" :text="isJP ? '' : $c('pages.MyAccount.settings.updatePhoto')" :type="type"></upload-file>
            <p>{{ $c("pages.MyAccount.settings.photoLimit") }}</p>
        </div>
        <template slot="footer">
            <div class="btn">
                <fs-button type="grayline" :text="$c('pages.MyAccount.settings.cancel')" @click="cancel" />
                <fs-button :text="$c('pages.MyAccount.settings.save')" @click="save" :loading="btnLoading" />
            </div>
        </template>
    </fs-popup-new>
</template>

<script>
import FsPopupNew from "@/components/FsPopupNew/FsPopupNew.vue"
import UploadFile from "@/components/UploadFile/UploadFile.vue"
import FsButton from "@/components/FsButton/FsButton"
import FsWarn from "@/components/FsWarn/FsWarn"
const MAX_FILE_SIZE = 3000 * 1024
export default {
    name: "UpdatePhoto",
    components: {
        FsPopupNew,
        UploadFile,
        FsButton,
        FsWarn,
    },
    props: {},
    data() {
        return {
            loading: false,
            type: "img",
            file: "",
            err: false,
            btnLoading: false,
            isJP: false,
        }
    },
    mounted() {
        this.isJP = ~location.pathname.indexOf("/jp/")
    },
    methods: {
        handleChange(params) {
            this.file = params.files[0]
            console.log(params)
            if (this.file) {
                this.err = false
            }
            if (params?.error) {
                this.err = true
            }
        },
        cancel() {
            this.$emit("close")
        },
        save() {
            if (!this.file || this.file.size > MAX_FILE_SIZE) {
                this.err = true
                return
            }
            this.btnLoading = true
            const data = new FormData()
            data.append("type", 5)
            data.append("customers_photo", this.file)
            this.$axios
                .post("/api/account_setting", data)
                .then((res) => {
                    this.btnLoading = false
                    this.$emit("success", res?.data?.full_name)
                })
                .catch((err) => {
                    this.btnLoading = false
                    this.$message.error(err.errors.customers_photo ? err.errors.customers_photo : err.message)
                })
        },
    },
}
</script>

<style lang="scss" scoped>
::v-deep .fs-popup-header {
    padding: 16px 24px 0;
    @media (max-width: 768px) {
        padding: 16px 16px 0;
    }
}
.update_photo {
    padding: 16px 24px 24px;
    width: 680px;
    max-width: 100vw;
    @media (max-width: 960px) {
        width: 100%;
    }
    ::v-deep .upload-file .img-btn {
        min-width: 152px;
        height: 48px;
    }
    ::v-deep .fs-warn {
        margin-bottom: 20px;
    }
    > p {
        color: #707070;
        font-size: 13px;
        margin: 16px 0 0 0;
    }
    @media (max-width: 640px) {
        padding: 16px 16px 20px;
        .btn button {
            width: 100%;
            margin-left: 0;
            &:first-child {
                display: none;
            }
        }
    }
}
.btn {
    padding: 0px 24px 24px;
    display: flex;
    justify-content: flex-end;
    button {
        margin-left: 12px;
        min-width: 118px;
    }
}
@media (max-width: 960px) {
    .fs-popup {
        &::v-deep .fs-popup-ctn {
            bottom: 0;
            top: auto;
            left: 0;
            transform: translate(0);
        }
        .btn {
            padding: 0 16px 16px;
            flex-direction: column-reverse;
            // border-top: 1px solid #e5e5e5;
            button {
                margin-left: 0;
                &:last-of-type {
                    margin-bottom: 12px;
                }
            }
        }
    }
}
</style>
