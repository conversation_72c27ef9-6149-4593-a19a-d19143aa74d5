<!--callback事件 非必须 登陆成功和注册成功后，需要执行的callback操作 -->
<template>
    <fs-popup v-bind="$attrs" v-on="$listeners" :show="show" @close="hide">
        <div class="tab_box" slot="header">
            <span class="tab" v-if="!hideLogin" :class="{ active: active === 'login' && !hideLogin && !hideRegist }" @click.stop="tabClick('login')">{{ $c("pages.Login.Sign_In") }}</span>
            <span class="tab" v-if="!hideRegist" :class="{ active: active === 'regist' && !hideLogin && !hideRegist }" @click.stop="tabClick('regist')">{{ $c("pages.Login.Create_an_account") }}</span>
        </div>
        <div class="form_wrap">
            <form class="form login_form" @submit.prevent="loginSubmit" v-if="active === 'login'">
                <validate-message :message="login.err"></validate-message>
                <div class="form_item">
                    <div class="label">{{ $c("pages.Login.Email_Address") }}</div>
                    <div class="inp_box">
                        <input type="text" v-model.trim="login.form.email" @input="inputCheck('login', 'email')" />
                        <validate-error :error="login.errors.email"></validate-error>
                    </div>
                </div>
                <div class="form_item">
                    <div class="label">{{ $c("pages.Login.Password") }}</div>
                    <div class="inp_box">
                        <input :type="login.eye ? 'text' : 'password'" class="pwd" v-model.trim="login.form.password" @input="inputCheck('login', 'password')" />
                        <validate-error :error="login.errors.password"></validate-error>
                        <!-- <span class="eye" @click.stop="toogleEye('login','eye')">{{login.eye?$c('pages.Login.hide'):$c('pages.Login.show')}}</span> -->
                        <span class="eye icon iconfont" :class="{ show: login.eye }" @click.stop="toogleEye('login', 'eye')">{{ login.eye ? "&#xe748;" : "&#xe706;" }}</span>
                    </div>
                </div>
                <fs-button :text="$c('pages.Login.Sign_In')" :loading="login.loading" htmlType="submit"></fs-button>
                <div class="forgot-box">
                    <nuxt-link :to="localePath({ name: 'forgot_password' })">{{ $c("pages.Login.Forgot_Your_Password") }}</nuxt-link>
                </div>
                <div class="third_login_box">
                    <div class="third_title">
                        <span class="line"></span>
                        <span class="text">{{ $c("pages.Login.Sign_in_with_other_ways") }}</span>
                        <span class="line"></span>
                    </div>
                    <div class="third_login">
                        <a class="iconfont" v-for="item in login.icon" :title="item.title" :key="item.type" v-html="item.icon"></a>
                    </div>
                </div>
            </form>
            <form class="form" @submit.prevent="registSubmit" v-if="active === 'regist'">
                <div class="user_name_box">
                    <div class="form_item">
                        <div class="label">{{ $c("form.form.first_name") }}</div>
                        <div class="inp_box">
                            <input type="text" v-model="regist.form.first_name" @input="inputCheck('regist', 'first_name')" />
                            <validate-error :error="regist.errors.first_name"></validate-error>
                        </div>
                    </div>
                    <div class="form_item">
                        <div class="label">{{ $c("form.form.last_name") }}</div>
                        <div class="inp_box">
                            <input type="text" v-model="regist.form.last_name" @input="inputCheck('regist', 'last_name')" />
                            <validate-error :error="regist.errors.last_name"></validate-error>
                        </div>
                    </div>
                </div>
                <div class="form_item">
                    <div class="label">{{ $c("form.form.email_ddress") }}</div>
                    <div class="inp_box">
                        <input type="text" v-model="regist.form.email" @input="inputCheck('regist', 'email')" @blur="emailBlur" />
                        <validate-error :error="regist.errors.email"></validate-error>
                    </div>
                </div>
                <div class="form_item">
                    <div class="label">{{ $c("form.form.password") }}</div>
                    <div class="inp_box">
                        <input :type="regist.eye ? 'text' : 'password'" class="pwd" v-model="regist.form.password" @input="inputCheck('regist', 'password')" />
                        <validate-error :error="regist.errors.password"></validate-error>
                        <!-- <span class="eye" @click.stop="toogleEye('regist','eye')">{{regist.eye?$c('pages.Login.hide'):$c('pages.Login.show')}}</span> -->
                        <span class="eye icon iconfont" :class="{ show: regist.eye }" @click.stop="toogleEye('regist', 'eye')">{{ regist.eye ? "&#xe748;" : "&#xe706;" }}</span>
                    </div>
                </div>
                <div class="form_item">
                    <div class="label">{{ $c("form.form.your_country_region") }}</div>
                    <div class="inp_box">
                        <select-country position="absolute"></select-country>
                        <validate-error :error="regist.errors.country"></validate-error>
                    </div>
                </div>
                <!-- <div class="form_item">
                    <div class="label">{{ $c("form.form.phone_number") }}</div>
                    <div class="inp_box">
                        <tel-code :phone="regist.form.phone" @change="phoneChange"></tel-code>
                        <validate-error :error="regist.errors.phone"></validate-error>
                    </div>
                </div> -->
                <div class="form_item">
                    <div class="label">{{ $c("pages.Login.Company_Name_optional") }}</div>
                    <div class="inp_box">
                        <input type="text" v-model="regist.form.company_name" @input="inputCheck('regist', 'company_name')" />
                        <validate-error :error="regist.errors.company_name"></validate-error>
                    </div>
                </div>
                <div class="form_item">
                    <div class="agreement_box">
                        <input type="checkbox" class="chk" v-model="regist.form.checked" @change.stop="checkedChange" />
                        <div
                            class="agreement"
                            v-html="
                                $c('pages.Login.I_agree_to_FSs_blank')
                                    .replace('XXX1', localePath({ name: 'privacy-policy' }))
                                    .replace('XXX2', localePath({ name: 'terms-of-use' }))
                            "></div>
                    </div>
                    <validate-error :error="regist.errors.checked"></validate-error>
                </div>
                <fs-button class="regist_sbtn" :text="$c('pages.Login.Create_My_account')" :loading="regist.loading" htmlType="submit"></fs-button>
            </form>
            <g-recaptcha ref="grecaptcha" @getValidateCode="getValidateCode"></g-recaptcha>
        </div>
    </fs-popup>
</template>
<script>
import FsPopup from "@/components/FsPopup/FsPopup.vue"
import FsButton from "@/components/FsButton/FsButton.vue"
import ValidateError from "@/components/ValidateError/ValidateError.vue"
import ValidateMessage from "@/components/ValidateMessage/ValidateMessage"
import SelectCountry from "@/components/SelectCountry/SelectCountry"
import TelCode from "@/components/TelCode/TelCode"
import { email_valdate, password_validate, company_name_validate } from "@/constants/validate"
import { mapState, mapMutations, mapActions } from "vuex"
import { setCookieOptions } from "@/util/util"
import GRecaptcha from "@/components/GRecaptcha/GRecaptcha"
import { isNeedGrecaptcha } from "@/util/grecaptchaHost"
import AES from "@/util/AES.js"
export default {
    name: "LoginRegist",
    components: {
        FsPopup,
        FsButton,
        ValidateError,
        ValidateMessage,
        SelectCountry,
        TelCode,
        GRecaptcha,
    },
    props: {
        fillData: {
            type: Object,
            default() {
                return {
                    entry_firstname: "", // firstname
                    entry_lastname: "", // lastname
                    email_address: "", // email
                    country_code: "", // 国家 iso_code
                    countryName: "", // country
                    entry_telephone: "", //entry_telephone
                }
            },
        },
    },
    data() {
        return {
            login: {
                eye: false,
                loading: false,
                err: "",
                icon: [
                    {
                        type: "google",
                        icon: "&#xf059;",
                        title: this.$c("pages.Login.Sign_in_with_google"),
                    },
                    {
                        type: "paypal",
                        icon: "&#xf058;",
                        title: this.$c("pages.Login.Sign_in_with_Paypal"),
                    },
                    {
                        type: "facebook",
                        icon: "&#xf056;",
                        title: this.$c("pages.Login.Sign_in_with_Facebook"),
                    },
                    {
                        type: "linkin",
                        icon: "&#xf057;",
                        title: this.$c("pages.Login.Sign_in_with_Linkedin"),
                    },
                ],
                form: {
                    email: "",
                    password: "",
                },
                errors: {
                    email: "",
                    password: "",
                },
            },
            regist: {
                loading: false,
                eye: false,
                is_regist: 0,
                form: {
                    first_name: "",
                    last_name: "",
                    email: "",
                    password: "",
                    country_code: "",
                    // phone: "",
                    company_name: "",
                    checked: true,
                },
                errors: {
                    first_name: "",
                    last_name: "",
                    email: "",
                    password: "",
                    country: "",
                    // phone: "",
                    company_name: "",
                    checked: "",
                },
            },
            recaptchaTp: "",
            timer: null,
        }
    },
    computed: {
        ...mapState({
            show: (state) => state.loginRegist.show,
            active: (state) => state.loginRegist.active,
            hideLogin: (state) => state.loginRegist.hideLogin,
            hideRegist: (state) => state.loginRegist.hideRegist,
            select_country_code: (state) => state.selectCountry.select_country_code,
            select_country_id: (state) => state.selectCountry.select_country_id,
        }),
    },
    mounted() {},
    methods: {
        ...mapMutations({
            setActive: "loginRegist/setActive",
            hide: "loginRegist/hide",
        }),
        ...mapActions({
            getUserInfo: "userInfo/getUserInfo",
        }),
        init() {
            this.$nextTick(() => {
                this.fillFormData()
            })
        },
        tabClick(s) {
            this.setActive(s)
        },
        toogleEye(p, c) {
            this[p][c] = !this[p][c]
        },
        inputCheck(p, c) {
            let flag = false
            if (c === "email") {
                if (!this[p]["form"][c].replace(/\s+/g, "")) {
                    this[p]["errors"][c] = this.$c("form.validate.email.email_required")
                    flag = true
                } else {
                    if (!email_valdate.test(this[p]["form"][c])) {
                        this[p]["errors"][c] = this.$c("form.validate.email.email_valid")
                        flag = true
                    } else {
                        this[p]["errors"][c] = ""
                    }
                }
            } else if (c === "password") {
                if (!this[p]["form"][c].replace(/\s+/g, "")) {
                    this[p]["errors"][c] = this.$c("form.validate.password.password_required")
                    flag = true
                } else {
                    if (p === "regist") {
                        if (!password_validate.test(this[p]["form"][c].replace(/\s+/g, ""))) {
                            this[p]["errors"][c] = this.$c("form.validate.password.password_validate")
                            flag = true
                        } else {
                            this[p]["errors"][c] = ""
                        }
                    } else {
                        this[p]["errors"][c] = ""
                    }
                }
            } else if (c === "first_name") {
                if (!this[p]["form"][c].replace(/\s+/g, "")) {
                    this[p]["errors"][c] = this.$c("form.validate.first_name.first_name_required")
                    flag = true
                } else {
                    if (this[p]["form"][c].replace(/\s+/g, "").length > 40) {
                        this[p]["errors"][c] = this.$c("form.validate.first_name.first_name_max")
                        flag = true
                    } else if (this[p]["form"][c].replace(/\s+/g, "").length < 2) {
                        this[p]["errors"][c] = this.$c("form.validate.first_name.first_name_min")
                        flag = true
                    } else {
                        this[p]["errors"][c] = ""
                    }
                }
            } else if (c === "last_name") {
                if (!this[p]["form"][c].replace(/\s+/g, "")) {
                    this[p]["errors"][c] = this.$c("form.validate.last_name.last_name_required")
                    flag = true
                } else {
                    if (this[p]["form"][c].replace(/\s+/g, "").length > 40) {
                        this[p]["errors"][c] = this.$c("form.validate.last_name.last_name_max")
                        flag = true
                    } else if (this[p]["form"][c].replace(/\s+/g, "").length < 2) {
                        this[p]["errors"][c] = this.$c("form.validate.last_name.last_name_min")
                        flag = true
                    } else {
                        this[p]["errors"][c] = ""
                    }
                }
            } else if (c === "phone") {
                if (!this[p]["form"][c].replace(/\s+/g, "")) {
                    // this[p]['errors'][c] = this.$c("form.validate.phone.phone_required")
                    // flag = true
                } else {
                    if (this[p]["form"][c].replace(/\s+/g, "").length < 6) {
                        this[p]["errors"][c] = this.$c("form.validate.phone.phone_min")
                        flag = true
                    } else {
                        this[p]["errors"][c] = ""
                    }
                }
            } else if (c === "company_name") {
                if (!this[p]["form"][c].replace(/\s+/g, "")) {
                    this[p]["errors"][c] = ""
                } else {
                    if (!company_name_validate.test(this[p]["form"][c].replace(/\s+/g, ""))) {
                        this[p]["errors"][c] = this.$c("form.validate.company_name.company_name_validate")
                        flag = true
                    } else {
                        this[p]["errors"][c] = ""
                    }
                }
            } else if (c === "checked") {
                if (this[p]["form"][c]) {
                    this[p]["errors"][c] = ""
                } else {
                    this[p]["errors"][c] = this.$c("form.form.errors.check2_error")
                    flag = true
                }
            }
            return flag
        },
        // phoneChange(p) {
        //     this.regist.form.phone = p
        //     this.inputCheck("regist", "phone")
        //     console.log(p, 9999)
        // },
        checkedChange() {
            this.inputCheck("regist", "checked")
        },
        emailBlur() {
            if (this.regist.errors.email) {
                return
            }
            this.regist.is_regist = 0
            this.$axios
                .post("/api/user/checkEmail", { email_address: this.regist.form.email })
                .then((res) => {
                    this.regist.is_regist = res.data.is_registered
                    if (res.data.is_registered === 1) {
                        this.regist.errors.email = this.$c("form.form.errors.email_address_error02")
                    } else {
                        this.regist.errors.email = ""
                    }
                })
                .catch((err) => {})
        },
        changeCountry(item) {
            console.log(item)
        },
        // 谷歌人机校验
        getValidateCode(val) {
            this.recaptchaTp = val.recaptchaTp
            if (val.recaptchaTp) {
                this.recaptchaVal = val.recaptchaVal
                this.loginSubmit()
            }
        },
        // 初始化谷歌验证
        initGrecaptcha() {
            this.recaptchaTp = false
            this.recaptchaVal = ""
        },
        loginSubmit() {
            let arr = [],
                attr = ["email", "password"]
            attr.map((item) => {
                let f = this.inputCheck("login", item)
                arr.push(f)
            })
            if (arr.includes(true) || this.login.loading) {
                return
            }
            if (isNeedGrecaptcha(window.location.hostname)) {
                if (!this.recaptchaTp && this.$refs.grecaptcha) {
                    this.$refs.grecaptcha.clickRecaptcha()
                    return
                }
            }
            this.login.loading = true
            this.$axios
                .post(
                    "/api/user/login",
                    { customers_email_address: this.login.form.email, customers_password: AES.encrypt(this.login.form.password, "_-yu_xuan_3507-_", "fs_com_phone2016"), login_type: 1 },
                    { headers: { "g-recaptcha-response": this.recaptchaVal } }
                )
                .then((res) => {
                    if (res.data.access_token) {
                        this.$cookies.set("token_new", res.data.access_token)
                    }
                    this.getUserInfo(() => {
                        this.login.loading = false
                        this.$emit("callback")
                        this.hide()
                    })
                    this.initGrecaptcha()
                    if (this.$refs.grecaptcha) {
                        this.$refs.grecaptcha.grecaptchaError = ""
                    }
                })
                .catch((err) => {
                    this.initGrecaptcha()
                    this.login.loading = false
                    if (err.code === 405) {
                        this.login.err = err.message
                        setTimeout(() => {
                            this.login.err = ""
                        }, 3000)
                    } else if (err.code === 422) {
                        this.login.errors.email = err.errors.customers_email_address
                        this.login.errors.password = err.errors.customers_password
                    } else if (err.code === 400) {
                        this.login.errors.password = err.message
                    } else if (err.code === 403) {
                        this.$message.error(err.message)
                    }
                    if (isNeedGrecaptcha(window.location.hostname)) {
                        if (err.code === 409 && this.$refs.grecaptcha) {
                            this.$refs.grecaptcha.grecaptchaError = this.$c("pages.Login.regist.grecaptcha_error")
                        }
                        if (this.timer) {
                            clearInterval(this.timer)
                        }
                        this.timer = setInterval(() => {
                            this.isntLogin = false
                            if (this.$refs.grecaptcha) {
                                this.$refs.grecaptcha.grecaptchaError = ""
                            }
                        }, 10000)
                    }
                })
        },
        registSubmit() {
            let arr = []
            // let attr = ["first_name", "last_name", "email", "password", "phone", "company_name", "checked"]
            let attr = ["first_name", "last_name", "email", "password", "company_name", "checked"]
            attr.map((item) => {
                let f = this.inputCheck("regist", item)
                arr.push(f)
            })
            if (arr.includes(true) || this.regist.is_regist || this.regist.loading) {
                return
            }
            this.regist.loading = true
            let obj = {
                first_name: this.regist.form.first_name,
                last_name: this.regist.form.last_name,
                email: this.regist.form.email,
                // phone_number: this.regist.form.phone || undefined,
                password: AES.encrypt(this.regist.form.password, "_-yu_xuan_3507-_", "fs_com_phone2016"),
                country: this.select_country_id,
                customers_company: this.regist.form.company_name,
            }
            this.$axios
                .post("/api/user/register", obj)
                .then((res) => {
                    if (res.data.access_token) {
                        this.$cookies.set("token_new", res.data.access_token)
                    }
                    // if (window.dataLayer) {
                    // 	window.dataLayer.push({
                    // 		'event': 'uaEvent',
                    // 		'eventCategory': 'regist',
                    // 		'eventAction': 'sign_up',
                    // 		'eventLabel': this.select_country_code,
                    // 		'nonInteraction': false
                    // 	});
                    // }
                    this.getUserInfo(() => {
                        this.regist.loading = false
                        this.$emit("callback")
                        this.hide()
                    })
                })
                .catch((err) => {
                    this.regist.loading = false
                    // if (window.dataLayer) {
                    // 	window.dataLayer.push({
                    // 		'event': 'uaEvent',
                    // 		'eventCategory': 'regist',
                    // 		'eventAction': 'sign_up_fail',
                    // 		'eventLabel': this.select_country_code,
                    // 		'nonInteraction': false
                    // 	});
                    // }
                })
        },
        // 表单数据填充
        fillFormData() {
            if (this.active === "regist") {
                this.regist.form.first_name = this.fillData.entry_firstname
                this.regist.form.last_name = this.fillData.entry_lastname
                this.regist.form.email = this.fillData.email_address
                // this.regist.form.phone = this.fillData.entry_telephone
            } else {
                this.login.form.email = this.fillData.email_address
            }
        },
    },
    // watch:{
    // 	fillData:{
    // 		handler(newVal){
    // 			console.log(newVal)
    // 			if(this.active==="regist"){
    // 				this.regist.form.first_name=newVal.entry_firstname;
    // 				this.regist.form.last_name=newVal.entry_lastname;
    // 				this.regist.form.email=newVal.email_address;
    // 				this.regist.form.phone=newVal.entry_telephone;
    // 			}else{
    // 				this.login.form.email=newVal.email_address;
    // 			}
    // 			console.log(newVal)
    // 			console.log(this.regist.form)
    // 		},
    // 		deep:true,
    // 		immediate:true
    // 	}
    // }
}
</script>
<style lang="scss" scoped>
.tab_box {
    display: flex;
    .tab {
        position: relative;
        margin-right: 30px;
        color: $textColor3;
        @include font20;
        line-height: 60px;
        height: 60px;
        cursor: pointer;
        margin-bottom: -1px;
        border-bottom: 2px solid transparent;
        &.active {
            border-bottom: 2px solid #19191a;
        }
    }
}
.form_wrap {
    width: 680px;
    @media (max-width: 1024px) {
        width: 100%;
        padding: 0 3%;
    }
}
.form {
    width: 380px;
    margin: 0 auto;
    padding-top: 34px;
    @media (max-width: 1024px) {
        width: 100%;
    }
    .validate-message {
        margin: 10px 0 15px 0;
    }
}
.user_name_box {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    .form_item {
        width: calc((100% - 12px) / 2);
    }
}

.form_item {
    margin-bottom: 14px;
    .label {
        display: flex;
        align-items: center;
        justify-content: space-between;
        color: $textColor3;
        margin-bottom: 6px;
        @include font13;
        a {
            color: $textColor3;
            @include font12;
        }
    }
    .pwd {
        padding-right: 44px;
    }
    .inp_box {
        position: relative;
        .eye {
            position: absolute;
            font-size: 16px;
            color: $textColor3;
            right: 8px;
            width: 22px;
            text-align: left;
            top: 12px;
            cursor: pointer;
            user-select: none;
            &.show {
                font-size: 16px;
            }
        }
    }
    .agreement_box {
        display: flex;
        align-items: center;
        .chk {
            margin-right: 8px;
            @media (max-width: 1024px) {
                display: none;
            }
        }
        .agreement {
            display: flex;
            align-items: center;
            @include font13;
            color: $textColor3;
            white-space: pre;
            a {
                color: $textColor1;
            }
            .info1 {
                @media (max-width: 1024px) {
                    display: none;
                }
            }
        }
    }
    .validate_error {
        &::v-deep {
            .login {
                color: $textColor4;
                text-decoration: underline;
            }
        }
    }
}
.fs-button {
    margin-top: 20px;
    width: 100%;
}
.regist_sbtn {
    margin: 28px 0 50px 0;
}
.forgot-box {
    margin: 58px 0 60px 0;
    text-align: center;
    @include font13;
    > a {
        color: $textColor3;
    }
}
.third_login_box {
    margin-top: 48px;
    margin-bottom: 34px;
    .third_title {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 24px;
        .text {
            color: $textColor1;
            @include font14;
            padding: 0 11px;
        }
        .line {
            flex: 1 1 auto;
            height: 1px;
            background: $bgColor2;
        }
    }
    .third_login {
        display: flex;
        justify-content: center;
        .iconfont {
            @include font24;
            color: $textColor3;
            margin: 0 20px;
            cursor: pointer;
            &:hover {
                text-decoration: none;
            }
        }
    }
}
.fs-popup {
    &::v-deep {
        .fs-popup-header {
            padding-top: 0;
            padding-bottom: 0;
        }
    }
}
</style>
