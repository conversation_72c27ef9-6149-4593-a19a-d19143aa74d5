<template>
    <fs-popup-new v-bind="$attrs" v-on="$listeners" width="650" :title="$c('pages.Products.Get_a_Quote')" isMDrawer isNew :hideHeader="success" @close="close">
        <div class="quote_box" v-if="!success">
            <div class="products_box">
                <div class="product_body">
                    <img :src="productsData.products_img" class="products_img head_img" alt="" />
                    <div class="head_right">
                        <div class="products_detail head_detail">
                            <p class="products_name" v-html="productsData.products_name"></p>
                            <p class="products_id">
                                <span>{{ $c("pages.ShoppingCart.FS_PN") }}{{ productsData.products_model }}</span>
                                <span>SKU:{{ productsData.products_id }}</span>
                            </p>
                        </div>
                        <div class="head_qty">
                            <qty-box isNewStyle :num="qty" @change="qtyChange"></qty-box>
                        </div>
                    </div>
                </div>
            </div>
            <div class="line"></div>
            <fs-warn class="sign_in_warn" v-if="!isLogin"> <p v-html="`${$c('pages.Products.signIn').replace('XXXX', localePath({ name: 'login', query: { redirect: $route.fullPath } }))}`"></p> </fs-warn>
            <div class="box-title">{{ $c("form.form.letUsContactYou") }}</div>
            <form class="quote_form" @submit.prevent="submit">
                <div class="form_item">
                    <label class="form_item_info">{{ $c("form.form.first_name") }}</label>
                    <input type="text" class="inp is_new" v-model.trim="form.first_name" @input="inputCheck('first_name')" />
                    <validate-error :error="errors.first_name"></validate-error>
                </div>
                <div class="form_item">
                    <label class="form_item_info">{{ $c("form.form.last_name") }}</label>
                    <input type="text" class="inp is_new" v-model.trim="form.last_name" @input="inputCheck('last_name')" />
                    <validate-error :error="errors.last_name"></validate-error>
                </div>
                <div class="form_item">
                    <label class="form_item_info">{{ $c(country_label) }}</label>
                    <select-country isNewStyle></select-country>
                    <validate-error :error="errors.iso_code"></validate-error>
                </div>
                <div class="form_item" v-if="isShowState">
                    <label class="form_item_info">{{ $c("single.ProductReturnForm.state.tit") }}</label>
                    <RegionSelect isNewStyle ref="regionSelect" />
                </div>
                <div class="form_item">
                    <label class="form_item_info">{{ $c("form.form.phone_number") }}</label>
                    <input type="text" class="inp is_new" v-model.trim="form.phone_number" @input="inputCheck('phone_number')" />
                    <validate-error :error="errors.phone_number"></validate-error>
                </div>
                <div class="form_item">
                    <label class="form_item_info">{{ $c("form.form.email_ddress") }}</label>
                    <input type="text" class="inp is_new" v-model.trim="form.email" @input="inputCheck('email')" />
                    <validate-error :error="errors.email"></validate-error>
                </div>

                <div class="form_item one_row">
                    <label class="form_item_info">{{ $c("form.form.Comments_Questions") }}</label>
                    <textarea class="text_area is_new" maxlength="500" v-model.trim="form.description" @input="inputCheck('description')"></textarea>
                    <validate-error :error="errors.description"></validate-error>
                </div>
                <PolicyCheck class="form_item one_row policy" v-model="form.checked" @change="inputCheck('checked')" :error="errors.checked"></PolicyCheck>
            </form>
        </div>
        <div>
            <g-recaptcha ref="grecaptcha" @getValidateCode="getValidateCode"></g-recaptcha>
        </div>
        <div class="btn_box" slot="footer">
            <fs-button class="sbtn" htmlType="submit" :loading="sbtn_loading" @click="submit">{{ $c("pages.Products.Submit") }}</fs-button>
        </div>
        <div class="success_box" v-if="success">
            <div class="success_title">
                <span class="iconfont iconfont_success">&#xf060;</span>
                <p>{{ $c("pages.Products.quote_submit_success") }}</p>
            </div>
            <div class="success_info">{{ $c("pages.Products.reply_24_hours") }}</div>
        </div>
    </fs-popup-new>
</template>

<script>
import FsButton from "@/components/FsButton/FsButton"
import FsPopupNew from "@/components/FsPopupNew/FsPopupNew"
import QtyBox from "@/components/QtyBox/QtyBox"
import ValidateError from "@/components/ValidateError/ValidateError"
import SelectCountry from "@/components/SelectCountry/SelectCountry"
import RegionSelect from "@/components/RegionSelect/RegionSelect"
import FsWarn from "@/components/FsWarn/FsWarn.vue"
import { email_valdate } from "@/constants/validate"
import { isObject } from "@/util/types"
import { mapState, mapGetters } from "vuex"
import GRecaptcha from "@/components/GRecaptcha/GRecaptcha"
import { isNeedGrecaptcha } from "../../util/grecaptchaHost"
import PolicyCheck from "@/components/PolicyCheck/PolicyCheck.vue"
export default {
    name: "GetQuote",
    components: {
        FsPopupNew,
        FsButton,
        QtyBox,
        SelectCountry,
        ValidateError,
        RegionSelect,
        GRecaptcha,
        FsWarn,
        PolicyCheck,
    },
    props: {
        productsData: {
            type: Object,
            required: true,
            default() {
                return {
                    products_img: "",
                    products_id: "",
                    products_name: "",
                    qty: 1,
                }
            },
        },
    },
    data() {
        return {
            qty: 1,
            sbtn_loading: false,
            success: false,
            form: {
                first_name: this.$store.state.userInfo.userInfo ? this.$store.state.userInfo.userInfo.customers_firstname : "",
                last_name: this.$store.state.userInfo.userInfo ? this.$store.state.userInfo.userInfo.customers_lastname : "",
                iso_code: "",
                phone_number: "",
                email: "",
                description: "",
                checked: false,
            },
            errors: {
                first_name: "",
                last_name: "",
                iso_code: "",
                phone_number: "",
                email: "",
                description: "",
                checked: "",
            },
            recaptchaTp: false,
            recaptchaVal: "",
        }
    },
    computed: {
        ...mapState({
            isLogin: (state) => state.userInfo.isLogin,
            current_country_code: (state) => state.selectCountry.current_country_code,
            current_country_name: (state) => state.selectCountry.current_country_name,
            select_country_code: (state) => state.selectCountry.select_country_code,
        }),
        ...mapGetters({
            filter: "selectCountry/exclude_current_country_code",
            isShowState: "selectCountry/isShowState",
            country_label: "selectCountry/country_label",
        }),
    },
    created() {
        this.qty = this.productsData.qty
    },
    mounted() {
        //console.log(this.form.first_name)
        // if(this.current_country_code){
        // 	this.filter.push(this.current_country_code);
        // 	this.filter_country.code = this.current_country_code;
        // 	this.filter_country.name = `Ship outside the  ${this.current_country_code}`
        // }
        // console.log(this.filter_country);
        // console.log(this.filter)
    },
    methods: {
        resetForm() {
            this.success = false
            this.qty = this.productsData.qty
            this.sbtn_loading = false
            this.form = {
                first_name: this.$store.state.userInfo.userInfo ? this.$store.state.userInfo.userInfo.customers_firstname : "",
                last_name: this.$store.state.userInfo.userInfo ? this.$store.state.userInfo.userInfo.customers_lastname : "",
                iso_code: "",
                phone_number: "",
                email: "",
                description: "",
                checked: false,
            }
            this.errors = {
                first_name: "",
                last_name: "",
                iso_code: "",
                phone_number: "",
                email: "",
                description: "",
                checked: "",
            }
        },
        close() {
            this.resetForm()
            this.$emit("close")
        },
        qtyChange(q) {
            this.qty = q
        },
        inputCheck(attr) {
            let flag = false
            if (attr === "first_name") {
                if (!this.form[attr].replace(/\s+/g, "")) {
                    this.errors[attr] = this.$c("form.validate.first_name.first_name_required")
                    flag = true
                } else {
                    if (this.form[attr].replace(/\s+/g, "").length > 40) {
                        this.errors[attr] = this.$c("form.validate.first_name.first_name_max")
                        flag = true
                    } else if (this.form[attr].replace(/\s+/g, "").length < 2) {
                        this.errors[attr] = this.$c("form.validate.first_name.first_name_min")
                        flag = true
                    } else {
                        this.errors[attr] = ""
                    }
                }
            } else if (attr === "last_name") {
                if (!this.form[attr].replace(/\s+/g, "")) {
                    this.errors[attr] = this.$c("form.validate.last_name.last_name_required")
                    flag = true
                } else {
                    if (this.form[attr].replace(/\s+/g, "").length > 40) {
                        this.errors[attr] = this.$c("form.validate.last_name.last_name_max")
                        flag = true
                    } else if (this.form[attr].replace(/\s+/g, "").length < 2) {
                        this.errors[attr] = this.$c("form.validate.last_name.last_name_min")
                        flag = true
                    } else {
                        this.errors[attr] = ""
                    }
                }
            } else if (attr === "email") {
                if (!this.form[attr].replace(/\s+/g, "")) {
                    this.errors[attr] = this.$c("form.validate.email.email_required")
                    flag = true
                } else {
                    if (!email_valdate.test(this.form[attr].replace(/\s+/g, ""))) {
                        this.errors[attr] = this.$c("form.validate.email.email_validate2")
                        flag = true
                    } else {
                        this.errors[attr] = ""
                    }
                }
            } else if (attr === "phone_number") {
                if (!this.form[attr].replace(/\s+/g, "")) {
                    this.errors[attr] = this.$c("form.validate.phone.phone_required")
                    flag = true
                } else {
                    if (!/^\d{6,}$/g.test(this.form[attr].replace(/\s+/g, ""))) {
                        this.errors[attr] = this.$c("form.validate.phone.phone_validate")
                        flag = true
                    } else {
                        this.errors[attr] = ""
                    }
                }
            } else if (attr === "description") {
                if (!this.form[attr].replace(/\s+/g, "")) {
                    this.errors[attr] = this.$c("form.validate.comments_questions.comments_questions_required")
                } else {
                    this.errors[attr] = ""
                }
            } else if (attr === "company_name") {
                if (!this.form[attr].replace(/\s+/g, "")) {
                    this.errors[attr] = ""
                } else {
                    if (!company_name_validate.test(this.form[attr].replace(/\s+/g, ""))) {
                        this.errors[attr] = this.$c("form.validate.company_name.company_name_validate")
                        flag = true
                    } else {
                        this.errors[attr] = ""
                    }
                }
            } else if (attr === "checked") {
                if (this.form[attr]) {
                    this.errors[attr] = ""
                } else {
                    this.errors[attr] = this.$c("form.form.errors.check2_error")
                    flag = true
                }
            }
            return flag
        },
        submit() {
            let arr = []
            let attr = ["first_name", "last_name", "iso_code", "phone_number", "email", "description", "checked"]
            attr.map((item) => {
                let f = this.inputCheck(item)
                arr.push(f)
            })
            if (arr.includes(true) || this.sbtn_loading) {
                return
            }
            if (["www.fs.com", "newfs.whgxwl.com", "pre-shop.whgxwl.com"].includes(window.location.hostname)) {
                if (!this.recaptchaTp && this.$refs.grecaptcha) {
                    this.$refs.grecaptcha.clickRecaptcha()
                    return
                }
            }
            this.sbtn_loading = true

            let { first_name, last_name, email, description, phone_number } = this.form

            let obj = {
                first_name,
                last_name,
                email,
                description,
                phone_number,
                iso_code: this.select_country_code,
                products: [],
            }

            obj.products.push({
                products_id: this.productsData.products_id,
                attributes: this.productsData.attributes,
                qty: this.qty,
                isChecked: 1,
            })
            if (this.$refs.regionSelect) {
                obj.state = this.$refs.regionSelect.state || ""
            }
            this.$axios
                .post("/api/products/insert_products_quotes", obj, { headers: { "g-recaptcha-response": this.recaptchaVal } })
                .then((res) => {
                    this.sbtn_loading = false
                    this.success = true
                    if (window.dataLayer) {
                        window.dataLayer.push({
                            event: "uaEvent",
                            eventCategory: `productDetailPage_${this.productsData.products_id}`,
                            eventAction: `get_a_quote`,
                            eventLabel: `Submit_Success`,
                            nonInteraction: false,
                        })
                    }

                    this.initGrecaptcha()
                    if (this.$refs.grecaptcha) {
                        this.$refs.grecaptcha.grecaptchaError = ""
                    }
                })
                .catch((err) => {
                    this.initGrecaptcha()
                    this.sbtn_loading = false
                    if (err.code == 422) {
                        if (err.errors && isObject(err.errors)) {
                            for (let attr in err.errors) {
                                this.errors[attr] = err.errors[attr]
                            }
                        }
                    }
                    if (window.dataLayer) {
                        window.dataLayer.push({
                            event: "uaEvent",
                            eventCategory: `productDetailPage_${this.productsData.products_id}`,
                            eventAction: `get_a_quote`,
                            eventLabel: `Submit_Fail`,
                            nonInteraction: false,
                        })
                    }
                    if (isNeedGrecaptcha(window.location.hostname)) {
                        if (err.code === 409 && this.$refs.grecaptcha) {
                            this.$refs.grecaptcha.grecaptchaError = this.$c("pages.Login.regist.grecaptcha_error")
                        }
                    }
                    if (err.code === 403 || err.code === 400) {
                        this.$message.error(err.message)
                    }
                })
        },
        // 谷歌人机校验
        getValidateCode(val) {
            console.log("val", val)

            this.recaptchaTp = val.recaptchaTp
            if (val.recaptchaTp) {
                this.recaptchaVal = val.recaptchaVal
                this.submit()
            }
        },
        // 初始化谷歌验证
        initGrecaptcha() {
            this.recaptchaTp = false
            this.recaptchaVal = ""
        },
    },
    watch: {
        productsData(newVal) {
            this.qty = newVal.qty
        },
    },
}
</script>

<style lang="scss" scoped>
.products_box {
    background-color: #fafafb;
    padding: 16px;
    border-radius: 4px;
    .product_body {
        display: flex;
        align-items: center;
        .products_name {
            @include font14;
            color: $textColor1;
            padding-right: 12px;
        }
        .products_id {
            @include font12;
            color: $textColor3;
            padding: 4px 0 8px 0;

            span {
                margin-right: 12px;
            }
        }
    }
    .head_img {
        width: 68px;
        height: 68px;
        mix-blend-mode: multiply;
        flex-shrink: 0;
        margin-top: -8px;
    }
    .head_right {
        flex: 1;
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding-left: 20px;
        display: flex;
        @include mediaM {
            flex-direction: column;
            align-items: start;
        }
    }
    .head_detail {
        flex: 1;
    }
}
.line {
    border-top: 1px solid $borderColor2;
    margin-top: 20px;
    margin-bottom: 16px;
}
.sign_in_warn {
    margin-bottom: 12px;
}
.box-title {
    margin: 0px 0 12px 0;
    @include font16();
    font-weight: 600;
    color: $textColor1;
}
.quote_form {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    grid-gap: 12px;
    .form_item {
        &.one_row {
            grid-column: span 2;
        }
        .form_item_info {
            display: inline-block;
            font-weight: 400;
            @include font12;
            color: $textColor3;
            margin-bottom: 4px;
        }

        .check_box {
            display: flex;
            align-items: center;
            cursor: pointer;
            .checked {
                font-size: 14px;
                margin-right: 8px;
            }
            input[type="checkbox"] {
                width: 14px;
                height: 14px;
            }

            .check_info {
                @include font13;
                color: $textColor3;
                > a {
                    color: $textColor1;
                }
            }
        }
        // :deep(.select-country) {
        //     .select-country-active {
        //         background: #f6f6f8;
        //         border: 0;
        //         border-radius: 4px;
        //     }
        // }
        // :deep(.dropdown) {
        //     background-color: #f6f6f8;
        //     .select-label {
        //         border: 0;
        //         border-radius: 4px;
        //     }
        // }
        &.policy {
            margin: 0;
        }
    }
    .btn_box {
        display: flex;
        justify-content: flex-end;
        position: fixed;
        bottom: 0;
        right: 0;
        width: 100%;
        background: #fff;
        padding: 16px 24px 24px 24px;
        :deep(.fs-button) {
            height: 36px;
            padding: 10px 16px;
            font-weight: normal;
        }
    }

    @include mediaM {
        grid-template-columns: repeat(1, 1fr);

        .form_item {
            grid-column: span 2;
        }
    }
}
.btn_box {
    display: flex;
    justify-content: flex-end;
    @include mediaM {
        button {
            width: 100%;
        }
    }
}
.success_box {
    .success_title {
        display: flex;
        align-items: flex-start;
        .iconfont_success {
            font-size: 24px;
            margin-right: 10px;
            color: #18a209;
        }
        > p {
            @include font16;
            color: $textColor1;
        }
    }
    .success_info {
        @include font13;
        color: $textColor3;
        font-weight: normal;
        padding-left: 37px;
    }
}
@include mediaM {
    .products_box .product_body {
        align-items: flex-start;
        .products_name {
            padding-right: 0;
        }
        .head_img {
            margin-top: 0;
        }
    }
}
</style>
