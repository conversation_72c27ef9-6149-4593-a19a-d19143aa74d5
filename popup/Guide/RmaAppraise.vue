<template>
    <fs-popup-new v-bind="$attrs" @close="handlePopupClose" :loading="btnLoading" :title="$c('pages.RMAReturn.guide.title')" v-on="$listeners" transition="slide-up" width="680">
        <section class="appraise">
            <!-- 已评论过 -->
            <div class="submit-success" v-if="isSubmitSuccess">
                <span class="iconfont">&#xe710;</span>
                <p class="title">{{ $c("pages.RMAReturn.guide.commentTips") }}</p>
            </div>
            <!-- 还未评论 -->
            <template v-else>
                <p class="title">{{ $c("pages.RMAReturn.guide.commentTitle") }}</p>
                <ul class="btn-box">
                    <li class="btn" :class="[item.type, item.value === form.like ? 'active' : '']" v-for="(item, index) in btnList" :key="index" @click="handleBtnClick(item)">
                        <span class="iconfont">&#xe719;</span>{{ item.name }}
                    </li>
                </ul>
                <template v-if="form.like !== ''">
                    <div class="textarea-box">
                        <p class="label">{{ $c("pages.CaseDetail.appraise.comments") }}</p>
                        <textarea v-model="form.content" :maxlength="maxlength" :placeholder="$c('pages.CaseDetail.appraise.textareaPlaceholder')"></textarea>
                        <p class="small">{{ form.content.length }}/{{ maxlength }}</p>
                    </div>
                </template>
            </template>
        </section>
        <template #footer>
            <div class="submit">
                <p class="back" @click="handleBack">
                    <span class="iconfont">&#xe702;</span>
                    {{ $c("pages.RMAReturn.guide.back") }}
                </p>
                <fs-button :loading="btnLoading" v-if="!isSubmitSuccess" :disabled="form.like === ''" @click="handleSubmit">{{ $c("common.basic.submit") }}</fs-button>
            </div>
        </template>
    </fs-popup-new>
</template>

<script>
import FsButton from "@/components/FsButton/FsButton"
import FsPopupNew from "@/components/FsPopupNew/FsPopupNew.vue"
import { deepClone } from "@/util/util.js"
const DEFAULT_FORM = {
    like: "",
    content: "",
}
export default {
    name: "RmaAppraise",
    components: {
        FsButton,
        FsPopupNew,
    },
    data() {
        return {
            form: deepClone(DEFAULT_FORM),
            btnList: [
                {
                    name: this.$c("pages.CaseDetail.appraise.dissatisfied"),
                    value: 0,
                    type: "dissatisfied",
                    eventLabel: "Dissatisfied",
                },
                {
                    name: this.$c("pages.CaseDetail.appraise.satisfied"),
                    value: 1,
                    type: "satisfied",
                    eventLabel: "Satisfied",
                },
            ],
            maxlength: 5000,
            btnLoading: false,
            isSubmitSuccess: false,
        }
    },
    methods: {
        resetPage() {
            this.form = deepClone(DEFAULT_FORM)
            this.isSubmitSuccess = false
        },
        handleBack() {
            this.$emit("back")
            this.resetPage()
        },
        handlePopupClose() {
            this.$emit("close")
            this.resetPage()
        },
        handleBtnClick(item) {
            this.form.like = item.value
        },
        handleSubmit() {
            if (this.btnLoading) return
            this.btnLoading = true
            this.fetchSubmit()
        },
        async fetchSubmit() {
            try {
                const params = {
                    ...this.form,
                }
                await this.$axios.post("/api/account/rma_review", params)
                this.isSubmitSuccess = true
            } catch (error) {
                this.$message.error(error.message)
            }
            this.btnLoading = false
        },
        buriedPointWrapper(params = {}) {
            const maxParams = Object.assign(
                {
                    event: "uaEvent",
                    nonInteraction: false,
                    eventCategory: "Personal Hub_Case Page Detail",
                    eventAction: "",
                    eventLabel: "",
                },
                params
            )
            if (window.dataLayer) {
                window.dataLayer.push(maxParams)
            }
        },
    },
}
</script>

<style scoped lang="scss">
.appraise {
    background: #ffffff;
    color: #19191a;
    width: 100%;
    position: relative;
    padding: 20px 32px;

    .title {
        @include font16();
        font-weight: 600;
    }
    .btn-box {
        display: flex;
        margin-top: 16px;
        .btn {
            padding: 8px;
            border: 1px solid #cccccc;
            border-radius: 3px;
            width: 214px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            @include font14();
            color: #707070;
            transition: all 0.3s;
            .iconfont {
                margin-right: 8px;
                font-weight: 600;
            }
            &:first-child {
                margin-right: 12px;
            }
            &.dissatisfied {
                .iconfont {
                    transform: rotate(180deg);
                }
            }
            &:hover {
                background: #fafafb;
                color: #19191a;
            }
            &.active {
                background: #fafafb;
                color: #19191a;
                border: 1px solid #19191a;
            }
        }
    }
    .textarea-box {
        margin-top: 20px;
        .label {
            @include font12();
            margin-bottom: 4px;
        }
        .small {
            margin-top: 4px;
            text-align: right;
            color: #707070;
            @include font12();
        }
    }

    .submit-success {
        display: flex;
        flex-direction: column;
        align-items: center;

        .iconfont {
            font-size: 50px;
            color: #329a34;
            line-height: 1;
        }
        .title {
            @include font16();
            font-weight: 600;
            color: #19191a;
            text-align: center;
            margin: 16px 0;
        }
        .describe {
            @include font14();
            color: #707070;
            text-align: center;
        }
    }
}
.submit {
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-top: 1px solid #e5e5e5;
    padding: 20px 32px;
    .back {
        color: #0060bf;
        @include font13();
        display: flex;
        align-items: center;
        cursor: pointer;
        .iconfont {
            @include font12;
            margin-right: 4px;
        }
    }
}
@include mediaM {
    .appraise {
        padding: 20px 16px;

        .submit-success {
            padding: 36px 16px;
        }
        .btn-box {
            margin-top: 20px;
            flex-direction: column-reverse;
            .btn {
                width: 100%;
                &:first-child {
                    margin-right: 0;
                    margin-top: 12px;
                }
            }
        }
    }
    .submit {
        padding: 20px 16px;
        flex-direction: column;
        ::v-deep {
            .fs-button {
                width: 100%;
                margin-top: 16px;
            }
        }
    }
}
</style>
