<template>
    <FsPopupNew v-bind="$attrs" v-on="$listeners" :title="$c('pages.OrderHistory.sampleRequest.extendTrial')" :loading="loading">
        <div class="extend_trial" ref="extend_trial">
            <div class="content">
                <p>{{ $c("pages.OrderHistory.sampleRequest.howLong") }}</p>
                <ul class="extend_day">
                    <li v-for="(v, i) in list" :key="i">
                        <label> <input type="radio" :value="v.val" v-model="days" name="days" />{{ v.txt }}</label>
                    </li>
                </ul>
                <div class="reason">
                    <p>{{ $c("pages.OrderHistory.sampleRequest.reason") }}</p>
                    <textarea
                        v-model.trim="comments"
                        @input="checkValue('comments')"
                        maxlength="5000"
                        :placeholder="['cn', 'hk', 'tw', 'mo'].includes(website) ? $c('form.form.extendTrialPH') : $c('form.form.comments_placeholder')"></textarea>
                    <div>
                        <p><validate-error :error="errors.comments"></validate-error></p>
                        <span class="length">{{ comments.length }}/5000</span>
                    </div>
                </div>

                <div class="upload_file">
                    <upload-file accept=".pdf,image/jpg,image/jepg,image/png" type="file" :text="$c('pages.OrderDetail.pop.uploadFile')" :limit="20" :multiple="true" :maxSize="5 * 1024 * 1024" @change="handleChange">
                        <fs-popover slot="tip">{{ $c("pages.OrderDetail.pop.allow") }}</fs-popover>
                    </upload-file>
                </div>
                <PolicyCheck v-model="check" @change="checkValue('check')" :error="errors.check" />
            </div>
        </div>
        <slot name="footer">
            <div class="btn">
                <FsButton type="grayline" :text="$c('pages.OrderDetail.pop.cancel')" @click="close" v-if="!btnLoading" />
                <FsButton type="red" :text="$c('pages.OrderDetail.pop.submit')" @click="submit" :loading="btnLoading" /></div
        ></slot>
    </FsPopupNew>
</template>

<script>
import FsPopupNew from "@/components/FsPopupNew/FsPopupNew"
import FsButton from "@/components/FsButton/FsButton.vue"
import ValidateError from "@/components/ValidateError/ValidateError.vue"
import FsPopover from "@/components/FsPopover"
import UploadFile from "@/components/UploadFile/UploadFile"
import { mapState } from "vuex"
import PolicyCheck from "@/components/PolicyCheck/PolicyCheck.vue"
export default {
    name: "ExtendTrial",
    components: {
        FsButton,
        ValidateError,
        FsPopupNew,
        FsPopover,
        UploadFile,
        PolicyCheck,
    },
    props: {},
    data() {
        return {
            loading: false,
            orders_id: "",
            days: 15,
            comments: "",
            upload: [],
            check: false,
            errors: {
                comments: "",
                check: "",
            },
            list: [
                { val: 15, txt: this.$c("pages.OrderHistory.sampleRequest.Xdays").replace("xx", "15") },
                { val: 30, txt: this.$c("pages.OrderHistory.sampleRequest.Xdays").replace("xx", "30") },
                { val: 60, txt: this.$c("pages.OrderHistory.sampleRequest.Xdays").replace("xx", "60") },
            ],
            btnLoading: false,
        }
    },
    computed: {
        ...mapState({
            pageGroup: (state) => state.ga.pageGroup,
            website: (state) => state.webSiteInfo.website,
        }),
    },
    created() {
        if (["cn", "hk", "tw", "mo"].includes(this.website)) {
            this.list = [
                { val: 30, txt: this.$c("pages.OrderHistory.sampleRequest.Xdays").replace("xx", "30") },
                { val: 60, txt: this.$c("pages.OrderHistory.sampleRequest.Xdays").replace("xx", "60") },
            ]
        }
    },
    methods: {
        init(id) {
            this.orders_id = id
        },
        checkValue(v) {
            if (v == "comments") {
                if (["cn", "hk", "tw", "mo"].includes(this.website)) {
                    if (this.comments.length < 2) {
                        this.errors.comments = this.$c("pages.OrderDetail.pop.writeRequestCnMore")
                    } else if (this.comments.length == 0) {
                        this.errors.comments = this.$c("pages.OrderDetail.pop.writeRequestCn")
                    } else {
                        this.errors.comments = ""
                    }
                } else {
                    if (this.comments.length < 3) {
                        this.errors.comments = this.$c("pages.OrderDetail.pop.writeRequest")
                    } else {
                        this.errors.comments = ""
                    }
                }
            } else {
                if (!this.check) {
                    this.errors.check = this.$c("pages.Review.form.agree")
                } else {
                    this.errors.check = ""
                }
            }
        },
        async submit() {
            this.checkValue("comments")
            this.checkValue("check")
            if (this.errors.check || this.errors.comments) {
                return
            }
            const data = new FormData()
            this.upload.forEach((v) => {
                data.append("upload[]", v)
            })
            data.append("comments", this.comments)
            data.append("days", this.days)
            data.append("orders_id", this.orders_id)
            this.btnLoading = true
            await this.$axios
                .post("/api/submit_sample_delay_apply", data)
                .then((res) => {
                    this.btnLoading = false
                    this.gioHandle("Success")
                    this.$router.push(this.localePath(`/support_ticket_detail?type=${res.data.type}&number=${res.data.number}`))
                })
                .catch((err) => {
                    this.gioHandle("Fail")
                    this.$message.error(err.message)
                })
        },
        gioHandle(v) {
            window.dataLayer &&
                window.dataLayer.push({
                    event: "uaEvent",
                    eventCategory: this.pageGroup,
                    eventAction: "extend_trial",
                    eventLabel: `Submit ${v}`,
                    nonInteraction: false,
                })
        },
        handleChange(v) {
            this.upload = v.files
        },
        close() {
            this.$emit("close")
        },
    },
}
</script>

<style lang="scss" scoped>
.extend_trial {
    padding: 20px 32px;
    color: #19191a;
    width: 680px;

    .content {
        > p {
            @include font16;
            font-weight: 600;
        }
        .extend_day {
            display: flex;
            align-items: center;
            padding: 16px 0;
            li {
                padding-right: 16px;
                input[type="radio"] {
                    margin-right: 8px;
                }
            }
        }
        .reason {
            > p {
                @include font12;
                padding-bottom: 4px;
            }
            > div {
                display: flex;
                justify-content: space-between;
                span {
                    @include font12;
                    color: #707070;
                    padding-top: 4px;
                    padding-left: 16px;
                }
            }
            textarea {
                height: 110px;
            }
        }
        .error {
            color: #936d1d;
            background: #f9efdb;
            border: 1px solid #f9efdb;
            border-radius: 2px;
            padding: 0 20px;
            height: 40px;
            display: flex;
            align-items: center;
            margin-bottom: 8px;
            i {
                margin-right: 12px;
            }
        }

        .upload_file {
            position: relative;
            margin-top: 8px;
            margin-bottom: 16px;

            ::v-deep .file-btn {
                padding: 10px 24px;
                border-radius: 3px;
                border: 1px solid #e5e5e5;
                .iconfont {
                    margin-right: 8px;
                }
            }
            ::v-deep .fs-popover {
                margin-left: 8px;
            }
        }
        .agree_check {
            display: flex;
            align-items: center;
            @include font13;
            color: #707070;
            input {
                margin-right: 8px;
            }
        }
    }
}
.btn {
    display: flex;
    justify-content: flex-end;
    padding: 20px 32px;
    border-top: 1px solid #e5e5e5;
    button {
        width: 110px;
        margin-left: 10px;
    }
}
@media (max-width: 960px) {
    .extend_trial {
        width: 100%;
        .content .extend_day {
            display: block;
            li {
                &:not(:first-child) {
                    margin-top: 4px;
                }
            }
        }
    }
}
</style>
