<template>
    <fs-popup-new :show="show" @close="close" v-bind="$attrs" v-on="$listeners" :title="type == 1 ? $c('pages.OrderDetail.share.shareWithBusinessAccount') : $c('pages.OrderDetail.share.unshare')" :loading="loading">
        <div class="technical_support" ref="technical_support">
            <div class="content" v-if="type == 1">
                <div class="choose">
                    <p>{{ $c("pages.OrderDetail.share.chooseATeam") }}</p>
                    <fs-select :options="Options" v-model="team_id" :placeholder="$c('pages.UserSetting.newSetting.popupText.selectPlace')"></fs-select>
                </div>
                <p>{{ map[page].shareTxt }}</p>
            </div>
            <div class="content" v-else>
                <div class="unshareTit">{{ map[page].unshareTit }}</div>
                <p class="unshareTxt">{{ map[page].unshareTxt }}</p>
            </div>
            <div class="btn">
                <FsButton type="red" :text="type == 1 ? $c('pages.OrderDetail.pop.submit') : $c('popup.share.unshare_button')" @click="submit" :loading="btnLoading" />
                <FsButton class="cancelButton" type="grayline" :text="$c('pages.OrderDetail.pop.cancel')" @click="close" v-if="!btnLoading" />
            </div>
        </div>
    </fs-popup-new>
</template>

<script>
import FsPopupNew from "@/components/FsPopupNew/FsPopupNew"
import FsButton from "@/components/FsButton/FsButton.vue"
import FsSelect from "@/components/FsSelect/FsSelect.vue"
import { mapState } from "vuex"
export default {
    name: "ShareWithBusinessAccount",
    components: {
        FsButton,
        FsPopupNew,
        FsSelect,
    },
    props: {
        page: {
            type: String,
            default: "order",
        },
        type: {
            type: Number,
            default: 1,
        },
        orders_id: {
            type: Number,
            default: 1,
        },
        quotes_id: {
            type: Number,
            default: 1,
        },
        show: {
            type: Boolean,
            default: false,
        },
    },
    computed: {
        ...mapState({
            allTeamList: (state) => state.userInfo.userInfo.companyInfo.allTeamList,
        }),
    },
    data() {
        return {
            loading: false,
            btnLoading: false,
            Options: [],
            team: "",
            shareTxt: "",
            unshareTit: "",
            unshareTxt: "",
            team_id: "",
            map: {
                order: {
                    api: "/api/orders/set_individual_share",
                    shareTxt: this.$c("pages.OrderDetail.share.orderShare"),
                    unshareTit: this.$c("pages.OrderDetail.share.UnshareOrderTit"),
                    unshareTxt: this.$c("pages.OrderDetail.share.UnshareOrderTxt"),
                },

                quote: {
                    api: "/api/quotes_set_individual_share",
                    shareTxt: this.$c("pages.OrderDetail.share.quoteShare"),
                    unshareTit: this.$c("pages.OrderDetail.share.UnshareQuoteTit"),
                    unshareTxt: this.$c("pages.OrderDetail.share.UnshareQuoteTxt"),
                },

                cart: {
                    api: "/api/saveCart/save_cart_set_individual_share",
                    shareTxt: this.$c("pages.OrderDetail.share.savedCartShare"),
                    unshareTit: this.$c("pages.OrderDetail.share.UnshareSavedCartTit"),
                    unshareTxt: this.$c("pages.OrderDetail.share.UnshareSavedCartTxt"),
                },
            },
        }
    },
    watch: {
        show(val) {
            val && this.getInitList()
        },
    },
    mounted() {
        this.getInitlist()
    },
    watch: {},
    methods: {
        getInitlist() {
            this.Options = []
            this.allTeamList?.forEach((v) => this.Options.push({ name: v.name, value: v.id, type: v.type }))
            if (this.Options.length == 1) {
                this.team_id == this.Options[0].value
            }
        },
        async submit() {
            this.btnLoading = true
            let params = {
                team_id: this.team_id,
                type: this.type == 1 ? "add" : "del",
            }
            if (this.page == "order") {
                // params.orders_id = this.orders_id
                // params.is_online = this.$route.query.offline
                if (this.$route.query.offline === "1") {
                    params["online"] = [this.orders_id]
                } else {
                    params["offline"] = [this.orders_id]
                }
            } else if (this.page == "quote") {
                // params.quotes_id = this.quotes_id
                // params.is_online = this.$route.query.type
                if (this.$route.query.type === "1") {
                    params["online"] = [this.quotes_id]
                } else {
                    params["offline"] = [this.quotes_id]
                }
            } else {
                // params.saved_id = this.$route.query.id
                params["online"] = [this.$route.query.id]
            }
            console.log(params)
            this.$axios
                .post(this.map[this.page].api, params)
                .then((res) => {
                    this.btnLoading = false
                    this.team_id = ""
                    this.$emit("success")
                })
                .catch((err) => {
                    this.btnLoading = false
                    this.$message.error(err.message)
                })
        },
        close() {
            this.$emit("close")
        },
    },
}
</script>

<style lang="scss" scoped>
.technical_support {
    padding: 20px 32px 20px;
    @include font14;
    color: #19191a;
    overflow: auto;
    ::v-deep .fs-select .options-wrap.options-wrap-absolute {
        position: relative;
        top: 4px;
    }
    .content {
        > p {
            padding-top: 20px;
        }
        .choose {
            p {
                padding-bottom: 8px;
            }
        }
        .unshareTit {
            @include font16;
        }
        .unshareTxt {
            padding-top: 12px;
            color: #707070;
        }
    }
    :deep(.fs-select) {
        .fs-select-active {
            border: none;
            background-color: #f6f6f8;
            border-radius: 4px;
        }
    }

    .btn {
        display: flex;
        justify-content: flex-start;
        flex-direction: row-reverse;
        margin-top: 40px;
        button {
            min-width: 110px;
            margin-left: 10px;
        }
        .cancelButton {
            border: none;
            color: #707070;
            &:hover {
                background-color: #f2f2f2;
                color: #19191a;
            }
        }
    }
    @media (max-width: 960px) {
        width: 100%;
        padding: 20px 16px;
        .btn {
            display: block;
            position: absolute;
            bottom: 20px;
            width: calc(100% - 32px);
            button {
                width: 100%;
                margin-left: 0;
                &:first-child {
                    margin-bottom: 12px;
                }
            }
        }
    }
}
::v-deep .fs-popup-ctn {
    width: 710px !important;
    @media (max-width: 960px) {
        position: relative;
        height: calc(100% - 64px);
        bottom: -32px;
    }
}
</style>
