<template>
    <fs-popup-new
        :show="show"
        @close="close"
        v-bind="$attrs"
        v-on="$listeners"
        :clickHide="false"
        width="750"
        :title="type === 'share' ? $c('pages.OrderDetail.share.shareWithBusinessAccount') : $c('pages.OrderDetail.share.unshare')"
        :loading="loading">
        <div class="technical_support" ref="technical_support">
            <div class="content" v-if="type === 'share'">
                <div class="choose">
                    <p>{{ $c("pages.OrderDetail.share.chooseATeam") }}</p>
                    <fs-select :options="Options" v-model="team_id" :placeholder="$c('pages.UserSetting.newSetting.popupText.selectPlace')"></fs-select>
                </div>
                <p>{{ map[shareType].shareTxt }}</p>
            </div>
            <div class="content" v-else>
                <div class="unshareTit">{{ map[shareType].unshareTit }}</div>
                <p class="unshareTxt">{{ map[shareType].unshareTxt }}</p>
            </div>
            <div class="btn">
                <FsButton type="red" :text="type === 'share' ? $c('pages.OrderDetail.pop.submit') : $c('popup.share.unshare_button')" @click="submit" :loading="buttonLoading" :disabled="!team_id && type === 'share'" />
                <FsButton class="cancelButton" type="grayline" :text="$c('pages.OrderDetail.pop.cancel')" @click="close" v-if="!btnLoading" />
            </div>
        </div>
    </fs-popup-new>
</template>

<script>
import FsPopupNew from "@/components/FsPopupNew/FsPopupNew"
import FsButton from "@/components/FsButton/FsButton.vue"
import FsSelect from "@/components/FsSelect/FsSelect.vue"
import { mapState } from "vuex"
import { shareMixin, AllowedShareTypes } from "./share"

export default {
    name: "ShareWithBusinessAccount",
    mixins: [shareMixin],
    components: {
        FsButton,
        FsPopupNew,
        FsSelect,
    },
    props: {},
    computed: {
        ...mapState({
            allTeamList: (state) => state.userInfo.userInfo.companyInfo.allTeamList,
        }),
    },
    data() {
        return {
            loading: false,
            btnLoading: false,
            Options: [],
            team_id: "",
            map: {
                order: {
                    api: "/api/orders/set_individual_share",
                    shareTxt: this.$c("pages.OrderDetail.share.orderShare"),
                    unshareTit: this.$c("pages.OrderDetail.share.UnshareOrderTit"),
                    unshareTxt: this.$c("pages.OrderDetail.share.UnshareOrderTxt"),
                },

                quote: {
                    api: "/api/quotes_set_individual_share",
                    shareTxt: this.$c("pages.OrderDetail.share.quoteShare"),
                    unshareTit: this.$c("pages.OrderDetail.share.UnshareQuoteTit"),
                    unshareTxt: this.$c("pages.OrderDetail.share.UnshareQuoteTxt"),
                },

                cart: {
                    api: "/api/saveCart/set_individual_share",
                    shareTxt: this.$c("pages.OrderDetail.share.savedCartShare"),
                    unshareTit: this.$c("pages.OrderDetail.share.UnshareSavedCartTit"),
                    unshareTxt: this.$c("pages.OrderDetail.share.UnshareSavedCartTxt"),
                },
            },
        }
    },
    watch: {
        show(val) {
            val && this.getInitList()
        },
    },
    mounted() {
        this.getInitlist()
    },
    watch: {},
    methods: {
        getInitlist() {
            this.Options = []
            this.allTeamList?.forEach((v) => this.Options.push({ name: v.name, value: v.id, type: v.type }))
            if (this.Options.length == 1) {
                this.team_id == this.Options[0].value
            }
        },
        async submit() {
            this.requestIndividualShare()
        },
        close() {
            this.$emit("close")
        },
    },
}
</script>

<style lang="scss" scoped>
.technical_support {
    padding: 24px;
    padding-top: 16px;
    @include font14;
    color: #19191a;
    overflow: auto;
    ::v-deep .fs-select .options-wrap.options-wrap-absolute {
        position: relative;
        top: 4px;
    }
    .content {
        > p {
            padding-top: 20px;
        }
        .choose {
            p {
                padding-bottom: 8px;
            }
        }
        .unshareTit {
            @include font16;
        }
        .unshareTxt {
            padding-top: 12px;
            color: #707070;
        }
    }

    :deep(.fs-select) {
        .fs-select-active {
            border: none;
            background-color: #f6f6f8;
            border-radius: 4px;
        }
    }

    .btn {
        display: flex;
        justify-content: flex-start;
        flex-direction: row-reverse;
        margin-top: 16px;
        button {
            min-width: 110px;
            margin-left: 16px;
        }
        .cancelButton {
            border: none;
            color: #707070;
            &:hover {
                background-color: #f2f2f2;
                color: #19191a;
            }
        }
    }
    @media (max-width: 960px) {
        width: 100%;
        padding: 20px 16px;
        .btn {
            display: block;
            position: absolute;
            bottom: 20px;
            width: calc(100% - 32px);
            button {
                width: 100%;
                margin-left: 0;
                &:first-child {
                    margin-bottom: 12px;
                }
            }
        }
    }
}
::v-deep .fs-popup-ctn {
    width: 750px !important;
    @media (max-width: 960px) {
        position: relative;
        height: calc(100% - 64px);
        bottom: -32px;
    }
    .fs-popup-header {
        padding: 24px 0 16px;
        margin: 0 24px;

        .title {
            font-family: Open Sans;
            font-size: 18px;
            font-weight: 600;
        }
    }
}
</style>
