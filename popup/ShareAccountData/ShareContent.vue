<template>
    <FsPopupNew transition="slide-up" :title="title" :show="show" :clickHide="false" :width="width" @close="handleClose" :isMDrawer="true" :hideHeader="hideHeader">
        <template slot="header">
            <slot name="header"></slot>
        </template>
        <div class="container" :class="{ containerStyle: nextButton || prevButton }">
            <slot></slot>
        </div>
        <template slot="footer">
            <div class="btn" v-if="nextButton || prevButton">
                <fs-button class="prevButton" v-if="prevButton" type="primary" @click="prevButtonClick">{{ prevButton }}</fs-button>
                <fs-button class="nextButton" v-if="nextButton" :loading="buttonLoading" :disabled="nextDisabled" @click="nextButtonClick">{{ nextButton }}</fs-button>
            </div>
        </template>
    </FsPopupNew>
</template>

<script>
import FsPopupNew from "@/components/FsPopupNew/FsPopupNew.vue"
import FsButton from "@/components/FsButton/FsButton.vue"
import SlideDown from "@/components/SlideDown/SlideDown.vue"

export default {
    components: {
        FsPopupNew,
        FsButton,
        SlideDown,
    },
    props: {
        show: {
            type: Boolean,
            default: () => false,
        },
        hideHeader: {
            type: Boolean,
            default: () => false,
        },
        title: {
            type: String,
            default: () => "",
        },
        width: {
            type: String,
            default: () => "480",
        },
        nextButton: {
            type: String,
            default: () => "",
        },
        nextDisabled: {
            type: Boolean,
            default: () => false,
        },
        prevButton: {
            type: String,
            default: () => "",
        },
        buttonLoading: {
            type: Boolean,
            default: () => false,
        },
    },
    methods: {
        handleClose() {
            this.$emit("change", false)
        },
        prevButtonClick() {
            this.$emit("prevClick")
        },
        nextButtonClick() {
            this.$emit("nextClick")
        },
    },
}
</script>

<style lang="scss" scoped>
::v-deep .fs-popup {
    .fs-popup-header {
        padding: 24px 0 16px;
        margin: 0 24px;

        .title {
            font-family: Open Sans;
            font-size: 18px;
            font-weight: 600;
        }
        &:not(:has(.title)) {
            border: none;
            padding-bottom: 0px;
        }
    }

    .prevButton {
        margin-right: 16px;
        color: #707070;
        &:hover {
            color: #000000;
            background-color: #f2f2f2;
        }
    }

    .fs-popup-footer {
        .btn {
            padding-top: 16px;
            display: flex;
            justify-content: flex-end;
            flex-shrink: 0;
            padding-right: 24px;
            padding-bottom: 24px;
        }
    }
    .container {
        padding: 24px;
        padding-top: 16px;
    }
    .containerStyle {
        padding-bottom: 0px;
    }
}
</style>
