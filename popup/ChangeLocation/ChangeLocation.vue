<template>
    <fs-popup-new v-bind="$attrs" v-on="$listeners" transition="slide-up" width="380" :title="$c('pages.Products.Choose_your_location')" :loading="loading" :isMDrawer="true">
        <div class="change_location_box">
            <p class="delivery_options" v-if="!isLogin">
                {{ $c("pages.Products.delivery_different_location") }} <nuxt-link :to="localePath({ name: 'login', query: { redirect: $route.fullPath } })" target="_blank"> {{ $c("pages.Products.Sign_in") }} </nuxt-link>
                {{ $c("pages.Products.see_your_addresses") }}
            </p>
            <div class="manage_address_box" v-if="isLogin && !loading">
                <template v-if="website == 'cn' && address.length == 0">
                    <p class="manage_info">运输方式和交付时间因不同地点而异。暂无收货地址，<nuxt-link class="address_book" :to="localePath({ name: 'address-book' })" target="_blank">添加</nuxt-link> 您的地址</p>
                </template>
                <template v-else>
                    <p class="manage_info">{{ $c("pages.Products.delivery_different_location") }}</p>
                    <div class="address_box" v-if="address && address.length">
                        <div class="address_list">
                            <label class="address_item" v-for="item in address" :key="item.address_book_id">
                                <input type="radio" :value="item.address_book_id" v-model="address_book_id" @change="selectAddress(item)" />
                                <span class="address_name">{{ `${item.entry_firstname} ${item.entry_lastname}` }}</span>
                                <span v-show="!['cn', 'hk', 'tw', 'mo'].includes(website)" class="address_text">{{
                                    `${item.entry_postcode} ${item.entry_city} ${item.entry_state}
                                                                    ${item.entry_country_name} `
                                }}</span>
                                <span v-show="['cn', 'hk', 'tw', 'mo'].includes(website)" class="address_text">{{
                                    `${item.entry_country_name} ${item.entry_state_name} ${item.entry_city}
                                                                    ${item.entry_suburb} ${item.entry_postcode} `
                                }}</span>
                            </label>
                        </div>
                        <!-- <div v-if="address && address.length" class="address_opacity"></div> -->
                    </div>
                    <nuxt-link class="address_book" :to="localePath({ name: 'address-book' })" target="_blank">{{ $c("pages.Products.Manage_address_book") }}</nuxt-link>
                </template>
            </div>
            <div class="postcdoe_zip_box" v-if="shipping_post.postCode">
                <div class="zip_code_text">
                    <span class="zip_line"></span>
                    <span class="zip_text" v-html="$c('pages.Products.enter_zip_code').replace('XXX', current_country_code)"></span>
                    <span class="zip_line"></span>
                </div>
                <div class="zip_wrap" v-if="default_address_info.postCode">
                    <div class="default_address_box" v-if="!show_zip">
                        <div class="default_address_info">
                            <!-- {{ $c("pages.Products.Deliver_to") }} <span class="default_address">{{ default_address_info.ipInfo }}, {{ current_country_name }}</span> -->
                            <!-- {{ $c("pages.Products.Deliver_to") }} <span class="default_address">{{ default_address_info.postCode ? default_address_info.postCode : default_address_info.ipInfo }}</span> -->
                            {{ $c("pages.Products.Deliver_to") }} <span class="default_address">{{ default_address_info.ipInfo }}</span>
                        </div>
                        <a href="javascript:;" @click.stop="changeZip" class="change_btn">{{ $c("pages.Products.Change") }}</a>
                    </div>
                    <div class="change_address_wrap" v-else>
                        <div class="change_address_box">
                            <div v-if="false" class="default_address_info">
                                <span>{{ $c("pages.Products.Deliver_to") }}</span>
                                <div class="current_country">
                                    <!-- <span class="country-code" :class="`country-${current_country_code?current_country_code.toString().toLowerCase():''}`"> </span> -->
                                    <span class="country_name">{{ current_country_name }}</span>
                                </div>
                            </div>
                            <div class="zip_box">
                                <input class="zip" type="text" v-model.trim="zip" @focus.stop="focus" @blur.stop="blur" maxlength="10" @input="checkValue" />
                                <fs-button class="apply" @click="getData(1)" type="grayline" :loading="apply_btn_loading">{{ $c("pages.Products.Apply") }}</fs-button>
                            </div>
                        </div>
                        <validate-error :error="zip_error"></validate-error>
                    </div>
                </div>
                <div class="express_box" v-if="!zip_error_type && apply_success && shipping && shipping.length && type === 2">
                    <div class="express_box_list">
                        <label class="express_item" v-for="s in shipping" :key="s.code" :class="{ disabled: s.readonly }">
                            <input type="radio" class="radio" :value="s.code" v-model="picked" :disabled="s.readonly" />
                            <div class="express_info">
                                <div class="express_name">
                                    {{ s.title }}
                                    <fs-popover position="right" v-if="s.readonly">
                                        <p v-html="s.bubble || s.notice"></p>
                                    </fs-popover>
                                </div>
                                <div class="express_price" v-html="s.code !== 'selfreferencezones' && !parseFloat(s.transitionCost.usPrice) ? $c('pages.Products.Free') : s.transitionCost.priceFormat"></div>
                            </div>
                        </label>
                    </div>
                </div>
            </div>
            <div class="postcode_other_country" v-if="shipping_post.postCode">
                <div class="zip_code_text">
                    <span class="zip_line"></span>
                    <span class="zip_text">{{ $c("pages.Products.or") }} </span>
                    <span class="zip_line"></span>
                </div>
                <div class="select_country_box" v-if="shipping_post && shipping_post.postCode">
                    <select-country ref="selectCountry" :filter_country="filter_country" :filter="filter" @change="countryChange" position="relative"></select-country>
                </div>
            </div>
            <div class="other_country no_postcode_other_country" v-if="!shipping_post.postCode">
                <div class="zip_code_text">
                    <span class="zip_line"></span>
                    <span class="zip_text">{{ $c("pages.Products.or_change_other_country") }}</span>
                    <span class="zip_line"></span>
                </div>
                <div class="select_country_box" v-if="!shipping_post.postCode">
                    <select-country ref="selectCountry2" position="relative"></select-country>
                </div>
                <div class="express_box" v-if="!address_book_id && select_country_code === current_country_code">
                    <div class="express_box_list" :class="{ express_box_list_cn: ['cn', 'hk', 'tw', 'mo'].includes(website) }">
                        <label class="express_item" v-for="s in shipping" :key="s.code">
                            <input type="radio" class="radio" :value="s.code" v-model="picked" />
                            <div class="express_info">
                                <div class="express_name" v-html="s.title" v-if="s.code != 'sfzones'"></div>
                                <div class="express_name" v-else>
                                    {{ s.title }}
                                    <fs-tip position="bottom">提示：若订单中含有重货产品可能使用其他物流运输方式发货。</fs-tip>
                                </div>
                                <div class="express_ser" v-if="s.code == 'sfzones'" v-html="s.shippingDays"></div>
                                <div class="express_price" v-html="s.code !== 'selfreferencezones' && !parseFloat(s.transitionCost.usPrice) ? $c('pages.Products.Free') : s.transitionCost.priceFormat"></div>
                            </div>
                        </label>
                    </div>
                </div>
            </div>
            <div class="shipping_cost">{{ $c("pages.Products.see_exact_shipping_costs_when_checkout") }}</div>
        </div>
        <template slot="footer">
            <div class="btn_box">
                <fs-button class="btn" :disabled="(type === 2 && show_zip) || zip_error_type" :loading="btn_loading" @click="done">{{ $c("pages.Products.Done") }}</fs-button>
            </div>
        </template>
    </fs-popup-new>
</template>

<script>
import FsButton from "@/components/FsButton/FsButton"
import FsPopupNew from "@/components/FsPopupNew/FsPopupNew"
import ValidateError from "@/components/ValidateError/ValidateError"
import SelectCountry from "@/components/SelectCountry/SelectCountry"
import SlideDown from "@/components/SlideDown/SlideDown"
import FsPopover from "@/components/FsPopover"
import { mapMutations, mapActions, mapState, mapGetters } from "vuex"
import { setCookieOptions } from "@/util/util"
import FsTip from "@/components/FsTip/FsTip"

export default {
    name: "ChangeLocation",
    components: {
        FsPopupNew,
        FsButton,
        SlideDown,
        SelectCountry,
        ValidateError,
        FsTip,
        FsPopover,
    },
    props: {
        products: {
            type: Array,
            default() {
                return []
            },
        },
        shipping_post: {
            type: Object,
            default() {
                return {}
            },
        },
    },
    data() {
        return {
            picked: "",
            loading: false,
            btn_loading: false,
            apply_btn_loading: false,
            btn_disabled: true,
            show_zip: false,
            zip: "",
            type: "", // 1 选择地址  2 改变地址邮编  3 切换站点  4.改变运输方式或者切换站点
            zip_error: "",
            apply_success: false,
            address_book_id: this.$cookies.get("address_book_id") || "",
            select_address: null,
            filter_country: {
                name: "",
                code: "",
            },
            address: [],
            shipping: [],
            default_address_info: {
                city: "",
                countryId: "",
                ipInfo: "",
                postCode: "",
                state: "",
            },
            isDefAddress: "",
        }
    },
    computed: {
        ...mapState({
            isLogin: (state) => state.userInfo.isLogin,
            current_country_code: (state) => state.selectCountry.current_country_code,
            select_country_code: (state) => state.selectCountry.select_country_code,
            isMobile: (state) => state.device.isMobile,
            current_country_name: (state) => state.selectCountry.current_country_name,
            website: (state) => state.webSiteInfo.website,
        }),
        ...mapGetters({
            filter: "selectCountry/exclude_current_country_code",
        }),
        zip_error_type() {
            if (this.zip_error.length > 0) {
                return true
            } else {
                return false
            }
        },
    },
    mounted() {
        // if(this.current_country_code){
        // 	this.filter.push(this.current_country_code);
        // 	this.filter_country.code = this.current_country_code;
        // 	this.filter_country.name = `Ship outside the  ${this.current_country_code}`
        // }
        // console.log(this.filter_country);
        // console.log(this.filter)
    },
    methods: {
        ...mapActions({
            updateSiteInfo: "selectCountry/updateSiteInfo",
        }),
        getData(t) {
            let city = "",
                state = "",
                postCode = ""
            this.address_book_id = this.$cookies.get("address_book_id") || ""
            if (t) {
                if (t === 1) {
                    this.type = 2
                    this.address_book_id = ""
                    this.select_address = null
                    if (!this.zip) {
                        this.zip_error = this.$c("pages.Products.zip_code_is_required")
                        return
                    } else if (this.zip_error_type) {
                        return
                    }
                    postCode = this.zip
                } else if (t === 2) {
                    city = this.select_address.entry_city
                    state = this.select_address.entry_state_name
                    postCode = this.select_address.entry_postcode
                }
            } else {
                city = this.shipping_post.city
                state = this.shipping_post.state
                postCode = this.shipping_post.postCode
                this.zip = postCode
                this.picked = this.shipping_post.shippingMethod
                this.address_book_id = this.$cookies.get("address_book_id") || ""
                if (postCode) {
                    this.type = 2

                    this.select_address = null
                } else {
                    this.type = 4
                    this.select_address = null
                }
            }
            if (t === 1) {
                this.apply_btn_loading = true
            } else {
                this.loading = true
            }
            this.$axios
                .post("/api/products/get_products_detail_shipping_list", {
                    products: this.products,
                    postCode,
                    city,
                    state,
                })
                .then((res) => {
                    if (t === 1) {
                        this.apply_btn_loading = false
                    } else if (t != 2) {
                        this.loading = false
                    } else {
                        this.$emit("close")
                        this.$emit("toog")
                    }
                    if (t !== 2) {
                        this.default_address_info = res.data.default_address_info
                        this.address.splice(0, this.address.length, ...res.data.address)
                        this.shipping.splice(0, this.shipping.length, ...res.data.shipping)
                        if (this.shipping && this.shipping.length > 0) {
                            this.shipping.map((item, index) => {
                                if (item.code == "sfzones") {
                                    this.picked = item.code
                                }
                                if (item.code == this.picked && item.readonly) {
                                    this.picked = this.shipping[0].code
                                }
                            })
                        }
                        this.show_zip = false
                        this.btn_disabled = false
                        this.zip_error = ""
                        this.apply_success = true
                        this.$nextTick(() => {
                            if (res.data.address && res.data.address.length) {
                                let flag = false,
                                    index = 0
                                for (let i = 0; i < res.data.address.length; i++) {
                                    if (res.data.address[i].entry_city == this.default_address_info.city) {
                                        flag = true
                                        index = i
                                    }
                                }
                                if (flag) {
                                    this.selectAddress(res.data.address[index])
                                }
                            }
                        })
                    } else {
                        let params = {
                            postCode: res.data.default_address_info.postCode,
                            countryId: res.data.default_address_info.countryId,
                            city: res.data.default_address_info.city,
                            state: res.data.default_address_info.state,
                            shippingMethod: this.shipping_post.shippingMethod,
                            ipInfo: res.data.default_address_info.ipInfo,
                        }
                        if (this.isLogin) {
                            params.addressId = this.isDefAddress ? this.isDefAddress : 0
                        }
                        this.$cookies.set("address_book_id", this.address_book_id)
                        if (this.select_address.country_code === this.current_country_code) {
                            this.$emit("getPackingPrice", params)
                        } else {
                            this.$cookies.set(`products_address_info_${this.current_country_code}`, this.params)
                            this.updateSiteInfo({ iso_code: this.select_address.country_code })
                        }
                    }

                    console.log("this.shipping", this.shipping)
                })
                .catch((err) => {
                    if (t === 1) {
                        this.apply_btn_loading = false
                    } else {
                        this.loading = false
                    }
                    this.picked = this.shipping_post.shippingMethod
                    if (err.code === 422) {
                        if (err.errors && err.errors.postCode) {
                            if (t === 1) {
                                this.zip_error = err.errors.postCode
                            }
                            if (t === 2) {
                                this.$message(err.errors.postCode)
                            }
                        }
                    }
                })
        },
        selectAddress(item) {
            this.isDefAddress = item.address_book_id
            this.address_book_id = item.address_book_id
            this.select_address = item
            if (this.shipping_post.postCode) {
                this.show_zip = true
                this.apply_success = false
                if (this.shipping_post.postCode) {
                    this.$refs.selectCountry.hideList()
                } else {
                    this.$refs.selectCountry2.hideList()
                }

                this.countrySelect({
                    countries_name: `${this.$c("pages.Products.Ship_outside_the")}  ${this.current_country_code}`,
                    iso_code: this.current_country_code,
                })
            }

            this.type = 1
        },
        focus() {
            this.type = 2
            this.$refs.selectCountry.hideList()
            // this.address_book_id = ""
            // this.select_address = null
        },
        blur() {
            this.type = 1
            if (this.website === "en") {
                if (!/^\d{5}(?:-\d{4})?$/.test(this.zip)) {
                    this.zip_error = "Please enter a valid US zip code"
                } else {
                    this.zip_error = ""
                }
            } else if (this.website === "de") {
                if (!/^\d{5}(?:\d{2})?$/.test(this.zip)) {
                    this.zip_error = "Bitte geben Sie eine gültige DE-Postleitzahl ein"
                } else {
                    this.zip_error = ""
                }
            }
        },
        done() {
            if (this.type === 1) {
                this.getData(2)
            } else if (this.type === 2) {
                let params = {
                    postCode: this.default_address_info.postCode,
                    countryId: this.default_address_info.countryId,
                    city: this.default_address_info.city,
                    state: this.default_address_info.state,
                    shippingMethod: this.picked,
                    ipInfo: this.default_address_info.ipInfo,
                    delete_address_id: 1,
                }
                if (this.isLogin) {
                    params.addressId = this.isDefAddress ? this.isDefAddress : 0
                }
                this.countrySelect({
                    countries_name: `${this.$c("pages.Products.Ship_outside_the")} ${this.current_country_code}`,
                    iso_code: this.current_country_code,
                })
                this.btn_loading = true
                this.$emit("getPackingPrice", params)
            } else if (this.type === 3) {
                this.btn_loading = true
                this.updateSiteInfo({ iso_code: this.filter_country.code })
            } else if (this.type === 4) {
                this.btn_loading = true
                if (this.select_country_code === this.current_country_code) {
                    let params = {
                        postCode: this.shipping_post.postCode,
                        city: this.shipping_post.city,
                        state: this.shipping_post.state,
                        shippingMethod: this.picked,
                        delete_address_id: 1,
                    }
                    if (this.isLogin) {
                        params.addressId = this.isDefAddress ? this.isDefAddress : 0
                    }
                    this.btn_loading = true
                    this.$emit("getPackingPrice", params)
                } else {
                    this.updateSiteInfo({ iso_code: this.select_country_code })
                }
            }
        },
        hideBtnLoading() {
            this.btn_loading = false
        },
        changeZip() {
            this.show_zip = true
            this.btn_disabled = true
        },

        countryChange(item) {
            if (this.shipping_post.postCode) {
                this.type = 3
                this.show_zip = true
                this.zip = ""
                this.zip_error = ""
                this.filter_country.name = item.countries_name
                this.filter_country.code = item.iso_code
            } else {
                this.type = 4
                this.address_book_id = ""
            }
        },
        countrySelect(item) {
            this.filter_country.name = item.countries_name
            this.filter_country.code = item.iso_code
        },
        checkValue() {
            if (this.website === "sg") {
                const reg = /[^\d]/g
                this.zip = this.zip.replace(reg, "") || ""
                if (this.website === "sg") {
                    if (this.zip.length != 6) {
                        this.zip_error = this.$c("form.validate.valid_post_number")
                    } else {
                        this.zip_error = ""
                    }
                }
            }
        },
    },
}
</script>

<style lang="scss" scoped>
.change_location_box {
    // width: 750px;
    padding: 0 24px;

    // height: 477px;
    // overflow: auto;
    @media (max-width: 960px) {
        width: 100%;
        // height: 100%;
        padding: 0 16px;
        // overflow: auto;
    }

    input[type="radio"] {
        margin-right: 8px;
        width: 14px;
        height: 14px;
        font-size: 14px;
    }
}

.delivery_options {
    @include font12;
    line-height: 18px;
    font-weight: 400;
    padding-top: 16px;
    border-top: 1px solid #e5e5e5;
}

.manage_address_box {
    border-top: 1px solid #e5e5e5;
    .manage_info {
        @include font12;
        font-weight: 400;
        padding-top: 16px;

        .address_book {
            @include font12;
            line-height: 18px;
        }
    }

    .address_box {
        position: relative;
        border-radius: 8px;
        border: 1px solid #eee;
        padding: 3px 0;
        margin: 12px 0;

        @media (max-width: 768px) {
            padding: 4px 0;
        }
    }

    .address_opacity {
        position: absolute;
        left: 20px;
        bottom: 1px;
        width: calc(100% - 40px);
        height: 27px;
        background: linear-gradient(180deg, rgba(255, 255, 255, 0.2) 4%, #ffffff 92%);
    }

    .address_list {
        padding: 0 11px;

        @media (max-width: 768px) {
            padding: 0 12px;
        }

        max-height: 102px;
        overflow-y: auto;

        // box-shadow: inset 0px 0px 5px 0px rgb(0 0 0 / 15%);
        // &::-webkit-scrollbar-track {
        //     width: 8px;
        // }
        // &::-webkit-scrollbar-thumb {
        //     background: #b6b6b6 !important;
        //     border-radius: 10px;
        // }
        // &::-webkit-scrollbar {
        //     background: #e5e5e5 !important;
        //     height: 8px;
        //     width: 8px !important;
        //     border-radius: 10px;
        // }
        .address_list_box {
        }

        .address_item {
            display: flex;
            cursor: pointer;
            width: 100%;
            // border-radius: 2px;
            // min-height: 64px;
            cursor: pointer;
            position: relative;
            padding: 7px 0;
            align-items: center;
            box-sizing: border-box;
            @include font12;
            line-height: 18px;
            color: $textColor3;

            .address_name {
                color: $textColor1;
                // font-weight: 600;
                @include font12;
                margin-right: 12px;
            }

            .address_text {
                flex: 1;
            }

            &.address_item_active {
                background: #fdf5f5;
                border-color: #c00000;
                border-bottom: 1px solid #c00000;
            }
        }
    }

    .address_book {
        @include font12;
        line-height: 18px;
    }
}

.zip_code_text {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    // margin: 12px auto;
    margin: 12px auto;

    .zip_line {
        flex: 1;
        height: 1px;
        background: #dee0e3;
    }

    .zip_text {
        flex-shrink: 0;
        background: #ffffff;
        color: $textColor3;
        @include font12;
        padding: 0 10px;
    }
}

.default_address_box {
    border: 1px solid $borderColor5;
    border-radius: 4px;
    padding: 10px 11px;
    display: flex;
    align-items: flex-start;

    .default_address_info {
        @include font13;
        color: $textColor2;

        .default_address {
            color: $textColor1;
            font-weight: 600;
        }
    }

    .change_btn {
        flex-shrink: 0;
        color: #0070bc;
        margin-left: 8px;
        @include font13;
    }
}

.change_address_wrap {
    .zip_box {
        width: 100%;
        justify-content: space-between;

        .zip {
            margin-right: 12px;
            flex: 1;
            @include font14;
        }

        .apply {
            border-color: $borderColor2;
        }
    }
}

.change_address_box {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;

    .default_address_info {
        display: flex;
        align-items: center;
        @include font14;
        color: $textColor3;

        .current_country {
            display: flex;
            align-items: center;
            margin-left: 4px;
            flex-shrink: 0;

            .country-code {
                margin-top: 2px;
            }
        }
    }

    .zip_box {
        display: flex;
        align-items: center;
        flex-shrink: 0;

        .zip {
            width: 94px;
            height: 42px;
            border-radius: 4px;
            border-color: #eee;
        }

        .apply {
            width: auto;
            padding: 0 16px;
            border-radius: 4px;
            border-color: #eee;
        }
    }
}

.express_box {
    // overflow: auto;
    // max-height: 80px;
    position: relative;
    margin-top: 12px;
    padding-left: 2px;
    padding: 3px 0;
    border-radius: 8px;
    border: 1px solid #eee;
    overflow: auto;

    @media (max-width: 768px) {
        padding: 3px 12px;
    }

    .express_box_list {
        padding: 0 11px;
        height: 102px;
        overflow: auto;

        @media (max-width: 768px) {
            padding: 0;
        }
    }

    .express_item {
        display: flex;
        align-items: center;
        padding: 7px 0;
        // padding-right: 18px;
        cursor: pointer;

        &.disabled {
            pointer-events: none;
            cursor: no-drop;

            .express_info {
                color: #707070;

                i {
                    background-color: #f7f7f7;
                    border: 1px solid #ccc;
                }
            }

            ::v-deep .iconfont-tip {
                color: #707070;
                pointer-events: auto;
                cursor: pointer;
            }

            input:disabled {
                border-radius: 50%;
            }
        }

        .radio {
            width: 14px;
            height: 14px;
            font-size: 14px;
            flex-shrink: 0;
        }

        .express_info {
            @include font12;
            line-height: 18px;
            color: $textColor1;
            flex: 1;
            display: flex;
            gap: 12px;
            align-items: center;
            justify-content: space-between;

            .express_name {
                // width: 65.72%;
                // width: 212px;
                @media (max-width: 768px) {
                    // width: 64%;
                }

                @media (max-width: 414px) {
                    // width: fit-content;
                }

                ::v-deep {
                    .info {
                        padding: 8px 20px;
                        width: 220px;
                        @include font12;
                    }
                }
            }

            .express_ser {
                // width: 32.86%;
                // width: 212px;
                @media (max-width: 768px) {
                    // display: none;
                }
            }

            .express_price {
                // width: 18.6%;
                // width: 120px;
                flex-shrink: 0;
                text-align: right;

                @media (max-width: 414px) {
                    width: fit-content;
                }
            }
        }
    }
}

.shipping_cost {
    @include font12;
    line-height: 18px;
    color: $textColor1;
    padding-top: 20px;
    margin-top: 20px;
    margin-bottom: 24px;
    border-top: 1px solid #dee0e3;
}

.btn_box {
    display: flex;
    justify-content: flex-end;
    padding: 0 24px 24px 0;

    @media (max-width: 960px) {
        padding: 16px;
    }

    .btn {
        padding: 0 24px;
        width: auto;

        // min-width: 110px;
        @media (max-width: 960px) {
            width: 100%;
        }
    }
}

::v-deep .fs-popup-header {
    padding: 16px 16px 16px 24px;
    border-bottom: none;
    align-items: start;
    .title_box {
        border-bottom: none;
        padding-bottom: 0;
    }
    .iconfont_close_box {
        padding: 4px;
    }
    .iconfont_close {
        font-size: 20px;
        width: 20px;
        height: 20px;
    }

    .title_box {
        .title {
            padding-top: 8px;
            @include font18;
            font-weight: 600;
            padding-right: 24px;
        }
    }

    @media (max-width: 960px) {
        border-bottom: none !important;
        padding: 16px !important;
        .iconfont_close_box {
            width: auto;
            min-height: auto;
        }
        .title_box {
            .title {
                padding-left: 0;
            }
        }
    }
}

::v-deep .select-country {
    .select-country-active {
        @include font13;
        border-radius: 4px;
        height: 40px;
        padding: 0 11px;
    }
}

::v-deep .fs-popup-ctn {
    // overflow: initial;
    max-height: 80vh;

    .fs-popup-body {
        // overflow: initial;
    }
}

.postcode_other_country {
    .select_country_box {
        // position: relative;
        // width: 100%;
        // height: 40px;
        ::v-deep {
            .select-country {
                // position: absolute;
                // top: 0;
                // z-index: 1;
                // width: 100%;
                @media (max-width: 960px) {
                    // position: relative;
                }
            }
        }
    }
}
</style>
