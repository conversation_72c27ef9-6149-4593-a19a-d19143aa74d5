<template>
    <fs-popup
        class="problem-consultation-pop"
        ref="popup"
        transition="slide-right"
        :show="show"
        :title="$c('single.ContactSales.ContactSales')"
        :loading="loading"
        @close="closeProblemConsultationPop"
        :isFixScroll="isFix"
        :customStyle="customStyle"
        :class="{ success: success, 'problem-consultation-pop-cn': isCn || isCn }">
        <div class="contact-sales-box">
            <div class="cont-box" v-if="!success">
                <!-- <div class="form-box" :style="{ height: isAutoHeight }"> -->
                <div class="form-box" :style="{ height: isAutoHeight }">
                    <div v-if="website === 'sg'" class="whatsapp">
                        <p @click="buriedLink($event)">You can chat with sales via <a href="https://wa.me/6564437951" target="_blank">WhatsApp</a>.</p>
                        <div>
                            <div class="line"></div>
                            <p>or fill out the form below</p>
                            <div class="line"></div>
                        </div>
                    </div>
                    <div class="input-block product" v-if="product_info">
                        <div class="product-item">
                            <img :src="product_info.images_arr" alt="" />
                            <div class="pro-cont" v-if="product_info.products_desc">
                                <div class="pro-tit">{{ product_info.products_desc.products_name }}</div>
                                <div class="pro-info-row">
                                    <span class="pro-price" v-html="price_str"></span>
                                    <div class="pro-id">SKU:{{ product_info.products_desc.products_id }}</div>
                                </div>
                            </div>
                        </div>
                        <div class="check-item">
                            <p class="tit">{{ $c("form.form.howCanHelp") }}</p>
                            <div class="select-list">
                                <label class="select-item" v-for="(v, i) in helpOptions" :key="v">
                                    <input :ref="`help_check_${i}`" type="checkbox" @change="helpCheckChange($event, i + 1)" />
                                    <span class="input-txt">{{ v }}</span>
                                </label>
                            </div>
                            <div class="select-other" :class="{ 'check-input-error': errors.help_other_error }">
                                <div class="other-check-box">
                                    <label>
                                        <input :ref="`help_check_other`" type="checkbox" @change="helpCheckChange($event, 'other')" />
                                        <span class="input-txt">{{ $c("form.form.other_txt") }}</span>
                                    </label>
                                </div>
                                <div class="other-input-box">
                                    <input
                                        :class="{ error_input: errors.help_other_error }"
                                        class="is_new"
                                        type="text"
                                        v-show="form.help.includes('other')"
                                        v-model.trim="form.help_other"
                                        placeholder=""
                                        @input="blurInput('help_other')"
                                        @blur="blurInput('help_other')" />
                                    <validate-error v-if="form.help.includes('other')" :error="errors.help_other_error"></validate-error>
                                </div>
                            </div>
                            <validate-error :error="errors.help_error"></validate-error>
                        </div>
                    </div>
                    <template v-if="!isCn">
                        <div class="input-block">
                            <div class="input-item width100">
                                <p class="tit">{{ $c("form.form.first_name") }}</p>
                                <input
                                    v-model.trim="form.entry_firstname"
                                    :class="{ error_input: errors.entry_firstname_error }"
                                    @input="blurInput('entry_firstname')"
                                    @blur="blurInput('entry_firstname')"
                                    class="inp is_new"
                                    type="text" />
                                <validate-error :error="errors.entry_firstname_error"></validate-error>
                            </div>
                        </div>
                        <div class="input-block">
                            <div class="input-item width100">
                                <p class="tit">{{ $c("form.form.last_name") }}</p>
                                <input
                                    v-model.trim="form.entry_lastname"
                                    :class="{ error_input: errors.entry_lastname_error }"
                                    @input="blurInput('entry_lastname')"
                                    class="inp is_new"
                                    type="text"
                                    @blur="blurInput('entry_lastname')" />
                                <validate-error :error="errors.entry_lastname_error"></validate-error>
                            </div>
                        </div>
                    </template>
                    <template v-else>
                        <div class="input-block input-block-cn">
                            <div class="input-item">
                                <p class="tit">{{ $c("form.form.last_name") }}</p>
                                <input
                                    v-model.trim="form.entry_lastname"
                                    :class="{ error_input: errors.entry_lastname_error }"
                                    @input="blurInput('entry_lastname')"
                                    class="inp is_new"
                                    type="text"
                                    @blur="blurInput('entry_lastname')" />
                                <validate-error :error="errors.entry_lastname_error"></validate-error>
                            </div>
                            <div class="input-item">
                                <p class="tit">{{ $c("form.form.first_name") }}</p>
                                <input
                                    v-model.trim="form.entry_firstname"
                                    :class="{ error_input: errors.entry_firstname_error }"
                                    @input="blurInput('entry_firstname')"
                                    @blur="blurInput('entry_firstname')"
                                    class="inp is_new"
                                    type="text" />
                                <validate-error :error="errors.entry_firstname_error"></validate-error>
                            </div>
                        </div>
                    </template>
                    <div class="input-block" v-if="isCn">
                        <div class="input-item width100">
                            <p class="tit">{{ $c("form.form.phone_business") }}</p>
                            <input
                                v-model.trim="form.entry_telephone"
                                :class="{ error_input: errors.entry_telephone_error }"
                                @input="blurInput('entry_telephone')"
                                class="inp is_new"
                                type="text"
                                @blur="blurInput('entry_telephone')" />
                            <validate-error :error="errors.entry_telephone_error"></validate-error>
                        </div>
                    </div>
                    <div class="input-block">
                        <div class="input-item width100">
                            <p class="tit">{{ $c("form.form.email") }}{{ isCn ? `(${$c("form.form.optional")})` : " " }}</p>
                            <input
                                v-model.trim="form.email_address"
                                :class="{ error_input: errors.email_address_error }"
                                @input="blurInput('email_address')"
                                class="inp is_new"
                                type="text"
                                @blur="blurInput('email_address')" />
                            <validate-error :error="errors.email_address_error.replace('XXXX', localePath({ name: 'login', query: { redirect: $route.fullPath } }))"></validate-error>
                        </div>
                    </div>
                    <div class="input-block">
                        <div class="input-item width100">
                            <p class="tit">{{ $c("form.form.company_name") }}{{ isCn ? `(${$c("form.form.optional")})` : " " }}</p>
                            <input
                                v-model.trim="form.company_name"
                                :class="{ error_input: errors.company_name_error }"
                                @input="blurInput('company_name')"
                                class="inp is_new"
                                type="text"
                                @blur="blurInput('company_name')" />
                            <validate-error :error="errors.company_name_error"></validate-error>
                        </div>
                    </div>
                    <div class="input-block">
                        <div class="input-item width100" :class="{ error: errors.requirements_error }">
                            <p class="tit" v-if="!isCn">{{ $c("form.form.comment") + `${product_info ? " (" + $c("form.form.optional") + ")" : ""}` }}</p>
                            <p class="tit" v-else>{{ $c("form.form.comment") }}</p>
                            <textarea
                                :class="{ error_input: errors.requirements_error }"
                                v-model.trim="form.requirements"
                                maxlength="5000"
                                class="textarea is_new"
                                @input="blurInput('requirements')"
                                @blur="blurInput('requirements')"></textarea>
                            <div :class="errors.requirements_error ? 'input-item-flex' : 'input-item-number'">
                                <validate-error :error="errors.requirements_error"></validate-error>
                                <span class="textarea-num">
                                    <em :class="{ active: form.requirements.length === 5000 }">{{ form.requirements.length }}</em
                                    >/5000
                                </span>
                            </div>
                        </div>
                    </div>
                    <!-- <div
                        class="agreement_wrap"
                        @click.stop="clickLink($event)"
                        v-html="
                            $c('single.ContactSales.submitTip')
                                .replace('XXXX1', localePath({ name: 'privacy-policy' }))
                                .replace('XXXX2', localePath({ name: 'terms-of-use' }))
                        "></div> -->
                    <PolicyCheck v-model="agree_policy" @change="inputCheck" :error="agree_policy_error" />
                    <div class="footer-box">
                        <fs-button id="contact_sales" tabindex="0" :text="$c('form.form.submit')" @click="submit" :loading="btnLoading" class="submit-btn" htmlType="submit"></fs-button>
                    </div>
                </div>
            </div>
        </div>
        <!-- <div class="footer-box" slot="footer"> -->
        <!-- <fs-button tabindex="0" :text="$c('components.smallComponents.search.cancel')" @click="closeProblemConsultationPop" class="cancel-btn" type="blackline"></fs-button> -->
        <!-- <fs-button id="contact_sales" tabindex="0" :text="$c('form.form.submit')" @click="submit" :loading="btnLoading" class="submit-btn" htmlType="submit"></fs-button> -->
        <!-- </div> -->
        <div class="success-box" v-if="success">
            <div class="success-logo iconfont">&#xe710;</div>
            <div class="success-tit">{{ $c("single.ContactSales.SubmittedSuccessfully") }}</div>
            <div class="success-des" v-html="subStr($c('form.form.success.txt'))"></div>
        </div>
    </fs-popup>
</template>

<script>
import FsPopup from "@/components/FsPopupNew/FsPopupNew.vue"
import FsPopover from "@/components/FsPopover"
import SuccessPopup from "@/popup/success/SuccessPopup.vue"
import FsButton from "@/components/FsButton/FsButton"
import ValidateError from "@/components/ValidateError/ValidateError.vue"
import { mapState, mapGetters } from "vuex"
import { email_valdate, cn_all_phone } from "@/constants/validate.js"
import { debounce } from "@/util/util.js"
import { getRecaptchaToken } from "@/util/grecaptchaHost"
import PolicyCheck from "@/components/PolicyCheck/PolicyCheck.vue"

export default {
    name: "ProblemConsultationPopup",
    components: {
        FsPopup,
        FsPopover,
        SuccessPopup,
        FsButton,
        ValidateError,
        PolicyCheck,
    },
    props: {
        show: {
            type: Boolean,
            default: false,
        },
        customStyle: {
            type: Object,
            default: () => {},
        },
    },
    data() {
        return {
            success: false,
            loading: false,
            btnLoading: false,
            price_str: "",
            form: {
                help: [],
                help_other: "",
                entry_firstname: "",
                entry_lastname: "",
                email_address: "",
                company_name: "",
                requirements: "",
                entry_telephone: "",
            },
            errors: {
                help_error: "",
                help_other_error: "",
                entry_firstname_error: "",
                entry_lastname_error: "",
                email_address_error: "",
                company_name_error: "",
                requirements_error: "",
                entry_telephone_error: "",
            },
            helpOptions: this.$c("form.form.howCanHelpOptions"),
            product_info: "",
            success_timer: null,
            isAutoHeight: "",
            agree_policy: false,
            agree_policy_error: "",
        }
    },
    computed: {
        ...mapState({
            website: (state) => state.webSiteInfo.website,
            isLogin: (state) => state.userInfo.isLogin,
            userInfo: (state) => state.userInfo.userInfo,
            resourcePage: (state) => state.device.resource_page,
            pageGroup: (state) => state.ga.pageGroup,
            screenHeight: (state) => state.device.screenHeight,
            screenWidth: (state) => state.device.screenWidth,
            website: (state) => state.webSiteInfo.website,
        }),
        ...mapGetters({
            isCn: "webSiteInfo/isCn",
        }),
        isFix() {
            if (this.screenWidth <= 768) {
                return true
            } else {
                return false
            }
        },
    },
    mounted() {
        this.handleWindowResize() //初始设置组件高度
        clearTimeout(this.success_timer)
        this.success_timer = null
        window.addEventListener("resize", debounce(this.handleWindowResize, 200))
    },
    beforeDestroy() {
        window.removeEventListener("resize", this.handleWindowResize)
    },
    methods: {
        buriedLink(e) {
            window.dataLayer &&
                window.dataLayer.push({
                    event: "uaEvent",
                    eventCategory: this.pageGroup,
                    eventAction: "floating_button",
                    eventLabel: "WhatsApp Clicks",
                    nonInteraction: false,
                })
        },
        handleWindowResize() {
            if (this.show) {
                this.$nextTick(() => {
                    const isMobile = /(iPhone|iPad|iPod|iOS|Android)/i.test(navigator.userAgent)
                    if (isMobile) {
                        this.isAutoHeight = ""
                        return
                    }
                    const windowHeight = window.innerHeight
                    const distanceToTop = document.querySelector(".problem-consultation-pop .fs-popup")?.offsetTop

                    if (distanceToTop < 64) {
                        this.isAutoHeight = windowHeight - 64 - 48 - 64 - 82 + "px"
                    } else {
                        this.isAutoHeight = 600 - 60 + "px"
                    }
                })
            }
        },
        gaEvent(eventAction, eventLabel) {
            window.dataLayer &&
                window.dataLayer.push({
                    event: "uaEvent",
                    eventCategory: this.pageGroup,
                    eventAction: eventAction,
                    eventLabel: eventLabel,
                    nonInteraction: false,
                })
        },
        subStr(str) {
            return str.replace("%XXXX%", `<a class="case_btn" href="${this.localePath({ path: `/support_ticket` })}">`).replace("%ZZZZ%", "</a>")
        },
        closeProblemConsultationPop() {
            clearTimeout(this.success_timer)
            this.success_timer = null
            this.$emit("closeProblemConsultationPop")
        },
        clickLink(e) {
            if (!e.target.href) return
            if (e.target.href.includes("privacy_policy")) {
                // this.$emit("gaEvent", "basic_information", `Privacy Policy and Notice at Collection`)
            } else {
                // this.$emit("gaEvent", "basic_information", `Terms of Use`)
            }
        },
        focusInput(attr, label) {
            this.errors[attr + "_error"] = ""
        },
        blurInput(attr) {
            if (attr === "email_address") {
                if (!this.isCn) {
                    if (!this.form.email_address.replace(/^\s+|\s+$/g, "")) {
                        this.errors.email_address_error = this.$c("form.form.errors.email_address_error")
                    } else if (!email_valdate.test(this.form.email_address)) {
                        this.errors.email_address_error = this.$c("form.form.errors.email_address_error01")
                    } else {
                        this.errors.email_address_error = ""
                    }
                } else {
                    if (this.form.email_address.replace(/^\s+|\s+$/g, "")) {
                        if (!email_valdate.test(this.form.email_address)) {
                            this.errors.email_address_error = this.$c("form.form.errors.email_address_error01")
                        } else {
                            if (this.isLogin) {
                                this.errors.email_address_error = ""
                            } else {
                                this.$axios
                                    .post("/api/user/isHasRegister", { customers_name: this.form.email_address })
                                    .then((res) => {
                                        if (res.code != 200) return
                                        const data = res.data
                                        if (data && data.is_has) {
                                            this.errors.email_address_error = "账户已存在，单击此处<a href=XXXX title='登录'>登录</a>。"
                                        } else {
                                            this.errors.email_address_error = ""
                                        }
                                    })
                                    .catch((e) => {
                                        this.errors.email_address_error = ""
                                    })
                            }
                        }
                    }
                }
            } else if (attr === "entry_telephone") {
                // if (!["cn", "cn"].includes(this.website)) {
                //     if (!this.form.entry_telephone.replace(/^\s+|\s+$/g, "")) {
                //         this.errors.entry_telephone_error = this.$c("form.form.errors.entry_telephone_error")
                //     } else if (!email_valdate.test(this.form.entry_telephone)) {
                //         this.errors.entry_telephone_error = this.$c("form.form.errors.entry_telephone_error01")
                //     }
                // } else {

                if (this.form.entry_telephone.replace(/^\s+|\s+$/g, "")) {
                    if (!cn_all_phone.test(this.form.entry_telephone)) {
                        this.errors.entry_telephone_error = this.$c("form.form.errors.entry_telephone_error01")
                    } else {
                        if (this.isLogin) {
                            this.errors.entry_telephone = ""
                        } else {
                            this.$axios
                                .post("/api/user/isHasRegister", { customers_name: this.form.entry_telephone })
                                .then((res) => {
                                    if (res.code != 200) return
                                    const data = res.data
                                    if (data && data.is_has) {
                                        this.errors.entry_telephone_error = "账户已存在，单击此处<a href=XXXX title='登录'>登录</a>。"
                                    } else {
                                        this.errors.entry_telephone_error = ""
                                    }
                                })
                                .catch((e) => {
                                    this.errors.entry_telephone_error = ""
                                })
                        }
                    }
                } else {
                    this.errors.entry_telephone_error = this.$c("form.form.errors.entry_telephone_error")
                }
                // }
            } else if (attr === "requirements") {
                if (!this.product_info && !this.form[attr].replace(/^\s+|\s+$/g, "")) {
                    this.errors[attr + "_error"] = this.isCn ? "请填写您的需求。" : this.$c("single.ContactSales.fieldRequired")
                    return
                }
                this.errors[attr + "_error"] = ""
            } else {
                if (!this.form[attr].replace(/^\s+|\s+$/g, "")) {
                    this.errors[attr + "_error"] = this.$c("single.ContactSales.fieldRequired")
                    return
                }
                this.errors[attr + "_error"] = ""
            }
        },
        helpCheckChange(e, val) {
            let buttonName = ["Product Recommendation", "Delivery Time", "Inventory", "Product price", "Other"]
            if (e.target.checked) {
                this.errors.help_error = ""
                this.form.help.push(val)
            } else {
                let index = this.form.help.indexOf(val)
                index !== -1 && this.form.help.splice(index, 1)
            }
            this.gaEvent("contact_sales", `Question Type_${e.target.checked}_${val === "other" ? buttonName[4] : buttonName[val - 1]}`)
        },
        async submit() {
            if (this.btnLoading) return
            if (this.validateFn()) return
            if (this.inputCheck()) return
            let data = {}
            data.entry_firstname = this.form.entry_firstname
            data.entry_lastname = this.form.entry_lastname
            data.company_name = this.form.company_name
            if (this.form.email_address) {
                data.email_address = this.form.email_address
            }
            data.entry_telephone = this.form.entry_telephone
            data.comments = this.form.requirements
            if (this.product_info) {
                data.products_id = this.product_info.products_desc.products_id
                let help_option = this.form.help.reduce((total, item) => {
                    if (item === "other") {
                        total = total.concat("999")
                    } else {
                        total = total.concat(item)
                    }
                    return total
                }, [])
                data.help_option = help_option.join(",")
                if (help_option.includes("999")) {
                    data.help_info = this.form.help_other
                }
            }
            if (/^products/g.test(this.$route.name)) {
                data.resource_page = "7"
            } else if (/^ContactSalesMail/g.test(this.$route.name)) {
                data.resource_page = "3"
            } else {
                data.resource_page = "0"
            }

            this.btnLoading = true
            const { recaptchaTp, headers = {}, errorMsg = "grecaptcha_error" } = await getRecaptchaToken()
            if (!recaptchaTp) {
                this.loading = false
                this.$message.error(this.$c(`pages.Login.regist.${errorMsg}`))
                return
            }
            this.$axios
                .post("/api/contact_sales", data, { headers })
                .then((res) => {
                    this.btnLoading = false
                    if (res.code === 200) {
                        if (res.status === "sensiWords" && this.website === "cn") {
                            for (let key in res.errors) {
                                if (key === "comments") {
                                    this.errors.requirements_error = this.$c("form.form.errors.sensiWords")
                                } else {
                                    this.errors[key + "_error"] = this.$c("form.form.errors.sensiWords")
                                }
                            }
                            return
                        }
                        this.success = true
                        this.success_timer = setTimeout(() => {
                            this.closeProblemConsultationPop()
                        }, 5000)
                        this.gaEvent("contact_sales", `Submit Success_${res.data.data.caseNumber}`)

                        this.$bdRequest({
                            conversionTypes: [
                                {
                                    logidUrl: location.href,
                                    newType: 3,
                                },
                            ],
                        })
                    } else {
                        this.$message.error(res.message)
                    }
                })
                .catch((err) => {
                    this.btnLoading = false
                    this.$message.error(err.message)
                    this.gaEvent("contact_sales", `Submit Fail`)
                })
        },
        validateFn() {
            let flag = false
            if (this.product_info) {
                if (this.form.help.length === 0) {
                    this.errors.help_error = this.$c("form.form.errors.select_error")
                    flag = true
                }
                if (!this.errors.help_error.replace(/^\s+|\s+$/g, "") && this.form.help.includes("other") && !this.form.help_other.replace(/^\s+|\s+$/g, "")) {
                    this.errors.help_other_error = this.$c("form.form.errors.interest_type_error")
                    flag = true
                }
            }
            if (!this.form.entry_firstname.replace(/^\s+|\s+$/g, "")) {
                this.errors.entry_firstname_error = this.$c("form.form.errors.interest_type_error")
                flag = true
            }
            if (!this.form.entry_lastname.replace(/^\s+|\s+$/g, "")) {
                this.errors.entry_lastname_error = this.$c("form.form.errors.interest_type_error")
                flag = true
            }
            if (!this.form.company_name.replace(/^\s+|\s+$/g, "")) {
                this.errors.company_name_error = this.$c("form.form.errors.interest_type_error")
                flag = true
            }

            if (this.isCn) {
                //中文站电话必填
                if (!this.form.entry_telephone.replace(/^\s+|\s+$/g, "")) {
                    this.errors.entry_telephone_error = this.$c("form.form.errors.entry_telephone_error")
                    flag = true
                } else if (!cn_all_phone.test(this.form.entry_telephone)) {
                    this.errors.entry_telephone_error = this.$c("form.form.errors.entry_telephone_error01")
                    flag = true
                }
            }
            if (!this.isCn) {
                //中文站邮箱选填
                if (!this.form.email_address.replace(/^\s+|\s+$/g, "")) {
                    this.errors.email_address_error = this.$c("form.form.errors.email_address_error")
                    flag = true
                } else if (!email_valdate.test(this.form.email_address)) {
                    this.errors.email_address_error = this.$c("form.form.errors.email_address_error01")
                    flag = true
                }
            }
            // else {
            //     if (!this.form.email_address.replace(/^\s+|\s+$/g, "")) {
            //         this.errors.email_address_error = this.$c("form.form.errors.email_address_error")
            //         flag = true
            //     } else if (!email_valdate.test(this.form.email_address)) {
            //         this.errors.email_address_error = this.$c("form.form.errors.email_address_error01")
            //         flag = true
            //     } else if (this.errors.email_address_error) {
            //         flag = true
            //     }
            // }

            if (!this.product_info && !this.form.requirements.replace(/^\s+|\s+$/g, "")) {
                this.errors.requirements_error = this.$c("form.form.errors.interest_type_error")
                flag = true
            }
            if (!this.agree_policy) {
                this.agree_policy_error = this.$c("form.validate.agreement.agree")
                flag = true
            }
            return flag
        },
        clear() {
            this.success = false
            this.form.help = []
            this.form.help_other = ""
            this.form.requirements = ""
            if (!this.isLogin) {
                this.form.entry_firstname = ""
                this.form.entry_lastname = ""
                this.form.email_address = ""
                this.form.entry_telephone = ""
            }
            this.errors = {
                help_error: "",
                help_other_error: "",
                entry_firstname_error: "",
                entry_lastname_error: "",
                email_address_error: "",
                requirements_error: "",
                entry_telephone_error: "",
            }
        },
        inputCheck() {
            this.agree_policy_error = this.agree_policy ? "" : this.$c("form.validate.agreement.agree")
            return this.agree_policy_error.length
        },
    },
    watch: {
        show(newVal, oldVal) {
            if (newVal) {
                this.clear()
                if (this.isLogin) {
                    this.form.entry_firstname = this.userInfo.customers_firstname
                    this.form.entry_lastname = this.userInfo.customers_lastname
                    this.form.email_address = this.userInfo.customers_email_address
                    this.form.entry_telephone = this.userInfo.customers_telephone
                }
                this.handleWindowResize()
            }
            // 详情页获取产品数据
            if (newVal && /^products/g.test(this.$route.name)) {
                this.loading = true
                this.$axios.get(`/api/qa_product_detail`, { params: { products_id: this.$route.params.id } }).then((res) => {
                    this.loading = false
                    this.product_info = res.data
                    if (this.product_info.tax_price.price_str) {
                        this.price_str = this.product_info.tax_price.price_str
                    } else {
                        this.price_str = this.product_info.products_price_str
                    }
                })
            } else {
                this.product_info = ""
            }
            if (newVal) {
                this.$refs.popup.fixScreen()
            } else {
                this.$refs.popup.noFixScreen()
            }
        },
    },
}
</script>

<style lang="scss" scoped>
.problem-consultation-pop {
    color: $textColor1;

    &.success {
        ::v-deep {
            .fs-popup-ctn {
                height: auto;
            }
        }
    }
    &.problem-consultation-pop-cn {
        ::v-deep {
            .fs-popup {
                // width: 712px;
                .fs-popup-ctn {
                    // width: 100%;
                }
            }
            .fs-popup-body .contact-sales-box .form-box .input-block .input-item {
                width: calc(50% - 4px);
            }
        }
    }
    ::v-deep {
        .fs-popup {
            width: 380px;
            max-height: 635px;
            height: auto;
            left: auto;
            top: auto;
            right: 84px;
            bottom: 48px;
        }
        .auto_height {
        }
        .mask {
            display: none;
        }
        .fs-popup-ctn {
            width: 360px;
            transform: translate3d(0, 0, 0);
            box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.1);
        }
        .fs-popup-header {
            position: relative; /* 必须有！用于定位伪元素 */
            padding-left: 16px;
            padding-right: 16px;
            padding-top: 24px;
            padding-bottom: 16px;
            @include font18;
            height: 60px;
            @media (max-width: 768px) {
                padding: 16px;
            }
            .iconfont_close_box {
                position: absolute;
                top: 16px;
                right: 16px;
                &:hover {
                    color: #19191a;
                    .iconfont_close {
                        color: #19191a;
                    }
                }
            }
            .iconfont_close {
                /* right: 16px; */
                color: #707070;
                font-size: 20px;
                line-height: 1;
                width: 20px;
                height: 20px;
            }
            .title_box {
                flex: 1;
                padding-bottom: 0;
                padding-right: 0px;
                border-bottom: 0;
                .title {
                    @include font18;
                    // line-height: 24px;
                    font-weight: 600;
                    user-select: none;
                }
            }
        }
        .fs-popup-header::after {
            content: "";
            position: absolute;
            bottom: 0;
            left: 16px;
            right: 16px;
            height: 1px;
            background-color: #e5e5e5;
            @media (max-width: 768px) {
                left: 16px;
                right: 16px;
            }
        }
        .fs-popup-body {
            .contact-sales-box {
                // height: 100%;
                .cont-box {
                    display: flex;
                    flex-direction: column;
                    // height: 100%;
                }
                .form-box {
                    padding: 16px;
                    padding-bottom: 24px;
                    // max-height: 482px;
                    overflow-y: auto;
                    @media (max-width: 768px) {
                        padding-bottom: 0;
                    }
                    .whatsapp {
                        margin-bottom: 16px;
                        > p {
                            @include font12;
                            font-weight: 400;
                            color: $textColor1;
                            text-align: center;
                            margin-bottom: 8px;
                        }
                        > div {
                            display: flex;
                            align-items: center;
                            justify-content: space-between;
                            div {
                                flex: 1;
                                height: 1px;
                                background-color: #e4e7ed;
                            }
                            p {
                                @include font12;
                                font-weight: 400;
                                color: $borderColor10;
                                padding: 0 12px;
                            }
                        }
                    }
                    .input-block {
                        margin-bottom: 12px;
                        display: flex;
                        justify-content: space-between;
                        &.product {
                            flex-direction: column;
                            padding: 20px;
                            margin-bottom: 16px;
                            background-color: #fafbfb;
                        }
                        .product-item {
                            display: flex;
                            padding-bottom: 16px;
                            border-bottom: 1px solid #eee;
                            img {
                                display: block;
                                width: 50px;
                                height: 50px;
                                margin-right: 12px;
                                mix-blend-mode: multiply;
                            }
                            .pro-tit {
                                @include font14;
                                @include txt-more-hid(2);
                                margin-bottom: 8px;
                            }
                            .pro-info-row {
                                display: flex;
                                align-items: center;
                                gap: 12px; // pro-price和pro-id间距，可根据需要调整
                                .pro-price {
                                    @include font12;
                                    color: #19191a;
                                    font-weight: 600;
                                }
                                .pro-id {
                                    @include font12;
                                    color: #707070;
                                }
                            }
                        }
                        .check-item {
                            padding-top: 16px;
                            .tit {
                                @include font14;
                                font-weight: 600;
                                margin-bottom: 12px;
                            }
                            input[type="checkbox"] {
                                margin-right: 8px;
                                width: 14px;
                                height: 14px;
                                font-size: 14px;
                            }
                            .input-txt {
                                @include font13;
                            }
                            .select-list {
                                display: flex;
                                flex-direction: column;
                                .select-item {
                                    flex-shrink: 0;
                                    display: flex;
                                    align-items: center;
                                    margin-right: 20px;
                                    margin-bottom: 8px;
                                    &:last-child {
                                        margin-right: 0;
                                    }
                                }
                            }
                            .select-other {
                                display: flex;
                                flex-direction: column;
                                .other-check-box {
                                    display: flex;
                                    align-items: center;
                                    margin-bottom: 4px;
                                    label {
                                        display: flex;
                                        align-items: center;
                                    }
                                }
                                input {
                                    background: #fff;
                                }
                            }
                        }
                        .input-item {
                            width: calc((100% - 32px) / 2);
                            position: relative;
                            &.width100 {
                                width: 100%;
                            }
                            .tit {
                                @include font12;
                                margin-bottom: 4px;
                                color: #707070;
                            }
                            .input-item-flex {
                                display: flex;
                                justify-content: space-between;
                                align-items: center;
                            }
                            .input-item-number {
                                display: flex;
                                justify-content: flex-end;
                                width: 100%;
                            }
                            textarea {
                                height: 110px;
                                padding: 10px 12px;
                            }
                            .textarea-num {
                                @include font12;
                                color: $textColor3;
                                // margin-top: 4px;
                                position: absolute;
                                right: 0;
                                top: 0;
                                em {
                                    font-style: normal;
                                    &.active {
                                        color: $textColor4;
                                    }
                                }
                            }
                            &.error {
                                .textarea-num {
                                    bottom: 2px;
                                }
                            }
                        }
                    }
                    .agreement_wrap {
                        @include font12;
                        color: $textColor3;
                        display: flex;
                        align-items: center;
                        column-gap: 6px;
                        .checkbox {
                            width: 14px;
                            height: 14px;
                            font-size: 14px;
                        }
                        a {
                            color: #0060bf !important;
                        }
                    }
                }
            }
        }
        .mask {
            background-color: transparent;
        }
        .slide-right-leave-active {
            opacity: 0.1;
        }
    }
    .footer-box {
        flex-shrink: 0;
        width: 100%;
        display: flex;
        justify-content: flex-end;
        align-items: center;
        // padding: 20px 20px;
        margin-top: 16px;
        // position: fixed;
        // bottom: 0;
        background-color: #ffffff;
        // border-top: 1px solid #e5e5e
        .cancel-btn {
            margin-right: 12px;
        }
        &::v-deep {
            .fs-button {
                height: 36px;
            }
        }
    }
    .success-box {
        padding: 36px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        text-align: center;
        .success-logo {
            font-size: 50px;
            color: #329a34;
        }
        .success-tit {
            @include font16;
            font-weight: 600;
            margin: 16px 0 8px 0;
        }
        .success-des {
            @include font14;
            color: $textColor3;
        }
    }
}
@media (max-width: 768px) {
    .problem-consultation-pop {
        &.problem-consultation-pop-cn {
            ::v-deep {
                .fs-popup {
                    width: 100%;
                    .fs-popup-ctn {
                        width: 100%;
                    }
                }
                .fs-popup-body .contact-sales-box .form-box .input-block {
                    &.input-block-cn {
                        flex-direction: column;
                        .input-item {
                            &:first-of-type {
                                margin-bottom: 16px;
                            }
                            width: 100%;
                        }
                    }
                    .input-item {
                        width: 100%;
                    }
                }
            }
        }
        ::v-deep {
            .fs-popup {
                z-index: 160;
                max-width: 100%;
                max-height: 100%;
                width: 100%;
                height: 100%;
                left: 0;
                right: 0;
                top: 0;
                bottom: 0;
                .iconfont_close_box {
                    padding: 6px;
                    // width: auto;
                    min-height: auto;
                    width: 24px;
                    height: 24px;
                }
            }
            .fs-popup-ctn {
                width: 100%;
                height: 100%;
                max-height: none;
                left: 0;
                right: 0;
                top: 0;
                bottom: 0;
                .fs-popup-body {
                    .contact-sales-box {
                        height: 100%;
                        .cont-box {
                            height: 100%;
                        }
                        .form-box {
                            // max-height: calc(100% - 201px);
                            max-height: 100%;
                            height: 100%;
                            padding: 16px;
                            display: flex;
                            flex-direction: column;
                            .input-block {
                                .check-item {
                                    .select-list {
                                        flex-direction: column;
                                        .select-item {
                                            margin-right: 0;
                                        }
                                    }
                                }
                                .input-item {
                                    width: calc((100% - 20px) / 2);
                                    position: relative;
                                    &.width100 {
                                        width: 100%;
                                    }
                                    .tit {
                                        @include font12;
                                        margin-bottom: 4px;
                                    }
                                    .input-item-flex {
                                        display: flex;
                                        justify-content: space-between;
                                        align-items: center;
                                    }
                                    .input-item-number {
                                        display: flex;
                                        justify-content: flex-end;
                                        width: 100%;
                                    }
                                    .textarea-num {
                                        @include font12;
                                        color: $textColor3;
                                        margin-top: 0;
                                        em {
                                            font-style: normal;
                                            &.active {
                                                color: $textColor4;
                                            }
                                        }
                                    }
                                    &.error {
                                        .textarea-num {
                                            bottom: 2px;
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
            .footer-box {
                flex-direction: column-reverse;
                // height: auto;
                // padding: 20px 16px;
                // position: fixed;
                // bottom: 0;
                margin-top: 16px;
                border-top: none;
                .cancel-btn,
                .submit-btn {
                    width: 100%;
                    margin: 0;
                }
                .cancel-btn {
                    margin-top: 20px;
                }
            }
        }
    }
}
</style>
