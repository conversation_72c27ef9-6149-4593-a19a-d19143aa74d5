<template>
    <fs-popup-new v-bind="$attrs" v-on="$listeners" :title="title" transition="slide-up" width="750" type="all" isNew :isMDrawer="true">
        <div class="create_pop" ref="create_pop">
            <div class="alert_popup_content">
                <p class="poOrder-alone-txt">
                    {{ $c("pages.TaxApplication.addApplication.tip") }}
                </p>
                <div class="tax_extemption_application_ul_ovrflow">
                    <ul class="tax_extemption_application_ul after">
                        <li class="tax_li" :class="disableStatus.includes(item) ? 'disable' : ''" v-for="(item, index) in state_info" :key="index">
                            <div class="radio-box" @click="chooseState(item, index, true)">
                                <input :ref="`radio-state-${index}`" type="radio" name="state" :disabled="disableStatus.includes(item) ? true : false" :value="state_check" :class="{ active: chooseIndex === index }" />
                                <span>{{ item }}</span>
                            </div>
                        </li>
                    </ul>
                </div>
                <validate-error :error="errors.state_check"></validate-error>
            </div>
        </div>
        <template slot="footer">
            <div class="alone_text_align_right">
                <fs-button :text="$c('pages.TaxApplication.addApplication.btn')" :loading="loading" @click="save(0, state_check, tax_token)"></fs-button>
            </div>
        </template>
    </fs-popup-new>
</template>

<script>
import FsPopupNew from "@/components/FsPopupNew/FsPopupNew.vue"
import FsButton from "@/components/FsButton/FsButton.vue"
import ValidateError from "@/components/ValidateError/ValidateError"
import { mapState } from "vuex"
export default {
    components: {
        FsPopupNew,
        FsButton,
        ValidateError,
    },
    props: {
        // state_info: {
        //     type: Array,
        //     default: () => [],
        // },
        tax_token: "",
        default_state_info: [],
    },
    data() {
        return {
            title: this.$c("pages.TaxApplication.addApplication.tit"),
            loading: false,
            validate: false,
            state_check: "",
            chooseIndex: 0,
            //不可选的州
            disableStatus: [],
            errors: {
                state_check: "",
            },
            state_info: [
                "Alabama",
                "Idaho",
                "North Carolina",
                "South Dakota",
                "Alaska",
                "Indiana",
                "North Dakota",
                "Tennessee",
                "Arizona",
                "Kansas",
                "Nebraska",
                "Texas",
                "Arkansas",
                "Kentucky",
                "New Jersey",
                "Utah",
                "California",
                "Louisiana",
                "New Mexico",
                "Virginia",
                "Colorado",
                "Massachusetts",
                "Nevada",
                "Vermont",
                "Connecticut",
                "Maryland",
                "New York",
                "Washington",
                "Florida",
                "Maine",
                "Ohio",
                "Washington D. C",
                "Georgia",
                "Michigan",
                "Oklahoma",
                "Wisconsin",
                "Hawaii",
                "Minnesota",
                "Pennsylvania",
                "West Virginia",
                "Iowa",
                "Missouri",
                "Rhode Island",
                "Wyoming",
                "Illinois",
                "Mississippi",
                "South Carolina",
                "Puerto Rico",
            ],
        }
    },
    created() {},
    watch: {
        default_state_info: function (newVal, oldVal) {
            this.disableStatus = newVal //newVal即是default_state_info
            this.getInit()
        },
    },
    computed: {
        ...mapState({
            customers_number: (state) => state.userInfo.userInfo.customers_number_new,
        }),
    },
    mounted() {
        this.getInit()
        window.axios = this.$axios
        // console.log(1111, this.customers_number);
    },
    methods: {
        getInit(flag = false) {
            let localState = JSON.parse(localStorage.getItem("tax_exemption_application_state_choose"))
            console.log(this.disableStatus)
            if ((localState?.value || localState?.value === 0) && !this.disableStatus.includes(localState.label) && flag) {
                this.$refs[`radio-state-${localState.value}`][0].checked = true
                this.state_check = localState.label
                this.validate = true
            }
            this.errors.state_check = ""
        },
        save(id, state_check, tax_token) {
            // console.log('33', id, state_check, tax_token, this.validate);
            // this.chooseState(this.state_check)
            this.loading = true
            if (this.validate) {
                this.$emit("getTaxExemption", [id, state_check, tax_token, this.customers_number])
                this.loading = false
            } else {
                this.loading = false
            }
        },
        chooseState(item, index, flag = false) {
            if (this.disableStatus.includes(item)) return
            localStorage.setItem("tax_exemption_application_state_choose", JSON.stringify({ label: item, value: index }))
            flag && (this.$refs[`radio-state-${index}`][0].checked = true)
            this.state_check = item
            index && (this.chooseIndex = index)
            if (this.state_check.length !== 0) {
                this.errors.state_check = ""
                this.validate = true
            } else {
                this.validate = false
                this.errors.state_check = this.$c("pages.TaxApplication.addApplication.state_check")
            }
        },
    },
}
</script>

<style lang="scss" scoped>
::v-deep .fs-popup-ctn {
    width: 680px;
    .fs-popup-body {
        max-height: 70vh;
        overflow-y: auto;
    }
}
::v-deep .validate_error {
    padding-bottom: 10px;
}
::v-deep .fs-popup-header {
    align-items: flex-start;
}
::v-deep .title_box .title {
    overflow: visible;
    white-space: normal;
}
.create_pop {
    // padding: 20px 32px;
    color: $textColor1;
    font-weight: 400;
    @media (max-width: 1024px) {
        // padding: 0 20px;
    }
    @media (max-width: 768px) {
        // padding: 16px 20px;
    }
    .create_pop_tip {
        padding-top: 14px;
        font-size: 13px;
        color: $textColor3;
        line-height: 22px;
    }

    ul {
        display: flex;
        justify-content: flex-start;
        flex-wrap: wrap;
        margin-top: 20px;
    }
    li {
        font-size: 14px;
        color: $textColor1;
        line-height: 22px;
        width: 25%;
        margin-bottom: 8px;
        cursor: pointer;
        display: flex;
        align-items: flex-start;
        input:disabled,
        select:disabled,
        textarea:disabled {
            background-color: #fff;
        }
        span {
            // margin-left: 3px;
        }
        .radio-box {
            display: flex;
            align-items: center;
            input[type="radio"] {
                font-size: 14px;
                width: 14px;
                height: 14px;
            }
            &:hover {
                input[type="radio"]:before {
                    color: #707070;
                }
            }
        }
    }
    .active {
        input[type="radio"]:before {
            color: #707070;
        }
    }
    .disable {
        color: $textColor3;
        input[type="radio"]:disabled:before {
            color: #ccc;
            opacity: 0.8;
            cursor: not-allowed;
        }
    }
    .disabled {
        color: $textColor3;
        ::v-deep input[type="radio"]:before {
            color: $textColor3;
        }
    }
}

.alert_popup_content {
    .poOrder-alone-txt {
        color: $textColor1;
        font-size: 13px;
        // padding-top: 20px;
        // margin-bottom: 12px;
    }
    .tax_extemption_application_ul_ovrflow {
        // margin-bottom: 22px;
        .tax_extemption_application_ul {
            .tax_li.disable {
                .radio-box {
                    cursor: not-allowed;
                    &:hover {
                        input[type="radio"]:before {
                            color: #ccc;
                            opacity: 0.8;
                        }
                    }
                }
            }
            span {
                word-break: break-word;
                @include font13();
            }
        }
    }
}
.alone_text_align_right {
    display: flex;
    justify-content: flex-end;
    // padding: 20px 32px;
    // border-top: 1px solid #e5e5e5;
    @media (max-width: 960px) {
        // padding: 20px 16px;
    }
    .fs-button {
        @media (max-width: 960px) {
            width: 100%;
        }
    }
}

::v-deep .fs-popup-body .slot-wraper {
    position: relative;
}
::v-deep .fs-popup.is_new {
    .fs-popup-body {
        padding-bottom: 24px;
    }
    .fs-popup-footer {
        z-index: 11;
        background-color: #fff;
        padding: 0 24px 24px 24px;
    }
}
@media (max-width: 960px) {
    ::v-deep .fs-popup-ctn {
        width: 100%;
    }
    .create_pop li {
        width: 33.33%;
    }
}
@media (max-width: 468px) {
    ::v-deep .fs-popup-ctn .fs-popup-body {
        max-height: 100vh;
        overflow-y: auto;
    }
    .create_pop li {
        width: 50%;
    }
}
</style>
