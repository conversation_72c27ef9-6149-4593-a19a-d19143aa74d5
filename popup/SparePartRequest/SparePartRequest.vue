<template>
    <div>
        <fs-popup :show="showPopu >= 0 && showPopu != 4" width="750" type="all" isNew :title="$c('pages.spareOrder.sparePartRequest')" transition="slide-up" @close="close" :loading="showloding" :isMDrawer="true">
            <div class="part-request">
                <!-- 内容部分 -->
                <div class="container">
                    <div class="main-content">
                        <!-- 1.0提示文字 -->
                        <div class="tips">
                            <!-- 图标 -->
                            <span class="iconfont">&#xe66a;</span>
                            <p>{{ $c("pages.spareOrder.totaltips") }}</p>
                        </div>
                        <!--2.0表单部分-->
                        <form>
                            <!-- 订单列表 -->
                            <div>
                                <muens-list v-if="orderInfo" :productInfo="canApplySpare" @change="productsChane" :error="errorList.products"></muens-list>
                            </div>
                            <!-- content-->
                            <div class="leavefb_details">
                                <p>
                                    <span>{{ $c("pages.spareOrder.content") }}*</span>
                                    <span
                                        class="text-number"
                                        :class="{
                                            active: formData.content.length === 5000,
                                        }"
                                        >{{ formData.content.length }}/5000</span
                                    >
                                </p>
                                <textarea placeholder="Your comments will help FS respond more quickly." :maxlength="5000" v-model="formData.content" @blur="inputCheck('content')" ref="content-textareas"></textarea>
                                <validate-error :error="errorList.content"></validate-error>
                            </div>
                            <!-- 文件上传 -->
                            <div class="upload">
                                <upload-file
                                    class="upload-file"
                                    @change="filesChange"
                                    ref="uploadFile"
                                    accept=".pdf,.doc,.docx,.xls,.xlsx,.txt,image/jpeg,image/jpg,image/png"
                                    :autoUpload="true"
                                    :text="$c('pages.Products.Upload_File')"
                                    :multiple="true"
                                    type="file"
                                    :limit="5"
                                    :maxSize="5 * 1024 * 1024">
                                    <fs-popover position="right" slot="tip">
                                        <div class="tip-font">
                                            <!-- <span class="iconfont">&#xe718;</span> -->
                                            <p v-html="$c('pages.spareOrder.allow')"></p>
                                        </div>
                                    </fs-popover>
                                </upload-file>
                            </div>
                            <!-- 购物地址 -->
                            <div class="shipping-adress-title">
                                <p class="contain">{{ $c("pages.OrderDetail.bill.deliveryAddress") }}</p>
                            </div>
                            <template v-if="showPopu === 1">
                                <div class="shipping-adress">
                                    <div class="address" v-if="!orderInfo.isCnOrder">
                                        <P>
                                            <span>{{ address.entry_firstname }} {{ address.entry_lastname }}</span>
                                            |
                                            <span>{{ address.entry_company }}</span>
                                        </P>
                                        <P> {{ address.entry_address1 }},{{ address.entry_city }},{{ address.entry_postcode }},{{ address.entry_country_name }},{{ address.entry_telephone }} </P>
                                    </div>
                                    <div class="address" v-else>
                                        <p>{{ address.entry_firstname }}</p>
                                        <p>
                                            {{ address.entry_state }}{{ address.entry_city }}{{ address.entry_suburb }}{{ address.entry_street_address || address.entry_address1 }}
                                            {{ address.entry_telephone }}
                                            {{ address.entry_postcode }}
                                        </p>
                                    </div>
                                    <span class="edit-adress" @click="selectAddress" v-if="!(orderInfo.isCnOrder && !['cn', 'hk', 'tw', 'mo'].includes(website))">
                                        <span class="iconfont">&#xe745;</span>
                                    </span>
                                </div>
                            </template>
                            <template v-else-if="showPopu === 2">
                                <SelectListAddress
                                    :show="showPopu === 2"
                                    @close="addressSelected"
                                    @cansel="showPopu = 1"
                                    @edit="editAddress"
                                    @loading="editLoading"
                                    :cnorder="orderInfo.isCnOrder"
                                    :selectId="selectAddressId"
                                    ref="selectAddress" />
                            </template>
                            <template v-else-if="showPopu === 3">
                                <AddNewAddress @close="showPopu = 2" @editSuccess="editSuccess" @addSuccess="addSuccess" ref="editAddress" @showError="showError" />
                            </template>
                            <!-- 添加地址 -->
                            <div v-if="showPopu === 2" class="add-new-address">
                                <span @click="addNewAddress">{{ $c("pages.confirmOrder.address.addNewAddress") }}</span>
                            </div>
                            <!-- 协议 -->
                            <Policy ref="policy"></Policy>
                        </form>
                    </div>
                </div>
            </div>
            <template slot="footer">
                <div class="option">
                    <fs-button class="btn" bold type="white" @click="close(), GaEven()">{{ $c("pages.spareOrder.cancel") }}</fs-button>
                    <fs-button class="btn" type="red" @click="sumbit" :loading="showloding">{{ $c("pages.spareOrder.submit") }}</fs-button>
                </div>
            </template>
        </fs-popup>

        <!-- <selelct-address :show="showPopu === 2" @close="addressSelected" @cansel="showPopu = 1" @edit="editAddress" :cnorder="orderInfo.isCnOrder" ref="selectAddress"></selelct-address> -->
        <!-- <edit-address :show="showPopu === 3" @close="showPopu = 2" @editSuccess="editSuccess" ref="editAddress" @showError="showError" /> -->
        <add-success :show="showPopu === 4" @close="closePopu" :text="successInfo" />
    </div>
</template>

<script>
// 引入组件
import MuensList from "./components/MuensList.vue"
import UploadFile from "@/components/UploadFile/UploadFile.vue"
import FsButton from "@/components/FsButton/FsButton.vue"
import ValidateError from "@/components/ValidateError/ValidateError.vue"
import FsPopover from "@/components/FsPopover"
import FsPopup from "@/components/FsPopupNew/FsPopupNew.vue"
import Policy from "./components/policy.vue"
import SelelctAddress from "./components/SelelctAddress.vue"
import EditAddress from "./components/EditAddress.vue"
import AddSuccess from "./components/AddSuccess.vue"
import SelectListAddress from "./components/SelectListAddress.vue"
import AddNewAddress from "./components/AddNewAddress.vue"
import { mapState, mapActions } from "vuex"

export default {
    components: {
        MuensList,
        UploadFile,
        FsButton,
        ValidateError,
        FsPopup,
        Policy,
        SelelctAddress,
        EditAddress,
        AddSuccess,
        FsPopover,
        SelectListAddress,
        AddNewAddress,
    },
    props: {
        show: {
            type: Boolean,
            default: false,
        },
        orderInfo: {
            type: Object,
            default: () => {},
        },
    },
    data() {
        return {
            showPopu: -1,
            errorList: {
                products: "",
                content: "",
            },
            loading: false,
            selectAddressId: 0,
            address: {
                company_type: "",
                entry_country_id: "", //'国家id',
                entry_country_name: "",
                entry_firstname: "", //'名字',
                entry_lastname: "", //'姓',
                entry_city: "", //'城市',
                entry_company: "", //'公司名',
                entry_address1: "", // '地址1',
                entry_address2: "", //'地址2',
                entry_postcode: "", // '城市编码',
                entry_state_name: "", // '州',
                entry_telephone: "", // '电话号码',
            },
            showloding: false,
            formData: {
                products: [],
                content: "",
                files: [],
            },
            agree_txt01: this.$c("form.form.agree_txt01"),
            terms_txt: this.$c("form.form.terms_of_use"),
            editAddressVisiable: false,
            successInfo: "",
        }
    },
    methods: {
        ...mapActions({
            updateSiteInfo: "selectCountry/updateSiteInfo",
            getCountry: "selectCountry/getCountry",
            getCountryState: "listCountryState/getCountryState",
        }),
        // 初始化数据
        init() {
            this.formData = {
                products: [],
                content: "",
                files: [],
            }
            // this.address = {
            //   company_type: "",
            //   entry_country_id: "", //'国家id',
            //   entry_country_name: "",
            //   entry_firstname: "", //'名字',
            //   entry_lastname: "", //'姓',
            //   entry_city: "", //'城市',
            //   entry_company: "", //'公司名',
            //   entry_address1: "", // '地址1',
            //   entry_address2: "", //'地址2',
            //   entry_postcode: "", // '城市编码',
            //   entry_state_name: "", // '州',
            //   entry_telephone: "" // '电话号码',
            // };
            this.errorList = {
                products: "",
                content: "",
            }
            this.$refs.policy?.init()
        },
        // 校验上传文件处理函数
        filesChange(files) {
            this.formData.files = files.files
            this.inputCheck("content")
        },
        close() {
            this.$emit("close")
            this.init()
        },
        // 产品列表change事件
        productsChane(products) {
            this.formData.products = products.filter((item) => item.check)
            this.inputCheck("products")
        },
        editLoading(val) {
            this.showloding = val
        },
        sumbit(val) {
            // 校验商品
            if (this.formData.products.length < 1) {
                this.inputCheck("products")
                return
            }
            // this.inputCheck("products");

            //  校验content
            // this.inputCheck("content");

            if (this.formData.content.length < 1) {
                this.inputCheck("content")
                return
            }
            // 校验政策
            const validataPolicy = this.$refs.policy.validataAll()
            if (validataPolicy.length) return
            const data = new FormData()
            if (this.formData.files.length > 0) {
                this.formData.files.forEach((i) => {
                    data.append("reviews_img[]", i)
                })
            }

            let customers_service_id = localStorage.getItem("spare_service_id")
            let service_number = localStorage.getItem("spare_service_number")

            const params = {
                order_number: this.orderInfo.orders_number,
                products_info: this.formData.products.map((item) => {
                    return {
                        product_id: item.products_id,
                        product_num: item.product_count,
                    }
                }),
                content: this.formData.content,
                address: this.address,
                customers_service_id: customers_service_id,
                service_number: service_number,
            }
            const arr = {}
            Object.keys(params).forEach((k) => {
                if (k === "order_number" || k === "content" || k === "customers_service_id" || k === "service_number") {
                    data.append(k, params[k])
                } else {
                    data.append(k, JSON.stringify(params[k]))
                }
            })
            this.showloding = true
            this.$axios
                .post("/api/spare_part", data)
                .then((res) => {
                    const { success, info } = res.data
                    if (success) {
                        this.showPopu = 4
                        this.successInfo = info
                        // 初始化数据
                        this.init()
                        this.showloding = false
                        // 触发埋点事件
                        this.GaEven("Success")

                        localStorage.removeItem("spare_service_id")
                        localStorage.removeItem("spare_service_number")
                    } else {
                        this.showloding = false
                        // 触发埋点事件
                        this.GaEven("Fail")
                    }
                    this.timer = setInterval(() => {
                        // this.close()
                        clearInterval(this.timer)
                    }, 3000)
                    // this.close()
                })
                .catch((err) => {
                    this.showloding = false
                    // 触发埋点事件
                    this.GaEven("Fail")
                })
        },
        // 校验函数
        inputCheck(attr) {
            let result = true
            const val = this.formData[attr]
            const length = val?.length || 0
            switch (attr) {
                case "content":
                    // this.errorList[attr] = (length||(this.formData.files?.length>0))? "":this.$c('pages.spareOrder.formError.content');
                    this.errorList[attr] = length ? "" : this.$c("pages.spareOrder.formError.content")
                    // this.$refs['content-textareas'].style.borderColor=(length||(this.formData.files?.length>0)) ?'#E5E5E5':'#C00000'
                    // this.$refs["content-textareas"].style.borderColor = length ? "#E5E5E5" : "#19191a"
                    break
                case "products":
                    this.errorList[attr] = length ? "" : this.$c("pages.RMA.form.notSelected")
                    break
                default:
                    break
            }
        },
        addressSelected(val) {
            // this.showPopu = 1
            if (val) {
                this.selectAddressId = val.addressBookId
                const camelToSnakeCase = (obj) => {
                    return Object.keys(obj).reduce((acc, key) => {
                        const snakeKey = key.replace(/[A-Z]/g, (letter) => `_${letter.toLowerCase()}`)
                        acc[snakeKey] = obj[key]
                        return acc
                    }, {})
                }
                const convertData = (data) => {
                    if (Array.isArray(data)) {
                        return data.map(camelToSnakeCase)
                    } else {
                        return camelToSnakeCase(data)
                    }
                }
                // 转换数据
                const convertedData = convertData(val)
                this.address = { ...convertedData, entry_address1: val.entryStreetAddress }
            }
        },
        editSuccess(val) {
            // 关闭修改
            // this.editAddressVisiable=false
            // 重新刷新地址列表
            // this.$refs.selectAddress.getAddressList()
            this.showPopu = 2
        },
        addSuccess(val) {
            this.showPopu = 2
        },
        showError(val) {
            console.log(val)
        },
        editAddress(val) {
            this.showPopu = 3
            this.$nextTick(() => {
                if (this.$refs.editAddress) {
                    this.$refs.editAddress.initForm(val, "Shipping")
                }
            })
        },
        addNewAddress() {
            this.showPopu = 3
            this.$nextTick(() => {
                if (this.$refs.editAddress) {
                    this.$refs.editAddress.initForm({}, "Shipping")
                }
            })
        },
        selectAddress() {
            // this.$refs.selectAddress.getAddressList()
            console.log("selectAddress", this.$refs.selectAddress)
            this.showPopu = 2
        },
        // 埋点函数
        GaEven(status) {
            window.dataLayer &&
                window.dataLayer.push({
                    event: "uaEvent",
                    eventCategory: this.pageGroup,
                    eventAction: "spare_part_request",
                    eventLabel: status ? `Submit ${status}` : "Cancel Submit",
                    nonInteraction: false,
                })
        },
        closePopu() {
            this.close()
            this.$router.go()
        },
    },
    mounted() {
        this.getCountryState && this.getCountryState()
    },
    computed: {
        ...mapState({
            website: (state) => state.webSiteInfo.website,
            pageGroup: (state) => state.ga.pageGroup,
        }),
        // 能够申请备件的产品列表
        canApplySpare() {
            const { products, sparePartTotal } = this.orderInfo
            const result = []
            if (products && products.length > 0 && sparePartTotal && sparePartTotal.data) {
                products.forEach((item) => {
                    const id = item.products_id
                    const target = sparePartTotal.data.filter((item) => item.product_id === id)
                    if (target[0].num > 0) {
                        result.push({ ...item, max_count: target[0].num || target[0].products_num })
                    }
                })
            }

            return result
        },
    },
    watch: {
        orderInfo: {
            handler(val) {
                if (val) {
                    this.init()
                    this.address = {
                        company_type: "",
                        entry_suburb: val.delivery_suburb,
                        entry_country_id: val.delivery_country_id, //'国家id',
                        entry_country_name: val.delivery_country,
                        entry_firstname: val.billing_name || val.delivery_name, //'名字',
                        entry_lastname: val.billing_lastname || val.delivery_lastname, //'姓',
                        entry_city: val.billing_city || val.delivery_city, //'城市',
                        entry_company: val.billing_company || val.delivery_company, //'公司名',
                        entry_address1: val.billing_street_address || val.delivery_street_address, // '地址1',
                        entry_address2: "", //'地址2',
                        entry_postcode: val.billing_postcode || val.delivery_postcode, // '城市编码',
                        entry_state_name: "", // '州',
                        entry_telephone: val.billing_telephone || val.delivery_telephone, // '电话号码',
                    }
                }
            },
            immediate: true,
        },
        show: {
            handler(val) {
                if (val) {
                    this.showPopu = 1
                } else {
                    this.showPopu = -1
                }
            },
            immediate: true,
        },
    },
    created() {
        this.terms_txt = this.terms_txt.slice(20, 31).replace("利用規約", `<a href="${this.localePath({ name: "terms-of-use" })}">利用規約</a>`)
    },
}
</script>

<style lang="scss" scoped>
.part-request {
    // width: 680px;
    // margin: 0 auto;
    // @include sourceSansPro;
    // background-color: #ccc;
}
.titel {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 24px;
    @include font20;
    border-bottom: 1px solid #e5e5e5;
    .iconfont {
        @include font16;
        color: $textColor3;
    }
}
.container {
    // padding: 20px 32px;
    // padding-bottom: 40px;
    .tips {
        @include font13;
        display: flex;
        justify-content: flex-start;
        padding: 10px 16px;
        background-color: rgba(0, 96, 191, 0.05);
        border-radius: 3px;
        color: $textColor3;
        margin-bottom: 8px;
        .iconfont {
            font-size: 16px;
            color: #0060bf;
            margin-right: 8px;
        }
    }
    .leavefb_details {
        margin: 24px 0 0 0;
        @media (max-width: 768px) {
            margin: 0;
        }
        > p {
            display: flex;
            justify-content: space-between;
            @include font14;
            color: $textColor1;
            margin: 0 0 4px;
            .text-number {
                text-align: right;
                @include font13;
                color: $textColor3;
                &.active {
                    color: $textColor4;
                }
            }
        }
        > textarea {
            // border-color: $bgColor2;
            background: #f6f6f8;
            border-radius: 4px;
            border: none;
            height: 110px;
            color: #19191a;
            font-size: 13px;
            &:focus {
                // border-color: #8d8d8f;
                border: 1px solid #707070;
                border-color: #707070;
            }
            &:hover {
                background: linear-gradient(rgba(25, 25, 26, 0.04), rgba(25, 25, 26, 0.04)), #f6f6f8;
            }
        }
    }
    .upload {
        margin: 12px 0px 24px 0px;
        @media (max-width: 768px) {
            margin: 12px 0;
        }
        :deep(.upload-file) {
            .fs-popover {
                margin-left: 8px;
            }
            .file-btn {
                border: none;
                border-radius: 4px;
                background: #f6f6f8;
                height: 42px;
            }
            &:hover {
                .file-btn {
                    background: linear-gradient(rgba(25, 25, 26, 0.04), rgba(25, 25, 26, 0.04)), #f6f6f8;
                }
                .info-txt {
                    text-decoration: none;
                }
            }
        }
        // .iconfont {
        //     // margin-left: 8px;
        //     font-size: 13px;
        // }
        // ::v-deep .upload-file {
        //     .logo-btn {
        //         height: auto;
        //         border: none;
        //         // min-width: 120px;
        //         @include font14;
        //         color: $textColor1;
        //         .info {
        //             padding: 0 8px;
        //             margin-left: -10px;
        //         }
        //     }
        //     .fs-popover {
        //         position: relative;
        //         z-index: 3;
        //     }
        //     .fs-tip {
        //         margin-left: 0;
        //         .tip-trigger {
        //             margin-left: 8px;
        //         }
        //     }
        //     .tip-font {
        //         padding: 10px 20px;
        //         width: 300px;
        //     }
        // }
    }
    .shipping-adress-title {
        font-family: Open Sans;
        font-size: 16px;
        font-weight: 600;
        line-height: 24px;
        letter-spacing: normal;
        color: #19191a;
        margin-bottom: 12px;
    }

    .add-new-address {
        font-family: Open Sans;
        font-size: 13px;
        font-weight: normal;
        line-height: 20px;
        letter-spacing: normal;
        color: #0060bf;
        padding-left: 16px;
        padding-top: 12px;
        span {
            cursor: pointer;
            &:hover {
                text-decoration: underline;
            }
        }
    }

    .shipping-adress {
        margin-bottom: 24px;
        padding: 16px;
        border: 1px solid #eeeeee;
        border-radius: 8px;
        display: flex;
        gap: 16px;
        > p {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
            @include font14;
            .contain {
                @include font14;
            }
            .iconfont {
                @include font14;
                // margin-right: 4px;
            }
        }
        .edit-adress {
            cursor: pointer;
            display: flex;
            text-align: center;
            align-items: center;
            justify-content: center;
            width: 24px;
            height: 24px;
            flex-shrink: 0;
            border-radius: 4px;
            .iconfont {
                color: #707070;
            }
            &:hover {
                .iconfont {
                    color: #19191a;
                }
                background: rgba(25, 25, 26, 0.08);
            }
        }
        .address {
            @include font14;
            flex: 1;
            min-width: 0;
            p:first-child {
                font-family: Open Sans;
                font-size: 14px;
                font-weight: 600;
                line-height: 22px;
                letter-spacing: normal;
                color: #4b4b4d;
            }
            p:last-child {
                font-family: Open Sans;
                font-size: 12px;
                font-weight: normal;
                line-height: 20px;
                letter-spacing: normal;
                color: #707070;
            }
        }
    }
    .agreement {
        @include font13;
        .policy-box {
            display: flex;
            > label {
                display: flex;
            }
        }

        .checkbox {
            font-size: 16px;
            margin-right: 5px;
        }
    }
}
.option {
    // margin-top: 32px;
    // padding: 20px 32px;
    display: flex;
    justify-content: flex-end;
    // border-top: 1px solid #e5e5e5;
    .btn {
        // width: 140px;
        margin-left: 16px;
        height: 36px;
        padding: 0 16px;
    }
}
.addList {
    width: 680px;
}
@media (max-width: 414px) {
    .part-request {
        height: 100%;
        width: 100%;
        .container {
            // padding: 0;
            // padding: 20px 16px;
            // margin-bottom: 118px;
            display: flex;
            flex-direction: column;
            // height: 90vh;
            height: 100%;
            .main-content {
                // padding: 20px 16px;
                flex: 1;
                overflow-y: scroll;
                // margin-bottom: 12px;
            }
        }
    }
    .option {
        margin-top: 0;
        background-color: #fff;
        flex-direction: column-reverse;
        // padding: 20px 16px;
        // border-top: 1px solid #e5e5e5;
        // box-shadow: 0px 0px 8px 0px rgba(0, 0, 0, 0.1);
        // z-index: 1;

        .btn {
            margin: 0 0;
            width: 100%;
            margin-bottom: 12px;
            &:first-of-type {
                margin-bottom: 0;
            }
        }
    }
}
</style>
