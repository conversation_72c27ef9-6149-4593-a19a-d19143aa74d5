<template>
    <fs-popup-new v-bind="$attrs" transition="slide-up" width="750" v-on="$listeners" :type="type" :title="title" :loading="loading" :isMDrawer="true">
        <div class="order_again" ref="order_again">
            <div class="tip" v-if="isQuote">
                <i class="iconfont icon">&#xe718;</i>
                <!-- <p>Orders on this site cannot be delivered to Australia. Please kindly go to FS Australia if you wish to deliver to Australia.</p> -->
                <p v-if="isAbnormal">{{ $c("pages.OrderDetail.pop.isAbnormal3") }} {{ list.length ? $c("pages.OrderDetail.pop.isAbnormal4") : "" }}</p>
                <p v-else>{{ $c("pages.OrderDetail.pop.quoteTip") }}</p>
            </div>
            <div class="tip" v-else>
                <i class="iconfont icon">&#xe718;</i>
                <!-- <p>Orders on this site cannot be delivered to Australia. Please kindly go to FS Australia if you wish to deliver to Australia.</p> -->
                <p v-if="isAbnormal">{{ $c("pages.OrderDetail.pop.isAbnormal") }} {{ list.length ? $c("pages.OrderDetail.pop.isAbnormal2") : "" }}</p>
                <p v-else>{{ $c("pages.OrderDetail.pop.itemsPurchased").replace("xxx", "#" + num) }}</p>
            </div>
            <ul class="goods_list">
                <li v-for="(v, i) in isAbnormal ? abnormalList : list" :key="v.products_id">
                    <section class="tip" v-if="v.type === 1">*{{ $c("pages.OrderDetail.pop.offline") }}</section>
                    <section class="tip" v-if="v.type === 2">*{{ $c("pages.OrderDetail.pop.customized") }}</section>
                    <section class="tip" v-if="v.type === 3">*{{ $c("pages.OrderDetail.pop.outOfStock") }}</section>
                    <section class="tip" v-if="v.type === 4">*{{ $c("pages.OrderDetail.pop.adjusted") }}</section>
                    <div class="img">
                        <i class="iconfont icon" v-if="v.type === 1">&#xe718;</i>
                        <img :src="v.image || v.products_image" alt="" :class="v.type === 1 ? 'name_failure' : ''" />
                    </div>
                    <div class="name">
                        <nuxt-link :to="localePath(`/products/${v.products_id}.html`)">{{ v.products_name }}</nuxt-link>
                        <!-- <h4 @click="toDetail(v.products_href)">{{v.products_name}}</h4> -->
                        <span>FS P/N: {{ v.products_model }} &nbsp;&nbsp; #{{ v.products_id }}</span>
                        <server-attributes v-if="v.server_attributes" :list="v.server_attributes"></server-attributes>

                        <div class="nature" :class="v.type === 1 ? 'name_failure' : ''" v-if="v.orders_products_attributes && v.orders_products_attributes.length">
                            <p v-for="(y, j) in v.orders_products_attributes" :key="j">{{ y.products_options }}: {{ y.products_options_values }}</p>
                        </div>
                        <div class="nature" :class="v.type === 1 ? 'name_failure' : ''" v-if="v.attributes && v.attributes.length">
                            <p v-for="(y, j) in v.attributes" :key="j">{{ y.products_options }}: {{ y.products_options_values }}</p>
                        </div>
                        <div class="nature" :class="v.type === 1 ? 'name_failure' : ''" v-if="v.length && v.length.length">
                            <p v-for="(y, j) in v.length" :key="j">{{ y.length_title }}: {{ y.length_name }}</p>
                        </div>
                        <div class="link" :class="v.type === 1 ? 'name_failure' : ''" v-if="v.type === 2" @click="toDetail(v.products_id)">
                            <span>{{ $c("pages.OrderDetail.pop.selectAttributes") }}</span> <i class="iconfont icon">&#xe703;</i>
                        </div>
                        <div class="link" :class="v.type === 1 ? 'name_failure' : ''" v-if="v.type === 1 || v.type === 3" @click="toDetail(v.products_id)">
                            <span>{{ $c("pages.OrderDetail.pop.viewSimilar") }}</span> <i class="iconfont icon">&#xe703;</i>
                        </div>
                    </div>
                    <div class="price_count" :class="{ is_quote: isQuote }" @click="getPrenum(i)">
                        <div class="price" v-if="!isQuote" v-html="v.final_price_currency || v.products_quotes_price_str">US$ 2,396.00</div>
                        <!-- <div class="stock">2 in Stock</div> -->
                        <QtyBox :num="v.products_quantity" @change="qtyChange($event, v)" v-if="!isAbnormal" @gaChange="gaChange($event, v.products_id)" @gaIcon="gaIcon($event, v.products_id)" />
                        <i @click="del(i)" class="iconfont icon" v-if="!isAbnormal && list.length > 1">&#xe65f;</i>
                    </div>
                </li>
            </ul>
        </div>
        <template slot="footer">
            <div class="operation">
                <!-- <div class="sum">
                    <i>Cart Subtotal (9 Items)</i>
                    <b>US$ 6,094.00</b> —
                    <span>View Cart</span>
                </div> -->
                <div class="btn">
                    <FsButton type="grayline" :text="$c('pages.OrderDetail.pop.cancel')" @click="close"></FsButton>
                    <FsButton type="red" :text="$c('pages.OrderDetail.pop.skip')" @click="skip" v-if="isAbnormal && list.length"></FsButton>
                    <FsButton type="red" :text="$c('pages.OrderDetail.pop.add')" @click="addToCart" v-else-if="list.length"></FsButton>
                </div>
                <!-- <div class="btn">
                    <FsButton type='gray' text='Cancel' @click="close"></FsButton>
                    <FsButton text='Skop and Continue' @click="addToCart"></FsButton>
                </div> -->
            </div>
        </template>
    </fs-popup-new>
</template>

<script>
import FsPopupNew from "@/components/FsPopupNew/FsPopupNew.vue"
import FsTip from "@/components/FsTip/FsTip.vue"
import FsButton from "@/components/FsButton/FsButton.vue"
import QtyBox from "@/components/QtyBox/QtyBox.vue"
import ServerAttributes from "@/components/ServerAttributes/ServerAttributes"
import { mapState } from "vuex"
export default {
    components: {
        FsPopupNew,
        FsButton,
        FsTip,
        QtyBox,
        ServerAttributes,
    },
    data() {
        return {
            title: this.$c("pages.OrderDetail.pop.orderAgain"),
            // height: "400px",
            loading: true,
            type: "",
            res: {},
            list: [],
            isAbnormal: false,
            abnormalList: [],
            isQuote: false,
            id: "",
            num: "",
            info: {},
            name: "",
            preNum: "",
            dataSource: "",
        }
    },
    created() {
        // this.init()
    },
    computed: {
        ...mapState({
            pageGroup: (state) => state.ga.pageGroup,
        }),
    },
    mounted() {
        if (this.$route.name.indexOf("quote_detail") != -1) {
            this.name = "Personal Hub_Quote History Page Detail_Cart Popup"
        } else if (this.$route.name.indexOf("quote_history") != -1) {
            this.name = "Personal Hub_Quote History Page_Cart Popup"
        } else {
            this.name = this.pageGroup
        }
    },
    methods: {
        getPrenum(i) {
            this.preNum = document.querySelector(`.goods_list li:nth-child(${i + 1}) .qty`).value
        },
        async init(val, i, dataSource) {
            console.log(val, i, dataSource)
            this.info = val
            this.type = ""
            this.abnormalList = []
            this.loading = true
            this.isAbnormal = false
            this.dataSource = dataSource
            // 1:close_products_not_add-失效产品
            // 2:custom_products_not_add-定制产品
            // 3:clearance_products_not_add-清仓产品
            // 4:clearance_products-改库存
            // console.log(val)
            let res
            if (i) {
                this.title = `<span class="iconfont iconfont_success" style="font-size: 20px; color: #339933;margin-right: 8px;">&#xf060;</span>${this.$c("pages.Quote.addToCart")}`
                this.isQuote = true
                this.id = val.id
                this.num = val.quotes_number
                // , type: val.inquiry_number ? 2 : 1
                res = await this.$axios.post("/api/quotes_again", { quotes_id: val.id })
            } else {
                this.id = val.orders_id
                this.num = val.orders_number
                res = await this.$axios.get("/api/orders/buyAgainFromOrder", { params: { orders_id: val.orders_id } })
            }
            if (res.code !== 200) return
            if (res.status === "error") {
                this.$message.error(res.message)
                this.$emit("close")
                return
            }
            this.list = res.data?.add_products || []
            this.list.forEach((v) => {
                v.products_quantity = v.products_qty || v.products_quantity
            })
            const list = [res.data.close_products_not_add, res.data.custom_products_not_add, res.data.clearance_products_not_add, res.data.clearance_products]
            list.forEach((v, i) => {
                v.forEach((y) => {
                    y.type = i + 1
                    y.products_quantity = y.products_qty || y.products_quantity
                    this.abnormalList.push(y)
                })
            })
            if (this.abnormalList.length) {
                this.isAbnormal = true
            }
            this.res = res.data
            this.loading = false
            // console.log(this.list)
            this.heightChange()
        },
        skip() {
            this.isAbnormal = false
            this.heightChange()
        },
        qtyChange(i, v) {
            v.products_quantity = i
        },
        gaIcon(e, id) {
            if (location.pathname.includes("/quote")) {
                window.dataLayer &&
                    window.dataLayer.push({
                        event: "uaEvent",
                        eventCategory: this.name,
                        eventAction: "edit_number",
                        eventLabel: `click_${e}_${id}`,
                        nonInteraction: false,
                    })
            }
        },
        gaChange(e, id) {
            console.log(e, id)
            e = e || 1
            if (e != 0) {
                let str = ``
                if (e > this.preNum) {
                    str = "increase"
                } else if (e < this.preNum) {
                    str = "decrease"
                }
                console.log(e, this.preNum, str)
                if (location.pathname.includes("/quote")) {
                    window.dataLayer &&
                        window.dataLayer.push({
                            event: "uaEvent",
                            eventCategory: this.name,
                            eventAction: "edit_number",
                            eventLabel: `input_${str}_${id}`,
                            nonInteraction: false,
                        })
                }
            } else {
                this.preNum = 1
            }
        },
        close() {
            this.$emit("close")
        },
        del(i) {
            this.list.splice(i, 1)
            this.heightChange()
        },
        async addToCart() {
            this.loading = true
            console.log("info", this.info, "list", this.list)
            if (window.dataLayer) {
                let products = [],
                    total = 0,
                    addQuantity = 0
                if (this.info.quotes_products || this.info.inquiry_products) {
                    ;(this.info.quotes_products || this.info.inquiry_products).forEach((v, i) => {
                        const item = {
                            id: v.products_id || v.inquiry_id,
                            name: v.products_name.slice(0, 100),
                            price: "",
                            variant: "",
                            position: i + 1,
                            category: v.categories_info && v.categories_info.join("_"),
                            quantity: v.products_qty || v.product_num,
                        }
                        addQuantity += item.quantity
                        if (v.products_quotes_price_num) {
                            item.price = parseFloat(v.products_quotes_price_num).toFixed(2)
                        } else {
                            item.price = parseFloat(v.products_price_convert.replace(/\,/g, "")).toFixed(2)
                        }
                        if (v.attributes && v.attributes.length) {
                            item.variant = v.attributes.map((y) => `${y.products_options || y.options_text}: ${y.products_options_values || y.options_values_text}`).join("|")
                        }
                        if (v.length && v.length.length) {
                            item.variant += `|${v.length[0].length_title}: ${v.length[0].length_name}`
                        }
                        products.push(item)
                    })
                    total = parseFloat(this.info.quotesTotal ? this.info.quotesTotal.subTotalNum : this.info.all_price_origin).toFixed(2)
                } else {
                    this.info.products.forEach((v, i) => {
                        const item = {
                            id: v.products && v.products.products_id,
                            name: v.products_name && v.products_name.slice(0, 100),
                            price: parseFloat(v.products_price).toFixed(2),
                            variant: "",
                            position: i + 1,
                            category: v.category_data && v.category_data.join("_"),
                            quantity: v.category_data && v.products_quantity,
                        }
                        addQuantity += item.quantity
                        total += item.price * item.quantity
                        products.push(item)
                    })
                }

                // console.log("products,", products, "name", this.name, "currency", this.info.currency || this.info.price_code, " total", total, "addQuantity", addQuantity)
                console.log(this.dataSource ? `${this.name}_${this.dataSource}` : this.name)
                window.dataLayer.push({
                    event: "eeEvent",
                    eventCategory: this.dataSource ? `${this.name}_${this.dataSource}` : this.name,
                    eventAction: "add_to_cart",
                    eventLabel: addQuantity,
                    eventValue: total,
                    nonInteraction: false,
                    ecommerce: {
                        currencyCode: this.info.currency || this.info.price_code,
                        add: {
                            products: products,
                        },
                    },
                })
            }
            const data = this.list.map((v) => {
                return {
                    products_id: v.products_prid,
                    qty: v.products_quantity,
                }
            })

            try {
                const url = this.isQuote ? "/api/cart/buyAgainQuotes" : "/api/cart/buyAgain"
                const res = await this.$axios.post(`${url}/${this.id}`, { products: data })
                this.loading = false
                if (res.code !== 200) return
                this.$emit("addSuccess", res.data)
            } catch (err) {
                if (err?.code === 400) {
                    this.shopCartLimitShow(err.message, err.data.addAllowFlag)
                } else {
                    this.$message.error(err.message)
                }
            }
        },

        shopCartLimitShow(message, showCheckOut) {
            this.close()
            this.$shopcartLimit.show({
                title: this.$c("pages.ShoppingCart.Confirmation"),
                message: message,
                viewCartName: this.$c("pages.Products.View_Cart"),
                checkoutName: this.$c("pages.ShoppingCart.Secure_Checkout2"),
                isShowCheckOutBtn: showCheckOut,
                checkOutMethod: () => {
                    // this.$router.push(this.localePath({ name: "confirm-order" }))
                    location.href = this.$localeLink(`/confirm-order`)
                },
                shopCartMethod: () => {
                    this.$router.push(this.localePath({ name: "shopping-cart" }))
                },
            })
        },

        toDetail(v) {
            this.$router.push(this.localePath(`/products/${v}.html`))
        },
        heightChange() {
            this.$nextTick(() => {
                if (!this.$refs.order_again) return
                this.height = this.$refs.order_again.offsetHeight + 61 + "px"
                this.loading = false
            })
        },
    },
}
</script>

<style lang="scss" scoped>
.order_again {
    padding: 16px 24px 24px;
    width: 750px;
    max-width: 100vw;
    @media (max-width: 960px) {
        width: 100%;
    }
    ::v-deep .fs-button {
        width: auto;
        // padding: 0 30px;
        min-width: 100px;
    }
    > .tip {
        // background: #F7F7F7;
        // border-radius: 2px;
        // border: 1px solid #b2d0ec;
        background: rgba(0, 96, 191, 0.04);
        border-radius: 3px;
        @include font14;
        padding: 10px 16px;
        display: flex;
        color: #707070;
        // margin-bottom: 20px;
        i {
            color: #0060bf;
            font-size: 16px;
            margin-right: 8px;
        }
        p {
            color: #707070;
            // font-size: 13px;
        }
    }
    .goods_list {
        // width: calc(100% + 40px);
        // overflow-y: auto;
        li {
            display: flex;
            flex-wrap: wrap;
            // padding-right: 40px;
            margin-top: 20px;
            @media (max-width: 960px) {
                padding-right: 0;
                word-break: break-all;
            }
            > .tip {
                width: 100%;
                font-size: 13px;
                color: #707070;
                margin-bottom: 16px;
            }
            > div {
                // padding-bottom: 20px;
                // border-bottom: 1px solid #e5e5e5;
            }
            &:last-child {
                > div {
                    border: none;
                }
            }
            &:hover {
                .price_count > i {
                    opacity: 1;
                }
            }
            > .img {
                padding-right: 20px;
                position: relative;
                height: 100px;
                img {
                    width: 100px;
                    height: 100px;
                    &.disabled {
                        opacity: 0.3;
                    }
                }
                i {
                    position: absolute;
                    top: 0;
                    left: 0;
                    font-size: 12px;
                    color: #707070;
                    cursor: pointer;
                }
            }
            .name {
                flex: 1;
                padding-right: 20px;
                a {
                    display: block;
                    color: #19191a;
                    font-size: 14px;
                    margin-bottom: 8px;
                    cursor: pointer;
                    line-height: 22px;
                    &:hover {
                        text-decoration: underline;
                    }
                }
                .size {
                    display: flex;
                    align-items: center;
                    span {
                        margin-bottom: 0;
                        &:first-child {
                            margin-right: 12px;
                        }
                    }
                }
                .nature {
                    color: #707070;
                    font-size: 13px;
                    // margin-bottom: 17px;
                    line-height: 20px;
                }
                span {
                    color: #707070;
                    font-size: 13px;
                    display: block;
                    margin-bottom: 8px;
                }
                .link {
                    display: flex;
                    align-items: baseline;
                    cursor: pointer;
                    margin-top: 16px;
                    color: #0060bf;
                    span {
                        margin-top: 16px;
                        color: #0060bf;
                        margin-top: 0;
                    }
                    i {
                        font-size: 12px;
                        margin-left: 6px;
                    }
                    &:hover span {
                        text-decoration: underline;
                    }
                }
            }
            .name_failure {
                opacity: 0.5;
            }
            .price_count {
                width: 120px;
                flex-shrink: 0;
                display: flex;
                align-items: flex-end;
                flex-direction: column;
                position: relative;
                // padding-right: 40px;
                .price {
                    font-size: 13px;
                    color: $textColor1;
                    margin-bottom: 10px;
                    font-weight: 400;
                }
                .stock {
                    font-size: 13px;
                    color: $textColor3;
                }
                .qty-box {
                    // width: 65px;
                    width: 100%;
                    background: $bgColor3;
                }
                > i {
                    position: absolute;
                    right: -26px;
                    top: 35px;
                    color: $textColor3;
                    font-size: 16px;
                    cursor: pointer;
                    transition: all 0.2s;
                    opacity: 0;
                    &:hover {
                        color: $textColor3;
                    }
                }
            }
            .is_quote > i {
                top: 10px;
            }
        }
    }
}
.operation {
    padding: 0 24px 24px;
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    background-color: #fff;
    // border-top: 1px solid #e5e5e5;
    .sum {
        display: flex;
        color: $textColor1;
        margin-bottom: 15px;
        i {
            font-size: 14px;
        }
        b {
            font-size: 16px;
            font-weight: 600;
            margin: 0 3px;
        }
        span {
            font-size: 14px;
            color: $textColor8;
            cursor: pointer;
            margin-left: 4px;
            &:hover {
                text-decoration: underline;
            }
        }
    }
    .btn {
        display: flex;
        button {
            // width: 220px;
            margin-left: 16px;
            &:first-child {
                // width: 100px;
            }
            i {
                margin-right: 8px;
            }
        }
    }
}
::v-deep .fs-popup-ctn {
    .fs-popup-header {
        .iconfont_success {
            display: none;
        }
    }
}
.fs-popup {
    @media (max-width: 960px) {
        .fs-popup-ctn {
            height: 100vh !important;

            .fs-popup-header {
                padding-left: 16px;
                padding-right: 16px;
            }
            .title_box .title {
                font-size: 16px;
            }
            .fs-popup-body {
                .order_again {
                    // height: 100%;
                    padding: 20px 0 0;
                    display: flex;
                    flex-direction: column;
                    .tip {
                        margin: 0 16px 0;
                    }
                    .goods_list {
                        width: 100%;
                        flex: 1;
                        padding-left: 16px;
                        padding-right: 16px;
                        li {
                            // border-bottom: 1px solid #e5e5e5;
                            position: relative;
                            > div {
                                border: none;
                                padding-bottom: 12px;
                            }
                            .price_count {
                                width: 100%;
                                position: static;
                                align-items: flex-start;
                                padding-left: 100px;
                                .qty-box {
                                    width: 114px;
                                }
                                i {
                                    opacity: 1;
                                    right: 0;
                                    top: 0;
                                    bottom: 12px;
                                    transform: translateY(calc(100% - 28px));
                                }
                                .handle-qty {
                                    display: flex;
                                }
                            }
                            > .img {
                                img {
                                    width: 80px;
                                    height: 80px;
                                }
                            }
                        }
                        .name {
                            padding-right: 0px;
                        }
                    }
                }
            }

            .operation {
                width: 100%;
                margin-left: 0;
                display: flex;
                padding: 16px;
                // border-top: 1px solid #e5e5e5;
                // box-shadow: 0 -2px 3px -1px rgb(120 102 102 / 30%);
                .btn {
                    display: flex;
                    flex-direction: column-reverse;
                    width: 100%;
                    button {
                        max-width: 100%;
                        width: 100%;
                        margin-left: 0;
                        &:last-child {
                            margin-bottom: 12px;
                        }
                    }
                }
            }
        }
    }
}
// @media (max-width: 768px) {
//     ::v-deep {
//         .fs-popup {
//             top: auto;
//             height: auto;
//             border-radius: 3px 3px 0 0;
//             overflow: hidden;
//             .fs-popup-ctn {
//                 max-height: calc(100vh - 64px);
//                 overflow: auto;
//             }
//         }
//     }
// }
</style>
