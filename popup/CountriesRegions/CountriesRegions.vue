<template>
    <fs-popup-new
        class="countries_regions_wrap"
        v-bind="$attrs"
        :show="show"
        :title="$c('popup.CountriesRegions.COUNTRIES_REGIONS')"
        v-on="$listeners"
        width="480"
        type="all"
        transition="slide-up"
        isNew
        :isMDrawer="true"
        @close="close">
        <div class="countries_regions_ctn">
            <!-- <p class="desc1">{{ desc1 }}</p> -->
            <p class="desc2">{{ desc1 }} {{ desc2 }}</p>
        </div>
        <div class="footer_btn">
            <FsButton :type="deviceType === 'm' ? 'gray' : 'white'" @click="close">{{ btn1 }}</FsButton>
            <FsButton @click="changeWebsite" v-loading="loading"> {{ btn2 }} </FsButton>
        </div>
    </fs-popup-new>
</template>

<script>
import FsPopupNew from "@/components/FsPopupNew/FsPopupNew"
import FsButton from "@/components/FsButton/FsButton.vue"
import { mapState, mapActions } from "vuex"
import { setCookieOptions } from "@/util/util"
export default {
    name: "CountriesRegions",
    components: {
        FsButton,
        FsPopupNew,
    },
    data() {
        return {
            show: false,
            desc1: "",
            desc2: "",
            btn1: "",
            btn2: "",
            type: "",
            loading: false,
            preWebsiteInfoNew: null,
            ipWebsiteInfo: null,
            fs_websiteinfo: null,
        }
    },
    computed: {
        ...mapState({
            userInfo: (state) => state.userInfo.userInfo,
            pageGroup: (state) => state.ga.pageGroup,
            isLogin: (state) => state.userInfo.isLogin,
            website: (state) => state.webSiteInfo.website,
            urlWebsite: (state) => state.webSiteInfo.urlWebsite,
            country_name: (state) => state.webSiteInfo.country_name,
            countryNameList: (state) => state.selectCountry.countryNameList,
            isMobile: (state) => state.device.isMobile,
            deviceType: (state) => state.device.deviceType,
        }),
    },
    methods: {
        ...mapActions({
            updateSiteInfo: "selectCountry/updateSiteInfo",
            getCountryNameList: "selectCountry/getCountryNameList",
        }),
        init() {
            this.preWebsiteInfoNew = this.$cookies.get("preWebsiteInfoNew")
            this.ipWebsiteInfo = this.$cookies.get("ipWebsiteInfo")
            this.fs_websiteinfo = this.$cookies.get("fs_websiteinfo")
            if (!this.$cookies.get("updateWebsiteInfoFlag")) {
                // if (process.env.ENV !== "prod_cn_local" && this.urlWebsite !== "en") {
                console.log("1122_33444")
                if (!this.preWebsiteInfoNew && this.ipWebsiteInfo) {
                    console.log("11__111")
                    if (this.website !== this.ipWebsiteInfo?.website) {
                        console.log("112__111")
                        this.desc1 = this.$c("popup.CountriesRegions.Seems_like_you_are_coming_from").replaceAll("XXXX", this.countryNameList[this.ipWebsiteInfo?.iso_code] || this.ipWebsiteInfo?.country_name)
                        this.desc2 = this.$c("popup.CountriesRegions.Do_you_want_to_visit_FS_website_in_your_country_region").replaceAll(
                            "XXXX",
                            this.countryNameList[this.ipWebsiteInfo?.iso_code] || this.ipWebsiteInfo?.country_name
                        )
                        this.btn1 = this.$c("popup.CountriesRegions.Stay_at").replaceAll("XXXX", this.country_name)
                        this.btn2 = this.$c("popup.CountriesRegions.Go_to").replaceAll("XXXX", this.countryNameList[this.ipWebsiteInfo?.iso_code] || this.ipWebsiteInfo?.country_name)
                        this.type = "ip"
                        this.show = true
                    }
                } else {
                    console.log("113__111")
                    if (this.preWebsiteInfoNew && this.website !== this.preWebsiteInfoNew?.website) {
                        console.log("114__111")
                        this.desc1 = this.$c("popup.CountriesRegions.Seems_like_you_have_recently_visited_our_website_in").replaceAll(
                            "XXXX",
                            this.countryNameList[this.preWebsiteInfoNew?.iso_code] || this.preWebsiteInfoNew?.country_name
                        )
                        this.desc2 = this.$c("popup.CountriesRegions.Do_you_want_to_visit_FS_website_in").replaceAll("XXXX", this.countryNameList[this.preWebsiteInfoNew?.iso_code] || this.preWebsiteInfoNew?.country_name)
                        this.btn1 = this.$c("popup.CountriesRegions.Stay_at").replaceAll("XXXX", this.country_name)
                        this.btn2 = this.$c("popup.CountriesRegions.Go_to").replaceAll("XXXX", this.countryNameList[this.preWebsiteInfoNew?.iso_code] || preWebsiteInfoNew?.country_name)
                        this.type = "pre"
                        this.show = true
                    }
                }
                this.$cookies.set("preWebsiteInfoNew", this.fs_websiteinfo)
                // }
            } else {
                this.$cookies.remove("updateWebsiteInfoFlag")
            }
        },
        changeWebsite() {
            let obj = {}
            if (this.type) {
                if (this.type === "ip") {
                    obj = this.ipWebsiteInfo
                } else if (this.type === "pre") {
                    obj = this.preWebsiteInfoNew
                }
                obj.isFetch = false

                console.log("change_website_change_website")
                console.log(obj)
                this.loading = true
                this.updateSiteInfo(obj)
            }
        },
        close() {
            this.show = false
        },
    },
    created() {},
    mounted() {
        this.init()
        this.$axios.get("/api/website/getCountries")
        // this.getCountryNameList();
        // setTimeout(() => {
        // 	console.log("22_@@")
        //    console.log(this.countryNameList)
        // }, 5000);
    },
}
</script>

<style lang="scss" scoped>
.countries_regions_wrap {
    ::v-deep {
        @include mobile() {
            .fs-popup {
                align-items: end;
                .fs-popup-header {
                    // padding: 12px 20px;
                }
                .countries_regions_ctn {
                    // padding: 20px;
                    padding-bottom: 36px;
                }
                .footer_btn {
                    // padding: 16px 10px;
                    // border-top: 1px solid #e5e5e5;
                    .fs-button {
                        margin-left: 0;
                        // height: 42px;
                        &:first-child {
                            margin-left: 0;
                        }
                    }
                }
            }
        }
    }
}
.countries_regions_ctn {
    // padding: 20px 32px;
    padding-bottom: 32px;
}

.desc1 {
    @include font16;
    font-weight: 600;
    color: $textColor1;
    margin-bottom: 16px;
}

.desc2 {
    @include font14;
    color: $textColor2;
}

.footer_btn {
    // padding: 20px 32px;
    display: flex;
    justify-content: flex-end;
    padding-bottom: 24px;
    @include mobile {
        padding-bottom: 20px;
    }
    .fs-button {
        height: 36px;
        margin-left: 16px;

        &:first-child {
            margin-left: 0;
        }
    }

    @include mobile() {
        display: flex;
        flex-direction: column-reverse;

        .fs-button {
            margin-left: 0;
            margin-bottom: 12px;
            height: 42px;
            &:first-child {
                margin-bottom: 0;
            }
        }
    }
}
</style>
