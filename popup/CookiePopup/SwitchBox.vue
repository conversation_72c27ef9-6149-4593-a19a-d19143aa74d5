<template>
    <div class="switch_box" :class="{ isOpen: value }" @click="changeValue">
        <span class="dot"></span>
    </div>
</template>

<script>
export default {
    props: {
        value: {
            type: Boolean,
            default: false,
        },
    },
    methods: {
        changeValue() {
            const newVal = !this.value
            this.$emit("input", newVal) // v-model 默认监听 input
            this.$emit("change", newVal) // 保留自定义事件
        },
    },
}
</script>

<style lang="scss" scoped>
.switch_box {
    display: flex;
    align-items: center;
    width: 28px;
    height: 16px;
    padding: 2px;
    border-radius: 100px;
    background: #c9cdd4;
    cursor: pointer;
    &.isOpen {
        justify-content: end;
        background-color: #10a300;
    }
    .dot {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        background: #ffffff;
    }
}
</style>
