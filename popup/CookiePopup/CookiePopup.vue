<template>
    <FsPopupNew :show="show" @close="handleClose" :isMDrawer="true" class="cookie_popup">
        <template #header>
            <div class="logo_box"></div>
        </template>
        <div class="popup_content">
            <div class="popup_content_main">
                <h1>{{ getTextContent("title") }}</h1>
                <p>{{ getTextContent("desc01") }}</p>
                <template v-if="cookieType">
                    <a class="policy_more" :href="$localeLink('/policies/privacy_policy.html')">{{ getTextContent("desc02") }}</a>
                </template>
                <template v-else>
                    <p class="policy_tips" v-html="validateFont(getTextContent('desc02'))"></p>
                </template>
                <h1>{{ getTextContent("manage") }}</h1>
                <div class="cookies_content">
                    <ul>
                        <li v-for="(item, index) in cookies_list" :key="item.id">
                            <div class="cookies_item">
                                <div class="label">
                                    <span class="iconfont" v-show="item.isShow" @click="handleDescShow(item)">&#xf067;</span>
                                    <span class="iconfont" v-show="!item.isShow" @click="handleDescShow(item)">&#xf068;</span>
                                    <b>{{ item.name }}</b>
                                </div>
                                <div>
                                    <span class="isActive" v-if="item.isAlways">{{ getTextContent("btn01") }}</span>
                                    <SwitchBox v-else v-model="item.isOpen" @change="(flag) => handleSwitchChange(flag, item)" />
                                </div>
                            </div>
                            <slide-down>
                                <p v-if="item.isShow" class="des_box">{{ item.desc }}</p>
                            </slide-down>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
        <template #footer>
            <div class="footer_btn">
                <FsButton v-if="!cookieType" type="gray" @click="handelAllowAll">{{ getTextContent("btn02") }}</FsButton>
                <FsButton type="gray" @click="handleRejectAll">{{ getTextContent("btn03") }}</FsButton>
                <FsButton type="black" @click="handleConfirmChoices">{{ getTextContent("btn04") }}</FsButton>
            </div>
        </template>
    </FsPopupNew>
</template>

<script>
import SlideDown from "@/components/SlideDown/SlideDown.vue"
import FsButton from "@/components/FsButton/FsButton.vue"
import FsPopupNew from "@/components/FsPopupNew/FsPopupNew.vue"
import SwitchBox from "./SwitchBox.vue"
import { setCookieOptions } from "@/util/util"
import { mapState, mapMutations } from "vuex"
export default {
    props: {
        show: {
            type: Boolean,
            default: false,
        },
        cookieType: {
            type: [Number, null],
            default: null,
        },
    },
    components: {
        SlideDown,
        FsButton,
        FsPopupNew,
        SwitchBox,
    },
    data() {
        return {
            cookies_list: [],
            cookieTipPop: {
                title: `Privacy Preference Center`,
                desc01: `When you visit any website, it may store or retrieve information on your browser, mostly in the form of cookies. This information might be about you, your preferences or your device and is mostly used to make the site work as yoou expect it to. The information does not usually directly identify you, biut it can give you a more personalized web experience. Because we respect your right to  privacy, you can choose not to allow some types of cookies. Click on the different category headings to find out more and change our default settings. However, blocking some types of cookies may impact your experience of the site and the services we are able to offer.`,
                desc02: `More information`,
                manage: `Manage Consent Preferences`,
                option1: `Strictly Necessary Cookies`,
                content1: `These cookies are necessary to enable the basic features of this site to function, such as providing secure login or remembering how far you are through an order, the cookies that are strictly necessary for us to provide information society services that you request. These cookies do not store any personally identifiable information.`,
                btn01: `Always Active`,
                option2: `Performance Cookies`,
                content2: `These cookies allow us to count visits and traffic sources so we can measure and improve the performance of our site. They help us to know which pages are the most and least popular and see how visitors move around the site. All information these cookies collect is aggregated and therefore anonymous. If you do not allow these cookies we will not know when you have visited our site, and will not be able to monitor its performance.`,
                btn02: `Allow All`,
                btn03: `Reject All`,
                btn04: `Confirm My Choice`,
            },
            cookieTipPop_en: {
                title: `Cookie Preference Center`,
                desc01: `When you visit any website, it may store or retrieve information on your browser, mostly in the form of cookies. This information might be about you, your preferences or your device and is mostly used to make the site work as you expect it to. The information does not usually directly identify you, but it can give you a more personalized web experience. Because we respect your right to privacy, you can choose not to allow some types of cookies. Click on the different category headings to find out more and change our default settings. However, blocking some types of cookies may impact your experience of the site and the services we are able to offer.`,
                desc02: `You can accept or decline all but Strictly Necessary Cookies, or customize your cookie settings below. You can change your cookie settings at any time.  To learn more about how FS processes personal data, please visit our <a href="XXXX">cookie policy</a>.`,
                manage: `Manage Consent Preferences`,
                option1: `Strictly Necessary Cookies`,
                content1: `These cookies are necessary to enable the basic features of this site to function, such as providing secure login or remembering how far you are through an order, the cookies that are strictly necessary for us to provide information society services that you request. These cookies do not store any personally identifiable information.`,
                btn01: `Always Active`,
                option2: `Performance Cookies`,
                content2: `These cookies allow us to count visits and traffic sources so we can measure and improve the performance of our site. They help us to know which pages are the most and least popular and see how visitors move around the site. All information these cookies collect is aggregated and therefore anonymous. If you do not allow these cookies we will not know when you have visited our site, and will not be able to monitor its performance.`,
                btn02: `Allow All`,
                btn03: `Reject All`,
                btn04: `Confirm My Choice`,
            },
            cookieTipPop_mx: {
                title: `Cookie Preference Center`,
                desc01: `Cuando visitas cualquier sitio web, este puede almacenar o recuperar información en tu navegador, principalmente en forma de cookies. Esta información puede referirse a ti, tus preferencias o tu dispositivo, y se utiliza principalmente para que el sitio funcione como esperas. Esta información generalmente no te identifica directamente, pero puede brindarte una experiencia web más personalizada. Como respetamos tu derecho a la privacidad, puedes optar por no permitir ciertos tipos de cookies. Haz clic en los encabezados de las diferentes categorías para obtener más información y cambiar nuestra configuración predeterminada. Sin embargo, bloquear algunos tipos de cookies puede afectar tu experiencia en el sitio y los servicios que podemos ofrecemos.`,
                desc02: `Puedes aceptar o rechazar todas las cookies excepto las estrictamente necesarias, o personalizar tu configuración de cookies a continuación. Puedes cambiar tu configuración de cookies en cualquier momento. Para obtener más información sobre cómo FS trata los datos personales, visita nuestra <a href="XXXX">política de cookies</a>.`,
                manage: `Administrar preferencias de consentimiento`,
                option1: `Cookies estrictamente necesarias`,
                content1: `Estas cookies son necesarias para que el sitio funcione correctamente, como permitir el inicio de sesión seguro o recordar el progreso de un pedido. Son cookies imprescindibles para que podamos ofrecerte los servicios de la sociedad de la información que solicites. Estas cookies no almacenan ninguna información que permita identificarte personalmente.`,
                btn01: `Siempre activas`,
                option2: `Cookies de rendimiento`,
                content2: `Estas cookies nos permiten contar las visitas y las fuentes de tráfico para poder medir y mejorar el rendimiento de nuestro sitio. Nos ayudan a saber qué páginas son las más y las menos populares y a ver cómo se mueven los visitantes dentro del sitio. Toda la información que recopilan estas cookies se agrupa y, por lo tanto, es anónima. Si no permites estas cookies, no sabremos cuándo has visitado nuestro sitio ni podremos monitorear su rendimiento.`,
                btn02: `Permitir todas`,
                btn03: `Rechazar todas`,
                btn04: `Confirmar mi elección`,
            },
        }
    },
    computed: {
        ...mapState({
            website: (state) => state.webSiteInfo.website,
            iso_code: (state) => state.webSiteInfo.iso_code,
        }),
        // 判断是否为de-en站点
        isDeEnSite() {
            return this.website === "de-en"
        },
        // 判断是否应该使用英文数据（au、sg站点）
        shouldUseEnglishData() {
            return this.website === "au" || this.website === "sg"
        },
    },
    methods: {
        ...mapMutations({
            setShowCookieTip: "webSiteInfo/setShowCookieTip",
        }),
        handleClose() {
            this.$emit("close")
        },
        handleSwitchChange(flag, item) {
            if (this.iso_code === "US") {
                if (item.id === 3) {
                    this.cookies_list[3].isOpen = flag
                } else if (item.id === 4) {
                    this.cookies_list[2].isOpen = flag
                }
            }
        },
        handleDescShow(item) {
            console.log(item, "1111")
            this.cookies_list = this.cookies_list.map((i) => {
                return item.id === i.id ? { ...i, isShow: !i.isShow } : i
            })
        },
        validateFont(params) {
            let str = ""
            str = params.replace(/XXXX/g, this.localePath({ path: "/policies/privacy_policy.html" }))
            str = str.replace(/YYYY/g, this.$localeLink("/policies/cookie_notice.html"))
            return str
        },
        handelAllow() {
            // 将未选择选中的打开
            this.cookies_list = this.cookies_list.map((item) => ({ ...item, isOpen: true }))
            this.handleSDK()
        },
        // 允许所有cookie - 保持不变
        handelAllowAll() {
            // 无条件设置所有cookie为同意状态
            this.$cookies.set("fs_google_analytics", "yes")

            this.$cookies.set("fs_marketing_sdk", "yes")

            if (this.iso_code === "US") {
                this.$cookies.set("fs_function_cookie", "yes")
            }

            this.$cookies.set("cookieconsent_dismissed", "yes")

            // 更新GTM同意状态
            if (window.gtag) {
                window.gtag("consent", "update", {
                    region: ["AT", "BE", "BG", "CY", "CZ", "DE", "DK", "EE", "ES", "FI", "FR", "GR", "HR", "HU", "IE", "IS", "IT", "LI", "LT", "LU", "LV", "MT", "NL", "NO", "PL", "PT", "RO", "SE", "SI", "SK"],
                    ad_storage: "granted",
                    ad_user_data: "granted",
                    ad_personalization: "granted",
                    analytics_storage: "granted",
                })
            }

            this.setShowCookieTip(false)
            this.handleClose()

            // 刷新页面
            window && window.location.reload()
        },
        handleConfirmChoices() {
            if (this.iso_code === "US") {
                if (this.cookies_list[1].isOpen) {
                    // 如果营销cookie开启，设置所有cookie
                    this.$cookies.set("fs_function_cookie", "yes")
                } else {
                    // 如果营销cookie关闭，移除该cookie - 传入域名选项
                    this.$cookies.remove("fs_function_cookie")
                }
                if (this.cookies_list[2].isOpen) {
                    // 如果营销cookie开启，设置所有cookie
                    this.$cookies.set("fs_marketing_sdk", "yes")
                } else {
                    // 如果营销cookie关闭，移除该cookie - 传入域名选项
                    this.$cookies.remove("fs_marketing_sdk")
                }
            } else {
                if (this.cookies_list[1].isOpen) {
                    // 如果营销cookie开启，设置所有cookie
                    this.$cookies.set("fs_marketing_sdk", "yes")
                } else {
                    // 如果营销cookie关闭，移除该cookie - 传入域名选项
                    this.$cookies.remove("fs_marketing_sdk")
                }
            }

            // 始终设置Google Analytics
            this.$cookies.set("fs_google_analytics", "yes")

            this.$cookies.set("cookieconsent_dismissed", "yes")

            this.setShowCookieTip(false)
            this.handleClose()

            // 刷新页面
            window && window.location.reload()
        },
        handleBasicConsent() {
            // 只设置Google Analytics，不设置营销SDK
            this.$cookies.set("fs_google_analytics", "yes")

            // 统一使用cookieconsent_dismissed替换cookies_tip_hidden
            this.$cookies.set("cookieconsent_dismissed", "yes")

            this.setShowCookieTip(false)
            this.handleClose()

            // 刷新页面
            window && window.location.reload()
        },
        handleSDK() {
            const fs_marketing_sdk = this.cookies_list[1].isOpen
            console.log(fs_marketing_sdk, "fs_marketing_sdk")

            if (fs_marketing_sdk) {
                this.$cookies.set("fs_marketing_sdk", "yes")
            } else {
                // 当营销SDK关闭时，移除该cookie - 传入域名选项
                this.$cookies.remove("fs_marketing_sdk")
            }

            // 始终设置Google Analytics
            this.$cookies.set("fs_google_analytics", "yes")

            this.setShowCookieTip(false)
            // 统一使用cookieconsent_dismissed替换cookies_tip_hidden
            this.$cookies.set("cookieconsent_dismissed", "yes")
            this.handleClose()
            //刷新页面
            window && window.location.reload()
        },
        // 拒绝所有cookie - 需要调整
        handleRejectAll() {
            // 设置所有非必需cookies为关闭状态
            this.cookies_list = this.cookies_list.map((item) => ({
                ...item,
                isOpen: item.isAlways ? true : false,
            }))

            // 设置为"rejected"表示用户拒绝
            this.$cookies.set("cookieconsent_dismissed", "rejected")

            // 移除营销相关cookies
            this.$cookies.remove("fs_marketing_sdk")

            if (this.iso_code === "US") {
                this.$cookies.remove("fs_function_cookie")
            }

            this.$cookies.remove("fs_google_analytics")

            this.setShowCookieTip(false)
            this.handleClose()
            window && window.location.reload()
        },
        // 关闭弹窗 - 需要调整
        handleClose() {
            // 如果用户直接关闭弹窗而没有做出选择，设置为"dismissed"
            const currentStatus = this.$cookies.get("cookieconsent_dismissed")
            if (!currentStatus || currentStatus === null || currentStatus === undefined) {
                this.$cookies.set("cookieconsent_dismissed", "dismissed")
            }
            this.$emit("close")
        },
        // 根据站点类型获取文本内容
        getTextContent(key) {
            if (this.isDeEnSite) {
                return this.cookieTipPop[key]
            } else if (this.shouldUseEnglishData) {
                return this.cookieTipPop_en[key]
            } else if (this.website === "mx") {
                return this.cookieTipPop_mx[key]
            } else {
                return this.$c(`pages.home.cookieTipPop.${key}`)
            }
        },
    },
    created() {
        // 初始化cookies_list
        this.cookies_list = [
            {
                id: 1,
                name: this.getTextContent("option1"),
                isShow: false,
                isAlways: true,
                isOpen: true,
                desc: this.getTextContent("content1"),
            },
        ]

        if (this.website === "en") {
            if (this.iso_code === "US") {
                this.cookies_list.push({
                    id: 2,
                    name: this.getTextContent("option2"),
                    isShow: false,
                    isAlways: false,
                    isOpen: false,
                    desc: this.getTextContent("content2"),
                })
                this.cookies_list.push({
                    id: 3,
                    name: this.getTextContent("option3"),
                    isShow: false,
                    isAlways: false,
                    isOpen: false,
                    desc: this.getTextContent("content3"),
                })
                this.cookies_list.push({
                    id: 4,
                    name: this.getTextContent("option4"),
                    isShow: false,
                    isAlways: false,
                    isOpen: false,
                    desc: this.getTextContent("content4"),
                })
            } else {
                this.cookies_list.push({
                    id: 2,
                    name: this.getTextContent("option3"),
                    isShow: false,
                    isAlways: false,
                    isOpen: false,
                    desc: this.getTextContent("content3"),
                })
            }
        } else {
            this.cookies_list.push({
                id: 2,
                name: this.getTextContent("option2"),
                isShow: false,
                isAlways: false,
                isOpen: false,
                desc: this.getTextContent("content2"),
            })
        }

        // 根据现有cookie状态设置Performance Cookies的开关状态
        const fs_marketing_sdk = this.$cookies.get("fs_marketing_sdk") === "yes" || false
        const fs_function_cookie = this.$cookies.get("fs_function_cookie") === "yes" || false
        this.cookies_list = this.cookies_list.map((item) => {
            if (this.website === "en") {
                if (this.iso_code === "US") {
                    if (item.id === 2) {
                        return { ...item, isOpen: fs_function_cookie }
                    } else if (item.id === 3 || item.id === 4) {
                        return { ...item, isOpen: fs_marketing_sdk }
                    } else {
                        return item
                    }
                } else {
                    if (item.id === 2) {
                        return { ...item, isOpen: fs_marketing_sdk }
                    } else {
                        return item
                    }
                }
            } else {
                if (item.id === 2) {
                    return { ...item, isOpen: fs_marketing_sdk }
                } else {
                    return item
                }
            }
        })
    },
}
</script>

<style lang="scss" scoped>
.logo_box {
    background-image: url(https://img-en.fs.com/includes/templates/fiberstore/images/fs-new/common/logo.svg);
    display: inline-block;
    flex-shrink: 0;
    height: 36px;
    margin-right: 0;
    width: 76px;
    background-repeat: no-repeat;
}
.popup_content {
    max-width: 750px;
    padding: 16px 24px 0;
    .popup_content_main {
        @include font12;
        h1 {
            @include font14;
            font-weight: 600;
            margin-bottom: 4px;
        }
        p {
            color: $textColor3;
        }
        .policy_tips {
            margin-top: 20px;
            margin-bottom: 16px;
        }
        .policy_more {
            display: inline-block;
            margin-top: 4px;
            margin-bottom: 16px;
        }
    }
    .cookies_content {
        border: 1px solid #ededf0;
        border-radius: 8px;
        margin-top: 8px;
        > ul {
            > li {
                padding: 16px 20px;
                &:not(:last-of-type) {
                    border-bottom: 1px solid #ededf0;
                }
            }
            .cookies_item {
                display: flex;
                align-items: center;
                justify-content: space-between;
                .label {
                    color: $textColor1;
                    display: flex;
                    align-items: center;
                    column-gap: 12px;
                    @include font13;
                    .iconfont {
                        color: $textColor3;
                        cursor: pointer;
                        display: block;
                        font-size: 14px;
                        line-height: 1;
                        &::before {
                            display: block;
                        }
                    }
                }
            }
            .des_box {
                margin-top: 12px;
                @include font12;
            }
            .isActive {
                color: #10a300;
                font-weight: 600;
            }
        }
    }
    @media (max-width: 768px) {
        padding: 20px 16px;
    }
}
.footer_btn {
    display: flex;
    justify-content: end;
    column-gap: 12px;
    padding: 24px;
    .fs-button {
        height: 36px;
        padding: 0px 12px;
    }
}
@media (max-width: 768px) {
    .cookie_popup {
        &::v-deep {
            .fs-popup {
                max-height: calc(100% - 64px);
                .fs-popup-ctn {
                    max-height: calc(100vh - 64px);
                    // max-height: none;
                    // height: 100%;
                    border-radius: 0;
                }
            }
        }
    }
    .footer_btn {
        margin-top: 0;
        display: flex;
        flex-direction: column-reverse;
        padding: 16px 16px 24px;
        row-gap: 12px;
        box-shadow: 2px 0 4px 0 rgba(0, 0, 0, 0.2);
        .fs-button {
            height: 42px;
        }
    }
}
</style>
