<template>
    <fs-popup-new v-bind="$attrs" :autoContent="true" transition="slide-up" v-on="$listeners" :loading="loading" :isMDrawer="true">
        <div class="body">
            <div class="head">
                <div class="top">
                    <span>{{ $c("pages.Products.product_develop[2]") }}</span>
                    <i class="iconfont" @click="close">&#xf30a;</i>
                </div>
                <!-- 步骤条 -->
                <div class="step" ref="step" v-if="success != 3">
                    <div class="left" :class="{ isAct: success == 2 }" @click="back(1)"></div>
                    <div class="stepBD">
                        <div class="item" :class="{ haveHover: step_index === 1 && i === 0 }" v-for="(t, i) in step_list" :key="i" @click="back(i + 1)">
                            <div class="bg" :class="{ active: i == step_index }"></div>
                            <div class="txt">
                                <span v-html="t"></span>
                            </div>
                        </div>
                    </div>
                    <div class="right" :class="{ isAct: success == 1 }"></div>
                </div>
            </div>
            <div class="content" v-if="success === 1">
                <!-- select product -->
                <div class="product_info">
                    <div class="title">{{ $c("pages.Products.product_develop[5]") }}</div>
                    <div class="info">
                        <img v-if="img" :src="img" />
                        <div class="con">
                            <div class="left">
                                <div>
                                    {{ info.products_name }} <span>#{{ info.products_id_show }}</span>
                                </div>
                                <div class="blue" v-if="is_customized" @click="showCustom">
                                    <span>{{ $c("pages.Products.product_develop[10]") }}</span>
                                    <span class="iconfont icofnont-down" :class="{ 'icofnont-down-up': showDown }">&#xe704;</span>
                                </div>
                                <div class="custom_list" v-if="productDevList.length && showDown">
                                    <div class="custom_item" v-for="(item, index) in productDevList" :key="index">
                                        <span>{{ item }}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- list -->
                <div class="list">
                    <div class="title">{{ $c("pages.Products.product_develop[6]") }}</div>
                    <validate-message :message="isOnly" :type="`error`"></validate-message>
                    <div class="components" v-if="list.components && list.components.length">
                        <div class="tit">{{ $c("pages.Products.product_develop[7]") }}</div>
                        <div class="comp_list">
                            <div class="comp_item" :class="`comp_item_${item.type}`" v-for="(item, index) in list.components" :key="index">
                                <template v-if="item.type == 'text'">
                                    <p class="txt">{{ item.title }}*</p>
                                    <input :class="{ is_new: item.type == 'text' }" :type="item.type" :placeholder="item.placeholder" v-model="item.value" @input="foInp(item)" @blur="blInp(item)" />
                                    <div class="error-box">
                                        <validate-error :error="item.error"></validate-error>
                                    </div>
                                </template>
                                <template v-else-if="item.type == 'radio'">
                                    <div class="subhead">{{ item.title }}</div>
                                    <div class="radio_list">
                                        <label class="radio_item" v-for="(t, i) in item.list" :key="i">
                                            <input :class="{ is_new: item.type == 'text' }" :type="item.type" :value="t.key" v-model="item.value" :placeholder="t.placeholder" @click="selectitem(item, t)" />
                                            <p class="txt">{{ t.value }}</p>
                                        </label>
                                        <template v-if="item.is_other">
                                            <input type="text" class="other is_new" v-model="item.other" @input="foInp(item)" @blur="blInp(item)" />
                                        </template>
                                    </div>
                                    <div class="error-box">
                                        <validate-error :error="item.error"></validate-error>
                                    </div>
                                </template>
                            </div>
                        </div>
                    </div>
                    <div class="performance" v-if="list.performance && list.performance.length">
                        <div class="tit">{{ $c("pages.Products.product_develop[8]") }}</div>
                        <div class="comp_list">
                            <div class="comp_item" :class="`comp_item_${item.type}`" v-for="(item, index) in list.performance" :key="index">
                                <template v-if="item.type == 'text'">
                                    <p class="txt">{{ item.title }}</p>
                                    <input :type="item.type" :class="{ is_new: item.type == 'text' }" :placeholder="item.placeholder" v-model="item.value" @input="foInp(item)" @blur="blInp(item)" />
                                    <div class="error-box">
                                        <validate-error :error="item.error"></validate-error>
                                    </div>
                                </template>
                                <template v-else-if="item.type == 'radio'">
                                    <div class="subhead">{{ item.title }}</div>
                                    <div class="radio_list">
                                        <label class="radio_item" v-for="(t, i) in item.list" :key="i">
                                            <input :type="item.type" :class="{ is_new: item.type == 'text' }" :value="t.key" v-model="item.value" :placeholder="t.placeholder" @click="selectitem(item, t)" />
                                            <p class="txt">{{ t.value }}</p>
                                        </label>
                                        <template v-if="item.is_other">
                                            <input type="text" class="other is_new" v-model="item.other" @input="foInp(item)" @blur="blInp(item)" />
                                        </template>
                                    </div>
                                    <div class="error-box">
                                        <validate-error :error="item.error"></validate-error>
                                    </div>
                                </template>
                            </div>
                        </div>
                    </div>
                    <div class="services" v-if="list.services && list.services.length">
                        <div class="tit">{{ $c("pages.Products.product_develop[9]") }}</div>
                        <div class="comp_list">
                            <div class="comp_item" :class="`comp_item_${item.type}`" v-for="(item, index) in list.services" :key="index">
                                <template v-if="item.type == 'text'">
                                    <p class="txt">{{ item.title }}</p>
                                    <input :type="item.type" :class="{ is_new: item.type == 'text' }" :placeholder="item.placeholder" v-model="item.value" @input="foInp(item)" @blur="blInp(item)" />
                                    <div class="error-box">
                                        <validate-error :error="item.error"></validate-error>
                                    </div>
                                </template>
                                <template v-else-if="item.type == 'radio'">
                                    <div class="subhead">{{ item.title }}</div>
                                    <div class="radio_list">
                                        <label class="radio_item" v-for="(t, i) in item.list" :key="i">
                                            <input :class="{ is_new: item.type == 'text' }" :type="item.type" :value="t.key" v-model="item.value" :placeholder="t.placeholder" @click="selectitem(item, t)" />
                                            <p class="txt">{{ t.value }}</p>
                                        </label>
                                        <template v-if="item.is_other">
                                            <input type="text" class="other is_new" v-model="item.other" @input="foInp(item)" @blur="blInp(item)" />
                                        </template>
                                    </div>
                                    <div class="error-box">
                                        <validate-error :error="item.error"></validate-error>
                                    </div>
                                </template>
                            </div>
                        </div>
                    </div>
                    <div class="numberInfo" v-if="list.numberInfo">
                        <div class="comp_list">
                            <div class="comp_item" :class="`comp_item_${list.numberInfo.type}`">
                                <template v-if="list.numberInfo.type == 'text'">
                                    <p class="txt">{{ list.numberInfo.title }} *</p>
                                    <input
                                        :class="{ is_new: list.numberInfo.type == 'text' }"
                                        :type="list.numberInfo.type"
                                        v-model="estimated_number"
                                        :placeholder="list.numberInfo.placeholder"
                                        @input="inputTxt"
                                        @blur="blInp(list.numberInfo)" />
                                    <div class="error-box">
                                        <validate-error :error="err_estimated_number"></validate-error>
                                    </div>
                                </template>
                            </div>
                        </div>
                    </div>
                    <div class="additionalInfo" v-if="list.additionalInfo">
                        <div class="comp_list">
                            <div class="comp_item" :class="`comp_item_${list.additionalInfo.type}`">
                                <template v-if="list.additionalInfo.type == 'textarea'">
                                    <div class="txt-num">
                                        <p class="txt">{{ list.additionalInfo.title }}</p>
                                        <div class="count">{{ additional_info.length }}/5000</div>
                                    </div>
                                    <textarea class="area is_new" :placeholder="list.additionalInfo.placeholder" v-model="additional_info" maxlength="5000"></textarea>

                                    <div class="error-box">
                                        <validate-error :error="err_additional_info"></validate-error>
                                    </div>
                                </template>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="upload_file">
                    <upload-file
                        :isNewStyle="true"
                        accept=".pdf,.doc,.docx,.xls,.xlsx,.txt,image/jpeg,image/jpg,image/png"
                        type="file"
                        :text="$c('pages.RMA.uploadFile')"
                        :limit="5"
                        :multiple="true"
                        :maxSize="5 * 1024 * 1024"
                        v-model="upload_list.files"
                        @change="handleChange">
                        <fs-popover slot="tip">
                            <p>{{ $c("pages.answerQuestion.upload.accept") }}</p>
                            <p>{{ $c("pages.answerQuestion.upload.max") }}</p>
                        </fs-popover>
                    </upload-file>
                </div>
            </div>
            <div class="footer" v-if="success === 1">
                <fs-button :text="this.$c('form.form.next')" @click="next"></fs-button>
            </div>
            <div class="content" v-if="success === 2">
                <!-- userInfo -->
                <div class="user_info">
                    <div class="title">{{ $c("pages.Products.product_develop[11]") }}</div>
                    <div class="form_list">
                        <div class="form_item">
                            <p class="txt">{{ $c("form.form.first_name") }} *</p>
                            <input type="text" class="inp is_new" v-model="user.first_name" @input="focusInput('first_name')" @blur="blurInput('first_name')" />
                            <div class="error-box">
                                <validate-error :error="errors.first_name"></validate-error>
                            </div>
                        </div>
                        <div class="form_item">
                            <p class="txt">{{ $c("form.form.last_name") }} *</p>
                            <input type="text" class="inp is_new" v-model="user.last_name" @input="focusInput('last_name')" @blur="blurInput('last_name')" />
                            <div class="error-box">
                                <validate-error :error="errors.last_name"></validate-error>
                            </div>
                        </div>
                        <div class="form_item">
                            <p class="txt">{{ $c("pages.CaseDetail.detailsPage.common.email") }}{{ isCn ? `(${$c("form.form.optional")})` : " *" }}</p>
                            <input type="text" class="inp is_new" v-model="user.email" @input="focusInput('email')" @blur="blurInput('email')" />
                            <div class="error-box">
                                <validate-error :error="errors.email && errors.email.replace('XXXX', localePath({ name: 'login', query: { redirect: $route.fullPath } }))"></validate-error>
                            </div>
                        </div>
                        <div class="form_item">
                            <p class="txt">{{ website == "cn" ? $c("form.form.phone_business") : $c("form.form.phone_number") }} *</p>
                            <tel-code :isNewStyle="true" @changeCode="changeCode" :phone="user.phone" @change="telChange" @input="telInput" :isTopPosition="true"></tel-code>
                            <div class="error-box">
                                <validate-error :error="errors.phone && errors.phone.replace('XXXX', localePath({ name: 'login', query: { redirect: $route.fullPath } }))"></validate-error>
                            </div>
                        </div>
                        <div class="form_item">
                            <p class="txt">{{ $c(country_label) }} *</p>
                            <select-country :isNewStyle="true" v-model="user.iso_code" @change="selectCountry" :isTopPosition="true"></select-country>
                            <div class="error-box">
                                <validate-error :error="errors.iso_code"></validate-error>
                            </div>
                        </div>
                        <div class="form_item" v-if="isShowState">
                            <p class="txt">{{ $c("single.ProductReturnForm.state.tit") }}</p>
                            <RegionSelect :isNewStyle="true" ref="regionSelect" :isTopPosition="true" />
                        </div>
                    </div>
                </div>
                <!-- agreement -->
                <PolicyCheck v-model="user.checked" @change="inputCheck('checked')" :error="errors.checked" />
            </div>
            <div class="footer" v-if="success === 2">
                <fs-button :text="website == 'jp' ? '送信' : this.$c('pages.Products.Submit')" @click="submit"></fs-button>
            </div>
            <div class="success" v-if="success === 3">
                <div class="iconfont iconfont_success">&#xe710;</div>
                <p class="title">{{ $c("pages.Products.product_develop[12]") }}</p>
                <p class="msg" v-html="$c('pages.Products.product_develop[13]')"></p>
                <fs-button type="gray" @click="toCase">{{ $c("pages.Products.product_develop[14]") }}</fs-button>
            </div>
        </div>
    </fs-popup-new>
</template>

<script>
import FsPopupNew from "@/components/FsPopupNew/FsPopupNew.vue"
import FsButton from "@/components/FsButton/FsButton.vue"
import UploadFile from "@/components/UploadFile/UploadFile"
import TelCode from "@/components/TelCode/TelCode.vue"
import SelectCountry from "@/components/SelectCountry/SelectCountry.vue"
import RegionSelect from "@/components/RegionSelect/RegionSelect"
import ValidateError from "@/components/ValidateError/ValidateError"
import ValidateMessage from "@/components/ValidateMessage/ValidateMessage"
import FsPopover from "@/components/FsPopover"
import { mapState, mapGetters } from "vuex"
import { phone_validate, email_valdate, cn_mobile_tel } from "@/constants/validate.js"
import PolicyCheck from "@/components/PolicyCheck/PolicyCheck.vue"
export default {
    components: {
        FsButton,
        FsPopupNew,
        UploadFile,
        TelCode,
        SelectCountry,
        RegionSelect,
        ValidateError,
        ValidateMessage,
        FsPopover,
        PolicyCheck,
    },
    props: {
        showGa: {
            type: Boolean,
            default: false,
        },
        type: {
            type: Number,
            default: 0,
        },
        info: {
            type: Object,
            default: () => ({}),
        },
        img: {
            type: String,
            default: "",
        },
        is_customized: {
            type: Number,
            default: 0,
        },
        productDevList: {
            type: Array,
            default: () => [],
        },
        productDevAttr: {
            type: Array,
            default: () => [],
        },
    },
    data() {
        return {
            loading: false,
            step_index: 0,
            step_list: [this.$c("pages.Products.product_develop[3]"), this.$c("pages.Products.product_develop[4]")],
            list: {},
            success: 1,
            upload_list: [],
            components: {
                components_cable_color: "",
                components_connector: "",
                components_fiber: "",
                components_fiber_other: "",
                components_jacket: "",
                components_jacket_other: "",
            },
            performance: {
                performance_insertion: "",
                performance_insertion_other: "",
                performance_return: "",
                performance_return_other: "",
            },
            services: {
                services_label: "",
                services_label_other: "",
                services_label_info: "",
            },
            estimated_number: "",
            additional_info: "",
            err_estimated_number: "",
            err_additional_info: "",
            user: {
                first_name: "",
                last_name: "",
                email: "",
                phone: "",
                iso_code: "",
                country_id: "",
                checked: false,
            },
            errors: {
                first_name: "",
                last_name: "",
                email: "",
                iso_code: "",
                phone: "",
                checked: "",
            },
            isOnly: "",
            regExp: /^\d{6,}$/,
            showDown: false,
            onceGa: true,
        }
    },
    computed: {
        ...mapState({
            userInfo: (state) => state.userInfo.userInfo,
            isLogin: (state) => state.userInfo.isLogin,
            select_country_code: (state) => state.selectCountry.select_country_code || "",
            select_country_name: (state) => state.selectCountry.select_country_name || "",
            select_country_id: (state) => state.selectCountry.select_country_id || "",
            iso_code: (state) => state.webSiteInfo.iso_code,
            iso_id: (state) => state.webSiteInfo.countries_id,
            website: (state) => state.webSiteInfo.website,
            isMobile: (state) => state.device.isMobile,
        }),
        ...mapGetters({
            isShowState: "selectCountry/isShowState",
            isCn: "webSiteInfo/isCn",
            country_label: "selectCountry/country_label",
        }),
    },
    mounted() {
        this.init()
    },
    watch: {
        showGa(val) {
            if (val && window.dataLayer) {
                window.dataLayer.push({
                    event: "uaEvent",
                    eventCategory: `productDetailPage_${this.info.products_id_show}`,
                    eventAction: "View_Product_Development",
                    eventLabel: "Step1",
                    nonInteraction: false,
                })
                this.onceGa = true
            } else {
                this.onceGa = false
            }
        },
        success(val) {
            if (val == 2 && this.onceGa) {
                if (window.dataLayer) {
                    window.dataLayer.push({
                        event: "uaEvent",
                        eventCategory: `productDetailPage_${this.info.products_id_show}`,
                        eventAction: "View_Product_Development",
                        eventLabel: "Step2",
                        nonInteraction: false,
                    })
                    this.onceGa = false
                }
            }
        },
    },
    methods: {
        async init() {
            const res = await this.$axios.get("/api/custom_service/getForm", { params: { form_type: this.type } })
            if (res && res.data && res.data.components && res.data.performance && res.data.services) {
                res.data.components.forEach((item) => {
                    item.value = ""
                    item.other = ""
                    item.error = ""
                })
                res.data.performance.forEach((item) => {
                    item.value = ""
                    item.other = ""
                    item.error = ""
                })
                res.data.services.forEach((item) => {
                    item.value = ""
                    item.other = ""
                    item.error = ""
                })
                this.list = res.data
            }
        },
        reset() {
            this.step_index = 0
            this.success = 1
            if (!this.user.iso_code) {
                this.user.iso_code = this.select_country_code
            }
            if (!this.user.country_id) {
                this.user.country_id = this.select_country_id
            }
            if (this.isLogin) {
                this.user.first_name = this.userInfo.customers_firstname
                this.user.last_name = this.userInfo.customers_lastname
                this.user.email = this.userInfo.customers_email_address
                this.user.phone = this.userInfo.customers_telephone
            }
        },
        close() {
            this.$emit("close")
        },
        handleChange(params) {
            if (params.files.length + this.upload_list.length > 5) {
                // this.error.files = this.multipleError.replace("XXXXX", 5)
                return
            }
            for (let i = 0; i < params.files.length; i++) {
                if (params.files[i].size > 5 * 1024 * 1024) {
                    // this.error.files = this.MaxSizeError.replace("XXXXX", formatFileSize(5 * 1024 * 1024))
                    return
                }
            }
            this.upload_list = params.files
        },
        next() {
            this.checkAll()
            if (window.dataLayer) {
                window.dataLayer.push({
                    event: "uaEvent",
                    eventCategory: `productDetailPage_${this.info.products_id_show}`,
                    eventAction: "submit_development",
                    eventLabel: "Next",
                    nonInteraction: false,
                })
            }
        },
        back(t) {
            if (this.step_index == 0) {
                return
            }
            this.step_index = t - 1
            this.success = t
            if (t == 1) {
                this.$refs.step.scrollLeft = 0
            }
        },
        submit() {
            if (this.isSubmit()) {
                this.$nextTick(() => {
                    let msg = document.querySelector(".validate_error")
                    if (msg) {
                        msg.style.marginTop = "-65px"
                        msg.scrollIntoView({ behavior: "smooth", block: "start" })
                        msg.style.marginTop = "0"
                    }
                })
                return
            }
            let obj = {
                estimated_number: this.estimated_number,
                additional_info: this.additional_info,
                upload_file: this.upload_list,
                form_type: this.type,
                products: {
                    products_id: this.info.products_id_show,
                    attributes: this.productDevAttr,
                },
                first_name: this.user.first_name,
                last_name: this.user.last_name,
                email: this.user.email,
                phone: this.user.phone,
                country_id: this.user.country_id,
            }
            switch (this.type) {
                case 1:
                    obj.components_cable_color = this.components.components_cable_color
                    obj.components_connector = this.components.components_connector
                    obj.components_fiber = this.components.components_fiber
                    obj.components_fiber_other = this.components.components_fiber_other
                    obj.components_jacket = this.components.components_jacket
                    obj.components_jacket_other = this.components.components_jacket_other
                    obj.performance_insertion = this.performance.performance_insertion
                    obj.performance_insertion_other = this.performance.performance_insertion_other
                    obj.performance_return = this.performance.performance_return
                    obj.performance_return_other = this.performance.performance_return_other
                    obj.services_label = this.services.services_label
                    obj.services_label_other = this.services.services_label_other
                    obj.services_label_info = this.services.services_label_info
                    break
                case 2:
                    obj.components_cable_color = this.components.components_cable_color
                    obj.components_head = this.components.components_head
                    obj.components_head_other = this.components.components_head_other
                    obj.components_gauge = this.components.components_gauge
                    obj.components_gauge_other = this.components.components_gauge_other
                    obj.components_cable = this.components.components_cable
                    obj.components_cable_other = this.components.components_cable_other
                    obj.performance_rating = this.performance.performance_rating
                    obj.performance_rating_other = this.performance.performance_rating_other
                    obj.performance_shielding = this.performance.performance_shielding
                    obj.performance_shielding_other = this.performance.performance_shielding_other
                    obj.performance_number_bends = this.performance.performance_number_bends
                    obj.performance_number_bends_other = this.performance.performance_number_bends_other
                    obj.services_label = this.services.services_label
                    obj.services_label_other = this.services.services_label_other
                    obj.services_label_info = this.services.services_label_info
                    break
                case 3:
                    obj.components_housing = this.components.components_housing
                    obj.components_housing_other = this.components.components_housing_other
                    obj.components_channels = this.components.components_channels
                    obj.components_channels_other = this.components.components_channels_other
                    obj.components_line = this.components.components_line
                    obj.components_line_other = this.components.components_line_other
                    obj.components_client = this.components.components_client
                    obj.components_client_other = this.components.components_client_other
                    obj.components_special_service = this.components.components_special_service
                    obj.components_special_service_other = this.components.components_special_service_other
                    obj.performance_power = this.performance.performance_power
                    obj.performance_power_other = this.performance.performance_power_other
                    obj.performance_insertion_other = this.performance.performance_insertion_other
                    obj.performance_waterproof = this.performance.performance_waterproof
                    obj.services_label_info = this.services.services_label_info
                    break
                case 4:
                    obj.components_connector = this.components.components_connector
                    obj.components_cable_color = this.components.components_cable_color
                    obj.components_cable = this.components.components_jacket
                    obj.components_cable_other = this.components.components_jacket_other
                    obj.performance_fiber_other = this.performance.performance_fiber
                    obj.performance_polarity = this.performance.performance_polarity
                    obj.services_label = this.services.services_label
                    obj.services_label_other = this.services.services_label_other
                    obj.services_label_info = this.services.services_label_info
                    break
                case 5:
                    obj.components_connector = this.components.components_connector
                    obj.components_jacket = this.components.components_jacket
                    obj.components_jacket_other = this.components.components_jacket_other
                    obj.components_length = this.components.components_length
                    obj.components_breakout = this.components.components_breakout
                    obj.components_breakout_other = this.components.components_breakout_other
                    obj.components_cable = this.components.components_cable
                    obj.components_cable_other = this.components.components_cable_other
                    obj.performance_fiber = this.performance.performance_fiber
                    obj.performance_fiber_other = this.performance.performance_fiber_other
                    obj.performance_polarity = this.performance.performance_polarity
                    obj.performance_insertion_other = this.performance.performance_insertion_other
                    obj.performance_return_other = this.performance.performance_return_other
                    break
                case 6:
                    obj.performance_receiver = this.performance.performance_receiver
                    obj.performance_transmitter = this.performance.performance_transmitter
                    obj.performance_differential = this.performance.performance_differential
                    // obj.services_label = this.services.services_label
                    // obj.services_label_other = this.services.services_label_other
                    // obj.services_label_info = this.services.services_label_info
                    break
                case 7:
                    obj.components_cable_color = this.components.components_cable_color
                    obj.components_connector = this.components.components_connector
                    obj.components_fiber = this.components.components_fiber
                    obj.components_fiber_other = this.components.components_fiber_other
                    obj.components_jacket = this.components.components_jacket
                    obj.components_jacket_other = this.components.components_jacket_other
                    obj.performance_insertion = this.performance.performance_insertion
                    obj.performance_insertion_other = this.performance.performance_insertion_other
                    obj.performance_return = this.performance.performance_return
                    obj.performance_return_other = this.performance.performance_return_other
                    obj.services_label = this.services.services_label
                    obj.services_label_other = this.services.services_label_other
                    obj.services_label_info = this.services.services_label_info
                    break
            }
            if (this.$refs.regionSelect) {
                obj.state = this.$refs.regionSelect.state || ""
            }
            const data = new FormData()
            for (let i in obj) {
                if (i == "products") {
                    for (let j in obj[i]) {
                        if (j == "products_id") {
                            data.append(`${i}[${j}]`, obj[i][j])
                        } else {
                            obj[i][j].forEach((attr, index) => {
                                for (let l in attr) {
                                    data.append(`${i}[${j}][${index}][${l}]`, attr[l])
                                }
                            })
                        }
                    }
                } else if (i == "upload_file") {
                    obj[i].forEach((file) => {
                        data.append(`${i}[]`, file)
                    })
                } else {
                    data.append(`${i}`, obj[i])
                }
            }
            this.loading = true
            this.$axios
                .post("/api/custom_service/formSubmit", data)
                .then((res) => {
                    this.loading = false
                    if (res.code == 200) {
                        this.success = 3
                        if (window.dataLayer) {
                            window.dataLayer.push({
                                event: "uaEvent",
                                eventCategory: `productDetailPage_${this.info.products_id_show}`,
                                eventAction: "submit_development",
                                eventLabel: `Submit Success_${res?.data?.case_number}`,
                                nonInteraction: false,
                            })
                        }
                    }
                })
                .catch((err) => {
                    this.loading = false
                    this.$message(err.message)
                    if (window.dataLayer) {
                        window.dataLayer.push({
                            event: "uaEvent",
                            eventCategory: `productDetailPage_${this.info.products_id_show}`,
                            eventAction: "submit_development",
                            eventLabel: `Submit Fail`,
                            nonInteraction: false,
                        })
                    }
                })
        },
        // 替换语言包中的变量
        subs(str, n, t) {
            if (t === 1) {
                return str.replace("xxxx", n)
            } else if (t === 2) {
                return str.replace("xxxx", this.localePath({ path: "/policies/privacy_policy.html" })).replace("yyyy", this.localePath({ path: "/policies/terms_of_use.html" }))
            }
        },
        checkAll() {
            let c = this.checkComponents(),
                p = this.checkPerformance(),
                s = this.checkServices()
            if (this.type == 5) {
                if (c && p) {
                    this.isOnly = this.$c("pages.Products.product_develop[15]")
                } else {
                    this.isOnly = ""
                    if (!this.checkOther()) {
                        this.isOnly = ""
                        this.step_index = 1
                        this.success = 2
                        this.$refs.step.scrollLeft = this.$refs.step.scrollWidth
                    }
                }
            } else if (this.type == 6) {
                if (p) {
                    this.isOnly = this.$c("pages.Products.product_develop[15]")
                } else {
                    this.isOnly = ""
                    if (!this.checkOther()) {
                        this.isOnly = ""
                        this.step_index = 1
                        this.success = 2
                        this.$refs.step.scrollLeft = this.$refs.step.scrollWidth
                    }
                }
            } else {
                setTimeout(() => {
                    let msg = document.querySelector(".validate-message-error")
                    let error = document.querySelector(".validate_error")
                    if (msg) {
                        msg.scrollIntoView({ behavior: "smooth" })
                    } else if (error) {
                        error.scrollIntoView({ behavior: "smooth" })
                    }
                }, 0)
                if (c && p && s) {
                    this.isOnly = this.$c("pages.Products.product_develop[15]")
                } else {
                    this.isOnly = ""
                    if (!this.checkOther()) {
                        this.isOnly = ""
                        this.step_index = 1
                        this.success = 2
                        this.$refs.step.scrollLeft = this.$refs.step.scrollWidth
                    }
                }
            }
        },
        checkComponents() {
            let flag = true
            switch (this.type) {
                case 1:
                    this.components = {
                        components_cable_color: this.list.components[0].value,
                        components_connector: this.list.components[1].value,
                        components_fiber: this.list.components[2].value,
                        components_fiber_other: this.list.components[2].other,
                        components_jacket: this.list.components[3].value,
                        components_jacket_other: this.list.components[3].other,
                    }
                    if (this.components.components_cable_color) {
                        flag = false
                    } else if (this.components.components_connector) {
                        flag = false
                    }
                    if (this.list.components[2].is_other) {
                        if (this.components.components_fiber_other) {
                            flag = false
                        }
                    } else if (this.components.components_fiber) {
                        flag = false
                    }
                    if (this.list.components[3].is_other) {
                        if (this.components.components_jacket_other) {
                            flag = false
                        }
                    } else if (this.components.components_jacket) {
                        flag = false
                    }
                    break
                case 2:
                    this.components = {
                        components_cable_color: this.list.components[0].value,
                        components_head: this.list.components[1].value,
                        components_head_other: this.list.components[1].other,
                        components_gauge: this.list.components[2].value,
                        components_gauge_other: this.list.components[2].other,
                        components_cable: this.list.components[3].value,
                        components_cable_other: this.list.components[3].other,
                    }
                    if (this.components.components_cable_color) {
                        flag = false
                    }
                    if (this.list.components[1].is_other) {
                        if (this.components.components_head_other) {
                            flag = false
                        }
                    } else if (this.components.components_head) {
                        flag = false
                    }
                    if (this.list.components[2].is_other) {
                        if (this.components.components_gauge_other) {
                            flag = false
                        }
                    } else if (this.components.components_fiber) {
                        flag = false
                    }
                    if (this.list.components[3].is_other) {
                        if (this.components.components_cable_other) {
                            flag = false
                        }
                    } else if (this.components.components_cable) {
                        flag = false
                    }
                    break
                case 3:
                    this.components = {
                        components_housing: this.list.components[0].value,
                        components_housing_other: this.list.components[0].other,
                        components_channels: this.list.components[1].value,
                        components_channels_other: this.list.components[1].other,
                        components_line: this.list.components[2].value,
                        components_line_other: this.list.components[2].other,
                        components_client: this.list.components[3].value,
                        components_client_other: this.list.components[3].other,
                        components_special_service: this.list.components[4].value,
                        components_special_service_other: this.list.components[4].other,
                    }
                    if (this.list.components[0].is_other) {
                        if (this.components.components_housing_other) {
                            flag = false
                        }
                    } else if (this.components.components_housing) {
                        flag = false
                    }
                    if (this.list.components[1].is_other) {
                        if (this.components.components_channels_other) {
                            flag = false
                        }
                    } else if (this.components.components_channels) {
                        flag = false
                    }
                    if (this.list.components[2].is_other) {
                        if (this.components.components_line_other) {
                            flag = false
                        }
                    } else if (this.components.components_line) {
                        flag = false
                    }
                    if (this.list.components[3].is_other) {
                        if (this.components.components_client_other) {
                            flag = false
                        }
                    } else if (this.components.components_client) {
                        flag = false
                    }
                    if (this.list.components[4].is_other) {
                        if (this.components.components_special_service_other) {
                            flag = false
                        }
                    } else if (this.components.components_special_service) {
                        flag = false
                    }
                    break
                case 4:
                    this.components = {
                        components_connector: this.list.components[0].value,
                        components_cable_color: this.list.components[1].value,
                        components_jacket: this.list.components[2].value,
                        components_jacket_other: this.list.components[2].other,
                    }
                    if (this.components.components_connector) {
                        flag = false
                    } else if (this.components.components_cable_color) {
                        flag = false
                    }
                    if (this.list.components[2].is_other) {
                        if (this.components.components_jacket_other) {
                            flag = false
                        }
                    } else if (this.components.components_jacket) {
                        flag = false
                    }
                    break
                case 5:
                    this.components = {
                        components_connector: this.list.components[0].value,
                        components_jacket: this.list.components[1].value,
                        components_jacket_other: this.list.components[1].other,
                        components_length: this.list.components[2].value,
                        components_breakout: this.list.components[3].value,
                        components_breakout_other: this.list.components[3].other,
                        components_cable: this.list.components[4].value,
                        components_cable_other: this.list.components[4].other,
                    }
                    if (this.components.components_connector) {
                        flag = false
                    } else if (this.components.components_length) {
                        flag = false
                    }
                    if (this.list.components[1].is_other) {
                        if (this.components.components_jacket_other) {
                            flag = false
                        }
                    } else if (this.components.components_jacket) {
                        flag = false
                    }
                    if (this.list.components[3].is_other) {
                        if (this.components.components_breakout_other) {
                            flag = false
                        }
                    } else if (this.components.components_breakout) {
                        flag = false
                    }
                    if (this.list.components[4].is_other) {
                        if (this.components.components_cable_other) {
                            flag = false
                        }
                    } else if (this.components.components_cable) {
                        flag = false
                    }
                    break
                case 6:
                    flag = false
                    break
                case 7:
                    this.components = {
                        components_cable_color: this.list.components[0].value,
                        components_connector: this.list.components[1].value,
                        components_fiber: this.list.components[2].value,
                        components_fiber_other: this.list.components[2].other,
                        components_jacket: this.list.components[3].value,
                        components_jacket_other: this.list.components[3].other,
                    }
                    if (this.components.components_cable_color) {
                        flag = false
                    } else if (this.components.components_connector) {
                        flag = false
                    }
                    if (this.list.components[2].is_other) {
                        if (this.components.components_fiber_other) {
                            flag = false
                        }
                    } else if (this.components.components_fiber) {
                        flag = false
                    }
                    if (this.list.components[3].is_other) {
                        if (this.components.components_jacket_other) {
                            flag = false
                        }
                    } else if (this.components.components_jacket) {
                        flag = false
                    }
                    break
            }
            return flag
        },
        checkPerformance() {
            let flag = true
            this.performance = {}
            switch (this.type) {
                case 1:
                    this.performance = {
                        performance_insertion: this.list.performance[0].value,
                        performance_insertion_other: this.list.performance[0].other,
                        performance_return: this.list.performance[1].value,
                        performance_return_other: this.list.performance[1].other,
                    }
                    if (this.list.performance[0].is_other) {
                        if (this.performance.performance_insertion_other) {
                            flag = false
                        }
                    } else if (this.performance.performance_insertion) {
                        flag = false
                    }
                    if (this.list.performance[1].is_other) {
                        if (this.performance.performance_return_other) {
                            flag = false
                        }
                    } else if (this.performance.performance_return) {
                        flag = false
                    }
                    break
                case 2:
                    this.performance = {
                        performance_rating: this.list.performance[0].value,
                        performance_rating_other: this.list.performance[0].other,
                        performance_shielding: this.list.performance[1].value,
                        performance_shielding_other: this.list.performance[1].other,
                        performance_number_bends: this.list.performance[2].value,
                        performance_number_bends_other: this.list.performance[2].other,
                    }
                    if (this.list.performance[0].is_other) {
                        if (this.performance.performance_rating_other) {
                            flag = false
                        }
                    } else if (this.performance.performance_rating) {
                        flag = false
                    }
                    if (this.list.performance[1].is_other) {
                        if (this.performance.performance_shielding_other) {
                            flag = false
                        }
                    } else if (this.performance.performance_shielding) {
                        flag = false
                    }
                    if (this.list.performance[2].is_other) {
                        if (this.performance.performance_number_bends_other) {
                            flag = false
                        }
                    } else if (this.performance.performance_number_bends) {
                        flag = false
                    }
                    break
                case 3:
                    this.performance = {
                        performance_power: this.list.performance[0].value,
                        performance_power_other: this.list.performance[0].other,
                        performance_insertion_other: this.list.performance[1].value,
                        performance_waterproof: this.list.performance[2].value,
                    }
                    if (this.list.performance[0].is_other) {
                        if (this.performance.performance_power_other) {
                            flag = false
                        }
                    } else if (this.performance.performance_insertion_other) {
                        flag = false
                    } else if (this.performance.performance_waterproof) {
                        flag = false
                    }
                    break
                case 4:
                    this.performance = {
                        performance_fiber: this.list.performance[0].value,
                        performance_polarity: this.list.performance[1].value,
                    }
                    if (this.performance.performance_fiber) {
                        flag = false
                    } else if (this.performance.performance_polarity) {
                        flag = false
                    }
                    break
                case 5:
                    this.performance = {
                        performance_fiber: this.list.performance[0].value,
                        performance_fiber_other: this.list.performance[0].other,
                        performance_polarity: this.list.performance[1].value,
                        performance_insertion_other: this.list.performance[2].value,
                        performance_return_other: this.list.performance[3].value,
                    }
                    if (this.list.performance[0].is_other) {
                        if (this.performance.performance_fiber_other) {
                            flag = false
                        }
                    } else if (this.performance.performance_fiber) {
                        flag = false
                    }
                    if (this.performance.performance_polarity) {
                        flag = false
                    } else if (this.performance.performance_insertion_other) {
                        flag = false
                    } else if (this.performance.performance_return_other) {
                        flag = false
                    }
                    break
                case 6:
                    this.performance = {
                        performance_receiver: this.list.performance[0].value,
                        performance_transmitter: this.list.performance[1].value,
                        performance_differential: this.list.performance[2].value,
                    }
                    if (this.performance.performance_receiver) {
                        flag = false
                    } else if (this.performance.performance_transmitter) {
                        flag = false
                    } else if (this.performance.performance_differential) {
                        flag = false
                    }
                    break
                case 7:
                    this.performance = {
                        performance_insertion: this.list.performance[0].value,
                        performance_insertion_other: this.list.performance[0].other,
                        performance_return: this.list.performance[1].value,
                        performance_return_other: this.list.performance[1].other,
                    }
                    if (this.list.performance[0].is_other) {
                        if (this.performance.performance_insertion_other) {
                            flag = false
                        }
                    } else if (this.performance.performance_insertion) {
                        flag = false
                    }
                    if (this.list.performance[1].is_other) {
                        if (this.performance.performance_return_other) {
                            flag = false
                        }
                    } else if (this.performance.performance_return) {
                        flag = false
                    }
                    break
            }
            return flag
        },
        checkServices() {
            let flag = true
            switch (this.type) {
                case 1:
                    this.services = {
                        services_label: this.list.services[0].value,
                        services_label_other: this.list.services[0].other,
                        services_label_info: this.list.services[1].value,
                    }
                    if (this.list.services[0].is_other) {
                        if (this.services.services_label_other) {
                            flag = false
                        }
                    } else if (this.services.services_label) {
                        flag = false
                    }
                    if (this.services.services_label_info) {
                        flag = false
                    }
                    break
                case 2:
                    this.services = {
                        services_label: this.list.services[0].value,
                        services_label_other: this.list.services[0].other,
                        services_label_info: this.list.services[1].value,
                    }
                    if (this.list.services[0].is_other) {
                        if (this.services.services_label_other) {
                            flag = false
                        }
                    } else if (this.services.services_label) {
                        flag = false
                    }
                    if (this.services.services_label_info) {
                        flag = false
                    }
                    break
                case 3:
                    this.services = {
                        services_label_info: this.list.services[0].value,
                    }
                    if (this.services.services_label_info) {
                        flag = false
                    }
                    break
                case 4:
                    this.services = {
                        services_label: this.list.services[0].value,
                        services_label_other: this.list.services[0].other,
                        services_label_info: this.list.services[1].value,
                    }
                    if (this.list.services[0].is_other) {
                        if (this.services.services_label_other) {
                            flag = false
                        }
                    } else if (this.services.services_label) {
                        flag = false
                    }
                    if (this.services.services_label_info) {
                        flag = false
                    }
                    break
                case 5:
                    flag = false
                    break
                case 6:
                    flag = false
                    // this.services = {
                    //     services_label: this.list.services[0].value,
                    //     services_label_other: this.list.services[0].other,
                    //     services_label_info: this.list.services[1].value,
                    // }
                    // if (this.list.services[0].is_other) {
                    //     if (this.services.services_label_other) {
                    //         flag = false
                    //     }
                    // } else if (this.services.services_label) {
                    //     flag = false
                    // }
                    // if (this.services.services_label_info) {
                    //     flag = false
                    // }
                    break
                case 7:
                    this.services = {
                        services_label: this.list.services[0].value,
                        services_label_other: this.list.services[0].other,
                        services_label_info: this.list.services[1].value,
                    }
                    if (this.list.services[0].is_other) {
                        if (this.services.services_label_other) {
                            flag = false
                        }
                    } else if (this.services.services_label) {
                        flag = false
                    }
                    if (this.services.services_label_info) {
                        flag = false
                    }
                    break
            }

            return flag
        },
        checkOther() {
            let flag = false
            if (!this.estimated_number) {
                this.err_estimated_number = this.$c("pages.spareOrder.formError.content")
                flag = true
            }
            return flag
        },
        // 聚焦输入框
        focusInput(attr) {
            // this.errors[attr] = ""
            this.blurInput(attr)
        },
        // 输入框失焦
        blurInput(attr) {
            if (attr === "first_name") {
                if (!this.user.first_name.replace(/^\s+|\s+$/g, "")) {
                    this.errors.first_name = this.$c("form.validate.first_name.first_name_required")
                } else if (this.user.first_name.length > 40) {
                    this.errors.first_name = this.$c("pages.confirmOrder.form.first_name_max")
                } else {
                    this.errors.first_name = ""
                }
            }
            if (attr === "last_name") {
                if (!this.user.last_name.replace(/^\s+|\s+$/g, "")) {
                    this.errors.last_name = this.$c("form.validate.last_name.last_name_required")
                } else if (this.user.last_name.length > 40) {
                    this.errors.last_name = this.$c("pages.confirmOrder.form.last_name_max")
                } else {
                    this.errors.last_name = ""
                }
            }
            if (attr === "email") {
                if (!["cn"].includes(this.website)) {
                    if (!this.user.email.replace(/^\s+|\s+$/g, "")) {
                        this.errors.email = this.$c("form.validate.email.email_required")
                    } else if (!email_valdate.test(this.user.email)) {
                        this.errors.email = this.$c("form.validate.email.email_valid")
                    } else {
                        this.errors.email = ""
                    }
                } else {
                    if (this.user.email.replace(/^\s+|\s+$/g, "")) {
                        if (this.isLogin == 1) {
                            this.errors.email = ""
                        } else {
                            this.$axios
                                .post("/api/user/isHasRegister", { customers_name: this.user.email })
                                .then((res) => {
                                    if (res.code != 200) return
                                    const data = res.data
                                    if (data && data.is_has) {
                                        this.errors.email = "账户已存在，单击此处<a href=XXXX title='登录'>登录</a>。"
                                    } else {
                                        this.errors.email = ""
                                    }
                                })
                                .catch((e) => {
                                    this.errors.email = ""
                                })
                        }
                    }
                }
            }
        },
        foInp(item) {
            // item.error = ""
            // this.err_estimated_number = ""
            this.blInp(item)
        },
        blInp(item) {
            let c = this.checkComponents()
            let p = this.checkPerformance()
            let s = this.checkServices()
            if (this.type == 5) {
                if (c && p) {
                    if (item.is_other) {
                        if (!item.other) {
                            item.error = this.$c("pages.spareOrder.formError.content")
                        }
                    } else if (!item.value) {
                        item.error = this.$c("pages.spareOrder.formError.content")
                    }
                } else {
                    this.isOnly = ""
                    this.list.components.forEach((item) => {
                        item.error = ""
                    })
                    this.list.performance.forEach((item) => {
                        item.error = ""
                    })
                    this.list.services.forEach((item) => {
                        item.error = ""
                    })
                }
            } else if (this.type == 6) {
                if (p) {
                    if (item.is_other) {
                        if (!item.other) {
                            item.error = this.$c("pages.spareOrder.formError.content")
                        }
                    } else if (!item.value) {
                        item.error = this.$c("pages.spareOrder.formError.content")
                    }
                } else {
                    this.isOnly = ""
                    this.list.components.forEach((item) => {
                        item.error = ""
                    })
                    this.list.performance.forEach((item) => {
                        item.error = ""
                    })
                    this.list.services.forEach((item) => {
                        item.error = ""
                    })
                }
            } else {
                if (c && p && s) {
                    if (item.is_other) {
                        if (!item.other) {
                            item.error = this.$c("pages.spareOrder.formError.content")
                        }
                    } else if (!item.value) {
                        item.error = this.$c("pages.spareOrder.formError.content")
                    }
                } else {
                    this.isOnly = ""
                    this.list.components.forEach((item) => {
                        item.error = ""
                    })
                    this.list.performance.forEach((item) => {
                        item.error = ""
                    })
                    this.list.services.forEach((item) => {
                        item.error = ""
                    })
                }
            }
            this.checkOther()
        },
        selectCountry(item) {
            this.user.iso_code = item.iso_code
            this.user.country_id = item.countries_id
            this.errors.iso_code = ""
        },
        changeCode(code) {
            this.code = code
        },
        telInput(inp) {
            this.telChange(inp)
        },
        telChange(inp) {
            this.user.phone = inp
            // 判断是否为6位数字
            if (!inp.replace(/^\s+|\s+$/g, "")) {
                this.errors.phone = this.$c("form.form.errors.entry_telephone_error")
            } else {
                if (!["cn", "cn"].includes(this.website)) {
                    if (inp.length > 0 && inp.length < 6) {
                        this.errors.phone = this.$c("pages.NetTermsApplication.validate.phone.phone_min")
                    } else if (inp.length > 40) {
                        this.errors.phone = this.$c("pages.NetTermsApplication.validate.phone.phone_validate")
                    } else {
                        this.errors.phone = ""
                    }
                } else {
                    if (inp.length > 0 && inp.length < 6) {
                        this.errors.phone = this.$c("pages.NetTermsApplication.validate.phone.phone_min")
                    } else if (inp.length > 40) {
                        this.errors.phone = this.$c("pages.NetTermsApplication.validate.phone.phone_validate")
                    } else {
                        if (this.isLogin == 1) {
                            this.errors.phone = ""
                        } else {
                            this.$axios
                                .post("/api/user/isHasRegister", { customers_name: inp })
                                .then((res) => {
                                    if (res.code != 200) return
                                    const data = res.data
                                    if (data && data.is_has) {
                                        this.errors.phone = "账户已存在，单击此处<a href=XXXX title='登录'>登录</a>。"
                                    } else {
                                        this.errors.phone = ""
                                    }
                                })
                                .catch((e) => {
                                    this.errors.phone = ""
                                })
                        }
                    }
                }
            }
        },
        inputCheck(attr) {
            if (attr === "checked") {
                if (this.user[attr]) {
                    this.errors[attr] = ""
                } else {
                    this.errors[attr] = this.$c("pages.Login.Please_make_sure_you_agree_Privacy_Policy")
                }
            }
        },
        selectitem(item, t) {
            if (!t.is_other) {
                item.error = ""
                item.is_other = false
            }
            if (t.is_other) {
                item.is_other = true
            }
        },
        isSubmit() {
            let flag = false
            if (!this.user.first_name.replace(/^\s+|\s+$/g, "")) {
                this.errors.first_name = this.$c("form.validate.first_name.first_name_required")
                flag = true
            }
            if (!this.user.last_name.replace(/^\s+|\s+$/g, "")) {
                this.errors.last_name = this.$c("form.validate.last_name.last_name_required")
                flag = true
            }
            if (!["cn"].includes(this.website)) {
                if (!this.user.email.replace(/^\s+|\s+$/g, "")) {
                    this.errors.email = this.$c("form.validate.email.email_required")
                    flag = true
                } else if (!email_valdate.test(this.user.email)) {
                    this.errors.email = this.$c("form.validate.email.email_valid")
                    flag = true
                }
            } else {
                if (email_valdate.test(this.user.email)) {
                    if (this.isLogin == 1) {
                        this.errors.email = ""
                    } else {
                        this.$axios
                            .post("/api/user/isHasRegister", { customers_name: this.user.email })
                            .then((res) => {
                                if (res.code != 200) return
                                const data = res.data
                                if (data && data.is_has) {
                                    this.errors.email = "账户已存在，单击此处<a href=XXXX title='登录'>登录</a>。"
                                } else {
                                    this.errors.email = ""
                                }
                            })
                            .catch((e) => {
                                this.errors.email = ""
                            })
                    }
                }
            }
            if (!this.user.phone.replace(/^\s+|\s+$/g, "")) {
                this.errors.phone = this.$c("form.form.errors.entry_telephone_error")
                flag = true
            } else if (!phone_validate.test(this.user.phone)) {
                this.errors.phone = this.$c("form.form.errors.entry_telephone_error01")
                flag = true
            }
            if (!this.user.iso_code.replace(/^\s+|\s+$/g, "")) {
                this.errors.iso_code = this.$c("single.SolutionDesign.right.errors.country_code_error")
                flag = true
            }
            if (!this.user.checked) {
                this.errors.checked = this.$c("pages.Login.Please_make_sure_you_agree_Privacy_Policy")
                flag = true
            }
            return flag
        },
        showCustom() {
            this.showDown = !this.showDown
        },
        toCase() {
            this.$router.push(this.localePath({ path: "/support_ticket" }))
            if (window.dataLayer) {
                window.dataLayer.push({
                    event: "uaEvent",
                    eventCategory: `productDetailPage_${this.info.products_id_show}`,
                    eventAction: "submit_development",
                    eventLabel: `View Cases`,
                    nonInteraction: false,
                })
            }
        },
        inputTxt() {
            this.estimated_number = this.estimated_number.replace(/[^0-9]/g, "")
            if (this.estimated_number) {
                this.err_estimated_number = ""
            }
        },
    },
}
</script>

<style lang="scss" scoped>
::v-deep .fs-popup-ctn {
    width: 750px;
}
.body {
    .head {
        padding: 20px 32px;
        border-bottom: 1px solid #e5e5e5;
        @media (max-width: 960px) {
            padding: 20px 16px;
        }
        .top {
            display: flex;
            align-items: center;
            justify-content: space-between;
            > span {
                @include font20;
                color: $textColor1;
            }
            .iconfont {
                font-size: 16px;
                line-height: 1;
                color: $textColor3;
                cursor: pointer;
                &:hover {
                    color: $textColor1;
                }
            }
        }
        .step {
            margin-top: 20px;
            position: relative;
            overflow-x: auto;
            &::-webkit-scrollbar {
                display: none;
            }
            .left,
            .right {
                display: none;
                position: fixed;
                width: 40px;
                height: 50px;
                top: 64px;
                z-index: 1;
                pointer-events: auto;
            }
            .stepBD {
                display: flex;
            }
            .item {
                flex: 1;
                position: relative;
                margin-right: 10px;
                &.haveHover:hover {
                    cursor: pointer;
                    .txt {
                        cursor: pointer;
                    }
                }
                .bg {
                    border-width: 18px 0px 18px 20px;
                    border-color: #f2f2f2 #ffffff;
                    border-style: solid;
                    &:after {
                        content: "";
                        display: inline-block;
                        position: absolute;
                        border-width: 18px 0px 18px 20px;
                        border-color: transparent #f2f2f2;
                        border-style: solid;
                        top: 0px;
                        right: -20px;
                        z-index: 1;
                    }
                    &.active {
                        border-color: #e5e5e5 #ffffff;
                        &:after {
                            border-color: transparent #e5e5e5;
                        }
                    }
                }
                .txt {
                    position: absolute;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    padding: 0 20px;
                    text-align: center;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    @include font13;
                    color: $textColor3;
                    cursor: default;
                }
                &:first-child {
                    .bg {
                        border-color: #f2f2f2;
                        &.active {
                            border-color: #e5e5e5;
                        }
                    }
                }
                &:last-child {
                    margin-right: 0px;
                    .bg {
                        &:after {
                            display: none;
                        }
                    }
                }
            }
            @media (max-width: 414px) {
                .stepBD {
                    display: inline-flex;
                }
                .left {
                    left: 0;
                    transform: rotate(180deg);
                    background: linear-gradient(270deg, #ffffff 36%, rgba(255, 255, 255, 0.6) 68%, rgba(255, 255, 255, 0.13) 93%, rgba(255, 255, 255, 0) 100%);
                }
                .right {
                    right: 0;
                    background: linear-gradient(270deg, #ffffff 36%, rgba(255, 255, 255, 0.6) 68%, rgba(255, 255, 255, 0.13) 93%, rgba(255, 255, 255, 0) 100%);
                }
                .isAct {
                    display: block;
                }
                .item {
                    flex: auto;
                    width: 338px;
                }
            }
        }
    }
    .content {
        max-height: calc(100vh - 356px);
        overflow: auto;
        padding: 20px 32px;
        border-bottom: 1px solid #e5e5e5;
        .product_info {
            margin-bottom: 24px;
            .title {
                @include font16;
                color: $textColor1;
                font-weight: 600;
                margin-bottom: 20px;
            }
            .info {
                background-color: #fafafb;
                padding: 12px 20px 12px 24px;
                display: flex;
                > img {
                    width: 60px;
                    height: 60px;
                    margin-right: 20px;
                    mix-blend-mode: multiply;
                }
                .con {
                    flex: 1;
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    .left {
                        > div {
                            @include font14;
                            color: $textColor1;
                            > span {
                                color: $textColor3;
                            }
                            &.blue {
                                cursor: pointer;
                                margin-top: 12px;
                                display: flex;
                                align-items: center;
                                > span {
                                    color: #0060bf;
                                }
                                .iconfont {
                                    font-size: 12px;
                                    line-height: 1;
                                    margin-top: 1px;
                                    margin-left: 8px;
                                    color: #0060bf;
                                    transition: all 0.2s;
                                    &.icofnont-down-up {
                                        transform: rotateX(-180deg);
                                    }
                                }
                            }
                            &.custom_list {
                                margin-top: 8px;
                                .custom_item {
                                    margin-bottom: 4px;
                                    @include font12;
                                    > span {
                                        color: $textColor3;
                                    }
                                    &:last-child {
                                        margin-bottom: 0;
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        .list {
            border-top: 1px solid #e5e5e5;
            .title {
                @include font16;
                color: $textColor1;
                font-weight: 600;
                margin-top: 24px;
            }
            > .validate-message {
                width: 100%;
                margin-top: 8px;
            }
            .components,
            .performance,
            .services,
            .numberInfo,
            .additionalInfo {
                padding-bottom: 24px;
                border-bottom: 1px solid #e5e5e5;
                .tit {
                    @include font14;
                    color: $textColor1;
                    font-weight: 600;
                    margin-top: 16px;
                    margin-bottom: 16px;
                }
                .comp_list {
                    .comp_item {
                        margin-bottom: 16px;
                        &:last-child {
                            margin-bottom: 0;
                        }
                        &.comp_item_text {
                            .txt {
                                @include font12;
                                color: $textColor1;
                                margin-bottom: 4px;
                            }
                            input {
                                text-overflow: ellipsis;
                            }
                            input[type="number"] {
                                &::-webkit-outer-spin-button,
                                &::-webkit-inner-spin-button {
                                    -webkit-appearance: none;
                                }
                                -moz-appearance: textfield;
                            }
                        }
                        &.comp_item_radio {
                            .subhead {
                                @include font12;
                                color: $textColor1;
                                // margin-bottom: 8px;
                            }
                            .radio_list {
                                display: flex;
                                flex-wrap: wrap;
                                .radio_item {
                                    margin-top: 8px;
                                    display: flex;
                                    align-items: center;
                                    margin-right: 48px;
                                    cursor: pointer;
                                    input[type="radio"] {
                                        font-size: 14px;
                                        width: 14px;
                                        height: 14px;
                                    }
                                    &:last-of-type {
                                        margin-right: 0;
                                    }
                                    .txt {
                                        @include font13;
                                        color: $textColor1;
                                    }
                                }
                                > .other {
                                    margin-top: 8px;
                                }
                            }
                        }
                        &.comp_item_textarea {
                            .txt-num {
                                display: flex;
                                justify-content: space-between;
                                align-content: center;
                            }
                            .txt {
                                @include font12;
                                color: $textColor1;
                                margin-bottom: 4px;
                            }
                            .area {
                                width: 100%;
                                height: 110px;
                            }
                            .count {
                                @include font12;
                                color: $textColor3;
                                margin-top: 4px;
                                text-align: right;
                            }
                        }
                    }
                }
            }
            .performance,
            .services,
            .numberInfo {
                margin-top: 24px;
            }
            .additionalInfo {
                margin-top: 16px;
                border-bottom: none;
                padding-bottom: 8px;
            }
            .performance {
                .comp_list {
                    .comp_item_radio {
                        .radio_list {
                            .radio_item {
                                @media (max-width: 414px) {
                                    &:last-child {
                                        margin-top: 8px;
                                    }
                                }
                            }
                        }
                    }
                }
            }
            .numberInfo {
                padding-bottom: 0;
                border-bottom: none;
            }
        }
        .upload_file {
            margin-top: 8px;
            position: relative;
            ::v-deep .upload-file {
                .upload-btn-box {
                    display: inline-block;
                    cursor: pointer;
                    input {
                    }
                }
            }
            .fs-popover {
                position: relative;
                z-index: 3;
            }
        }
        .user_info {
            .title {
                @include font16;
                color: $textColor1;
                font-weight: 600;
                margin-bottom: 16px;
            }
            .form_list {
                display: flex;
                flex-wrap: wrap;
                justify-content: space-between;
                .form_item {
                    width: calc((100% - 20px) / 2);
                    margin-bottom: 16px;
                    .txt {
                        @include font12;
                        color: $textColor1;
                        margin-bottom: 4px;
                    }
                    ::v-deep .select-country {
                        .country-box {
                            max-height: 160px;
                        }
                    }
                    ::v-deep .tel-code {
                        .menu-list {
                            max-height: 80px;
                            @media (max-width: 768px) {
                                max-height: 224px;
                            }
                        }
                    }
                    ::v-deep .dropdown-menu {
                        top: inherit;
                        .menu-list {
                            max-height: 160px;
                        }
                    }
                }
                @media (max-width: 960px) {
                    flex-direction: column;
                    .form_item {
                        width: 100%;
                    }
                }
            }
        }
        .agreement {
            .agreement_box {
                display: flex;
                align-items: center;
                margin-top: 12px;
                cursor: pointer;
                .chk {
                    margin-right: 8px;
                    width: 14px;
                    height: 14px;
                    font-size: 14px;
                }
                .agreement {
                    @include font13;
                    color: $textColor3;
                    ::v-deep a {
                        color: $textColor6;
                    }
                }
                &:hover {
                    .chk::before {
                        color: #707070;
                    }
                }
            }
        }
        @media (max-width: 960px) {
            padding: 20px 16px;
            max-height: calc(100vh - 152px);
        }
    }
    .footer {
        display: flex;
        justify-content: flex-end;
        padding: 20px 32px;
        background: #fff;
        .fs-button {
            margin: 0;
            width: initial;
        }
        @media (max-width: 960px) {
            width: 100%;
            position: fixed;
            bottom: 0;
            left: 0;
            padding: 20px 16px;
            .fs-button {
                width: 100%;
            }
        }
    }
    .success {
        margin: 0 32px 36px;
        text-align: center;
        .iconfont_success {
            display: block;
            text-align: center;
            font-size: 50px;
            color: #10a300;
            margin: 36px auto 16px;
        }
        .title {
            @include font16;
            max-width: 584px;
            margin: 0 auto 8px;
            font-weight: 600;
            color: $textColor1;
            text-align: center;
        }
        .msg {
            @include font14;
            max-width: 584px;
            margin: 0 auto 32px;
            color: $textColor3;
            text-align: center;
        }
        .fs-button {
            @include font14;
            color: $textColor1;
        }
        @media (max-width: 960px) {
            margin: 0 16px;
        }
    }
}
@media (max-width: 768px) {
    // ::v-deep {
    //     .fs-popup {
    //         top: auto;
    //         height: auto;
    //         border-radius: 3px 3px 0 0;
    //         overflow: hidden;
    //     }
    // }
    .body {
        display: flex;
        flex-direction: column;
        height: 100%;
        max-height: calc(100vh - 64px);
        .content {
            flex: 1;
        }
        .footer {
            position: static;
        }
        .head {
            .step {
                .isAct {
                    display: none;
                }
            }
            .top > span {
                @include font16;
            }
        }
    }
}
</style>
