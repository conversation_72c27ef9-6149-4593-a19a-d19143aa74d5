<template>
    <fs-popup-new :clickHide="false" v-bind="$attrs" v-on="$listeners" width="680" transition="slide-up" :title="title" :isMDrawer="true">
        <form class="form" v-if="!success" @submit.prevent="submit" v-loading="loading">
            <div class="form_main">
                <div class="form_item">
                    <div class="label">{{ $c("pages.ShoppingCart.Form") }} *</div>
                    <div class="inp_box">
                        <input class="is_new" type="text" v-model.trim="form.from_email" :placeholder="$c('pages.ShoppingCart.Your_Email')" @input="inputCheck('from_email')" @blur="getEmailData" />
                        <validate-error :error="errors.from_email"></validate-error>
                    </div>
                </div>
                <div class="form_item">
                    <div class="label">{{ $c("pages.ShoppingCart.To") }} *</div>
                    <div class="inp_box">
                        <fs-checkbox-select
                            v-if="options.length > 0"
                            :isNewStyle="true"
                            :options="options"
                            :placeholder="$c('pages.ShoppingCart.Separate_multiple_recipients_with_semicolons')"
                            @change="handleCheckChange">
                            <template #inputBox>
                                <div class="input-wrapper" :class="{ 'input-wrapper-border': borderShow }">
                                    <input
                                        type="text"
                                        @focus="borderShow = true"
                                        @blur="borderShow = false"
                                        v-model="addInput"
                                        :placeholder="$c('pages.InvoiceService.modify.placeholder.add_way_email')"
                                        class="input-box" />
                                    <i class="iconfont icon" @click.prevent="addEmailItem">&#xf068;</i>
                                </div>
                            </template>
                        </fs-checkbox-select>
                        <input
                            v-else
                            class="is_new"
                            type="text"
                            v-model.trim="form.to_email"
                            :class="{ error_input: errors.to_email }"
                            :placeholder="$c('pages.ShoppingCart.Separate_multiple_recipients_with_semicolons')"
                            @focus="gaEventInput('Email Input')"
                            @input="inputCheck('to_email')" />

                        <validate-error :error="errors.to_email"></validate-error>
                    </div>
                </div>
                <div class="form_item" v-if="isLogin">
                    <label class="checkbox"
                        ><input type="checkbox" v-model="form.checked" @change="chkChange" /><span>{{ $c("pages.ShoppingCart.Send_to_Account_Manager") }}</span></label
                    >
                </div>
                <div class="form_item">
                    <div class="tit-num">
                        <div class="label">{{ $c("pages.ShoppingCart.Comments_optional") }}</div>
                        <span class="num">{{ form.comments.length }}/500</span>
                    </div>
                    <div class="inp_box">
                        <textarea class="comments is_new" maxlength="500" v-model.trim="form.comments" :placeholder="$c('pages.ShoppingCart.characters_maximum_500')" @focus="gaEventInput('Content Input')"></textarea>
                        <div :class="errors.comments ? 'input-item-flex' : 'input-item-number'">
                            <validate-error :error="errors.comments"></validate-error>
                        </div>
                    </div>
                </div>
            </div>
            <div class="btn_box">
                <fs-button class="send" htmlType="submit" type="red" :loading="send_loading">{{ $c("pages.ShoppingCart.Send_Email") }}</fs-button>
                <fs-button class="cancel" type="grayline" @click="close('cancel')">{{ $c("pages.ShoppingCart.Cancel") }}</fs-button>
            </div>
        </form>
        <div class="success_box" v-if="success">
            <p class="success_info">{{ success_info }}</p>
            <div class="btn_box">
                <fs-button class="send" type="red" @click="shareAgain">{{ $c("pages.ShoppingCart.Share_Again") }}</fs-button>
                <fs-button class="cancel" type="grayline" @click="close">{{ from === "cart" ? $c("pages.ShoppingCart.Return_to_Shopping_Cart") : $c("pages.ShoppingCart.Return_to_Saved_Cart") }}</fs-button>
            </div>
        </div>
    </fs-popup-new>
</template>
<script>
import FsPopupNew from "@/components/FsPopupNew/FsPopupNew.vue"
import FsButton from "@/components/FsButton/FsButton.vue"
import ValidateError from "@/components/ValidateError/ValidateError.vue"
import FsCheckboxSelect from "../../pages/UserSettings/components/FsCheckboxSelect/FsCheckboxSelect.vue"
import { email_valdate } from "@/constants/validate"
import { mapState, mapMutations, mapActions } from "vuex"

export default {
    name: "ShareCart",
    components: {
        FsPopupNew,
        FsButton,
        ValidateError,
        FsCheckboxSelect,
    },
    props: {
        from: {
            type: String,
            default: "cart",
        },
        savedId: {
            type: [String, Number],
        },
        productsIds: {
            type: Array,
            default: () => {
                return []
            },
        },
    },
    data() {
        return {
            title: "",
            success: false,
            success_info: "",
            send_loading: false,
            form: {
                from_email: "",
                to_email: "",
                comments: "",
                checked: false,
            },
            errors: {
                from_email: "",
                to_email: "",
                comments: "",
            },
            obj: {},
            options: [],
            loading: false,
            addInput: "",
            borderShow: false,
        }
    },
    computed: {
        ...mapState({
            isLogin: (state) => state.userInfo.isLogin,
            userInfo: (state) => state.userInfo.userInfo,
            cartData: (state) => state.cart.cartData,
            managerInfo: (state) => state.userInfo.managerInfo,
            website: (state) => state.webSiteInfo.website,
        }),
    },
    created() {
        if (this.from === "cart") {
            this.title = this.$c("pages.ShoppingCart.Share_Cart_popup_title")
        } else if (this.from === "savecart") {
            this.title = this.$c("pages.ShoppingCart.Email_Your_Cart")
        }
    },
    mounted() {
        console.log(this.savedId)
        this.success = false
        this.form.from_email = this.userInfo ? this.userInfo.customers_email_address : ""
        this.getEmailData()
    },
    methods: {
        addEmailItem() {
            if (!email_valdate.test(this.addInput)) {
                //验证不合格
                this.$message.error(this.$c("form.validate.email.email_validate2"))
            } else {
                let value = { name: this.addInput, value: this.addInput, checked: true }
                // this.options.unshift(value)
                this.options.push(value)
                this.addInput = ""
            }
        },
        getEmailData() {
            if (this.form.from_email.length > 0) {
                this.loading = true
                let obj = {}
                obj.from_email = this.form.from_email
                this.$axios
                    .post(`/api/shareCart/show_form`, obj)
                    .then((res) => {
                        let list = res.data.shareToList
                        list.forEach((item, index) => {
                            item.name = item.email
                            item.value = item.email
                            item.checked = item.isSelected
                        })
                        this.options = list
                        this.loading = false
                        this.inputCheck("from_email")
                    })
                    .catch((err) => {
                        this.loading = false
                    })
            }
        },
        handleCheckChange(val) {
            console.log(val, "valsss")
            this.form.to_email = val.join(";")
            this.inputCheck("to_email")
        },
        inputCheck(c) {
            let flag = false
            if (c === "from_email") {
                if (!this["form"][c].replace(/\s+/g, "")) {
                    this["errors"][c] = this.$c("form.validate.email.email_required")
                    flag = true
                } else {
                    if (!email_valdate.test(this["form"][c])) {
                        this["errors"][c] = this.$c("form.validate.email.email_valid")
                        flag = true
                    } else {
                        this["errors"][c] = ""
                    }
                }
            }
            if (c === "to_email") {
                if (!this["form"][c].replace(/\s+/g, "")) {
                    this["errors"][c] = this.$c("pages.ShoppingCart.empty")
                    flag = true
                } else {
                    let s = this["form"][c]
                    let sa = s.split(";")
                    let f = false
                    if (this.cartData.adminEmail) {
                        if (s.indexOf(this.cartData.adminEmail) !== -1) {
                            this.form.checked = true
                        } else {
                            this.form.checked = false
                        }
                    }
                    for (let i = 0; i < sa.length; i++) {
                        if (sa[i]) {
                            if (!email_valdate.test(sa[i])) {
                                f = true
                            }
                        }
                    }
                    if (f) {
                        this["errors"][c] = this.$c("form.validate.email.email_valid")
                        flag = true
                    } else {
                        this["errors"][c] = ""
                    }
                }
            }
            return flag
        },
        chkChange() {
            if (this.managerInfo && this.managerInfo.admin_email) {
                if (this.form.checked) {
                    if (this.form.to_email.indexOf(this.managerInfo.admin_email) === -1) {
                        this.form.to_email = `${this.managerInfo.admin_email};${this.form.to_email}`
                    }
                } else {
                    this.form.to_email = this.form.to_email.replace(this.managerInfo.admin_email, "")
                    if (this.form.to_email.startsWith(";")) {
                        this.form.to_email = this.form.to_email.substr(1)
                    }
                }
            }
        },
        shareAgain() {
            if (this.from === "cart") {
                this.title = this.$c("pages.ShoppingCart.Share_Cart_popup_title")
            } else if (this.from === "savecart") {
                this.title = this.$c("pages.ShoppingCart.Email_Your_Cart")
            }
            this.restore()
        },
        gaEventInput(s) {
            if (window.dataLayer) {
                window.dataLayer.push({
                    event: "uaEvent",
                    eventCategory: "Cart Page",
                    eventAction: "share_cart",
                    eventLabel: s,
                    nonInteraction: false,
                })
            }
        },
        restore() {
            this.success = false
            // this.form.from_email = this.userInfo ? this.userInfo.customers_email_address : ""
            this.form.to_email = ""
            this.form.comments = ""
            this.form.checked = false
        },
        close(type) {
            this.$emit("close")
            this.shareAgain()
            if (type !== "cancel") return
            if (window.dataLayer) {
                window.dataLayer.push({
                    event: "uaEvent",
                    eventCategory: "Cart Page",
                    eventAction: "share_cart",
                    eventLabel: "Cancel",
                    nonInteraction: false,
                })
            }
        },
        submit() {
            if (this.send_loading) {
                return
            }
            let arr = [],
                attr = ["from_email", "to_email"]
            attr.map((item) => {
                let f = this.inputCheck(item)
                arr.push(f)
            })
            if (arr.includes(true)) {
                return
            }
            this.send_loading = true

            let api = ""
            let obj = {}
            obj.from_email = this.form.from_email
            obj.comments = this.form.comments
            let s = this.form.to_email
            let sa = s.split(";")
            let e_arr = []
            for (let i = 0; i < sa.length; i++) {
                if (sa[i]) {
                    e_arr.push(sa[i])
                }
            }
            obj.to_email = e_arr

            if (this.from === "cart") {
                api = "/api/shareCart/share"
                if (!this.isSendToAdmin) {
                    obj.isSendToAdmin = 0
                } else {
                    obj.isSendToAdmin = this.form.checked ? 1 : 0
                }
            } else if (this.from === "savecart") {
                api = "/api/saveCart/send"
                obj.savedId = this.savedId
                if (this.productsIds.length) {
                    obj.productsIds = this.productsIds
                }
            }
            this.$axios
                .post(api, obj)
                .then((res) => {
                    this.send_loading = false
                    if (res.code === 200 && res.status === "sensiWords" && this.website === "cn") {
                        for (let key in res.errors) {
                            this.errors[key] = this.$c("form.form.errors.sensiWords")
                        }
                        return
                    }
                    if (this.from === "cart") {
                        this.title = this.$c("pages.ShoppingCart.Email_Sent_Successfully")
                        this.success_info = this.$c("pages.ShoppingCart.emailed_cart_to_recipient")
                        window.dataLayer &&
                            window.dataLayer.push({
                                event: "uaEvent",
                                eventCategory: "Cart Page",
                                eventAction: "share_cart",
                                eventLabel: "Send Email",
                                nonInteraction: false,
                            })
                    } else if (this.from === "savecart") {
                        this.title = this.$c("pages.ShoppingCart.Your_Saved_Cart_Has_Been_Sent")
                        this.success_info = this.$c("pages.ShoppingCart.successfully_sent_email_on_your_behalf")
                    }
                    this.success = true
                })
                .catch((err) => {
                    this.send_loading = false
                    if (err.code === 422) {
                        let errs = err.errors
                        for (let attr in errs) {
                            if (attr) {
                                this.errors[attr] = errs[attr]
                            }
                        }
                    }
                })
        },
    },
}
</script>
<style lang="scss" scoped>
::v-deep .fs-popup-header .title {
    font-weight: 600;
}
/* ::v-deep .fs-popup-header .iconfont_close {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 32px;
    height: 32px;
    &:hover {
        background: rgba($color: #19191a, $alpha: 0.04);
    }
} */
.form,
.success_box {
    // width: 680px;
    // padding: 20px 32px;
    padding: 16px 24px 24px;
    @media (max-width: 960px) {
        // padding: 28px 16px 40px 16px;
        padding: 20px 16px;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
    }
}

.success_info {
    @include font13;
    color: $textColor1;
    margin-bottom: 24px;
}
.form_item {
    margin-bottom: 16px;
    &:nth-child(2) {
        margin-bottom: 12px;
    }
    &:last-child {
        margin-bottom: 20px;
    }
    @media (max-width: 768px) {
        margin-bottom: 12px;
        &:nth-child(2) {
            margin-bottom: 8px;
        }
    }
    .tit-num {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    .input-item-flex {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    .input-item-number {
        display: flex;
        justify-content: flex-end;
        width: 100%;
    }
    .label {
        display: flex;
        align-items: center;
        justify-content: space-between;
        @include font12;
        color: $textColor3;
        font-weight: normal;
        margin-bottom: 4px;
    }
    .num {
        @include font12;
        color: $textColor3;
        margin-top: 4px;
    }
    .comments {
        width: 100%;
        min-height: 82px;
        padding: 10px;
        @include font13;
    }
    .checkbox {
        display: flex;
        align-items: center;
        cursor: pointer;
        input {
            margin-right: 8px;
            width: 14px;
            height: 14px;
            font-size: 14px;
        }
        > span {
            color: $textColor3;
            @include font13;
        }
    }
    .error_input {
        @include errorInput;
    }
}
.btn_box {
    display: flex;
    flex-direction: row-reverse;
    // margin-top: 24px;
    .fs-button {
        width: auto;
        // padding: 0 30px;
        margin-left: 12px;
        min-width: 140px;
    }
    @media (max-width: 960px) {
        flex-direction: column;
        justify-content: flex-end;
        flex: 1;
        .fs-button {
            margin-left: 0;
            &:first-child {
                margin-bottom: 12px;
            }
        }
    }
}
::v-deep .fs-popup-header {
    padding: 16px 24px 0 24px;
}
@media (max-width: 768px) {
    ::v-deep .fs-popup-header {
        padding: 16px 16px 0 16px;
    }
    .form {
        display: flex;
        flex-direction: column;
        max-height: calc(100vh - 64px);
        padding: 0;
        .form_main {
            flex: 1;
            overflow: scroll;
            padding: 16px 16px 20px;
        }
        .btn_box {
            margin-top: 0;
            border-top: 1px solid #e5e5e5;
            padding: 16px;
            @media (max-width: 767px) {
                .fs-button {
                    margin: 0;
                }
                .cancel {
                    display: none;
                }
            }
        }
    }
}

.input-wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
    width: calc(100% - 24px);
    background: #f6f6f8;
    border: 1px solid transparent;
    transition: all 0.3s;
    border-radius: 4px;
    overflow: hidden;
    margin: 12px 12px 8px 12px;
    &:hover {
        background: linear-gradient(0deg, rgba(25, 25, 26, 0.04), rgba(25, 25, 26, 0.04)), #f6f6f8;
    }
    &.input-wrapper-border {
        border: 1px solid #707070;
        background: linear-gradient(0deg, rgba(25, 25, 26, 0.04), rgba(25, 25, 26, 0.04)), #f6f6f8;
    }
    .input-box {
        flex: 1;
        border: none;
        padding: 8px 12px;
        font-size: 13px;
        outline: none;
        color: #19191a;

        &:disabled {
            color: #ccc;
            background: #f6f6f8;
            cursor: not-allowed;
        }
        &::-webkit-input-placeholder {
            @include font12;
        }

        &:-moz-placeholder {
            @include font12;
        }

        &::-moz-placeholder {
            @include font12;
        }

        &:-ms-input-placeholder {
            @include font12;
        }
    }
    .iconfont {
        font-size: 12px;
    }
    .icon {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 28px;
        height: 28px;
        padding: 6px;
        font-size: 16px;
        cursor: pointer;
        border-radius: 4px;
        transition: background-color 0.3s;
        margin-right: 12px;
        color: #707070;
        &:hover {
            color: #19191a;
            // background-color: #e5e5e5;
            background-color: #fff;
        }
    }
}
</style>
